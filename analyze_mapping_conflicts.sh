#!/bin/bash

# 分析映射文件中的冲突和问题
echo "=== 分析映射文件中的冲突和问题 ==="

MAPPING_FILE="need_rename_mapping.txt"
ANALYSIS_LOG="mapping_analysis.txt"

# 清空分析日志
> "$ANALYSIS_LOG"

if [[ ! -f "$MAPPING_FILE" ]]; then
    echo "错误：映射文件 $MAPPING_FILE 不存在"
    exit 1
fi

echo "开始分析映射文件..."
echo "映射文件: $MAPPING_FILE"
echo "分析日志: $ANALYSIS_LOG"
echo ""

# 统计变量
total_mappings=0
class_mappings=0
implementation_mappings=0
conflicts=0
duplicates=0

# 存储映射关系的数组
declare -A old_to_new_map
declare -A new_class_count
declare -A problematic_mappings

echo "=== 映射文件分析报告 ===" | tee -a "$ANALYSIS_LOG"
echo "分析时间: $(date)" | tee -a "$ANALYSIS_LOG"
echo "" | tee -a "$ANALYSIS_LOG"

# 第一遍：收集所有映射关系
echo "第一步：收集映射关系..." | tee -a "$ANALYSIS_LOG"
while read -r line; do
    if [[ -n "$line" && "$line" == *" -> "* ]]; then
        ((total_mappings++))
        
        # 分割映射行
        old_class="${line% -> *}"
        new_class="${line#* -> }"
        
        # 判断是类名映射还是implementation映射
        if [[ "$old_class" == *"类名: @implementation"* ]]; then
            ((implementation_mappings++))
            # 提取实际的类名
            actual_old_class="${old_class#*@implementation }"
            actual_old_class="${actual_old_class% {*}"
            actual_old_class="${actual_old_class% *}"
            
            echo "  Implementation映射: $actual_old_class -> $new_class" | tee -a "$ANALYSIS_LOG"
        else
            ((class_mappings++))
            old_to_new_map["$old_class"]="$new_class"
            
            # 统计新类名的使用次数
            if [[ -n "${new_class_count[$new_class]}" ]]; then
                ((new_class_count["$new_class"]++))
            else
                new_class_count["$new_class"]=1
            fi
            
            echo "  类名映射: $old_class -> $new_class" | tee -a "$ANALYSIS_LOG"
        fi
    fi
done < "$MAPPING_FILE"

echo "" | tee -a "$ANALYSIS_LOG"
echo "映射统计:" | tee -a "$ANALYSIS_LOG"
echo "  总映射数: $total_mappings" | tee -a "$ANALYSIS_LOG"
echo "  类名映射: $class_mappings" | tee -a "$ANALYSIS_LOG"
echo "  Implementation映射: $implementation_mappings" | tee -a "$ANALYSIS_LOG"
echo "" | tee -a "$ANALYSIS_LOG"

# 第二步：检查冲突
echo "第二步：检查映射冲突..." | tee -a "$ANALYSIS_LOG"
echo "" | tee -a "$ANALYSIS_LOG"

# 检查新类名重复使用
echo "检查新类名重复使用:" | tee -a "$ANALYSIS_LOG"
for new_class in "${!new_class_count[@]}"; do
    count=${new_class_count[$new_class]}
    if [[ $count -gt 1 ]]; then
        ((conflicts++))
        echo "  ❌ 冲突: '$new_class' 被 $count 个不同的旧类名映射" | tee -a "$ANALYSIS_LOG"
        
        # 找出所有映射到这个新类名的旧类名
        echo "    映射到 '$new_class' 的旧类名:" | tee -a "$ANALYSIS_LOG"
        for old_class in "${!old_to_new_map[@]}"; do
            if [[ "${old_to_new_map[$old_class]}" == "$new_class" ]]; then
                echo "      - $old_class" | tee -a "$ANALYSIS_LOG"
                problematic_mappings["$old_class -> $new_class"]="CONFLICT"
            fi
        done
        echo "" | tee -a "$ANALYSIS_LOG"
    fi
done

# 第三步：检查特殊问题
echo "第三步：检查特殊问题..." | tee -a "$ANALYSIS_LOG"
echo "" | tee -a "$ANALYSIS_LOG"

# 检查TCRangeContentConfig问题
if [[ -n "${old_to_new_map['TCRangeContentConfig']}" ]]; then
    target="${old_to_new_map['TCRangeContentConfig']}"
    echo "发现TCRangeContentConfig映射: TCRangeContentConfig -> $target" | tee -a "$ANALYSIS_LOG"
    
    if [[ "$target" == "TCRangeContent" ]]; then
        echo "  ❌ 问题: TCRangeContentConfig 和 TCRangeContent 是同一文件中的不同类" | tee -a "$ANALYSIS_LOG"
        echo "  建议: TCRangeContentConfig 应该保持原名或使用不同的前缀" | tee -a "$ANALYSIS_LOG"
        problematic_mappings["TCRangeContentConfig -> TCRangeContent"]="SAME_FILE_CONFLICT"
    fi
fi

# 第四步：生成修复建议
echo "" | tee -a "$ANALYSIS_LOG"
echo "=== 修复建议 ===" | tee -a "$ANALYSIS_LOG"
echo "" | tee -a "$ANALYSIS_LOG"

if [[ $conflicts -gt 0 ]]; then
    echo "发现 $conflicts 个冲突需要修复:" | tee -a "$ANALYSIS_LOG"
    echo "" | tee -a "$ANALYSIS_LOG"
    
    # 为冲突的映射提供建议
    for mapping in "${!problematic_mappings[@]}"; do
        issue_type="${problematic_mappings[$mapping]}"
        old_class="${mapping% -> *}"
        new_class="${mapping#* -> }"
        
        case "$issue_type" in
            "CONFLICT")
                echo "冲突映射: $mapping" | tee -a "$ANALYSIS_LOG"
                echo "  建议: 为 '$old_class' 使用唯一的新类名，如 'XGG${old_class}'" | tee -a "$ANALYSIS_LOG"
                ;;
            "SAME_FILE_CONFLICT")
                echo "同文件冲突: $mapping" | tee -a "$ANALYSIS_LOG"
                echo "  建议: 保持 '$old_class' 原名不变，或使用 'XGG${old_class}'" | tee -a "$ANALYSIS_LOG"
                ;;
        esac
        echo "" | tee -a "$ANALYSIS_LOG"
    done
else
    echo "✅ 未发现映射冲突" | tee -a "$ANALYSIS_LOG"
fi

echo "=== 分析完成 ===" | tee -a "$ANALYSIS_LOG"
echo "详细分析结果已保存到: $ANALYSIS_LOG" | tee -a "$ANALYSIS_LOG"

if [[ $conflicts -gt 0 ]]; then
    echo ""
    echo "⚠️  发现 $conflicts 个冲突，需要修复映射文件后再继续"
    exit 1
else
    echo ""
    echo "✅ 映射文件分析完成，未发现冲突"
    exit 0
fi
