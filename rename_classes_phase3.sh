#!/bin/bash

# 第三阶段：更新全局引用
echo "=== 第三阶段：更新全局引用 ==="

MAPPING_FILE="need_rename_mapping.txt"
LOG_FILE="phase3_rename_log.txt"

# 清空日志文件
> "$LOG_FILE"

if [[ ! -f "$MAPPING_FILE" ]]; then
    echo "错误：映射文件 $MAPPING_FILE 不存在"
    exit 1
fi

echo "开始更新全局引用..."
echo "映射文件: $MAPPING_FILE"
echo "日志文件: $LOG_FILE"
echo ""

# 统计变量
total_mappings=0
success_mappings=0
error_mappings=0

# 读取映射文件，处理每个类名重命名
while IFS=' -> ' read -r old_class new_class; do
    if [[ -n "$old_class" && -n "$new_class" && "$old_class" != "$new_class" ]]; then
        ((total_mappings++))
        
        echo "处理类名映射: $old_class -> $new_class" | tee -a "$LOG_FILE"
        
        # 搜索所有引用该类的文件（排除备份文件和第三方库）
        files_with_references=$(grep -r -l "\b${old_class}\b" YBVideo/ --include="*.h" --include="*.m" --include="*.mm" --exclude="*.backup" 2>/dev/null | grep -v "Pods/" | grep -v "三方_SDK" | head -50)
        
        if [[ -n "$files_with_references" ]]; then
            echo "  找到引用文件:" | tee -a "$LOG_FILE"
            echo "$files_with_references" | while read -r file; do
                echo "    $file" | tee -a "$LOG_FILE"
            done
            
            # 对每个文件进行替换
            echo "$files_with_references" | while read -r file; do
                if [[ -f "$file" ]]; then
                    # 备份文件
                    cp "$file" "${file}.phase3.backup"
                    
                    # 使用sed进行替换，只替换完整的单词
                    if sed -E "s/\b${old_class}\b/${new_class}/g" "$file" > "${file}.tmp"; then
                        mv "${file}.tmp" "$file"
                        echo "    ✅ 更新成功: $file" | tee -a "$LOG_FILE"
                    else
                        echo "    ❌ 更新失败: $file" | tee -a "$LOG_FILE"
                        # 恢复备份
                        mv "${file}.phase3.backup" "$file"
                    fi
                fi
            done
            
            ((success_mappings++))
        else
            echo "  未找到引用该类的文件" | tee -a "$LOG_FILE"
            ((success_mappings++))
        fi
        
        echo "" | tee -a "$LOG_FILE"
    fi
done < "$MAPPING_FILE"

echo "=== 第三阶段完成 ==="
echo "总处理映射数: $total_mappings"
echo "成功处理: $success_mappings"
echo "失败: $error_mappings"
echo "详细日志: $LOG_FILE"

if [[ $error_mappings -gt 0 ]]; then
    echo ""
    echo "⚠️  有 $error_mappings 个映射处理失败，请检查日志文件"
    exit 1
else
    echo ""
    echo "✅ 第三阶段全部完成！"
fi
