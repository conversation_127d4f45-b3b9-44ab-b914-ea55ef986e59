#!/bin/bash

# 过滤出只需要修改的业务代码文件
# 排除第三方SDK、框架等

echo "=== 过滤业务代码文件 ==="

INPUT_FILE="class_filename_inconsistency_report.txt"
BUSINESS_REPORT="business_code_inconsistency_report.txt"
BUSINESS_MAPPING="business_code_rename_mapping.txt"

# 清空输出文件
> "$BUSINESS_REPORT"
> "$BUSINESS_MAPPING"

# 需要排除的路径模式
EXCLUDE_PATTERNS=(
    "三方_SDK"
    "QMapKit.framework"
    "QMapSearchKit.framework" 
    "MHBeautySDK.framework"
    "美狐sdk"
    "MJExtension"
    "sbjson"
    "V8HorizontalPickerView"
    "HMSegmentedControl"
    "PreviewPicture"
    "ChatImageBubble"
    "HMPhotoPicker"
    "RecordView"
    "External"
    "CWStar"
    "aliPay"
    "百度语音"
    "极光消息"
    "腾讯消息"
    "ZZCircleProgress"
    "XLProgress"
    "Categories"
    "Vendor"
)

echo "正在过滤第三方SDK和框架文件..."

# 读取原始报告文件并过滤
while IFS= read -r line; do
    if [[ "$line" == "不一致:"* ]]; then
        # 提取文件路径
        file_path=$(echo "$line" | sed 's/不一致: //')
        
        # 检查是否需要排除
        should_exclude=false
        for pattern in "${EXCLUDE_PATTERNS[@]}"; do
            if [[ "$file_path" == *"$pattern"* ]]; then
                should_exclude=true
                break
            fi
        done
        
        # 如果不需要排除，则添加到业务代码报告中
        if [[ "$should_exclude" == false ]]; then
            echo "$line" >> "$BUSINESS_REPORT"
            # 读取接下来的4行（文件名、类名、类型、空行）
            read -r filename_line
            read -r classname_line  
            read -r type_line
            read -r empty_line
            
            echo "$filename_line" >> "$BUSINESS_REPORT"
            echo "$classname_line" >> "$BUSINESS_REPORT"
            echo "$type_line" >> "$BUSINESS_REPORT"
            echo "$empty_line" >> "$BUSINESS_REPORT"
            
            # 提取类名映射信息
            filename=$(echo "$filename_line" | sed 's/  文件名: //')
            classname=$(echo "$classname_line" | sed 's/  类名: //')
            echo "$classname -> $filename" >> "$BUSINESS_MAPPING"
        else
            # 跳过这个条目的其他行
            read -r filename_line
            read -r classname_line  
            read -r type_line
            read -r empty_line
        fi
    fi
done < "$INPUT_FILE"

# 统计结果
business_count=$(grep -c "不一致:" "$BUSINESS_REPORT")
total_count=$(grep -c "不一致:" "$INPUT_FILE")
excluded_count=$((total_count - business_count))

echo ""
echo "=== 过滤结果 ==="
echo "原始不一致文件数: $total_count"
echo "排除第三方文件数: $excluded_count"  
echo "需要修改的业务文件数: $business_count"
echo ""
echo "业务代码报告: $BUSINESS_REPORT"
echo "业务代码映射: $BUSINESS_MAPPING"

if [[ $business_count -gt 0 ]]; then
    echo ""
    echo "=== 业务代码不一致文件预览 ==="
    head -20 "$BUSINESS_REPORT"
    echo "..."
    echo "(更多内容请查看完整报告文件)"
fi
