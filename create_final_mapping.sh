#!/bin/bash

# 创建最终的正确映射文件
echo "=== 创建最终映射文件 ==="

INPUT_REPORT="filtered_inconsistency_report.txt"
FINAL_MAPPING="final_rename_mapping.txt"

# 清空输出文件
> "$FINAL_MAPPING"

echo "正在生成最终的类名映射..."

# 读取过滤后的报告文件并生成正确的映射
while IFS= read -r line; do
    if [[ "$line" == "不一致:"* ]]; then
        # 读取接下来的3行
        read -r filename_line
        read -r classname_line  
        read -r type_line
        read -r empty_line
        
        # 提取文件名和类名
        filename=$(echo "$filename_line" | sed 's/  文件名: //')
        full_classname=$(echo "$classname_line" | sed 's/  类名: //')
        
        # 从完整类名中提取实际的类名（去掉@interface/@implementation和继承部分）
        if [[ "$full_classname" == *"@interface"* ]]; then
            # 提取@interface后面的类名，去掉继承部分
            actual_classname=$(echo "$full_classname" | sed -E 's/@interface\s+([A-Za-z_][A-Za-z0-9_]*).*/\1/')
        elif [[ "$full_classname" == *"@implementation"* ]]; then
            # 提取@implementation后面的类名
            actual_classname=$(echo "$full_classname" | sed -E 's/@implementation\s+([A-Za-z_][A-Za-z0-9_]*).*/\1/')
        else
            actual_classname="$full_classname"
        fi
        
        # 只有当类名和文件名不同时才需要重命名
        if [[ "$actual_classname" != "$filename" ]]; then
            # 生成映射：原类名 -> 新类名（文件名）
            echo "$actual_classname -> $filename" >> "$FINAL_MAPPING"
        fi
    fi
done < "$INPUT_REPORT"

# 统计结果
mapping_count=$(wc -l < "$FINAL_MAPPING")

echo ""
echo "=== 最终映射完成 ==="
echo "需要重命名的类数量: $mapping_count"
echo "映射文件: $FINAL_MAPPING"
echo ""
echo "=== 映射示例 ==="
head -15 "$FINAL_MAPPING"
echo "..."

# 按模块分类统计
echo ""
echo "=== 按模块统计 ==="
echo "录制编辑发布模块:"
grep -c "录制_编辑_发布" "$INPUT_REPORT" || echo "0"
echo "店铺模块:"
grep -c "店铺" "$INPUT_REPORT" || echo "0"
echo "直播模块:"
grep -c "直播模块" "$INPUT_REPORT" || echo "0"
echo "消息模块:"
grep -c "消息" "$INPUT_REPORT" || echo "0"
echo "个人中心模块:"
grep -c "个人中心" "$INPUT_REPORT" || echo "0"
echo "功能模块:"
grep -c "功能" "$INPUT_REPORT" || echo "0"
