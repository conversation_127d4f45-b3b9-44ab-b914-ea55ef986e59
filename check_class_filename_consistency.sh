#!/bin/bash

# 检查Objective-C类名与文件名一致性的脚本
# 作者: AI Assistant
# 日期: $(date)

echo "=== YBVideo项目类名与文件名一致性检查 ==="
echo "开始时间: $(date)"
echo ""

# 创建结果文件
RESULT_FILE="class_filename_inconsistency_report.txt"
MAPPING_FILE="class_rename_mapping.txt"

# 清空之前的结果文件
> "$RESULT_FILE"
> "$MAPPING_FILE"

echo "扫描范围: YBVideo目录下所有.h和.m文件（排除Pods目录）"
echo "结果将保存到: $RESULT_FILE"
echo "重命名映射将保存到: $MAPPING_FILE"
echo ""

# 计数器
total_files=0
inconsistent_files=0

# 函数：检查.h文件中的@interface声明
check_header_file() {
    local file_path="$1"
    local filename=$(basename "$file_path" .h)
    
    # 跳过Category文件（包含+号）
    if [[ "$filename" == *"+"* ]]; then
        return
    fi
    
    # 查找@interface声明
    local interface_class=$(grep -E "^@interface\s+\w+" "$file_path" | head -1 | sed -E 's/@interface\s+([A-Za-z_][A-Za-z0-9_]*).*/\1/')
    
    if [[ -n "$interface_class" && "$interface_class" != "$filename" ]]; then
        echo "不一致: $file_path" >> "$RESULT_FILE"
        echo "  文件名: $filename" >> "$RESULT_FILE"
        echo "  类名: $interface_class" >> "$RESULT_FILE"
        echo "  类型: @interface声明" >> "$RESULT_FILE"
        echo "" >> "$RESULT_FILE"
        
        # 添加到重命名映射
        echo "$interface_class -> $filename" >> "$MAPPING_FILE"
        
        echo "发现不一致: $file_path ($interface_class -> $filename)"
        ((inconsistent_files++))
    fi
}

# 函数：检查.m文件中的@implementation声明
check_implementation_file() {
    local file_path="$1"
    local filename=$(basename "$file_path" .m)
    
    # 跳过Category文件（包含+号）
    if [[ "$filename" == *"+"* ]]; then
        return
    fi
    
    # 查找@implementation声明
    local impl_class=$(grep -E "^@implementation\s+\w+" "$file_path" | head -1 | sed -E 's/@implementation\s+([A-Za-z_][A-Za-z0-9_]*).*/\1/')
    
    if [[ -n "$impl_class" && "$impl_class" != "$filename" ]]; then
        echo "不一致: $file_path" >> "$RESULT_FILE"
        echo "  文件名: $filename" >> "$RESULT_FILE"
        echo "  类名: $impl_class" >> "$RESULT_FILE"
        echo "  类型: @implementation声明" >> "$RESULT_FILE"
        echo "" >> "$RESULT_FILE"
        
        # 添加到重命名映射
        echo "$impl_class -> $filename" >> "$MAPPING_FILE"
        
        echo "发现不一致: $file_path ($impl_class -> $filename)"
        ((inconsistent_files++))
    fi
}

echo "正在扫描.h文件..."
# 扫描所有.h文件
while IFS= read -r -d '' file; do
    ((total_files++))
    check_header_file "$file"
done < <(find YBVideo -name "*.h" -not -path "*/Pods/*" -not -path "*/.*" -print0)

echo "正在扫描.m文件..."
# 扫描所有.m文件
while IFS= read -r -d '' file; do
    ((total_files++))
    check_implementation_file "$file"
done < <(find YBVideo -name "*.m" -not -path "*/Pods/*" -not -path "*/.*" -print0)

echo ""
echo "=== 扫描完成 ==="
echo "总文件数: $total_files"
echo "不一致文件数: $inconsistent_files"
echo "结束时间: $(date)"

if [[ $inconsistent_files -gt 0 ]]; then
    echo ""
    echo "发现 $inconsistent_files 个不一致的文件，详细信息请查看: $RESULT_FILE"
    echo "重命名映射表请查看: $MAPPING_FILE"
    echo ""
    echo "=== 不一致文件摘要 ==="
    cat "$RESULT_FILE"
else
    echo ""
    echo "✅ 所有文件的类名与文件名都一致！"
fi
