开始回滚第三阶段的更改...
找到备份文件，开始回滚...

回滚文件 1: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.h
  ✅ 回滚成功
回滚文件 2: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
  ✅ 回滚成功
回滚文件 3: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.m
  ✅ 回滚成功
回滚文件 4: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
  ✅ 回滚成功
回滚文件 5: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.m
  ✅ 回滚成功
回滚文件 6: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m
  ✅ 回滚成功
回滚文件 7: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
  ✅ 回滚成功
回滚文件 8: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.h
  ✅ 回滚成功
回滚文件 9: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
  ✅ 回滚成功
回滚文件 10: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h
  ✅ 回滚成功
回滚文件 11: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h
  ✅ 回滚成功
回滚文件 12: YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoPreview.h
  ✅ 回滚成功
回滚文件 13: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h
  ✅ 回滚成功
回滚文件 14: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
  ✅ 回滚成功
回滚文件 15: YBVideo//录制_编辑_发布/UGCEditor/Views/TCTextAddView.h
  ✅ 回滚成功
回滚文件 16: YBVideo//录制_编辑_发布/UGCEditor/Views/TCBottomTabBar.h
  ✅ 回滚成功
回滚文件 17: YBVideo//录制_编辑_发布/UGCEditor/Views/TCFilterSettingView.h
  ✅ 回滚成功
回滚文件 18: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
  ✅ 回滚成功
回滚文件 19: YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoCutView.h
  ✅ 回滚成功
回滚文件 20: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.h
  ✅ 回滚成功
回滚文件 21: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.h
  ✅ 回滚成功
回滚文件 22: YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoTextFiled.h
  ✅ 回滚成功
回滚文件 23: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
  ✅ 回滚成功
回滚文件 24: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
  ✅ 回滚成功
回滚文件 25: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.h
  ✅ 回滚成功
回滚文件 26: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.m
  ✅ 回滚成功
回滚文件 27: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
  ✅ 回滚成功
回滚文件 28: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.h
  ✅ 回滚成功
回滚文件 29: YBVideo//录制_编辑_发布/Record/views/TXBaseBeautyView.m
  ✅ 回滚成功
回滚文件 30: YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.h
  ✅ 回滚成功
回滚文件 31: YBVideo//录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m
  ✅ 回滚成功
回滚文件 32: YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m
  ✅ 回滚成功
回滚文件 33: YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.h
  ✅ 回滚成功
回滚文件 34: YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.m
  ✅ 回滚成功
回滚文件 35: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h
  ✅ 回滚成功
回滚文件 36: YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.m
  ✅ 回滚成功
回滚文件 37: YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.h
  ✅ 回滚成功
回滚文件 38: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
  ✅ 回滚成功
回滚文件 39: YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.m
  ✅ 回滚成功
回滚文件 40: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.h
  ✅ 回滚成功
回滚文件 41: YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.h
  ✅ 回滚成功
回滚文件 42: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
  ✅ 回滚成功
回滚文件 43: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h
  ✅ 回滚成功
回滚文件 44: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m
  ✅ 回滚成功
回滚文件 45: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.h
  ✅ 回滚成功
回滚文件 46: YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m
  ✅ 回滚成功
回滚文件 47: YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.h
  ✅ 回滚成功
回滚文件 48: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
  ✅ 回滚成功
回滚文件 49: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
  ✅ 回滚成功
回滚文件 50: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
  ✅ 回滚成功
回滚文件 51: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
  ✅ 回滚成功
回滚文件 52: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.m
  ✅ 回滚成功
回滚文件 53: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h
  ✅ 回滚成功
回滚文件 54: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.h
  ✅ 回滚成功
回滚文件 55: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
  ✅ 回滚成功
回滚文件 56: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
  ✅ 回滚成功
回滚文件 57: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
  ✅ 回滚成功
回滚文件 58: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h
  ✅ 回滚成功
回滚文件 59: YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.h
  ✅ 回滚成功
回滚文件 60: YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.m
  ✅ 回滚成功
回滚文件 61: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m
  ✅ 回滚成功
回滚文件 62: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
  ✅ 回滚成功
回滚文件 63: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.h
  ✅ 回滚成功
回滚文件 64: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
  ✅ 回滚成功
回滚文件 65: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.h
  ✅ 回滚成功
回滚文件 66: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.h
  ✅ 回滚成功
回滚文件 67: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
  ✅ 回滚成功
回滚文件 68: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.h
  ✅ 回滚成功
回滚文件 69: YBVideo//其他类/TCBaseAppDelegate.m
  ✅ 回滚成功
回滚文件 70: YBVideo//其他类/XGGYBBaseAppDelegate.m
  ✅ 回滚成功
回滚文件 71: YBVideo//其他类/XGGYBBaseViewController.m
  ✅ 回滚成功
回滚文件 72: YBVideo//其他类/TCNavigationController.h
  ✅ 回滚成功
回滚文件 73: YBVideo//其他类/XGGYBNavigationController.h
  ✅ 回滚成功
回滚文件 74: YBVideo//其他类/XGGcityDefault.h
  ✅ 回滚成功
回滚文件 75: YBVideo//其他类/XGGAppDelegate.h
  ✅ 回滚成功
回滚文件 76: YBVideo//其他类/XGGAppDelegate.m
  ✅ 回滚成功
回滚文件 77: YBVideo//其他类/Config.h
  ✅ 回滚成功
回滚文件 78: YBVideo//其他类/XGGYBNavigationController.m
  ✅ 回滚成功
回滚文件 79: YBVideo//其他类/XGGRKLBSManager.h
  ✅ 回滚成功
回滚文件 80: YBVideo//其他类/XGGYBBaseAppDelegate.h
  ✅ 回滚成功
回滚文件 81: YBVideo//其他类/sproutCommon.h
  ✅ 回滚成功
回滚文件 82: YBVideo//其他类/common.h
  ✅ 回滚成功
回滚文件 83: YBVideo//其他类/XGGYBBaseViewController.h
  ✅ 回滚成功
回滚文件 84: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h
  ✅ 回滚成功
回滚文件 85: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
  ✅ 回滚成功
回滚文件 86: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
  ✅ 回滚成功
回滚文件 87: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
  ✅ 回滚成功
回滚文件 88: YBVideo//首页/XGGYBHomeViewController.m
  ✅ 回滚成功
回滚文件 89: YBVideo//首页/推荐/XGGYBLookVideoVC.h
  ✅ 回滚成功
回滚文件 90: YBVideo//首页/推荐/XGGYBLookVideoVC.m
  ✅ 回滚成功
回滚文件 91: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
  ✅ 回滚成功
回滚文件 92: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
  ✅ 回滚成功
回滚文件 93: YBVideo//首页/推荐/view/XGGYBVideoControlView.h
  ✅ 回滚成功
回滚文件 94: YBVideo//首页/推荐/view/XGGYBLookVideoCell.h
  ✅ 回滚成功
回滚文件 95: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.h
  ✅ 回滚成功
回滚文件 96: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.h
  ✅ 回滚成功
回滚文件 97: YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.h
  ✅ 回滚成功
回滚文件 98: YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.m
  ✅ 回滚成功
回滚文件 99: YBVideo//首页/热门-关注-分类/XGGmyVideoV.h
  ✅ 回滚成功
回滚文件 100: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
  ✅ 回滚成功
回滚文件 101: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
  ✅ 回滚成功
回滚文件 102: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.m
  ✅ 回滚成功
回滚文件 103: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.h
  ✅ 回滚成功
回滚文件 104: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
  ✅ 回滚成功
回滚文件 105: YBVideo//首页/XGGYBHomeViewController.h
  ✅ 回滚成功
回滚文件 106: YBVideo//登录注册/XGGDspLoginVC.m
  ✅ 回滚成功
回滚文件 107: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
  ✅ 回滚成功
回滚文件 108: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.h
  ✅ 回滚成功
回滚文件 109: YBVideo//登录注册/国家代号/XGGCountryCodeVC.h
  ✅ 回滚成功
回滚文件 110: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
  ✅ 回滚成功
回滚文件 111: YBVideo//登录注册/XGGDspLoginVC.h
  ✅ 回滚成功
回滚文件 112: YBVideo//main.m
  ✅ 回滚成功
回滚文件 113: YBVideo//XGGNetwork/XGGNetworkManager.m
  ✅ 回滚成功
回滚文件 114: YBVideo//XGGNetwork/XGGNetworkUtils.h
  ✅ 回滚成功
回滚文件 115: YBVideo//XGGNetwork/XGGNetworkManager.h
  ✅ 回滚成功
回滚文件 116: YBVideo//公共方法类/XGGPublicView.m
  ✅ 回滚成功
回滚文件 117: YBVideo//公共方法类/XGGRKUUIDManager.h
  ✅ 回滚成功
回滚文件 118: YBVideo//公共方法类/XGGYBNetworking.h
  ✅ 回滚成功
回滚文件 119: YBVideo//公共方法类/XGGPublicObj.h
  ✅ 回滚成功
回滚文件 120: YBVideo//公共方法类/XGGYBShowBigImageView.h
  ✅ 回滚成功
回滚文件 121: YBVideo//公共方法类/XGGiOSNetworking.h
  ✅ 回滚成功
回滚文件 122: YBVideo//公共方法类/XGGYBAlertView.m
  ✅ 回滚成功
回滚文件 123: YBVideo//公共方法类/XGGBGSetting.h
  ✅ 回滚成功
回滚文件 124: YBVideo//公共方法类/XGGRKActionSheet.h
  ✅ 回滚成功
回滚文件 125: YBVideo//公共方法类/XGGRKSysAccess.m
  ✅ 回滚成功
回滚文件 126: YBVideo//公共方法类/XGGYBImageView.m
  ✅ 回滚成功
回滚文件 127: YBVideo//公共方法类/XGGYBProgressObj.h
  ✅ 回滚成功
回滚文件 128: YBVideo//公共方法类/XGGYBProgressObj.m
  ✅ 回滚成功
回滚文件 129: YBVideo//公共方法类/XGGRKActionSheet.m
  ✅ 回滚成功
回滚文件 130: YBVideo//公共方法类/XGGBGSetting.m
  ✅ 回滚成功
回滚文件 131: YBVideo//公共方法类/XGGiOSNetworking.m
  ✅ 回滚成功
回滚文件 132: YBVideo//公共方法类/XGGYBAlertView.h
  ✅ 回滚成功
回滚文件 133: YBVideo//公共方法类/XGGRKSysAccess.h
  ✅ 回滚成功
回滚文件 134: YBVideo//公共方法类/XGGYBImageView.h
  ✅ 回滚成功
回滚文件 135: YBVideo//公共方法类/XGGPublicObj.m
  ✅ 回滚成功
回滚文件 136: YBVideo//公共方法类/XGGYBShowBigImageView.m
  ✅ 回滚成功
回滚文件 137: YBVideo//公共方法类/XGGRKUUIDManager.m
  ✅ 回滚成功
回滚文件 138: YBVideo//公共方法类/XGGPublicView.h
  ✅ 回滚成功
回滚文件 139: YBVideo//公共方法类/XGGYBNetworking.m
  ✅ 回滚成功
回滚文件 140: YBVideo//引导页/XGGGuideViewController.h
  ✅ 回滚成功
回滚文件 141: YBVideo//引导页/XGGGuideViewController.m
  ✅ 回滚成功
回滚文件 142: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
  ✅ 回滚成功
回滚文件 143: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
  ✅ 回滚成功
回滚文件 144: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.m
  ✅ 回滚成功
回滚文件 145: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.m
  ✅ 回滚成功
回滚文件 146: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
  ✅ 回滚成功
回滚文件 147: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h
  ✅ 回滚成功
回滚文件 148: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h
  ✅ 回滚成功
回滚文件 149: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h
  ✅ 回滚成功
回滚文件 150: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
  ✅ 回滚成功
回滚文件 151: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
  ✅ 回滚成功
回滚文件 152: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h
  ✅ 回滚成功
回滚文件 153: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.h
  ✅ 回滚成功
回滚文件 154: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
  ✅ 回滚成功
回滚文件 155: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h
  ✅ 回滚成功
回滚文件 156: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h
  ✅ 回滚成功
回滚文件 157: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.m
  ✅ 回滚成功
回滚文件 158: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.m
  ✅ 回滚成功
回滚文件 159: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.m
  ✅ 回滚成功
回滚文件 160: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
  ✅ 回滚成功
回滚文件 161: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
  ✅ 回滚成功
回滚文件 162: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
  ✅ 回滚成功
回滚文件 163: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
  ✅ 回滚成功
回滚文件 164: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
  ✅ 回滚成功
回滚文件 165: YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.m
  ✅ 回滚成功
回滚文件 166: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
  ✅ 回滚成功
回滚文件 167: YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.h
  ✅ 回滚成功
回滚文件 168: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.h
  ✅ 回滚成功
回滚文件 169: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
  ✅ 回滚成功
回滚文件 170: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
  ✅ 回滚成功
回滚文件 171: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.h
  ✅ 回滚成功
回滚文件 172: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
  ✅ 回滚成功
回滚文件 173: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h
  ✅ 回滚成功
回滚文件 174: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
  ✅ 回滚成功
回滚文件 175: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h
  ✅ 回滚成功
回滚文件 176: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
  ✅ 回滚成功
回滚文件 177: YBVideo//店铺/买家端/评价/CWStar/CWStarRateView.h
  ✅ 回滚成功
回滚文件 178: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
  ✅ 回滚成功
回滚文件 179: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
  ✅ 回滚成功
回滚文件 180: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h
  ✅ 回滚成功
回滚文件 181: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.h
  ✅ 回滚成功
回滚文件 182: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m
  ✅ 回滚成功
回滚文件 183: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
  ✅ 回滚成功
回滚文件 184: YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.h
  ✅ 回滚成功
回滚文件 185: YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.m
  ✅ 回滚成功
回滚文件 186: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h
  ✅ 回滚成功
回滚文件 187: YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m
  ✅ 回滚成功
回滚文件 188: YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.m
  ✅ 回滚成功
回滚文件 189: YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.h
  ✅ 回滚成功
回滚文件 190: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.m
  ✅ 回滚成功
回滚文件 191: YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.h
  ✅ 回滚成功
回滚文件 192: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.m
  ✅ 回滚成功
回滚文件 193: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.m
  ✅ 回滚成功
回滚文件 194: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.h
  ✅ 回滚成功
回滚文件 195: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
  ✅ 回滚成功
回滚文件 196: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.h
  ✅ 回滚成功
回滚文件 197: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
  ✅ 回滚成功
回滚文件 198: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.h
  ✅ 回滚成功
回滚文件 199: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
  ✅ 回滚成功
回滚文件 200: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.m
  ✅ 回滚成功
回滚文件 201: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
  ✅ 回滚成功
回滚文件 202: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
  ✅ 回滚成功
回滚文件 203: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.h
  ✅ 回滚成功
回滚文件 204: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
  ✅ 回滚成功
回滚文件 205: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.m
  ✅ 回滚成功
回滚文件 206: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
  ✅ 回滚成功
回滚文件 207: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.h
  ✅ 回滚成功
回滚文件 208: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.h
  ✅ 回滚成功
回滚文件 209: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h
  ✅ 回滚成功
回滚文件 210: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.h
  ✅ 回滚成功
回滚文件 211: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.m
  ✅ 回滚成功
回滚文件 212: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
  ✅ 回滚成功
回滚文件 213: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.h
  ✅ 回滚成功
回滚文件 214: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.h
  ✅ 回滚成功
回滚文件 215: YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.m
  ✅ 回滚成功
回滚文件 216: YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.h
  ✅ 回滚成功
回滚文件 217: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.h
  ✅ 回滚成功
回滚文件 218: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
  ✅ 回滚成功
回滚文件 219: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
  ✅ 回滚成功
回滚文件 220: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
  ✅ 回滚成功
回滚文件 221: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
  ✅ 回滚成功
回滚文件 222: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
  ✅ 回滚成功
回滚文件 223: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.h
  ✅ 回滚成功
回滚文件 224: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.h
  ✅ 回滚成功
回滚文件 225: YBVideo//店铺/买家端/保证金/XGGBondViewController.h
  ✅ 回滚成功
回滚文件 226: YBVideo//店铺/买家端/保证金/XGGBondViewController.m
  ✅ 回滚成功
回滚文件 227: YBVideo//店铺/买家端/我的地址/XGGAddressModel.m
  ✅ 回滚成功
回滚文件 228: YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
  ✅ 回滚成功
回滚文件 229: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
  ✅ 回滚成功
回滚文件 230: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
  ✅ 回滚成功
回滚文件 231: YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.h
  ✅ 回滚成功
回滚文件 232: YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
  ✅ 回滚成功
回滚文件 233: YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.m
  ✅ 回滚成功
回滚文件 234: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
  ✅ 回滚成功
回滚文件 235: YBVideo//店铺/买家端/我的地址/XGGAddressCell.m
  ✅ 回滚成功
回滚文件 236: YBVideo//店铺/买家端/我的地址/XGGAddressModel.h
  ✅ 回滚成功
回滚文件 237: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.h
  ✅ 回滚成功
回滚文件 238: YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.m
  ✅ 回滚成功
回滚文件 239: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h
  ✅ 回滚成功
回滚文件 240: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
  ✅ 回滚成功
回滚文件 241: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
  ✅ 回滚成功
回滚文件 242: YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.h
  ✅ 回滚成功
回滚文件 243: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.h
  ✅ 回滚成功
回滚文件 244: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
  ✅ 回滚成功
回滚文件 245: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h
  ✅ 回滚成功
回滚文件 246: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.h
  ✅ 回滚成功
回滚文件 247: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.m
  ✅ 回滚成功
回滚文件 248: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
  ✅ 回滚成功
回滚文件 249: YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.m
  ✅ 回滚成功
回滚文件 250: YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.h
  ✅ 回滚成功
回滚文件 251: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJCoding.h
  ✅ 回滚成功
回滚文件 252: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJProperty.h
  ✅ 回滚成功
回滚文件 253: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJKeyValue.m
  ✅ 回滚成功
回滚文件 254: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJClass.m
  ✅ 回滚成功
回滚文件 255: YBVideo//店铺/买家端/三方/MJExtension/MJFoundation.m
  ✅ 回滚成功
回滚文件 256: YBVideo//店铺/买家端/三方/MJExtension/MJExtension.h
  ✅ 回滚成功
回滚文件 257: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJKeyValue.h
  ✅ 回滚成功
回滚文件 258: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJClass.h
  ✅ 回滚成功
回滚文件 259: YBVideo//店铺/买家端/三方/MJExtension/MJPropertyType.h
  ✅ 回滚成功
回滚文件 260: YBVideo//店铺/买家端/三方/MJExtension/MJFoundation.h
  ✅ 回滚成功
回滚文件 261: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJProperty.m
  ✅ 回滚成功
回滚文件 262: YBVideo//店铺/买家端/三方/MJExtension/MJProperty.h
  ✅ 回滚成功
回滚文件 263: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJCoding.m
  ✅ 回滚成功
回滚文件 264: YBVideo//店铺/买家端/三方/MJExtension/MJPropertyKey.h
  ✅ 回滚成功
回滚文件 265: YBVideo//店铺/买家端/我的订单/XGGOrderListCell.h
  ✅ 回滚成功
回滚文件 266: YBVideo//店铺/买家端/我的订单/XGGOrderModel.h
  ✅ 回滚成功
回滚文件 267: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
  ✅ 回滚成功
回滚文件 268: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.h
  ✅ 回滚成功
回滚文件 269: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
  ✅ 回滚成功
回滚文件 270: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.h
  ✅ 回滚成功
回滚文件 271: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m
  ✅ 回滚成功
回滚文件 272: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
  ✅ 回滚成功
回滚文件 273: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
  ✅ 回滚成功
回滚文件 274: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
  ✅ 回滚成功
回滚文件 275: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
  ✅ 回滚成功
回滚文件 276: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
  ✅ 回滚成功
回滚文件 277: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
  ✅ 回滚成功
回滚文件 278: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
  ✅ 回滚成功
回滚文件 279: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
  ✅ 回滚成功
回滚文件 280: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.h
  ✅ 回滚成功
回滚文件 281: YBVideo//店铺/买家端/我的订单/XGGOrderListCell.m
  ✅ 回滚成功
回滚文件 282: YBVideo//店铺/买家端/我的订单/XGGOrderModel.m
  ✅ 回滚成功
回滚文件 283: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
  ✅ 回滚成功
回滚文件 284: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h
  ✅ 回滚成功
回滚文件 285: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.h
  ✅ 回滚成功
回滚文件 286: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
  ✅ 回滚成功
回滚文件 287: YBVideo//店铺/小店主页/XGGShopHomeVC.h
  ✅ 回滚成功
回滚文件 288: YBVideo//店铺/小店主页/XGGShopHomeVC.m
  ✅ 回滚成功
回滚文件 289: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
  ✅ 回滚成功
回滚文件 290: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.h
  ✅ 回滚成功
回滚文件 291: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
  ✅ 回滚成功
回滚文件 292: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.h
  ✅ 回滚成功
回滚文件 293: YBVideo//店铺/卖家端/商品管理/XGGCommodityModel.m
  ✅ 回滚成功
回滚文件 294: YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.m
  ✅ 回滚成功
回滚文件 295: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.h
  ✅ 回滚成功
回滚文件 296: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
  ✅ 回滚成功
回滚文件 297: YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h
  ✅ 回滚成功
回滚文件 298: YBVideo//店铺/卖家端/商品管理/XGGCommodityModel.h
  ✅ 回滚成功
回滚文件 299: YBVideo//店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.m
  ✅ 回滚成功
回滚文件 300: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
  ✅ 回滚成功
回滚文件 301: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h
  ✅ 回滚成功
回滚文件 302: YBVideo//店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.h
  ✅ 回滚成功
回滚文件 303: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
  ✅ 回滚成功
回滚文件 304: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h
  ✅ 回滚成功
回滚文件 305: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
  ✅ 回滚成功
回滚文件 306: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
  ✅ 回滚成功
回滚文件 307: YBVideo//店铺/卖家端/订单管理/XGGSellOrderModel.m
  ✅ 回滚成功
回滚文件 308: YBVideo//店铺/卖家端/订单管理/XGGSellOrderModel.h
  ✅ 回滚成功
回滚文件 309: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.h
  ✅ 回滚成功
回滚文件 310: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.h
  ✅ 回滚成功
回滚文件 311: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.h
  ✅ 回滚成功
回滚文件 312: YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.h
  ✅ 回滚成功
回滚文件 313: YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m
  ✅ 回滚成功
回滚文件 314: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
  ✅ 回滚成功
回滚文件 315: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h
  ✅ 回滚成功
回滚文件 316: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.m
  ✅ 回滚成功
回滚文件 317: YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.h
  ✅ 回滚成功
回滚文件 318: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.h
  ✅ 回滚成功
回滚文件 319: YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.m
  ✅ 回滚成功
回滚文件 320: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
  ✅ 回滚成功
回滚文件 321: YBVideo//店铺/卖家端/资质/XGGQualificationsVC.h
  ✅ 回滚成功
回滚文件 322: YBVideo//店铺/卖家端/资质/XGGQualificationsVC.m
  ✅ 回滚成功
回滚文件 323: YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.m
  ✅ 回滚成功
回滚文件 324: YBVideo//店铺/卖家端/价格与库存/XGGStockView.m
  ✅ 回滚成功
回滚文件 325: YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.h
  ✅ 回滚成功
回滚文件 326: YBVideo//店铺/卖家端/价格与库存/XGGStockView.h
  ✅ 回滚成功
回滚文件 327: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
  ✅ 回滚成功
回滚文件 328: YBVideo//店铺/卖家端/账单管理/XGGBillCell.m
  ✅ 回滚成功
回滚文件 329: YBVideo//店铺/卖家端/账单管理/XGGBillCell.h
  ✅ 回滚成功
回滚文件 330: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.h
  ✅ 回滚成功
回滚文件 331: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.h
  ✅ 回滚成功
回滚文件 332: YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.h
  ✅ 回滚成功
回滚文件 333: YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m
  ✅ 回滚成功
回滚文件 334: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h
  ✅ 回滚成功
回滚文件 335: YBVideo//店铺/卖家端/添加商品/子页面view/XGGStandardsView.m
  ✅ 回滚成功
回滚文件 336: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
  ✅ 回滚成功
回滚文件 337: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.h
  ✅ 回滚成功
回滚文件 338: YBVideo//店铺/卖家端/添加商品/子页面view/XGGStandardsView.h
  ✅ 回滚成功
回滚文件 339: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
  ✅ 回滚成功
回滚文件 340: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
  ✅ 回滚成功
回滚文件 341: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
  ✅ 回滚成功
回滚文件 342: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.h
  ✅ 回滚成功
回滚文件 343: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsModel.m
  ✅ 回滚成功
回滚文件 344: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
  ✅ 回滚成功
回滚文件 345: YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.h
  ✅ 回滚成功
回滚文件 346: YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.m
  ✅ 回滚成功
回滚文件 347: YBVideo//店铺/卖家端/我的店铺/XGGshopCell.h
  ✅ 回滚成功
回滚文件 348: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.h
  ✅ 回滚成功
回滚文件 349: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.h
  ✅ 回滚成功
回滚文件 350: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
  ✅ 回滚成功
回滚文件 351: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
  ✅ 回滚成功
回滚文件 352: YBVideo//店铺/卖家端/我的店铺/XGGshopCell.m
  ✅ 回滚成功
回滚文件 353: YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.h
  ✅ 回滚成功
回滚文件 354: YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
  ✅ 回滚成功
回滚文件 355: YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
  ✅ 回滚成功
回滚文件 356: YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.h
  ✅ 回滚成功
回滚文件 357: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsModel.h
  ✅ 回滚成功
回滚文件 358: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.h
  ✅ 回滚成功
回滚文件 359: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.h
  ✅ 回滚成功
回滚文件 360: YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.h
  ✅ 回滚成功
回滚文件 361: YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.h
  ✅ 回滚成功
回滚文件 362: YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.m
  ✅ 回滚成功
回滚文件 363: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
  ✅ 回滚成功
回滚文件 364: YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.m
  ✅ 回滚成功
回滚文件 365: YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.m
  ✅ 回滚成功
回滚文件 366: YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.h
  ✅ 回滚成功
回滚文件 367: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.h
  ✅ 回滚成功
回滚文件 368: YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.h
  ✅ 回滚成功
回滚文件 369: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m
  ✅ 回滚成功
回滚文件 370: YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.m
  ✅ 回滚成功
回滚文件 371: YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.h
  ✅ 回滚成功
回滚文件 372: YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
  ✅ 回滚成功
回滚文件 373: YBVideo//缓存/权限(tab中间按钮)/XGGPower.h
  ✅ 回滚成功
回滚文件 374: YBVideo//缓存/定位/XGGRKLBSManager.m
  ✅ 回滚成功
回滚文件 375: YBVideo//缓存/定位/XGGcityDefault.h
  ✅ 回滚成功
回滚文件 376: YBVideo//缓存/定位/XGGRKLBSManager.h
  ✅ 回滚成功
回滚文件 377: YBVideo//缓存/配置信息/XGGcommon.m
  ✅ 回滚成功
回滚文件 378: YBVideo//缓存/配置信息/XGGcommon.h
  ✅ 回滚成功
回滚文件 379: YBVideo//缓存/个人信息/XGGConfig.m
  ✅ 回滚成功
回滚文件 380: YBVideo//缓存/个人信息/XGGConfig.h
  ✅ 回滚成功
回滚文件 381: YBVideo//底部导航/XGGYBTabBar.m
  ✅ 回滚成功
回滚文件 382: YBVideo//底部导航/XGGYBTabBarController.h
  ✅ 回滚成功
回滚文件 383: YBVideo//底部导航/XGGYBTabBarController.m
  ✅ 回滚成功
回滚文件 384: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
  ✅ 回滚成功
回滚文件 385: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.h
  ✅ 回滚成功
回滚文件 386: YBVideo//底部导航/XGGYBTabBar.h
  ✅ 回滚成功
回滚文件 387: YBVideo//消息/XGGOrderMessageVC.h
  ✅ 回滚成功
回滚文件 388: YBVideo//消息/XGGMessageFansVC.m
  ✅ 回滚成功
回滚文件 389: YBVideo//消息/XGGchatmessageCell.h
  ✅ 回滚成功
回滚文件 390: YBVideo//消息/XGGOrderMessageModel.h
  ✅ 回滚成功
回滚文件 391: YBVideo//消息/XGGMsgSysVC.m
  ✅ 回滚成功
回滚文件 392: YBVideo//消息/XGGMsgTopPubVC.h
  ✅ 回滚成功
回滚文件 393: YBVideo//消息/选择联系人/XGGSelPeopleCell.m
  ✅ 回滚成功
回滚文件 394: YBVideo//消息/选择联系人/XGGSelPeopleV.m
  ✅ 回滚成功
回滚文件 395: YBVideo//消息/选择联系人/XGGSelPeopleCell.h
  ✅ 回滚成功
回滚文件 396: YBVideo//消息/选择联系人/XGGSelPeopleV.h
  ✅ 回滚成功
回滚文件 397: YBVideo//消息/MessageVC.m
  ✅ 回滚成功
回滚文件 398: YBVideo//消息/model/XGGMsgTopPubModel.h
  ✅ 回滚成功
回滚文件 399: YBVideo//消息/model/XGGMsgSysModel.m
  ✅ 回滚成功
回滚文件 400: YBVideo//消息/model/XGGMessageListModel.m
  ✅ 回滚成功
回滚文件 401: YBVideo//消息/model/XGGMessageFansModel.m
  ✅ 回滚成功
回滚文件 402: YBVideo//消息/model/XGGMessageFansModel.h
  ✅ 回滚成功
回滚文件 403: YBVideo//消息/model/XGGMsgSysModel.h
  ✅ 回滚成功
回滚文件 404: YBVideo//消息/model/XGGMessageListModel.h
  ✅ 回滚成功
回滚文件 405: YBVideo//消息/model/XGGMsgTopPubModel.m
  ✅ 回滚成功
回滚文件 406: YBVideo//消息/view/XGGMessageHeaderV.h
  ✅ 回滚成功
回滚文件 407: YBVideo//消息/view/MessageCell.m
  ✅ 回滚成功
回滚文件 408: YBVideo//消息/view/XGGMsgSysCell.m
  ✅ 回滚成功
回滚文件 409: YBVideo//消息/view/XGGMessageFansCell.h
  ✅ 回滚成功
回滚文件 410: YBVideo//消息/view/XGGMsgTopPubCell.h
  ✅ 回滚成功
回滚文件 411: YBVideo//消息/view/XGGMessageListCell.m
  ✅ 回滚成功
回滚文件 412: YBVideo//消息/view/XGGMsgTopPubCell.m
  ✅ 回滚成功
回滚文件 413: YBVideo//消息/view/XGGMessageListCell.h
  ✅ 回滚成功
回滚文件 414: YBVideo//消息/view/XGGMessageFansCell.m
  ✅ 回滚成功
回滚文件 415: YBVideo//消息/view/XGGMessageHeaderV.m
  ✅ 回滚成功
回滚文件 416: YBVideo//消息/view/XGGMsgSysCell.h
  ✅ 回滚成功
回滚文件 417: YBVideo//消息/XGGMsgTopPubVC.m
  ✅ 回滚成功
回滚文件 418: YBVideo//消息/XGGOrderMessageVC.m
  ✅ 回滚成功
回滚文件 419: YBVideo//消息/XGGMessageFansVC.h
  ✅ 回滚成功
回滚文件 420: YBVideo//消息/XGGchatmessageCell.m
  ✅ 回滚成功
回滚文件 421: YBVideo//消息/XGGMsgSysVC.h
  ✅ 回滚成功
回滚文件 422: YBVideo//消息/XGGOrderMessageModel.m
  ✅ 回滚成功
回滚文件 423: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
  ✅ 回滚成功
回滚文件 424: YBVideo//直播模块/用户端相关/XGGYBPlayVC.h
  ✅ 回滚成功
回滚文件 425: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
  ✅ 回滚成功
回滚文件 426: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h
  ✅ 回滚成功
回滚文件 427: YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.h
  ✅ 回滚成功
回滚文件 428: YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.m
  ✅ 回滚成功
回滚文件 429: YBVideo//直播模块/直播列表/XGGYBLiveListVC.h
  ✅ 回滚成功
回滚文件 430: YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.h
  ✅ 回滚成功
回滚文件 431: YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.m
  ✅ 回滚成功
回滚文件 432: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
  ✅ 回滚成功
回滚文件 433: YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.h
  ✅ 回滚成功
回滚文件 434: YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m
  ✅ 回滚成功
回滚文件 435: YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.m
  ✅ 回滚成功
回滚文件 436: YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.h
  ✅ 回滚成功
回滚文件 437: YBVideo//直播模块/主播-用户公用/XGGYBChatToolBar.h
  ✅ 回滚成功
回滚文件 438: YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.h
  ✅ 回滚成功
回滚文件 439: YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.m
  ✅ 回滚成功
回滚文件 440: YBVideo//直播模块/主播-用户公用/XGGYBChatToolBar.m
  ✅ 回滚成功
回滚文件 441: YBVideo//直播模块/主播端相关/YBLiveFunView.h
  ✅ 回滚成功
回滚文件 442: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
  ✅ 回滚成功
回滚文件 443: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
  ✅ 回滚成功
回滚文件 444: YBVideo//直播模块/主播端相关/XGGYBLiveVC.h
  ✅ 回滚成功
回滚文件 445: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
  ✅ 回滚成功
回滚文件 446: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.h
  ✅ 回滚成功
回滚文件 447: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
  ✅ 回滚成功
回滚文件 448: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.h
  ✅ 回滚成功
回滚文件 449: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m
  ✅ 回滚成功
回滚文件 450: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
  ✅ 回滚成功
回滚文件 451: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
  ✅ 回滚成功
回滚文件 452: YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m
  ✅ 回滚成功
回滚文件 453: YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.h
  ✅ 回滚成功
回滚文件 454: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.h
  ✅ 回滚成功
回滚文件 455: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.h
  ✅ 回滚成功
回滚文件 456: YBVideo//直播模块/socket/XGGYBSocketLive.h
  ✅ 回滚成功
回滚文件 457: YBVideo//直播模块/socket/XGGYBSocketPlay.m
  ✅ 回滚成功
回滚文件 458: YBVideo//直播模块/socket/XGGYBSocketLive.m
  ✅ 回滚成功
回滚文件 459: YBVideo//直播模块/socket/XGGYBSocketPlay.h
  ✅ 回滚成功
回滚文件 460: YBVideo//功能/会员/XGGYBVipVC.m
  ✅ 回滚成功
回滚文件 461: YBVideo//功能/会员/view/XGGYBVipHeader.h
  ✅ 回滚成功
回滚文件 462: YBVideo//功能/会员/view/XGGvipBuyView.m
  ✅ 回滚成功
回滚文件 463: YBVideo//功能/会员/view/XGGYBVipCell.h
  ✅ 回滚成功
回滚文件 464: YBVideo//功能/会员/view/XGGvipBuyView.h
  ✅ 回滚成功
回滚文件 465: YBVideo//功能/会员/view/XGGYBVipCell.m
  ✅ 回滚成功
回滚文件 466: YBVideo//功能/会员/view/XGGYBVipHeader.m
  ✅ 回滚成功
回滚文件 467: YBVideo//功能/会员/XGGYBVipVC.h
  ✅ 回滚成功
回滚文件 468: YBVideo//功能/钱包/XGGYBRechargeVC.m
  ✅ 回滚成功
回滚文件 469: YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.h
  ✅ 回滚成功
回滚文件 470: YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.m
  ✅ 回滚成功
回滚文件 471: YBVideo//功能/钱包/aliPay/Util/XGGRSADataVerifier.h
  ✅ 回滚成功
回滚文件 472: YBVideo//功能/钱包/aliPay/Util/XGGRSADataSigner.h
  ✅ 回滚成功
回滚文件 473: YBVideo//功能/钱包/aliPay/Util/XGGMD5DataSigner.h
  ✅ 回滚成功
回滚文件 474: YBVideo//功能/钱包/aliPay/Util/base64.h
  ✅ 回滚成功
回滚文件 475: YBVideo//功能/钱包/aliPay/XGGOrder.h
  ✅ 回滚成功
回滚文件 476: YBVideo//功能/钱包/XGGYBRechargeVC.h
  ✅ 回滚成功
回滚文件 477: YBVideo//功能/粉丝_关注_拉黑/XGGfans.m
  ✅ 回滚成功
回滚文件 478: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
  ✅ 回滚成功
回滚文件 479: YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.h
  ✅ 回滚成功
回滚文件 480: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
  ✅ 回滚成功
回滚文件 481: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
  ✅ 回滚成功
回滚文件 482: YBVideo//功能/粉丝_关注_拉黑/blackListCell.h
  ✅ 回滚成功
回滚文件 483: YBVideo//功能/粉丝_关注_拉黑/blackListCell.m
  ✅ 回滚成功
回滚文件 484: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.h
  ✅ 回滚成功
回滚文件 485: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.h
  ✅ 回滚成功
回滚文件 486: YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.m
  ✅ 回滚成功
回滚文件 487: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.h
  ✅ 回滚成功
回滚文件 488: YBVideo//功能/粉丝_关注_拉黑/XGGfans.h
  ✅ 回滚成功
回滚文件 489: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
  ✅ 回滚成功
回滚文件 490: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.h
  ✅ 回滚成功
回滚文件 491: YBVideo//功能/极光消息/单聊/Model/JCHATRawAudioDataPlayer.h
  ✅ 回滚成功
回滚文件 492: YBVideo//功能/极光消息/单聊/Model/JCHATChatModel.m
  ✅ 回滚成功
回滚文件 493: YBVideo//功能/极光消息/单聊/Model/JCHATChatModel.h
  ✅ 回滚成功
回滚文件 494: YBVideo//功能/极光消息/单聊/View/JCHATMessageContentView.m
  ✅ 回滚成功
回滚文件 495: YBVideo//功能/极光消息/单聊/View/JCHATToolBar.m
  ✅ 回滚成功
回滚文件 496: YBVideo//功能/极光消息/单聊/View/JCHATMoreView.h
  ✅ 回滚成功
回滚文件 497: YBVideo//功能/极光消息/单聊/View/JCHATAudioPlayerHelper.h
  ✅ 回滚成功
回滚文件 498: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.h
  ✅ 回滚成功
回滚文件 499: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
  ✅ 回滚成功
回滚文件 500: YBVideo//功能/极光消息/单聊/View/JCHATToolBar.h
  ✅ 回滚成功
回滚文件 501: YBVideo//功能/极光消息/单聊/View/JCHATMoreView.m
  ✅ 回滚成功
回滚文件 502: YBVideo//功能/极光消息/单聊/View/RecordView/XHVoiceCommonHelper.h
  ✅ 回滚成功
回滚文件 503: YBVideo//功能/极光消息/单聊/View/RecordView/XHVoiceRecordHelper.h
  ✅ 回滚成功
回滚文件 504: YBVideo//功能/极光消息/单聊/View/JCHATMessageTableView.h
  ✅ 回滚成功
回滚文件 505: YBVideo//功能/极光消息/Category/NSObject+TimeConvert.m
  ✅ 回滚成功
回滚文件 506: YBVideo//功能/极光消息/Category/NSObject+TimeConvert.h
  ✅ 回滚成功
回滚文件 507: YBVideo//功能/极光消息/发送位置/XGGTencentLocationVC.h
  ✅ 回滚成功
回滚文件 508: YBVideo//功能/极光消息/发送位置/XGGTencentLocationVC.m
  ✅ 回滚成功
回滚文件 509: YBVideo//功能/极光消息/发送位置/XGGSearchResultView.m
  ✅ 回滚成功
回滚文件 510: YBVideo//功能/极光消息/Utils/XGGViewUtil.h
  ✅ 回滚成功
回滚文件 511: YBVideo//功能/极光消息/Common/JCHATAlertToSendImage.h
  ✅ 回滚成功
回滚文件 512: YBVideo//功能/极光消息/Common/JCHATSendMsgManager.h
  ✅ 回滚成功
回滚文件 513: YBVideo//功能/极光消息/Common/JCHATSendMsgController.h
  ✅ 回滚成功
回滚文件 514: YBVideo//功能/极光消息/Common/JCHATTimeOutManager.h
  ✅ 回滚成功
回滚文件 515: YBVideo//功能/极光消息/Common/JCHATFileManager.h
  ✅ 回滚成功
回滚文件 516: YBVideo//功能/极光消息/Common/JCHATStringUtils.h
  ✅ 回滚成功
回滚文件 517: YBVideo//功能/极光消息/Common/JCHATAlertToSendImage.m
  ✅ 回滚成功
回滚文件 518: YBVideo//功能/极光消息/发送表情/XGGtwEmojiView.h
  ✅ 回滚成功
回滚文件 519: YBVideo//功能/极光消息/External/JCHATAlertViewWait.h
  ✅ 回滚成功
回滚文件 520: YBVideo//功能/极光消息/External/PreviewPicture/MJPhotoBrowser.h
  ✅ 回滚成功
回滚文件 521: YBVideo//功能/极光消息/External/PreviewPicture/MJPhotoToolbar.h
  ✅ 回滚成功
回滚文件 522: YBVideo//功能/极光消息/External/PreviewPicture/MJPhotoToolbar.m
  ✅ 回滚成功
回滚文件 523: YBVideo//功能/极光消息/External/PreviewPicture/MJPhotoView.h
  ✅ 回滚成功
回滚文件 524: YBVideo//功能/极光消息/External/PreviewPicture/MJPhoto.h
  ✅ 回滚成功
回滚文件 525: YBVideo//功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoSelectViewController.m
  ✅ 回滚成功
回滚文件 526: YBVideo//功能/极光消息/External/HMPhotoPicker/Controller/JCHATAlbumViewController.m
  ✅ 回滚成功
回滚文件 527: YBVideo//功能/极光消息/External/HMPhotoPicker/Controller/JCHATAlbumViewController.h
  ✅ 回滚成功
回滚文件 528: YBVideo//功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoSelectViewController.h
  ✅ 回滚成功
回滚文件 529: YBVideo//功能/极光消息/External/HMPhotoPicker/Model/JCHATAlbumModel.h
  ✅ 回滚成功
回滚文件 530: YBVideo//功能/极光消息/External/HMPhotoPicker/Model/JCHATPhotoModel.h
  ✅ 回滚成功
回滚文件 531: YBVideo//功能/极光消息/External/JCHATCustomFormatter.h
  ✅ 回滚成功
回滚文件 532: YBVideo//功能/极光消息/External/JCHATAlertViewWait.m
  ✅ 回滚成功
回滚文件 533: YBVideo//功能/H5/XGGPubH5.m
  ✅ 回滚成功
回滚文件 534: YBVideo//功能/H5/XGGPubH5.h
  ✅ 回滚成功
回滚文件 535: YBVideo//功能/上热门/XGGUpHotCell.m
  ✅ 回滚成功
回滚文件 536: YBVideo//功能/上热门/XGGHotVideoDetailVC.m
  ✅ 回滚成功
回滚文件 537: YBVideo//功能/上热门/XGGaddHotVideoVC.h
  ✅ 回滚成功
回滚文件 538: YBVideo//功能/上热门/XGGaddHotVideoVC.m
  ✅ 回滚成功
回滚文件 539: YBVideo//功能/上热门/XGGUpHotCell.h
  ✅ 回滚成功
回滚文件 540: YBVideo//功能/上热门/XGGHotVideoDetailVC.h
  ✅ 回滚成功
回滚文件 541: YBVideo//功能/登录奖励/XGGLogFirstCell.m
  ✅ 回滚成功
回滚文件 542: YBVideo//功能/登录奖励/XGGLoginbonus.h
  ✅ 回滚成功
回滚文件 543: YBVideo//功能/登录奖励/XGGLogFirstCell2.m
  ✅ 回滚成功
回滚文件 544: YBVideo//功能/登录奖励/XGGLogFirstCell2.h
  ✅ 回滚成功
回滚文件 545: YBVideo//功能/登录奖励/XGGLogFirstCell.h
  ✅ 回滚成功
回滚文件 546: YBVideo//功能/登录奖励/XGGLoginbonus.m
  ✅ 回滚成功
回滚文件 547: YBVideo//功能/顶部导航搜索/XGGsearchVC.h
  ✅ 回滚成功
回滚文件 548: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
  ✅ 回滚成功
回滚文件 549: YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.m
  ✅ 回滚成功
回滚文件 550: YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.h
  ✅ 回滚成功
回滚文件 551: YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.h
  ✅ 回滚成功
回滚文件 552: YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.m
  ✅ 回滚成功
回滚文件 553: YBVideo//功能/存储功能类/XGGStorageConfig.h
  ✅ 回滚成功
回滚文件 554: YBVideo//功能/存储功能类/XGGYBStorageObj.h
  ✅ 回滚成功
回滚文件 555: YBVideo//功能/存储功能类/XGGYBStorageObj.m
  ✅ 回滚成功
回滚文件 556: YBVideo//功能/存储功能类/XGGStorageConfig.m
  ✅ 回滚成功
回滚文件 557: YBVideo//功能/评论/XGGcommDetailCell.h
  ✅ 回滚成功
回滚文件 558: YBVideo//功能/评论/XGGcommCell.m
  ✅ 回滚成功
回滚文件 559: YBVideo//功能/评论/XGGcommentModel.m
  ✅ 回滚成功
回滚文件 560: YBVideo//功能/评论/XGGdetailmodel.h
  ✅ 回滚成功
回滚文件 561: YBVideo//功能/评论/XGGcommentview.m
  ✅ 回滚成功
回滚文件 562: YBVideo//功能/评论/XGGcommentModel.h
  ✅ 回滚成功
回滚文件 563: YBVideo//功能/评论/XGGdetailmodel.m
  ✅ 回滚成功
回滚文件 564: YBVideo//功能/评论/XGGcommentview.h
  ✅ 回滚成功
回滚文件 565: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
  ✅ 回滚成功
回滚文件 566: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
  ✅ 回滚成功
回滚文件 567: YBVideo//功能/评论/XGGcommDetailCell.m
  ✅ 回滚成功
回滚文件 568: YBVideo//功能/评论/XGGcommCell.h
  ✅ 回滚成功
回滚文件 569: YBVideo//功能/青少年/XGGYBYoungManager.m
  ✅ 回滚成功
回滚文件 570: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
  ✅ 回滚成功
回滚文件 571: YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.h
  ✅ 回滚成功
回滚文件 572: YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
  ✅ 回滚成功
回滚文件 573: YBVideo//功能/青少年/vc/密码/XGGRKCodeView.h
  ✅ 回滚成功
回滚文件 574: YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.h
  ✅ 回滚成功
回滚文件 575: YBVideo//功能/青少年/vc/密码/XGGRKCodeView.m
  ✅ 回滚成功
回滚文件 576: YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.m
  ✅ 回滚成功
回滚文件 577: YBVideo//功能/青少年/vc/XGGYBYoungModeVC.h
  ✅ 回滚成功
回滚文件 578: YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m
  ✅ 回滚成功
回滚文件 579: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.h
  ✅ 回滚成功
回滚文件 580: YBVideo//功能/青少年/小窗/XGGYBYoungSmall.h
  ✅ 回滚成功
回滚文件 581: YBVideo//功能/青少年/小窗/XGGYBYoungSmall.m
  ✅ 回滚成功
回滚文件 582: YBVideo//功能/青少年/XGGYBYoungManager.h
  ✅ 回滚成功
回滚文件 583: YBVideo//功能/我的名片/XGGBusinessCardVC.h
  ✅ 回滚成功
回滚文件 584: YBVideo//功能/我的名片/XGGBusinessCardVC.m
  ✅ 回滚成功
回滚文件 585: YBVideo//功能/KeepLive/XGGRKKeepAlive.m
  ✅ 回滚成功
回滚文件 586: YBVideo//功能/KeepLive/XGGRKKeepAlive.h
  ✅ 回滚成功
回滚文件 587: YBVideo//功能/广告管理/XGGAdvertManagerVC.m
  ✅ 回滚成功
回滚文件 588: YBVideo//功能/广告管理/XGGMyAdvertCell.h
  ✅ 回滚成功
回滚文件 589: YBVideo//功能/广告管理/XGGMyAdvertVC.m
  ✅ 回滚成功
回滚文件 590: YBVideo//功能/广告管理/XGGAdvertManagerVC.h
  ✅ 回滚成功
回滚文件 591: YBVideo//功能/广告管理/XGGMyAdvertCell.m
  ✅ 回滚成功
回滚文件 592: YBVideo//功能/广告管理/XGGMyAdvertVC.h
  ✅ 回滚成功
回滚文件 593: YBVideo//功能/观看商品/XGGlookVGoodsDView.m
  ✅ 回滚成功
回滚文件 594: YBVideo//功能/观看商品/XGGlookVGoodsDView.h
  ✅ 回滚成功
回滚文件 595: YBVideo//功能/邀请码/XGGYBInviteCode.m
  ✅ 回滚成功
回滚文件 596: YBVideo//功能/邀请码/XGGYBInvitationView.m
  ✅ 回滚成功
回滚文件 597: YBVideo//功能/邀请码/XGGYBInviteCode.h
  ✅ 回滚成功
回滚文件 598: YBVideo//功能/邀请码/XGGYBInvitationView.h
  ✅ 回滚成功
回滚文件 599: YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m
  ✅ 回滚成功
回滚文件 600: YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
  ✅ 回滚成功
回滚文件 601: YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.h
  ✅ 回滚成功
回滚文件 602: YBVideo//功能/举报(直播间+看视频)/view/XGGYBReportCell.m
  ✅ 回滚成功
回滚文件 603: YBVideo//功能/举报(直播间+看视频)/view/XGGYBReportCell.h
  ✅ 回滚成功
回滚文件 604: YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.h
  ✅ 回滚成功
回滚文件 605: YBVideo//功能/语言包/XGGYBLanguageTools.m
  ✅ 回滚成功
回滚文件 606: YBVideo//功能/语言包/XGGYBLanguageTools.h
  ✅ 回滚成功
回滚文件 607: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.h
  ✅ 回滚成功
回滚文件 608: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
  ✅ 回滚成功
回滚文件 609: YBVideo//功能/注销账号/XGGYBDestroyAccount.m
  ✅ 回滚成功
回滚文件 610: YBVideo//功能/注销账号/XGGYBDestroySureVC.h
  ✅ 回滚成功
回滚文件 611: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
  ✅ 回滚成功
回滚文件 612: YBVideo//功能/注销账号/view/XGGYBDestroyCell.m
  ✅ 回滚成功
回滚文件 613: YBVideo//功能/注销账号/view/XGGYBDestroyCell.h
  ✅ 回滚成功
回滚文件 614: YBVideo//功能/注销账号/XGGYBDestroyAccount.h
  ✅ 回滚成功
回滚文件 615: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.h
  ✅ 回滚成功
回滚文件 616: YBVideo//功能/腾讯消息/XGGYBMessageManager.h
  ✅ 回滚成功
回滚文件 617: YBVideo//功能/腾讯消息/翻译api/GDYTranslateTool.m
  ✅ 回滚成功
回滚文件 618: YBVideo//功能/腾讯消息/翻译api/GDYTranslateTool.h
  ✅ 回滚成功
回滚文件 619: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/commom/XGGTHelper.h
  ✅ 回滚成功
回滚文件 620: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTInputController.m
  ✅ 回滚成功
回滚文件 621: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.h
  ✅ 回滚成功
回滚文件 622: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.m
  ✅ 回滚成功
回滚文件 623: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceView.h
  ✅ 回滚成功
回滚文件 624: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMoreView.h
  ✅ 回滚成功
回滚文件 625: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMenuCell.h
  ✅ 回滚成功
回滚文件 626: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTTextMessageCell.m
  ✅ 回滚成功
回滚文件 627: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTImageMessageCell.h
  ✅ 回滚成功
回滚文件 628: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGiftMessageCell.m
  ✅ 回滚成功
回滚文件 629: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.m
  ✅ 回滚成功
回滚文件 630: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMessageCell.h
  ✅ 回滚成功
回滚文件 631: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMenuView.h
  ✅ 回滚成功
回滚文件 632: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMessageCell.m
  ✅ 回滚成功
回滚文件 633: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTTextView.h
  ✅ 回滚成功
回滚文件 634: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.m
  ✅ 回滚成功
回滚文件 635: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceCell.h
  ✅ 回滚成功
回滚文件 636: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTVideoMessageCell.h
  ✅ 回滚成功
回滚文件 637: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMoreCell.h
  ✅ 回滚成功
回滚文件 638: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTInputController.h
  ✅ 回滚成功
回滚文件 639: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
  ✅ 回滚成功
回滚文件 640: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.h
  ✅ 回滚成功
回滚文件 641: YBVideo//功能/腾讯消息/IM管理/XGGYBImManager.h
  ✅ 回滚成功
回滚文件 642: YBVideo//功能/腾讯消息/IM管理/工具资源/TUIKit.h
  ✅ 回滚成功
回滚文件 643: YBVideo//功能/腾讯消息/IM管理/工具资源/third/voiceConvert/XGGEMVoiceConverter.h
  ✅ 回滚成功
回滚文件 644: YBVideo//功能/腾讯消息/IM管理/工具资源/TUIKitConfig.m
  ✅ 回滚成功
回滚文件 645: YBVideo//功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.m
  ✅ 回滚成功
回滚文件 646: YBVideo//功能/腾讯消息/IM管理/工具资源/TUIKitConfig.h
  ✅ 回滚成功
回滚文件 647: YBVideo//功能/腾讯消息/IM管理/XGGYBImManager.m
  ✅ 回滚成功
回滚文件 648: YBVideo//功能/腾讯消息/XGGYBMessageManager.m
  ✅ 回滚成功
回滚文件 649: YBVideo//功能/腾讯消息/view/TConversationCell.h
  ✅ 回滚成功
回滚文件 650: YBVideo//功能/腾讯消息/view/TConversationCell.m
  ✅ 回滚成功
回滚文件 651: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
  ✅ 回滚成功
回滚文件 652: YBVideo//功能/礼物/page/XGGYBPageControl.m
  ✅ 回滚成功
回滚文件 653: YBVideo//功能/礼物/page/XGGYBPageControl.h
  ✅ 回滚成功
回滚文件 654: YBVideo//功能/礼物/XGGYBGiftView.m
  ✅ 回滚成功
回滚文件 655: YBVideo//功能/礼物/XGGYBGiftPage.m
  ✅ 回滚成功
回滚文件 656: YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.h
  ✅ 回滚成功
回滚文件 657: YBVideo//功能/礼物/手绘礼物/XGGRKShowPaintedView.m
  ✅ 回滚成功
回滚文件 658: YBVideo//功能/礼物/手绘礼物/XGGRKShowPaintedView.h
  ✅ 回滚成功
回滚文件 659: YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.m
  ✅ 回滚成功
回滚文件 660: YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.m
  ✅ 回滚成功
回滚文件 661: YBVideo//功能/礼物/礼物特效/XGGliansongBackView.m
  ✅ 回滚成功
回滚文件 662: YBVideo//功能/礼物/礼物特效/XGGexoensiveGifGiftV.m
  ✅ 回滚成功
回滚文件 663: YBVideo//功能/礼物/礼物特效/XGGcontinueGift.m
  ✅ 回滚成功
回滚文件 664: YBVideo//功能/礼物/礼物特效/XGGexoensiveGifGiftV.h
  ✅ 回滚成功
回滚文件 665: YBVideo//功能/礼物/礼物特效/XGGcontinueGift.h
  ✅ 回滚成功
回滚文件 666: YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.h
  ✅ 回滚成功
回滚文件 667: YBVideo//功能/礼物/礼物特效/XGGliansongBackView.h
  ✅ 回滚成功
回滚文件 668: YBVideo//功能/礼物/model/GiftModel.m
  ✅ 回滚成功
回滚文件 669: YBVideo//功能/礼物/model/XGGYBGiftModel.m
  ✅ 回滚成功
回滚文件 670: YBVideo//功能/礼物/model/XGGYBGiftModel.h
  ✅ 回滚成功
回滚文件 671: YBVideo//功能/礼物/view/XGGYBGiftCell.h
  ✅ 回滚成功
回滚文件 672: YBVideo//功能/礼物/view/GiftCell.m
  ✅ 回滚成功
回滚文件 673: YBVideo//功能/礼物/view/XGGYBGiftCell.m
  ✅ 回滚成功
回滚文件 674: YBVideo//功能/礼物/PageBar/TYPagerView.h
  ✅ 回滚成功
回滚文件 675: YBVideo//功能/礼物/PageBar/TYPagerViewLayout.m
  ✅ 回滚成功
回滚文件 676: YBVideo//功能/礼物/PageBar/TYTabPagerBarCell.h
  ✅ 回滚成功
回滚文件 677: YBVideo//功能/礼物/PageBar/TYTabPagerBarLayout.h
  ✅ 回滚成功
回滚文件 678: YBVideo//功能/礼物/PageBar/TYTabPagerBar.h
  ✅ 回滚成功
回滚文件 679: YBVideo//功能/礼物/PageBar/TYPagerViewLayout.h
  ✅ 回滚成功
回滚文件 680: YBVideo//功能/礼物/XGGYBGiftView.h
  ✅ 回滚成功
回滚文件 681: YBVideo//功能/礼物/XGGYBGiftPage.h
  ✅ 回滚成功
回滚文件 682: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
  ✅ 回滚成功
回滚文件 683: YBVideo//功能/标签全部视频/XGGtopicVideoCell.h
  ✅ 回滚成功
回滚文件 684: YBVideo//功能/标签全部视频/XGGtopicVideoCell.m
  ✅ 回滚成功
回滚文件 685: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.h
  ✅ 回滚成功
回滚文件 686: YBVideo//功能/直播/守护/XGGguardShowView.h
  ✅ 回滚成功
回滚文件 687: YBVideo//功能/直播/守护/XGGshouhuView.h
  ✅ 回滚成功
回滚文件 688: YBVideo//功能/直播/守护/model/XGGguardListModel.m
  ✅ 回滚成功
回滚文件 689: YBVideo//功能/直播/守护/model/XGGguardListModel.h
  ✅ 回滚成功
回滚文件 690: YBVideo//功能/直播/守护/view/XGGgrardButton.m
  ✅ 回滚成功
回滚文件 691: YBVideo//功能/直播/守护/view/XGGguardListCell.h
  ✅ 回滚成功
回滚文件 692: YBVideo//功能/直播/守护/view/XGGguardAlertView.m
  ✅ 回滚成功
回滚文件 693: YBVideo//功能/直播/守护/view/XGGguardAlertView.h
  ✅ 回滚成功
回滚文件 694: YBVideo//功能/直播/守护/view/XGGgrardButton.h
  ✅ 回滚成功
回滚文件 695: YBVideo//功能/直播/守护/view/XGGguardListCell.m
  ✅ 回滚成功
回滚文件 696: YBVideo//功能/直播/守护/XGGshouhuView.m
  ✅ 回滚成功
回滚文件 697: YBVideo//功能/直播/守护/XGGguardShowView.m
  ✅ 回滚成功
回滚文件 698: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.h
  ✅ 回滚成功
回滚文件 699: YBVideo//功能/直播/连麦+PK/PK/XGGYBPkProgressView.h
  ✅ 回滚成功
回滚文件 700: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.m
  ✅ 回滚成功
回滚文件 701: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.h
  ✅ 回滚成功
回滚文件 702: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.m
  ✅ 回滚成功
回滚文件 703: YBVideo//功能/直播/连麦+PK/PK/XGGYBPkProgressView.m
  ✅ 回滚成功
回滚文件 704: YBVideo//功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.h
  ✅ 回滚成功
回滚文件 705: YBVideo//功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.m
  ✅ 回滚成功
回滚文件 706: YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.h
  ✅ 回滚成功
回滚文件 707: YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.m
  ✅ 回滚成功
回滚文件 708: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
  ✅ 回滚成功
回滚文件 709: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m
  ✅ 回滚成功
回滚文件 710: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/anchorCell.h
  ✅ 回滚成功
回滚文件 711: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.h
  ✅ 回滚成功
回滚文件 712: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.h
  ✅ 回滚成功
回滚文件 713: YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.m
  ✅ 回滚成功
回滚文件 714: YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.h
  ✅ 回滚成功
回滚文件 715: YBVideo//功能/直播/用户列表/XGGYBUserListView.m
  ✅ 回滚成功
回滚文件 716: YBVideo//功能/直播/用户列表/XGGYBUserListView.h
  ✅ 回滚成功
回滚文件 717: YBVideo//功能/直播/用户列表/model/XGGYBUserListModel.m
  ✅ 回滚成功
回滚文件 718: YBVideo//功能/直播/用户列表/model/XGGYBUserListModel.h
  ✅ 回滚成功
回滚文件 719: YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.h
  ✅ 回滚成功
回滚文件 720: YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.m
  ✅ 回滚成功
回滚文件 721: YBVideo//功能/直播/管理员列表/XGGadminCell.m
  ✅ 回滚成功
回滚文件 722: YBVideo//功能/直播/管理员列表/XGGadminLists.h
  ✅ 回滚成功
回滚文件 723: YBVideo//功能/直播/管理员列表/XGGadminLists.m
  ✅ 回滚成功
回滚文件 724: YBVideo//功能/直播/管理员列表/XGGadminCell.h
  ✅ 回滚成功
回滚文件 725: YBVideo//功能/直播/每日任务/XGGYBDayTaskManager.h
  ✅ 回滚成功
回滚文件 726: YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.m
  ✅ 回滚成功
回滚文件 727: YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.h
  ✅ 回滚成功
回滚文件 728: YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.h
  ✅ 回滚成功
回滚文件 729: YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.m
  ✅ 回滚成功
回滚文件 730: YBVideo//功能/直播/每日任务/XGGYBDayTaskManager.m
  ✅ 回滚成功
回滚文件 731: YBVideo//功能/直播/每日任务/View/XGGYBDayTaskCell.m
  ✅ 回滚成功
回滚文件 732: YBVideo//功能/直播/每日任务/View/XGGYBDayTaskCell.h
  ✅ 回滚成功
回滚文件 733: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
  ✅ 回滚成功
回滚文件 734: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.h
  ✅ 回滚成功
回滚文件 735: YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.m
  ✅ 回滚成功
回滚文件 736: YBVideo//功能/直播/直播间聊天/model/XGGYBLiveChatModel.m
  ✅ 回滚成功
回滚文件 737: YBVideo//功能/直播/直播间聊天/model/XGGYBLiveChatModel.h
  ✅ 回滚成功
回滚文件 738: YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.h
  ✅ 回滚成功
回滚文件 739: YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.m
  ✅ 回滚成功
回滚文件 740: YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.h
  ✅ 回滚成功
回滚文件 741: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.h
  ✅ 回滚成功
回滚文件 742: YBVideo//功能/直播/在售商品/view/XGGYBOnSaleCell.m
  ✅ 回滚成功
回滚文件 743: YBVideo//功能/直播/在售商品/view/XGGYBOnSaleCell.h
  ✅ 回滚成功
回滚文件 744: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
  ✅ 回滚成功
回滚文件 745: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
  ✅ 回滚成功
回滚文件 746: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.m
  ✅ 回滚成功
回滚文件 747: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.h
  ✅ 回滚成功
回滚文件 748: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.h
  ✅ 回滚成功
回滚文件 749: YBVideo//功能/直播/幸运转盘/XGGturntableResultView.m
  ✅ 回滚成功
回滚文件 750: YBVideo//功能/直播/幸运转盘/XGGturntableView.m
  ✅ 回滚成功
回滚文件 751: YBVideo//功能/直播/幸运转盘/view/XGGturntableRecordCell.h
  ✅ 回滚成功
回滚文件 752: YBVideo//功能/直播/幸运转盘/view/XGGturntableResultCell.h
  ✅ 回滚成功
回滚文件 753: YBVideo//功能/直播/幸运转盘/view/XGGturntableResultCell.m
  ✅ 回滚成功
回滚文件 754: YBVideo//功能/直播/幸运转盘/view/XGGturntableRecordCell.m
  ✅ 回滚成功
回滚文件 755: YBVideo//功能/直播/幸运转盘/XGGturntableView.h
  ✅ 回滚成功
回滚文件 756: YBVideo//功能/直播/幸运转盘/XGGturntableResultView.h
  ✅ 回滚成功
回滚文件 757: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRecordView.m
  ✅ 回滚成功
回滚文件 758: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.m
  ✅ 回滚成功
回滚文件 759: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.h
  ✅ 回滚成功
回滚文件 760: YBVideo//功能/直播/进房间动画/XGGYBUserEnterAnimation.h
  ✅ 回滚成功
回滚文件 761: YBVideo//功能/直播/进房间动画/XGGYBUserEnterAnimation.m
  ✅ 回滚成功
回滚文件 762: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
  ✅ 回滚成功
回滚文件 763: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.h
  ✅ 回滚成功
回滚文件 764: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
  ✅ 回滚成功
回滚文件 765: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.h
  ✅ 回滚成功
回滚文件 766: YBVideo//功能/直播榜单/XGGLiveRankVC.h
  ✅ 回滚成功
回滚文件 767: YBVideo//功能/直播榜单/XGGLiveRankCell.h
  ✅ 回滚成功
回滚文件 768: YBVideo//功能/直播榜单/XGGLiveRankCell.m
  ✅ 回滚成功
回滚文件 769: YBVideo//功能/直播榜单/XGGLiveRankVC.m
  ✅ 回滚成功
回滚文件 770: YBVideo//功能/百度语音/VC/Settings/BDVRSettings.h
  ✅ 回滚成功
回滚文件 771: YBVideo//功能/百度语音/VC/Settings/BDVRSettingsItem.h
  ✅ 回滚成功
回滚文件 772: YBVideo//功能/百度语音/VC/XGGASRView.m
  ✅ 回滚成功
回滚文件 773: YBVideo//功能/百度语音/VC/XGGASRView.h
  ✅ 回滚成功
回滚文件 774: YBVideo//功能/百度语音/资源/ASRHeader/BDSUploaderDefines.h
  ✅ 回滚成功
回滚文件 775: YBVideo//功能/百度语音/资源/ASRHeader/BDSASRDefines.h
  ✅ 回滚成功
回滚文件 776: YBVideo//功能/百度语音/资源/ASRHeader/BDSEventManager.h
  ✅ 回滚成功
回滚文件 777: YBVideo//功能/百度语音/资源/ASRHeader/BDSWakeupDefines.h
  ✅ 回滚成功
回滚文件 778: YBVideo//功能/百度语音/资源/ASRHeader/UIHeaders/BDRecognizerViewParamsObject.h
  ✅ 回滚成功
回滚文件 779: YBVideo//功能/百度语音/资源/ASRHeader/UIHeaders/BDRecognizerViewDelegate.h
  ✅ 回滚成功
回滚文件 780: YBVideo//功能/百度语音/资源/ASRHeader/UIHeaders/BDTheme.h
  ✅ 回滚成功
回滚文件 781: YBVideo//功能/分享/发布分享/XGGPublishShareV.h
  ✅ 回滚成功
回滚文件 782: YBVideo//功能/分享/发布分享/XGGPublishShareV.m
  ✅ 回滚成功
回滚文件 783: YBVideo//功能/分享/观看分享/XGGYBShareView.m
  ✅ 回滚成功
回滚文件 784: YBVideo//功能/分享/观看分享/XGGYBShareViewCell.h
  ✅ 回滚成功
回滚文件 785: YBVideo//功能/分享/观看分享/XGGYBShareView.h
  ✅ 回滚成功
回滚文件 786: YBVideo//功能/分享/观看分享/XGGYBShareViewCell.m
  ✅ 回滚成功
回滚文件 787: YBVideo//附近/XGGNearbyVC.m
  ✅ 回滚成功
回滚文件 788: YBVideo//附近/XGGNearbyVC.h
  ✅ 回滚成功
回滚文件 789: YBVideo//附近/城市选择/XGGYBCitySelVC.h
  ✅ 回滚成功
回滚文件 790: YBVideo//附近/城市选择/XGGYBCitySelVC.m
  ✅ 回滚成功
回滚文件 791: YBVideo//附近/城市选择/view/XGGYBCitySelCell.h
  ✅ 回滚成功
回滚文件 792: YBVideo//附近/城市选择/view/XGGYBCitySelCell.m
  ✅ 回滚成功
回滚文件 793: YBVideo//附近/view/XGGNearbyCell.m
  ✅ 回滚成功
回滚文件 794: YBVideo//附近/view/XGGNearbyCell.h
  ✅ 回滚成功
回滚文件 795: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.h
  ✅ 回滚成功
回滚文件 796: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsCell.h
  ✅ 回滚成功
回滚文件 797: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsCell.m
  ✅ 回滚成功
回滚文件 798: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
  ✅ 回滚成功
回滚文件 799: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
  ✅ 回滚成功
回滚文件 800: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.h
  ✅ 回滚成功
回滚文件 801: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
  ✅ 回滚成功
回滚文件 802: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
  ✅ 回滚成功
回滚文件 803: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.h
  ✅ 回滚成功
回滚文件 804: YBVideo//个人中心/自己更多/我的收益/addTypeView.h
  ✅ 回滚成功
回滚文件 805: YBVideo//个人中心/自己更多/我的收益/profitTypeVC.h
  ✅ 回滚成功
回滚文件 806: YBVideo//个人中心/自己更多/我的收益/profitTypeCell.h
  ✅ 回滚成功
回滚文件 807: YBVideo//个人中心/自己更多/我的收益/view/WLCardNoFormatter.h
  ✅ 回滚成功
回滚文件 808: YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.m
  ✅ 回滚成功
回滚文件 809: YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.h
  ✅ 回滚成功
回滚文件 810: YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.m
  ✅ 回滚成功
回滚文件 811: YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.h
  ✅ 回滚成功
回滚文件 812: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.h
  ✅ 回滚成功
回滚文件 813: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
  ✅ 回滚成功
回滚文件 814: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
  ✅ 回滚成功
回滚文件 815: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.h
  ✅ 回滚成功
回滚文件 816: YBVideo//个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.h
  ✅ 回滚成功
回滚文件 817: YBVideo//个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.m
  ✅ 回滚成功
回滚文件 818: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
  ✅ 回滚成功
回滚文件 819: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
  ✅ 回滚成功
回滚文件 820: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.h
  ✅ 回滚成功
回滚文件 821: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.h
  ✅ 回滚成功
回滚文件 822: YBVideo//个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.h
  ✅ 回滚成功
回滚文件 823: YBVideo//个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.m
  ✅ 回滚成功
回滚文件 824: YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
  ✅ 回滚成功
回滚文件 825: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
  ✅ 回滚成功
回滚文件 826: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.h
  ✅ 回滚成功
回滚文件 827: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserTypeCell.h
  ✅ 回滚成功
回滚文件 828: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserTypeCell.m
  ✅ 回滚成功
回滚文件 829: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
  ✅ 回滚成功
回滚文件 830: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.h
  ✅ 回滚成功
回滚文件 831: YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.h
  ✅ 回滚成功
回滚文件 832: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.h
  ✅ 回滚成功
回滚文件 833: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
  ✅ 回滚成功
回滚文件 834: YBVideo//个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.h
  ✅ 回滚成功
回滚文件 835: YBVideo//个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.m
  ✅ 回滚成功
回滚文件 836: YBVideo//个人中心/自己更多/明细/XGGaccountDetails.h
  ✅ 回滚成功
回滚文件 837: YBVideo//个人中心/自己更多/明细/XGGaccountDetails.m
  ✅ 回滚成功
回滚文件 838: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
  ✅ 回滚成功
回滚文件 839: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.h
  ✅ 回滚成功
回滚文件 840: YBVideo//个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.m
  ✅ 回滚成功
回滚文件 841: YBVideo//个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.h
  ✅ 回滚成功
回滚文件 842: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.h
  ✅ 回滚成功
回滚文件 843: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
  ✅ 回滚成功
回滚文件 844: YBVideo//个人中心/自己更多/view/XGGYBCenterMoreCell.h
  ✅ 回滚成功
回滚文件 845: YBVideo//个人中心/自己更多/view/XGGYBCenterMoreCell.m
  ✅ 回滚成功
回滚文件 846: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
  ✅ 回滚成功
回滚文件 847: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.m
  ✅ 回滚成功
回滚文件 848: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.h
  ✅ 回滚成功
回滚文件 849: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.h
  ✅ 回滚成功
回滚文件 850: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.h
  ✅ 回滚成功
回滚文件 851: YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.m
  ✅ 回滚成功
回滚文件 852: YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.h
  ✅ 回滚成功
回滚文件 853: YBVideo//个人中心/XGGYBCenterVC.h
  ✅ 回滚成功
回滚文件 854: YBVideo//个人中心/设置/setView.h
  ✅ 回滚成功
回滚文件 855: YBVideo//个人中心/设置/XGGSetViewControllor.h
  ✅ 回滚成功
回滚文件 856: YBVideo//个人中心/设置/userItemCell5.h
  ✅ 回滚成功
回滚文件 857: YBVideo//个人中心/设置/view/XGGSetCell.h
  ✅ 回滚成功
回滚文件 858: YBVideo//个人中心/设置/view/XGGSetLogoutCell.h
  ✅ 回滚成功
回滚文件 859: YBVideo//个人中心/设置/view/XGGSetLogoutCell.m
  ✅ 回滚成功
回滚文件 860: YBVideo//个人中心/设置/view/XGGSetCell.m
  ✅ 回滚成功
回滚文件 861: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
  ✅ 回滚成功
回滚文件 862: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.h
  ✅ 回滚成功
回滚文件 863: YBVideo//个人中心/设置/XGGSetViewControllor.m
  ✅ 回滚成功
回滚文件 864: YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m
  ✅ 回滚成功
回滚文件 865: YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.h
  ✅ 回滚成功
回滚文件 866: YBVideo//个人中心/头部/XGGYBCenterTopView.m
  ✅ 回滚成功
回滚文件 867: YBVideo//个人中心/头部/XGGHeaderBackImgView.h
  ✅ 回滚成功
回滚文件 868: YBVideo//个人中心/头部/XGGHeaderBackImgView.m
  ✅ 回滚成功
回滚文件 869: YBVideo//个人中心/头部/XGGYBCenterTopView.h
  ✅ 回滚成功
回滚文件 870: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.h
  ✅ 回滚成功
回滚文件 871: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.m
  ✅ 回滚成功
回滚文件 872: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.h
  ✅ 回滚成功
回滚文件 873: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
  ✅ 回滚成功
回滚文件 874: YBVideo//个人中心/编辑资料/XGGEditHeader.h
  ✅ 回滚成功
回滚文件 875: YBVideo//个人中心/编辑资料/XGGEditVC.m
  ✅ 回滚成功
回滚文件 876: YBVideo//个人中心/编辑资料/XGGEditCell.h
  ✅ 回滚成功
回滚文件 877: YBVideo//个人中心/编辑资料/XGGEditVC.h
  ✅ 回滚成功
回滚文件 878: YBVideo//个人中心/编辑资料/XGGEditCell.m
  ✅ 回滚成功
回滚文件 879: YBVideo//个人中心/编辑资料/XGGEditHeader.m
  ✅ 回滚成功
回滚文件 880: YBVideo//个人中心/XGGYBCenterVC.m
  ✅ 回滚成功
回滚文件 881: YBVideo//工具和自定义类/XGGUtils.m
  ✅ 回滚成功
回滚文件 882: YBVideo//工具和自定义类/XGGmylabels.h
  ✅ 回滚成功
回滚文件 883: YBVideo//工具和自定义类/XGGMyTextField.m
  ✅ 回滚成功
回滚文件 884: YBVideo//工具和自定义类/XGGYBSegControl.m
  ✅ 回滚成功
回滚文件 885: YBVideo//工具和自定义类/XGGMyTextView.m
  ✅ 回滚成功
回滚文件 886: YBVideo//工具和自定义类/XGGYBAlertActionSheet.m
  ✅ 回滚成功
回滚文件 887: YBVideo//工具和自定义类/XGGRKHorPickerView.h
  ✅ 回滚成功
回滚文件 888: YBVideo//工具和自定义类/XGGYBButton.h
  ✅ 回滚成功
回滚文件 889: YBVideo//工具和自定义类/跑马灯/XGGRKLampView.m
  ✅ 回滚成功
回滚文件 890: YBVideo//工具和自定义类/跑马灯/XGGRKLampView.h
  ✅ 回滚成功
回滚文件 891: YBVideo//工具和自定义类/XGGYBButton.m
  ✅ 回滚成功
回滚文件 892: YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.h
  ✅ 回滚成功
回滚文件 893: YBVideo//工具和自定义类/发布进度/XGGRKCircularProgress.m
  ✅ 回滚成功
回滚文件 894: YBVideo//工具和自定义类/发布进度/XGGRKCircularProgress.h
  ✅ 回滚成功
回滚文件 895: YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.m
  ✅ 回滚成功
回滚文件 896: YBVideo//工具和自定义类/XGGMyTextView.h
  ✅ 回滚成功
回滚文件 897: YBVideo//工具和自定义类/XGGYBAlertActionSheet.h
  ✅ 回滚成功
回滚文件 898: YBVideo//工具和自定义类/XGGRKHorPickerView.m
  ✅ 回滚成功
回滚文件 899: YBVideo//工具和自定义类/XGGYBSegControl.h
  ✅ 回滚成功
回滚文件 900: YBVideo//工具和自定义类/XGGMyTextField.h
  ✅ 回滚成功
回滚文件 901: YBVideo//工具和自定义类/XGGUtils.h
  ✅ 回滚成功
回滚文件 902: YBVideo//工具和自定义类/Categories/ZFModalTransitionAnimator.m
  ✅ 回滚成功
回滚文件 903: YBVideo//工具和自定义类/Categories/UIButton+Additions.h
  ✅ 回滚成功
回滚文件 904: YBVideo//工具和自定义类/Categories/UIButton+Additions.m
  ✅ 回滚成功
回滚文件 905: YBVideo//工具和自定义类/Categories/UIColor+Util.m
  ✅ 回滚成功
回滚文件 906: YBVideo//工具和自定义类/Vendor/NSString+Common.m
  ✅ 回滚成功
回滚文件 907: YBVideo//工具和自定义类/Vendor/V8HorizontalPickerView/V8HorizontalPickerView.h
  ✅ 回滚成功
回滚文件 908: YBVideo//工具和自定义类/Vendor/V8HorizontalPickerView/V8HorizontalPickerViewProtocol.h
  ✅ 回滚成功
回滚文件 909: YBVideo//工具和自定义类/Vendor/NSObject+CommonBlock.m
  ✅ 回滚成功
回滚文件 910: YBVideo//工具和自定义类/Vendor/sbjson/NSObject+SBJson.m
  ✅ 回滚成功
回滚文件 911: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamWriterState.h
  ✅ 回滚成功
回滚文件 912: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamParserState.h
  ✅ 回滚成功
回滚文件 913: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamParser.h
  ✅ 回滚成功
回滚文件 914: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamWriter.h
  ✅ 回滚成功
回滚文件 915: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamParserAdapter.h
  ✅ 回滚成功
回滚文件 916: YBVideo//工具和自定义类/Vendor/sbjson/SBJson.h
  ✅ 回滚成功
回滚文件 917: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamWriterAccumulator.h
  ✅ 回滚成功
回滚文件 918: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonTokeniser.m
  ✅ 回滚成功
回滚文件 919: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonWriter.h
  ✅ 回滚成功
回滚文件 920: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonParser.h
  ✅ 回滚成功
回滚文件 921: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamParserAccumulator.h
  ✅ 回滚成功
回滚文件 922: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonUTF8Stream.h
  ✅ 回滚成功
回滚文件 923: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonTokeniser.h
  ✅ 回滚成功
回滚文件 924: YBVideo//工具和自定义类/Vendor/sbjson/SBJsonStreamParser.m
  ✅ 回滚成功
回滚文件 925: YBVideo//工具和自定义类/Vendor/sbjson/NSObject+SBJson.h
  ✅ 回滚成功
回滚文件 926: YBVideo//工具和自定义类/Vendor/NSObject+CommonBlock.h
  ✅ 回滚成功
回滚文件 927: YBVideo//工具和自定义类/XGGmylabels.m
  ✅ 回滚成功

=== 回滚完成 ===
总备份文件数: 927
成功回滚: 927
失败: 0

✅ 第三阶段回滚完成！所有文件已恢复到第三阶段执行前的状态。
