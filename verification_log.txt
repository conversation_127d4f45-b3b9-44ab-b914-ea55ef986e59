验证映射: TCRangeContentConfig -> > TCRangeContent
  ❌ 旧类名 'TCRangeContentConfig' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m:        TCRangeContentConfig* sliderConfig = [TCRangeContentConfig new];
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h:@property (nonatomic) TCRangeContentConfig* appearanceConfig;
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h:@interface TCRangeContentConfig : NSObject
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h:- (instancetype)initWithImageList:(NSArray *)images config:(TCRangeContentConfig*)config;
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.m:@implementation TCRangeContentConfig

验证映射: VideoColorInfo -> > XGGVideoColorInfo
  ❌ 旧类名 'VideoColorInfo' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h:#import "VideoColorInfo.h"
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h://@interface VideoColorInfo : NSObject
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h:@property(nonatomic,strong)NSMutableArray <VideoColorInfo *> *colorInfos;
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m://  VideoColorInfo.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m:#import "VideoColorInfo.h"

验证映射: EffectSelectView -> > XGGEffectSelectView
  ❌ 旧类名 'EffectSelectView' 仍有        6 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:#import "EffectSelectView.h"
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:@property(nonatomic,strong)EffectSelectView *effectSelectView;       //动效选择
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        _effectSelectView = [[EffectSelectView alloc] initWithFrame:_timeSelectView.frame];
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h:@interface EffectSelectView : UIView
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m:#import "EffectSelectView.h"

验证映射: TimeSelectView -> > XGGTimeSelectView
  ❌ 旧类名 'TimeSelectView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:#import "TimeSelectView.h"
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:@property(nonatomic,strong)TimeSelectView *timeSelectView;           //时间特效栏
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        _timeSelectView = [[TimeSelectView alloc] initWithFrame:CGRectMake(0, _bottomBar.top -  selectViewHeight, _window_width, selectViewHeight)];
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h://  TimeSelectView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h:@interface TimeSelectView : UIView

验证映射: TCMusicInfo -> > TCMusicCollectionCell
  ❌ 旧类名 'TCMusicInfo' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        TCMusicInfo* musicInfo = [TCMusicInfo new];
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    TCMusicInfo* musicInfo = [TCMusicInfo new];
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:- (void)saveAssetURLToFile:(TCMusicInfo*)musicInfo assetURL:(NSURL*)assetURL {
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m: - (void)saveAssetURLToFile:(TCMusicInfo*)musicInfo assetURL:(NSURL*)assetURL {
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m:- (void)addMusicInfo:(TCMusicInfo *)musicInfo{

验证映射: AlbumVideoCell -> > XGGAlbumVideoCell
  ❌ 旧类名 'AlbumVideoCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:#import "AlbumVideoCell.h"
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:    AlbumVideoCell *cell = (AlbumVideoCell*)[collectionView dequeueReusableCellWithReuseIdentifier:@"AlbumVideoCell" forIndexPath:indexPath];
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:        [_collectionView registerNib:[UINib nibWithNibName:@"AlbumVideoCell" bundle:nil] forCellWithReuseIdentifier:@"AlbumVideoCell"];
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.h://  AlbumVideoCell.h
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.h:@interface AlbumVideoCell : UICollectionViewCell

验证映射: AlbumVideoVC -> > XGGAlbumVideoVC
  ❌ 旧类名 'AlbumVideoVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m://  AlbumVideoVC.m
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:#import "AlbumVideoVC.h"
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:@interface AlbumVideoVC ()<UICollectionViewDelegate,UICollectionViewDataSource,PHPhotoLibraryChangeObserver>
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:@implementation AlbumVideoVC
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h://  AlbumVideoVC.h

验证映射: VideoRecordProcessView -> > XGGVideoRecordProcessView
  ❌ 旧类名 'VideoRecordProcessView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.h://  VideoRecordProcessView.h
    YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.h:@interface VideoRecordProcessView : UIView
    YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m://  VideoRecordProcessView.m
    YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m:#import "VideoRecordProcessView.h"
    YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m:@implementation VideoRecordProcessView

验证映射: SpeedView -> > XGGSpeedView
  ❌ 旧类名 'SpeedView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.m:#import "SpeedView.h"
    YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.m:@implementation SpeedView{
    YBVideo//录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m:#import "SpeedView.h"
    YBVideo//录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m:@implementation SpeedView{
    YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.h:@interface SpeedView : UIView

验证映射: YBPicTransitionVC -> > XGGYBPicTransitionVC
  ❌ 旧类名 'YBPicTransitionVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:#import "YBPicTransitionVC.h"
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:        YBPicTransitionVC *picVC = [[YBPicTransitionVC alloc]init];
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m://  YBPicTransitionVC.m
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m:#import "YBPicTransitionVC.h"
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m:@interface YBPicTransitionVC ()<TXVideoGenerateListener,TCVideoPreviewDelegate,TransitionViewDelegate>

验证映射: SmallButton -> > XGGSmallButton
  ❌ 旧类名 'SmallButton' 仍有        5 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.h://  SmallButton.h
    YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.h:@interface SmallButton : UIButton
    YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.m://  SmallButton.m
    YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.m:#import "SmallButton.h"
    YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.m:@implementation SmallButton

验证映射: PhotoTransitionToolbar -> > XGGPhotoTransitionToolbar
  ❌ 旧类名 'PhotoTransitionToolbar' 仍有        7 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m:#import "PhotoTransitionToolbar.h"
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m:@property(nonatomic,strong) PhotoTransitionToolbar *photoTransitionToolbar;
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m:- (PhotoTransitionToolbar *)photoTransitionToolbar {
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m:        _photoTransitionToolbar = [[PhotoTransitionToolbar alloc] initWithFrame:CGRectMake(0, self.view.height - bottomInset - bottomToolbarHeight-ShowDiff, self.view.width-90, bottomToolbarHeight)];
    YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m:#import "PhotoTransitionToolbar.h"

验证映射: VerticalButton -> > XGGVerticalButton
  ❌ 旧类名 'VerticalButton' 仍有        5 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m:#import "VerticalButton.h"
    YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m:            UIButton *btn = [[VerticalButton alloc] initWithTitle:transitionNames[i]];
    YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.m:#import "VerticalButton.h"
    YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.m:@implementation VerticalButton
    YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.h:@interface VerticalButton : UIButton

验证映射: YBPublishCoverVC -> > XGGYBPublishCoverVC
  ❌ 旧类名 'YBPublishCoverVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "YBPublishCoverVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    YBPublishCoverVC *pvc = [[YBPublishCoverVC alloc]init];
    YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m://  YBPublishCoverVC.m
    YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m:#import "YBPublishCoverVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m:@interface YBPublishCoverVC ()

验证映射: YBSetChargeView -> > XGGYBSetChargeView
  ❌ 旧类名 'YBSetChargeView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "YBSetChargeView.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    [YBSetChargeView showInputView:_videoChargeNum complete:^(NSString * _Nonnull priceStr) {
    YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m://  YBSetChargeView.m
    YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m:#import "YBSetChargeView.h"
    YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m:@interface YBSetChargeView()<UITextFieldDelegate>

验证映射: YBVideoAddGoodsVC -> > XGGYBVideoAddGoodsVC
  ❌ 旧类名 'YBVideoAddGoodsVC' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "YBVideoAddGoodsVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    YBVideoAddGoodsVC *goodsVC = [[YBVideoAddGoodsVC alloc]init];
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m://  YBVideoAddGoodsVC.m
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m:#import "YBVideoAddGoodsVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m:@interface YBVideoAddGoodsVC ()<TZImagePickerControllerDelegate,UIImagePickerControllerDelegate,UINavigationControllerDelegate,UITextViewDelegate>{

验证映射: videoTopicCell -> > XGGvideoTopicCell
  ❌ 旧类名 'videoTopicCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m:#import "videoTopicCell.h"
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m:    videoTopicCell *cell = [tableView dequeueReusableCellWithIdentifier:@"videoTopicCELL"];
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m:        cell = [[[NSBundle mainBundle] loadNibNamed:@"videoTopicCell" owner:nil options:nil] lastObject];
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.h://  videoTopicCell.h
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.h:@interface videoTopicCell : UITableViewCell

验证映射: videoTopicVC -> > XGGvideoTopicVC
  ❌ 旧类名 'videoTopicVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "videoTopicVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    videoTopicVC *topic = [[videoTopicVC alloc]init];
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m://  videoTopicVC.m
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m:#import "videoTopicVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m:@interface videoTopicVC ()<UISearchBarDelegate,UITableViewDataSource,UITableViewDelegate>{

验证映射: YBVideoClassVC -> > XGGYBVideoClassVC
  ❌ 旧类名 'YBVideoClassVC' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "YBVideoClassVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    YBVideoClassVC *videoClass = [[YBVideoClassVC alloc]init];
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h://  YBVideoClassVC.h
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h:@interface YBVideoClassVC : YBBaseViewController
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m://  YBVideoClassVC.m

验证映射: MusicClassVC -> > XGGMusicClassVC
  ❌ 旧类名 'MusicClassVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:#import "MusicClassVC.h"
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:            MusicClassVC *classVC = [[MusicClassVC alloc]init];
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h://  MusicClassVC.h
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h:@interface MusicClassVC : YBBaseViewController
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m://  MusicClassVC.m

验证映射: YBVideoMusicView -> > XGGYBVideoMusicView
  ❌ 旧类名 'YBVideoMusicView' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:#import "YBVideoMusicView.h"
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    YBVideoMusicView *mVC = [[YBVideoMusicView alloc]init];
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:            if ([subVC isKindOfClass:[YBVideoMusicView class]]) {
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:#import "YBVideoMusicView.h"
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:    YBVideoMusicView *mVC = [[YBVideoMusicView alloc]init];

验证映射: MusicModel -> > XGGMusicModel
  ❌ 旧类名 'MusicModel' 仍有       19 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:#import "MusicModel.h"
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:        MusicModel *model = [MusicModel modelWithDic:dic];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m://    MusicModel *model = _models[indexPath.row];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    MusicModel *model = _models[indexPath.row];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    MusicModel *model = _models[indexPath.row];

验证映射: MusicCell -> > XGGMusicCell
  ❌ 旧类名 'MusicCell' 仍有       22 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:#import "MusicCell.h"
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:-(void)playMusic:(NSString *)path currentCell:(MusicCell *)cell currentIndex:(NSIndexPath*)indexPath{
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    MusicCell *cell = [MusicCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    __weak MusicCell *weakCell = cell;
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    MusicCell *cell = (MusicCell *)[tableView cellForRowAtIndexPath:indexPath];

验证映射: MusicHeaderView -> > XGGMusicHeaderView
  ❌ 旧类名 'MusicHeaderView' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:#import "MusicHeaderView.h"
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:@property(nonatomic,strong)MusicHeaderView *musicClassV;    //音乐分类
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:- (MusicHeaderView *)musicClassV {
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:        _musicClassV = [[MusicHeaderView alloc]initWithFrame:CGRectMake(0, _searchBg.bottom+5, _window_width, classHeight) withBlock:^(NSString *type, NSString *title) {
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m://  MusicHeaderView.m

验证映射: MusicHeaderCell -> > XGGMusicHeaderCell
  ❌ 旧类名 'MusicHeaderCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m:#import "MusicHeaderCell.h"
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m:    MusicHeaderCell *cell = (MusicHeaderCell*)[collectionView dequeueReusableCellWithReuseIdentifier:@"MusicHeaderCell" forIndexPath:indexPath];
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m:        [_collectionView registerNib:[UINib nibWithNibName:@"MusicHeaderCell" bundle:nil] forCellWithReuseIdentifier:@"MusicHeaderCell"];
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m://  MusicHeaderCell.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m:#import "MusicHeaderCell.h"

验证映射: YBBaseAppDelegate -> > XGGYBBaseAppDelegate
  ❌ 旧类名 'YBBaseAppDelegate' 仍有       24 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m:    [[[YBBaseAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
    YBVideo//其他类/XGGYBBaseAppDelegate.h://  YBBaseAppDelegate.h
    YBVideo//其他类/XGGYBBaseAppDelegate.h:@interface YBBaseAppDelegate : UIResponder<UIApplicationDelegate>
    YBVideo//其他类/TCBaseAppDelegate.m://  YBBaseAppDelegate.m
    YBVideo//其他类/TCBaseAppDelegate.m:#import "YBBaseAppDelegate.h"

验证映射: RKLBSManager -> > XGGRKLBSManager
  ❌ 旧类名 'RKLBSManager' 仍有       25 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGRKLBSManager.h://  RKLBSManager.h
    YBVideo//其他类/XGGRKLBSManager.h:@interface RKLBSManager : NSObject
    YBVideo//其他类/XGGAppDelegate.m:    [[RKLBSManager shareManager] startLocation];
    YBVideo//缓存/定位/XGGRKLBSManager.h://  RKLBSManager.h
    YBVideo//缓存/定位/XGGRKLBSManager.h:@interface RKLBSManager : NSObject

验证映射: AppDelegate -> > XGGAppDelegate
  ❌ 旧类名 'AppDelegate' 仍有       39 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    AppDelegate *app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    YBVideo//其他类/XGGAppDelegate.m:#import "AppDelegate.h"
    YBVideo//其他类/XGGAppDelegate.m:@interface AppDelegate ()<CLLocationManagerDelegate,WXApiDelegate,JMessageDelegate,OpenInstallDelegate,XGPushDelegate,V2TXLivePremierObserver>
    YBVideo//其他类/XGGAppDelegate.m:@implementation AppDelegate
    YBVideo//其他类/XGGAppDelegate.h:@interface AppDelegate : NSObject

验证映射: YBBaseViewController -> > XGGYBBaseViewController
  ❌ 旧类名 'YBBaseViewController' 仍有      185 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.h:@interface TCVideoEditViewController : YBBaseViewController
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.h:@interface TCVideoRecordViewController : YBBaseViewController
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h:@interface AlbumVideoVC : YBBaseViewController
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h:#import "YBBaseViewController.h"
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h:@interface YBPicTransitionVC : YBBaseViewController

验证映射: YBNavigationController -> > XGGYBNavigationController
  ❌ 旧类名 'YBNavigationController' 仍有       22 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:mVC];
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:    YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:mVC];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:            YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:videoRecord];
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m:            YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:videoRecord];
    YBVideo//其他类/XGGAppDelegate.m:    self.window.rootViewController = [[YBNavigationController alloc] initWithRootViewController:[[GuideViewController alloc] init]];

验证映射: YBNavigationController -> > TCNavigationController
  ❌ 旧类名 'YBNavigationController' 仍有       22 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:mVC];
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:    YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:mVC];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:            YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:videoRecord];
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m:            YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:videoRecord];
    YBVideo//其他类/XGGAppDelegate.m:    self.window.rootViewController = [[YBNavigationController alloc] initWithRootViewController:[[GuideViewController alloc] init]];

验证映射: YBGetVideoObj -> > XGGYBGetVideoObj
  ❌ 旧类名 'YBGetVideoObj' 仍有      118 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGAppDelegate.m:#import "YBGetVideoObj.h"
    YBVideo//其他类/XGGAppDelegate.m:        [YBGetVideoObj lookManeger].fromWhere = @"AppNoti";
    YBVideo//其他类/XGGAppDelegate.m:        [YBGetVideoObj lookManeger].videoID = minstr([not_dic valueForKey:@"videoid"]);
    YBVideo//其他类/XGGAppDelegate.m:        [YBGetVideoObj lookManeger].playIndex = 0;
    YBVideo//其他类/XGGAppDelegate.m:        [YBGetVideoObj lookManeger].videoList = @[].mutableCopy;

验证映射: YBHomeRedObj -> > XGGYBHomeRedObj
  ❌ 旧类名 'YBHomeRedObj' 仍有       22 个引用未替换
  未替换的引用:
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h://  YBHomeRedObj.h
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h:@interface YBHomeRedObj : NSObject
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m://  YBHomeRedObj.m
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m:#import "YBHomeRedObj.h"
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m:@implementation YBHomeRedObj

验证映射: YBLookVideoCell -> > XGGYBLookVideoCell
  ❌ 旧类名 'YBLookVideoCell' 仍有       16 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:#import "YBLookVideoCell.h"
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:@property(nonatomic,strong)YBLookVideoCell *playingCell;
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:            YBLookVideoCell *disCell = (YBLookVideoCell*)[self.collectionView cellForItemAtIndexPath:indexPath];
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:        YBLookVideoCell *lastCell = (YBLookVideoCell*)[_collectionView cellForItemAtIndexPath:[NSIndexPath indexPathForItem:_lastPlayCellIndex inSection:0]];
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:    YBLookVideoCell *currentCell = (YBLookVideoCell*)[_collectionView cellForItemAtIndexPath:indexPath];

验证映射: YBVideoControlView -> > XGGYBVideoControlView
  ❌ 旧类名 'YBVideoControlView' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:#import "YBVideoControlView.h"
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:@property (nonatomic, strong) YBVideoControlView *controlView;
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:- (YBVideoControlView *)controlView {
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:        _controlView = [YBVideoControlView new];
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m://  YBVideoControlView.m

验证映射: YBLookVideoVC -> > XGGYBLookVideoVC
  ❌ 旧类名 'YBLookVideoVC' 仍有       18 个引用未替换
  未替换的引用:
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h: *  此功能类仅用于其他页面 push 到 观看页面(YBLookVideoVC)
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m:#import "YBLookVideoVC.h"
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m:    YBLookVideoVC *ybLook = [[YBLookVideoVC alloc]init];
    YBVideo//首页/XGGYBHomeViewController.m:#import "YBLookVideoVC.h"
    YBVideo//首页/XGGYBHomeViewController.m:    YBLookVideoVC *_ybLook;

验证映射: NearbyVideoModel -> > XGGNearbyVideoModel
  ❌ 旧类名 'NearbyVideoModel' 仍有       41 个引用未替换
  未替换的引用:
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:#import "NearbyVideoModel.h"
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:        NearbyVideoModel *model = [NearbyVideoModel modelWithDic:dic];
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:    NearbyVideoModel *model = _models[indexPath.row];
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m:#import "NearbyVideoModel.h"
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m:    NearbyVideoModel *model = [[NearbyVideoModel alloc] initWithDic:dic];

验证映射: MyFollowViewController -> > XGGMyFollowViewController
  ❌ 旧类名 'MyFollowViewController' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//首页/XGGYBHomeViewController.m:#import "MyFollowViewController.h"
    YBVideo//首页/XGGYBHomeViewController.m:       MyFollowViewController *videoVC = [[MyFollowViewController alloc]init];
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m://  MyFollowViewController.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m:#import "MyFollowViewController.h"
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m:@interface MyFollowViewController () <UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout,UIAlertViewDelegate>

验证映射: YBVideosVC -> > XGGYBVideosVC
  ❌ 旧类名 'YBVideosVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//首页/XGGYBHomeViewController.m:#import "YBVideosVC.h"
    YBVideo//首页/XGGYBHomeViewController.m:       YBVideosVC *classVC = [[YBVideosVC alloc]init];
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m://  YBVideosVC.m
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:#import "YBVideosVC.h"
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:@interface YBVideosVC ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>

验证映射: myVideoV -> > XGGmyVideoV
  ❌ 旧类名 'myVideoV' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//首页/XGGYBHomeViewController.m:#import "myVideoV.h"
    YBVideo//首页/XGGYBHomeViewController.m:       myVideoV *videoVC= [[myVideoV alloc]init];
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m://  myVideoV.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m:#import "myVideoV.h"
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m:@interface myVideoV ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>

验证映射: VideoCollectionCell -> > XGGVideoCollectionCell
  ❌ 旧类名 'VideoCollectionCell' 仍有       21 个引用未替换
  未替换的引用:
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:#import "VideoCollectionCell.h"
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:    VideoCollectionCell *cell = (VideoCollectionCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"VideoCollectionCell" forIndexPath:indexPath];
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m:        [_collectionView registerNib:[UINib nibWithNibName:@"VideoCollectionCell" bundle:nil] forCellWithReuseIdentifier:@"VideoCollectionCell"];
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m:#import "VideoCollectionCell.h"
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m:    VideoCollectionCell *cell = (VideoCollectionCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"VideoCollectionCell" forIndexPath:indexPath];

验证映射: YBHomeViewController -> > XGGYBHomeViewController
  ❌ 旧类名 'YBHomeViewController' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//首页/XGGYBHomeViewController.m://  YBHomeViewController.m
    YBVideo//首页/XGGYBHomeViewController.m:#import "YBHomeViewController.h"
    YBVideo//首页/XGGYBHomeViewController.m:@interface YBHomeViewController ()<TYTabPagerBarDelegate,TYTabPagerBarDataSource,TYPagerControllerDelegate,TYPagerControllerDataSource,V2TIMConversationListener,V2TIMAdvancedMsgListener>
    YBVideo//首页/XGGYBHomeViewController.m:@implementation YBHomeViewController
    YBVideo//首页/XGGYBHomeViewController.h://  YBHomeViewController.h

验证映射: DspLoginVC -> > XGGDspLoginVC
  ❌ 旧类名 'DspLoginVC' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//登录注册/XGGDspLoginVC.h://  DspLoginVC.h
    YBVideo//登录注册/XGGDspLoginVC.h:@interface DspLoginVC : YBBaseViewController
    YBVideo//登录注册/XGGDspLoginVC.m://  DspLoginVC.m
    YBVideo//登录注册/XGGDspLoginVC.m:#import "DspLoginVC.h"
    YBVideo//登录注册/XGGDspLoginVC.m:@interface DspLoginVC ()

验证映射: RegAlertView -> > XGGRegAlertView
  ❌ 旧类名 'RegAlertView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m://  RegAlertView.m
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m:#import "RegAlertView.h"
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m:@implementation RegAlertView
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m:    RegAlertView *regView = [[[NSBundle mainBundle]loadNibNamed:@"RegAlertView" owner:nil options:nil]objectAtIndex:0];
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.h://  RegAlertView.h

验证映射: CountryCodeVC -> > XGGCountryCodeVC
  ❌ 旧类名 'CountryCodeVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m://  CountryCodeVC.m
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m:#import "CountryCodeVC.h"
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m:@interface CountryCodeVC ()<UITableViewDelegate,UITableViewDataSource>
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m:@implementation CountryCodeVC
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.h://  CountryCodeVC.h

验证映射: RKActionSheet -> > XGGRKActionSheet
  ❌ 旧类名 'RKActionSheet' 仍有       25 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m:    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    YBVideo//公共方法类/XGGRKActionSheet.h://  RKActionSheet.h
    YBVideo//公共方法类/XGGRKActionSheet.h:@interface RKActionSheet : UIView

验证映射: YBProgressObj -> > XGGYBProgressObj
  ❌ 旧类名 'YBProgressObj' 仍有       33 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:#import "YBProgressObj.h"
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    [[YBProgressObj progressManeger]setUpViewCancelHidden:NO andComplete:^{
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    [YBProgressObj progressManeger].generationHidden = NO;
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    [YBProgressObj progressManeger].generationHidden = YES;
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    [[YBProgressObj progressManeger] progressDestroy];

验证映射: PublicObj -> > XGGPublicObj
  ❌ 旧类名 'PublicObj' 仍有      718 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        _selVolumeBtn = [PublicObj setUpImgDownText:_selVolumeBtn];
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        _selMusicBtn = [PublicObj setUpImgDownText:_selMusicBtn];
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        _effectBtn = [PublicObj setUpImgDownText:_effectBtn];
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        _coverBtn = [PublicObj setUpImgDownText:_coverBtn];
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        _filterfBtn = [PublicObj setUpImgDownText:_filterfBtn];

验证映射: YBNetworking -> > XGGYBNetworking
  ❌ 旧类名 'YBNetworking' 仍有      338 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    [YBNetworking postWithUrl:@"Video.setVideo" Dic:pullDic Suc:^(int code, id info, NSString *msg) {
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m:    [YBNetworking postWithUrl:postUrl Dic:postDic Suc:^(int code, id info, NSString *msg) {
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m:    [YBNetworking postWithUrl:@"Shop.GetApplets" Dic:@{@"id":idT.text} Suc:^(int code, id info, NSString *msg) {
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m:    [YBNetworking postWithUrl:@"Label.getList" Dic:nil Suc:^(int code, id info, NSString *msg) {
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m:    [YBNetworking postWithUrl:@"Label.SearchLabel" Dic:@{@"key":searchBars.text} Suc:^(int code, id info, NSString *msg) {

验证映射: BGSetting -> > XGGBGSetting
  ❌ 旧类名 'BGSetting' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    [BGSetting getBgSettingUpdate:NO maintain:NO eventBack:nil];
    YBVideo//其他类/XGGAppDelegate.m:    [BGSetting getBgSettingUpdate:NO maintain:NO eventBack:nil];
    YBVideo//公共方法类/XGGBGSetting.h://  BGSetting.h
    YBVideo//公共方法类/XGGBGSetting.h:@interface BGSetting : NSObject
    YBVideo//公共方法类/XGGBGSetting.m://  BGSetting.m

验证映射: RKSysAccess -> > XGGRKSysAccess
  ❌ 旧类名 'RKSysAccess' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//公共方法类/XGGRKSysAccess.h://  RKSysAccess.h
    YBVideo//公共方法类/XGGRKSysAccess.h:@interface RKSysAccess : NSObject
    YBVideo//公共方法类/XGGRKSysAccess.m://  RKSysAccess.m
    YBVideo//公共方法类/XGGRKSysAccess.m:#import "RKSysAccess.h"
    YBVideo//公共方法类/XGGRKSysAccess.m:@interface RKSysAccess()

验证映射: iOSNetworking -> > XGGiOSNetworking
  ❌ 旧类名 'iOSNetworking' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//公共方法类/XGGiOSNetworking.m://  iOSNetworking.m
    YBVideo//公共方法类/XGGiOSNetworking.m:#import "iOSNetworking.h"
    YBVideo//公共方法类/XGGiOSNetworking.m:@implementation iOSNetworking
    YBVideo//公共方法类/XGGiOSNetworking.h://  iOSNetworking.h
    YBVideo//公共方法类/XGGiOSNetworking.h:@interface iOSNetworking : NSObject

验证映射: YBAlertView -> > XGGYBAlertView
  ❌ 旧类名 'YBAlertView' 仍有       60 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:           [YBAlertView showAlertView:contentDic complete:^(int eventType) {
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:        [YBAlertView showAlertView:contentDic complete:^(int eventType) {
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:                    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:    [YBAlertView showAlertView:contentDic complete:^(int eventType) {

验证映射: RKUUIDManager -> > XGGRKUUIDManager
  ❌ 旧类名 'RKUUIDManager' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGAppDelegate.m:#import "RKUUIDManager.h"
    YBVideo//其他类/XGGAppDelegate.m:    [RKUUIDManager saveUUID:deviceUUID];
    YBVideo//公共方法类/XGGRKUUIDManager.m://  RKUUIDManager.m
    YBVideo//公共方法类/XGGRKUUIDManager.m:#import "RKUUIDManager.h"
    YBVideo//公共方法类/XGGRKUUIDManager.m:@implementation RKUUIDManager

验证映射: YBShowBigImageView -> > XGGYBShowBigImageView
  ❌ 旧类名 'YBShowBigImageView' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//公共方法类/XGGYBShowBigImageView.m://  YBShowBigImageView.m
    YBVideo//公共方法类/XGGYBShowBigImageView.m:#import "YBShowBigImageView.h"
    YBVideo//公共方法类/XGGYBShowBigImageView.m:@interface YBShowBigImageView ()<UIScrollViewDelegate>
    YBVideo//公共方法类/XGGYBShowBigImageView.m:@implementation YBShowBigImageView
    YBVideo//公共方法类/XGGYBImageView.m:#import "YBShowBigImageView.h"

验证映射: YBImageView -> > XGGYBImageView
  ❌ 旧类名 'YBImageView' 仍有       17 个引用未替换
  未替换的引用:
    YBVideo//公共方法类/XGGYBImageView.m://  YBImageView.m
    YBVideo//公共方法类/XGGYBImageView.m:#import "YBImageView.h"
    YBVideo//公共方法类/XGGYBImageView.m:@interface YBImageView ()<UIScrollViewDelegate>{
    YBVideo//公共方法类/XGGYBImageView.m:@implementation YBImageView{
    YBVideo//公共方法类/XGGYBImageView.h://  YBImageView.h

验证映射: PublicView -> > XGGPublicView
  ❌ 旧类名 'PublicView' 仍有      196 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:        [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"无本地视频") centerY:0.8];
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m:        [PublicView hiddenTextNoData:_collectionView];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    [PublicView indictorHide];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    [PublicView indictorHide];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:                [PublicView showTextNoData:weakSelf.tableView text1:YZMsg(@"对不起") text2:YZMsg(@"没有搜索到相关内容") centerY:0.8];

验证映射: GuideViewController -> > XGGGuideViewController
  ❌ 旧类名 'GuideViewController' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGAppDelegate.m:#import "GuideViewController.h"
    YBVideo//其他类/XGGAppDelegate.m:    self.window.rootViewController = [[YBNavigationController alloc] initWithRootViewController:[[GuideViewController alloc] init]];
    YBVideo//引导页/XGGGuideViewController.h://  GuideViewController.h
    YBVideo//引导页/XGGGuideViewController.h:@interface GuideViewController : YBBaseViewController
    YBVideo//引导页/XGGGuideViewController.m://  GuideViewController.m

验证映射: ApplyRefundVC -> > XGGApplyRefundVC
  ❌ 旧类名 'ApplyRefundVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m://  ApplyRefundVC.m
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m:#import "ApplyRefundVC.h"
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m:@interface ApplyRefundVC ()<UIPickerViewDelegate,UIPickerViewDataSource,UITextViewDelegate,TZImagePickerControllerDelegate>{
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m:@implementation ApplyRefundVC
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h://  ApplyRefundVC.h

验证映射: SelectClassVC -> > XGGSelectClassVC
  ❌ 旧类名 'SelectClassVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h://  SelectClassVC.h
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h:@interface SelectClassVC : YBBaseViewController
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m://  SelectClassVC.m
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m:#import "SelectClassVC.h"
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m:@interface SelectClassVC ()<UITableViewDelegate, UITableViewDataSource>

验证映射: CommodityClassModel -> > XGGCommodityClassModel
  ❌ 旧类名 'CommodityClassModel' 仍有       25 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h://  CommodityClassModel.h
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h:@interface CommodityClassModel : NSObject
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h:#import "CommodityClassModel.h"
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h:@property(nonatomic, strong)CommodityClassModel *models;
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m:#import "CommodityClassModel.h"

验证映射: CommodityClassCell -> > XGGCommodityClassCell
  ❌ 旧类名 'CommodityClassCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h://  CommodityClassCell.h
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h:@interface CommodityClassCell : UITableViewCell
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m:#import "CommodityClassCell.h"
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m:    CommodityClassCell *cell = [tableView dequeueReusableCellWithIdentifier:@"CommodityClassCell"];
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m:        cell = [[[NSBundle mainBundle] loadNibNamed:@"CommodityClassCell" owner:nil options:nil] lastObject];

验证映射: ConfirmOrderVC -> > XGGConfirmOrderVC
  ❌ 旧类名 'ConfirmOrderVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m://  ConfirmOrderVC.m
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:#import "ConfirmOrderVC.h"
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:@interface ConfirmOrderVC ()<UITableViewDelegate, UITableViewDataSource,addressSeletedDelegate>
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:@implementation ConfirmOrderVC
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h://  ConfirmOrderVC.h

验证映射: HistoryListModel -> > XGGHistoryListModel
  ❌ 旧类名 'HistoryListModel' 仍有       19 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h://  HistoryListModel.h
    YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h:@interface HistoryListModel : NSObject
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:#import "HistoryListModel.h"
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:                    HistoryListModel *model = [HistoryListModel mj_objectWithKeyValues:dic];
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:                    HistoryListModel *model = [HistoryListModel mj_objectWithKeyValues:dic];

验证映射: LookHistoryCell -> > XGGLookHistoryCell
  ❌ 旧类名 'LookHistoryCell' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h://  LookHistoryCell.h
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h:@interface LookHistoryCell : UITableViewCell
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h:+(LookHistoryCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:#import "LookHistoryCell.h"
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:    LookHistoryCell *cell = [LookHistoryCell cellWithTab:tableView andIndexPath:indexPath];

验证映射: LookHistoryVC -> > XGGLookHistoryVC
  ❌ 旧类名 'LookHistoryVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m://  LookHistoryVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:#import "LookHistoryVC.h"
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:@interface LookHistoryVC ()<UITableViewDataSource, UITableViewDelegate>
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:@implementation LookHistoryVC
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h://  LookHistoryVC.h

验证映射: LookHistoryModel -> > XGGLookHistoryModel
  ❌ 旧类名 'LookHistoryModel' 仍有       17 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h:#import "LookHistoryModel.h"
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h:@property (nonatomic, strong)LookHistoryModel *model;
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:#import "LookHistoryModel.h"
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:    LookHistoryModel *models =((HistoryListModel*)self.listArr[indexPath.section]).list[indexPath.row];
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:    LookHistoryModel *models =((HistoryListModel*)self.listArr[indexPath.section]).list[indexPath.row];

验证映射: OutsideGoodsDetailVC -> > XGGOutsideGoodsDetailVC
  ❌ 旧类名 'OutsideGoodsDetailVC' 仍有       28 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:#import "OutsideGoodsDetailVC.h"
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:            OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h://  OutsideGoodsDetailVC.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h:@interface OutsideGoodsDetailVC : YBBaseViewController
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m://  OutsideGoodsDetailVC.m

验证映射: OutsideHeadCell -> > XGGOutsideHeadCell
  ❌ 旧类名 'OutsideHeadCell' 仍有        7 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h://  OutsideHeadCell.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h:@interface OutsideHeadCell : UITableViewCell<SDCycleScrollViewDelegate>
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m://  OutsideHeadCell.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m:#import "OutsideHeadCell.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m:@implementation OutsideHeadCell

验证映射: ShareGoodsAlert -> > XGGShareGoodsAlert
  ❌ 旧类名 'ShareGoodsAlert' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:#import "ShareGoodsAlert.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:    ShareGoodsAlert *alert = [[ShareGoodsAlert alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andGoodsModel:self.commodityModel];
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h://  ShareGoodsAlert.h
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h:@interface ShareGoodsAlert : UIView
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m://  ShareGoodsAlert.m

验证映射: ShareFriendVC -> > XGGShareFriendVC
  ❌ 旧类名 'ShareFriendVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m://  ShareFriendVC.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m:#import "ShareFriendVC.h"
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m:@interface ShareFriendVC ()<UITableViewDelegate,UITableViewDataSource>
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m:@implementation ShareFriendVC
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h://  ShareFriendVC.h

验证映射: ShareFriendCell -> > XGGShareFriendCell
  ❌ 旧类名 'ShareFriendCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m://  ShareFriendCell.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m:#import "ShareFriendCell.h"
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m:@implementation ShareFriendCell
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m:+(ShareFriendCell *)cellWithTableView:(UITableView *)tableView{
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m:    ShareFriendCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ShareFriendCeLL"];

验证映射: FriendModel -> > XGGFriendModel
  ❌ 旧类名 'FriendModel' 仍有       15 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m:-(void)setModel:(FriendModel *)model
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m:#import "FriendModel.h"
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m:        FriendModel *model = [FriendModel modelWithDic:dic];
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m:    FriendModel *allmodel =self.modelArr[indexPath.row];
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m:    for (FriendModel *model in selArr) {

验证映射: ShareGoodView -> > XGGShareGoodView
  ❌ 旧类名 'ShareGoodView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m://  ShareGoodView.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m:#import "ShareGoodView.h"
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m:@implementation ShareGoodView
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.h://  ShareGoodView.h
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.h:@interface ShareGoodView : UIView

验证映射: PlatformInterventionVC -> > XGGPlatformInterventionVC
  ❌ 旧类名 'PlatformInterventionVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h://  PlatformInterventionVC.h
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h:@interface PlatformInterventionVC : YBBaseViewController
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m://  PlatformInterventionVC.m
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m:#import "PlatformInterventionVC.h"
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m:@interface PlatformInterventionVC ()<UIPickerViewDelegate,UIPickerViewDataSource,UITextViewDelegate,TZImagePickerControllerDelegate>

验证映射: PublishEvaluateVC -> > XGGPublishEvaluateVC
  ❌ 旧类名 'PublishEvaluateVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m://  PublishEvaluateVC.m
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m:#import "PublishEvaluateVC.h"
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m:@interface PublishEvaluateVC ()<CWStarRateViewDelegate,TZImagePickerControllerDelegate>
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m:@implementation PublishEvaluateVC
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h://  PublishEvaluateVC.h

验证映射: ClassToExamineVC -> > XGGClassToExamineVC
  ❌ 旧类名 'ClassToExamineVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h://  ClassToExamineVC.h
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h:@interface ClassToExamineVC : YBBaseViewController
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m://  ClassToExamineVC.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m:#import "ClassToExamineVC.h"
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m:@interface ClassToExamineVC ()

验证映射: ClassificationVC -> > XGGClassificationVC
  ❌ 旧类名 'ClassificationVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m://  ClassificationVC.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m:#import "ClassificationVC.h"
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m:@interface ClassificationVC ()<UITableViewDelegate, UITableViewDataSource>
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m:@implementation ClassificationVC
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m:#import "ClassificationVC.h"

验证映射: StandardsCell -> > XGGStandardsCell
  ❌ 旧类名 'StandardsCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.h://  StandardsCell.h
    YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.h:@interface StandardsCell : UICollectionViewCell
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m:#import "StandardsCell.h"
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m:    [self.standardsCollection registerNib:[UINib nibWithNibName:@"StandardsCell" bundle:nil] forCellWithReuseIdentifier:@"StandardsCell"];
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m:    StandardsCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"StandardsCell" forIndexPath:indexPath];

验证映射: SelectStandardsView -> > XGGSelectStandardsView
  ❌ 旧类名 'SelectStandardsView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m://  SelectStandardsView.m
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m:#import "SelectStandardsView.h"
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m:@implementation SelectStandardsView
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h://  SelectStandardsView.h
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h:@interface SelectStandardsView : UIView<UICollectionViewDelegate, UICollectionViewDataSource>

验证映射: GuaranteeView -> > XGGGuaranteeView
  ❌ 旧类名 'GuaranteeView' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:#import "GuaranteeView.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:@property (nonatomic, strong)GuaranteeView *guarView;
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:            _guarView = [[NSBundle mainBundle]loadNibNamed:@"GuaranteeView" owner:self options:nil].firstObject;
    YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.h://  GuaranteeView.h
    YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.h:@interface GuaranteeView : UIView<UIGestureRecognizerDelegate>

验证映射: sliderCollectionVCell -> > XGGsliderCollectionVCell
  ❌ 旧类名 'sliderCollectionVCell' 仍有        6 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.h://  sliderCollectionVCell.h
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.h:@interface sliderCollectionVCell : UICollectionViewCell
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.m://  sliderCollectionVCell.m
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.m:#import "sliderCollectionVCell.h"
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.m:@implementation sliderCollectionVCell

验证映射: sliderCollectionView -> > XGGsliderCollectionView
  ❌ 旧类名 'sliderCollectionView' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h:#import "sliderCollectionView.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h:    sliderCollectionView *sliderView;
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m:        sliderView = [[sliderCollectionView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 375)];
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.h://  sliderCollectionView.h
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.h:@interface sliderCollectionView : UIView<UIScrollViewDelegate>

验证映射: CommodityDetailVC -> > XGGCommodityDetailVC
  ❌ 旧类名 'CommodityDetailVC' 仍有       31 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:#import "CommodityDetailVC.h"
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m:            CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m:#import "CommodityDetailVC.h"
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m:            CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.h://  CommodityDetailVC.h

验证映射: ShowDetailVC -> > XGGShowDetailVC
  ❌ 旧类名 'ShowDetailVC' 仍有       15 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m:#import "ShowDetailVC.h"
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m:    ShowDetailVC *detail = [[ShowDetailVC alloc]init];
    YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h://  ShowDetailVC.h
    YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h:@interface ShowDetailVC : UIViewController
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m:#import "ShowDetailVC.h"

验证映射: GoodsExplainCell -> > XGGGoodsExplainCell
  ❌ 旧类名 'GoodsExplainCell' 仍有        7 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.h://  GoodsExplainCell.h
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.h:@interface GoodsExplainCell : UITableViewCell<WKNavigationDelegate,UIScrollViewDelegate>
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m://  GoodsExplainCell.m
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m:#import "GoodsExplainCell.h"
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m:@implementation GoodsExplainCell

验证映射: CommodityCell3 -> > XGGCommodityCell3
  ❌ 旧类名 'CommodityCell3' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:#import "CommodityCell3.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:        CommodityCell3 *cell3 = [[CommodityCell3 alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell3"];
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h://  CommodityCell3.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h:@interface CommodityCell3 : UITableViewCell
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m://  CommodityCell3.m

验证映射: CommodityCell2Row2 -> > XGGCommodityCell2Row2
  ❌ 旧类名 'CommodityCell2Row2' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:#import "CommodityCell2Row2.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:        CommodityCell2Row2 *row2 = [[NSBundle mainBundle]loadNibNamed:@"CommodityCell2Row2" owner:self options:nil].firstObject;
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.h://  CommodityCell2Row2.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.h:@interface CommodityCell2Row2 : UITableViewCell
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.m://  CommodityCell2Row2.m

验证映射: CommodityCell2Row1 -> > XGGCommodityCell2Row1
  ❌ 旧类名 'CommodityCell2Row1' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:#import "CommodityCell2Row1.h"
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.h://  CommodityCell2Row1.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.h:@interface CommodityCell2Row1 : UITableViewCell
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.m://  CommodityCell2Row1.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.m:#import "CommodityCell2Row1.h"

验证映射: CommodityCell1 -> > XGGCommodityCell1
  ❌ 旧类名 'CommodityCell1' 仍有        7 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m://  CommodityCell1.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m:#import "CommodityCell1.h"
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m:@implementation CommodityCell1
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h://  CommodityCell1.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h:@interface CommodityCell1 : UITableViewCell<SDCycleScrollViewDelegate>

验证映射: CommodityDetailModel -> > XGGCommodityDetailModel
  ❌ 旧类名 'CommodityDetailModel' 仍有       65 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h:#import "CommodityDetailModel.h"
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h:@property (nonatomic, strong)CommodityDetailModel *orderModel;
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h:#import "CommodityDetailModel.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h:@property (nonatomic, strong)CommodityDetailModel *model;
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m:-(void)setModel:(CommodityDetailModel *)model

验证映射: YBGoodPlayerCtrView -> > XGGYBGoodPlayerCtrView
  ❌ 旧类名 'YBGoodPlayerCtrView' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m://  YBGoodPlayerCtrView.m
    YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m:#import "YBGoodPlayerCtrView.h"
    YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m:@interface YBGoodPlayerCtrView()
    YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m:@implementation YBGoodPlayerCtrView
    YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.h://  YBGoodPlayerCtrView.h

验证映射: StoreInfoView -> > XGGStoreInfoView
  ❌ 旧类名 'StoreInfoView' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h:#import "StoreInfoView.h"
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h:@property (nonatomic, strong)StoreInfoView *infoView;
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m:    _infoView = [[StoreInfoView alloc]initWithFrame:CGRectMake(15, _headImg.bottom +15, _window_width-30, 64)];
    YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.h://  StoreInfoView.h
    YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.h:@interface StoreInfoView : UIView

验证映射: GoodsDetailVC -> > XGGGoodsDetailVC
  ❌ 旧类名 'GoodsDetailVC' 仍有        6 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.m://  GoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.m:#import "GoodsDetailVC.h"
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.m:@interface GoodsDetailVC ()
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.m:@implementation GoodsDetailVC
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.h://  GoodsDetailVC.h

验证映射: CommodityEvaluationCell -> > XGGCommodityEvaluationCell
  ❌ 旧类名 'CommodityEvaluationCell' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m://  CommodityEvaluationCell.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m:#import "CommodityEvaluationCell.h"
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m:@implementation CommodityEvaluationCell
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.h://  CommodityEvaluationCell.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.h:@interface CommodityEvaluationCell : UITableViewCell<CWStarRateViewDelegate>

验证映射: PayOrderView -> > XGGPayOrderView
  ❌ 旧类名 'PayOrderView' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:#import "PayOrderView.h"
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:@property (nonatomic, strong)PayOrderView *payView;
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:    _payView = [[PayOrderView alloc]initWithPrice:payLb.text AndOrderId:orderID AndShopName:self.orderModel.shop_name];
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m://  PayOrderView.m
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m:#import "PayOrderView.h"

验证映射: AppendEvaluateVC -> > XGGAppendEvaluateVC
  ❌ 旧类名 'AppendEvaluateVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h://  AppendEvaluateVC.h
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h:@interface AppendEvaluateVC : YBBaseViewController
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m://  AppendEvaluateVC.m
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m:#import "AppendEvaluateVC.h"
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m:@interface AppendEvaluateVC ()<TZImagePickerControllerDelegate>

验证映射: ApplyShopVC -> > XGGApplyShopVC
  ❌ 旧类名 'ApplyShopVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m://  ApplyShopVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m:#import "ApplyShopVC.h"
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m:@interface ApplyShopVC ()<UIPickerViewDelegate, UIPickerViewDataSource,TZImagePickerControllerDelegate,UITextFieldDelegate,UITextViewDelegate,CLLocationManagerDelegate>
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m:@implementation ApplyShopVC
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m:#import "ApplyShopVC.h"

验证映射: ShopApplyStatusVC -> > XGGShopApplyStatusVC
  ❌ 旧类名 'ShopApplyStatusVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m://  ShopApplyStatusVC.m
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m:#import "ShopApplyStatusVC.h"
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m:@interface ShopApplyStatusVC ()
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m:@implementation ShopApplyStatusVC
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.h://  ShopApplyStatusVC.h

验证映射: BondViewController -> > XGGBondViewController
  ❌ 旧类名 'BondViewController' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m:#import "BondViewController.h"
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m:                BondViewController *bond = [[BondViewController alloc]init];
    YBVideo//店铺/买家端/保证金/XGGBondViewController.h://  BondViewController.h
    YBVideo//店铺/买家端/保证金/XGGBondViewController.h:@interface BondViewController : YBBaseViewController
    YBVideo//店铺/买家端/保证金/XGGBondViewController.m://  BondViewController.m

验证映射: AddressModel -> > XGGAddressModel
  ❌ 旧类名 'AddressModel' 仍有       18 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m:-(void)setModel:(AddressModel *)model
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:#import "AddressModel.h"
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:        AddressModel *model = [AddressModel modelWithDic:dic];
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:-(void)editAddressWithModel:(AddressModel *)model
    YBVideo//店铺/买家端/我的地址/XGGAddressModel.h://  AddressModel.h

验证映射: EditAdressVC -> > XGGEditAdressVC
  ❌ 旧类名 'EditAdressVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:#import "EditAdressVC.h"
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:    EditAdressVC *edit = [[EditAdressVC alloc]initWithNibName:@"EditAdressVC" bundle:nil];
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:            EditAdressVC *edit = [[EditAdressVC alloc]initWithNibName:@"EditAdressVC" bundle:nil];
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:    EditAdressVC *edit = [[EditAdressVC alloc]initWithNibName:@"EditAdressVC" bundle:nil];
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h://  EditAdressVC.h

验证映射: RejectAddressModel -> > XGGRejectAddressModel
  ❌ 旧类名 'RejectAddressModel' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m:-(void)setRejectModel:(RejectAddressModel *)rejectModel
    YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.m://  RejectAddressModel.m
    YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.m:#import "RejectAddressModel.h"
    YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.m:@implementation RejectAddressModel
    YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.m:    RejectAddressModel *model = [[RejectAddressModel alloc]initWithDic:subdic];

验证映射: AddressCell -> > XGGAddressCell
  ❌ 旧类名 'AddressCell' 仍有        7 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m://  AddressCell.m
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m:#import "AddressCell.h"
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m:@implementation AddressCell
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:#import "AddressCell.h"
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:    AddressCell *cell = [[AddressCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"AddressCell"];

验证映射: AddressVC -> > XGGAddressVC
  ❌ 旧类名 'AddressVC' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:#import "AddressVC.h"
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:        AddressVC *address = [[AddressVC alloc]init];
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m://  AddressVC.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:#import "AddressVC.h"
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:@interface AddressVC ()<UITableViewDelegate, UITableViewDataSource,addressCellEditDelagate>

验证映射: GoodsEvaluationListVC -> > XGGGoodsEvaluationListVC
  ❌ 旧类名 'GoodsEvaluationListVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m:#import "GoodsEvaluationListVC.h"
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m:                GoodsEvaluationListVC *listVC = [[GoodsEvaluationListVC alloc]init];
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h://  GoodsEvaluationListVC.h
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h:@interface GoodsEvaluationListVC : YBBaseViewController
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m://  GoodsEvaluationListVC.m

验证映射: EvaluationListModel -> > XGGEvaluationListModel
  ❌ 旧类名 'EvaluationListModel' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m:-(void)setModel:(EvaluationListModel *)model
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.m://  EvaluationListModel.m
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.m:#import "EvaluationListModel.h"
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.m:@implementation EvaluationListModel
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.m:    EvaluationListModel *model = [[EvaluationListModel alloc]initWithDic:subdic];

验证映射: EvaluationListCell -> > XGGEvaluationListCell
  ❌ 旧类名 'EvaluationListCell' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m://  EvaluationListCell.m
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m:#import "EvaluationListCell.h"
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m:@implementation EvaluationListCell
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m:#import "EvaluationListCell.h"
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m:    NSString *CellIdentifier = [NSString stringWithFormat:@"EvaluationListCell%ld%ld",indexPath.section,indexPath.row];

验证映射: BuyerGetMoneyVC -> > XGGBuyerGetMoneyVC
  ❌ 旧类名 'BuyerGetMoneyVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m://  BuyerGetMoneyVC.m
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m:#import "BuyerGetMoneyVC.h"
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m:@interface BuyerGetMoneyVC ()
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m:@implementation BuyerGetMoneyVC
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.h://  BuyerGetMoneyVC.h

验证映射: BuyerRefundDetailVC -> > XGGBuyerRefundDetailVC
  ❌ 旧类名 'BuyerRefundDetailVC' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h://  BuyerRefundDetailVC.h
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h:@interface BuyerRefundDetailVC : YBBaseViewController
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m://  BuyerRefundDetailVC.m
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:#import "BuyerRefundDetailVC.h"
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:@interface BuyerRefundDetailVC (){

验证映射: BuyerRefundModel -> > XGGBuyerRefundModel
  ❌ 旧类名 'BuyerRefundModel' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.h://  BuyerRefundModel.h
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.h:@interface BuyerRefundModel : NSObject
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:#import "BuyerRefundModel.h"
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:@property(nonatomic, strong)BuyerRefundModel *model;
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:            self.model = [BuyerRefundModel modelWithDic:infos];

验证映射: BuyerRefundHeadView -> > XGGBuyerRefundHeadView
  ❌ 旧类名 'BuyerRefundHeadView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:#import "BuyerRefundHeadView.h"
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:@property(nonatomic, strong)BuyerRefundHeadView *headView;
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:-(BuyerRefundHeadView *)headView{
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m:        _headView = [[BuyerRefundHeadView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 120)];
    YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.h://  BuyerRefundHeadView.h

验证映射: OrderDetailVC -> > XGGOrderDetailVC
  ❌ 旧类名 'OrderDetailVC' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:#import "OrderDetailVC.h"
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:        OrderDetailVC *detail = [[OrderDetailVC alloc]init];
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:#import "OrderDetailVC.h"
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:        OrderDetailVC *detail = [[OrderDetailVC alloc]init];
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:        OrderDetailVC *detail = [[OrderDetailVC alloc]init];

验证映射: OrderDetailModel -> > XGGOrderDetailModel
  ❌ 旧类名 'OrderDetailModel' 仍有       26 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h:#import "OrderDetailModel.h"
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h:@property (nonatomic, strong)OrderDetailModel *models;
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m://  OrderDetailModel.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m:#import "OrderDetailModel.h"
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m:@implementation OrderDetailModel

验证映射: OrderPublicView -> > XGGOrderPublicView
  ❌ 旧类名 'OrderPublicView' 仍有       22 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:#import "OrderPublicView.h"
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:    OrderPublicView *messagePublicView;//买家留言
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:        OrderPublicView* publicView = [[OrderPublicView alloc]init];
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:        OrderPublicView* publicView = [[OrderPublicView alloc]init];
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:        OrderPublicView* publicView1 = [[OrderPublicView alloc]init];

验证映射: OrderHeaderView -> > XGGOrderHeaderView
  ❌ 旧类名 'OrderHeaderView' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:#import "OrderHeaderView.h"
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:    OrderHeaderView*headView;
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:    headView = [[OrderHeaderView alloc]init];
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h://  OrderHeaderView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h:@interface OrderHeaderView : UIView

验证映射: OrderInfoView -> > XGGOrderInfoView
  ❌ 旧类名 'OrderInfoView' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:#import "OrderInfoView.h"
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:    OrderInfoView *infosView;
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:        infosView = [[OrderInfoView alloc]init];
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:        infosView = [[OrderInfoView alloc]init];
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:        infosView = [[OrderInfoView alloc]init];

验证映射: OrderPriceView -> > XGGOrderPriceView
  ❌ 旧类名 'OrderPriceView' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:#import "OrderPriceView.h"
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m:    OrderPriceView *price = [[OrderPriceView alloc]init];
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m://  OrderPriceView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m:#import "OrderPriceView.h"
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m:@implementation OrderPriceView

验证映射: OrderListCell -> > XGGOrderListCell
  ❌ 旧类名 'OrderListCell' 仍有       16 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:#import "OrderListCell.h"
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:    OrderListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"OrderListCell"];
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:            cell = [[[NSBundle mainBundle]loadNibNamed:@"OrderListCell" owner:nil options:nil]objectAtIndex:0];
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:            cell = [[[NSBundle mainBundle]loadNibNamed:@"OrderListCell" owner:nil options:nil]objectAtIndex:5];
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:            cell = [[[NSBundle mainBundle]loadNibNamed:@"OrderListCell" owner:nil options:nil]objectAtIndex:1];

验证映射: OrderModel -> > XGGOrderModel
  ❌ 旧类名 'OrderModel' 仍有       32 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h:#import "OrderModel.h"
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h:@property (nonatomic, strong)OrderModel *model;
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h:#import "OrderModel.h"
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h:@property (nonatomic, strong)OrderModel *model;
    YBVideo//店铺/买家端/我的订单/XGGOrderModel.m://  OrderModel.m

验证映射: OrderListVC -> > XGGOrderListVC
  ❌ 旧类名 'OrderListVC' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m://  OrderListVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:#import "OrderListVC.h"
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:@interface OrderListVC ()<SPPageMenuDelegate,UITableViewDataSource, UITableViewDelegate,orderCellBtnDelegate>
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m:@implementation OrderListVC
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.h://  OrderListVC.h

验证映射: AccountBalanceVC -> > XGGAccountBalanceVC
  ❌ 旧类名 'AccountBalanceVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h://  AccountBalanceVC.h
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h:@interface AccountBalanceVC : YBBaseViewController
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m://  AccountBalanceVC.m
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m:#import "AccountBalanceVC.h"
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m:@interface AccountBalanceVC ()<UITableViewDelegate, UITableViewDataSource>

验证映射: ShopHomeVC -> > XGGShopHomeVC
  ❌ 旧类名 'ShopHomeVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/XGGShopHomeVC.h://  ShopHomeVC.h
    YBVideo//店铺/小店主页/XGGShopHomeVC.h:@interface ShopHomeVC : UIViewController
    YBVideo//店铺/小店主页/XGGShopHomeVC.m://  ShopHomeVC.m
    YBVideo//店铺/小店主页/XGGShopHomeVC.m:#import "ShopHomeVC.h"
    YBVideo//店铺/小店主页/XGGShopHomeVC.m:@interface ShopHomeVC ()

验证映射: SellerView -> > XGGSellerView
  ❌ 旧类名 'SellerView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m://  SellerView.m
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "SellerView.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:@implementation SellerView
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.h://  SellerView.h
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.h:@interface SellerView : UIView<JMessageDelegate>

验证映射: BuyerView -> > XGGBuyerView
  ❌ 旧类名 'BuyerView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/XGGShopHomeVC.m:#import "BuyerView.h"
    YBVideo//店铺/小店主页/XGGShopHomeVC.m:@property (nonatomic, strong)BuyerView *buyerView;
    YBVideo//店铺/小店主页/XGGShopHomeVC.m:        _buyerView = [[BuyerView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.h://  BuyerView.h
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.h:@interface BuyerView : UIView

验证映射: GetMoneyVC -> > XGGGetMoneyVC
  ❌ 旧类名 'GetMoneyVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.h://  GetMoneyVC.h
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.h:@interface GetMoneyVC : UIViewController
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m://  GetMoneyVC.m
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m:#import "GetMoneyVC.h"
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m:@interface GetMoneyVC ()

验证映射: CommodityCell -> > XGGCommodityCell
  ❌ 旧类名 'CommodityCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:#import "CommodityCell.h"
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:    CommodityCell *cell = [CommodityCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h://  CommodityCell.h
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h:@interface CommodityCell : UITableViewCell
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h:+(CommodityCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;

验证映射: CommodityModel -> > XGGCommodityModel
  ❌ 旧类名 'CommodityModel' 仍有       15 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:#import "CommodityModel.h"
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:    CommodityModel *models = [[CommodityModel alloc]initWithDic:self.listArr[indexPath.row]];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:    cell.btnEvent = ^(NSString * _Nonnull titles, CommodityModel * _Nonnull models) {
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:    CommodityModel *models = [[CommodityModel alloc]initWithDic:self.listArr[indexPath.row]];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h:#import "CommodityModel.h"

验证映射: CommodityManagementVC -> > XGGCommodityManagementVC
  ❌ 旧类名 'CommodityManagementVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "CommodityManagementVC.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:            CommodityManagementVC *commodity = [[CommodityManagementVC alloc]init];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m://  CommodityManagementVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:#import "CommodityManagementVC.h"
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:@interface CommodityManagementVC ()<SPPageMenuDelegate,UITableViewDelegate,UITableViewDataSource>{

验证映射: OtherSellOrderDetailVC -> > XGGOtherSellOrderDetailVC
  ❌ 旧类名 'OtherSellOrderDetailVC' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m://  OtherSellOrderDetailVC.m
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m:#import "OtherSellOrderDetailVC.h"
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m:@interface OtherSellOrderDetailVC (){
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m:@implementation OtherSellOrderDetailVC
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h://  OtherSellOrderDetailVC.h

验证映射: SellOrderDetailModel -> > XGGSellOrderDetailModel
  ❌ 旧类名 'SellOrderDetailModel' 仍有       29 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h:#import "SellOrderDetailModel.h"
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h:-(void)setSellOrderModelData:(SellOrderDetailModel *)model AndIndex:(int)index;
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m:-(void)setSellerModel:(SellOrderDetailModel *)sellerModel
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h:#import "SellOrderDetailModel.h"
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h:-(void)setSellerData:(SellOrderDetailModel *)model;

验证映射: EditSaveAddressVC -> > XGGEditSaveAddressVC
  ❌ 旧类名 'EditSaveAddressVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:#import "EditSaveAddressVC.h"
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m:            EditSaveAddressVC *address = [[EditSaveAddressVC alloc]init];
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m://  EditSaveAddressVC.m
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m:#import "EditSaveAddressVC.h"
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m:@interface EditSaveAddressVC ()<UIPickerViewDelegate, UIPickerViewDataSource,CLLocationManagerDelegate>

验证映射: SellerOrderManagementVC -> > XGGSellerOrderManagementVC
  ❌ 旧类名 'SellerOrderManagementVC' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "SellerOrderManagementVC.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:            SellerOrderManagementVC *order = [[SellerOrderManagementVC alloc]init];
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:            SellerOrderManagementVC *order = [[SellerOrderManagementVC alloc]init];
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:            SellerOrderManagementVC *order = [[SellerOrderManagementVC alloc]init];
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:            SellerOrderManagementVC *order = [[SellerOrderManagementVC alloc]init];

验证映射: SellOrderCell -> > XGGSellOrderCell
  ❌ 旧类名 'SellOrderCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m://  SellOrderCell.m
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:#import "SellOrderCell.h"
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:@implementation SellOrderCell
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:+(SellOrderCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath {
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:    SellOrderCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SellOrderCell"];

验证映射: SellOrderModel -> > XGGSellOrderModel
  ❌ 旧类名 'SellOrderModel' 仍有       24 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h:#import "SellOrderModel.h"
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h:@property (nonatomic, strong)SellOrderModel *orderModel;
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:-(void)setModel:(SellOrderModel *)model
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:-(void)lookExpress:(SellOrderModel *)model{
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:-(NSString *)addurl:(NSString *)url addModel:(SellOrderModel *)models{

验证映射: RefundDetailVC -> > XGGRefundDetailVC
  ❌ 旧类名 'RefundDetailVC' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:#import "RefundDetailVC.h"
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:        RefundDetailVC *refund = [[RefundDetailVC alloc]init];
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m:#import "RefundDetailVC.h"
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m:            RefundDetailVC *refund = [[RefundDetailVC alloc]init];
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h://  RefundDetailVC.h

验证映射: RefundDetailModel -> > XGGRefundDetailModel
  ❌ 旧类名 'RefundDetailModel' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.h://  RefundDetailModel.h
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.h:@interface RefundDetailModel : NSObject
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m:#import "RefundDetailModel.h"
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m:@property (nonatomic, strong)RefundDetailModel *model;
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m:            self.model = [RefundDetailModel modelWithDic:infos];

验证映射: RefuseRefundVC -> > XGGRefuseRefundVC
  ❌ 旧类名 'RefuseRefundVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.h://  RefuseRefundVC.h
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.h:@interface RefuseRefundVC : YBBaseViewController
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m://  RefuseRefundVC.m
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m:#import "RefuseRefundVC.h"
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m:@interface RefuseRefundVC ()<UIPickerViewDelegate,UIPickerViewDataSource,UITextViewDelegate>

验证映射: PlatformListCell -> > XGGPlatformListCell
  ❌ 旧类名 'PlatformListCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.h://  PlatformListCell.h
    YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.h:@interface PlatformListCell : UICollectionViewCell
    YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.m://  PlatformListCell.m
    YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.m:#import "PlatformListCell.h"
    YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.m:@implementation PlatformListCell

验证映射: PlatformGoodsVC -> > XGGPlatformGoodsVC
  ❌ 旧类名 'PlatformGoodsVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "PlatformGoodsVC.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:                    PlatformGoodsVC *platgoods = [[PlatformGoodsVC alloc]init];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:#import "PlatformGoodsVC.h"
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:            PlatformGoodsVC *platgoods = [[PlatformGoodsVC alloc]init];
    YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.h://  PlatformGoodsVC.h

验证映射: QualificationsVC -> > XGGQualificationsVC
  ❌ 旧类名 'QualificationsVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/资质/XGGQualificationsVC.m://  QualificationsVC.m
    YBVideo//店铺/卖家端/资质/XGGQualificationsVC.m:#import "QualificationsVC.h"
    YBVideo//店铺/卖家端/资质/XGGQualificationsVC.m:@interface QualificationsVC ()
    YBVideo//店铺/卖家端/资质/XGGQualificationsVC.m:@implementation QualificationsVC
    YBVideo//店铺/卖家端/资质/XGGQualificationsVC.h://  QualificationsVC.h

验证映射: EditStockVC -> > XGGEditStockVC
  ❌ 旧类名 'EditStockVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:#import "EditStockVC.h"
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:            EditStockVC *edit = [[EditStockVC alloc]init];
    YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.h://  EditStockVC.h
    YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.h:@interface EditStockVC : YBBaseViewController
    YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.m://  EditStockVC.m

验证映射: StockView -> > XGGStockView
  ❌ 旧类名 'StockView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/价格与库存/XGGStockView.m://  StockView.m
    YBVideo//店铺/卖家端/价格与库存/XGGStockView.m:#import "StockView.h"
    YBVideo//店铺/卖家端/价格与库存/XGGStockView.m:@implementation StockView
    YBVideo//店铺/卖家端/价格与库存/XGGStockView.h://  StockView.h
    YBVideo//店铺/卖家端/价格与库存/XGGStockView.h:@interface StockView : UIView

验证映射: BillManageVC -> > XGGBillManageVC
  ❌ 旧类名 'BillManageVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "BillManageVC.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:    BillManageVC *bill = [[BillManageVC alloc]init];
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.h://  BillManageVC.h
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.h:@interface BillManageVC : YBBaseViewController
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m://  BillManageVC.m

验证映射: BillCell -> > XGGBillCell
  ❌ 旧类名 'BillCell' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m:#import "BillCell.h"
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m:    BillCell *cell = [BillCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//店铺/卖家端/账单管理/XGGBillCell.m://  BillCell.m
    YBVideo//店铺/卖家端/账单管理/XGGBillCell.m:#import "BillCell.h"
    YBVideo//店铺/卖家端/账单管理/XGGBillCell.m:@implementation BillCell

验证映射: AddCommodityVC -> > XGGAddCommodityVC
  ❌ 旧类名 'AddCommodityVC' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "AddCommodityVC.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:                    AddCommodityVC *add = [[AddCommodityVC alloc]init];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:#import "AddCommodityVC.h"
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:            AddCommodityVC *add = [[AddCommodityVC alloc]init];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:        AddCommodityVC *edit = [[AddCommodityVC alloc]init];

验证映射: SelCommodityClassVC -> > XGGSelCommodityClassVC
  ❌ 旧类名 'SelCommodityClassVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.h://  SelCommodityClassVC.h
    YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.h:@interface SelCommodityClassVC : YBBaseViewController
    YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m://  SelCommodityClassVC.m
    YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m:#import "SelCommodityClassVC.h"
    YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m:@interface SelCommodityClassVC ()<UITableViewDelegate,UITableViewDataSource>

验证映射: StandardsView -> > XGGStandardsView
  ❌ 旧类名 'StandardsView' 仍有       17 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:#import "StandardsView.h"
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:@property (nonatomic, strong)StandardsView *standardsView;
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:-(StandardsView *)standardsView{
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:        _standardsView = [[StandardsView alloc]initWithFrame:CGRectMake(0, 40, _window_width, 240)];
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:    StandardsView *standardsView =  [[StandardsView alloc]initWithFrame:CGRectMake(0, 40+standarsArr.count *240, _window_width, 240)];

验证映射: CommodityTitleView -> > XGGCommodityTitleView
  ❌ 旧类名 'CommodityTitleView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:#import "CommodityTitleView.h"
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:@property (nonatomic, strong)CommodityTitleView *titleView;
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:-(CommodityTitleView *)titleView{
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:        _titleView = [[CommodityTitleView alloc]initWithFrame:CGRectMake(0, self.commodityClassView.bottom+5, _window_width, 170)];
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.h://  CommodityTitleView.h

验证映射: CommodityDetailView -> > XGGCommodityDetailView
  ❌ 旧类名 'CommodityDetailView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:#import "CommodityDetailView.h"
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:@property (nonatomic, strong)CommodityDetailView *contentView;
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:-(CommodityDetailView *)contentView{
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m:        _contentView =[[CommodityDetailView alloc]initWithFrame:CGRectMake(0, self.titleView.bottom+5, _window_width, 330)];
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m://  CommodityDetailView.m

验证映射: RelationVideoGoodsVC -> > XGGRelationVideoGoodsVC
  ❌ 旧类名 'RelationVideoGoodsVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "RelationVideoGoodsVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    RelationVideoGoodsVC *vc = [[RelationVideoGoodsVC alloc]init];
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m://  RelationVideoGoodsVC.m
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:#import "RelationVideoGoodsVC.h"
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:@interface RelationVideoGoodsVC ()<UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate>

验证映射: RelationGoodsModel -> > XGGRelationGoodsModel
  ❌ 旧类名 'RelationGoodsModel' 仍有       37 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:#import "RelationGoodsModel.h"
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:    RelationGoodsModel *selectModel;
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];

验证映射: goodsShowCell -> > XGGgoodsShowCell
  ❌ 旧类名 'goodsShowCell' 仍有       21 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:#import "goodsShowCell.h"
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:        goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:@"goodsShowCELL"];
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:            cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:        goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:@"goodsShowCELL"];
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m:            cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];

验证映射: shopDetailVC -> > XGGshopDetailVC
  ❌ 旧类名 'shopDetailVC' 仍有       18 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:#import "shopDetailVC.h"
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m:    shopDetailVC *vc = [[shopDetailVC alloc]init];
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m:#import "shopDetailVC.h"
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m:    shopDetailVC *vc = [[shopDetailVC alloc]init];
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m:#import "shopDetailVC.h"

验证映射: shopCell -> > XGGshopCell
  ❌ 旧类名 'shopCell' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m:        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"shopCell"];
    YBVideo//店铺/卖家端/我的店铺/XGGshopCell.m://  shopCell.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopCell.m:#import "shopCell.h"
    YBVideo//店铺/卖家端/我的店铺/XGGshopCell.m:@implementation shopCell
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m:#import "shopCell.h"

验证映射: AddGoodsVC -> > XGGAddGoodsVC
  ❌ 旧类名 'AddGoodsVC' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "AddGoodsVC.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:                    AddGoodsVC *vc = [[AddGoodsVC alloc]init];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:#import "AddGoodsVC.h"
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:            AddGoodsVC *vc = [[AddGoodsVC alloc]init];
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m:        AddGoodsVC *vc = [[AddGoodsVC alloc]init];

验证映射: RelationGoodsVC -> > XGGRelationGoodsVC
  ❌ 旧类名 'RelationGoodsVC' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m://  RelationGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m:#import "RelationGoodsVC.h"
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m:@interface RelationGoodsVC ()<UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate>{
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m:@implementation RelationGoodsVC
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.h://  RelationGoodsVC.h

验证映射: PlatformCell -> > XGGPlatformCell
  ❌ 旧类名 'PlatformCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.m://  PlatformCell.m
    YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.m:#import "PlatformCell.h"
    YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.m:@implementation PlatformCell
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m:#import "PlatformCell.h"
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m:        [_shopCollectView registerNib:[UINib nibWithNibName:@"PlatformCell" bundle:nil] forCellWithReuseIdentifier:@"PlatformCELL"];

验证映射: GoodsDetailViewController -> > XGGGoodsDetailViewController
  ❌ 旧类名 'GoodsDetailViewController' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m://  GoodsDetailViewController.m
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m:#import "GoodsDetailViewController.h"
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m:@interface GoodsDetailViewController ()<UICollectionViewDelegate,UICollectionViewDataSource>{
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m:@implementation GoodsDetailViewController
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m:    GoodsDetailViewController *vc = [[GoodsDetailViewController alloc]init];

验证映射: SendGoodsInfo -> > XGGSendGoodsInfo
  ❌ 旧类名 'SendGoodsInfo' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.h://  SendGoodsInfo.h
    YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.h:@interface SendGoodsInfo : UIView<UITextFieldDelegate>
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m:#import "SendGoodsInfo.h"
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m:    SendGoodsInfo *logisticsView;
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m:    logisticsView = [[SendGoodsInfo alloc]initWithFrame:CGRectMake(0, headBack.bottom, _window_width, 220)];

验证映射: LogisticsCell -> > XGGLogisticsCell
  ❌ 旧类名 'LogisticsCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m:#import "LogisticsCell.h"
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m:    LogisticsCell *cell = [LogisticsCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.h://  LogisticsCell.h
    YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.h:@interface LogisticsCell : UITableViewCell
    YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.h:+(LogisticsCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;

验证映射: WaitSendGoodsVC -> > XGGWaitSendGoodsVC
  ❌ 旧类名 'WaitSendGoodsVC' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:#import "WaitSendGoodsVC.h"
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m:        WaitSendGoodsVC *send = [[WaitSendGoodsVC alloc]init];
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m:#import "WaitSendGoodsVC.h"
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m:            WaitSendGoodsVC *wait = [[WaitSendGoodsVC alloc]init];
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m://  WaitSendGoodsVC.m

验证映射: ShopInfoVC -> > XGGShopInfoVC
  ❌ 旧类名 'ShopInfoVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:#import "ShopInfoVC.h"
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m:    ShopInfoVC *shop = [[ShopInfoVC alloc]init];
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m:#import "ShopInfoVC.h"
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m:    ShopInfoVC *shop = [[ShopInfoVC alloc]init];
    YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.h://  ShopInfoVC.h

验证映射: RefundHeadView -> > XGGRefundHeadView
  ❌ 旧类名 'RefundHeadView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m:#import "RefundHeadView.h"
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m:@property (nonatomic, strong)RefundHeadView *headView;
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m:-(RefundHeadView *)headView{
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m:        _headView = [[RefundHeadView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 120)];
    YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.h://  RefundHeadView.h

验证映射: SellOrderPublicView -> > XGGSellOrderPublicView
  ❌ 旧类名 'SellOrderPublicView' 仍有        7 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m:#import "SellOrderPublicView.h"
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m:    SellOrderPublicView *infos = [[SellOrderPublicView alloc]init];
    YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m://  SellOrderPublicView.m
    YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m:#import "SellOrderPublicView.h"
    YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m:@implementation SellOrderPublicView

验证映射: AddOtherSaleGoodsVC -> > XGGAddOtherSaleGoodsVC
  ❌ 旧类名 'AddOtherSaleGoodsVC' 仍有        6 个引用未替换
  未替换的引用:
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m://  AddOtherSaleGoodsVC.m
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m:#import "AddOtherSaleGoodsVC.h"
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m:@interface AddOtherSaleGoodsVC ()<UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate>
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m:@implementation AddOtherSaleGoodsVC
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.h://  AddOtherSaleGoodsVC.h

验证映射: RKLBSManager -> > XGGRKLBSManager
  ❌ 旧类名 'RKLBSManager' 仍有       25 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGRKLBSManager.h://  RKLBSManager.h
    YBVideo//其他类/XGGRKLBSManager.h:@interface RKLBSManager : NSObject
    YBVideo//其他类/XGGAppDelegate.m:    [[RKLBSManager shareManager] startLocation];
    YBVideo//缓存/定位/XGGRKLBSManager.h://  RKLBSManager.h
    YBVideo//缓存/定位/XGGRKLBSManager.h:@interface RKLBSManager : NSObject

验证映射: common -> > XGGcommon
  ❌ 旧类名 'common' 仍有      128 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:            [[MHSDK shareInstance] initWithAppID:[common getTISDKAppid] key:[common getTISDKKey]];
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:            NSString *audit_switch = [NSString stringWithFormat:@"%@",[common getAuditSwitch]];
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:           titles = [NSString stringWithFormat:@"%@%@",[Config getOwnNicename],[common video_share_des]];
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    NSString *shareDes = [common video_share_des];
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:        if ([common share_type].count==0) {

验证映射: Config -> > XGGConfig
  ❌ 旧类名 'Config' 仍有      719 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:                              @"uid":[Config getOwnID],
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:                              @"token":[Config getOwnToken],
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:           titles = [NSString stringWithFormat:@"%@%@",[Config getOwnNicename],[common video_share_des]];
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m://    gVC.touserID = [Config getOwnID];
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m:    NSString *url = [NSString stringWithFormat:@"Music.searchMusic&key=%@&p=%d&uid=%@",self.search.text,_paging,[Config getOwnID]];

验证映射: YBTabBarController -> > XGGYBTabBarController
  ❌ 旧类名 'YBTabBarController' 仍有       37 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "YBTabBarController.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m://        [PublicObj resetVC:[[YBTabBarController alloc]initWithAlert:NO]];
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    YBTabBarController *tabbar = [PublicObj currentTabbar];
    YBVideo//其他类/XGGAppDelegate.m:#import "YBTabBarController.h"
    YBVideo//其他类/XGGAppDelegate.m:    [PublicObj resetVC:[[YBTabBarController alloc]initWithAlert:NO]];

验证映射: YBLiveOrVideo -> > XGGYBLiveOrVideo
  ❌ 旧类名 'YBLiveOrVideo' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.h://  YBLiveOrVideo.h
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.h:@interface YBLiveOrVideo : UIView
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m://  YBLiveOrVideo.m
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m:#import "YBLiveOrVideo.h"
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m:@implementation YBLiveOrVideo

验证映射: YBTabBar -> > XGGYBTabBar
  ❌ 旧类名 'YBTabBar' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//底部导航/XGGYBTabBar.m://  YBTabBar.m
    YBVideo//底部导航/XGGYBTabBar.m:#import "YBTabBar.h"
    YBVideo//底部导航/XGGYBTabBar.m:@interface YBTabBar()
    YBVideo//底部导航/XGGYBTabBar.m:@implementation YBTabBar
    YBVideo//底部导航/XGGYBTabBar.h://  YBTabBar.h

验证映射: MessageFansVC -> > XGGMessageFansVC
  ❌ 旧类名 'MessageFansVC' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMessageFansVC.h://  MessageFansVC.h
    YBVideo//消息/XGGMessageFansVC.h:@interface MessageFansVC : YBBaseViewController
    YBVideo//消息/XGGMessageFansVC.m://  MessageFansVC.m
    YBVideo//消息/XGGMessageFansVC.m:#import "MessageFansVC.h"
    YBVideo//消息/XGGMessageFansVC.m:@interface MessageFansVC ()<UITableViewDelegate,UITableViewDataSource>

验证映射: SelPeopleCell -> > XGGSelPeopleCell
  ❌ 旧类名 'SelPeopleCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//消息/选择联系人/XGGSelPeopleCell.h://  SelPeopleCell.h
    YBVideo//消息/选择联系人/XGGSelPeopleCell.h:@interface SelPeopleCell : UITableViewCell
    YBVideo//消息/选择联系人/XGGSelPeopleCell.h:+(SelPeopleCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath;
    YBVideo//消息/选择联系人/XGGSelPeopleCell.m://  SelPeopleCell.m
    YBVideo//消息/选择联系人/XGGSelPeopleCell.m:#import "SelPeopleCell.h"

验证映射: SelPeopleV -> > XGGSelPeopleV
  ❌ 旧类名 'SelPeopleV' 仍有       17 个引用未替换
  未替换的引用:
    YBVideo//消息/选择联系人/XGGSelPeopleV.h://  SelPeopleV.h
    YBVideo//消息/选择联系人/XGGSelPeopleV.h:@interface SelPeopleV : UIView
    YBVideo//消息/选择联系人/XGGSelPeopleV.m://  SelPeopleV.m
    YBVideo//消息/选择联系人/XGGSelPeopleV.m:#import "SelPeopleV.h"
    YBVideo//消息/选择联系人/XGGSelPeopleV.m:@interface SelPeopleV()<UISearchBarDelegate,UITableViewDelegate,UITableViewDataSource>

验证映射: MsgSysModel -> > XGGMsgSysModel
  ❌ 旧类名 'MsgSysModel' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMsgSysVC.m:#import "MsgSysModel.h"
    YBVideo//消息/XGGMsgSysVC.m:        MsgSysModel *model = [MsgSysModel modelWithDic:dic lisModel:_listModel];
    YBVideo//消息/XGGMsgSysVC.m:    MsgSysModel *model = _sysModels[indexPath.row];
    YBVideo//消息/model/XGGMsgSysModel.h://  MsgSysModel.h
    YBVideo//消息/model/XGGMsgSysModel.h:@interface MsgSysModel : NSObject

验证映射: MessageListModel -> > XGGMessageListModel
  ❌ 旧类名 'MessageListModel' 仍有       43 个引用未替换
  未替换的引用:
    YBVideo//消息/选择联系人/XGGSelPeopleV.h:#import "MessageListModel.h"
    YBVideo//消息/选择联系人/XGGSelPeopleV.h:typedef void (^SelBlockEvent)(NSString *state,MessageListModel *userModel);
    YBVideo//消息/选择联系人/XGGSelPeopleV.m:#import "MessageListModel.h"
    YBVideo//消息/选择联系人/XGGSelPeopleV.m:    MessageListModel *model = [[MessageListModel alloc]init];
    YBVideo//消息/model/XGGMsgSysModel.h:#import "MessageListModel.h"

验证映射: MessageFansModel -> > XGGMessageFansModel
  ❌ 旧类名 'MessageFansModel' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMessageFansVC.m:#import "MessageFansModel.h"
    YBVideo//消息/XGGMessageFansVC.m:        MessageFansModel *model = [MessageFansModel modelWithDic:dic];
    YBVideo//消息/XGGMessageFansVC.m:    MessageFansModel *model = _models[indexPath.row];
    YBVideo//消息/model/XGGMessageFansModel.h://  MessageFansModel.h
    YBVideo//消息/model/XGGMessageFansModel.h:@interface MessageFansModel : NSObject

验证映射: MsgTopPubModel -> > XGGMsgTopPubModel
  ❌ 旧类名 'MsgTopPubModel' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMsgTopPubVC.m:#import "MsgTopPubModel.h"
    YBVideo//消息/XGGMsgTopPubVC.m:        MsgTopPubModel *model = [MsgTopPubModel modelWithDic:dic vcType:_type];
    YBVideo//消息/XGGMsgTopPubVC.m:    MsgTopPubModel *model = _models[indexPath.row];
    YBVideo//消息/model/XGGMsgTopPubModel.m://  MsgTopPubModel.m
    YBVideo//消息/model/XGGMsgTopPubModel.m:#import "MsgTopPubModel.h"

验证映射: MessageFansCell -> > XGGMessageFansCell
  ❌ 旧类名 'MessageFansCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMessageFansVC.m:#import "MessageFansCell.h"
    YBVideo//消息/XGGMessageFansVC.m:    MessageFansCell *cell = [MessageFansCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//消息/view/XGGMessageFansCell.h://  MessageFansCell.h
    YBVideo//消息/view/XGGMessageFansCell.h:@interface MessageFansCell : UITableViewCell
    YBVideo//消息/view/XGGMessageFansCell.h:+(MessageFansCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath;

验证映射: MsgSysCell -> > XGGMsgSysCell
  ❌ 旧类名 'MsgSysCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMsgSysVC.m:#import "MsgSysCell.h"
    YBVideo//消息/XGGMsgSysVC.m:    MsgSysCell *cell = [MsgSysCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//消息/view/XGGMsgSysCell.h://  MsgSysCell.h
    YBVideo//消息/view/XGGMsgSysCell.h:@interface MsgSysCell : UITableViewCell
    YBVideo//消息/view/XGGMsgSysCell.h:+(MsgSysCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath;

验证映射: MessageListCell -> > XGGMessageListCell
  ❌ 旧类名 'MessageListCell' 仍有       18 个引用未替换
  未替换的引用:
    YBVideo//消息/view/XGGMessageListCell.m://  MessageListCell.m
    YBVideo//消息/view/XGGMessageListCell.m:#import "MessageListCell.h"
    YBVideo//消息/view/XGGMessageListCell.m:@interface MessageListCell()
    YBVideo//消息/view/XGGMessageListCell.m:@implementation MessageListCell
    YBVideo//消息/view/XGGMessageListCell.m:+(MessageListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {

验证映射: MessageHeaderV -> > XGGMessageHeaderV
  ❌ 旧类名 'MessageHeaderV' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//消息/view/XGGMessageHeaderV.m://  MessageHeaderV.m
    YBVideo//消息/view/XGGMessageHeaderV.m:#import "MessageHeaderV.h"
    YBVideo//消息/view/XGGMessageHeaderV.m:@implementation MessageHeaderV
    YBVideo//消息/view/XGGMessageHeaderV.h://  MessageHeaderV.h
    YBVideo//消息/view/XGGMessageHeaderV.h:@interface MessageHeaderV : UIView

验证映射: MsgTopPubCell -> > XGGMsgTopPubCell
  ❌ 旧类名 'MsgTopPubCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMsgTopPubVC.m:#import "MsgTopPubCell.h"
    YBVideo//消息/XGGMsgTopPubVC.m:    MsgTopPubCell *cell = [MsgTopPubCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//消息/view/XGGMsgTopPubCell.m://  MsgTopPubCell.m
    YBVideo//消息/view/XGGMsgTopPubCell.m:#import "MsgTopPubCell.h"
    YBVideo//消息/view/XGGMsgTopPubCell.m:@implementation MsgTopPubCell

验证映射: OrderMessageVC -> > XGGOrderMessageVC
  ❌ 旧类名 'OrderMessageVC' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGOrderMessageVC.m://  OrderMessageVC.m
    YBVideo//消息/XGGOrderMessageVC.m:#import "OrderMessageVC.h"
    YBVideo//消息/XGGOrderMessageVC.m:@interface OrderMessageVC ()<UITableViewDelegate, UITableViewDataSource>
    YBVideo//消息/XGGOrderMessageVC.m:@implementation OrderMessageVC
    YBVideo//消息/XGGOrderMessageVC.h://  OrderMessageVC.h

验证映射: chatmessageCell -> > XGGchatmessageCell
  ❌ 旧类名 'chatmessageCell' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGchatmessageCell.m:#import "chatmessageCell.h"
    YBVideo//消息/XGGchatmessageCell.m:@interface chatmessageCell ()
    YBVideo//消息/XGGchatmessageCell.m:@implementation chatmessageCell
    YBVideo//消息/XGGchatmessageCell.m:+(chatmessageCell *)cellWithTableView:(UITableView *)tableView
    YBVideo//消息/XGGchatmessageCell.m:    chatmessageCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];

验证映射: OrderMessageModel -> > XGGOrderMessageModel
  ❌ 旧类名 'OrderMessageModel' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGchatmessageCell.m:-(void)setOrdermodel:(OrderMessageModel *)ordermodel{
    YBVideo//消息/XGGOrderMessageVC.m:#import "OrderMessageModel.h"
    YBVideo//消息/XGGOrderMessageVC.m:        OrderMessageModel *model = [OrderMessageModel messageWithDic:dic];
    YBVideo//消息/XGGOrderMessageVC.m:    OrderMessageModel *model = self.models[indexPath.row];
    YBVideo//消息/XGGOrderMessageModel.m://  OrderMessageModel.m

验证映射: MsgSysVC -> > XGGMsgSysVC
  ❌ 旧类名 'MsgSysVC' 仍有       15 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMsgSysVC.m://  MsgSysVC.m
    YBVideo//消息/XGGMsgSysVC.m:#import "MsgSysVC.h"
    YBVideo//消息/XGGMsgSysVC.m:@interface MsgSysVC ()<UITableViewDelegate,UITableViewDataSource>
    YBVideo//消息/XGGMsgSysVC.m:@implementation MsgSysVC
    YBVideo//消息/XGGMsgSysVC.h://  MsgSysVC.h

验证映射: MsgTopPubVC -> > XGGMsgTopPubVC
  ❌ 旧类名 'MsgTopPubVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//消息/XGGMsgTopPubVC.m://  MsgTopPubVC.m
    YBVideo//消息/XGGMsgTopPubVC.m:#import "MsgTopPubVC.h"
    YBVideo//消息/XGGMsgTopPubVC.m:@interface MsgTopPubVC ()<UITableViewDelegate,UITableViewDataSource,MsgClickDelegate>
    YBVideo//消息/XGGMsgTopPubVC.m:@implementation MsgTopPubVC
    YBVideo//消息/XGGMsgTopPubVC.m:    [YBGetVideoObj lookManeger].fromWhere = @"MsgTopPubVC";

验证映射: YBPlayVC -> > XGGYBPlayVC
  ❌ 旧类名 'YBPlayVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.h://  YBPlayVC.h
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.h:@interface YBPlayVC : YBBaseViewController
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m://  YBPlayVC.m
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:#import "YBPlayVC.h"
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:@interface YBPlayVC ()<UserBulletWindowDelegate,UIScrollViewDelegate,V2TXLivePlayerObserver,V2TIMConversationListener> {

验证映射: YBPlayCtrlView -> > XGGYBPlayCtrlView
  ❌ 旧类名 'YBPlayCtrlView' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m://  YBPlayCtrlView.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:#import "YBPlayCtrlView.h"
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:@interface YBPlayCtrlView()<UIGestureRecognizerDelegate,haohuadelegate,SDCycleScrollViewDelegate,turntableViewDelegate,shouhuViewDelegate,guardShowDelegate>
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:@implementation YBPlayCtrlView
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h://  YBPlayCtrlView.h

验证映射: YBCheckLiveObj -> > XGGYBCheckLiveObj
  ❌ 旧类名 'YBCheckLiveObj' 仍有       36 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:#import "YBCheckLiveObj.h"
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:        [YBCheckLiveObj checkLiveManeger].liveUid = minstr([liveInfo valueForKey:@"uid"]);
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:        [YBCheckLiveObj checkLiveManeger].liveStream = minstr([liveInfo valueForKey:@"stream"]);
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:        [YBCheckLiveObj checkLiveManeger].currentIndex = 0;
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:        [YBCheckLiveObj checkLiveManeger].listArray = nil;

验证映射: YBLiveListVC -> > XGGYBLiveListVC
  ❌ 旧类名 'YBLiveListVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//首页/XGGYBHomeViewController.m:#import "YBLiveListVC.h"
    YBVideo//首页/XGGYBHomeViewController.m:    YBLiveListVC *lVC = [[YBLiveListVC alloc]init];
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.h://  YBLiveListVC.h
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.h:@interface YBLiveListVC : YBBaseViewController
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m://  YBLiveListVC.m

验证映射: YBLiveListCell -> > XGGYBLiveListCell
  ❌ 旧类名 'YBLiveListCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m:#import "YBLiveListCell.h"
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m:    YBLiveListCell *cell = (YBLiveListCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"YBLiveListCell" forIndexPath:indexPath];
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m:        [_collectionView registerNib:[UINib nibWithNibName:@"YBLiveListCell" bundle:nil] forCellWithReuseIdentifier:@"YBLiveListCell"];
    YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.h://  YBLiveListCell.h
    YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.h:@interface YBLiveListCell : UICollectionViewCell

验证映射: YBLiveRoomAlertView -> > XGGYBLiveRoomAlertView
  ❌ 旧类名 'YBLiveRoomAlertView' 仍有       17 个引用未替换
  未替换的引用:
    YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m://  YBLiveRoomAlertView.m
    YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m:#import "YBLiveRoomAlertView.h"
    YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m:@interface YBLiveRoomAlertView()<UIPickerViewDelegate,UIPickerViewDataSource>
    YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m:@implementation YBLiveRoomAlertView
    YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m:+(YBLiveRoomAlertView *)showRoomWarning:(NSDictionary *)warningInfo;{

验证映射: YBLiveRTCManager -> > XGGYBLiveRTCManager
  ❌ 旧类名 'YBLiveRTCManager' 仍有       25 个引用未替换
  未替换的引用:
    YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.h://  YBLiveRTCManager.h
    YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.h:@interface YBLiveRTCManager : NSObject
    YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.m://  YBLiveRTCManager.m
    YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.m:#import "YBLiveRTCManager.h"
    YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.m:@interface YBLiveRTCManager ()<V2TXLivePusherObserver>

验证映射: YBLiveEndView -> > XGGYBLiveEndView
  ❌ 旧类名 'YBLiveEndView' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:#import "YBLiveEndView.h"
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:@property(nonatomic,strong)YBLiveEndView *playEndView;          //直播结束页面
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:- (YBLiveEndView *)playEndView {
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:        _playEndView = [[YBLiveEndView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.h://  YBLiveEndView.h

验证映射: YBChatToolBar -> > XGGYBChatToolBar
  ❌ 旧类名 'YBChatToolBar' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:- (YBChatToolBar *)chatTool {
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:        _chatTool = [[YBChatToolBar alloc]initWithFrame:CGRectMake(0, _window_height-44, _window_width, 44)];
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h:#import "YBChatToolBar.h"
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h:@property(nonatomic,strong)YBChatToolBar *chatTool;             //直播聊天工具条
    YBVideo//直播模块/主播-用户公用/XGGYBChatToolBar.m://  YBChatToolBar.m

验证映射: roomShowGoodsView -> > XGGroomShowGoodsView
  ❌ 旧类名 'roomShowGoodsView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m://  roomShowGoodsView.m
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m:#import "roomShowGoodsView.h"
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m:@interface roomShowGoodsView ()<UITableViewDelegate,UITableViewDataSource,goodsShowCellDelegate> {
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m:@implementation roomShowGoodsView
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h://  roomShowGoodsView.h

验证映射: YBLiveVC -> > XGGYBLiveVC
  ❌ 旧类名 'YBLiveVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m:#import "YBLiveVC.h"
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m:                YBLiveVC *liveVC = [[YBLiveVC alloc]init];
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.h://  YBLiveVC.h
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.h:@interface YBLiveVC : YBBaseViewController
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m://  YBLiveVC.m

验证映射: YBLiveFucView -> > YBLiveFunView
  ❌ 旧类名 'YBLiveFucView' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/YBLiveFunView.h://  YBLiveFucView.h
    YBVideo//直播模块/主播端相关/YBLiveFunView.h:@interface YBLiveFucView : UIView
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m://  YBLiveFucView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m:#import "YBLiveFucView.h"
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m:@interface YBLiveFucView()<UIGestureRecognizerDelegate>

验证映射: startLiveClassCell -> > XGGstartLiveClassCell
  ❌ 旧类名 'startLiveClassCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.h://  startLiveClassCell.h
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.h:@interface startLiveClassCell : UITableViewCell
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m://  startLiveClassCell.m
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m:#import "startLiveClassCell.h"
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m:@implementation startLiveClassCell

验证映射: startLiveClassVC -> > XGGstartLiveClassVC
  ❌ 旧类名 'startLiveClassVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.h://  startLiveClassVC.h
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.h:@interface startLiveClassVC : UIViewController
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m://  startLiveClassVC.m
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m:#import "startLiveClassVC.h"
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m:@interface startLiveClassVC ()<UITableViewDelegate,UITableViewDataSource>{

验证映射: YBLiveCtrlView -> > XGGYBLiveCtrlView
  ❌ 旧类名 'YBLiveCtrlView' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.h://  YBLiveCtrlView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.h:@interface YBLiveCtrlView : UIView
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m://  YBLiveCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m:#import "YBLiveCtrlView.h"
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m:@interface YBLiveCtrlView()<UIGestureRecognizerDelegate,haohuadelegate,SDCycleScrollViewDelegate,guardShowDelegate> {

验证映射: YBLivePreview -> > XGGYBLivePreview
  ❌ 旧类名 'YBLivePreview' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.h://  YBLivePreview.h
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.h:@interface YBLivePreview : UIView
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m://  YBLivePreview.m
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m:#import "YBLivePreview.h"
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m:@interface YBLivePreview()<UITextViewDelegate,TZImagePickerControllerDelegate,UIImagePickerControllerDelegate,UINavigationControllerDelegate> {

验证映射: YBLiveFucView -> > XGGYBLiveFucView
  ❌ 旧类名 'YBLiveFucView' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/YBLiveFunView.h://  YBLiveFucView.h
    YBVideo//直播模块/主播端相关/YBLiveFunView.h:@interface YBLiveFucView : UIView
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m://  YBLiveFucView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m:#import "YBLiveFucView.h"
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m:@interface YBLiveFucView()<UIGestureRecognizerDelegate>

验证映射: YBSocketPlay -> > XGGYBSocketPlay
  ❌ 旧类名 'YBSocketPlay' 仍有       44 个引用未替换
  未替换的引用:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:#import "YBSocketPlay.h"
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:    [[YBSocketPlay playSocketManeger]enterRoomAndConnectSocket:_playDic complete:^(NSString *socEvent, NSDictionary *socketDic) {
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:    [YBSocketPlay playSocketManeger].roomCloseByAdmin = ^(NSString *socEvent, NSDictionary *socketDic) {
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:    [YBSocketPlay playSocketManeger].userLight = ^(NSString *socEvent, NSDictionary *socketDic) {
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:    [YBSocketPlay playSocketManeger].userSendMsg = ^(NSString *socEvent, NSDictionary *socketDic) {

验证映射: YBSocketLive -> > XGGYBSocketLive
  ❌ 旧类名 'YBSocketLive' 仍有       43 个引用未替换
  未替换的引用:
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m:#import "YBSocketLive.h"
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m:    [[YBSocketLive liveSocketManeger]liveDisconnectSocket];
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m:        [[YBSocketLive liveSocketManeger] liveSendMsg:minstr([eventDic valueForKey:@"ct"])];
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m:        [[YBSocketLive liveSocketManeger] liveSendOnSaleShowHidden:eventDic];
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m:            [[YBSocketLive liveSocketManeger] liveSendAnchorCtrOfAcntion:@"5" andExtDic:@{@"pkuid":minstr([eventDic valueForKey:@"uid"]),@"pkpull":@""}];

验证映射: YBVipVC -> > XGGYBVipVC
  ❌ 旧类名 'YBVipVC' 仍有       16 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:#import "YBVipVC.h"
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m:                 YBVipVC *vipVC = [[YBVipVC alloc]init];
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m:#import "YBVipVC.h"
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m:                YBVipVC *vipVC = [[YBVipVC alloc]init];
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:#import "YBVipVC.h"

验证映射: vipBuyView -> > XGGvipBuyView
  ❌ 旧类名 'vipBuyView' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//功能/会员/view/XGGvipBuyView.h://  vipBuyView.h
    YBVideo//功能/会员/view/XGGvipBuyView.h:@interface vipBuyView : UIView
    YBVideo//功能/会员/view/XGGvipBuyView.m://  vipBuyView.m
    YBVideo//功能/会员/view/XGGvipBuyView.m:#import "vipBuyView.h"
    YBVideo//功能/会员/view/XGGvipBuyView.m:@interface vipBuyView(){

验证映射: YBVipCell -> > XGGYBVipCell
  ❌ 旧类名 'YBVipCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//功能/会员/view/XGGYBVipCell.m://  YBVipCell.m
    YBVideo//功能/会员/view/XGGYBVipCell.m:#import "YBVipCell.h"
    YBVideo//功能/会员/view/XGGYBVipCell.m:@implementation YBVipCell
    YBVideo//功能/会员/view/XGGYBVipCell.m:+(YBVipCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index {
    YBVideo//功能/会员/view/XGGYBVipCell.m:    YBVipCell *cell = [table dequeueReusableCellWithIdentifier:@"YBVipCell"];

验证映射: YBVipHeader -> > XGGYBVipHeader
  ❌ 旧类名 'YBVipHeader' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//功能/会员/view/XGGYBVipHeader.m://  YBVipHeader.m
    YBVideo//功能/会员/view/XGGYBVipHeader.m:#import "YBVipHeader.h"
    YBVideo//功能/会员/view/XGGYBVipHeader.m:@implementation YBVipHeader
    YBVideo//功能/会员/view/XGGYBVipHeader.h://  YBVipHeader.h
    YBVideo//功能/会员/view/XGGYBVipHeader.h:@interface YBVipHeader : UIView

验证映射: YBRechargeVC -> > XGGYBRechargeVC
  ❌ 旧类名 'YBRechargeVC' 仍有       18 个引用未替换
  未替换的引用:
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m:#import "YBRechargeVC.h"
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m:           YBRechargeVC *chargeVC = [[YBRechargeVC alloc]init];
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:#import "YBRechargeVC.h"
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:           YBRechargeVC *chargeVC = [[YBRechargeVC alloc]init];
    YBVideo//店铺/买家端/保证金/XGGBondViewController.m:#import "YBRechargeVC.h"

验证映射: YBRechargeType -> > XGGYBRechargeType
  ❌ 旧类名 'YBRechargeType' 仍有       40 个引用未替换
  未替换的引用:
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m:#import "YBRechargeType.h"
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m:            [[YBRechargeType chargeManeger] selPayPalAndPatameter:payDic rechargeType:rechargeType_Goods complete:^(int stateCode, int payType, NSString *msg) {
    YBVideo//功能/会员/view/XGGvipBuyView.m:#import "YBRechargeType.h"
    YBVideo//功能/会员/view/XGGvipBuyView.m:            [YBRechargeType chargeManeger].aliPayKey = _aliapp_key_ios;
    YBVideo//功能/会员/view/XGGvipBuyView.m:            [YBRechargeType chargeManeger].aliPayPartner = _aliapp_partner;

验证映射: fansModel -> > XGGfansModel
  ❌ 旧类名 'fansModel' 仍有       44 个引用未替换
  未替换的引用:
    YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.h://  fansModel.h
    YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.h:@interface fansModel : NSObject
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:#import "fansModel.h"
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:        fansModel *model = [fansModel modelWithDic:dic];
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:    fansModel *model = self.fansmodels[indexPath.row];

验证映射: BlackListVC -> > XGGBlackListVC
  ❌ 旧类名 'BlackListVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.h://  BlackListVC.h
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.h:@interface BlackListVC : YBBaseViewController
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m://  BlackListVC.m
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m:#import "BlackListVC.h"
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m:@interface BlackListVC ()<UITableViewDelegate,UITableViewDataSource>

验证映射: fans -> > XGGfans
  ❌ 旧类名 'fans' 仍有       23 个引用未替换
  未替换的引用:
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:#import "fans.h"
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:    fans *cell = [fans cellWithTableView:tableView];
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m:#import "fans.h"
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m:    fans *cell = [fans cellWithTableView:tableView];
    YBVideo//功能/粉丝_关注_拉黑/XGGfans.h://  fans.h

验证映射: BlackListCell -> > blackListCell
  ❌ 旧类名 'BlackListCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.m://  BlackListCell.m
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.m:#import "BlackListCell.h"
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.m:@implementation BlackListCell
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.m:+(BlackListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.m:    BlackListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BlackListCell"];

验证映射: fansViewController -> > XGGfansViewController
  ❌ 旧类名 'fansViewController' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:#import "fansViewController.h"
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:@interface  fansViewController()<UITableViewDelegate,UITableViewDataSource,guanzhu>
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m:@implementation fansViewController
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.h://  fansViewController.h
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.h:@interface fansViewController : YBBaseViewController

验证映射: attrViewController -> > XGGattrViewController
  ❌ 旧类名 'attrViewController' 仍有        7 个引用未替换
  未替换的引用:
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m:#import "attrViewController.h"
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m:@interface attrViewController ()<UITableViewDelegate,UITableViewDataSource,guanzhu>
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m:@implementation attrViewController
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.h://  attrViewController.h
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.h:@interface attrViewController : YBBaseViewController

验证映射: PubH5 -> > XGGPubH5
  ❌ 旧类名 'PubH5' 仍有       44 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:#import "PubH5.h"
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m:#import "PubH5.h"
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m:            PubH5 *h5vc = [[PubH5 alloc]init];
    YBVideo//登录注册/XGGDspLoginVC.m:#import "PubH5.h"
    YBVideo//登录注册/XGGDspLoginVC.m://    PubH5 *VC = [[PubH5 alloc]init];

验证映射: addHotVideoVC -> > XGGaddHotVideoVC
  ❌ 旧类名 'addHotVideoVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:#import "addHotVideoVC.h"
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:            addHotVideoVC *hot = [[addHotVideoVC alloc]init];
    YBVideo//功能/上热门/XGGaddHotVideoVC.h://  addHotVideoVC.h
    YBVideo//功能/上热门/XGGaddHotVideoVC.h:@interface addHotVideoVC : YBBaseViewController
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m:#import "addHotVideoVC.h"

验证映射: UpHotCell -> > XGGUpHotCell
  ❌ 旧类名 'UpHotCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//功能/上热门/XGGUpHotCell.m://  UpHotCell.m
    YBVideo//功能/上热门/XGGUpHotCell.m:#import "UpHotCell.h"
    YBVideo//功能/上热门/XGGUpHotCell.m:@implementation UpHotCell
    YBVideo//功能/上热门/XGGUpHotCell.m:+(UpHotCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
    YBVideo//功能/上热门/XGGUpHotCell.m:    UpHotCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UpHotCell"];

验证映射: HotVideoDetailVC -> > XGGHotVideoDetailVC
  ❌ 旧类名 'HotVideoDetailVC' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m://  HotVideoDetailVC.m
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m:#import "HotVideoDetailVC.h"
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m:@interface HotVideoDetailVC ()<UITableViewDelegate, UITableViewDataSource,upHotCellDelegate>
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m:@implementation HotVideoDetailVC
    YBVideo//功能/上热门/XGGaddHotVideoVC.m:#import "HotVideoDetailVC.h"

验证映射: Loginbonus -> > XGGLoginbonus
  ❌ 旧类名 'Loginbonus' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:#import "Loginbonus.h"  //每天第一次登录
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:    Loginbonus *firstLV;
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:    firstLV = [[Loginbonus alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)AndNSArray:bonus_list AndDay:bonus_day andDayCount:dayCount andIsBonus:is_bonus];
    YBVideo//功能/登录奖励/XGGLoginbonus.h://  Loginbonus.h
    YBVideo//功能/登录奖励/XGGLoginbonus.h:@interface Loginbonus : UIView

验证映射: LogFirstCell -> > XGGLogFirstCell
  ❌ 旧类名 'LogFirstCell' 仍有       10 个引用未替换
  未替换的引用:
    YBVideo//功能/登录奖励/XGGLogFirstCell.m://  LogFirstCell.m
    YBVideo//功能/登录奖励/XGGLogFirstCell.m:#import "LogFirstCell.h"
    YBVideo//功能/登录奖励/XGGLogFirstCell.m:@interface LogFirstCell ()
    YBVideo//功能/登录奖励/XGGLogFirstCell.m:@implementation LogFirstCell
    YBVideo//功能/登录奖励/XGGLogFirstCell.h://  LogFirstCell.h

验证映射: LogFirstCell2 -> > XGGLogFirstCell2
  ❌ 旧类名 'LogFirstCell2' 仍有        9 个引用未替换
  未替换的引用:
    YBVideo//功能/登录奖励/XGGLogFirstCell2.m://  LogFirstCell2.m
    YBVideo//功能/登录奖励/XGGLogFirstCell2.m:#import "LogFirstCell2.h"
    YBVideo//功能/登录奖励/XGGLogFirstCell2.m:@implementation LogFirstCell2
    YBVideo//功能/登录奖励/XGGLoginbonus.m:#import "LogFirstCell2.h"
    YBVideo//功能/登录奖励/XGGLoginbonus.m:    LogFirstCell2 *selectCell2;

验证映射: searchVC -> > XGGsearchVC
  ❌ 旧类名 'searchVC' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//首页/XGGYBHomeViewController.m:#import "searchVC.h"
    YBVideo//首页/XGGYBHomeViewController.m:    searchVC *search = [[searchVC alloc]init];
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m:#import "searchVC.h"
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m://  searchVC.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m:#import "searchVC.h"

验证映射: SearchHistoryCell -> > XGGSearchHistoryCell
  ❌ 旧类名 'SearchHistoryCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m:#import "SearchHistoryCell.h"
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m:            SearchHistoryCell *cell = [SearchHistoryCell cellWithTab:tableView andIndexPath:indexPath];
    YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.h://  SearchHistoryCell.h
    YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.h:@interface SearchHistoryCell : UITableViewCell
    YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.h:+(SearchHistoryCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath;

验证映射: YBSearchBarView -> > XGGYBSearchBarView
  ❌ 旧类名 'YBSearchBarView' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m:#import "YBSearchBarView.h"
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m:@property (nonatomic, strong) YBSearchBarView *searchView;
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m:    _searchView = [[YBSearchBarView alloc]initWithFrame:CGRectMake(0,9, _window_width,40)];
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m:#import "YBSearchBarView.h"
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m:    YBSearchBarView *searchView;

验证映射: StorageConfig -> > XGGStorageConfig
  ❌ 旧类名 'StorageConfig' 仍有       39 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGAppDelegate.m:    [StorageConfig clearResTime];
    YBVideo//功能/存储功能类/XGGStorageConfig.h://  StorageConfig.h
    YBVideo//功能/存储功能类/XGGStorageConfig.h:@interface StorageConfig : NSObject
    YBVideo//功能/存储功能类/XGGYBStorageObj.m:#import "StorageConfig.h"
    YBVideo//功能/存储功能类/XGGYBStorageObj.m:    NSDate *oldDate = [StorageConfig getResTime] ? [StorageConfig getResTime] : [NSDate date];

验证映射: YBStorageObj -> > XGGYBStorageObj
  ❌ 旧类名 'YBStorageObj' 仍有       66 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "YBStorageObj.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:    [[YBStorageObj storageManeger]getCOSType:^(int code) {
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:        [[YBStorageObj storageManeger]yb_storageImg:saveImg andName:imageName progress:^(CGFloat percent) {
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:        [[YBStorageObj storageManeger]yb_storageVideoOrVoice:videoPath andName:videoName progress:^(CGFloat percent) {
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:            [[YBStorageObj storageManeger]yb_storageVideoOrVoice:_waterVideoPath andName:videoName progress:^(CGFloat percent) {

验证映射: detailmodel -> > XGGdetailmodel
  ❌ 旧类名 'detailmodel' 仍有       21 个引用未替换
  未替换的引用:
    YBVideo//功能/评论/XGGcommDetailCell.m:- (void)setModel:(detailmodel *)model{
    YBVideo//功能/评论/XGGcommentview.m:- (void)playDetailesVoice:(detailmodel *)model andCell:(commDetailCell *)comcell{
    YBVideo//功能/评论/XGGcommentview.m:        detailmodel *model = mod;
    YBVideo//功能/评论/XGGdetailmodel.h://  detailmodel.h
    YBVideo//功能/评论/XGGdetailmodel.h:@interface detailmodel : NSObject

验证映射: commentModel -> > XGGcommentModel
  ❌ 旧类名 'commentModel' 仍有       20 个引用未替换
  未替换的引用:
    YBVideo//功能/评论/XGGcommentview.m:#import "commentModel.h"
    YBVideo//功能/评论/XGGcommentview.m:- (void)reloadCurCell:(commentModel *)model andIndex:(NSIndexPath *)curIndex andReplist:(NSArray *)list needRefresh:(BOOL)needRefresh{
    YBVideo//功能/评论/XGGcommentview.m:-(void)showActionSheet:(NSIndexPath*)indexPath model:(commentModel *)cModel{
    YBVideo//功能/评论/XGGcommentview.m://    commentModel *model = _modelarray[indexPath.row];
    YBVideo//功能/评论/XGGcommentview.m:    cell.model = [[commentModel alloc]initWithDic:_itemsarray[indexPath.row]];

验证映射: commDetailCell -> > XGGcommDetailCell
  ❌ 旧类名 'commDetailCell' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//功能/评论/XGGcommDetailCell.m://  commDetailCell.m
    YBVideo//功能/评论/XGGcommDetailCell.m:#import "commDetailCell.h"
    YBVideo//功能/评论/XGGcommDetailCell.m:@implementation commDetailCell
    YBVideo//功能/评论/XGGcommentview.m:#import "commDetailCell.h"
    YBVideo//功能/评论/XGGcommentview.m:@property (nonatomic,strong) commDetailCell *detailVoiceCell;

验证映射: commentview -> > XGGcommentview
  ❌ 旧类名 'commentview' 仍有       15 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:#import "commentview.h"
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:    commentview *_commentView;
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:    _commentView = [[commentview alloc]initWithFrame:CGRectMake(0,_window_height, _window_width, _window_height) hide:^(NSString *type) {
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:    __weak commentview *weakComment = _commentView;
    YBVideo//消息/XGGMsgTopPubVC.m:#import "commentview.h"

验证映射: YBCommentToolBar -> > XGGYBCommentToolBar
  ❌ 旧类名 'YBCommentToolBar' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:#import "YBCommentToolBar.h"
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:@property(nonatomic,strong)YBCommentToolBar *commentTool;
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:- (YBCommentToolBar *)commentTool {
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:        _commentTool = [[YBCommentToolBar alloc]initWithFrame:CGRectMake(0,_window_height - 50-ShowDiff, _window_width, 50+ShowDiff) andVCType:0 superView:self.view];
    YBVideo//功能/评论/XGGcommentview.m:#import "YBCommentToolBar.h"

验证映射: commCell -> > XGGcommCell
  ❌ 旧类名 'commCell' 仍有       15 个引用未替换
  未替换的引用:
    YBVideo//功能/评论/XGGcommentview.m:#import "commCell.h"
    YBVideo//功能/评论/XGGcommentview.m:    commCell *_currentCell;
    YBVideo//功能/评论/XGGcommentview.m:@property (nonatomic,strong) commCell *voiceCell;
    YBVideo//功能/评论/XGGcommentview.m:    commCell *cell;
    YBVideo//功能/评论/XGGcommentview.m:    _currentCell = (commCell*)[tableView cellForRowAtIndexPath:indexPath];

验证映射: YBYoungModeVC -> > XGGYBYoungModeVC
  ❌ 旧类名 'YBYoungModeVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/青少年/XGGYBYoungManager.m:#import "YBYoungModeVC.h"
    YBVideo//功能/青少年/XGGYBYoungManager.m:    YBYoungModeVC *modeVC = [[YBYoungModeVC alloc]init];
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.h://  YBYoungModeVC.h
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.h:@interface YBYoungModeVC : YBBaseViewController
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m://  YBYoungModeVC.m

验证映射: YBYoungSetVC -> > XGGYBYoungSetVC
  ❌ 旧类名 'YBYoungSetVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.h://  YBYoungSetVC.h
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.h:@interface YBYoungSetVC : YBBaseViewController
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m:#import "YBYoungSetVC.h"
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m:    YBYoungSetVC *setVC = [[YBYoungSetVC alloc]init];
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m://  YBYoungSetVC.m

验证映射: RKCodeView -> > XGGRKCodeView
  ❌ 旧类名 'RKCodeView' 仍有       21 个引用未替换
  未替换的引用:
    YBVideo//功能/青少年/vc/密码/XGGRKCodeView.h://  RKCodeView.h
    YBVideo//功能/青少年/vc/密码/XGGRKCodeView.h:@interface RKCodeView : UIView
    YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.h:#import "RKCodeView.h"
    YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.m://#import "RKCodeView.h"
    YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.m:@property (nonatomic,strong) NSMutableArray <RKCodeView *> *arrayTextFidld;

验证映射: RKCodeInputView -> > XGGRKCodeInputView
  ❌ 旧类名 'RKCodeInputView' 仍有       16 个引用未替换
  未替换的引用:
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:#import "RKCodeInputView.h"
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:@property(nonatomic,strong)RKCodeInputView *civOld;     //旧密码
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:@property(nonatomic,strong)RKCodeInputView *civNew;     //新密码
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:@property(nonatomic,strong)RKCodeInputView *civSure;    //确认密码
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:        RKCodeInputView *codeIV = [[RKCodeInputView alloc]init];

验证映射: YBYoungModifyVC -> > XGGYBYoungModifyVC
  ❌ 旧类名 'YBYoungModifyVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m://  YBYoungModifyVC.m
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:#import "YBYoungModifyVC.h"
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:@interface YBYoungModifyVC ()
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m:@implementation YBYoungModifyVC
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m:#import "YBYoungModifyVC.h"

验证映射: YBYoungSmall -> > XGGYBYoungSmall
  ❌ 旧类名 'YBYoungSmall' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//公共方法类/XGGPublicObj.m:#import "YBYoungSmall.h"
    YBVideo//公共方法类/XGGPublicObj.m:    YBYoungSmall *youngSmall = nil;
    YBVideo//公共方法类/XGGPublicObj.m:        }else if ([view isKindOfClass:[YBYoungSmall class]]){
    YBVideo//公共方法类/XGGPublicObj.m:            youngSmall = (YBYoungSmall *)view;
    YBVideo//功能/青少年/XGGYBYoungManager.m:#import "YBYoungSmall.h"

验证映射: YBYoungManager -> > XGGYBYoungManager
  ❌ 旧类名 'YBYoungManager' 仍有       35 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGAppDelegate.m:    [[YBYoungManager shareInstance] appKilled];
    YBVideo//其他类/XGGAppDelegate.m:    [[YBYoungManager shareInstance] appResignActive];
    YBVideo//其他类/XGGAppDelegate.m:    [[YBYoungManager shareInstance] appActive];
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m:            if ([YBYoungManager shareInstance].youngSwitch == 1) {
    YBVideo//首页/XGGYBHomeViewController.m:    [[YBYoungManager shareInstance] checkYoungStatus:YoungFrom_Home];

验证映射: BusinessCardVC -> > XGGBusinessCardVC
  ❌ 旧类名 'BusinessCardVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/我的名片/XGGBusinessCardVC.h://  BusinessCardVC.h
    YBVideo//功能/我的名片/XGGBusinessCardVC.h:@interface BusinessCardVC : YBBaseViewController
    YBVideo//功能/我的名片/XGGBusinessCardVC.m://  BusinessCardVC.m
    YBVideo//功能/我的名片/XGGBusinessCardVC.m:#import "BusinessCardVC.h"
    YBVideo//功能/我的名片/XGGBusinessCardVC.m:@interface BusinessCardVC ()

验证映射: RKKeepAlive -> > XGGRKKeepAlive
  ❌ 旧类名 'RKKeepAlive' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGAppDelegate.m:    [[RKKeepAlive sharedKeepInstance] startAppLifeCycleMonitor];
    YBVideo//功能/KeepLive/XGGRKKeepAlive.h://  RKKeepAlive.h
    YBVideo//功能/KeepLive/XGGRKKeepAlive.h:@interface RKKeepAlive : NSObject
    YBVideo//功能/KeepLive/XGGRKKeepAlive.m://  RKKeepAlive.m
    YBVideo//功能/KeepLive/XGGRKKeepAlive.m:#import "RKKeepAlive.h"

验证映射: MyAdvertCell -> > XGGMyAdvertCell
  ❌ 旧类名 'MyAdvertCell' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/广告管理/XGGMyAdvertCell.h://  MyAdvertCell.h
    YBVideo//功能/广告管理/XGGMyAdvertCell.h:@interface MyAdvertCell : UICollectionViewCell
    YBVideo//功能/广告管理/XGGMyAdvertVC.m:#import "MyAdvertCell.h"
    YBVideo//功能/广告管理/XGGMyAdvertVC.m:    [self.collectionView registerNib:[UINib nibWithNibName:@"MyAdvertCell" bundle:nil] forCellWithReuseIdentifier:@"MyAdvertCell"];
    YBVideo//功能/广告管理/XGGMyAdvertVC.m:    MyAdvertCell *cell = (MyAdvertCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"MyAdvertCell" forIndexPath:indexPath];

验证映射: AdvertManagerVC -> > XGGAdvertManagerVC
  ❌ 旧类名 'AdvertManagerVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m://  AdvertManagerVC.m
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m:#import "AdvertManagerVC.h"
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m:@interface AdvertManagerVC ()<TZImagePickerControllerDelegate,UINavigationControllerDelegate,UIImagePickerControllerDelegate,UITextViewDelegate>
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m:@implementation AdvertManagerVC
    YBVideo//功能/广告管理/XGGAdvertManagerVC.h://  AdvertManagerVC.h

验证映射: MyAdvertVC -> > XGGMyAdvertVC
  ❌ 旧类名 'MyAdvertVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/广告管理/XGGMyAdvertVC.m://  MyAdvertVC.m
    YBVideo//功能/广告管理/XGGMyAdvertVC.m:#import "MyAdvertVC.h"
    YBVideo//功能/广告管理/XGGMyAdvertVC.m:@interface MyAdvertVC ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>
    YBVideo//功能/广告管理/XGGMyAdvertVC.m:@implementation MyAdvertVC
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m:#import "MyAdvertVC.h"

验证映射: lookVGoodsDView -> > XGGlookVGoodsDView
  ❌ 旧类名 'lookVGoodsDView' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:#import "lookVGoodsDView.h"
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:    lookVGoodsDView *goodsDView;
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:            goodsDView = [[lookVGoodsDView alloc]initWithGoodsMsg:infos];//[_dataDic valueForKey:@"goodsinfo"]];
    YBVideo//功能/观看商品/XGGlookVGoodsDView.m://  lookVGoodsDView.m
    YBVideo//功能/观看商品/XGGlookVGoodsDView.m:#import "lookVGoodsDView.h"

验证映射: YBInvitationView -> > XGGYBInvitationView
  ❌ 旧类名 'YBInvitationView' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//公共方法类/XGGYBAlertView.m:#import "YBInvitationView.h"
    YBVideo//公共方法类/XGGPublicObj.m:#import "YBInvitationView.h"
    YBVideo//公共方法类/XGGPublicObj.m:    YBInvitationView *inviteV = nil;
    YBVideo//公共方法类/XGGPublicObj.m:        if ([view isKindOfClass:[YBInvitationView class]]) {
    YBVideo//公共方法类/XGGPublicObj.m:            inviteV = (YBInvitationView *)view;

验证映射: YBInviteCode -> > XGGYBInviteCode
  ❌ 旧类名 'YBInviteCode' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//首页/XGGYBHomeViewController.m:    [YBInviteCode checkAgent];
    YBVideo//登录注册/XGGDspLoginVC.m:    [YBInviteCode checkAgent];
    YBVideo//功能/邀请码/XGGYBInviteCode.m://  YBInviteCode.m
    YBVideo//功能/邀请码/XGGYBInviteCode.m:#import "YBInviteCode.h"
    YBVideo//功能/邀请码/XGGYBInviteCode.m:@implementation YBInviteCode

验证映射: YBVideoReportVC -> > XGGYBVideoReportVC
  ❌ 旧类名 'YBVideoReportVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:#import "YBVideoReportVC.h"
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m:            YBVideoReportVC *jubao = [[YBVideoReportVC alloc]init];
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.h://  YBVideoReportVC.h
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.h:@interface YBVideoReportVC : YBBaseViewController
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m://  YBVideoReportVC.m

验证映射: YBReportCell -> > XGGYBReportCell
  ❌ 旧类名 'YBReportCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m:#import "YBReportCell.h"
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m:    YBReportCell *cell = [tableView dequeueReusableCellWithIdentifier:@"jubaoCell"];
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m:        cell = [[YBReportCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"jubaoCell"];
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m:#import "YBReportCell.h"
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m:    YBReportCell *cell = [tableView dequeueReusableCellWithIdentifier:@"jubaoCell"];

验证映射: YBLiveReportVC -> > XGGYBLiveReportVC
  ❌ 旧类名 'YBLiveReportVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:#import "YBLiveReportVC.h"
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m:    YBLiveReportVC *vc = [[YBLiveReportVC alloc]init];
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m://  YBLiveReportVC.m
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m:#import "YBLiveReportVC.h"
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m:@interface YBLiveReportVC ()<UITableViewDelegate,UITableViewDataSource,UITextViewDelegate>{

验证映射: YBLanguageTools -> > XGGYBLanguageTools
  ❌ 旧类名 'YBLanguageTools' 仍有       25 个引用未替换
  未替换的引用:
    YBVideo//其他类/XGGAppDelegate.m:        BOOL isCn = [[YBLanguageTools shareInstance] isChinese];
    YBVideo//其他类/XGGAppDelegate.m:    [[YBLanguageTools shareInstance] resetLanguage:[[NSUserDefaults standardUserDefaults] objectForKey:CurrentLanguage] withFrom:@"appdelegate"];
    YBVideo//XGGNetwork/XGGNetworkManager.m:    NSString *serLang = [YBLanguageTools serviceLang];
    YBVideo//公共方法类/XGGiOSNetworking.m:    NSString *serLang = [YBLanguageTools serviceLang];
    YBVideo//直播模块/socket/XGGYBSocketLive.m:                          @"lang":[YBLanguageTools serviceLang],

验证映射: YBTakeSameVideoVC -> > XGGYBTakeSameVideoVC
  ❌ 旧类名 'YBTakeSameVideoVC' 仍有       14 个引用未替换
  未替换的引用:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:#import "YBTakeSameVideoVC.h"
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m:            if ([forVC isKindOfClass:[YBTakeSameVideoVC class]]) {
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:#import "YBTakeSameVideoVC.h"
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m:                if ([forVC isKindOfClass:[YBTakeSameVideoVC class]]) {
    YBVideo//首页/推荐/XGGYBLookVideoVC.m:            if ([_fromWhere isEqual:@"YBTakeSameVideoVC"]) {

验证映射: YBDestroySureVC -> > XGGYBDestroySureVC
  ❌ 旧类名 'YBDestroySureVC' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/注销账号/XGGYBDestroySureVC.h://  YBDestroySureVC.h
    YBVideo//功能/注销账号/XGGYBDestroySureVC.h:@interface YBDestroySureVC : YBBaseViewController
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m:#import "YBDestroySureVC.h"
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m:    YBDestroySureVC *sureVC = [[YBDestroySureVC alloc]init];
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m://  YBDestroySureVC.m

验证映射: YBDestroyCell -> > XGGYBDestroyCell
  ❌ 旧类名 'YBDestroyCell' 仍有       11 个引用未替换
  未替换的引用:
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m:#import "YBDestroyCell.h"
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m:    YBDestroyCell *cell = [YBDestroyCell cellWithTab:tableView index:indexPath];
    YBVideo//功能/注销账号/view/XGGYBDestroyCell.m://  YBDestroyCell.m
    YBVideo//功能/注销账号/view/XGGYBDestroyCell.m:#import "YBDestroyCell.h"
    YBVideo//功能/注销账号/view/XGGYBDestroyCell.m:@implementation YBDestroyCell

验证映射: YBDestroyAccount -> > XGGYBDestroyAccount
  ❌ 旧类名 'YBDestroyAccount' 仍有        8 个引用未替换
  未替换的引用:
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m://  YBDestroyAccount.m
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m:#import "YBDestroyAccount.h"
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m:@interface YBDestroyAccount ()<UITableViewDelegate,UITableViewDataSource>
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m:@implementation YBDestroyAccount
    YBVideo//功能/注销账号/XGGYBDestroyAccount.h://  YBDestroyAccount.h

验证映射: YBPageControl -> > XGGYBPageControl
  ❌ 旧类名 'YBPageControl' 仍有       13 个引用未替换
  未替换的引用:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:@property(nonatomic,strong)YBPageControl *roomGamePage;
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:- (YBPageControl *)roomGamePage {
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:        _roomGamePage = [[YBPageControl alloc]init];
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m:@property(nonatomic,strong)YBPageControl *roomGamePage;
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m:- (YBPageControl *)roomGamePage {

验证映射: RKPaintedGiftView -> > XGGRKPaintedGiftView
  ❌ 旧类名 'RKPaintedGiftView' 仍有       12 个引用未替换
  未替换的引用:
    YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.h://  RKPaintedGiftView.h
    YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.h:@interface RKPaintedGiftView : UIView
    YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.m://  RKPaintedGiftView.m
    YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.m:#import "RKPaintedGiftView.h"
    YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.m:@interface RKPaintedGiftView()

验证映射: RKShowPaintedView -> > XGGRKShowPaintedView
  ❌ 旧类名 'RKShowPaintedView' 仍有       18 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:#import "RKShowPaintedView.h"
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:@property(nonatomic,strong)RKShowPaintedView *paintedShowRegion;    //手绘礼物显示区域
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:- (RKShowPaintedView *)paintedShowRegion {
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:        _paintedShowRegion = [[RKShowPaintedView alloc]init];
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:#import "RKShowPaintedView.h"

验证映射: expensiveGiftV -> > XGGexpensiveGiftV
  ❌ 旧类名 'expensiveGiftV' 仍有       17 个引用未替换
  未替换的引用:
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:#import "expensiveGiftV.h"
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:    expensiveGiftV *haohualiwuV;//豪华礼物
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:        haohualiwuV = [[expensiveGiftV alloc]init];
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m:        haohualiwuV = [[expensiveGiftV alloc]init];
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m:#import "expensiveGiftV.h"

验证映射: continueGift -> > XGGcontinueGift
  ❌ 旧类名 'continueGift' 仍有       14 个引用未替换
  未替换的引用:
