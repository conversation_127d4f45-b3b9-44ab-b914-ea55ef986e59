#!/bin/bash

# 回滚第三阶段的更改
echo "=== 回滚第三阶段的更改 ==="

ROLLBACK_LOG="rollback_phase3_log.txt"

# 清空日志
> "$ROLLBACK_LOG"

echo "开始回滚第三阶段的更改..." | tee -a "$ROLLBACK_LOG"
echo "回滚日志: $ROLLBACK_LOG"
echo ""

# 统计变量
total_backups=0
success_rollbacks=0
error_rollbacks=0

# 查找所有第三阶段备份文件
backup_files=$(find YBVideo/ -name "*.phase3.backup" 2>/dev/null)

if [[ -z "$backup_files" ]]; then
    echo "❌ 错误：未找到第三阶段备份文件" | tee -a "$ROLLBACK_LOG"
    exit 1
fi

echo "找到备份文件，开始回滚..." | tee -a "$ROLLBACK_LOG"
echo "" | tee -a "$ROLLBACK_LOG"

# 回滚每个备份文件
while read -r backup_file; do
    if [[ -n "$backup_file" ]]; then
        ((total_backups++))
        
        # 获取原始文件路径
        original_file="${backup_file%.phase3.backup}"
        
        echo "回滚文件 $total_backups: $original_file" | tee -a "$ROLLBACK_LOG"
        
        if [[ -f "$backup_file" && -f "$original_file" ]]; then
            # 执行回滚
            if cp "$backup_file" "$original_file"; then
                echo "  ✅ 回滚成功" | tee -a "$ROLLBACK_LOG"
                ((success_rollbacks++))
            else
                echo "  ❌ 回滚失败" | tee -a "$ROLLBACK_LOG"
                ((error_rollbacks++))
            fi
        else
            echo "  ⚠️  文件不存在，跳过" | tee -a "$ROLLBACK_LOG"
            ((error_rollbacks++))
        fi
        
        # 每处理100个文件显示进度
        if [[ $((total_backups % 100)) -eq 0 ]]; then
            echo "已处理 $total_backups 个备份文件..."
        fi
    fi
done <<< "$backup_files"

echo "" | tee -a "$ROLLBACK_LOG"
echo "=== 回滚完成 ===" | tee -a "$ROLLBACK_LOG"
echo "总备份文件数: $total_backups" | tee -a "$ROLLBACK_LOG"
echo "成功回滚: $success_rollbacks" | tee -a "$ROLLBACK_LOG"
echo "失败: $error_rollbacks" | tee -a "$ROLLBACK_LOG"

if [[ $error_rollbacks -gt 0 ]]; then
    echo "" | tee -a "$ROLLBACK_LOG"
    echo "⚠️  有 $error_rollbacks 个文件回滚失败，请检查日志" | tee -a "$ROLLBACK_LOG"
    exit 1
else
    echo "" | tee -a "$ROLLBACK_LOG"
    echo "✅ 第三阶段回滚完成！所有文件已恢复到第三阶段执行前的状态。" | tee -a "$ROLLBACK_LOG"
    exit 0
fi
