//
//  YBCitySelCell.m
//  YBVideo
//
//  Created by YB007 on 2020/10/17.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBCitySelCell.h"

@implementation YBCitySelCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+(YBCitySelCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index {
    YBCitySelCell *cell = [table dequeueReusableCellWithIdentifier:@"YBCitySelCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"YBCitySelCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.backgroundColor = CellRow_Cor;
    cell.contentView.backgroundColor = CellRow_Cor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}


@end
