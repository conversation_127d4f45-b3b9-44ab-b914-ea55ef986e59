//
//  YBCitySelVC.m
//  YBVideo
//
//  Created by YB007 on 2020/10/17.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBCitySelVC.h"
#import "YBCitySelCell.h"

@interface YBCitySelVC ()<UITableViewDelegate,UITableViewDataSource>
{
    int _paging;
    int _allNums;
}
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)NSMutableArray *dataArray;
@property(nonatomic,strong)NSMutableArray *indexArray;
@property(nonatomic,strong)UIView *tableHeader;
@property(nonatomic,strong)UILabel *tableHTL;
@property(nonatomic,strong)UILabel *tableCityL;

@end

@implementation YBCitySelVC

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self pullData];
    
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.titleL.text = YZMsg(@"切换城市");
    self.leftBtn.imageEdgeInsets = UIEdgeInsetsMake(13, 13, 13, 13);
    [self.leftBtn setImage:[UIImage imageNamed:@"gray_close"] forState:0];
    
    _paging = 1;
    _allNums = 0;
    self.dataArray = [NSMutableArray array];
    self.indexArray = [NSMutableArray array];
    
    [self.view addSubview:self.tableView];
    self.tableView.tableHeaderView = self.tableHeader;
    self.tableView.tableHeaderView.frame = CGRectMake(0, 0, _window_width, 90);
    
}

#pragma mark -
-(void)pullData {
    
    [YBNetworking postWithUrl:@"Video.getCitys" Dic:@{} Suc:^(int code, id info, NSString *msg) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging == 1) {
                [_dataArray removeAllObjects];
                [_indexArray removeAllObjects];
                _allNums = 0;
            }
            if (infoA.count <= 0) {
                [_tableView.mj_footer endRefreshingWithNoMoreData];
            }else {
                [_dataArray addObjectsFromArray:infoA];
                for (int i = 0; i < infoA.count; i++) {
                    NSDictionary *subDic = infoA[i];
                    [_indexArray addObject:minstr([subDic valueForKey:@"title"])];
                    NSArray *listA = [NSArray arrayWithArray:[subDic valueForKey:@"lists"]];
                    _allNums += listA.count;
                }
            }
            if (_allNums <= 0) {
                [PublicView showTextNoData:_tableView text1:@"" text2:YZMsg(@"暂无数据") centerY:0.8];
            }else {
                [PublicView hiddenTextNoData:_tableView];
            }
            [_tableView reloadData];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
    }];
    
}
#pragma mark - UITableViewDelegate、UITableViewDataSource
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    
    return 30;
}
-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *headerVie = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 30)];
    NSDictionary *subDic = _dataArray[section];
    UILabel *titleL = [[UILabel alloc]init];
    titleL.font = SYS_Font(16);
    titleL.textColor = RGB_COLOR(@"#323232", 1);
    titleL.text = minstr([subDic valueForKey:@"title"]);
    [headerVie addSubview:titleL];
    [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(headerVie.mas_left).offset(15);
        make.centerY.equalTo(headerVie);
    }];
    return headerVie;
}
-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    
    return 0;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return nil;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return _dataArray.count;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    NSDictionary *subDic = _dataArray[section];
    NSArray *listA = @[];
    if ([[subDic valueForKey:@"lists"] isKindOfClass:[NSArray class]]) {
        listA = [NSArray arrayWithArray:[subDic valueForKey:@"lists"]];
    }
    return listA.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBCitySelCell *cell = [YBCitySelCell cellWithTab:tableView index:indexPath];
    NSDictionary *subDic = _dataArray[indexPath.section];
    NSArray *listA = @[];
    if ([[subDic valueForKey:@"lists"] isKindOfClass:[NSArray class]]) {
        listA = [NSArray arrayWithArray:[subDic valueForKey:@"lists"]];
    }
    cell.cityNameL.text = listA[indexPath.row];
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    NSDictionary *subDic = _dataArray[indexPath.section];
    NSArray *listA = @[];
    if ([[subDic valueForKey:@"lists"] isKindOfClass:[NSArray class]]) {
        listA = [NSArray arrayWithArray:[subDic valueForKey:@"lists"]];
    }
    NSString *cityName = listA[indexPath.row];
    if (self.citySelEvent) {
        self.citySelEvent(cityName);
    }
    [self clickNaviLeftBtn];
}
-(NSArray<NSString *> *)sectionIndexTitlesForTableView:(UITableView *)tableView{
    return _indexArray;
}
#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height - 64-statusbarHeight)style:UITableViewStyleGrouped];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = Normal_Color;
        _tableView.sectionIndexColor = RGB_COLOR(@"#ffffff", 0.53);
    }
    return _tableView;
}
- (UIView *)tableHeader {
    if (!_tableHeader) {
        _tableHeader = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 80)];
        _tableHeader.backgroundColor = CellRow_Cor;
        
        UIImageView *locIV = [[UIImageView alloc]init];
        locIV.image = [UIImage imageNamed:@"城市-定位"];
        [_tableHeader addSubview:locIV];
        
        _tableHTL = [[UILabel alloc]init];
        _tableHTL.font = SYS_Font(14);
        _tableHTL.text = YZMsg(@"自动定位");
        _tableHTL.textColor = RGB_COLOR(@"#ffffff", 1);
        [_tableHeader addSubview:_tableHTL];
        
        _tableCityL = [[UILabel alloc]init];
        NSString *curStr = [PublicObj checkNull:[XGGcityDefault getLocationCity]]?@"":[XGGcityDefault getLocationCity];
        if ([curStr hasSuffix:@"市"]) {
            curStr = [curStr substringToIndex:curStr.length-1];
        }
        _tableCityL.text = curStr;
        _tableCityL.font = SYS_Font(16);
        _tableCityL.textColor = RGB_COLOR(@"#ffffff", 0.53);
        [_tableHeader addSubview:_tableCityL];
        
        [_tableHTL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(30);
            make.top.equalTo(_tableHeader);
            make.left.equalTo(locIV.mas_right).offset(3);
        }];
        [locIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(12);
            make.left.equalTo(_tableHeader.mas_left).offset(15);
            make.centerY.equalTo(_tableHTL);
        }];
        [_tableCityL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(locIV);
            make.right.lessThanOrEqualTo(_tableHeader.mas_right).offset(-15);
            make.bottom.equalTo(_tableHeader);
            make.height.mas_equalTo(50);
        }];
        
        UIButton *shadowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [shadowBtn addTarget:self action:@selector(clickShadowBtn) forControlEvents:UIControlEventTouchUpInside];
        [_tableHeader addSubview:shadowBtn];
        [shadowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.bottom.centerX.equalTo(_tableHeader);
            make.height.equalTo(_tableCityL);
        }];
        
    }
    return _tableHeader;
}
-(void)clickShadowBtn {
    NSString *curStr = [PublicObj checkNull:[XGGcityDefault getLocationCity]]?@"":[XGGcityDefault getLocationCity];
    if ([curStr hasSuffix:@"市"]) {
        curStr = [curStr substringToIndex:curStr.length-1];
    }
    if (self.citySelEvent) {
        self.citySelEvent(curStr);
    }
    [self clickNaviLeftBtn];
}


@end
