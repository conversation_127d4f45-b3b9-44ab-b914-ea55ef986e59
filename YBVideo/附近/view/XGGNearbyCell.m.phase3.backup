//
//  NearbyCell.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/27.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "NearbyCell.h"

@implementation NearbyCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    _iconIV.layer.masksToBounds = YES;
    _iconIV.layer.cornerRadius = _iconIV.width/2;
    
}

-(void)setModel:(NearbyVideoModel *)model {
    _model = model;
    int videoCoin = [model.videoCoin intValue];
    _videoCoinBtn.hidden = YES;
    if (videoCoin > 0) {
        _videoCoinBtn.hidden = NO;
        [_videoCoinBtn setTitle:model.videoCoin forState:0];
    }
    [_coverIV sd_setImageWithURL:[NSURL URLWithString:_model.videoImage]];
    [_iconIV sd_setImageWithURL:[NSURL URLWithString:_model.userAvatar]];
    _unameL.text = [NSString stringWithFormat:@"%@",_model.userName];
    _distanceL.text = [NSString stringWithFormat:@"%@",_model.distance];
    //_videoTitleL.text = minstr(_model.videoTitle);
    
    if (_isSelfLoc) {
        _locIV.hidden = _distanceL.hidden = NO;
    }else {
        _locIV.hidden = _distanceL.hidden = YES;
    }
}


@end
