//
//  MyFollowViewController.m
//  YBVideo
//
//  Created by YangBiao on 2017/9/7.
//  Copyright © 2017年 cat. All rights reserved.
//
#import "MyFollowViewController.h"
#import "searchVC.h"
#import "VideoCollectionCell.h"

#import "NearbyVideoModel.h"
#import <MJRefresh/MJRefresh.h>
#import "YBGetVideoObj.h"
@interface MyFollowViewController () <UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout,UIAlertViewDelegate>
@property(nonatomic,strong)NSString *MD5;

@property(nonatomic,strong)NSMutableArray *videoArray;
@property(nonatomic,strong)UICollectionView *videoCollection;

@end
@implementation MyFollowViewController
{
    
    UIImageView *_noVideoImage;
    NSDictionary *selectedDic;
    int selected;
    
    MJRefreshAutoNormalFooter *footer;
    NSInteger _page;
}
-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [[PublicObj publicInstance]addCusDelayedRemove];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBar.hidden = YES;
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = Normal_Color;
    
    self.leftBtn.hidden = YES;
    self.rightBtn.hidden = YES;
    //self.titleL.text = @"关注";
    self.naviView.hidden = YES;
    
    _page = 1;
    self.videoArray = [NSMutableArray array];

    [self setCollectionView];
    [self getNewVideo];
    //返回后如果有删除
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(getLiveList:) name:@"delete" object:nil];
}
//在视频页面删除视频回来后删除
-(void)getLiveList:(NSNotification *)nsnitofition{
    NSString *videoid = [NSString stringWithFormat:@"%@",[[nsnitofition userInfo] valueForKey:@"videoid"]];
    NSDictionary *deletedic = [NSDictionary dictionary];
    for (NSDictionary *subdic in _videoArray) {
        NSString *videoids = [NSString stringWithFormat:@"%@",[subdic valueForKey:@"id"]];
        if ([videoid isEqual:videoids]) {
            deletedic = subdic;
            break;
        }
    }
    if (deletedic) {
        [_videoArray removeObject:deletedic];
        [_videoCollection reloadData];
    }
}

- (void)getMoreVideo{
    _page ++;
    [self getVideoListWithPage:_page];
}
- (void)getNewVideo{
    _videoArray = [NSMutableArray array];
    _page = 1;
    [self getVideoListWithPage:_page];
}
- (void)getVideoListWithPage:(NSInteger)page{
    
    if ([[Config getOwnID] intValue] <= 0) {
        [PublicView showTextNoData:_videoCollection text1:YZMsg(@"你还没有关注任何人") text2:YZMsg(@"赶快去关注自己感兴趣的人吧") centerY:0.8];
        return;
    }
    [PublicView hiddenTextNoData:_videoCollection];
    YBWeakSelf;
    [YBNetworking postWithUrl:_url Dic:@{@"p":@(page)} Suc:^(int code, id info, NSString *msg) {
        [weakSelf.videoCollection.mj_header endRefreshing];
        [weakSelf.videoCollection.mj_footer endRefreshing];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_page == 1) {
                [_videoArray removeAllObjects];
            }
            if (infoA.count <= 0) {
                [weakSelf.videoCollection.mj_footer endRefreshingWithNoMoreData];
            }else {
                [weakSelf.videoArray addObjectsFromArray:infoA];
            }
            
            [weakSelf.videoCollection reloadData];
            if (weakSelf.videoArray.count > 0) {
                footer.hidden = NO;
                [PublicView hiddenTextNoData:weakSelf.videoCollection];
            }else{
                footer.hidden = YES;
                [PublicView showTextNoData:weakSelf.videoCollection text1:YZMsg(@"你还没有关注任何人") text2:YZMsg(@"赶快去关注自己感兴趣的人吧") centerY:0.8];
            }
        }else{
            if (weakSelf.videoArray) {
                [weakSelf.videoArray removeAllObjects];
            }
            [weakSelf.videoCollection reloadData];
            [PublicView showTextNoData:weakSelf.videoCollection text1:YZMsg(@"你还没有关注任何人") text2:YZMsg(@"赶快去关注自己感兴趣的人吧") centerY:0.8];
            [MBProgressHUD showError:msg];
           
        }
    } Fail:^(id fail) {
        if (weakSelf.videoArray) {
            [weakSelf.videoArray removeAllObjects];
        }
        [weakSelf.videoCollection reloadData];
        [PublicView showTextNoData:weakSelf.videoCollection text1:YZMsg(@"你还没有关注任何人") text2:YZMsg(@"赶快去关注自己感兴趣的人吧") centerY:0.8];
        [weakSelf.videoCollection.mj_header endRefreshing];
        [weakSelf.videoCollection.mj_footer endRefreshing];
    }];
    
    
}
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    
    NSString *vidoeID = minstr([_videoArray[indexPath.row] valueForKey:@"id"]);
    [YBGetVideoObj lookManeger].fromWhere = @"MyFollowViewController";
    [YBGetVideoObj lookManeger].videoID = vidoeID;
    [YBGetVideoObj lookManeger].playIndex = (int)indexPath.row;
    [YBGetVideoObj lookManeger].videoList = [_videoArray mutableCopy];
    [YBGetVideoObj lookManeger].paging = (int)_page;
    NSString *basUrl = [NSString stringWithFormat:@"video.getAttentionVideo&uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
    [YBGetVideoObj lookManeger].baseUrl = basUrl;
    [[YBGetVideoObj lookManeger]goLookVC];
    
}
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

//- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
//        return CGSizeMake((_window_width - 3) / 2, (_window_width - 3) / 2 * 1.3);
//}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 1;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section{
    return 1;
}
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    
    return _videoArray.count;
}
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    
    VideoCollectionCell *cell = (VideoCollectionCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"VideoCollectionCell" forIndexPath:indexPath];
    NSDictionary *dic = _videoArray[indexPath.row];
    cell.isAtten = @"1";
    NearbyVideoModel *model = [[NearbyVideoModel alloc] initWithDic:dic];
    cell.model = model;
    return cell;
}
- (void)setCollectionView{
    
    UICollectionViewFlowLayout *layout2 = [[UICollectionViewFlowLayout alloc] init];
    layout2.scrollDirection = UICollectionViewScrollDirectionVertical;
    layout2.itemSize = CGSizeMake(_window_width/2-1, (_window_width/2-1) * 1.4);
    layout2.minimumLineSpacing = 2;
    layout2.minimumInteritemSpacing = 2;
    _videoCollection = [[UICollectionView alloc] initWithFrame:CGRectMake(0, 0, _window_width, _window_height - 49 -ShowDiff) collectionViewLayout:layout2];
    _videoCollection.backgroundColor = Normal_Color;//[UIColor whiteColor];
    _videoCollection.delegate = self;
    _videoCollection.dataSource = self;
    _videoCollection.alwaysBounceVertical = YES;
    [self.view addSubview:_videoCollection];
    if (@available(iOS 11.0,*)) {
        _videoCollection.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    [_videoCollection registerNib:[UINib nibWithNibName:@"VideoCollectionCell" bundle:[NSBundle mainBundle]] forCellWithReuseIdentifier:@"VideoCollectionCell"];
    _videoCollection.mj_header = ({
        MJRefreshNormalHeader *header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(getNewVideo)];
        header.lastUpdatedTimeLabel.hidden = YES;
        header.stateLabel.hidden = YES;
        header;
    });
    footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(getMoreVideo)];
    [footer setTitle:@"" forState:MJRefreshStateRefreshing];
    [footer setTitle:@"" forState:MJRefreshStateNoMoreData];
    footer.stateLabel.font = [UIFont systemFontOfSize:15.0f];
    _videoCollection.mj_footer = footer;
    
    
}

@end
