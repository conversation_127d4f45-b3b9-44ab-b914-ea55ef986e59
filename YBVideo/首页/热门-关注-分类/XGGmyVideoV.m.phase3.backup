//
//  myVideoV.m
//  YBVideo
//
//  Created by 王敏欣 on 2017/8/4.
//  Copyright © 2017年 cat. All rights reserved.
//
#import "myVideoV.h"
#import <MJRefresh/MJRefresh.h>
#import "YBGetVideoObj.h"
#import "NearbyVideoModel.h"
#import "VideoCollectionCell.h"

#import "AFNetworking.h"
@interface myVideoV ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>
@property(nonatomic,strong)NSMutableArray *allArray;
@property(nonatomic,strong)NSArray *modelrray;
@property(nonatomic,strong)UICollectionView *collectionView;
@end
@implementation myVideoV
{
  
    NSInteger _page;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    
    self.naviView.hidden = YES;
    
    _modelrray = [NSArray array];
    _page = 1;
    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.allArray = [NSMutableArray array];
    
    
    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    flow.itemSize = CGSizeMake(_window_width/2-1, (_window_width/2-1) * 1.4);
    flow.minimumLineSpacing = 2;
    flow.minimumInteritemSpacing = 2;
    self.collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height-49-ShowDiff) collectionViewLayout:flow];
    [self.collectionView registerNib:[UINib nibWithNibName:@"VideoCollectionCell" bundle:nil] forCellWithReuseIdentifier:@"VideoCollectionCell"];
    self.collectionView.delegate =self;
    self.collectionView.dataSource = self;
    self.collectionView.mj_footer  = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        _page ++;
        [self pullInternetforNew:_page];
    }];
    
    self.collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        _page = 1;
        [self pullInternetforNew:_page];
        
    }];
    
    [self.view addSubview:self.collectionView];
    if (@available(iOS 11.0,*)) {
        self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    self.view.backgroundColor = Normal_Color;
    self.collectionView.backgroundColor = [UIColor blackColor];
  
    [self pullInternetforNew:1];
    //在视频页面输出视频后返回
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(getLiveList:) name:@"delete" object:nil];
    //发布视频成功之后返回首页刷新列表
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pullInternetforNewDown) name:@"reloadlist" object:nil];
    
}
//在视频页面删除视频回来后删除
-(void)getLiveList:(NSNotification *)nsnitofition{
    NSString *videoid = [NSString stringWithFormat:@"%@",[[nsnitofition userInfo] valueForKey:@"videoid"]];
    NSDictionary *deletedic = [NSDictionary dictionary];
    for (NSDictionary *subdic in self.allArray) {
        NSString *videoids = [NSString stringWithFormat:@"%@",[subdic valueForKey:@"id"]];
        if ([videoid isEqual:videoids]) {
            deletedic = subdic;
            break;
        }
    }
    if (deletedic) {
        [self.allArray removeObject:deletedic];
        [self.collectionView reloadData];
    }
}
-(void)refreshNear{
}
//down
-(void)pullInternetforNewDown{
    self.allArray = [NSMutableArray array];
    _page = 1;
    [self pullInternetforNew:_page];
}
-(void)getDataByFooterup{
    _page ++;
    [self pullInternetforNew:_page];
}
-(void)pullInternetforNew:(NSInteger)pages{
   
    self.collectionView.userInteractionEnabled = NO;
   
    NSString *url = [NSString stringWithFormat:@"%@&p=%ld",_url,(long)pages];
    YBWeakSelf;
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        [weakSelf.collectionView.mj_header endRefreshing];
        [weakSelf.collectionView.mj_footer endRefreshing];
        weakSelf.collectionView.userInteractionEnabled = YES;
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_page == 1) {
                [self.allArray removeAllObjects];
            }
            [self.allArray addObjectsFromArray:infoA];
            //加载成功 停止刷新
            [self.collectionView.mj_header endRefreshing];
            [self.collectionView.mj_footer endRefreshing];
            [self.collectionView reloadData];
            if (self.allArray.count > 0) {
                [PublicView hiddenTextNoData:_collectionView];
            }else{
                [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂无热门视频哦~") centerY:0.8];
            }
            if (infoA.count <= 0) {
                [self.collectionView.mj_footer endRefreshingWithNoMoreData];
            }
        }else{
            if (self.allArray) {
                [self.allArray removeAllObjects];
            }
            [self.collectionView reloadData];
            [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂无热门视频哦~") centerY:0.8];
        }
    } Fail:^(id fail) {
        weakSelf.collectionView.userInteractionEnabled = YES;
        self.collectionView.userInteractionEnabled = YES;
        if (self.allArray) {
            [self.allArray removeAllObjects];
        }
        [self.collectionView reloadData];
        [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂无热门视频哦~") centerY:0.8];
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
    }];
    
    
}
#pragma mark - Table view data source
//-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
//    return CGSizeMake((_window_width - 6)/2,(_window_width - 6)/2 * 1.4);
//}
//-(UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section
//{
//    return UIEdgeInsetsMake(2,2,2,2);
//}

-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.allArray.count;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 2;
}
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{

    NSString *vidoeID = minstr([_allArray[indexPath.row] valueForKey:@"id"]);
    [YBGetVideoObj lookManeger].fromWhere = @"myVideoV";
    [YBGetVideoObj lookManeger].videoID = vidoeID;
    [YBGetVideoObj lookManeger].playIndex = (int)indexPath.row;
    [YBGetVideoObj lookManeger].videoList = [_allArray mutableCopy];
    [YBGetVideoObj lookManeger].paging = (int)_page;
    [YBGetVideoObj lookManeger].baseUrl = _url;
    [[YBGetVideoObj lookManeger]goLookVC];
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    VideoCollectionCell *cell = (VideoCollectionCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"VideoCollectionCell" forIndexPath:indexPath];
//    cell.layer.cornerRadius = 5.0;
//    cell.layer.masksToBounds = YES;
    NSDictionary *subdic = _allArray[indexPath.row];
    cell.isList = @"1";
    cell.model = [[NearbyVideoModel alloc] initWithDic:subdic];
  
    return cell;
}



@end
