//
//  YBVideosVC.m
//  YBVideo
//
//  Created by YB007 on 2019/12/6.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBVideosVC.h"

#import "NearbyVideoModel.h"
#import "VideoCollectionCell.h"
#import "YBGetVideoObj.h"

@interface YBVideosVC ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>
{
    NSInteger _paging;
    NSString *_url;
}
@property(nonatomic,strong)NSMutableArray *dataArray;
@property(nonatomic,strong)NSArray *models;
@property(nonatomic,strong)UICollectionView *collectionView;


@end

@implementation YBVideosVC

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if (self.dataArray.count <= 0 ) {
        [self pullData];
    }
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.naviView.hidden = YES;
    
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = Normal_Color;
    
    self.dataArray = [NSMutableArray array];
    self.models = [NSArray array];
    _paging = 1;
    
    _url = [NSString stringWithFormat:@"Video.getVideoListByClass&classid=%@",_classID];
    
    [self.view addSubview:self.collectionView];
    if (@available(iOS 11.0,*)) {
        self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
}
- (NSArray *)models {
    NSMutableArray *m_array = [NSMutableArray array];
    for (NSDictionary *dic in _dataArray) {
        NearbyVideoModel *model = [NearbyVideoModel modelWithDic:dic];
        [m_array addObject:model];
    }
    _models = m_array;
    return _models;
}

-(void)pullData {
    _collectionView.userInteractionEnabled = NO;
    NSString *url = [NSString stringWithFormat:@"%@&p=%ld",_url,(long)_paging];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
        _collectionView.userInteractionEnabled = YES;
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging == 1) {
                [_dataArray removeAllObjects];
            }
            if (infoA.count <= 0) {
                [_collectionView.mj_footer endRefreshingWithNoMoreData];
            }else{
                [_dataArray addObjectsFromArray:infoA];
            }
            if (_dataArray.count > 0) {
                [PublicView hiddenTextNoData:_collectionView];
            }else{
                [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂无视频哦~") centerY:0.8];
            }
            [_collectionView reloadData];
            
        }else{
            if (_dataArray) {
                [_dataArray removeAllObjects];
            }
            [_collectionView reloadData];
            [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂无视频哦~") centerY:0.8];
        }
    } Fail:^(id fail) {
        _collectionView.userInteractionEnabled = YES;
        
        _collectionView.userInteractionEnabled = YES;
        if (_dataArray) {
            [_dataArray removeAllObjects];
        }
        [_collectionView reloadData];
        [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂无视频哦~") centerY:0.8];
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
    }];
    
}
#pragma mark - CollectionView 代理
/*
 * minimumLineSpacing、minimumInteritemSpacing去设置
 -(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
 return CGSizeMake(0,0);
 }
 -(UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section
 {
 return UIEdgeInsetsMake(0,0,0,0);
 }
 */
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 2;
}
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.models.count;
}

-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    VideoCollectionCell *cell = (VideoCollectionCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"VideoCollectionCell" forIndexPath:indexPath];
    NearbyVideoModel *model = _models[indexPath.row];
    cell.isList = @"1";
    cell.model = model;
    return cell;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    
    NSString *vidoeID = minstr([_dataArray[indexPath.row] valueForKey:@"id"]);
    [YBGetVideoObj lookManeger].fromWhere = @"YBVideoClassVC";
    [YBGetVideoObj lookManeger].videoID = vidoeID;
    [YBGetVideoObj lookManeger].playIndex = (int)indexPath.row;
    [YBGetVideoObj lookManeger].videoList = [_dataArray mutableCopy];
    [YBGetVideoObj lookManeger].paging = (int)_paging;
    [YBGetVideoObj lookManeger].baseUrl = _url;
    [[YBGetVideoObj lookManeger]goLookVC];
}

#pragma mark - set/get
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionVertical;
        flow.itemSize = CGSizeMake(_window_width/2-1, (_window_width/2-1) * 1.4);
        flow.minimumLineSpacing = 2;
        flow.minimumInteritemSpacing = 2;
        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height-49-ShowDiff) collectionViewLayout:flow];
        [_collectionView registerNib:[UINib nibWithNibName:@"VideoCollectionCell" bundle:nil] forCellWithReuseIdentifier:@"VideoCollectionCell"];
        _collectionView.delegate =self;
        _collectionView.dataSource = self;
        _collectionView.backgroundColor = Normal_Color;
        _collectionView.mj_footer  = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
            _paging ++;
            [self pullData];
        }];
        
        _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            _paging = 1;
            [self pullData];
            
        }];
    }
    return _collectionView;
}


- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    
}

@end
