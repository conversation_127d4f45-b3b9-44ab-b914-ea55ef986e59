//
//  YBLookVideoVC.h
//  YBVideo
//
//  Created by YB007 on 2019/11/6.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBBaseViewController.h"
#import <ZFPlayer/ZFPlayer.h>

typedef NS_ENUM(NSInteger,RKPlayerLeaveState) {
    RKLeaveState_Unknown,
    RKLeaveState_Playing,
    RKLeaveState_Pause,
};

@interface YBLookVideoVC : YBBaseViewController

@property (nonatomic, assign) ZFPlayerScrollViewDirection scrollViewDirection; //滚动方向(垂直、横向)

@property(nonatomic,strong)NSString *fromWhere;
/**
 *  从消息顶部的 赞、评论、@ 功能 进来不能上下滑动 sourceBaseUrl传空字符串即可
 */
@property(nonatomic,strong)NSString *sourceBaseUrl;

@property(nonatomic,assign)BOOL firstPush;                  //第一次从其他页面跳转
@property(nonatomic,assign) NSInteger pushPlayIndex;        //第一次从第几个开始播放

@property(nonatomic,strong)NSMutableArray *videoList;
@property (nonatomic,assign) NSInteger pages;

//rk_1-30
-(void)doubleClickRefreshForYou;

//视频上传进度
@property(nonatomic,assign)CGFloat uploadProgress;
@property(nonatomic,strong)UIImage *uploadCover;

/// 青少年模式切换,刷新首页
-(void)youngModeRefresh;

@end

