//
//  YBVideoControlView.h
//  YBVideo
//
//  Created by YB007 on 2019/11/7.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <ZFPlayer/ZFPlayer.h>
#import <ZFVolumeBrightnessView.h>

#import <ZFPlayer/UIImageView+ZFCache.h>
#import <ZFPlayer/ZFUtilities.h>
#import "ZFLoadingView.h"
#import <ZFPlayer/ZFSliderView.h>
#import <ZFPlayer/UIView+ZFFrame.h>

typedef void (^YBControlBlock)(NSString *eventStr,ZFPlayerGestureControl *gesControl);

@interface YBVideoControlView : UIView<ZFPlayerMediaControl>

@property(nonatomic,copy)YBControlBlock ybContorEvent;

@property(nonatomic,strong)ZFVolumeBrightnessView *zfVolumeView;
@property(nonatomic,assign)BOOL isDisappear;
@property(nonatomic,assign)CGFloat currentVol;

-(void)videoMuted:(BOOL)mute;
-(void)showPlayBtn;
-(void)hiddenPlayBtn;
-(void)controlSingleTapped;
-(void)changeFullScreen:(BOOL)isFull;

- (void)resetControlView;
- (void)showCoverViewWithUrl:(NSString *)coverUrl withImageMode:(UIViewContentMode)contentMode;


#pragma mark - 礼物打赏
-(void)showGift:(NSDictionary *)playDic;

@end


