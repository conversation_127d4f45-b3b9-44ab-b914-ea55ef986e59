//
//  YBLookVideoCell.m
//  YBVideo
//
//  Created by YB007 on 2019/11/7.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLookVideoCell.h"
#import <ZFPlayer/UIImageView+ZFCache.h>
#import "RKLampView.h"
#import "CCAnimationBtn.h"
#import <YYText/YYLabel.h>
#import <YYText/NSAttributedString+YYText.h>
#import "lookVGoodsDView.h"
#import "topicDetailsVC.h"
#import "PubH5.h"
#import "YBCenterVC.h"
#import "commentview.h"
#import "YBShareView.h"
#import "YBVideoReportVC.h"
#import "addHotVideoVC.h"
#import "YBGiftPage.h"
#import "TCVideoRecordViewController.h"
#import "YBTakeSameVideoVC.h"
#import "YBCheckLiveObj.h"
@interface YBLookVideoCell()
{
     NSTimer *_adLookTimer;
     int _adTimerCount;
    
    lookVGoodsDView *goodsDView;
    commentview *_commentView;
}
@property (nonatomic, strong) UIImageView *coverImageView;
@property (nonatomic, strong) UIImageView *bgImgView;
@property (nonatomic, strong) UIView *effectView;

/** 头像 点赞 评论 分享集合 */
@property(nonatomic,strong)UIView *rightView;
@property(nonatomic,strong)UIView *liveingV;
@property(nonatomic,strong)UILabel *liveingL;
@property(nonatomic,strong) UIButton *iconBtn;
//@property(nonatomic,strong) UIButton *followBtn;                    //关注
@property(nonatomic,strong)NSString *isLike;                        //是否点赞
@property(nonatomic,strong) UIButton *likebtn;                      //点赞

@property(nonatomic,strong) UIButton *commentBtn;
@property(nonatomic,strong) UIButton *enjoyBtn;
@property(nonatomic,strong)UIButton *giftBtn;
@property(nonatomic,strong)UIButton *collectionBtn;
@property(nonatomic,strong)UIImageView *discIV;                     //唱片背景
@property(nonatomic,strong)UIImageView *musicIV;                    //歌曲背景图
@property(nonatomic,strong)UIImageView *symbolAIV;                  //音符A
@property(nonatomic,strong)UIImageView *symbolBIV;                  //音符B

/** 名字 标题 (音乐)集合 */
@property(nonatomic,strong)UIView *botView;
@property(nonatomic,strong)RKLampView *musicL;                     //音乐
@property(nonatomic,strong)YYLabel *titleL;                        //视频标题
@property(nonatomic,strong)UILabel *nameL;
@property(nonatomic,strong)UILabel *adLabel;                       //广告
@property(nonatomic,strong) UIButton *adButton;                    //广告按钮
@property(nonatomic,strong) UIButton *topicBtn;                    //标签按钮

@property(nonatomic,strong)UIButton *ttFullBtn;

@end

@implementation YBLookVideoCell


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _adTimerCount = 5;
        self.backgroundColor = Normal_Color;
        [self.contentView addSubview:self.bgImgView];
        [_bgImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(self.contentView);
        }];
        
        /*
        [self.bgImgView addSubview:self.effectView];
        [_effectView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(_bgImgView);
        }];
         */
        
        [self.contentView addSubview:self.coverImageView];
        [_coverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(self.contentView);
        }];
        /*
        UIImageView *mask_top = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, _window_width, 100+statusbarHeight)];
        [mask_top setImage:[UIImage imageNamed:@"home_mask_top"]];
        [self.contentView addSubview:mask_top];
        
        UIImageView* mask_buttom = [[UIImageView alloc] initWithFrame:CGRectMake(0,  _window_height-49-ShowDiff, _window_width, 49+ShowDiff)];
        [mask_buttom setImage:[UIImage imageNamed:@"home_mask_bottom"]];
        mask_buttom.userInteractionEnabled = YES;
        [self.contentView addSubview:mask_buttom];
        */
        [self.contentView addSubview:self.rightView];
        [_rightView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(85);
            make.right.equalTo(self.contentView);
            make.bottom.equalTo(self.contentView.mas_bottom).offset(-ShowDiff-50);
            make.top.equalTo(_iconBtn.mas_top).offset(-10);
        }];
        
        [self.contentView addSubview:self.botView];
        [_botView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(10);
            make.right.equalTo(_rightView.mas_left).offset(-8);
            make.bottom.equalTo(_rightView.mas_bottom).offset(-25);
//            make.top.equalTo(_adLabel.mas_top).offset(-10);
            make.top.equalTo(_topicBtn.mas_top).offset(-10);

        }];
        [self.contentView addSubview:self.ttFullBtn];

        
    }
    return self;
}
-(void)releaseObj:(NSDictionary *)oldDic isBackGround:(BOOL)isBack{
    if (_adLookTimer) {
        [_adLookTimer invalidate];
        _adLookTimer = nil;
    }
    _adTimerCount = 5;
    NSString *is_ad_str = minstr([oldDic valueForKey:@"is_ad"]);
    NSString *is_userad = minstr([_dataDic valueForKey:@"is_userad"]);//是否是用户广告视频0否1是

    NSLog(@"=====player:%f",_player.currentTime);
    if ([is_ad_str isEqual:@"1"] && !isBack) {
        [_adButton setBackgroundColor:[[UIColor blackColor] colorWithAlphaComponent:0.3]];
        [_adButton setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
        _adButton.userInteractionEnabled = NO;
    }
    if ([is_userad isEqual:@"1"] && !isBack) {
        [_adButton setBackgroundColor:[[UIColor blackColor] colorWithAlphaComponent:0.3]];
        [_adButton setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
        _adButton.userInteractionEnabled = NO;
    }

    if (goodsDView) {
        [goodsDView removeFromSuperview];
        goodsDView = nil;
    }
    [self removeCommentView];
    [_iconBtn.imageView.layer removeAllAnimations];
    [_liveingV.layer removeAllAnimations];
    _liveingV.hidden = _liveingL.hidden = YES;
}
-(void)checkLiveOrAdState:(NSDictionary *)cureentDic{
    NSString *is_ad_str = minstr([cureentDic valueForKey:@"is_ad"]);
    NSString *is_userad = minstr([_dataDic valueForKey:@"is_userad"]);//是否是用户广告视频0否1是

    if ([is_ad_str isEqual:@"1"] || [is_userad isEqual:@"1"]) {
       
        [self releaseObj:nil isBackGround:NO];
        _adLookTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(adLookDaojishi) userInfo:nil repeats:YES];
    }
    
    //直播中动画
    NSDictionary *liveInfo = [cureentDic valueForKey:@"liveinfo"];
    if ([[liveInfo valueForKey:@"islive"] isEqual:@"1"]) {
        _liveingV.hidden = _liveingL.hidden = NO;
        [_iconBtn.imageView.layer addAnimation:[PublicObj avatarScaleAnimation] forKey:@"avatar_scale"];
        [_liveingV.layer addAnimation:[PublicObj bottomScaleAnimation] forKey:@"bottom_scale"];
    }else {
        _liveingV.hidden = _liveingL.hidden = YES;
    }
    
}
-(void)startMusicAnimation:(NSDictionary *)startDic {
    [_musicL startLamp];
    
    _symbolAIV.hidden = NO;
    [_discIV.layer addAnimation:[PublicObj rotationAnimation] forKey:@"rotation"];
    [_symbolAIV.layer addAnimation:[PublicObj caGroup] forKey:nil];
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(startBAnimation) object:nil];
    [self performSelector:@selector(startBAnimation) withObject:nil afterDelay:1.5];
}
-(void)startBAnimation {
    _symbolBIV.hidden = NO;
    [_symbolBIV.layer addAnimation:[PublicObj caGroup] forKey:nil];
}
-(void)stopMusicAnimation:(NSDictionary *)stopDic {
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(startBAnimation) object:nil];
    [_symbolAIV.layer removeAllAnimations];
    [_symbolBIV.layer removeAllAnimations];
    [_discIV.layer removeAllAnimations];
    _symbolAIV.hidden = YES;
    _symbolBIV.hidden = YES;
    [_musicL stopLamp];
    
}
- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    //player-contentMode
    CGFloat coverRatio = 1.78;
    if (![PublicObj checkNull:minstr([_dataDic valueForKey:@"anyway"])]) {
        coverRatio = [minstr([_dataDic valueForKey:@"anyway"]) floatValue];
    }
    if (coverRatio > 1) {
        _coverImageView.contentMode = UIViewContentModeScaleAspectFill;
        self.coverImageView.clipsToBounds = YES;
        _ttFullBtn.hidden = YES;

    }else {
        _coverImageView.contentMode = UIViewContentModeScaleAspectFit;
        self.coverImageView.clipsToBounds = NO;
        _ttFullBtn.hidden = NO;
        CGFloat videoH = coverRatio * _window_width;
        [_ttFullBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
            make.height.mas_equalTo(26);
            make.centerY.equalTo(self.mas_centerY).offset(videoH/2+20);
        }];

    }
    _bgImgView.contentMode = _coverImageView.contentMode;
    _bgImgView.clipsToBounds = _coverImageView.clipsToBounds;
    /*
    [PublicObj getThumbSizeWithUrl:minstr([_dataDic valueForKey:@"thumb"]) complete:^(CGFloat width, CGFloat height) {
        if (width>=height) {
            _coverImageView.contentMode = UIViewContentModeScaleAspectFit;
            self.coverImageView.clipsToBounds = NO;
        }else {
            _coverImageView.contentMode = UIViewContentModeScaleAspectFill;
            self.coverImageView.clipsToBounds = YES;
        }
        _bgImgView.contentMode = _coverImageView.contentMode;
        _bgImgView.clipsToBounds = _coverImageView.clipsToBounds;
    }];
    */
    [_coverImageView sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])] placeholderImage:[UIImage imageNamed:@"loading_bgView"]];
    [_bgImgView sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])] placeholderImage:[UIImage imageNamed:@"loading_bgView"]];
    
    id userInfo = [_dataDic valueForKey:@"userinfo"];
    NSString *uidStr = @"";
    NSString *iconStr = @"";
    NSString *uNameStr = @"";
    if ([userInfo isKindOfClass:[NSDictionary class]]) {
        uidStr = minstr([userInfo valueForKey:@"id"]);
        iconStr = minstr([userInfo valueForKey:@"avatar"]);
        uNameStr = [NSString stringWithFormat:@"@%@",[userInfo valueForKey:@"user_nickname"]];
    }
    //音乐
    id musicInfo = [_dataDic valueForKey:@"musicinfo"];
    if ([musicInfo isKindOfClass:[NSDictionary class]]) {
        NSString *musicID = minstr([musicInfo valueForKey:@"id"]);
        NSString *musicCover = minstr([musicInfo valueForKey:@"img_url"]);
        if ([musicID isEqual:@"0"]) {
            [_musicIV sd_setImageWithURL:[NSURL URLWithString:iconStr]];
        }else{
            [_musicIV sd_setImageWithURL:[NSURL URLWithString:musicCover]];
        }
        _musicL.contentStr = minstr([musicInfo valueForKey:@"music_format"]);
        //[_musicL startLamp];
    }
    
    //右边赋值
    [_enjoyBtn setTitle:minstr([_dataDic valueForKey:@"shares"]) forState:0];
    _enjoyBtn = [PublicObj setUpImgDownText:_enjoyBtn];
    [_commentBtn setTitle:minstr([_dataDic valueForKey:@"comments"]) forState:0];
    _commentBtn = [PublicObj setUpImgDownText:_commentBtn];
    [_likebtn setTitle:minstr([_dataDic valueForKey:@"likes"]) forState:0];
    _likebtn = [PublicObj setUpImgDownText:_likebtn];
    NSString *isLike = minstr([_dataDic valueForKey:@"islike"]);
    if ([isLike isEqual:@"1"]) {
        [_likebtn setImage:[UIImage imageNamed:@"home_zan_sel"] forState:0];
    }else {
        [_likebtn setImage:[UIImage imageNamed:@"home_zan"] forState:0];
    }
    
    [_collectionBtn setTitle:minstr([_dataDic valueForKey:@"collections"]) forState:0];
    _collectionBtn = [PublicObj setUpImgDownText:_collectionBtn];
    int iscollection = [minstr([_dataDic valueForKey:@"iscollection"]) intValue];
    _collectionBtn.selected = iscollection;
    int youngSwitch = [minstr([_dataDic valueForKey:@"teenagers_switch"]) intValue];
    [self judgeYoungSwitch:youngSwitch];
    
    [_iconBtn sd_setImageWithURL:[NSURL URLWithString:iconStr] forState:0 placeholderImage:[PublicObj getAppIcon]];
    NSString *isAttent = minstr([_dataDic valueForKey:@"isattent"]);
    if ([[Config getOwnID] isEqual:uidStr] || [isAttent isEqual:@"1"]) {
        _followBtn.hidden = YES;
    }else{
        [_followBtn setImage:[UIImage imageNamed:@"home_follow"] forState:0];
        _followBtn.hidden = NO;
        [_followBtn.layer addAnimation:[PublicObj followShowTransition] forKey:nil];
    }
    
    //底部赋值
    NSString *is_ad_str = minstr([_dataDic valueForKey:@"is_ad"]);
    NSString *ad_url_str = minstr([_dataDic valueForKey:@"ad_url"]);
    NSString *is_goods_str = minstr([dataDic valueForKey:@"isgoods"]);
    // rk_warning
    // 0-站内 1-站外 2-平台自营
    int goods_type = [minstr([dataDic valueForKey:@"goods_type"]) intValue];
    if ([PublicObj isUp] && goods_type == 1) {
        // 上架期间站外商品不展示
        is_goods_str = @"0";
    }
    
    NSString *is_userad = minstr([_dataDic valueForKey:@"is_userad"]);//是否是用户广告视频0否1是
    NSString *userad_url = minstr([_dataDic valueForKey:@"userad_url"]);
    
    if ([is_ad_str isEqual:@"1"] || [is_userad isEqual:@"1"]) {
        _topicBtn.hidden = YES;
        [self topicBtnLayoutOfHeight:0];
        _adLabel.hidden = NO;
        [self adLabelLayoutOfHeight:20];
        _adButton.hidden = NO;
        [self adButtonLayoutOfHeight:30];
        [_adButton setBackgroundColor:[[UIColor blackColor] colorWithAlphaComponent:0.3]];
        [_adButton setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
        _adButton.userInteractionEnabled = NO;
        [_adButton setTitle:YZMsg(@"查看详情") forState:0];
    }else {
        _adLabel.hidden = YES;
        [self adLabelLayoutOfHeight:0];
        if ([is_goods_str isEqual:@"1"]) {
            _adButton.hidden = NO;
            [self adButtonLayoutOfHeight:30];
            [_adButton setBackgroundColor:RGB_COLOR(@"#EA377F", 1)];
            [_adButton setTitleColor:[UIColor whiteColor] forState:0];
            _adButton.userInteractionEnabled = YES;
            [_adButton setTitle:YZMsg(@"查看同款商品") forState:0];
            
        }else{
            _adButton.hidden = YES;
            [self adButtonLayoutOfHeight:0];
        }
    }
    if (![minstr([_dataDic valueForKey:@"labelid"]) isEqual:@"0"]) {
        NSString *stttt = [NSString stringWithFormat: @" %@",minstr([_dataDic valueForKey:@"label_name"])];
       [_topicBtn setTitle:stttt forState:0];
        _topicBtn.hidden = NO;
        [self topicBtnLayoutOfHeight:20];
    }else{
        _topicBtn.hidden = YES;
        [self topicBtnLayoutOfHeight:0];
    }

    //标题
    _titleL.preferredMaxLayoutWidth = _window_width-85;//_botView.width-10;
    _titleL.text = minstr([_dataDic valueForKey:@"title"]);
    NSString *att_text = [NSString stringWithFormat:@"%@ ",_titleL.text];
    NSMutableAttributedString *title_att = [[NSMutableAttributedString alloc]initWithString:att_text];
    title_att.yy_font = SYS_Font(15);
    title_att.yy_color = [UIColor whiteColor];
    title_att.yy_lineBreakMode = NSLineBreakByTruncatingHead;
    title_att.yy_kern = [NSNumber numberWithFloat:0.2];
    if (![PublicObj checkNull:ad_url_str]&&[is_ad_str isEqual:@"1"]) {
        /*
        NSString *att_text = [NSString stringWithFormat:@"%@ ",_titleL.text];
        NSMutableAttributedString *title_att = [[NSMutableAttributedString alloc]initWithString:att_text];
        */
        UIImage *ad_link_img = [UIImage imageNamed:getImagename(@"广告-详情")];
        NSMutableAttributedString *att_img = [NSMutableAttributedString yy_attachmentStringWithContent:ad_link_img contentMode:UIViewContentModeCenter attachmentSize:CGSizeMake(35, 13) alignToFont:SYS_Font(15) alignment:YYTextVerticalAlignmentCenter];
        //NSLog(@"-==-:%@==:%@==img:%@",att_text,title_att,att_img);
        [title_att appendAttributedString:att_img];
        NSRange click_range = [[title_att string] rangeOfString:[att_img string]];
        /*
        title_att.yy_font = SYS_Font(15);
        title_att.yy_color = [UIColor whiteColor];
        title_att.yy_lineBreakMode = NSLineBreakByTruncatingHead;
        title_att.yy_kern = [NSNumber numberWithFloat:0.2];
        */
        [title_att addAttribute:NSBackgroundColorAttributeName value:[UIColor clearColor] range:click_range];
        [title_att yy_setTextHighlightRange:click_range color:[UIColor clearColor] backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
            [self clickAdButton];
        }];
    }
    if (![PublicObj checkNull:userad_url]&&[is_userad isEqual:@"1"]) {
        UIImage *ad_link_img = [UIImage imageNamed:getImagename(@"广告-详情")];
        NSMutableAttributedString *att_img = [NSMutableAttributedString yy_attachmentStringWithContent:ad_link_img contentMode:UIViewContentModeCenter attachmentSize:CGSizeMake(35, 13) alignToFont:SYS_Font(15) alignment:YYTextVerticalAlignmentCenter];
        [title_att appendAttributedString:att_img];
        NSRange click_range = [[title_att string] rangeOfString:[att_img string]];
        [title_att addAttribute:NSBackgroundColorAttributeName value:[UIColor clearColor] range:click_range];
        [title_att yy_setTextHighlightRange:click_range color:[UIColor clearColor] backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
            [self clickAdButton];
        }];
    }

    _titleL.attributedText = title_att;
    _nameL.text = uNameStr;
    
}
-(void)judgeYoungSwitch:(int)youngSwitch {
    _giftBtn.hidden = youngSwitch;
    _collectionBtn.hidden = !youngSwitch;
}
- (void)adLookDaojishi{
    _adTimerCount -- ;
    if (_adTimerCount <= 0) {
        [_adButton setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        _adButton.userInteractionEnabled = YES;
        [_adButton setBackgroundColor:RGB_COLOR(@"#EA377F", 1)];
        [_adLookTimer invalidate];
        _adLookTimer = nil;
        _adTimerCount = 5;
    }
}
#pragma mark === click

#pragma mark - 拍同款
-(void)clickMusicTap {
    NSLog(@"-----");
//    if ([[Config getOwnID] intValue] <= 0) {
//        [PublicObj warnLogin];
//        return;
//    }
    id musicInfo = [_dataDic valueForKey:@"musicinfo"];
    NSString *musicID = [NSString stringWithFormat:@"%@",[musicInfo valueForKey:@"id"]];
    if ([musicID isEqual:@"0"] || ![musicInfo isKindOfClass:[NSDictionary class]]) {
        [MBProgressHUD showPop:YZMsg(@"该视频不支持拍摄同款")];
        return;
    }
    if ([YBYoungManager shareInstance].youngSwitch == 1) {
        [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
        return;
    }
    YBTakeSameVideoVC *sameVC = [[YBTakeSameVideoVC alloc]init];
    sameVC.musicDic = musicInfo;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:sameVC animated:YES];
    
}

#pragma mark - 送礼
-(void)clickGiftBtn {
    NSLog(@"gift:%@",_dataDic);
    if ([[Config getOwnID] intValue] <= 0) {
        [PublicObj warnLogin];
        return;
    }
    //礼物打赏
    YBWeakSelf;
    YBGiftPage *gView = [YBGiftPage showGiftViewComplete:^(int eventCode,NSDictionary *giftDic) {
        if (eventCode == -1) {
            //关闭
        }else {
            //发送
            if (weakSelf.videoCellEvent) {
                weakSelf.videoCellEvent(@"视频-打赏", giftDic);
            }
        }
    }];
    gView.receiveID =  minstr([_dataDic valueForKey:@"id"]);;
    gView.sendType = SendGiftType_Video;
    [gView requestGiftData];
    
}
#pragma mark - 分享
-(void)clickEnjoyBtn {
    //NSLog(@"enjoy:%@",_dataDic);

    YBWeakSelf;
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Video.checkPayVideoIsShare" Dic:@{@"videoid":minstr([_dataDic valueForKey:@"id"])} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            if ([info isKindOfClass:[NSArray class]]) {
                [weakSelf shareLimit:[info firstObject]];
            }else {
                [weakSelf shareLimit:info];
            }
        }else {
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
-(void)shareLimit:(NSDictionary *)infoDic {
    YBWeakSelf;
    YBShareView *shareV = [YBShareView showShareWithType:RKShareType_LookVdieo parameter:_dataDic commplete:^(int codeEvent, NSString *nums) {
        if (codeEvent == 0) {
            //分享成功
            [_enjoyBtn setTitle:minstr(nums) forState:0];
            _enjoyBtn = [PublicObj setUpImgDownText:_enjoyBtn];
        }else if (codeEvent == 2){
            //举报
            if ([[Config getOwnID] intValue] <= 0) {
                [PublicObj warnLogin];
                return;
            }
            YBVideoReportVC *jubao = [[YBVideoReportVC alloc]init];
            jubao.dongtaiId =  minstr([_dataDic valueForKey:@"id"]);;
            jubao.fromWhere = _fromWhere ? _fromWhere:@"LookVideo";
            [[XGGAppDelegate sharedAppDelegate]pushViewController:jubao animated:YES];
        }else if (codeEvent == 3){
            //删除
            if (weakSelf.videoCellEvent) {
                weakSelf.videoCellEvent(@"视频-删除", @{});
            }
        }else if (codeEvent == 4){
            //合拍
            if ([[Config getOwnID] intValue] <= 0) {
                [PublicObj warnLogin];
                return;
            }
            if (self.player.totalTime < 5) {
                [MBProgressHUD showPop:YZMsg(@"该视频时长太短,无法合拍")];
                [[NSFileManager defaultManager] removeItemAtPath:nums error:nil];
                return;
            }
            if ([YBYoungManager shareInstance].youngSwitch == 1) {
                [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
                return;
            }
            TCVideoRecordViewController *recorddVC = [[TCVideoRecordViewController alloc]init];
            recorddVC.mp4Path = nums;
            recorddVC.recordType = RecordType_Chorus;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:recorddVC animated:YES];
        }else if (codeEvent == 5){
            //上热门
            if ([[Config getOwnID] intValue] <= 0) {
                [PublicObj warnLogin];
                return;
            }
            if ([YBYoungManager shareInstance].youngSwitch == 1) {
                [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
                return;
            }
            addHotVideoVC *hot = [[addHotVideoVC alloc]init];
            hot.videoInfo = _dataDic;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:hot animated:YES];
        }else if (codeEvent == 7){
            // 收藏
            if ([[Config getOwnID] intValue] <= 0) {
                [PublicObj warnLogin];
                return;
            }
            [weakSelf clickVideoCollectionAni:NO];
        }
        
    }];
    shareV.limitDic = infoDic;
    
}
#pragma mark - 收藏
-(void)clickCollectionBtn {
    [self clickVideoCollectionAni:YES];
}
-(void)clickVideoCollectionAni:(BOOL)animation {
    NSString *videoID = minstr([_dataDic valueForKey:@"id"]);
    YBWeakSelf;
    [YBNetworking postWithUrl:@"video.addCollection" Dic:@{@"videoid":videoID} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *iscollection = minstr([infoDic valueForKey:@"iscollection"]);
            NSString *collections  = minstr([infoDic valueForKey:@"collections"]);
            NSDictionary *newDic = @{@"iscollection":iscollection,@"collections":collections};
            [weakSelf updateDataDic:newDic];
            if (weakSelf.videoCellEvent) {
                weakSelf.videoCellEvent(@"视频-收藏", newDic);
            }
            [_collectionBtn setTitle:collections forState:0];
            _collectionBtn = [PublicObj setUpImgDownText:_collectionBtn];
            _collectionBtn.selected = [iscollection intValue];
            if (animation) {
                [weakSelf colAnimationWithIsCol:iscollection];
            }else{
                [MBProgressHUD showError:msg];
            }
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
//收藏-取消收藏动画
-(void)colAnimationWithIsCol:(NSString *)isCol {
    NSMutableArray *m_sel_arr = [NSMutableArray array];
    NSString *endImgName = @"";
    UIControlState btnState;
    if ([isCol isEqual:@"1"]) {
        for (int i=1; i<=12; i++) {
            UIImage *img = [UIImage imageNamed:[NSString stringWithFormat:@"icon_video_col_%02d",i]];
            [m_sel_arr addObject:img];
        }
        btnState = UIControlStateSelected;
        endImgName = @"icon_video_col_12";
    }else{
        for (int i=1; i<=6; i++) {
            UIImage *img = [UIImage imageNamed:[NSString stringWithFormat:@"icon_video_col_cancel_%02d",i]];
            [m_sel_arr addObject:img];
        }
        btnState = UIControlStateNormal;
        endImgName = @"icon_video_col_01";
    }
    CGFloat showTime = 0.05*m_sel_arr.count;
    _collectionBtn.imageView.animationImages = m_sel_arr;
    _collectionBtn.imageView.animationDuration = showTime;
    _collectionBtn.imageView.animationRepeatCount = 1;
    [_collectionBtn.imageView startAnimating];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)((showTime-0.06) * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [_collectionBtn.imageView stopAnimating];
        [_collectionBtn setImage:[UIImage imageNamed:endImgName] forState:btnState];
    });
}

#pragma mark - 评论
-(void)clickCommentBtn {
    NSLog(@"comment:%@",_dataDic);
    [self removeCommentView];
    NSDictionary *userDic = [_dataDic valueForKey:@"userinfo"];
    NSString *videoUserID = minstr([userDic valueForKey:@"id"]);
    NSString *videoID = minstr([_dataDic valueForKey:@"id"]);
    NSString *commentStr = minstr([_dataDic valueForKey:@"comments"]);
    
    YBWeakSelf;
    _commentView = [[commentview alloc]initWithFrame:CGRectMake(0,_window_height, _window_width, _window_height) hide:^(NSString *type) {
        [UIView animateWithDuration:0.25 animations:^{
            _commentView.frame = CGRectMake(0,_window_height, _window_width, _window_height);
        } completion:^(BOOL finished) {
            [weakSelf removeCommentView];
        }];
    } andvideoid:videoID andhostid:videoUserID count:[commentStr intValue] talkCount:^(id type) {
        [weakSelf updateCommentNums:type];
    } detail:^(id type) {
        
    } youke:^(id type) {
        [weakSelf comNeedLogin];
    } andFrom:_fromWhere];
    ZFPlayerPlaybackState playState = self.player.currentPlayerManager.playState;
    _commentView.mutedBlock = ^(id type) {
        if (weakSelf.videoCellEvent) {
            weakSelf.videoCellEvent(@"视频-静音", @{@"mutedType":minstr(type),@"playState":@(playState)});
        }
    };
    _commentView.fromWhere = _fromWhere;
    [_commentView getNewCount:[commentStr intValue]];
    
    [[UIApplication sharedApplication].delegate.window addSubview:_commentView];
    
    [UIView animateWithDuration:0.25 animations:^{
        _commentView.frame = CGRectMake(0,0,_window_width, _window_height);
    }];
    __weak commentview *weakComment = _commentView;
    _commentView.goCenterEvent = ^(BOOL isGo) {
        weakComment.hidden = isGo;
    };
}
-(void)removeCommentView {
    if (_commentView) {
        [_commentView removeFromSuperview];
        _commentView = nil;
    }
}
-(void)comNeedLogin {
    [self removeCommentView];
    [PublicObj warnLogin];
}
-(void)updateCommentNums:(NSString *)nums {
    NSDictionary *newDic = @{@"comments":nums};
    [self updateDataDic:newDic];
    if (self.videoCellEvent) {
        self.videoCellEvent(@"视频-评论", newDic);
    }
    [_commentBtn setTitle:minstr(nums) forState:0];
    _commentBtn = [PublicObj setUpImgDownText:_commentBtn];
}

#pragma mark - 点赞
-(void)clickLikeBtn {
    NSLog(@"like:%@",_dataDic);
    if ([[Config getOwnID] intValue] <= 0) {
        [PublicObj warnLogin];
        return;
    }
    //[MBProgressHUD showMessage:@""];
    _likebtn.userInteractionEnabled = NO;
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Video.addLike" Dic:@{@"videoid":minstr([_dataDic valueForKey:@"id"])} Suc:^(int code, id info, NSString *msg) {
        //[MBProgressHUD hideHUD];
        _likebtn.userInteractionEnabled = YES;
        if(code == 0) {
            
            NSDictionary *infoDic = [info firstObject];
            NSString *isLike = minstr([infoDic valueForKey:@"islike"]);
            NSString *likes  = minstr([infoDic valueForKey:@"likes"]);
            NSDictionary *newDic = @{@"islike":isLike,@"likes":likes};
            [weakSelf updateDataDic:newDic];
            
            if (weakSelf.videoCellEvent) {
                weakSelf.videoCellEvent(@"视频-点赞", newDic);
            }
            [_likebtn setTitle:likes forState:0];
            _likebtn = [PublicObj setUpImgDownText:_likebtn];
            [weakSelf likeAnimationShowIsLike:isLike];
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        //[MBProgressHUD hideHUD];
        _likebtn.userInteractionEnabled = YES;
    }];
}
//点赞-取消赞动画
-(void)likeAnimationShowIsLike:(NSString *)isLike {
    
    NSMutableArray *m_sel_arr = [NSMutableArray array];
    NSString *endImgName = @"";
    if ([isLike isEqual:@"1"]) {
        for (int i=1; i<=12; i++) {
            UIImage *img = [UIImage imageNamed:[NSString stringWithFormat:@"icon_video_zan_%02d",i]];
            [m_sel_arr addObject:img];
        }
        endImgName = @"icon_video_zan_12";
    }else{
        for (int i=1; i<=6; i++) {
            UIImage *img = [UIImage imageNamed:[NSString stringWithFormat:@"icon_video_zan_cancel_%02d",i]];
            [m_sel_arr addObject:img];
        }
        endImgName = @"icon_video_zan_01";
    }
    CGFloat showTime = 0.05*m_sel_arr.count;
    _likebtn.imageView.animationImages = m_sel_arr;
    _likebtn.imageView.animationDuration = showTime;
    _likebtn.imageView.animationRepeatCount = 1;
    [_likebtn.imageView startAnimating];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)((showTime-0.06) * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [_likebtn.imageView stopAnimating];
        [_likebtn setImage:[UIImage imageNamed:endImgName] forState:0];
    });
    
}

#pragma mark - 进入主页
-(void)swipGesGoHome;{
    //侧滑无论是否直播都进个人主页【11月】
    [self goHomeOrRoom:NO];
}
-(void)clickIconBtn {
    NSLog(@"icon:%@",_dataDic);
//    if ([[Config getOwnID] intValue]<=0) {
//        [PublicObj warnLogin];
//        return;
//    }
    //点头像区分是否在直播中【11月】
    [self goHomeOrRoom:YES];
}
-(void)goHomeOrRoom:(BOOL)judgeLiving {
    NSDictionary *userDic = [_dataDic valueForKey:@"userinfo"];
    NSString *videoUserID = minstr([userDic valueForKey:@"id"]);
    
    NSDictionary *liveInfo = [_dataDic valueForKey:@"liveinfo"];
    if ([[liveInfo valueForKey:@"islive"] isEqual:@"1"] && judgeLiving) {
        [YBCheckLiveObj checkLiveManeger].liveUid = minstr([liveInfo valueForKey:@"uid"]);
        [YBCheckLiveObj checkLiveManeger].liveStream = minstr([liveInfo valueForKey:@"stream"]);
        [YBCheckLiveObj checkLiveManeger].currentIndex = 0;
        [YBCheckLiveObj checkLiveManeger].listArray = nil;
        [[YBCheckLiveObj checkLiveManeger] checkLiving];
        return;
    }
    
    YBWeakSelf;
    YBCenterVC *center = [[YBCenterVC alloc]init];
    center.otherUid =videoUserID;
    center.isPush = YES;
    center.followEvent = ^(NSString *isAttent) {
        NSDictionary *newDic = @{@"isattent":isAttent};
        [weakSelf updateDataDic:newDic];
        if ([isAttent isEqual:@"1"]) {
            _followBtn.hidden = YES;
        }else{
            [_followBtn setImage:[UIImage imageNamed:@"home_follow"] forState:0];
            _followBtn.hidden = NO;
        }
    };
    [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];
}

#pragma mark - 关注
-(void)clickFollowBtn {
    NSLog(@"follow:%@",_dataDic);
    if ([[Config getOwnID] intValue]<=0) {
        [PublicObj warnLogin];
        return;
    }
    [MBProgressHUD showMessage:@""];
    NSDictionary *userDic = [_dataDic valueForKey:@"userinfo"];
    NSString *videoUserID = minstr([userDic valueForKey:@"id"]);
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.setAttent" Dic:@{@"touid":videoUserID} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if(code == 0) {
            NSDictionary *newDic = @{@"isattent":@"1"};
            [weakSelf updateDataDic:newDic];
            
            if (weakSelf.videoCellEvent) {
                weakSelf.videoCellEvent(@"视频-关注", newDic);
            }
            [_followBtn setImage:[UIImage imageNamed:@"home_follow_sel"] forState:0];
            dispatch_async(dispatch_get_main_queue(), ^{
                [_followBtn.imageView.layer addAnimation:[PublicObj smallToBigToSmall] forKey:nil];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.9 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    _followBtn.hidden = YES;
                });
            });
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
#pragma mark - 广告
-(void)clickAdButton {
   if ([minstr([_dataDic valueForKey:@"isgoods"]) isEqual:@"1"]) {
       if ([YBYoungManager shareInstance].youngSwitch == 1) {
           [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
           return;
       }
       [self showGoodsDeatile];
   }else{
       NSString *is_ad_str = minstr([_dataDic valueForKey:@"is_ad"]);
       NSString *ad_url_str = minstr([_dataDic valueForKey:@"ad_url"]);
       if ([is_ad_str isEqual:@"1"]) {
           if ([PublicObj checkNull:ad_url_str]) {
               [MBProgressHUD showError:YZMsg(@"视频即详情")];
               return;
           }
           [[UIApplication sharedApplication]openURL:[NSURL URLWithString:ad_url_str] options:@{} completionHandler:^(BOOL success) {
               
           }];
       }
       NSString *is_userad = minstr([_dataDic valueForKey:@"is_userad"]);//是否是用户广告视频0否1是
       NSString *userad_url = minstr([_dataDic valueForKey:@"userad_url"]);

       if ([is_userad isEqual:@"1"]) {
           if ([PublicObj checkNull:userad_url]) {
               [MBProgressHUD showError:YZMsg(@"视频即详情")];
               return;
           }
           [[UIApplication sharedApplication]openURL:[NSURL URLWithString:userad_url] options:@{} completionHandler:^(BOOL success) {
               
           }];

       }
   }
    
}
#pragma mark - 商品
- (void)showGoodsDeatile{
    if (goodsDView) {
        [goodsDView removeFromSuperview];
        goodsDView = nil;
    }
    NSDictionary *dic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"goodsid":minstr([_dataDic valueForKey:@"goodsid"])};
    [YBNetworking postWithUrl:@"Shop.getVideoBindGoodsInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infos = info;
            NSDictionary *userDic = [_dataDic valueForKey:@"userinfo"];
            NSString *videoUserID = minstr([userDic valueForKey:@"id"]);

            goodsDView = [[lookVGoodsDView alloc]initWithGoodsMsg:infos];//[_dataDic valueForKey:@"goodsinfo"]];
            goodsDView.videoid = minstr([_dataDic valueForKey:@"id"]);
            goodsDView.goodsID = minstr([infos valueForKey:@"id"]);
            goodsDView.videoUid = videoUserID;
            [[UIApplication sharedApplication].delegate.window addSubview:goodsDView];

        }
    } Fail:^(id fail) {
        
    }];
    
}

#pragma mark - 标签
-(void)clickTopicBtn {
    topicDetailsVC *topic = [[topicDetailsVC alloc]init];
    topic.labelID = minstr([_dataDic valueForKey:@"labelid"]);
    [[XGGAppDelegate sharedAppDelegate]pushViewController:topic animated:YES];
}

#pragma mark - 部分控件的显示隐藏
//20
-(void)topicBtnLayoutOfHeight:(CGFloat)height {
    NSString *is_ad_str = minstr([_dataDic valueForKey:@"is_ad"]);
    NSString *is_userad = minstr([_dataDic valueForKey:@"is_userad"]);//是否是用户广告视频0否1是

    if ([is_ad_str isEqual:@"1"] || [is_userad isEqual:@"1"]) {
        [_topicBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_botView.mas_left).offset(3);
            make.bottom.equalTo(_adLabel.mas_top).offset(-2);
            make.height.mas_equalTo(height);
        }];

    }else{
        [_topicBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_botView.mas_left).offset(3);
            make.bottom.equalTo(_nameL.mas_top).offset(-2);
            make.height.mas_equalTo(height);
        }];

    }
    BOOL isBoldText = [PublicObj isBlodText];
    if (isBoldText) {
        [_botView layoutIfNeeded];
        CGFloat www = _topicBtn.width;
        [_topicBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(www*1.1);
        }];
    }
}
//20
-(void)adLabelLayoutOfHeight:(CGFloat)height {
    [_adLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(45);
        make.height.mas_equalTo(height);
        make.left.equalTo(_botView.mas_left).offset(3);
//        make.bottom.equalTo(_topicBtn.mas_top).offset(-2);
        make.bottom.equalTo(_nameL.mas_top).offset(-2);
    }];
}
//30
-(void)adButtonLayoutOfHeight:(CGFloat)height {
    [_adButton mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_botView.mas_left).offset(5);
        make.height.mas_equalTo(height);
        make.width.equalTo(_botView.mas_width).multipliedBy(0.7);
        make.bottom.equalTo(_botView.mas_bottom).offset(-10);
    }];
}
#pragma mark - set/get

- (UIView *)rightView{
    if (!_rightView) {

        _rightView = [[UIView alloc]init];
        _rightView.backgroundColor = [UIColor clearColor];
        
        //音乐背景+歌曲(作者头像)-->整个动画背景
        UIView *animationBgView = [[UIView alloc]init];
        animationBgView.backgroundColor = [UIColor clearColor];
        [_rightView addSubview:animationBgView];
        [animationBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@75);
            make.height.equalTo(@50);
            make.left.equalTo(_rightView);
            make.bottom.equalTo(_rightView).offset(-10);
        }];
        
        //唱片图片
        _discIV = [[UIImageView alloc]init];
        [_discIV setImage:[UIImage imageNamed:@"music_disc"]];
        [animationBgView addSubview:_discIV];
        [_discIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@25);
            make.top.equalTo(animationBgView);
            make.width.height.equalTo(@50);
        }];
        //音乐图片(或者作者头像)
        _musicIV = [[UIImageView alloc]init];
        CGFloat l_s = (50-20*50/33)/2;
        _musicIV.layer.masksToBounds = YES;
        _musicIV.layer.cornerRadius = 20*50/33/2;
        [_discIV addSubview:_musicIV];
        [_musicIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@(l_s));
            make.top.equalTo(@(l_s));
            make.width.height.equalTo(@(20*50/33));
        }];
        //音符A+B
        _symbolAIV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"music_symbolA"]];
        _symbolAIV.frame = CGRectMake(0, _discIV.top, 12, 12);
        [animationBgView addSubview:_symbolAIV];
        _symbolAIV.hidden = YES;
        _symbolBIV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"music_symbolB"]];
        _symbolBIV.frame = CGRectMake(0, _discIV.top, 12, 12);
        _symbolBIV.hidden = YES;
        [animationBgView addSubview:_symbolBIV];
        
        _discIV.userInteractionEnabled = YES;
        _musicIV.userInteractionEnabled = YES;
        UITapGestureRecognizer *discTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(clickMusicTap)];
        [_discIV addGestureRecognizer:discTap];
        
        UITapGestureRecognizer *musicTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(clickMusicTap)];
        [_musicIV addGestureRecognizer:musicTap];
        
        //送礼
        _giftBtn = [UIButton buttonWithType:0];
        _giftBtn.backgroundColor = [UIColor clearColor];
        [_giftBtn setImage:[UIImage imageNamed:@"首页-礼物"] forState:0];
        [_giftBtn addTarget:self action:@selector(clickGiftBtn) forControlEvents:UIControlEventTouchUpInside];
        _giftBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _giftBtn = [PublicObj setUpImgDownText:_giftBtn];
        [_rightView addSubview:_giftBtn];
        [_giftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_discIV);
            make.bottom.equalTo(animationBgView.mas_top).offset(-15);
            make.width.equalTo(@80);
            make.height.equalTo(@60);
        }];
        
        // 收藏
        _collectionBtn = [UIButton buttonWithType:0];
        _collectionBtn.backgroundColor = [UIColor clearColor];
        [_collectionBtn setImage:[UIImage imageNamed:@"home_col"] forState:0];
        [_collectionBtn setImage:[UIImage imageNamed:@"home_col_sel"] forState:UIControlStateSelected];
        [_collectionBtn addTarget:self action:@selector(clickCollectionBtn) forControlEvents:UIControlEventTouchUpInside];
        _collectionBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _collectionBtn = [PublicObj setUpImgDownText:_collectionBtn];
        [_rightView addSubview:_collectionBtn];
        [_collectionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.centerY.width.height.equalTo(_giftBtn);
        }];
        
        //分享
        _enjoyBtn = [UIButton buttonWithType:0];
        _enjoyBtn.backgroundColor = [UIColor clearColor];
        [_enjoyBtn setImage:[UIImage imageNamed:@"home_share"] forState:0];
        [_enjoyBtn addTarget:self action:@selector(clickEnjoyBtn) forControlEvents:UIControlEventTouchUpInside];
        _enjoyBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _enjoyBtn = [PublicObj setUpImgDownText:_enjoyBtn];
        [_rightView addSubview:_enjoyBtn];
        [_enjoyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_discIV);
            make.bottom.equalTo(_giftBtn.mas_top).offset(-3);
            make.width.equalTo(@80);
            make.height.equalTo(@60);
        }];
        
        //评论列表
        _commentBtn = [UIButton buttonWithType:0];
        _commentBtn.backgroundColor = _enjoyBtn.backgroundColor;
        [_commentBtn setImage:[UIImage imageNamed:@"home_comment"] forState:0];
        [_commentBtn addTarget:self action:@selector(clickCommentBtn) forControlEvents:UIControlEventTouchUpInside];
        _commentBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _commentBtn = [PublicObj setUpImgDownText:_commentBtn];
         [_rightView addSubview:_commentBtn];
        [_commentBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_enjoyBtn);
            make.width.height.equalTo(_enjoyBtn);
            make.bottom.equalTo(_enjoyBtn.mas_top).offset(-8);
        }];
        
        //点赞
        _likebtn = [UIButton buttonWithType:0];
        _likebtn.backgroundColor = _enjoyBtn.backgroundColor;
        [_likebtn addTarget:self action:@selector(clickLikeBtn) forControlEvents:UIControlEventTouchUpInside];
        [_likebtn setImage:[UIImage imageNamed:@"home_zan"] forState:0];
        _likebtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _likebtn = [PublicObj setUpImgDownText:_likebtn];
         [_rightView addSubview:_likebtn];
        [_likebtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_enjoyBtn);
            make.width.height.equalTo(_enjoyBtn);
            make.bottom.equalTo(_commentBtn.mas_top).offset(-8);
        }];
        
        //直播中动画外边缘
        _liveingV = [[UIView alloc]init];
        _liveingV.layer.borderColor = RGB_COLOR(@"#EA377F", 0.5).CGColor;
        _liveingV.layer.borderWidth = 0.8;
        _liveingV.cornerRadius = 25;
        [_rightView addSubview:_liveingV];
        
        //主播头像
        _iconBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _iconBtn.layer.masksToBounds = YES;
        _iconBtn.layer.cornerRadius = 25;
        _iconBtn.layer.borderWidth = 1.5;
        _iconBtn.layer.borderColor = Pink_Cor.CGColor;
        _iconBtn.imageView.contentMode = UIViewContentModeScaleAspectFill;
        _iconBtn.imageView.clipsToBounds = YES;
        _iconBtn.imageView.layer.masksToBounds = YES;
        _iconBtn.imageView.layer.cornerRadius = 25;
        [_iconBtn addTarget:self action:@selector(clickIconBtn) forControlEvents:UIControlEventTouchUpInside];
        [_iconBtn sd_setImageWithURL:[NSURL URLWithString:@""] forState:0 placeholderImage:[PublicObj getAppIcon]];
        [_rightView addSubview:_iconBtn];
        [_iconBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_enjoyBtn);
            make.width.height.equalTo(@50);
            make.bottom.equalTo(_likebtn.mas_top).offset(-20);
        }];
        
        _liveingV.userInteractionEnabled = NO;
        [_liveingV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(_iconBtn);
        }];
        
        _liveingL = [[UILabel alloc]init];
        _liveingL.text = YZMsg(@"直播");
        _liveingL.textAlignment = NSTextAlignmentCenter;
        _liveingL.textColor = UIColor.whiteColor;
        _liveingL.backgroundColor = Pink_Cor;
        _liveingL.font = SYS_Font(10);
        _liveingL.layer.cornerRadius = 16;
        _liveingL.layer.masksToBounds = YES;
        _liveingL.userInteractionEnabled = YES;
        [_iconBtn.imageView addSubview:_liveingL];
        [_liveingL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.top.equalTo(_iconBtn.imageView);
            make.width.equalTo(_iconBtn.imageView.mas_width).multipliedBy(0.85);
            make.height.mas_equalTo(16);
        }];
        _liveingL.hidden = _liveingV.hidden = YES;;
        
        //关注按钮
        _followBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_followBtn setImage:[UIImage imageNamed:@"home_follow"] forState:0];
        [_followBtn addTarget:self action:@selector(clickFollowBtn) forControlEvents:UIControlEventTouchUpInside];
        [_rightView addSubview:_followBtn];
        [_followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_iconBtn);
            make.width.height.equalTo(@26);
            make.centerY.equalTo(_iconBtn.mas_bottom).offset(0);
        }];
        
    }
    return _rightView;
}

- (UIView *)botView {
    if (!_botView) {

        _botView = [[UIView alloc]init];
        _botView.backgroundColor = [UIColor clearColor];
        
        //布局顺序从下向上（视频标题最多三行，可没有）
        _adButton = [UIButton buttonWithType:0];
        _adButton.titleLabel.font = [UIFont systemFontOfSize:10];
        _adButton.layer.cornerRadius = 3;
        _adButton.layer.masksToBounds = YES;
        [_adButton addTarget:self action:@selector(clickAdButton) forControlEvents:UIControlEventTouchUpInside];
        [_botView addSubview:_adButton];
        _adButton.hidden = YES;
        [_adButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_botView.mas_left).offset(5);
            make.height.mas_equalTo(30);
            make.width.equalTo(_botView.mas_width).multipliedBy(0.7);
            make.bottom.equalTo(_botView.mas_bottom).offset(-10);
        }];
        
        //音乐
        UIImageView *symIV = [[UIImageView alloc]init];
        symIV.image = [UIImage imageNamed:@"music_symbolB"];
        [_botView addSubview:symIV];
//        _symIV = symIV;
        [symIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(20);
            make.left.equalTo(_botView);
            make.bottom.equalTo(_adButton.mas_top).offset(-5);
        }];
        
        _musicL = [[RKLampView alloc]init];
        _musicL.contentStr = @"这是一首好听的音乐";
        [_botView addSubview:_musicL];
        [_musicL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(_botView.mas_width).multipliedBy(0.5);
            make.left.equalTo(symIV.mas_right).offset(2);
            make.centerY.height.equalTo(symIV);
        }];
        
        //视频标题
        _titleL = [[YYLabel alloc]init];
        _titleL.textAlignment = NSTextAlignmentLeft;
        _titleL.textColor = [UIColor whiteColor];
        _titleL.backgroundColor = [UIColor clearColor];
        _titleL.shadowOffset = CGSizeMake(1,1);//设置阴影
        _titleL.numberOfLines = 3;
        _titleL.font = SYS_Font(15);
        //_titleL.userInteractionEnabled = YES;
        [_botView addSubview:_titleL];
        [_titleL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_botView.mas_left);
            make.bottom.equalTo(_musicL.mas_top).offset(-2);
            make.height.mas_greaterThanOrEqualTo(20);
            make.right.mas_lessThanOrEqualTo(_botView.mas_right).offset(-5);
        }];
        
        //视频作者名称
        _nameL = [[UILabel alloc]init];
        _nameL.textAlignment = NSTextAlignmentLeft;
        _nameL.textColor = [UIColor whiteColor];
        _nameL.shadowOffset = CGSizeMake(1,1);//设置阴影
        _nameL.font = SYS_Font(20);
        _nameL.userInteractionEnabled = YES;
        [_botView addSubview:_nameL];
        [_nameL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_botView);
            make.bottom.equalTo(_titleL.mas_top).offset(-2);
            make.right.lessThanOrEqualTo(_botView.mas_right).offset(-5);
        }];
        
        UITapGestureRecognizer *nametap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(clickIconBtn)];
        //nametap.numberOfTouchesRequired = 1;
        [_nameL addGestureRecognizer:nametap];
        
        
        //广告
        _adLabel = [[UILabel alloc]init];
        _adLabel.text = YZMsg(@"广告");
        _adLabel.textAlignment = NSTextAlignmentCenter;
        _adLabel.textColor = [UIColor whiteColor];
        _adLabel.backgroundColor = RGB_COLOR(@"#000000", 0.3);
        _adLabel.font = SYS_Font(15);
        _adLabel.layer.cornerRadius = 2;
        _adLabel.layer.masksToBounds = YES;
        [_botView addSubview:_adLabel];
        _adLabel.hidden = YES;
        [_adLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(45);
            make.height.mas_equalTo(0);
            make.left.equalTo(_botView.mas_left).offset(3);
//            make.top.equalTo(_topicBtn.mas_bottom).offset(3);
            make.bottom.equalTo(_nameL.mas_top).offset(-2);
        }];
        
        //标签
        _topicBtn = [UIButton buttonWithType:0];
        [_topicBtn addTarget:self action:@selector(clickTopicBtn) forControlEvents:UIControlEventTouchUpInside];
        _topicBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        _topicBtn.layer.cornerRadius = 3;
        _topicBtn.layer.masksToBounds = YES;
        _topicBtn.backgroundColor = RGB_COLOR(@"#000000", 0.3);
        [_topicBtn setImage:[UIImage imageNamed:@"video_topic"] forState:0];;
        [_botView addSubview:_topicBtn];
        _topicBtn.hidden = YES;
        [_topicBtn setContentEdgeInsets:UIEdgeInsetsMake(5, 6, 5, 6)];
        [_topicBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_botView.mas_left).offset(3);
//            make.bottom.equalTo(_nameL.mas_top).offset(-2);
            make.bottom.equalTo(_adLabel.mas_top).offset(-2);

            make.height.mas_equalTo(20);
        }];

        
    }
    return _botView;
}

- (UIImageView *)coverImageView {
    if (!_coverImageView) {
        _coverImageView = [[UIImageView alloc] init];
        _coverImageView.userInteractionEnabled = YES;
        _coverImageView.tag = 191107;
    }
    return _coverImageView;
}

- (UIImageView *)bgImgView {
    if (!_bgImgView) {
        _bgImgView = [[UIImageView alloc] init];
        _bgImgView.userInteractionEnabled = YES;
    }
    return _bgImgView;
}

- (UIView *)effectView {
    if (!_effectView) {
        if (@available(iOS 8.0, *)) {
            UIBlurEffect *effect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
            _effectView = [[UIVisualEffectView alloc] initWithEffect:effect];
        } else {
            UIToolbar *effectView = [[UIToolbar alloc] init];
            effectView.barStyle = UIBarStyleBlackTranslucent;
            _effectView = effectView;
        }
    }
    return _effectView;
}
///点赞、评论、关注后这里也更新一下
-(void)updateDataDic:(NSDictionary *)dic {
    NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:_dataDic];
    [m_dic addEntriesFromDictionary:dic];
    _dataDic = [NSDictionary dictionaryWithDictionary:m_dic];
    
    if (![PublicObj checkNull:_fromWhere]) {
        [[NSUserDefaults standardUserDefaults]setObject:_dataDic forKey:@"yb_update_video_on_other"];
    }
}
-(void)noticeUpdateUI:(NSDictionary *)updateDic; {
    self.dataDic = updateDic;
    [[NSUserDefaults standardUserDefaults]removeObjectForKey:@"yb_update_video_on_other"];
}

#pragma mark - 全屏按钮
- (UIButton *)ttFullBtn {
    if (!_ttFullBtn) {
        _ttFullBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_ttFullBtn setTitle:@"全屏观看" forState:0];
        [_ttFullBtn setImage:[UIImage imageNamed:@"tt_全屏"] forState:0];
        [_ttFullBtn addTarget:self action:@selector(clickTtFullBtn) forControlEvents:UIControlEventTouchUpInside];
        _ttFullBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 15, 0, 10);
        _ttFullBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -5, 0, 5);
        _ttFullBtn.layer.cornerRadius = 13;
        _ttFullBtn.layer.masksToBounds = YES;
        _ttFullBtn.backgroundColor = RGB_COLOR(@"#2a2a2a", 1);
        _ttFullBtn.layer.borderColor = RGB_COLOR(@"#4c4c4c", 1).CGColor;
        _ttFullBtn.layer.borderWidth = 1;
        _ttFullBtn.titleLabel.font = SYS_Font(13);
    }
    return _ttFullBtn;
}
-(void)clickTtFullBtn {
    [self endEditing:YES];
    BOOL cuurentIsFull = [self.player isFullScreen];
    [self.player enterFullScreen:!cuurentIsFull animated:YES];
}

@end
