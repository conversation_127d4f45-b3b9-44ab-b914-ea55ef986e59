//
//  YBLookVideoCell.h
//  YBVideo
//
//  Created by YB007 on 2019/11/7.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <ZFPlayer/ZFPlayer.h>
/**
 *  eventType 回调事件
 *  eventDic 事件回调信息(例:点赞,这里回调是否已经点赞,以及点赞总数)
 */
typedef void (^VideoCellBlock)(NSString *eventType ,NSDictionary *eventDic);

@interface YBLookVideoCell : UICollectionViewCell

@property(nonatomic,copy)VideoCellBlock videoCellEvent;
@property(nonatomic,strong)NSString *fromWhere;                     //VC来源
@property(nonatomic,strong) UIButton *followBtn;                    //关注
@property (nonatomic, strong) ZFPlayerController *player;

@property(nonatomic,strong)NSDictionary *dataDic;

-(void)releaseObj:(NSDictionary *)oldDic isBackGround:(BOOL)isBack;
-(void)checkLiveOrAdState:(NSDictionary *)cureentDic;
-(void)startMusicAnimation:(NSDictionary *)startDic;
-(void)stopMusicAnimation:(NSDictionary *)stopDic;

-(void)swipGesGoHome;
-(void)clickLikeBtn;

-(void)updateCommentNums:(NSString *)nums;
-(void)noticeUpdateUI:(NSDictionary *)updateDic;
@end


