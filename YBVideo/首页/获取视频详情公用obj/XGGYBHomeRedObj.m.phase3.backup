//
//  YBHomeRedObj.m
//  YBVideo
//
//  Created by ybRRR on 2023/1/13.
//  Copyright © 2023 cat. All rights reserved.
//

#import "YBHomeRedObj.h"

@implementation YBHomeRedObj
static YBHomeRedObj *_redInstance = nil;

+(instancetype)redInstance{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _redInstance = [[super allocWithZone:NULL]init];
        _redInstance.lookCount = 0;
    });
    return _redInstance;
}
+ (instancetype)allocWithZone:(struct _NSZone *)zone{
    return  [self redInstance];
}
-(void)initWithTimer{
    if(!_lookTimer){
        _lookTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(lookTimerStart) userInfo:nil repeats:YES];
    }
}
-(void)lookTimerStart{
    _lookCount ++;

    if (_lookCount >= 60) {
        _lookCount = 60;
        [self timerPause];
        [self getWatchVideoAward];
    }else{
        CGFloat timeS = (CGFloat)1/60;
        _startFloattime +=timeS;
//        if([self.delegate respondsToSelector:@selector(homeRedReloadTime:)]){
//            [self.delegate homeRedReloadTime:_startFloattime];
//        }
        if(self.startEvent){
            self.startEvent(_startFloattime);
        }
//        progressView.progress =_startFloattime;
    }
    NSLog(@"yblookvc-------:%d",_lookCount);
}
-(void)timePause{
    [self timerPause];
}
-(void)timerPause{
    if (_lookTimer) {
        [_lookTimer setFireDate:[NSDate distantFuture]];
    }
}
-(void)timerBegin{
    if (_lookTimer) {
        [_lookTimer setFireDate:[NSDate date]];
    }
}
-(void)timerEnd{
    if (_lookTimer) {
        [_lookTimer invalidate];
        _lookTimer = nil;
    }

}
-(void)timeGo{
    [self timerBegin];
}

-(void)getWatchVideoAward{
    NSDictionary *dic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken]};
    [YBNetworking postWithUrl:@"Video.watchVideoAward" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if(code == 0){
            NSDictionary *infos = [info firstObject];
            if ([minstr([infos valueForKey:@"continue_receive"]) isEqual:@"1"]) {
                _lookCount = 0;
                _startFloattime = 0.0f;
                [self timerBegin];
            }

        }else{
            [self timerEnd];
        }
        [MBProgressHUD showError:msg];
        } Fail:^(id fail) {
            
        }];

}

@end
