//
//  YBHomeRedObj.h
//  YBVideo
//
//  Created by ybRRR on 2023/1/13.
//  Copyright © 2023 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void(^homeRedEvent)(CGFloat currentTime);
//@protocol homeRedDelegate <NSObject>
//-(void)homeRedReloadTime:(CGFloat)currentTime;
//@end

@interface YBHomeRedObj : NSObject
+(instancetype)redInstance;

@property(nonatomic,strong)NSTimer *lookTimer;
@property(nonatomic,assign)int lookCount;
@property(nonatomic,assign)CGFloat startFloattime;
@property(nonatomic, copy)homeRedEvent startEvent;
//@property(nonatomic, assign)id<homeRedDelegate>delegate;
-(void)initWithTimer;
-(void)lookTimerStart;
-(void)timerBegin;
-(void)timerEnd;
-(void)timerPause;
@end

