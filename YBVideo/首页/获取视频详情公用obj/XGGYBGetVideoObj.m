//
//  YBGetVideoObj.m
//  YBVideo
//
//  Created by YB007 on 2019/11/21.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBGetVideoObj.h"

#import "YBLookVideoVC.h"
#import "YBVipVC.h"
#import "YBRechargeVC.h"

@implementation YBGetVideoObj
static  YBGetVideoObj*_lookManeger = nil;

+(instancetype)lookManeger {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _lookManeger = [[super allocWithZone:NULL]init];
        _lookManeger.playIndex = 0;
        _lookManeger.paging = 1;
        _lookManeger.baseUrl = @"";
    });
    return _lookManeger;
}

+(instancetype)allocWithZone:(struct _NSZone *)zone {
    return [self lookManeger];
}

-(void)goLookVC {
    if ([PublicObj checkNull:minstr(_fromWhere)]) {
        [MBProgressHUD showPop:YZMsg(@"请标记页面来源")];
        return;
    }
    if ([PublicObj checkNull:minstr(_videoID)]) {
        [MBProgressHUD showPop:YZMsg(@"请标记视频id")];
        return;
    }
    YBWeakSelf;
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Video.getVideo" Dic:@{@"videoid":_videoID,@"mobileid":[PublicObj getDeviceUUID]} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            [weakSelf getInfo:infoDic];
        }else if (code == 1001){
            //需要登录
            [PublicObj warnLogin];
        }else if(code == 1002){
            //需要开通Vip
            [weakSelf limitPlayVideo:1 andMsg:msg];
        }else if (code == 1003){
            //需要支付
            [weakSelf limitPlayVideo:2 andMsg:msg];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
    
}
-(void)getInfo:(NSDictionary *)infoDic {
    if ([PublicObj checkNull:minstr(_baseUrl)] && _videoList.count<=0) {
        //为空代表进入的观看页面将不能上下滑动
        _videoList = @[infoDic].mutableCopy;
    }else{
        NSMutableDictionary *m_dic;
        int index = -1;
        for (int i = 0; i<_videoList.count; i++) {
            NSDictionary *listDic = _videoList[i];
            if ([[listDic valueForKey:@"id"]isEqual:[infoDic valueForKey:@"id"]]) {
                index = i;
                m_dic = [NSMutableDictionary dictionaryWithDictionary:listDic];
                [m_dic setObject:[infoDic valueForKey:@"islike"] forKey:@"islike"];
                [m_dic setObject:[infoDic valueForKey:@"likes"] forKey:@"likes"];
                [m_dic setObject:[infoDic valueForKey:@"comments"] forKey:@"comments"];
                [m_dic setObject:[infoDic valueForKey:@"isattent"] forKey:@"isattent"];
                [m_dic setObject:[infoDic valueForKey:@"shares"] forKey:@"shares"];
                break;
            }
        }
        if (index != -1) {
            NSDictionary *needChangeDic = [NSDictionary dictionaryWithDictionary:m_dic];
            [_videoList replaceObjectAtIndex:index withObject:needChangeDic];
        }
    }
    [self canLookVideo:_playIndex andList:_videoList andPage:_paging andUrl:_baseUrl];
}

-(void)limitPlayVideo:(int)limiteType andMsg:(NSString *)msg{
    //limiteType   1：vip   2：pay
    YBWeakSelf
    NSDictionary *contentDic = @{@"title":@"",@"msg":msg,@"left":YZMsg(@"取消"),@"right":(limiteType==1)?YZMsg(@"开通会员"):YZMsg(@"观看")};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            if ([[Config getOwnID] intValue]<=0) {
                [PublicObj warnLogin];
                return;
            }
            if ([YBYoungManager shareInstance].youngSwitch == 1) {
                [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
                return;
            }
            //确认按钮
            if (limiteType == 1) {
                //vip
                YBVipVC *vipVC = [[YBVipVC alloc]init];
                [[XGGAppDelegate sharedAppDelegate]pushViewController:vipVC animated:YES];
            }else {
                //付费观看
                [weakSelf goPay];
            }
        }
    }];
}
//可以播放
-(void)canLookVideo:(int)row andList:(NSMutableArray *)videoList andPage:(int)page andUrl:(NSString *)baseUrl{
    YBLookVideoVC *ybLook = [[YBLookVideoVC alloc]init];
    ybLook.fromWhere = _fromWhere;
    ybLook.firstPush = YES;
    ybLook.pushPlayIndex = row;
    ybLook.sourceBaseUrl = baseUrl;
    ybLook.videoList = [videoList mutableCopy];
    ybLook.pages = page;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:ybLook animated:YES];
}

#pragma mark -
-(void)goPay {
    YBWeakSelf;
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Video.setVideoPay" Dic:@{@"videoid":_videoID} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            [weakSelf canLookVideo:_playIndex andList:_videoList andPage:_paging andUrl:_baseUrl];
        }else if (code == 1004){
            //余额不足
            [weakSelf noMoney:msg];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
    
}
//余额不足提示
-(void)noMoney:(NSString *)msg {
    NSDictionary *contentDic = @{@"title":@"",@"msg":msg,@"left":YZMsg(@"取消"),@"right":YZMsg(@"去充值")};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            //确认按钮
           YBRechargeVC *chargeVC = [[YBRechargeVC alloc]init];
           [[XGGAppDelegate sharedAppDelegate]pushViewController:chargeVC animated:YES];
        }
    }];
}


@end
