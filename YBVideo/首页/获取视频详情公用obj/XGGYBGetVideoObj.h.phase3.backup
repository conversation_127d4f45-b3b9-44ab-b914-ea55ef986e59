//
//  YBGetVideoObj.h
//  YBVideo
//
//  Created by YB007 on 2019/11/21.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface YBGetVideoObj : NSObject

+(instancetype)lookManeger;


/**
 *  此功能类仅用于其他页面 push 到 观看页面(YBLookVideoVC)
 *
 *  注意: 从消息顶部的 赞、评论、@ 功能 进来不能上下滑动 baseUrl传空字符串即可
 */

/** 列表开始播放下标 默认:0 */
@property(nonatomic,assign)int playIndex;
/** 列表开始播放视频的id */
@property(nonatomic,strong)NSString *videoID;
/** 视频列表 */
@property(nonatomic,strong)NSMutableArray *videoList;
/** 当前分页下标  默认:1*/
@property(nonatomic,assign)int paging;
/** 视频列表url */
@property(nonatomic,strong)NSString *baseUrl;           //为空代表进入的观看页面将不能上下滑动
/** 页面来源 */
@property(nonatomic,strong)NSString *fromWhere;


-(void)goLookVC;

@end


