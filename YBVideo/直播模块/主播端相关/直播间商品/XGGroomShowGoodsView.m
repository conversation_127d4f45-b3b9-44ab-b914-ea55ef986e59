//
//  roomShowGoodsView.m
//  yunbaolive
//
//  Created by IOS1 on 2019/9/2.
//  Copyright © 2019 cat. All rights reserved.
//

#import "roomShowGoodsView.h"
#import "goodsShowCell.h"
#import "RelationGoodsVC.h"

@interface roomShowGoodsView ()<UITableViewDelegate,UITableViewDataSource,goodsShowCellDelegate> {
    NSMutableArray *goodsList;
    int page;
    UIView *whiteView;
    UILabel *nothingLabel;
    BOOL _isAnchor;
    NSDictionary *zhuboDic;
    UILabel *titleL;
}
@property (nonatomic,strong) UITableView *goodsTableV;

@end

@implementation roomShowGoodsView

- (instancetype)initWithFrom:(BOOL)isAnchor andZhuboMsg:(NSDictionary *)dic{
    self = [super init];
    self.frame = CGRectMake(0, 0, _window_width, _window_height);
    if (self) {
        _isAnchor = isAnchor;
        zhuboDic = dic;
        page = 1;
        goodsList = [NSMutableArray array];
        [self creatUI];
        if (isAnchor) {
            nothingLabel.text = YZMsg(@"当前暂无在售商品\n点击右上角添加商品到购物车");
        }else{
            nothingLabel.text = YZMsg(@"当前暂无在售商品");
        }
        [self requestData];
    }
    return self;
}
- (void)closeBtnClick{
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.y = _window_height;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}
- (void)creatUI{
    UIButton *closeBtn = [UIButton buttonWithType:0];
    closeBtn.frame = CGRectMake(0, 0, _window_width, _window_height*0.52);
    [closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:closeBtn];
    whiteView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, _window_height*0.48)];
    whiteView.backgroundColor = [UIColor whiteColor];
    [self addSubview:whiteView];
    UIView *headerView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 40)];
    headerView.backgroundColor = [UIColor whiteColor];
    [whiteView addSubview:headerView];
    titleL = [[UILabel alloc]init];
    titleL.font = [UIFont boldSystemFontOfSize:13];
    titleL.text = [NSString stringWithFormat:YZMsg(@"在售商品 %@"),@"0"];//@"在售商品 0";
    [headerView addSubview:titleL];
    [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(headerView);
    }];
    [PublicObj lineViewWithFrame:CGRectMake(0, 39, _window_width, 1) andColor:RGB_COLOR(@"#f0f0f0", 1) andView:headerView];
    if (_isAnchor) {
        UIButton *addBtn = [UIButton buttonWithType:0];//在售-加
        [addBtn setImage:[UIImage imageNamed:@"在售-加"] forState:0];
        [addBtn setTitle:YZMsg(@"添加商品") forState:0];
        [addBtn setTitleColor:Pink_Cor forState:0];
        addBtn.titleLabel.font = SYS_Font(13);
        [addBtn addTarget:self action:@selector(doAddGoods) forControlEvents:UIControlEventTouchUpInside];
        [headerView addSubview:addBtn];
        [addBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(headerView);
            make.right.equalTo(headerView).offset(-10);
        }];
    }
    [whiteView addSubview:self.goodsTableV];
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.y = _window_height *0.52;
    }];
}
- (UITableView *)goodsTableV{
    if (!_goodsTableV) {
        _goodsTableV = [[UITableView alloc]initWithFrame:CGRectMake(0, 40, _window_width, whiteView.height-ShowDiff-40) style:0];
        _goodsTableV.delegate = self;
        _goodsTableV.dataSource = self;
        _goodsTableV.separatorStyle = 0;
        _goodsTableV.backgroundColor = [UIColor whiteColor];
        nothingLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, _goodsTableV.height/2-30, _goodsTableV.width, 30)];
        nothingLabel.numberOfLines = 2;
        nothingLabel.textColor = RGB_COLOR(@"#B4B4B4", 1);
        nothingLabel.font = SYS_Font(12);
        nothingLabel.hidden = YES;
        nothingLabel.textAlignment = NSTextAlignmentCenter;
        [_goodsTableV addSubview:nothingLabel];
        _goodsTableV.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
            page = 1;
            [self requestData];
        }];
        _goodsTableV.mj_footer = [MJRefreshFooter footerWithRefreshingBlock:^{
            page ++;
            [self requestData];
        }];
    }
    return _goodsTableV;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return goodsList.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSString *indexrow  = [NSString stringWithFormat:@"goodsShowCELL%ld",indexPath.row];
    goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:indexrow];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];
        cell.stateImgV.hidden = YES;
        cell.delegate = self;
    }
    cell.model = goodsList[indexPath.row];
    if (_isAnchor) {
        cell.setBtn.hidden = YES;
        cell.liveGoodsView.hidden = NO;
        if ([cell.model.live_isshow isEqual:@"1"]) {
            [cell.showBtn setTitleColor:[UIColor grayColor] forState:0];
        }else{
            [cell.showBtn setTitleColor:Pink_Cor forState:0];
        }
        if ([cell.model.type isEqual:@"2"]) {
            cell.priceOldL.hidden = NO;
            cell.priceOldL.text = [NSString stringWithFormat:YZMsg(@"佣 ¥%@"),cell.model.commission];
            cell.priceOldL.textColor = Pink_Cor;
        }
        cell.contentView.backgroundColor = [UIColor whiteColor];
        cell.nameL.textColor = [UIColor blackColor];
        cell.bottomLine.backgroundColor = RGB(240, 240, 240);
    }else{
        [cell.setBtn setTitle:YZMsg(@"去看看") forState:0];
    }
    cell.liveUid = _isAnchor?[Config getOwnID]:minstr([zhuboDic valueForKey:@"uid"]);
    [cell.setBtn setTitleColor:Pink_Cor forState:0];
    cell.setBtn.layer.borderColor = Pink_Cor.CGColor;

    return cell;
    
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 101;
}

- (void)requestData{
    [YBNetworking postWithUrl:@"Shop.GetSale" Dic:@{@"p":@(page),@"liveuid":_isAnchor?[Config getOwnID]:minstr([zhuboDic valueForKey:@"uid"])} Suc:^(int code, id info, NSString *msg) {
        [_goodsTableV.mj_header endRefreshing];
        [_goodsTableV.mj_footer endRefreshing];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            if (page == 1) {
                [goodsList removeAllObjects];
                titleL.text = [NSString stringWithFormat:YZMsg(@"在售商品 %@"),minstr([infoDic valueForKey:@"nums"])];
            }
            NSArray *array = [infoDic valueForKey:@"list"];
            for (NSDictionary *dic in array) {
                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
                model.goosdType = _isAnchor ? 1 : 2;
                [goodsList addObject:model];
            }
            [_goodsTableV reloadData];
            if (goodsList.count == 0) {
                nothingLabel.hidden = NO;
            }else{
                nothingLabel.hidden = YES;
            }
        }

        } Fail:^(id fail) {
            [_goodsTableV.mj_header endRefreshing];
            [_goodsTableV.mj_footer endRefreshing];

        }];
}
- (void)doAddGoods{
    YBWeakSelf;
    NSArray *arr = @[YZMsg(@"我的商品"),YZMsg(@"平台商品")];
    _actionSheet = [[YBAlertActionSheet alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) cancelTitle:YZMsg(@"取消") cancelColor:[UIColor blackColor]  andRowHeight:50 andOtherTitle:arr];
    
    _actionSheet.btnEvent = ^(NSString *btnTitle) {
        NSString *titleStr = btnTitle;
        if ([titleStr isEqual:YZMsg(@"取消")]) {
            [weakSelf.actionSheet removeFromSuperview];
            weakSelf.actionSheet = nil;
        }else if ([titleStr isEqual:YZMsg(@"我的商品")]) {
            [weakSelf.actionSheet removeFromSuperview];
            weakSelf.actionSheet = nil;

            RelationGoodsVC *vc = [[RelationGoodsVC alloc]init];
            vc.haveGoods = goodsList;
            vc.modalPresentationStyle = UIModalPresentationFullScreen;
            [[XGGAppDelegate sharedAppDelegate] presentViewController:vc animated:YES completion:nil];
        }else if ([titleStr isEqual:YZMsg(@"平台商品")]) {
            [weakSelf.actionSheet removeFromSuperview];
            weakSelf.actionSheet = nil;
            
            RelationGoodsVC *vc = [[RelationGoodsVC alloc]init];
            vc.isOtherSale = YES;
            vc.haveGoods = goodsList;
            vc.modalPresentationStyle = UIModalPresentationFullScreen;

            [[XGGAppDelegate sharedAppDelegate] presentViewController:vc animated:YES completion:nil];

        }
    };
    [self addSubview:_actionSheet];

}
- (void)removeThisGoods:(RelationGoodsModel *)model{
    [goodsList removeObject:model];
    [_goodsTableV reloadData];
    NSDictionary *infos = @{@"status":@"0"};
    if (self.showEvent) {
        self.showEvent(infos, model);
    }

}
-(void)reloadShowGoods:(NSDictionary *)infos andmodel:(nonnull RelationGoodsModel *)model
{
    //0代表不显示  1 代表显示
    NSLog(@"showGoods00000l:%@", infos);
    if (_isAnchor) {
        if (self.showEvent) {
            self.showEvent(infos, model);
        }
    }
    [_goodsTableV.mj_header beginRefreshing];
}
@end
