//
//  startLiveClassVC.m
//  yunbaolive
//
//  Created by Boom on 2018/9/28.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "startLiveClassVC.h"
#import "startLiveClassCell.h"
@interface startLiveClassVC ()<UITableViewDelegate,UITableViewDataSource>{
    UITableView *classTable;
    NSArray *classArray;
}
@property (nonatomic,strong) UIView *naviView;
@property (nonatomic,strong) UIView *subNavi;
@property (nonatomic,strong) UILabel *titleL;
@property (nonatomic,strong) UIButton *leftBtn;

@end

@implementation startLiveClassVC
-(void)navtion{
    _naviView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
    _naviView.backgroundColor = UIColor.clearColor;
    [self.view addSubview:_naviView];
    
    _subNavi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _naviView.height)];
    _subNavi.backgroundColor = RGB_COLOR(@"#0D0927", 1);
    [_naviView addSubview:_subNavi];
    
    _leftBtn = [UIButton buttonWithType:0];
    _leftBtn.titleLabel.font = SYS_Font(15);
    [_leftBtn setTitleColor:[UIColor whiteColor] forState:0];
    _leftBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
    [_leftBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
    [_leftBtn addTarget:self action:@selector(clickNaviLeftBtn) forControlEvents:UIControlEventTouchUpInside];
    [_naviView addSubview:_leftBtn];
    
    _titleL = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2-80, 34+statusbarHeight, 160, 20)];
    _titleL.font = NaviTitle_Font;
    _titleL.textColor = NaviTitle_Color;
    _titleL.textAlignment = NSTextAlignmentCenter;
    //_titleL加到 _subNavi 做个人中心滑动隐藏效果
    _titleL.text = YZMsg(@"选择直播频道");
    [_subNavi addSubview:_titleL];
    
}
-(void)clickNaviLeftBtn{
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.interactivePopGestureRecognizer.delegate = nil;
    self.navigationController.navigationBar.hidden = YES;
    if ([[UIDevice currentDevice] systemVersion].floatValue >= 11.0) {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    self.view.backgroundColor =  RGB_COLOR(@"#0D0927", 1);;
    [self navtion];
    classArray = [common liveclass];
    NSMutableArray *arr = [NSMutableArray array];
    for (NSDictionary*dic in classArray) {
        if (![minstr([dic valueForKey:@"id"]) isEqual:@"0"]) {
            [arr addObject:dic];
        }
    }
    classArray = arr;

    classTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
    classTable.delegate = self;
    classTable.dataSource = self;
    classTable.separatorStyle = 0;
    classTable.backgroundColor =  RGB_COLOR(@"#0D0927", 1);;
    [self.view addSubview:classTable];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return classArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    startLiveClassCell *cell = [tableView dequeueReusableCellWithIdentifier:@"startLiveClassCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"startLiveClassCell" owner:nil options:nil] lastObject];
    }
    NSDictionary *dic = classArray[indexPath.row];
    if ([minstr([dic valueForKey:@"id"])isEqual:_classID]) {
        cell.selectImfView.hidden = NO;
    }else{
        cell.selectImfView.hidden = YES;
    }
    [cell.iconImgView sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"thumb"])] placeholderImage:[UIImage imageNamed:@"live_all"]];
    cell.nameLabel.text = minstr([dic valueForKey:@"name"]);
    cell.contentLabel.text = minstr([dic valueForKey:@"des"]);
    cell.backgroundColor =  RGB_COLOR(@"#0D0927", 1);;
    return cell;
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 50)];
    view.backgroundColor = RGB_COLOR(@"#1B1633", 1);
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(10, 5, _window_width-20, 40)];
    label.font = [UIFont systemFontOfSize:13];
    label.textColor = UIColor.whiteColor;
    label.numberOfLines = 2;
    label.text = YZMsg(@"注意选择适合自己的频道。直播过程中，若运营人员发现选择的频道和直播内容不相符的情况，会调整您的直播频道。");
    [view addSubview:label];
    return view;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 50;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 60;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *dic = classArray[indexPath.row];
    if (![minstr([dic valueForKey:@"id"])isEqual:_classID]) {
        self.block(dic);
        [self dismissViewControllerAnimated:YES completion:nil];
    }
    
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
