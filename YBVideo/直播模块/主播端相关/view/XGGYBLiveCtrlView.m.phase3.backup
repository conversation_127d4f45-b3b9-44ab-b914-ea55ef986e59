//
//  YBLiveCtrlView.m
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveCtrlView.h"
#import "YBLiveChatView.h"
#import "YBUserListView.h"
#import "UIImage+RKCircleImg.h"
#import "YBLiveFucView.h"
#import "YBUserEnterAnimation.h"
#import "expensiveGiftV.h"
#import "continueGift.h"
#import "YBShareView.h"
#import "YBOnSaleView.h"
#import "YBGoodsListVC.h"
#import "RKShowPaintedView.h"
#import "SDCycleScrollView.h"
#import "YBDayTaskView.h"
#import "guardShowView.h"
#import "YBTxLinkMicView.h"
#import "YBAnchorOnline.h"
#import "YBAnchorPKAlert.h"
#import "YBAnchorPKView.h"
#import "YBAlertActionSheet.h"
#import "roomShowGoodsView.h"
#import "RelationGoodsVC.h"
#import "YBAnchorLinkInfo.h"

@interface YBLiveCtrlView()<UIGestureRecognizerDelegate,haohuadelegate,SDCycleScrollViewDelegate,guardShowDelegate> {
    PublicView *_pubTiems;      //开播倒计时
    int _liveTime;              //直播时长
    expensiveGiftV *haohualiwuV;                //豪华礼物
    continueGift *continueGifts;                //连送礼物
    UIView *liansongliwubottomview;
    UIImageView *starImage;
    CGFloat starX;
    CGFloat starY;
    guardShowView *gShowView;
    BOOL _hostLinking;                          //主播连麦中【包含用户-主播、主播-主播连麦】
    
    roomShowGoodsView *roomGoodsV;

}
@property(nonatomic,strong)UIView *hostInfoView;                //左上角主播信息
@property(nonatomic,strong)UIButton *hostAvatarBtn;             //主播头像
@property(nonatomic,strong)UILabel *hostNameL;                  //主播昵称
@property(nonatomic,strong)UILabel *hostIDL;                    //主播id

@property(nonatomic,strong)UIButton *hostVotesBtn;              //主播映票
@property(nonatomic,strong)UIButton *hostGuardBtn;              //守护
@property(nonatomic,strong)UIButton *hostLiveTimeBtn;           //主播直播时长
@property(nonatomic,strong)NSArray *roomGameArray;
@property(nonatomic,strong)SDCycleScrollView *roomGameCycle;    //每日任务
@property(nonatomic,strong)YBPageControl *roomGamePage;
@property(nonatomic,strong)YBDayTaskView *dayTaskView;          //每日任务

@property(nonatomic,strong)YBUserListView *userListView;        //用户列表
@property(nonatomic,strong)YBLiveChatView *liveChatView;        //公屏聊天
@property(nonatomic,strong)YBUserEnterAnimation *userAnimation; //进场动画
@property(nonatomic,strong)YBOnSaleView *onsaleView;            //在售商品小窗
@property(nonatomic,strong)RKShowPaintedView *paintedShowRegion;//手绘礼物显示区域


@property(nonatomic,strong)UIButton *closeLiveBtn;              //↓...底部功能键
@property(nonatomic,strong)UIButton *msgBtn;
@property(nonatomic,strong)UILabel *redPoint;
@property(nonatomic,strong)UIButton *moreBtn;
//@property(nonatomic,strong)UIButton *cartBtn;

@property(nonatomic,strong)YBTxLinkMicView *linkMicView;        //连麦窗口
@property(nonatomic,strong)YBAnchorOnline *anchorOnlineView;    //主播在线列表
@property(nonatomic,strong)YBAnchorPKAlert *pkAlertView;
@property(nonatomic,strong)YBAnchorPKView *pkView;
@property(nonatomic,strong)YBAnchorLinkInfo *pkTohostInfoV;
@property(nonatomic,strong)UIButton *pkFollowBtn;
@property (nonatomic, strong) YBAlertActionSheet *actionSheet;

@end

@implementation YBLiveCtrlView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _liveTime = 0;
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tapGes)];
        tap.delegate = self;
        [self addGestureRecognizer:tap];
        //手绘礼物
        [self addSubview:self.paintedShowRegion];
        //连送礼物显示区域
        liansongliwubottomview = [[UIView alloc]init];
        liansongliwubottomview.userInteractionEnabled = NO;
        liansongliwubottomview.backgroundColor = UIColor.clearColor;
        [self addSubview:liansongliwubottomview];
        liansongliwubottomview.frame = CGRectMake(0, statusbarHeight + 200,_window_width,140);
        
        [self addSubview:self.hostInfoView];
        [self addSubview:self.hostVotesBtn];
        [self addSubview:self.hostGuardBtn];
        [self addSubview:self.hostLiveTimeBtn];
        [self addSubview:self.userListView];
        [self addSubview:self.liveChatView];
        [self addSubview:self.userAnimation];
        [self addSubview:self.botBtnFunView];
        [self addSubview:self.chatTool];
        self.roomGameArray = @[@"日任-房间"];
        if ([lagType isEqual:EN]) {
            self.roomGameArray = @[@"日任-房间_en"];
        }
        [self addSubview:self.roomGameCycle];
        [self addSubview:self.roomGamePage];
        if ([PublicObj isUp]) {
            self.roomGameCycle.hidden = self.roomGamePage.hidden = YES;
        }
        [self addSubview:self.linkCtrBtn];
        [self layoutIfNeeded];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [_hostInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(11);
        make.top.equalTo(self.mas_top).offset(27+statusbarHeight);
        make.right.equalTo(_hostNameL.mas_right).offset(15);
        make.height.mas_equalTo(34);
    }];
    
    [_userListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(40);
        make.left.equalTo(_hostInfoView.mas_right).offset(5);
        make.centerY.equalTo(_hostInfoView.mas_centerY).offset(-5);
        make.right.equalTo(self.mas_right).offset(0);
    }];
    
    [_hostVotesBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_hostInfoView);
        make.top.equalTo(_hostInfoView.mas_bottom).offset(8);
        make.height.mas_equalTo(22);
    }];
    [_hostGuardBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_hostVotesBtn.mas_right).offset(5);
        make.height.centerY.equalTo(_hostVotesBtn);
    }];
    [_hostLiveTimeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_hostVotesBtn);
        make.top.equalTo(_hostVotesBtn.mas_bottom).offset(4);
        make.height.mas_equalTo(22);
    }];
    [_roomGameCycle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(70);
        make.height.mas_equalTo(50);
        make.left.equalTo(_hostVotesBtn);
        make.top.equalTo(_hostVotesBtn.mas_bottom).offset(30);
    }];
    [_roomGamePage mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(10);
        if (@available(iOS 14.0,*)) {
            make.width.mas_equalTo(200);
        }else {
            make.width.mas_equalTo(80);
        }
        make.centerX.equalTo(_roomGameCycle);
        make.top.equalTo(_roomGameCycle.mas_bottom).offset(0);
    }];
    
    [_userAnimation mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(10);
        make.bottom.equalTo(_liveChatView.mas_top).offset(-20);
        make.width.equalTo(self);
        make.height.mas_equalTo(20);
    }];
    CGFloat blodW = 50;
    if ([PublicObj isBlodText]) {
        blodW = 60;
    }
    [_linkCtrBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(blodW);
        make.bottom.equalTo(_botBtnFunView.mas_top).offset(-10);
        make.right.equalTo(self.mas_right).offset(-15);
    }];
    [_linkCtrBtn layoutIfNeeded];
    _linkCtrBtn = [PublicObj setUpImgDownText:_linkCtrBtn];
    
    [_hostGuardBtn layoutIfNeeded];
    [_hostGuardBtn setImageEdgeInsets:UIEdgeInsetsMake(0, _hostGuardBtn.titleLabel.size.width, 0, -_hostGuardBtn.titleLabel.size.width)];
    [_hostGuardBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, -_hostGuardBtn.imageView.size.width-1, 0, _hostGuardBtn.imageView.size.width+1)];
}

#pragma mark - 点击事件
-(void)clickHostAvatarBtn {
    if (self.liveCtrEvent) {
        self.liveCtrEvent(@"控制-信息卡", @{@"id":[Config getOwnID]});
    }
}
-(void)clickKeyBtn {
    [_chatTool.chatTF becomeFirstResponder];
    _botBtnFunView.hidden = YES;
    _chatTool.hidden = NO;
}
-(void)clikcCloseLiveBtn {
    if (self.liveCtrEvent) {
        self.liveCtrEvent(@"控制-关闭", @{});
    }
}
-(void)clikcMsgBtn {
    if (self.liveCtrEvent) {
        self.liveCtrEvent(@"控制-私信", @{});
    }
}
-(void)clikcMoreBtn {
    _moreBtn.selected = !_moreBtn.selected;
    YBWeakSelf;
    [YBLiveFucView showBotFunViewWithTorch:_isTorch Complete:^(NSString *eventType) {
        [weakSelf funViewCallBack:eventType];
    }];
}
-(void)clikcCartBtn {
    
    YBWeakSelf;
    if (roomGoodsV) {
        [roomGoodsV removeFromSuperview];
        roomGoodsV = nil;
    }
    roomGoodsV = [[roomShowGoodsView alloc]initWithFrom:YES andZhuboMsg:@{}];
    roomGoodsV.showEvent = ^(NSDictionary * _Nonnull infoDic, RelationGoodsModel * _Nonnull models) {
        NSLog(@"-----broadcast:%@", infoDic);
        NSDictionary *goodDic = @{@"name":models.name,
                                  @"old_price":[PublicObj checkNull: models.original_price]?@"":models.original_price,
                              @"price":models.price,
                              @"thumb":models.thumb,
                              @"type":models.type,
                              @"id":models.goodsid};
        if ([minstr([infoDic valueForKey:@"status"]) isEqual:@"1"]) {
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:goodDic];
            [m_dic addEntriesFromDictionary:@{@"action":@"1"}];
            weakSelf.liveCtrEvent(@"控制-在售",[NSDictionary dictionaryWithDictionary:m_dic]);

        }else{
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:goodDic];
            [m_dic addEntriesFromDictionary:@{@"action":@"0"}];
            weakSelf.liveCtrEvent(@"控制-在售",[NSDictionary dictionaryWithDictionary:m_dic]);

        }
    };
    [self addSubview:roomGoodsV];
//    _onsaleView = [YBOnSaleView showSaleWithAnchor:YES andAnchorID:[Config getOwnID]];
//    YBWeakSelf;
//    _onsaleView.onSaleEvent = ^(OnSaleType saleCtrEvent,NSDictionary *goodsDic) {
//        [weakSelf onSaleEvent:saleCtrEvent andGoodsDic:goodsDic];
//    };
}
-(void)onSaleEvent:(OnSaleType)ctrType andGoodsDic:(NSDictionary *)goodsDic{
    YBWeakSelf;
    if (ctrType == OnSaleType_addGoods) {
        //添加在售商品
//        _onsaleView.hidden = YES;
//        YBGoodsListVC *gVC = [[YBGoodsListVC alloc]init];
//        gVC.enterType = GoodsEnterType_Live;
//        gVC.touserID = [Config getOwnID];
//        gVC.refreshEvent = ^{
//            _onsaleView.hidden = NO;
//            [_onsaleView pullData];
//        };
//        [[XGGAppDelegate sharedAppDelegate]pushViewController:gVC animated:YES];
        
        YBWeakSelf;
        NSArray *arr = @[YZMsg(@"我的商品"),YZMsg(@"平台商品")];
        _actionSheet = [[YBAlertActionSheet alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) cancelTitle:YZMsg(@"取消") cancelColor:[UIColor blackColor]  andRowHeight:50 andOtherTitle:arr];
        
        _actionSheet.btnEvent = ^(NSString *btnTitle) {
            NSString *titleStr = btnTitle;
            if ([titleStr isEqual:YZMsg(@"取消")]) {
                [weakSelf.actionSheet removeFromSuperview];
                weakSelf.actionSheet = nil;
            }else if ([titleStr isEqual:YZMsg(@"我的商品")]) {
                [weakSelf.actionSheet removeFromSuperview];
                weakSelf.actionSheet = nil;

                RelationGoodsVC *vc = [[RelationGoodsVC alloc]init];
//                vc.haveGoods = goodsList;
                [[XGGAppDelegate sharedAppDelegate] presentViewController:vc animated:YES completion:nil];
            }else if ([titleStr isEqual:YZMsg(@"平台商品")]) {
                [weakSelf.actionSheet removeFromSuperview];
                weakSelf.actionSheet = nil;
                
                RelationGoodsVC *vc = [[RelationGoodsVC alloc]init];
                vc.isOtherSale = YES;
//                vc.haveGoods = goodsList;
                [[XGGAppDelegate sharedAppDelegate] presentViewController:vc animated:YES completion:nil];

            }
        };
        [self addSubview:_actionSheet];

    }
    else if (ctrType == OnSaleType_Delete){
        //删除在售商品
        [MBProgressHUD showMessage:@""];
        [YBNetworking postWithUrl:@"Shop.setSale" Dic:@{@"goodsid":minstr([goodsDic valueForKey:@"id"]),@"issale":@"0"} Suc:^(int code, id info, NSString *msg) {
            [MBProgressHUD hideHUD];
            [MBProgressHUD showPop:msg];
            if (code == 0 && _onsaleView) {
                int live_isshow = [minstr([goodsDic valueForKey:@"live_isshow"]) intValue];
                if (live_isshow == 1) {
                    //删除的商品正在展示,自己发送一个取消展示的socket
                    NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:goodsDic];
                    [m_dic addEntriesFromDictionary:@{@"action":@"0"}];
                    weakSelf.liveCtrEvent(@"控制-在售",[NSDictionary dictionaryWithDictionary:m_dic]);
                }
                [_onsaleView pullData];
            }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];
        }];
    }
    else if (ctrType == OnSaleType_Show || ctrType == OnSaleType_Hidden){
        //在用户端公屏聊天下方:展示-非展示
        NSString *showStr = (ctrType == OnSaleType_Show)?@"1":@"0";
        [MBProgressHUD showMessage:@""];
        [YBNetworking postWithUrl:@"Shop.setShowGoods" Dic:@{@"goodsid":minstr([goodsDic valueForKey:@"id"]),@"isshow":showStr} Suc:^(int code, id info, NSString *msg) {
            [MBProgressHUD hideHUD];
            [MBProgressHUD showPop:msg];
            if (code == 0) {
                [_onsaleView pullData];
                NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:goodsDic];
                [m_dic addEntriesFromDictionary:@{@"action":showStr}];
                weakSelf.liveCtrEvent(@"控制-在售",[NSDictionary dictionaryWithDictionary:m_dic]);
            }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];
        }];
        
    }
    
}
#pragma mark - 连麦开始
//用户-主播连麦
-(void)clickLinkCtrBtn:(UIButton *)sender {
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Linkmic.setMic" Dic:@{@"ismic":@(!sender.selected)} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showPop:msg];
        if (code == 0) {
            sender.selected = !sender.selected;
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
/** socket展示连麦用户小窗 */
-(void)socketShowLinkUserPop:(NSDictionary *)linkDic;{
    
    NSDictionary *subDic = @{@"userid":minstr([linkDic valueForKey:@"uid"]),
                             @"uname":minstr([linkDic valueForKey:@"uname"]),
                             @"playurl":minstr([linkDic valueForKey:@"playurl"]),
                             @"pushurl":@"0",
    };
    [self createLinkMicView:subDic andHostToHost:NO];
}
/** socket连麦用户挂断 */
-(void)socketLinkUserDisconnect:(NSDictionary *)linkDic; {
    //混流
    NSDictionary *mixDic = @{@"selfUrl":_liveHostUrl,@"otherUrl":@""};
    [_linkMicView linkMicMixStream:mixDic andHostToHost:_linkMicView.isHostToHost];
    
    [self destroyLinkMicView];
}

//主播-主播连麦
-(void)showAnchorOnlineList {
    [self destroyAnchorOnlineList];
    _anchorOnlineView = [YBAnchorOnline showAnchorListOnView:self];
    YBWeakSelf;
    _anchorOnlineView.anchorListEvent = ^(AnchorListType callBackType,NSDictionary *otherInfo, NSDictionary *myInfo) {
        [weakSelf anchorOnlineViewBack:callBackType andOtherInfo:otherInfo andMyInfo:myInfo];
    };
    _anchorOnlineView.myStream = _liveStream;
    [_anchorOnlineView showOnlineView];
}
-(void)anchorOnlineViewBack:(AnchorListType)callBackType andOtherInfo:(NSDictionary *)otherInfo andMyInfo:(NSDictionary *)myInfo {
    [self destroyAnchorOnlineList];
    switch (callBackType) {
        case AnchorListType_StartLink:{
           //发起主播-主播连麦
            [MBProgressHUD showPop:YZMsg(@"连麦请求已发送")];
            if (self.liveCtrEvent) {
                self.liveCtrEvent(@"控制-主播和主播连麦请求", @{@"otherInfo":otherInfo,@"myInfo":myInfo});
            }
        }break;
        default:{
        }break;
    }
}
-(void)destroyAnchorOnlineList {
    if (_anchorOnlineView) {
        [_anchorOnlineView removeFromSuperview];
        _anchorOnlineView = nil;
    }
}

/** socekt主播-主播连麦成功 */
-(void)socketLinkHostToHostSuc:(NSDictionary*)linkDic;{
    NSDictionary *subDic = @{@"userid":minstr([linkDic valueForKey:@"pkuid"]),
                             @"uname":minstr([linkDic valueForKey:@"uname"]),   //uname暂时未使用
                             @"playurl":minstr([linkDic valueForKey:@"pkpull"]),
                             @"pushurl":@"0",
    };
    [self createLinkMicView:subDic andHostToHost:YES];
    
    [self destroyAnchorOnlineList];
    _startPkBtn.hidden = NO;
}

/** socket主播-主播连麦挂断 */
-(void)socketlinkHostToHostDisconnect:(NSDictionary *)linkDic{
    //混流
    NSDictionary *mixDic = @{@"selfUrl":_liveHostUrl,@"otherUrl":@""};
    [_linkMicView linkMicMixStream:mixDic andHostToHost:_linkMicView.isHostToHost];
    
    [self destroyLinkMicView];
    _startPkBtn.hidden = YES;
    [self destroyPkView];
}

//主播-用户、主播-主播连麦公共
-(void)createLinkMicView:(NSDictionary *)linkDic andHostToHost:(BOOL)isHostToHost{
    [self destroyLinkMicView];
    _hostLinking = YES;
    _linkMicView = [YBTxLinkMicView createLinkMicViewOnSuper:self andHostToHost:isHostToHost];
    _linkMicView.linkDic = linkDic;
    [_linkMicView linkMicShowViewHaveCloseBtn:YES];
    YBWeakSelf;
    _linkMicView.linkMicEvent = ^(TxLinkEventType eventType, NSDictionary *eventDic) {
        [weakSelf linkMicViewEvent:eventType andDic:eventDic];
    };
    //混流
    NSDictionary *mixDic = @{@"selfUrl":_liveHostUrl,@"otherUrl":minstr([linkDic valueForKey:@"playurl"])};
    [_linkMicView linkMicMixStream:mixDic andHostToHost:isHostToHost];
    if (!isHostToHost) {
        //主播-用户连麦上报信息
        [_linkMicView linkMicUploadInfo:@{@"pull_url":minstr([linkDic valueForKey:@"playurl"]),@"touid":minstr([linkDic valueForKey:@"userid"])}];
    }
}
-(void)linkMicViewEvent:(TxLinkEventType)eventType andDic:(NSDictionary *)eventDic {
    switch (eventType) {
        case TxLinkEventType_LinkDisconnect:{
            //混流
            NSDictionary *mixDic = @{@"selfUrl":_liveHostUrl,@"otherUrl":@""};
            [_linkMicView linkMicMixStream:mixDic andHostToHost:_linkMicView.isHostToHost];
            _hostLinking = NO;
            //主播下麦用户 或者 主播下麦对方主播
            if (self.liveCtrEvent) {
                NSDictionary *passDic = @{@"uid":minstr([eventDic valueForKey:@"userid"]),
                                          @"uname":minstr([eventDic valueForKey:@"uname"]),
                                          @"hostToHost":@(_linkMicView.isHostToHost),};
                self.liveCtrEvent(@"控制-主播下麦他人",passDic);
            }
        }break;
        default:
            break;
    }
}
-(void)destroyLinkMicView {
    _hostLinking = NO;
    if (_linkMicView) {
        [_linkMicView linkMicViewDismiss];
        _linkMicView = nil;
    }
}
#pragma mark - 连麦结束

/** 主播-主播:对方主播id */
-(void)showToHostInfoWithId:(NSString *)toHostId;{
    [self destroyPkTohostInfo];
    _pkTohostInfoV = [YBAnchorLinkInfo showHostInfoWithSuperView:self];
    [_pkTohostInfoV reqToHostInfo:toHostId];
    YBWeakSelf;
    _pkTohostInfoV.attentEvent = ^(int isAttent) {
        weakSelf.pkFollowBtn.hidden = isAttent;
    };
    //遮罩【控制层级响应点击,实际UI显示在_pkTohostInfoV里的子视图】
    _pkFollowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _pkFollowBtn.backgroundColor = UIColor.clearColor;
    [_pkFollowBtn addTarget:self action:@selector(clickPkFollow) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:_pkFollowBtn];
    [_pkFollowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right);
        make.bottom.equalTo(_pkTohostInfoV.mas_bottom);
        make.width.height.mas_equalTo(40);
    }];
}
-(void)clickPkFollow {
    _pkFollowBtn.hidden = YES;
    [_pkTohostInfoV updateFollow];
}
-(void)destroyPkTohostInfo {
    if (_pkTohostInfoV) {
        [_pkTohostInfoV removeFromSuperview];
        _pkTohostInfoV = nil;
        [_pkFollowBtn removeFromSuperview];
        _pkFollowBtn = nil;
    }
}
#pragma mark - PK
-(void)resetPKBtnFrame {
    [_startPkBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(60);
        make.height.mas_equalTo(30);
        make.centerY.equalTo(_botBtnFunView);
        if (!_cartBtn.hidden) {
            make.right.equalTo(_cartBtn.mas_left).offset(-10);
        }else{
            make.right.equalTo(_moreBtn.mas_left).offset(-10);
        }
    }];
}
-(void)clikcStartPkBtn {
    [self cancelPKAlert];
    _startPkBtn.hidden = YES;
    _pkAlertView = [[YBAnchorPKAlert alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight+_window_width*2/6, _window_width, 70) andIsStart:YES];
    [[UIApplication sharedApplication].delegate.window addSubview:_pkAlertView];
    YBWeakSelf
    _pkAlertView.anchorPkEvent = ^(AnchorPkAlertType pkAlertType) {
        [weakSelf pkAlertCallBack:pkAlertType];
    };
    if (self.liveCtrEvent) {
        self.liveCtrEvent(@"控制-发起PK", @{});
    }
    [MBProgressHUD showError:YZMsg(@"PK请求已发送")];
}
-(void)pkAlertCallBack:(AnchorPkAlertType)pkAlertType {
    [self cancelPKAlert];
}
-(void)cancelPKAlert {
    if (_pkAlertView) {
        [_pkAlertView removeTimer];
        [_pkAlertView removeFromSuperview];
        _pkAlertView = nil;
    }
}
/** PK 开始 */
-(void)socketLinkPKStart:(NSDictionary *)pkDic;{
    [self destroyPkView];
    _pkView = [[YBAnchorPKView alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight, _window_width, _window_width*2/3+20) andTime:@"300"];
    YBWeakSelf;
    _pkView.pkViewEvent = ^{
        weakSelf.startPkBtn.hidden = NO;
        [weakSelf destroyPkView];
    };
    [self addSubview:_pkView];
    [self bringToFront];
    
}
///层级关系处理
-(void)bringToFront {
    [self bringSubviewToFront:_chatTool];
    [self bringSubviewToFront:_liveChatView];
    [self bringSubviewToFront:haohualiwuV];
    [self bringSubviewToFront:_paintedShowRegion];
    [self bringSubviewToFront:liansongliwubottomview];
    [self bringSubviewToFront:_userAnimation];
}
/** PK 结果 */
-(void)socketLinkPKReult:(NSDictionary *)pkDic{
    int win;
    if ([minstr([pkDic valueForKey:@"win_uid"]) isEqual:@"0"]) {
        win = 0;
    }else if ([minstr([pkDic valueForKey:@"win_uid"]) isEqual:[Config getOwnID]]) {
        win = 1;
    }else{
        win = 2;
    }
    [_pkView showPkResult:pkDic andWin:win];
}
/** PK 进度 */
-(void)socketLinkPKProgress:(NSDictionary *)pkDic;{
    NSString *blueNum;
    NSString *redNum;
    CGFloat progress = 0.0;
    if ([minstr([pkDic valueForKey:@"pkuid1"]) isEqual:[Config getOwnID]]) {
        blueNum = minstr([pkDic valueForKey:@"pktotal1"]);
        redNum = minstr([pkDic valueForKey:@"pktotal2"]);
    }else{
        redNum = minstr([pkDic valueForKey:@"pktotal1"]);
        blueNum = minstr([pkDic valueForKey:@"pktotal2"]);
    }
    if ([blueNum isEqual:@"0"]) {
        progress = 0.2;
    }else if ([redNum isEqual:@"0"]) {
        progress = 0.8;
    }else{
        CGFloat ppp = [blueNum floatValue]/([blueNum floatValue] + [redNum floatValue]);
        if (ppp < 0.2) {
            progress = 0.2;
        }else if (ppp > 0.8){
            progress = 0.8;
        }else{
            progress = ppp;
        }
    }
    [_pkView updateProgress:progress withBlueNum:blueNum withRedNum:redNum];
}

-(void)destroyPkView {
    if (_pkView) {
        [_pkView removeTimer];
        [_pkView removeFromSuperview];
        _pkView = nil;
    }
}
#pragma mark - /** 键盘弹起事件: 目前只处理 PK和连麦 的界面相对位置不变*/
-(void)keyBoardNoticeIsShow:(BOOL)isShow andHeight:(CGFloat)height;{
    _pkView.top = 130+statusbarHeight + height;
    [_linkMicView keyBoardNoticeIsShow:isShow andHeight:height];
}
#pragma mark - 功能按钮回调
-(void)funViewCallBack:(NSString *)eventType {
    if ([eventType isEqual:@"直播功能-取消"]) {
        _moreBtn.selected = NO;
    }
    if ([eventType isEqual:@"直播功能-美颜"] && self.liveCtrEvent) {
        self.liveCtrEvent(@"控制-美颜", @{});
    }
    if ([eventType isEqual:@"直播功能-翻转"] && self.liveCtrEvent) {
         self.liveCtrEvent(@"控制-翻转", @{});
    }
    if ([eventType isEqual:@"直播功能-闪光灯"] && self.liveCtrEvent) {
         self.liveCtrEvent(@"控制-闪光灯", @{});
    }
    if ([eventType isEqual:@"直播功能-分享"]) {
//         self.liveCtrEvent(@"控制-分享", @{});
        NSDictionary *shareDic = @{@"hostName":[Config getOwnNicename],@"thumb_s":[Config getUserAvatarThumb],@"hostId":[Config getOwnID],@"liveTitle":_liveTitle};
        [YBShareView showShareWithType:RKShareType_Liveing parameter:shareDic commplete:^(int codeEvent, NSString *nums) {
            
        }];
    }
    if ([eventType isEqual:@"直播功能-连麦"]) {
        if (_hostLinking) {
            [MBProgressHUD showError:YZMsg(@"当前正在进行连麦")];
            return;
        }
        [self showAnchorOnlineList];
    }
}
#pragma mark - 聊天工具栏回调
-(void)chatToolCallBack:(NSString *)eventType andContent:(NSString *)eventContent{
    //去发送socke；
    if ([eventType isEqual:@"直播聊天-发送"] && self.liveCtrEvent) {
        self.liveCtrEvent(@"控制-直播聊天", @{@"ct":eventContent});
    }
}
#pragma mark - 公屏聊天处点击回调
-(void)roomChatCallBack:(NSString *)eventType andEventInfo:(NSDictionary *)eventDic {
    
    if ([eventType isEqual:@"房间聊天-用户信息"] && self.liveCtrEvent) {
        [_chatTool.chatTF resignFirstResponder];
        //@{@"id":model.userID,@"name":model.userName}
        self.liveCtrEvent(@"控制-信息卡", eventDic);
    }
}
#pragma mark - 用户列表回调
-(void)userListCallBack:(NSString *)eventType andEventInfo:(NSDictionary *)eventDic{
    if ([eventType isEqual:@"用户列表-用户信息"] && self.liveCtrEvent) {
        [_chatTool.chatTF resignFirstResponder];
        //@{@"id":model.userID,@"name":model.userName}
        self.liveCtrEvent(@"控制-信息卡", eventDic);
    }
}

#pragma mark - 开播动画
-(void)startLiveAnimation {
    YBWeakSelf;
    _pubTiems = [PublicView showTimerView:^(int eventCode) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf timeEnd];
        });
    }];
}
-(void)timeEnd {
    if (self.liveCtrEvent) {
        self.liveCtrEvent(@"控制-动画结束", @{});
    }
    if (_pubTiems) {
        [_pubTiems removeFromSuperview];
        _pubTiems = nil;
    }
}
#pragma mark - 房间更新映票
-(void)createroomUpdateInfo:(NSDictionary *)roomDic; {
    
    _hostVotesBtn.hidden = NO;
    NSString *votesStr = [NSString stringWithFormat:@"%@ %@",[common name_votes],[roomDic valueForKey:@"votestotal"]];
    [_hostVotesBtn setTitle:votesStr forState:0];
    
    [self updateGuardNums:minstr([roomDic valueForKey:@"guard_nums"])];
}
#pragma mark - 更新直播时长
-(void)updateLiveTime {
    _liveTime ++;
    NSString *timeFormat = @"00:00";
    if (_liveTime < 3600) {
        timeFormat = [NSString stringWithFormat:@"%02d:%02d",_liveTime/60,_liveTime%60];
    }else{
        timeFormat = [NSString stringWithFormat:@"%02d:%02d:%02d",_liveTime/3600,(_liveTime%3600)/60,(_liveTime%3600)%60];
    }
    [_hostLiveTimeBtn setTitle:timeFormat forState:0];
}
#pragma mark - 用户点亮
-(void)socketLightAnimation {
    [_botBtnFunView layoutIfNeeded];
    
    starX = CGRectGetMaxX(_botBtnFunView.frame)-20 ;
    starY = _botBtnFunView.frame.origin.y;
    starImage = [[UIImageView alloc]initWithFrame:CGRectMake(starX, starY, 30, 30)];
    starImage.contentMode = UIViewContentModeScaleAspectFit;
    NSMutableArray *array = [NSMutableArray arrayWithObjects:@"plane_heart_cyan.png",@"plane_heart_pink.png",@"plane_heart_red.png",@"plane_heart_yellow.png",@"plane_heart_heart.png", nil];
    NSInteger random = arc4random()%array.count;
    starImage.image = [UIImage imageNamed:[array objectAtIndex:random]];
    [UIView animateWithDuration:0.2 animations:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            starImage.alpha = 1.0;
            starImage.frame = CGRectMake(starX+random - 10, starY-random - 30, 30, 30);
            CGAffineTransform transfrom = CGAffineTransformMakeScale(1.3, 1.3);
            starImage.transform = CGAffineTransformScale(transfrom, 1, 1);
        });
    }];
    [self addSubview:starImage];
    [self sendSubviewToBack:starImage];
    CGFloat finishX = _window_width - round(arc4random() % 200);
    //  动画结束点的Y值
    CGFloat finishY = 200;
    //  imageView在运动过程中的缩放比例
    CGFloat scale = round(arc4random() % 2) + 0.7;
    // 生成一个作为速度参数的随机数
    CGFloat speed = 1 / round(arc4random() % 900) + 0.6;
    //  动画执行时间
    NSTimeInterval duration = 4 * speed;
    //  如果得到的时间是无穷大，就重新附一个值（这里要特别注意，请看下面的特别提醒）
    if (duration == INFINITY) duration = 2.412346;
    //  开始动画
    [UIView beginAnimations:nil context:(__bridge void *_Nullable)(starImage)];
    //  设置动画时间
    [UIView setAnimationDuration:duration];
    
    //  设置imageView的结束frame
    starImage.frame = CGRectMake( finishX, finishY, 30 * scale, 30 * scale);
    
    //  设置渐渐消失的效果，这里的时间最好和动画时间一致
    [UIView animateWithDuration:duration animations:^{
        starImage.alpha = 0;
    }];
    
    //  结束动画，调用onAnimationComplete:finished:context:函数
    [UIView setAnimationDidStopSelector:@selector(onAnimationComplete:finished:context:)];
    //  设置动画代理
    [UIView setAnimationDelegate:self];
    [UIView commitAnimations];
    
}
/// 动画完后销毁iamgeView
- (void)onAnimationComplete:(NSString *)animationID finished:(NSNumber *)finished context:(void *)context{
    UIImageView *imageViewsss = (__bridge UIImageView *)(context);
    [imageViewsss removeFromSuperview];
    imageViewsss = nil;
}
#pragma mark - 发言
-(void)socketChatDic:(NSDictionary *)chatDic {
    [_liveChatView reloadMsg:chatDic];
}
#pragma mark - 用户离开
-(void)socketUserLeave:(NSDictionary *)leaveDic {
    NSLog(@"leave");
    [_userListView userEventOfType:UserEvent_Leave andInfo:leaveDic];
}
#pragma mark - 用户进入
-(void)socketUserEnter:(NSDictionary *)enterDic {
    NSLog(@"enter");
    [_userListView userEventOfType:UserEvent_Enter andInfo:enterDic];
    NSString *vipType = [NSString stringWithFormat:@"%@",[[enterDic valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *guardType = [NSString stringWithFormat:@"%@",[[enterDic valueForKey:@"ct"] valueForKey:@"guard_type"]];
    //vip暂时没有动画，只有守护有
    if ([vipType isEqual:@"1"] || [guardType isEqual:@"1"] || [guardType isEqual:@"2"]) {
        //进场动画
        [_userAnimation addUserMove:enterDic];
    }
    NSDictionary *ctDic = [enterDic valueForKey:@"ct"];
    NSDictionary *chatDic = @{@"userName":minstr([ctDic valueForKey:@"user_nickname"]),
                              @"contentChat":YZMsg(@" 进入了直播间"),
                              @"id":minstr([ctDic valueForKey:@"id"]),
                              @"titleColor":@"userLogin",
                              @"usertype":minstr([ctDic valueForKey:@"usertype"]),
                              @"guard_type":minstr([ctDic valueForKey:@"guard_type"]),
    };
    [self socketChatDic:chatDic];
    
}
#pragma mark - 送礼物
-(void)socketSendGift:(NSDictionary *)giftDic {
    NSDictionary *ctDic = [giftDic valueForKey:@"ct"];
    NSMutableDictionary *showDic = @{@"avatar":minstr([giftDic valueForKey:@"uhead"]),
                                     @"nickname":minstr([giftDic valueForKey:@"uname"]),
                                     @"paintedHeight":[giftDic valueForKey:@"paintedHeight"]?[giftDic valueForKey:@"paintedHeight"]:@"",
                                     @"paintedPath":[giftDic valueForKey:@"paintedPath"]?[giftDic valueForKey:@"paintedPath"]:@[],//数组
                                     @"paintedWidth":[giftDic valueForKey:@"paintedWidth"]?[giftDic valueForKey:@"paintedWidth"]:@"",
    }.mutableCopy;
    [showDic addEntriesFromDictionary:ctDic];
    [self showGift:showDic];
    
    NSString *votes = minstr([ctDic valueForKey:@"votestotal"]);
    [self updateVotes:votes];
    
}
#pragma mark - 更新映票 
-(void)updateVotes:(NSString *)votes {
    NSString *votesStr = [NSString stringWithFormat:@"%@ %@",[common name_votes],votes];
    [_hostVotesBtn setTitle:votesStr forState:0];
}
#pragma mark - 更新守护数量
-(void)updateGuardNums:(NSString *)nums {
    if ([nums intValue] <= 0) {
        return;
    }
    NSString *guardStr = [NSString stringWithFormat:YZMsg(@"守护 %@人"),nums];
    [_hostGuardBtn setTitle:guardStr forState:0];
    [self layoutSubviews];
}
#pragma mark - /** 计时器刷新列表 */
-(void)timerReloadList {
    [_userListView timerReloadList];
}
#pragma mark - /** 私信未读数 */
-(void)updateUnread:(int)unread {
    _redPoint.hidden = YES;
    if (unread > 0) {
        _redPoint.hidden = NO;
    }
}
#pragma mark - 打赏开始
-(void)showGift:(NSDictionary *)giftDic {
    NSString *type = minstr([giftDic valueForKey:@"type"]);
    if (!continueGifts) {
        continueGifts = [[continueGift alloc]initWithFrame:CGRectMake(0, 0, liansongliwubottomview.width, liansongliwubottomview.height)];
        [liansongliwubottomview addSubview:continueGifts];
        //初始化礼物空位
        [continueGifts initGift];
        YBWeakSelf;
        continueGifts.rkPaintedEvent = ^(NSDictionary *giftDic) {
            [weakSelf showPaintedGift:giftDic];
        };
    }
    if ([type isEqual:@"1"]) {
        [self expensiveGift:giftDic];
    }else{
        //手绘以及普通礼物都进 continueGifts 由它执行队列
        [continueGifts GiftPopView:giftDic andLianSong:@"Y"];
    }
}

-(void)showPaintedGift:(NSDictionary *)giftDic {
    //手绘显示动画
    _paintedShowRegion.giftPathStr = minstr([giftDic valueForKey:@"gifticon"]);
    _paintedShowRegion.paintedWidth = [minstr([giftDic valueForKey:@"paintedWidth"]) floatValue];
    _paintedShowRegion.paintedHeight = [minstr([giftDic valueForKey:@"paintedHeight"]) floatValue];
    _paintedShowRegion.paintedPointArray = [NSArray arrayWithArray:[giftDic valueForKey:@"paintedPath"]];
}

/************ 礼物弹出及队列显示开始 *************/
-(void)expensiveGiftdelegate:(NSDictionary *)giftData{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]init];
        haohualiwuV.delegate = self;
        [self addSubview:haohualiwuV];
    }
    if (giftData == nil) {
        
    }else {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive];
    }
}
-(void)expensiveGift:(NSDictionary *)giftData{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]init];
        haohualiwuV.delegate = self;
        [self addSubview:haohualiwuV];
    }
    if (giftData == nil) {
     
    }else {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive];
    }
}
#pragma mark - 销毁子视图
-(void)destroyCtrSubView {
    if (continueGifts) {
        [continueGifts stopTimerAndArray];
        [continueGifts initGift];
        [continueGifts removeFromSuperview];
        continueGifts = nil;
    }
    if (haohualiwuV) {
        [haohualiwuV stopHaoHUaLiwu];
        [haohualiwuV removeFromSuperview];
        haohualiwuV.expensiveGiftCount = nil;
        haohualiwuV = nil;
    }
    liansongliwubottomview.top = statusbarHeight+200;
    if (_onsaleView) {
        [_onsaleView removeFromSuperview];
    }
    if (_paintedShowRegion) {
        [_paintedShowRegion destroyPaitend];
    }
    [self desTroyDaytaskView];
    [self removeShouhuView];
    [self destroyLinkMicView];
    [self destroyAnchorOnlineList];
    [self cancelPKAlert];
    [self destroyPkView];
}

#pragma mark - 每日任务
/** 点击图片回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index {
    NSLog(@"点击轮播----------index:%ld",index);
    if (index == 0) {
        //每日任务
        [self desTroyDaytaskView];
        _dayTaskView = [YBDayTaskView showRoomDayTask];
        _dayTaskView.liveuid = minstr(_liveUid);
        [_dayTaskView requestListData];
    }
}

/** 图片滚动回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didScrollToIndex:(NSInteger)index {
    self.roomGamePage.currentPage = index;
}
#pragma mark - 守护
-(void)clickGuardBtn {
    gShowView = [[guardShowView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andUserGuardMsg:nil andLiveUid:[Config getOwnID]];
    gShowView.delegate = self;
    [self addSubview:gShowView];
    [gShowView show];
}
- (void)removeShouhuView{
    if (gShowView) {
        [gShowView removeFromSuperview];
        gShowView = nil;
    }
}
- (void)socketUpdateGuardMsg:(NSDictionary *)dic{
    
    [self updateVotes:minstr([dic valueForKey:@"votestotal"])];
    [self timerReloadList];
    
    [self updateGuardNums:minstr([dic valueForKey:@"guard_nums"])];
    
}

#pragma mark - set/get
- (UIView *)hostInfoView {
    if (!_hostInfoView) {
        _hostInfoView = [[UIView alloc]init];
        _hostInfoView.layer.cornerRadius = 17;
        _hostInfoView.layer.masksToBounds = YES;
        _hostInfoView.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        
        _hostAvatarBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_hostAvatarBtn sd_setImageWithURL:[NSURL URLWithString:[Config getUserAvatar]] forState:0];
        _hostAvatarBtn.layer.masksToBounds = YES;
        _hostAvatarBtn.layer.cornerRadius = 15;
        [_hostAvatarBtn.imageView setContentMode:UIViewContentModeScaleAspectFill];
        _hostAvatarBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
        _hostAvatarBtn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
        [_hostAvatarBtn addTarget:self action:@selector(clickHostAvatarBtn) forControlEvents:UIControlEventTouchUpInside];
        [_hostInfoView addSubview:_hostAvatarBtn];
        [_hostAvatarBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(_hostInfoView);
            make.left.equalTo(_hostInfoView).offset(2);
            make.width.height.mas_equalTo(30);
        }];
        
        _hostNameL = [[UILabel alloc]init];
        _hostNameL.font = SYS_Font(13);
        _hostNameL.textColor = RGB_COLOR(@"#ffffff", 1);
        _hostNameL.text = minstr([Config getOwnNicename]);
        [_hostInfoView addSubview:_hostNameL];
        [_hostNameL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(_hostAvatarBtn.mas_centerY).multipliedBy(0.65);
            make.left.equalTo(_hostAvatarBtn.mas_right).offset(5);
        }];
        
        _hostIDL = [[UILabel alloc]init];
        _hostIDL.font = SYS_Font(10);
        _hostIDL.text = [NSString stringWithFormat:@"ID:%@",[Config getOwnID]];
        _hostIDL.textColor = RGB_COLOR(@"#f0f0f0", 1);
        [_hostInfoView addSubview:_hostIDL];
        [_hostIDL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(_hostNameL);
            make.centerY.equalTo(_hostAvatarBtn.mas_centerY).multipliedBy(1.53);
        }];
    }
    return _hostInfoView;
}
- (UIButton *)hostVotesBtn {
    if (!_hostVotesBtn) {
        _hostVotesBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        //无点击事件
        [_hostVotesBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        _hostVotesBtn.titleLabel.font = SYS_Font(10);
        _hostVotesBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 11, 0, 11);
        _hostVotesBtn.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        _hostVotesBtn.layer.masksToBounds = YES;
        _hostVotesBtn.layer.cornerRadius = 11;
        _hostVotesBtn.hidden = YES;
    }
    return _hostVotesBtn;
}
- (UIButton *)hostGuardBtn {
    if (!_hostGuardBtn) {
        _hostGuardBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _hostGuardBtn.titleLabel.font = SYS_Font(10);
        _hostGuardBtn.layer.masksToBounds = YES;
        _hostGuardBtn.layer.cornerRadius = 11;
        [_hostGuardBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        _hostGuardBtn.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        [_hostGuardBtn setTitle:YZMsg(@"守护 虚位以待") forState:0];
        [_hostGuardBtn setImage:[UIImage imageNamed:@"pub_white_right"] forState:0];
        [_hostGuardBtn addTarget:self action:@selector(clickGuardBtn) forControlEvents:UIControlEventTouchUpInside];
        _hostGuardBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 8, 0, 5);
    }
    return _hostGuardBtn;;
}
- (UIButton *)hostLiveTimeBtn {
    if (!_hostLiveTimeBtn) {
        _hostLiveTimeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        //无点击事件
        [_hostLiveTimeBtn setTitleColor:RGB_COLOR(@"#f0f0f0", 1) forState:0];
        _hostLiveTimeBtn.titleLabel.font = SYS_Font(10);
        _hostLiveTimeBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 14, 0, 11);
        _hostLiveTimeBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -5, 0, 0);
        _hostLiveTimeBtn.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        _hostLiveTimeBtn.layer.masksToBounds = YES;
        _hostLiveTimeBtn.layer.cornerRadius = 11;
        UIImage *pointImg = [PublicObj getImgWithColor:Pink_Cor withSize:CGSizeMake(5, 5)];
        [_hostLiveTimeBtn setImage:[pointImg rk_circleImage] forState:0];
        [_hostLiveTimeBtn setTitle:@"00:00" forState:0];
    }
    return _hostLiveTimeBtn;
}
- (SDCycleScrollView *)roomGameCycle {
    if (!_roomGameCycle) {
        
        _roomGameCycle = [[SDCycleScrollView alloc]init];
        _roomGameCycle.backgroundColor = [UIColor clearColor];
        _roomGameCycle.bannerImageViewContentMode = UIViewContentModeScaleAspectFit;
        _roomGameCycle.delegate = self;
        _roomGameCycle.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
        _roomGameCycle.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        _roomGameCycle.autoScrollTimeInterval = 3.0;
        _roomGameCycle.currentPageDotColor = [UIColor whiteColor];
        _roomGameCycle.pageDotColor = [[UIColor whiteColor] colorWithAlphaComponent:0.4];
        _roomGameCycle.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
        _roomGameCycle.imageURLStringsGroup = _roomGameArray;
        
    }
    return _roomGameCycle;
}
- (YBPageControl *)roomGamePage {
    if (!_roomGamePage) {
        _roomGamePage = [[YBPageControl alloc]init];
        _roomGamePage.numberOfPages = _roomGameArray.count;
        _roomGamePage.currentPage = 0;
        _roomGamePage.pageIndicatorTintColor = [UIColor grayColor];
        _roomGamePage.currentPageIndicatorTintColor = [UIColor whiteColor];
//        _roomGamePage.transform = CGAffineTransformScale(CGAffineTransformIdentity, 0.7, 0.7);
        _roomGamePage.userInteractionEnabled = NO;
    }
    return _roomGamePage;
}

- (YBUserListView *)userListView {
    if (!_userListView) {
        YBWeakSelf;
        _userListView = [[YBUserListView alloc]init];
        _userListView.backgroundColor = UIColor.clearColor;
        if (![PublicObj checkNull:_liveUid] && ![PublicObj checkNull:_liveStream]) {
            _userListView.liveUid = minstr(_liveUid);
            _userListView.liveStream = minstr(_liveStream);
        }
        _userListView.listEvent = ^(NSString *eventStr, NSDictionary *eventDic) {
            [weakSelf userListCallBack:eventStr andEventInfo:eventDic];
        };
    }
    return _userListView;
}
- (void)setLiveUid:(NSString *)liveUid {
    _liveUid = liveUid;
    if (_userListView) {
        _userListView.liveUid = minstr(_liveUid);
    }
}
-(void)setLiveStream:(NSString *)liveStream {
    _liveStream = liveStream;
    if (_userListView) {
        _userListView.liveStream = minstr(_liveStream);
    }
}
- (YBLiveChatView *)liveChatView {
    if (!_liveChatView) {
        YBWeakSelf;
        _liveChatView = [[YBLiveChatView alloc]initWithFrame:CGRectMake(10,_window_height-ChatViewHeight-60-ShowDiff, ChatViewWidth, ChatViewHeight)];
        _liveChatView.liveChatEvent = ^(NSString *eventStr, NSDictionary *eventDic) {
            [weakSelf roomChatCallBack:eventStr andEventInfo:eventDic];
        };
    }
    return _liveChatView;
}
- (YBUserEnterAnimation *)userAnimation {
    if (!_userAnimation) {
        _userAnimation = [[YBUserEnterAnimation alloc]init];
    }
    return _userAnimation;
}
- (UIButton *)linkCtrBtn {
    if (!_linkCtrBtn) {
        _linkCtrBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_linkCtrBtn addTarget:self action:@selector(clickLinkCtrBtn:) forControlEvents:UIControlEventTouchUpInside];
        [_linkCtrBtn setTitleColor:RGB_COLOR(@"#dddddd", 1) forState:0];
        [_linkCtrBtn setTitleColor:RGB_COLOR(@"#dddddd", 1) forState:UIControlStateSelected];
        [_linkCtrBtn setTitle:YZMsg(@"连麦互动") forState:0];
        [_linkCtrBtn setTitle:YZMsg(@"禁止连麦") forState:UIControlStateSelected];
        _linkCtrBtn.titleLabel.font = SYS_Font(8);
        [_linkCtrBtn setImage:[UIImage imageNamed:@"直播-连控彩"] forState:0];
        [_linkCtrBtn setImage:[UIImage imageNamed:@"直播-连控灰"] forState:UIControlStateSelected];
        _linkCtrBtn.layer.cornerRadius = 8;
        _linkCtrBtn.layer.masksToBounds = YES;
        _linkCtrBtn.backgroundColor = RGB_COLOR(@"#000000", 0.4);
    }
    return _linkCtrBtn;
}
- (UIView *)botBtnFunView {
    if (!_botBtnFunView) {
        _botBtnFunView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-50-ShowDiff, _window_width, 40)];
        CGFloat btnW = 30;
        
        UIButton *keyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [keyBtn addTarget:self action:@selector(clickKeyBtn) forControlEvents:UIControlEventTouchUpInside];
        [keyBtn setImage:[UIImage imageNamed:@"live_聊天"] forState:0];
        [_botBtnFunView addSubview:keyBtn];
        [keyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.left.equalTo(_botBtnFunView.mas_left).offset(10);
        }];
        
        _closeLiveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_closeLiveBtn addTarget:self action:@selector(clikcCloseLiveBtn) forControlEvents:UIControlEventTouchUpInside];
        [_closeLiveBtn setImage:[UIImage imageNamed:@"live_关闭"] forState:0];
        [_botBtnFunView addSubview:_closeLiveBtn];
        [_closeLiveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_botBtnFunView.mas_right).offset(-10);
        }];
        
        _msgBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_msgBtn addTarget:self action:@selector(clikcMsgBtn) forControlEvents:UIControlEventTouchUpInside];
        [_msgBtn setImage:[UIImage imageNamed:@"live_私信"] forState:0];
        [_botBtnFunView addSubview:_msgBtn];
        [_msgBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_closeLiveBtn.mas_left).offset(-10);
        }];
        
        _redPoint = [[UILabel alloc]init];
        _redPoint.layer.masksToBounds = YES;
        _redPoint.layer.cornerRadius = 4;
        _redPoint.backgroundColor = UIColor.redColor;
        [_botBtnFunView addSubview:_redPoint];
        _redPoint.hidden = YES;
        [_redPoint mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(8);
            make.centerY.equalTo(_msgBtn.mas_top);
            make.right.equalTo(_msgBtn.mas_right).offset(-4);
        }];
        
        _moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_moreBtn addTarget:self action:@selector(clikcMoreBtn) forControlEvents:UIControlEventTouchUpInside];
        [_moreBtn setImage:[UIImage imageNamed:@"功能"] forState:0];
        [_moreBtn setImage:[UIImage imageNamed:@"功能_S"] forState:UIControlStateSelected];
        [_botBtnFunView addSubview:_moreBtn];
        [_moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_msgBtn.mas_left).offset(-10);
        }];
        
        _cartBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cartBtn addTarget:self action:@selector(clikcCartBtn) forControlEvents:UIControlEventTouchUpInside];
        [_cartBtn setImage:[UIImage imageNamed:@"live_店铺"] forState:0];
        [_botBtnFunView addSubview:_cartBtn];
        _cartBtn.hidden = YES;
        [_cartBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_moreBtn.mas_left).offset(-10);
        }];
        _startPkBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_startPkBtn addTarget:self action:@selector(clikcStartPkBtn) forControlEvents:UIControlEventTouchUpInside];
        [_startPkBtn setImage:[UIImage imageNamed:getImagename(@"发起pk")] forState:0];
        [_botBtnFunView addSubview:_startPkBtn];
        _startPkBtn.hidden = YES;
        
    }
    return _botBtnFunView;
}

- (YBChatToolBar *)chatTool {
    if (!_chatTool) {
        YBWeakSelf;
        _chatTool = [[YBChatToolBar alloc]initWithFrame:CGRectMake(0, _window_height-44, _window_width, 44)];
        _chatTool.hidden = YES;
        _chatTool.chatToolEvnt = ^(NSString *eventType, NSString *eventContent) {
            [weakSelf chatToolCallBack:eventType andContent:eventContent];
        };
    }
    return _chatTool;
}
-(void)tapGes {
    if ([_chatTool.chatTF isFirstResponder]) {
        [_chatTool.chatTF resignFirstResponder];
    }
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch;{
    if ([touch.view isDescendantOfView:_botBtnFunView]
        ||[touch.view isDescendantOfView:_liveChatView]
        ||[touch.view isDescendantOfView:_userListView]
        ||[touch.view isDescendantOfView:_hostInfoView]
        ||[touch.view isDescendantOfView:_chatTool]
        ||[touch.view isDescendantOfView:_roomGameCycle]) {
        return NO;
    }
    return YES;
}
- (RKShowPaintedView *)paintedShowRegion {
    if (!_paintedShowRegion) {
        _paintedShowRegion = [[RKShowPaintedView alloc]init];
        //_paintedShowRegion.backgroundColor = UIColor.redColor;
    }
    return _paintedShowRegion;
}
-(void)desTroyDaytaskView {
    if (_dayTaskView) {
        [_dayTaskView dismissView];
        _dayTaskView = nil;
    }
}
@end
