//
//  YBLivePreview.h
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void (^LivePreBlock)(NSString *eventType,NSString*eventContent);

@interface YBLivePreview : UIView

@property(nonatomic,copy)LivePreBlock livePreEvent;
@property(nonatomic,strong)NSString *inputTitleStr;
@property(nonatomic,assign)BOOL locationSwitch;
@property(nonatomic,assign)BOOL cartOpen;
@property(nonatomic,strong)UIImage *thumbImage;

@end


