//
//  YBLivePreview.m
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLivePreview.h"
#import "ZFModalTransitionAnimator.h"
#import "startLiveClassVC.h"
@interface YBLivePreview()<UITextViewDelegate,TZImagePickerControllerDelegate,UIImagePickerControllerDelegate,UINavigationControllerDelegate> {
    UIImageView *loactionImgView;
    UILabel *locationLabel;
    
    UIButton *preThumbBtn;                  //上传封面按钮
    UILabel *thumbLabel;                    //上传封面状态的label
    
    UITextView *liveTitleTextView;
    UILabel *textPlaceholdLabel;
    NSMutableArray *preShareBtnArray;       //分享按钮数组
    NSString *selectShareName;              //选择分享的名称
    
    UILabel *liveClassLabel;
    NSString *liveClassID;

}
@property (nonatomic, strong) ZFModalTransitionAnimator *animator;

@end

@implementation YBLivePreview

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        liveClassID = @"-99999999";

        [self createUI];
    }
    return self;
}
-(void)createUI {
    _inputTitleStr = @"";
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hidePreTextView)];
    [self addGestureRecognizer:tap];
    
    loactionImgView = [[UIImageView alloc]initWithFrame:CGRectMake(10, 42+statusbarHeight, 22, 22)];
    loactionImgView.image = [UIImage imageNamed:@"pre_location"];
    [self addSubview:loactionImgView];
    UIView *locationLabelView = [[UIView alloc]init];
    locationLabelView.backgroundColor = RGB_COLOR(@"#000000", 0.3);
    locationLabelView.layer.cornerRadius = 8;
    locationLabelView.layer.masksToBounds = YES;
    [self addSubview:locationLabelView];
    [locationLabelView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(loactionImgView);
        make.left.equalTo(loactionImgView.mas_right).offset(-8);
        make.height.mas_equalTo(22);
    }];
    
    locationLabel = [[UILabel alloc]init];
    locationLabel.font = [UIFont systemFontOfSize:11];
    locationLabel.textColor = [UIColor whiteColor];
    locationLabel.text = [XGGcityDefault getLocationCity];
    [locationLabelView addSubview:locationLabel];
    [locationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(locationLabelView);
        make.height.mas_equalTo(22);
        make.left.equalTo(locationLabelView).offset(10);
        make.right.equalTo(locationLabelView).offset(-10);
    }];
    [self insertSubview:locationLabelView belowSubview:loactionImgView];
    UIButton *locationSwitchBtn = [UIButton buttonWithType:0];
    [locationSwitchBtn addTarget:self action:@selector(locationSwitchBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:locationSwitchBtn];
    [locationSwitchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.height.equalTo(loactionImgView);
        make.right.equalTo(locationLabelView);
    }];
    //定位不展示做隐藏处理
    loactionImgView.hidden = locationLabelView.hidden = locationSwitchBtn.hidden = YES;
    
    UIButton *switchBtn = [UIButton buttonWithType:0];
    [switchBtn setImage:[UIImage imageNamed:@"pre_camer"] forState:0];
    [switchBtn addTarget:self action:@selector(rotateCamera) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:switchBtn];
    [switchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        //make.left.equalTo(locationLabelView.mas_right).offset(10);
        make.left.equalTo(self.mas_left).offset(10);
        make.centerY.equalTo(locationSwitchBtn);
        make.height.width.equalTo(loactionImgView.mas_height);
    }];
    _locationSwitch = YES;
    
    
    UIButton *preCloseBtn = [UIButton buttonWithType:0];
    preCloseBtn.frame = CGRectMake(_window_width-30, loactionImgView.top, loactionImgView.height, loactionImgView.height);
    [preCloseBtn setImage:[UIImage imageNamed:@"live_close"] forState:0];
    [preCloseBtn addTarget:self action:@selector(doClosePreView) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:preCloseBtn];
    
    
    UIView *preMiddleView = [[UIView alloc]initWithFrame:CGRectMake(10, _window_height/2-(_window_width-20)*38/71, _window_width-20, (_window_width-20)*38/71)];
    preMiddleView.backgroundColor = RGB_COLOR(@"#0000000", 0.3);
    preMiddleView.layer.cornerRadius = 5;
    [self addSubview:preMiddleView];
    
    preThumbBtn = [UIButton buttonWithType:0];
    preThumbBtn.frame = CGRectMake(10, preMiddleView.height*2/19, preMiddleView.height*10/19, preMiddleView.height*10/19);
    [preThumbBtn setImage:[UIImage imageNamed:@"pre_uploadThumb"] forState:0];
    [preThumbBtn addTarget:self action:@selector(doUploadPicture) forControlEvents:UIControlEventTouchUpInside];
    preThumbBtn.layer.cornerRadius = 5.0;
    preThumbBtn.layer.masksToBounds = YES;
    [preMiddleView addSubview:preThumbBtn];
    thumbLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, preThumbBtn.height*0.75, preThumbBtn.width, preThumbBtn.height/4)];
    thumbLabel.textColor = RGB_COLOR(@"#c8c8c8", 1);
    thumbLabel.textAlignment = NSTextAlignmentCenter;
    thumbLabel.text = YZMsg(@"直播封面");
    thumbLabel.font = [UIFont systemFontOfSize:13];
    [preThumbBtn addSubview:thumbLabel];
   
    UIButton *liveClassBtn = [UIButton buttonWithType:0];
    liveClassBtn.frame = CGRectMake(preMiddleView.width-80, 0, 70, 30);
    [liveClassBtn addTarget:self action:@selector(showAllClassView) forControlEvents:UIControlEventTouchUpInside];
    [preMiddleView addSubview:liveClassBtn];
    liveClassLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, 55, 30)];
    liveClassLabel.text = YZMsg(@"频道");
    liveClassLabel.textAlignment = NSTextAlignmentRight;
    liveClassLabel.textColor = [UIColor whiteColor];
    liveClassLabel.font = [UIFont systemFontOfSize:14];
    [liveClassBtn addSubview:liveClassLabel];
    
    UIImageView *rightImgView = [[UIImageView alloc]initWithFrame:CGRectMake(liveClassLabel.right+1, 8, 14, 14)];
    rightImgView.image = [UIImage imageNamed:@"pre_right"];
    rightImgView.userInteractionEnabled = YES;
    [liveClassBtn addSubview:rightImgView];

    
    UILabel *preTitlelabel = [[UILabel alloc]initWithFrame:CGRectMake(preThumbBtn.right+10, preThumbBtn.top, 100, preThumbBtn.height/4)];
    preTitlelabel.font = [UIFont systemFontOfSize:13];
    preTitlelabel.textColor = RGB_COLOR(@"#c7c7c7", 1);
    preTitlelabel.text = YZMsg(@"直播标题");
    [preMiddleView addSubview:preTitlelabel];
    liveTitleTextView = [[UITextView alloc]initWithFrame:CGRectMake(preTitlelabel.left, preTitlelabel.bottom, preMiddleView.width-10-preThumbBtn.right, preThumbBtn.height*0.75)];
    liveTitleTextView.delegate = self;
    liveTitleTextView.font = [UIFont systemFontOfSize:20];
    liveTitleTextView.textColor = [UIColor whiteColor];
    liveTitleTextView.backgroundColor = [UIColor clearColor];
    [preMiddleView addSubview:liveTitleTextView];
    textPlaceholdLabel = [[UILabel alloc]initWithFrame:CGRectMake(3, 10, liveTitleTextView.width, 22)];
    textPlaceholdLabel.font = [UIFont systemFontOfSize:20];
    textPlaceholdLabel.textColor = RGB_COLOR(@"#c9c9c9", 1);
    textPlaceholdLabel.text = YZMsg(@"给直播写个标题吧");
    [liveTitleTextView addSubview:textPlaceholdLabel];
    
    [PublicObj lineViewWithFrame:CGRectMake(10, preMiddleView.height*14/19, preMiddleView.width-20, 1) andColor:RGB_COLOR(@"#ffffff", 0.2) andView:preMiddleView];
    
    UILabel *shareLabel = [[UILabel alloc]initWithFrame:CGRectMake(10, preMiddleView.height*14/19, 50, preMiddleView.height*5/19)];
    shareLabel.font = [UIFont systemFontOfSize:13];
    shareLabel.textColor = RGB_COLOR(@"#c7c7c7", 1);
    shareLabel.text = YZMsg(@"分享到");
    if ([lagType isEqual:EN]) {
        shareLabel.text = @"Share";
    }
    [preMiddleView addSubview:shareLabel];
    NSArray *shareArray = [common share_type];
    CGFloat speace = (preMiddleView.width-70-180)/5;
    preShareBtnArray = [NSMutableArray array];
    if ([shareArray isKindOfClass:[NSArray class]]) {
        for (int i = 0; i<shareArray.count; i++) {
            UIButton *btn = [UIButton buttonWithType:0];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"直播分享灰-%@",shareArray[i]]] forState:UIControlStateNormal];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"直播分享-%@",shareArray[i]]] forState:UIControlStateSelected];
            [btn setTitle:shareArray[i] forState:UIControlStateNormal];
            [btn setTitle:shareArray[i] forState:UIControlStateSelected];
            [btn setTitleColor:[UIColor clearColor] forState:UIControlStateNormal|UIControlStateSelected];
            [btn addTarget:self action:@selector(share:) forControlEvents:UIControlEventTouchUpInside];
            [btn setTitle:shareArray[i] forState:UIControlStateNormal];
            [btn setTitleColor:[UIColor clearColor] forState:UIControlStateNormal];
            btn.imageView.contentMode = UIViewContentModeScaleAspectFit;
            btn.frame = CGRectMake(shareLabel.right+i*(30+speace),shareLabel.top+ preMiddleView.height*5/19/2-15, 30, 30);
            [preMiddleView addSubview:btn];
            [preShareBtnArray addObject:btn];
        }
        
    }
    
    /// 禁用提示
    UILabel *banAlertL = [[UILabel alloc]init];
    banAlertL.textColor = Pink_Cor;
    banAlertL.font = SYS_Font(15);
    banAlertL.numberOfLines = 0;
    banAlertL.text = [XGGPower getBanLiveMsg];
    [self addSubview:banAlertL];
    [banAlertL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(preMiddleView.mas_bottom).offset(10);
        make.width.equalTo(preMiddleView.mas_width).offset(-20);
        make.centerX.equalTo(preMiddleView);
    }];
    banAlertL.hidden = ![XGGPower getBanLiveStatus];
    
    //开播按钮
    UIButton *startLiveBtn = [UIButton buttonWithType:0];
    startLiveBtn.size = CGSizeMake(_window_width*0.8,40);
    startLiveBtn.center = CGPointMake(_window_width*0.5, _window_height*0.8);
    startLiveBtn.layer.cornerRadius = 20.0;
    startLiveBtn.layer.masksToBounds = YES;
    [startLiveBtn setBackgroundColor:Pink_Cor];
    [startLiveBtn addTarget:self action:@selector(clickStartLiveBtn:) forControlEvents:UIControlEventTouchUpInside];
    [startLiveBtn setTitle:YZMsg(@"开始直播") forState:0];
    startLiveBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [self addSubview:startLiveBtn];
    
    //美颜
    UIButton *preFitterBtn = [UIButton buttonWithType:0];
    preFitterBtn.frame = CGRectMake(_window_width/2-30, startLiveBtn.top-40, 60, 30);
    [preFitterBtn setTitle:YZMsg(@"美颜") forState:0];
    [preFitterBtn setImage:[UIImage imageNamed:@"pre_fitter"] forState:0];
    preFitterBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [preFitterBtn addTarget:self action:@selector(showFitterView) forControlEvents:UIControlEventTouchUpInside];
    preFitterBtn.imageEdgeInsets = UIEdgeInsetsMake(5, 0, 5, 40);
    preFitterBtn.titleEdgeInsets = UIEdgeInsetsMake(0, -5, 0, 0);
    if ([lagType isEqual:EN]) {
        preFitterBtn.frame = CGRectMake(_window_width/2-40, startLiveBtn.top-40, 80, 30);
        preFitterBtn.imageEdgeInsets = UIEdgeInsetsMake(5, -5, 5, 0);
        preFitterBtn.titleEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 0);
    }
    [self addSubview:preFitterBtn];
    
    //购物车
    UIButton *cartBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [cartBtn setTitle:YZMsg(@"开启购物车") forState:0];
    [cartBtn setTitleColor:UIColor.whiteColor forState:0];
    [cartBtn setBackgroundImage:[PublicObj getImgWithColor:RGB_COLOR(@"#000000", 0.3)] forState:0];
    [cartBtn setTitle:YZMsg(@"关闭购物车") forState:UIControlStateSelected];
    [cartBtn setTitleColor:Pink_Cor forState:UIControlStateSelected];
    [cartBtn setBackgroundImage:[PublicObj getImgWithColor:RGB_COLOR(@"#ffffff", 1)] forState:UIControlStateSelected];
    cartBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 10);
    cartBtn.layer.cornerRadius = 12;
    cartBtn.layer.masksToBounds = YES;
    cartBtn.titleLabel.font = SYS_Font(10);
    [cartBtn addTarget:self action:@selector(clickCartBtn:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:cartBtn];
    cartBtn.hidden = YES;
    _cartOpen = NO;
    [cartBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(24);
        make.centerX.equalTo(preFitterBtn);
        make.bottom.equalTo(preFitterBtn.mas_top).offset(-10);
    }];
    
    NSString *isShop = minstr([XGGPower getIsShop]);
    if ([isShop isEqual:@"1"]) {
        cartBtn.hidden = NO;
    }
    
}

//选择频道
- (void)showAllClassView{
    YBWeakSelf;
    startLiveClassVC *vc = [[startLiveClassVC alloc]init];
    vc.classID = liveClassID;
    vc.block = ^(NSDictionary * _Nonnull dic) {
        liveClassID = minstr([dic valueForKey:@"id"]);
        liveClassLabel.text = minstr([dic valueForKey:@"name"]);
        [weakSelf setSelClass];
    };
    vc.modalPresentationStyle = UIModalPresentationFullScreen;

//    vc.modalPresentationStyle = UIModalPresentationFullScreen;
//    self.animator = [[ZFModalTransitionAnimator alloc] initWithModalViewController:vc];
//    self.animator.bounces = NO;
//    self.animator.behindViewAlpha = 1;
//    self.animator.behindViewScale = 0.5f;
//    self.animator.transitionDuration = 0.4f;
//    vc.transitioningDelegate = self.animator;
//    self.animator.dragable = YES;
//    self.animator.direction = ZFModalTransitonDirectionRight;
//    [self presentViewController:vc animated:YES completion:nil];
    [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:vc animated:YES completion:nil];
}

-(void)clickCartBtn:(UIButton *)sender {
    sender.selected = !sender.selected;
    _cartOpen = sender.selected;
}
- (void)hidePreTextView{
    [liveTitleTextView resignFirstResponder];
}
#pragma mark - 定位开关
- (void)locationSwitchBtnClick{
    if ([locationLabel.text isEqual:YZMsg(@"开定位")]) {
        loactionImgView.image = [UIImage imageNamed:@"pre_location"];
        locationLabel.text = [XGGcityDefault getLocationCity];
        _locationSwitch = YES;
    }else{
        NSDictionary *contentDic = @{@"title":@"",@"msg":YZMsg(@"关闭定位，直播不会被附近的人看到，直播间人数可能会减少，确认关闭吗？"),@"left":YZMsg(@"取消"),@"right":YZMsg(@"坚决关闭")};
        [YBAlertView showAlertView:contentDic complete:^(int eventType) {
            if (eventType == 1) {
                loactionImgView.image = [UIImage imageNamed:@"pre_location_off"];
                locationLabel.text = YZMsg(@"开定位");
                _locationSwitch = NO;
            }
        }];
    }
}

#pragma mark - 切换
-(void)rotateCamera {
    if (self.livePreEvent) {
        self.livePreEvent(@"预览-切换", @"");
    }
}
- (void)doClosePreView {
    if (self.livePreEvent) {
        self.livePreEvent(@"预览-关闭", @"");
    }
}

-(void)clickStartLiveBtn:(UIButton *)sender {
    if (self.livePreEvent) {
        self.livePreEvent(@"预览-开直播", @"");
    }
}
-(void)showFitterView {
    if (self.livePreEvent) {
        self.livePreEvent(@"预览-美颜", @"");
    }
}
-(void)setSelClass{
    if (self.livePreEvent) {
        self.livePreEvent(@"预览-分类", liveClassID);
    }

}
- (void)share:(UIButton *)sender{
    if ([selectShareName isEqual:sender.titleLabel.text]) {
        sender.selected = NO;
        selectShareName = @"";
    }else{
        sender.selected = YES;
        selectShareName = sender.titleLabel.text;
        for (UIButton *btn in preShareBtnArray) {
            if (btn != sender) {
                btn.selected = NO;
            }
        }
    }
    if (self.livePreEvent) {
        self.livePreEvent(@"预览-分享", selectShareName);
    }
}

-(void)doUploadPicture{
    
    /*
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相机") complete:^{
        [weakSelf selectThumbWithType:UIImagePickerControllerSourceTypeCamera];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相册") complete:^{
        [weakSelf selectThumbWithType:UIImagePickerControllerSourceTypePhotoLibrary];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];
    */
     //安卓直接跳相册->去掉弹窗2020-7-2
    [self selectPhotos];
}

-(void)selectPhotos {
    TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.showSelectBtn = YES;
    imagePC.allowCrop = NO;
    imagePC.allowPickingOriginalPhoto = NO;
    imagePC.oKButtonTitleColorNormal = Pink_Cor;
    imagePC.allowTakePicture = YES;
    imagePC.allowTakeVideo = NO;
    imagePC.allowPickingVideo = NO;
    imagePC.allowPickingMultipleVideo = NO;
    imagePC.modalPresentationStyle = 0;
    [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    _thumbImage = photos[0];
    [preThumbBtn setImage:_thumbImage forState:UIControlStateNormal];
    thumbLabel.text = YZMsg(@"更换封面");
    thumbLabel.backgroundColor = RGB_COLOR(@"#0000000", 0.3);
}

-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"])
    {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        _thumbImage = image;
        [preThumbBtn setImage:image forState:UIControlStateNormal];
        thumbLabel.text = YZMsg(@"更换封面");
        thumbLabel.backgroundColor = RGB_COLOR(@"#0000000", 0.3);
    }
    [picker dismissViewControllerAnimated:YES completion:nil];
}
-(void)imagePickerControllerDidCancel:(UIImagePickerController *)picker{
    [picker dismissViewControllerAnimated:YES completion:nil];
}
- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if ([UIDevice currentDevice].systemVersion.floatValue < 11) {
        return;
    }
    if ([viewController isKindOfClass:NSClassFromString(@"PUPhotoPickerHostViewController")]) {
        [viewController.view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.frame.size.width < 42) {
                [viewController.view sendSubviewToBack:obj];
                *stop = YES;
            }
        }];
    }
}
- (void)textViewDidChange:(UITextView *)textView {
    if (textView.text.length == 0) {
        textPlaceholdLabel.text = YZMsg(@"给直播写个标题吧");
    }else{
        textPlaceholdLabel.text = @"";
    }
    _inputTitleStr = textView.text;
}


@end
