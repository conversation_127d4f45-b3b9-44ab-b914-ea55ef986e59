//
//  YBLiveFucView.m
//  YBVideo
//
//  Created by YB007 on 2019/12/1.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveFucView.h"

@interface YBLiveFucView()<UIGestureRecognizerDelegate>
{
    NSMutableArray *_btn_m_array;
    UIView *_bgView;
}
@end

@implementation YBLiveFucView

+(instancetype)showBotFunViewWithTorch:(BOOL)isTorch Complete:(LiveFunBlcok)complete {
    YBLiveFucView *view = [[YBLiveFucView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) torch:isTorch];
    view.backgroundColor = UIColor.clearColor;
    if (complete) {
        view.funEvent = ^(NSString *eventType) {
            complete(eventType);
        };
    }
    return view;
}
- (instancetype)initWithFrame:(CGRect)frame torch:(BOOL)isTorch {
    self = [super initWithFrame:frame];
    if (self) {
        [self createUI:isTorch];
    }
    return self;
}
-(void)createUI:(BOOL)isTorch {
    
    //不需要在这翻译，设置标题的时候翻译
    _btn_m_array = @[@"美颜",@"翻转",@"闪光灯",@"连麦",@"分享"].mutableCopy;
    
    //判断分享
    if ([[common share_type] count] == 0) {
        [_btn_m_array removeObject:@"分享"];
    }
    CGFloat bgView_W = _window_width - 20;
    int rowNum = 5;
    int lines = (int)_btn_m_array.count%rowNum == 0 ?((int)_btn_m_array.count/rowNum): ((int)_btn_m_array.count/rowNum+1);
    CGFloat btn_W = bgView_W/rowNum;
    CGFloat btnTopSpace = 5;
    
    _bgView = [[UIView alloc]init];
    _bgView.layer.cornerRadius = 5;
    _bgView.layer.masksToBounds = YES;
    _bgView.backgroundColor = [UIColor whiteColor];
    [self addSubview:_bgView];
    [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(bgView_W);
        make.centerX.equalTo(self);
        make.height.mas_equalTo((btnTopSpace+btn_W)*lines+btnTopSpace);
        make.bottom.equalTo(self.mas_bottom).offset(-ShowDiff-55);
    }];
    [self layoutIfNeeded];
    MASViewAttribute *leftAtt = _bgView.mas_left;
    MASViewAttribute *topAtt = _bgView.mas_top;
    for (int i=0; i<_btn_m_array.count; i++) {
        UIButton *btn = [UIButton buttonWithType:0];
        [btn addTarget:self action:@selector(doaction:) forControlEvents:UIControlEventTouchUpInside];
        [btn setTitleColor:[UIColor clearColor] forState:0];
        btn.tag = i + 1000;
        btn.titleLabel.font = SYS_Font(12);
        [btn setTitle:YZMsg(_btn_m_array[i]) forState:0];
        [btn setTitleColor:RGB_COLOR(@"#959697", 1) forState:0];
        [_bgView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btn_W);
            make.left.equalTo(leftAtt);
            make.top.equalTo(topAtt).offset(btnTopSpace);
        }];
        leftAtt = btn.mas_right;
        if ((i+1)%rowNum == 0) {
            leftAtt = _bgView.mas_left;
            topAtt = btn.mas_bottom;
        }
        [_bgView layoutIfNeeded];
        [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"功能_%@",_btn_m_array[i]]] forState:0];
        if ([_btn_m_array[i] isEqual:@"闪光灯"]) {
            if (isTorch) {
                [btn setImage:[UIImage imageNamed:@"功能_闪光灯开"] forState:0];
            }else {
                [btn setImage:[UIImage imageNamed:@"功能_闪光灯关"] forState:0];
            }
        }
        btn = [PublicObj setUpImgDownText:btn];
    }
    
    UITapGestureRecognizer *tagGes = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(dismiss)];
    tagGes.delegate = self;
    [self addGestureRecognizer:tagGes];
    
    [[UIApplication sharedApplication].delegate.window addSubview:self];
}
-(void)doaction:(UIButton*)sender {
    [self dismiss];
    NSString *str = _btn_m_array[sender.tag - 1000];
    if (self.funEvent) {
        self.funEvent([NSString stringWithFormat:@"直播功能-%@",str]);
    }
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    if ([touch.view isDescendantOfView:_bgView]) {
        return NO;
    }
    return YES;
}
-(void)dismiss {
    if (self.funEvent) {
        self.funEvent(@"直播功能-取消");
    }
    [UIView animateWithDuration:0.4 animations:^{
        self.top = _bgView.height+60+ShowDiff;
    } completion:^(BOOL finished) {
        [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
        [self removeFromSuperview];
    }];
}
@end
