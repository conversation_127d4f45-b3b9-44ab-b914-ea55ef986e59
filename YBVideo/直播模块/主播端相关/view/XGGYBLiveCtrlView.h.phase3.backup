//
//  YBLiveCtrlView.h
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "YBChatToolBar.h"

typedef void (^LiveCtrBlock)(NSString *eventStr,NSDictionary *eventDic);

@interface YBLiveCtrlView : UIView

@property(nonatomic,copy)LiveCtrBlock liveCtrEvent;
@property(nonatomic,assign)BOOL keyBoardIsShow;                 //键盘正在显示
@property(nonatomic,assign)BOOL isTorch;
@property(nonatomic,strong)NSString *liveUid;                   //主播uid
@property(nonatomic,strong)NSString *liveStream;                //主播留地址
@property(nonatomic,strong)NSString *liveTitle;
@property(nonatomic,strong)NSString *liveHostUrl;

@property(nonatomic,strong)YBChatToolBar *chatTool;             //直播聊天工具条
@property(nonatomic,strong)UIView *botBtnFunView;               //底部功能键(发言、私信、更多、关闭)

@property(nonatomic,strong)UIButton *cartBtn;                   //店铺
@property(nonatomic,strong)UIButton *startPkBtn;                //开始PK
@property(nonatomic,strong)UIButton *linkCtrBtn;                //连麦开关

/** 开播动画 */
-(void)startLiveAnimation;

/** 进房间更新用户列表、映票 */
-(void)createroomUpdateInfo:(NSDictionary *)roomDic;

/** 更新直播时长 */
-(void)updateLiveTime;

/** 用户点亮 */
-(void)socketLightAnimation;

/** 发言 */
-(void)socketChatDic:(NSDictionary *)chatDic;

/** 用户离开 */
-(void)socketUserLeave:(NSDictionary *)leaveDic;

/** 用户进入 */
-(void)socketUserEnter:(NSDictionary *)enterDic;

/** 送礼物 */
-(void)socketSendGift:(NSDictionary *)giftDic;

/** 计时器刷新列表 */
-(void)timerReloadList;

/** 私信未读数 */
-(void)updateUnread:(int)unread;

//销毁
-(void)destroyCtrSubView;

/** socket更新购买守护信息 */
-(void)socketUpdateGuardMsg:(NSDictionary *)dic;

/** socket展示连麦用户小窗 */
-(void)socketShowLinkUserPop:(NSDictionary *)linkDic;

/** socket连麦用户挂断 */
-(void)socketLinkUserDisconnect:(NSDictionary *)linkDic;

-(void)destroyAnchorOnlineList;
/** socekt主播-主播连麦成功 */
-(void)socketLinkHostToHostSuc:(NSDictionary*)linkDic;
/** socket主播-主播连麦挂断 */
-(void)socketlinkHostToHostDisconnect:(NSDictionary *)linkDic;

-(void)resetPKBtnFrame;
-(void)cancelPKAlert;

/** 主播-主播:对方主播id */
-(void)showToHostInfoWithId:(NSString *)toHostId;
-(void)destroyPkTohostInfo;
/** PK 开始 */
-(void)socketLinkPKStart:(NSDictionary *)pkDic;
/** PK 结果 */
-(void)socketLinkPKReult:(NSDictionary *)pkDic;
/** PK 进度 */
-(void)socketLinkPKProgress:(NSDictionary *)pkDic;

/** 键盘弹起事件: 目前只处理 PK和连麦 的界面相对位置不变*/
-(void)keyBoardNoticeIsShow:(BOOL)isShow andHeight:(CGFloat)height;
@end


