//
//  YBCheckLiveObj.m
//  YBVideo
//
//  Created by YB007 on 2019/12/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBCheckLiveObj.h"
#import "YBPlayVC.h"

@implementation YBCheckLiveObj

static  YBCheckLiveObj*_checkLiveManeger = nil;

+(instancetype)checkLiveManeger {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _checkLiveManeger = [[super allocWithZone:NULL]init];
    });
    return _checkLiveManeger;
}

+(instancetype)allocWithZone:(struct _NSZone *)zone {
    return [self checkLiveManeger];
}

-(void)checkLiving {
    if ([[Config getOwnID] intValue]<=0) {
        [PublicObj warnLogin];
        return;
    }
    if ([PublicObj checkNull:_liveUid] || [PublicObj checkNull:_liveStream]) {
        [MBProgressHUD showPop:YZMsg(@"缺少信息")];
        return;
    }
    
    [MBProgressHUD showMessage:@""];
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Live.checkLive" Dic:@{@"liveuid":_liveUid,@"stream":_liveStream} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            [weakSelf canEnterRoom:infoDic];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];

}

-(void)canEnterRoom:(NSDictionary *)playDic {
    
    YBPlayVC *pVC = [[YBPlayVC alloc]init];
    pVC.playDic = playDic;
    pVC.currentIndex = _currentIndex?_currentIndex:0;
    pVC.listArray = _listArray?_listArray:@[playDic];
    [[XGGAppDelegate sharedAppDelegate]pushViewController:pVC animated:YES];
    
}

@end
