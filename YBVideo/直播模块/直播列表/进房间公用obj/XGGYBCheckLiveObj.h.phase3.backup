//
//  YBCheckLiveObj.h
//  YBVideo
//
//  Created by YB007 on 2019/12/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface YBCheckLiveObj : NSObject


+(instancetype)checkLiveManeger;

@property(nonatomic,strong)NSString *liveUid;
@property(nonatomic,strong)NSString *liveStream;

@property(nonatomic,assign)NSInteger currentIndex;
@property(nonatomic,strong)NSArray *listArray;

-(void)checkLiving;

@end


