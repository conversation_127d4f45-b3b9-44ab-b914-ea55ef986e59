//
//  YBLiveListCell.m
//  YBVideo
//
//  Created by YB007 on 2019/12/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveListCell.h"

@implementation YBLiveListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
}

- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    [_thumbIV sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])] placeholderImage:[PublicObj getAppIcon]];
    [_avatarIV sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"avatar"])] placeholderImage: [PublicObj getAppIcon]];
    _nameL.text = minstr([_dataDic valueForKey:@"user_nickname"]);
    _liveNumsL.text = minstr([_dataDic valueForKey:@"nums"]);
    _titleL.text = minstr([_dataDic valueForKey:@"title"]);
    int isShop = [minstr([_dataDic valueForKey:@"isshop"]) intValue];
    _cartIV.hidden = !isShop;
}


@end
