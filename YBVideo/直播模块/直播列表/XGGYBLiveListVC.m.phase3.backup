//
//  YBLiveListVC.m
//  YBVideo
//
//  Created by YB007 on 2019/12/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveListVC.h"

#import "YBLiveListCell.h"
#import "YBCheckLiveObj.h"

#import "SPPageMenu.h"

@interface YBLiveListVC ()<UICollectionViewDelegate,UICollectionViewDataSource,SPPageMenuDelegate>
{
    int _paging;
    NSArray *classArray;
    NSArray *liveClass;
    NSString *classid;
     
    NSInteger currentPageIndex;
}
@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,strong)NSMutableArray *dataArray;
@property (nonatomic, weak) SPPageMenu *pageMenu;

@end

@implementation YBLiveListVC

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self pullData];
    [_pageMenu setSelectedItemIndex:0];
    
    if ([YBYoungManager shareInstance].youngSwitch == 1) {
        [[YBYoungManager shareInstance] checkYoungStatus:YoungFrom_Home];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"直播广场");
    _paging = 1;
    _dataArray = [NSMutableArray array];
    classid = @"";
    currentPageIndex = 0;
    liveClass = [common liveclass];
    NSMutableArray *class = [NSMutableArray array];
    for (NSDictionary *dic in liveClass) {
        [class addObject:minstr([dic valueForKey:@"name"])];
    }
    classArray = class;
    
    SPPageMenu *pageMenu = [SPPageMenu pageMenuWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, 40) trackerStyle:SPPageMenuTrackerStyleNothing];
    [pageMenu setItems:classArray selectedItemIndex:0];
    pageMenu.delegate = self;
    pageMenu.dividingLine.hidden = YES;
    pageMenu.selectedItemTitleColor = [UIColor whiteColor];
    pageMenu.unSelectedItemTitleColor = Normal_TextColor;
    pageMenu.selectedItemTitleFont =  [UIFont systemFontOfSize:14];
    pageMenu.unSelectedItemTitleFont = [UIFont systemFontOfSize:14];
    pageMenu.backgroundColor = Normal_SubColor;
    [self.view addSubview:pageMenu];
    _pageMenu = pageMenu;

    [self.view addSubview:self.collectionView];
    
}
- (void)pageMenu:(SPPageMenu *)pageMenu itemSelectedAtIndex:(NSInteger)index {
    NSLog(@"sssssss::::%zd",index);
    currentPageIndex = index;
//
    if (index != 0) {
        classid = minstr([liveClass[index] valueForKey:@"id"]);
        _paging = 1;
        [self requstLiveClass:classid];

    }else{
        _paging = 1;
        [self pullData];
    }

}
-(void)requstLiveClass:(NSString *)classID{
    [YBNetworking postWithUrl:@"Live.getClassLive" Dic:@{@"liveclassid":classID,@"p":@(_paging)} Suc:^(int code, id info, NSString *msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging == 1) {
                [_dataArray removeAllObjects];
            }
            if (infoA.count <= 0 ) {
                [_collectionView.mj_footer endRefreshingWithNoMoreData];
            }else {
                [_dataArray addObjectsFromArray:infoA];
            }
            if (_dataArray.count<=0) {
                [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂时没有主播开播") centerY:0.8];
            }else {
                [PublicView hiddenTextNoData:_collectionView];
            }
            [_collectionView reloadData];
            
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
    }];

}
-(void)pullData {
    
    [YBNetworking postWithUrl:@"Live.getRecommendLists" Dic:@{@"p":@(_paging)} Suc:^(int code, id info, NSString *msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging == 1) {
                [_dataArray removeAllObjects];
            }
            if (infoA.count <= 0 ) {
                [_collectionView.mj_footer endRefreshingWithNoMoreData];
            }else {
                [_dataArray addObjectsFromArray:infoA];
            }
            if (_dataArray.count<=0) {
                [PublicView showTextNoData:_collectionView text1:@"" text2:YZMsg(@"暂时没有主播开播") centerY:0.8];
            }else {
                [PublicView hiddenTextNoData:_collectionView];
            }
            [_collectionView reloadData];
            
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
    }];
    
}
#pragma mark - CollectionView 代理
/*
 * minimumLineSpacing、minimumInteritemSpacing去设置
 -(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
 return CGSizeMake(0,0);
 }
  */
 -(UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
 return UIEdgeInsetsMake(5,5,5,5);
 }

/*
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 0.01;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section{
    return 0.01;
}
*/
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    
    return _dataArray.count;
}

-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    YBLiveListCell *cell = (YBLiveListCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"YBLiveListCell" forIndexPath:indexPath];
    cell.dataDic = _dataArray[indexPath.row];
    return cell;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    
    NSDictionary *subDic = _dataArray[indexPath.row];
    [YBCheckLiveObj checkLiveManeger].liveUid = minstr([subDic valueForKey:@"uid"]);
    [YBCheckLiveObj checkLiveManeger].liveStream = minstr([subDic valueForKey:@"stream"]);
    [YBCheckLiveObj checkLiveManeger].currentIndex = indexPath.row;
    [YBCheckLiveObj checkLiveManeger].listArray = _dataArray.copy;
    [[YBCheckLiveObj checkLiveManeger] checkLiving];
    
}

#pragma mark - set/get
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionVertical;
        flow.itemSize = CGSizeMake((_window_width-15)/2, (_window_width-15)/2);
        flow.minimumLineSpacing = 5;
        flow.minimumInteritemSpacing = 5;
        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,statusbarHeight+64+40, _window_width, _window_height-statusbarHeight-64-40) collectionViewLayout:flow];
        [_collectionView registerNib:[UINib nibWithNibName:@"YBLiveListCell" bundle:nil] forCellWithReuseIdentifier:@"YBLiveListCell"];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.backgroundColor = Normal_Color;
        _collectionView.mj_footer  = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
            _paging ++;
            if (currentPageIndex == 0) {
                [self pullData];
            }else{
                [self requstLiveClass:classid];
            }

        }];

        _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            _paging = 1;
            if (currentPageIndex == 0) {
                [self pullData];
            }else{
                [self requstLiveClass:classid];

            }
        }];
        _collectionView.contentInset = UIEdgeInsetsMake(0, 0, ShowDiff, 0);
    }
    return _collectionView;
}

@end
