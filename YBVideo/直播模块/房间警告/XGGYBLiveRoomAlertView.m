//
//  YBLiveRoomAlertView.m
//  YBVideo
//
//  Created by YB007 on 2022/3/3.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBLiveRoomAlertView.h"


@interface YBLiveRoomAlertView()<UIPickerViewDelegate,UIPickerViewDataSource>
{
    NSInteger selRow;
}
/// 直播间警告
@property(nonatomic,strong)NSDictionary *warningInfo;
@property(nonatomic,strong)UIView *warningBgView;

/// 直播间封禁选项
@property(nonatomic,strong)NSArray *banOptionArray;
@property(nonatomic,strong)UIView *banBgView;
@property(nonatomic,strong)UIPickerView *banPicker;

@end

@implementation YBLiveRoomAlertView

-(void)dissmissView {
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}

#pragma mark - 直播间警告

+(YBLiveRoomAlertView *)showRoomWarning:(NSDictionary *)warningInfo;{
    YBLiveRoomAlertView *view = [[YBLiveRoomAlertView alloc]init];
    view.frame = CGRectMake(0, 0, _window_width, _window_height);
    view.warningInfo = warningInfo;
    //[[XGGAppDelegate sharedAppDelegate].topViewController.view addSubview:view];
    [[UIApplication sharedApplication].delegate.window addSubview:view];
    [view createWarningUI];
    return view;
}

-(void)createWarningUI {
    _warningBgView = [[UIView alloc]init];
    _warningBgView.backgroundColor = UIColor.whiteColor;
    _warningBgView.layer.cornerRadius = 10;
    _warningBgView.layer.masksToBounds = YES;
    [self addSubview:_warningBgView];
    [_warningBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mas_width).multipliedBy(0.75);
        make.centerX.centerY.equalTo(self);
    }];
    
    //
    UIImageView *topIV = [[UIImageView alloc]init];
    topIV.image = [UIImage imageNamed:@"房间-警告"];
    [_warningBgView addSubview:topIV];
    [topIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(40);
        make.top.equalTo(_warningBgView.mas_top).offset(10);
        make.centerX.equalTo(_warningBgView);
    }];
    
    ///
    UILabel *desL = [[UILabel alloc]init];
    desL.text = minstr([_warningInfo valueForKey:@"ct"]);
    desL.textColor = RGB_COLOR(@"#323232", 1);
    desL.font = SYS_Font(15);
    desL.textAlignment = NSTextAlignmentCenter;
    desL.numberOfLines = 0;
    [_warningBgView addSubview:desL];
    [desL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_warningBgView.mas_width).offset(-30);
        make.centerX.equalTo(_warningBgView);
        make.top.equalTo(topIV.mas_bottom).offset(10);
        make.height.mas_greaterThanOrEqualTo(40);
    }];
    
    ///
    UIButton *knowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [knowBtn setTitle:YZMsg(@"知道了") forState:0];
    [knowBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
    knowBtn.titleLabel.font = SYS_Font(15);
    knowBtn.backgroundColor = RGB_COLOR(@"#ff0000", 1);
    knowBtn.layer.cornerRadius = 5;
    knowBtn.layer.masksToBounds = YES;
    [knowBtn addTarget:self action:@selector(clickKnowBtn) forControlEvents:UIControlEventTouchUpInside];
    [_warningBgView addSubview:knowBtn];
    [knowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(40);
        make.top.equalTo(desL.mas_bottom).offset(15);
        make.bottom.equalTo(_warningBgView.mas_bottom).offset(-15);
        make.width.equalTo(_warningBgView.mas_width).multipliedBy(0.8);
        make.centerX.equalTo(_warningBgView);
    }];
    
}

-(void)clickKnowBtn {
    [self dissmissView];
}


#pragma mark - 直播间封禁选项
+(YBLiveRoomAlertView *)showBanOption:(NSArray *)banArray;{
    YBLiveRoomAlertView *view = [[YBLiveRoomAlertView alloc]init];
    view.frame = CGRectMake(0, 0, _window_width, _window_height);
    view.banOptionArray = banArray;
    [[XGGAppDelegate sharedAppDelegate].topViewController.view addSubview:view];
    [view createBanOptionUI];
    return view;
}


-(void)createBanOptionUI {
    
//    _banOptionArray = @[
//        @{@"type":@"1",@"title":@"30分钟"},
//        @{@"type":@"2",@"title":@"1天"},
//        @{@"type":@"3",@"title":@"7天"},
//        @{@"type":@"4",@"title":@"15天"},
//        @{@"type":@"5",@"title":@"30天"},
//        @{@"type":@"6",@"title":@"90天"},
//        @{@"type":@"7",@"title":@"180天"},
//        @{@"type":@"8",@"title":@"永久"},
//    ];
//
    
    _banBgView = [[UIView alloc]init];
    _banBgView.backgroundColor = UIColor.whiteColor;
    _banBgView.layer.cornerRadius = 10;
    _banBgView.layer.masksToBounds = YES;
    [self addSubview:_banBgView];
    [_banBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mas_width).multipliedBy(0.75);
        make.centerX.centerY.equalTo(self);
    }];
    
    UILabel *titleL = [[UILabel alloc]init];
    titleL.text = YZMsg(@"请选择封禁时间");
    titleL.textColor = RGB_COLOR(@"#323232", 1);
    titleL.font = SYS_Font(15);
    titleL.textAlignment = NSTextAlignmentCenter;
    [_banBgView addSubview:titleL];
    [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_banBgView.mas_width).offset(-30);
        make.centerX.equalTo(_banBgView);
        make.top.equalTo(_banBgView.mas_top).offset(0);
        make.height.mas_equalTo(50);
    }];
    
    _banPicker = [[UIPickerView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 100)];
    _banPicker.backgroundColor = [UIColor whiteColor];
    _banPicker.delegate = self;
    _banPicker.dataSource = self;
    _banPicker.showsSelectionIndicator = YES;
    [_banPicker selectRow: 0 inComponent: 0 animated: YES];
    [_banBgView addSubview:_banPicker];
    [_banPicker mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(_banBgView);
        make.top.equalTo(titleL.mas_bottom).offset(0);
        make.height.mas_equalTo(100);
    }];
    
    UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [cancelBtn setTitle:YZMsg(@"取消") forState:0];
    cancelBtn.titleLabel.font = SYS_Font(13);
    [cancelBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
    cancelBtn.layer.cornerRadius = 15;
    cancelBtn.layer.masksToBounds = YES;
    cancelBtn.backgroundColor = RGB_COLOR(@"#b3b3b3", 1);
    [cancelBtn addTarget:self action:@selector(clickCancelBtn) forControlEvents:UIControlEventTouchUpInside];
    [_banBgView addSubview:cancelBtn];
    [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_banBgView.mas_width).multipliedBy(0.3);
        make.centerX.equalTo(_banBgView.mas_centerX).multipliedBy(0.5);
        make.height.mas_equalTo(30);
        make.top.equalTo(_banPicker.mas_bottom).offset(10);
        make.bottom.equalTo(_banBgView).offset(-15);
    }];
    
    UIButton *sureBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [sureBtn setTitle:YZMsg(@"确定") forState:0];
    sureBtn.titleLabel.font = SYS_Font(13);
    [sureBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
    sureBtn.layer.cornerRadius = 15;
    sureBtn.layer.masksToBounds = YES;
    sureBtn.backgroundColor = Pink_Cor;
    [sureBtn addTarget:self action:@selector(clickSureBtnBtn) forControlEvents:UIControlEventTouchUpInside];
    [_banBgView addSubview:sureBtn];
    [sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerY.equalTo(cancelBtn);
        make.centerX.equalTo(_banBgView.mas_centerX).multipliedBy(1.5);
    }];
    
}
-(void)clickCancelBtn {
    [self dissmissView];
}
-(void)clickSureBtnBtn {
    int idx = (int)[_banPicker selectedRowInComponent:0];
    NSDictionary *selDic = _banOptionArray[idx];
    
    NSString *selId = minstr([selDic valueForKey:@"id"]);
    if (self.banEvent) {
        self.banEvent(selId);
    }
}
#pragma mark--- Picker Data Source Methods-----
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return 1;
}
- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    return _banOptionArray.count;
}
- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    return minstr([_banOptionArray[row] valueForKey:@"title"]);
}
- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component{
    selRow = row;
    [_banPicker reloadAllComponents];
}
- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view {
    
    for(UIView *speartorView in pickerView.subviews) {
            speartorView.backgroundColor = UIColor.clearColor;
            if (speartorView.frame.size.height < 80) {//找出当前的 View
                // 添加分割线 (判断只添加一次  滑动不断刷新)
                if (speartorView.subviews.count == 0){
                    /*
                    UIView *line = [self lineView];
                    line.frame = CGRectMake(0, 0, speartorView.mj_w, 0.5);
                    [speartorView addSubview:line];
                    UIView *line2 = [self lineView];
                    line2.frame = CGRectMake(0, speartorView.mj_h-1, speartorView.mj_w, 0.5);
                    [speartorView addSubview:line2];
                    */
                }
                speartorView.backgroundColor = [UIColor clearColor];
            }else{
                speartorView.backgroundColor = [UIColor clearColor];
            }
        }
    
    UILabel* pickerLabel = (UILabel*)view;
    if (!pickerLabel){
        pickerLabel = [[UILabel alloc] init];
        pickerLabel.adjustsFontSizeToFitWidth = YES;
        [pickerLabel setTextAlignment:NSTextAlignmentCenter];
        pickerLabel.backgroundColor = RGB_COLOR(@"#f3f3f3", 1);
        pickerLabel.font = [UIFont systemFontOfSize:15];
    }
    if (row == selRow) {
        pickerLabel.textColor = RGB_COLOR(@"#323232", 1);
    }
    pickerLabel.text=[self pickerView:pickerView titleForRow:row forComponent:component];
    return pickerLabel;
}

- (UIView *)lineView {
    UIView *line = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _banPicker.width*0.8, 0.5)];
    line.backgroundColor = UIColor.clearColor;//RGB_COLOR(@"#DCDCDC", 1);
    return line;
}


@end
