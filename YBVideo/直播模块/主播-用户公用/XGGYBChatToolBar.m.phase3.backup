//
//  YBChatToolBar.m
//  YBVideo
//
//  Created by YB007 on 2019/11/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBChatToolBar.h"

@interface YBChatToolBar()<UITextFieldDelegate>

@property(nonatomic,strong)UIButton *sendBtn;

@property (nonatomic, strong) UIToolbar *customAccessoryView;

@end
@implementation YBChatToolBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self createUI];
    }
    return self;
}
-(void)createUI {
    
    UIView *tooBgv = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 44)];
    tooBgv.backgroundColor = [UIColor whiteColor];
    tooBgv.alpha = 0.7;
    [self addSubview:tooBgv];
    
    _chatTF = [[UITextField alloc]init];
    _chatTF.returnKeyType = UIReturnKeySend;
    _chatTF.delegate  = self;
    _chatTF.borderStyle = UITextBorderStyleNone;
    _chatTF.placeholder = YZMsg(@"和大家说点什么...");
    _chatTF.backgroundColor = [UIColor whiteColor];
    _chatTF.layer.cornerRadius = 15.0;
    _chatTF.layer.masksToBounds = YES;
    UIView *fieldLeft = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 15, 30)];
    fieldLeft.backgroundColor = [UIColor whiteColor];
    _chatTF.leftView = fieldLeft;
    _chatTF.leftViewMode = UITextFieldViewModeAlways;
    _chatTF.font = [UIFont systemFontOfSize:15];
    [_chatTF addTarget:self action:@selector(chatTFChangeValue) forControlEvents:UIControlEventEditingChanged];
    [self addSubview:_chatTF];
    [_chatTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(30);
        make.centerY.equalTo(self);
        make.left.equalTo(self.mas_left).offset(10);
    }];
    
    UILabel *v_line = [[UILabel alloc]init];
    v_line.backgroundColor = RGB_COLOR(@"#b0b0b0", 1);
    v_line.alpha = 0.5;
    [self addSubview:v_line];
    _sendBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [_sendBtn setImage:[UIImage imageNamed:@"chat_send_gray"] forState:UIControlStateNormal];
    [_sendBtn setImage:[UIImage imageNamed:@"chat_send_pink"] forState:UIControlStateSelected];
    _sendBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
    _sendBtn.layer.masksToBounds = YES;
    _sendBtn.layer.cornerRadius = 5;
    [_sendBtn addTarget:self action:@selector(clickSendMsg:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:_sendBtn];
    [_sendBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(50);
        make.height.mas_equalTo(30);
        make.centerY.equalTo(_chatTF.mas_centerY);
        make.right.equalTo(self.mas_right).offset(-5);
    }];
    [v_line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(1);
        make.height.mas_equalTo(20);
        make.centerY.equalTo(_chatTF);
        make.left.equalTo(_chatTF.mas_right).offset(7);
        make.right.equalTo(_sendBtn.mas_left).offset(-5);
    }];
    
    
    //_chatTF.inputAccessoryView = self.customAccessoryView;
    
}
/*
- (UIToolbar *)customAccessoryView{
    if (!_customAccessoryView) {
        _customAccessoryView = [[UIToolbar alloc]initWithFrame:(CGRect){0,0,_window_width,40}];
        _customAccessoryView.barTintColor = [UIColor orangeColor];
        UIBarButtonItem *space = [[UIBarButtonItem alloc]initWithBarButtonSystemItem:UIBarButtonSystemItemFlexibleSpace target:nil action:nil];
        UIBarButtonItem *finish = [[UIBarButtonItem alloc]initWithTitle:@"Done" style:UIBarButtonItemStyleDone target:self action:@selector(done)];
        [_customAccessoryView setItems:@[space,space,space,finish]];
    }
    return _customAccessoryView;
}

- (void)done{
    [_chatTF resignFirstResponder];
}
*/
-(void)clickSendMsg:(UIButton *)sender {
    if (_chatTF.text.length<=0) {
        return;
    }
    //敏感词过滤
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"User.checkBlack" Dic:@{@"content":_chatTF.text,@"touid":@"0"} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *contStr = minstr([infoDic valueForKey:@"content"]);
            if (self.chatToolEvnt) {
                self.chatToolEvnt(@"直播聊天-发送", contStr);
                _chatTF.text = @"";
                _sendBtn.selected = NO;
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
    
}
-(void)chatTFChangeValue {
    if (_chatTF.text.length > 0) {
        _sendBtn.selected = YES;
    }else{
        _sendBtn.selected = NO;
    }
}
- (BOOL)textFieldShouldReturn:(UITextField *)textField; {
    if (textField == _chatTF) {
        [self clickSendMsg:nil];
    }
    return YES;
}
@end
