//
//  YBPlayCtrlView.m
//  YBVideo
//
//  Created by YB007 on 2019/12/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBPlayCtrlView.h"

#import "YBLiveChatView.h"
#import "YBUserListView.h"
#import "YBUserEnterAnimation.h"
#import "YBGiftPage.h"
#import "expensiveGiftV.h"
#import "continueGift.h"
#import "YBShareView.h"
#import "YBOnSaleView.h"
#import "YBGoodsInfoVC.h"
#import "YBGoodsBriefView.h"
#import "RKShowPaintedView.h"
#import "SDCycleScrollView.h"
#import "YBDayTaskView.h"
#import "turntableView.h"
#import "guardShowView.h"
#import "shouhuView.h"
#import "YBTxLinkMicView.h"
#import "YBAnchorPKView.h"
#import "OutsideGoodsDetailVC.h"
#import "CommodityDetailVC.h"
#import "YBAnchorLinkInfo.h"
@interface YBPlayCtrlView()<UIGestureRecognizerDelegate,haohuadelegate,SDCycleScrollViewDelegate,turntableViewDelegate,shouhuViewDelegate,guardShowDelegate>
{
    NSString *_liveUid;                         //主播uid
    NSString *_liveStream;                      //主播留地址
    
    expensiveGiftV *haohualiwuV;                //豪华礼物
    continueGift *continueGifts;                //连送礼物
    UIView *liansongliwubottomview;
    UIImageView *starImage;
    CGFloat starX;
    CGFloat starY;
    guardShowView *gShowView;
    shouhuView *guardView;
    int _linkMicAskLast;
    NSTimer *_linkMicAskTimer;                  //连麦请求中倒计时
    YBAlertView *_disconnectAlert;
}
//@property(nonatomic,strong)UIScrollView *horScrollView;         //水平滑动Scroll
@property(nonatomic,strong)UIView *showUIView;

@property(nonatomic,strong)UIView *hostInfoView;                //左上角主播信息
@property(nonatomic,strong)UIButton *hostAvatarBtn;             //主播头像
@property(nonatomic,strong)UILabel *hostNameL;                  //主播昵称
@property(nonatomic,strong)UILabel *hostIDL;                    //主播id
@property(nonatomic,strong)UIButton *followBtn;                 //关注按钮

@property(nonatomic,strong)UIButton *hostVotesBtn;              //主播映票
@property(nonatomic,strong)UIButton *hostGuardBtn;              //守护
@property(nonatomic,strong)NSArray *roomGameArray;
@property(nonatomic,strong)SDCycleScrollView *roomGameCycle;    //幸运转盘+每日任务
@property(nonatomic,strong)YBPageControl *roomGamePage;
@property(nonatomic,strong)YBDayTaskView *dayTaskView;          //每日任务
@property(nonatomic,strong)turntableView *turntableView;        //转盘

@property(nonatomic,strong)YBUserListView *userListView;        //用户列表
@property(nonatomic,strong)YBLiveChatView *liveChatView;        //公屏聊天
@property(nonatomic,strong)YBGoodsBriefView *goodsBriefView;    //当前展示的商品
@property(nonatomic,strong)YBUserEnterAnimation *userAnimation; //进场动画
@property(nonatomic,strong)YBOnSaleView *onsaleView;
@property(nonatomic,strong)RKShowPaintedView *paintedShowRegion;//手绘礼物显示区域


@property(nonatomic,strong)UIButton *closeLiveBtn;              //↓...底部功能键
@property(nonatomic,strong)UIButton *shareBtn;
@property(nonatomic,strong)UIButton *msgBtn;
@property(nonatomic,strong)UILabel *redPoint;
@property(nonatomic,strong)UIButton *giftBtn;
@property(nonatomic,strong)UIButton *cartBtn;

@property(nonatomic,strong)YBTxLinkMicView *linkMicView;        //连麦窗口
@property(nonatomic,strong)YBAnchorPKView *pkView;
@property(nonatomic,strong)YBAnchorLinkInfo *pkTohostInfoV;
@property(nonatomic,strong)UIButton *pkFollowBtn;

@end

@implementation YBPlayCtrlView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.avatarEffect];
        [self addSubview:self.horScrollView];
        if (@available(iOS 11.0,*)) {
            _horScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }else {
            [XGGAppDelegate sharedAppDelegate].topViewController.automaticallyAdjustsScrollViewInsets = NO;
        }
        [_showUIView addSubview:self.hostInfoView];
        [_showUIView addSubview:self.hostVotesBtn];
        [_showUIView addSubview:self.hostGuardBtn];
        [_showUIView addSubview:self.userListView];
        [_showUIView addSubview:self.liveChatView];
        [_showUIView addSubview:self.goodsBriefView];
        [_showUIView addSubview:self.userAnimation];
        [_showUIView addSubview:self.botBtnFunView];
        [_showUIView addSubview:self.chatTool];
        [_showUIView addSubview:self.linkCtrBtn];
        
        self.roomGameArray = @[@"日任-房间",@"幸运大转盘"];
        if ([lagType isEqual:EN]) {
            self.roomGameArray = @[@"日任-房间_en",@"幸运大转盘_en"];
        }
        [_showUIView addSubview:self.roomGameCycle];
        [_showUIView addSubview:self.roomGamePage];
        if ([PublicObj isUp]) {
            self.roomGameCycle.hidden = self.roomGamePage.hidden = YES;
        }
        [_showUIView addSubview:self.turntableView];
        _turntableView.hidden = YES;
        
        /// 青少年
        int youngSwitch = [YBYoungManager shareInstance].youngSwitch;
        if (youngSwitch == 1) {
            _linkCtrBtn.hidden = YES;
            _roomGameCycle.hidden = YES;
            _roomGamePage.hidden = YES;
            _hostVotesBtn.hidden = YES;
            _hostGuardBtn.hidden = YES;
        }
    }
    return self;
}
- (void)layoutSubviews {
    [super layoutSubviews];
    [_hostInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_showUIView.mas_left).offset(11);
        make.top.equalTo(_showUIView.mas_top).offset(27+statusbarHeight);
        make.right.equalTo(_followBtn.mas_right).offset(5);
        make.height.mas_equalTo(34);
    }];
    
    [_userListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(40);
        make.left.equalTo(_hostInfoView.mas_right).offset(5);
        make.centerY.equalTo(_hostInfoView.mas_centerY).offset(-5);
        make.right.equalTo(_showUIView.mas_right).offset(0);
    }];
    
    [_hostVotesBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_hostInfoView);
        make.top.equalTo(_hostInfoView.mas_bottom).offset(8);
        make.height.mas_equalTo(22);
    }];
    [_hostGuardBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_hostVotesBtn.mas_right).offset(5);
        make.height.centerY.equalTo(_hostVotesBtn);
    }];
    [_roomGameCycle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(70);
        make.height.mas_equalTo(50);
        make.left.equalTo(_hostVotesBtn);
        make.top.equalTo(_hostVotesBtn.mas_bottom).offset(30);
    }];
    [_roomGamePage mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(10);
        if (@available(iOS 14.0,*)) {
            make.width.mas_equalTo(200);
        }else {
            make.width.mas_equalTo(80);
        }
        make.centerX.equalTo(_roomGameCycle);
        make.top.equalTo(_roomGameCycle.mas_bottom).offset(0);
    }];
    
    [_userAnimation mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_showUIView.mas_left).offset(10);
        make.bottom.equalTo(_liveChatView.mas_top).offset(-20);
        make.width.mas_equalTo(_window_width);
        make.height.mas_equalTo(20);
    }];
    CGFloat blodW = 50;
    if ([PublicObj isBlodText]) {
        blodW = 60;
    }
    [_linkCtrBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(blodW);
        make.bottom.equalTo(_botBtnFunView.mas_top).offset(-10);
        make.right.equalTo(_showUIView.mas_right).offset(-15);
    }];
    [_linkCtrBtn layoutIfNeeded];
    _linkCtrBtn = [PublicObj setUpImgDownText:_linkCtrBtn];
    
    [_hostGuardBtn layoutIfNeeded];
    [_hostGuardBtn setImageEdgeInsets:UIEdgeInsetsMake(0, _hostGuardBtn.titleLabel.size.width, 0, -_hostGuardBtn.titleLabel.size.width)];
    [_hostGuardBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, -_hostGuardBtn.imageView.size.width-1, 0, _hostGuardBtn.imageView.size.width+1)];
}
- (void)setPlayDic:(NSDictionary *)playDic {
    _playDic = playDic;
    
    _liveUid = minstr([_playDic valueForKey:@"uid"]);
    _liveStream = minstr([_playDic valueForKey:@"stream"]);
    
    if (_userListView) {
        _userListView.liveUid = minstr(_liveUid);
        _userListView.liveStream = minstr(_liveStream);
    }
    //主播信息
    [_avatarEffect sd_setImageWithURL:[NSURL URLWithString:minstr([_playDic valueForKey:@"avatar"])] placeholderImage:[PublicObj getAppIcon]];
    [_hostAvatarBtn sd_setImageWithURL:[NSURL URLWithString:minstr([_playDic valueForKey:@"avatar"])] forState:0 placeholderImage:[PublicObj getAppIcon]];
    _hostNameL.text = minstr([_playDic valueForKey:@"user_nickname"]);
    _hostIDL.text = [NSString stringWithFormat:@"ID:%@",[_playDic valueForKey:@"uid"]];
    
    int isShop = [minstr([playDic valueForKey:@"isshop"]) intValue];
    int youngSwitch = [YBYoungManager shareInstance].youngSwitch;
    if (youngSwitch == 1) {
        // 开启青少年后购物开关置为0
        isShop = 0;
        [_giftBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(30);
            make.width.mas_equalTo(0);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_cartBtn.mas_left).offset(-10);
        }];
    }
    _cartBtn.hidden = !isShop;
    CGFloat btnW = (isShop == 1 )?30:0;
    CGFloat leftSpace = (isShop == 1 )?(-10):0;
    [_cartBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(btnW);
        make.centerY.equalTo(_botBtnFunView);
        make.right.equalTo(_msgBtn.mas_left).offset(leftSpace);
    }];
    
    [self showGoodsBtnAnimaition];
}
#pragma mark - 点击事件
-(void)clickHostAvatarBtn {
    if (self.playCtrEvent && ![PublicObj checkNull:_liveUid]) {
        self.playCtrEvent(@"控制-信息卡", @{@"id":_liveUid});
    }
}
-(void)clickFollowBtn {
    [MBProgressHUD showMessage:@""];
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.setAttent" Dic:@{@"touid":_liveUid} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        [weakSelf updateFollowShow:NO];
        if (weakSelf.playCtrEvent) {
            weakSelf.playCtrEvent(@"控制-关注", @{});
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
-(void)updateFollowShow:(BOOL)isShow {
    if (isShow) {
        [_followBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(24);
            make.centerY.equalTo(_hostAvatarBtn);
            make.left.equalTo(_hostNameL.mas_right).offset(6);
        }];
    }else {
        [_followBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(0);
            make.height.mas_equalTo(24);
            make.centerY.equalTo(_hostAvatarBtn);
            make.left.equalTo(_hostNameL.mas_right).offset(6);
        }];
    }
}

-(void)clickKeyBtn {
    [_chatTool.chatTF becomeFirstResponder];
    _botBtnFunView.hidden = YES;
    _chatTool.hidden = NO;
}
-(void)clikcCloseLiveBtn {
    if (self.playCtrEvent) {
        self.playCtrEvent(@"控制-关闭", @{});
    }
}
-(void)clikcShareBtn {
    //分享
    NSDictionary *shareDic = @{@"hostName":minstr([_playDic valueForKey:@"user_nickname"]),@"thumb_s":minstr([_playDic valueForKey:@"avatar_thumb"]),@"hostId":minstr([_playDic valueForKey:@"uid"]),@"liveTitle":minstr([_playDic valueForKey:@"title"])};
    [YBShareView showShareWithType:RKShareType_Liveing parameter:shareDic commplete:^(int codeEvent, NSString *nums) {
        
    }];
    
}
-(void)clikcMsgBtn {
    //私信
    if (self.playCtrEvent) {
        self.playCtrEvent(@"控制-私信", @{});
    }
}
-(void)clikcGiftBtn {
    //礼物打赏
    YBWeakSelf;
    /*
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Live.getGiftList" Dic:nil Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            YBGiftPage *gView = [YBGiftPage showGiftWithData:infoA complete:^(int eventCode,NSDictionary *giftDic) {
                if (eventCode == -1) {
                    //关闭
                }else {
                    //发送socket
                    [weakSelf sendGiftInfo:giftDic];
                }
            }];
            gView.receiveID =  minstr(_liveUid);;
            gView.receiveStream = minstr(_liveStream);
            gView.sendType = SendGiftType_Live;
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
     */
    YBGiftPage *gView = [YBGiftPage showGiftViewComplete:^(int eventCode,NSDictionary *giftDic) {
        if (eventCode == -1) {
            //关闭
        }else {
            //发送socket
            [weakSelf sendGiftInfo:giftDic];
        }
    }];
    gView.receiveID =  minstr(_liveUid);;
    gView.receiveStream = minstr(_liveStream);
    gView.sendType = SendGiftType_Live;
    [gView requestGiftData];
}
-(void)sendGiftInfo:(NSDictionary *)giftDic {
    if (self.playCtrEvent) {
        self.playCtrEvent(@"控制-礼物", giftDic);
    }
}

-(void)clikcCartBtn {
    _onsaleView = [YBOnSaleView showSaleWithAnchor:NO andAnchorID:minstr(_liveUid)];
    YBWeakSelf;
    _onsaleView.onSaleEvent = ^(OnSaleType saleCtrEvent,NSDictionary *goodsDic) {
        if (saleCtrEvent == OnSaleType_Info) {
            //用户端只能查看详情
            [weakSelf goGoodsInfo:goodsDic];
        }
    };
}
-(void)goGoodsInfo:(NSDictionary *)goodsDic {
    
    
    [PublicObj checkGoodsExistenceWithID:minstr([goodsDic valueForKey:@"id"]) Existence:^(int code, NSString *msg) {
        if (code ==0) {
            if ([minstr([goodsDic valueForKey:@"type"]) isEqual:@"1"]) {
                OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
                detail.goodsID = minstr([goodsDic valueForKey:@"id"]);
                detail.liveUid= _liveUid;
                [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];

            }else{
                CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                detail.goodsID = minstr([goodsDic valueForKey:@"id"]);
                detail.backTags = @"live";
                detail.liveUid= _liveUid;
                [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];

            }

        }else{
            [MBProgressHUD showError:msg];

        }
    }];
}
#pragma mark - 连麦开始
//用户-主播连麦
-(void)clickLinkCtrBtn:(UIButton *)sender {
    if (_linkCtrBtn.selected) {
        //连麦中
        [self userEndLinkMic];
    }else {
        //未连麦
        if (_linkMicAskTimer) {
            [MBProgressHUD showPop:YZMsg(@"您已申请，请稍等")];
            return;
        }
        YBWeakSelf;
        [MBProgressHUD showMessage:@""];
        [YBNetworking postWithUrl:@"Linkmic.isMic" Dic:@{@"liveuid":_liveUid} Suc:^(int code, id info, NSString *msg) {
            [MBProgressHUD hideHUD];
            if (code == 0) {
                [MBProgressHUD showPop:YZMsg(@"连麦请求已发送")];
                [weakSelf userStartLinkMic];
            }else {
                [MBProgressHUD showPop:msg];
            }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];
        }];
    }
}
-(void)userStartLinkMic {
    //发送socket
    if (self.playCtrEvent) {
        self.playCtrEvent(@"控制-用户申请连麦",@{});
    }
    if (!_linkMicAskTimer) {
        _linkMicAskLast = 11;
        _linkMicAskTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(linkMicAskEvent) userInfo:nil repeats:YES];
    }
}
-(void)linkMicAskEvent {
    _linkMicAskLast -- ;
    if (_linkMicAskLast <= 0) {
        [self destroyLinkMicAskTimer];
    }
}
-(void)destroyLinkMicAskTimer {
    if (_linkMicAskTimer) {
        [_linkMicAskTimer invalidate];
        _linkMicAskTimer = nil;
    }
}
-(void)userEndLinkMic {
    YBWeakSelf;
    NSDictionary *contentDic = @{@"title":@"",@"msg":YZMsg(@"是否断开连麦"),@"left":YZMsg(@"取消"),@"right":YZMsg(@"确定"),@"richImg":@""};
   _disconnectAlert = [YBAlertView showAlertView:contentDic complete:^(int eventType) {
       if (eventType == 1) {
           [weakSelf userSendLinkDisconnectSocket];
           [weakSelf stopUserLinkView];
       }
    }];
}

-(void)stopUserLinkView {
    
    _linkCtrBtn.selected = NO;
    [self destroyLinkMicAskTimer];
    if (_disconnectAlert) {
        [_disconnectAlert removeFromSuperview];
        _disconnectAlert = nil;
    }
    [self destroyLinkMicView];
    
}
/** socket主播同意-拒绝连麦 */
-(void)socketHostIsAgree:(BOOL)isAgree andSocDic:(NSDictionary *)socDic {
    [self destroyLinkMicAskTimer];
    if (isAgree) {
        //同意
        YBWeakSelf;
        NSDictionary *parDic = @{@"uid":[Config getOwnID],@"liveuid":minstr(_liveUid)};
        [YBNetworking postWithUrl:@"Linkmic.RequestLVBAddrForLinkMic" Dic:parDic Suc:^(int code, id info, NSString *msg) {
            if (code == 0) {
                NSDictionary *infoDic = [info firstObject];
                NSDictionary *subDic = @{@"userid":[Config getOwnID],
                                         @"uname":[Config getOwnNicename],
                                         @"playurl":minstr([infoDic valueForKey:@"playurl"]),
                                         @"pushurl":minstr([infoDic valueForKey:@"pushurl"]),
                };
                [weakSelf createLinkMicView:subDic];
            }else {
                [MBProgressHUD showPop:msg];
            }
        } Fail:^(id fail) {
            
        }];
    }else {
        //拒绝
        _linkCtrBtn.selected = NO;
    }
}
-(void)createLinkMicView:(NSDictionary *)linkDic {
    [self destroyLinkMicView];
    _linkCtrBtn.selected = YES;
    _linkMicView = [YBTxLinkMicView createLinkMicViewOnSuper:self andHostToHost:NO];
    _linkMicView.linkDic = linkDic;
    [_linkMicView linkMicShowViewHaveCloseBtn:NO];
    YBWeakSelf;
    _linkMicView.linkMicEvent = ^(TxLinkEventType eventType, NSDictionary *eventDic) {
        [weakSelf linkMicViewEvent:eventType andDic:eventDic];
    };
    
}
-(void)linkMicViewEvent:(TxLinkEventType)eventType andDic:(NSDictionary *)eventDic {
    switch (eventType) {
        case TxLinkEventType_StartPush:{
            //开始推流-发送socket
            YBWeakSelf;
//            NSDictionary *postDic = @{@"originStreamUrl":minstr([_playDic valueForKey:@"pull"])};
            NSDictionary *postDic = @{@"uid":[Config getOwnID],@"liveuid":minstr(_liveUid),@"stream":minstr(_liveStream)};
            [YBNetworking postWithUrl:@"Linkmic.RequestPlayUrlWithSignForLinkMic" Dic:postDic Suc:^(int code, id info, NSString *msg) {
                if (code == 0) {
                    NSDictionary *infoDic = [info firstObject];
                    NSString *streamUrlWithSignature = [infoDic valueForKey:@"streamUrlWithSignature"];
                    if (weakSelf.playCtrEvent) {
                        NSDictionary *eventDic = @{@"playurl":minstr([_linkMicView.linkDic valueForKey:@"playurl"]),
                                                   @"streamUrlWithSignature":streamUrlWithSignature,
                        };
                        weakSelf.playCtrEvent(@"控制-用户连麦重新拉流", eventDic);
                    }
                }else {
                    [MBProgressHUD showPop:msg];
                }
            } Fail:^(id fail) {
                
            }];
            
        }break;
        case TxLinkEventType_StopPush:{
            //结束推流
            [self userSendLinkDisconnectSocket];
        }break;
        case TxLinkEventType_LinkDisconnect:{
            //断开连麦
            [self userSendLinkDisconnectSocket];
        }break;
            
        default:
            break;
    }
}
-(void)destroyLinkMicView {
    if (_linkMicView) {
        [_linkMicView linkMicViewDismiss];
        _linkMicView = nil;
    }
}
-(void)userSendLinkDisconnectSocket {
    if (self.playCtrEvent) {
        self.playCtrEvent(@"控制-用户断开连麦",@{});
    }
}

/** socket主播下麦用户 */
-(void)socketHostDisconnect:(NSDictionary *)linkDic {
    [self stopUserLinkView];
}
/** socket主播忙碌 */
-(void)socketHostBusy:(NSDictionary *)busyDic {
    [self stopUserLinkView];
}
/** socket主播超时 */
-(void)socketHostTimeout:(NSDictionary *)timeoutDic {
    [self stopUserLinkView];
}
#pragma mark 连麦结束

/** 主播-主播:对方主播id */
-(void)showToHostInfoWithId:(NSString *)toHostId;{
    [self destroyPkTohostInfo];
    _pkTohostInfoV = [YBAnchorLinkInfo showHostInfoWithSuperView:self];
    [_pkTohostInfoV reqToHostInfo:toHostId];
    YBWeakSelf;
    _pkTohostInfoV.attentEvent = ^(int isAttent) {
        weakSelf.pkFollowBtn.hidden = isAttent;
    };
    //遮罩【控制层级响应点击,实际UI显示在_pkTohostInfoV里的子视图】
    _pkFollowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _pkFollowBtn.backgroundColor = UIColor.clearColor;
    [_pkFollowBtn addTarget:self action:@selector(clickPkFollow) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:_pkFollowBtn];
    [_pkFollowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right);
        make.bottom.equalTo(_pkTohostInfoV.mas_bottom);
        make.width.height.mas_equalTo(40);
    }];
}
-(void)clickPkFollow {
    _pkFollowBtn.hidden = YES;
    [_pkTohostInfoV updateFollow];
}
-(void)destroyPkTohostInfo {
    if (_pkTohostInfoV) {
        [_pkTohostInfoV removeFromSuperview];
        _pkTohostInfoV = nil;
        [_pkFollowBtn removeFromSuperview];
        _pkFollowBtn = nil;
    }
}
#pragma mark - PK
/** PK 开始 */
-(void)socketLinkPKStart:(NSDictionary *)pkDic;{
    [self destroyPkView];
    NSString *pkTime = @"300";
    if (pkDic) {
        pkTime = minstr([pkDic valueForKey:@"pk_time"]);
    }
    _pkView = [[YBAnchorPKView alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight, _window_width, _window_width*2/3+20) andTime:pkTime];
    YBWeakSelf;
    _pkView.pkViewEvent = ^{
        [weakSelf destroyPkView];
    };
    //[_showUIView addSubview:_pkView];
    [self addSubview:_pkView];
    
    [self bringToFront];
}
///层级关系处理
-(void)bringToFront {
    [_showUIView bringSubviewToFront:_chatTool];
    [_showUIView bringSubviewToFront:_liveChatView];
    [_showUIView bringSubviewToFront:_goodsBriefView];
    [_showUIView bringSubviewToFront:haohualiwuV];
    [_showUIView bringSubviewToFront:_paintedShowRegion];
    [_showUIView bringSubviewToFront:liansongliwubottomview];
    [_showUIView bringSubviewToFront:_userAnimation];
    [_showUIView bringSubviewToFront:_turntableView];
}
/** PK 结果 */
-(void)socketLinkPKReult:(NSDictionary *)pkDic;{
    int win;
    if ([minstr([pkDic valueForKey:@"win_uid"]) isEqual:@"0"]) {
        win = 0;
    }else if ([minstr([pkDic valueForKey:@"win_uid"]) isEqual:minstr([_playDic valueForKey:@"uid"])]) {
        win = 1;
    }else{
        win = 2;
    }
    [_pkView showPkResult:pkDic andWin:win];
}
/** PK 进度 */
-(void)socketLinkPKProgress:(NSDictionary *)pkDic;{
    NSString *blueNum;
    NSString *redNum;
    CGFloat progress = 0.0;
    if ([minstr([pkDic valueForKey:@"pkuid1"]) isEqual:minstr([_playDic valueForKey:@"uid"])]) {
        blueNum = minstr([pkDic valueForKey:@"pktotal1"]);
        redNum = minstr([pkDic valueForKey:@"pktotal2"]);
    }else{
        redNum = minstr([pkDic valueForKey:@"pktotal1"]);
        blueNum = minstr([pkDic valueForKey:@"pktotal2"]);
    }
    if ([blueNum isEqual:@"0"] && [redNum isEqual:@"0"]) {
        progress = 0.5;
    }else{
        if ([blueNum isEqual:@"0"]) {
            progress = 0.2;
        }else if ([redNum isEqual:@"0"]) {
            progress = 0.8;
        }else{
            CGFloat ppp = [blueNum floatValue]/([blueNum floatValue] + [redNum floatValue]);
            if (ppp < 0.2) {
                progress = 0.2;
            }else if (ppp > 0.8){
                progress = 0.8;
            }else{
                progress = ppp;
            }
        }
    }
    [_pkView updateProgress:progress withBlueNum:blueNum withRedNum:redNum];
}
-(void)destroyPkView {
    if (_pkView) {
        [_pkView removeTimer];
        [_pkView removeFromSuperview];
        _pkView = nil;
        [_pkFollowBtn removeFromSuperview];
        _pkFollowBtn = nil;
    }
}

#pragma mark - /** 键盘弹起事件: 目前只处理 PK和连麦 的界面相对位置不变*/
-(void)keyBoardNoticeIsShow:(BOOL)isShow andHeight:(CGFloat)height;{
    _pkView.top = 130+statusbarHeight + height;
    [_linkMicView keyBoardNoticeIsShow:isShow andHeight:height];
}

#pragma mark - 进房间更新用户列表、映票
-(void)enterroomUpdateInfo:(NSDictionary *)roomDic {
    
    _guardInfo = @{};
    if ([[roomDic valueForKey:@"guard"] isKindOfClass:[NSDictionary class]]) {
        _guardInfo = [roomDic valueForKey:@"guard"];
    }
    NSArray *userListA = [NSArray arrayWithArray:[roomDic valueForKey:@"userlists"]];
    [_userListView updateListCount:userListA];
    
    NSString *isAttent = minstr([roomDic valueForKey:@"isattention"]);
    if ([isAttent isEqual:@"0"]) {
        [_followBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(24);
            make.centerY.equalTo(_hostAvatarBtn);
            make.left.equalTo(_hostNameL.mas_right).offset(6);
        }];
    }
    
    [self updateVotes:minstr([roomDic valueForKey:@"votestotal"])];
    [self updateGuardNums:minstr([roomDic valueForKey:@"guard_nums"])];
    
    //展示的商品
    NSDictionary *goodsDic = [roomDic valueForKey:@"show_goods"];
    NSString *goodsID = minstr([goodsDic valueForKey:@"goodsid"]);
    if (![PublicObj checkNull:goodsID] && ![goodsID isEqual:@"0"]) {
        [self goodsBriefIsShow:YES withInfo:goodsDic];
    }
    
}
#pragma mark - 用户点亮
-(void)socketLightAnimation {
    [_botBtnFunView layoutIfNeeded];
    
    starX = CGRectGetMaxX(_botBtnFunView.frame)-20 ;
    starY = _botBtnFunView.frame.origin.y;
    starImage = [[UIImageView alloc]initWithFrame:CGRectMake(starX, starY, 30, 30)];
    starImage.contentMode = UIViewContentModeScaleAspectFit;
    NSMutableArray *array = [NSMutableArray arrayWithObjects:@"plane_heart_cyan.png",@"plane_heart_pink.png",@"plane_heart_red.png",@"plane_heart_yellow.png",@"plane_heart_heart.png", nil];
    NSInteger random = arc4random()%array.count;
    starImage.image = [UIImage imageNamed:[array objectAtIndex:random]];
    [UIView animateWithDuration:0.2 animations:^{
        starImage.alpha = 1.0;
        starImage.frame = CGRectMake(starX+random - 10, starY-random - 30, 30, 30);
        CGAffineTransform transfrom = CGAffineTransformMakeScale(1.3, 1.3);
        starImage.transform = CGAffineTransformScale(transfrom, 1, 1);
    }];
    [_showUIView addSubview:starImage];
    [_showUIView sendSubviewToBack:starImage];
    CGFloat finishX = _window_width - round(arc4random() % 200);
    //  动画结束点的Y值
    CGFloat finishY = 200;
    //  imageView在运动过程中的缩放比例
    CGFloat scale = round(arc4random() % 2) + 0.7;
    // 生成一个作为速度参数的随机数
    CGFloat speed = 1 / round(arc4random() % 900) + 0.6;
    //  动画执行时间
    NSTimeInterval duration = 4 * speed;
    //  如果得到的时间是无穷大，就重新附一个值（这里要特别注意，请看下面的特别提醒）
    if (duration == INFINITY) duration = 2.412346;
    //  开始动画
    [UIView beginAnimations:nil context:(__bridge void *_Nullable)(starImage)];
    //  设置动画时间
    [UIView setAnimationDuration:duration];
    //  设置imageView的结束frame
    starImage.frame = CGRectMake( finishX, finishY, 30 * scale, 30 * scale);
    //  设置渐渐消失的效果，这里的时间最好和动画时间一致
    [UIView animateWithDuration:duration animations:^{
        starImage.alpha = 0;
    }];
    //  结束动画，调用onAnimationComplete:finished:context:函数
    [UIView setAnimationDidStopSelector:@selector(onAnimationComplete:finished:context:)];
    //  设置动画代理
    [UIView setAnimationDelegate:self];
    [UIView commitAnimations];
    
}
/// 动画完后销毁iamgeView
- (void)onAnimationComplete:(NSString *)animationID finished:(NSNumber *)finished context:(void *)context{
    if ([finished isEqual:[NSNumber numberWithBool:YES]]) {
        UIImageView *imageViewsss = (__bridge UIImageView *)(context);
        [imageViewsss removeFromSuperview];
        imageViewsss = nil;
    }
}
#pragma mark - 清理聊天消息
-(void)clearChatList;{
    [_liveChatView clearChatList];
}
#pragma mark - 发言
-(void)socketChatDic:(NSDictionary *)chatDic {
    [_liveChatView reloadMsg:chatDic];
}
#pragma mark - 用户离开
-(void)socketUserLeave:(NSDictionary *)leaveDic {
    NSLog(@"leave");
    [_userListView userEventOfType:UserEvent_Leave andInfo:leaveDic];
}
#pragma mark - 用户进入
-(void)socketUserEnter:(NSDictionary *)enterDic {
    NSLog(@"enter");
    [_userListView userEventOfType:UserEvent_Enter andInfo:enterDic];
    NSString *vipType = [NSString stringWithFormat:@"%@",[[enterDic valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *guardType = [NSString stringWithFormat:@"%@",[[enterDic valueForKey:@"ct"] valueForKey:@"guard_type"]];
    //vip暂时没有动画，只有守护有
    if ([vipType isEqual:@"1"] || [guardType isEqual:@"1"] || [guardType isEqual:@"2"]) {
        //进场动画
        [_userAnimation addUserMove:enterDic];
    }
    
    NSDictionary *ctDic = [enterDic valueForKey:@"ct"];
    NSDictionary *chatDic = @{@"userName":minstr([ctDic valueForKey:@"user_nickname"]),
                              @"contentChat":YZMsg(@" 进入了直播间"),
                              @"id":minstr([ctDic valueForKey:@"id"]),
                              @"titleColor":@"userLogin",
                              @"usertype":minstr([ctDic valueForKey:@"usertype"]),
                              @"guard_type":minstr([ctDic valueForKey:@"guard_type"]),
    };
    [self socketChatDic:chatDic];
}
#pragma mark - 送礼物
-(void)socketSendGift:(NSDictionary *)giftDic {
    
    NSDictionary *ctDic = [giftDic valueForKey:@"ct"];
    NSMutableDictionary *showDic = @{@"avatar":minstr([giftDic valueForKey:@"uhead"]),
                                     @"nickname":minstr([giftDic valueForKey:@"uname"]),
                                     @"paintedHeight":[giftDic valueForKey:@"paintedHeight"]?[giftDic valueForKey:@"paintedHeight"]:@"",
                                     @"paintedPath":[giftDic valueForKey:@"paintedPath"]?[giftDic valueForKey:@"paintedPath"]:@[],//数组
                                     @"paintedWidth":[giftDic valueForKey:@"paintedWidth"]?[giftDic valueForKey:@"paintedWidth"]:@"",
    }.mutableCopy;
    [showDic addEntriesFromDictionary:ctDic];
    [self showGift:showDic];
    
    NSString *votes = minstr([ctDic valueForKey:@"votestotal"]);
    [self updateVotes:votes];
}
#pragma mark - 展示主播介绍的商品
-(void)socketOnSale:(NSDictionary *)onSaleDic; {
    //1-展示  0-隐藏
    BOOL show = [minstr([onSaleDic valueForKey:@"action"]) boolValue];
    [self goodsBriefIsShow:show withInfo:onSaleDic];
    
}
#pragma mark - 更新映票
-(void)updateVotes:(NSString *)votes {
    _hostVotesBtn.hidden = NO;
    /// 青少年
    int youngSwitch = [YBYoungManager shareInstance].youngSwitch;
    if (youngSwitch == 1) {
        _hostVotesBtn.hidden = YES;
    }
    NSString *votesStr = [NSString stringWithFormat:@"%@ %@",[common name_votes],votes];
    [_hostVotesBtn setTitle:votesStr forState:0];
}
#pragma mark - 更新守护数量
-(void)updateGuardNums:(NSString *)nums {
    if ([nums intValue] <= 0) {
        return;
    }
    NSString *guardStr = [NSString stringWithFormat:YZMsg(@"守护 %@人"),nums];
    [_hostGuardBtn setTitle:guardStr forState:0];
    [self layoutSubviews];
}
#pragma mark - /** 计时器刷新列表 */
-(void)timerReloadList {
    [_userListView timerReloadList];
}
#pragma mark - /** 私信未读数 */
-(void)updateUnread:(int)unread {
    _redPoint.hidden = YES;
    if (unread > 0) {
        _redPoint.hidden = NO;
    }
}
#pragma mark - 打赏开始
-(void)showGift:(NSDictionary *)giftDic {
    NSString *type = minstr([giftDic valueForKey:@"type"]);
    if (!continueGifts) {
        continueGifts = [[continueGift alloc]initWithFrame:CGRectMake(0, 0, liansongliwubottomview.width, liansongliwubottomview.height)];
        [liansongliwubottomview addSubview:continueGifts];
        //初始化礼物空位
        [continueGifts initGift];
        YBWeakSelf;
        continueGifts.rkPaintedEvent = ^(NSDictionary *giftDic) {
            [weakSelf showPaintedGift:giftDic];
        };
    }
    if ([type isEqual:@"1"]) {
        [self expensiveGift:giftDic];
    }else{
        //手绘以及普通礼物都进 continueGifts 由它执行队列
        [continueGifts GiftPopView:giftDic andLianSong:@"Y"];
    }
}
-(void)showPaintedGift:(NSDictionary *)giftDic {
    //手绘显示动画
    _paintedShowRegion.giftPathStr = minstr([giftDic valueForKey:@"gifticon"]);
    _paintedShowRegion.paintedWidth = [minstr([giftDic valueForKey:@"paintedWidth"]) floatValue];
    _paintedShowRegion.paintedHeight = [minstr([giftDic valueForKey:@"paintedHeight"]) floatValue];
    _paintedShowRegion.paintedPointArray = [NSArray arrayWithArray:[giftDic valueForKey:@"paintedPath"]];
}
/************ 礼物弹出及队列显示开始 *************/
-(void)expensiveGiftdelegate:(NSDictionary *)giftData{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]init];
        haohualiwuV.delegate = self;
        [_showUIView addSubview:haohualiwuV];
    }
    if (giftData == nil) {
        
    }else {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive];
    }
}
-(void)expensiveGift:(NSDictionary *)giftData{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]init];
        haohualiwuV.delegate = self;
        [_showUIView addSubview:haohualiwuV];
    }
    if (giftData == nil) {
     
    }else {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive];
    }
}
#pragma mark - 销毁
-(void)destroyCtrSubView {
    _avatarEffect.hidden = NO;
    [self goodsBriefIsShow:NO withInfo:@{}];
    if (continueGifts) {
        [continueGifts stopTimerAndArray];
        [continueGifts initGift];
        [continueGifts removeFromSuperview];
        continueGifts = nil;
    }
    if (haohualiwuV) {
        [haohualiwuV stopHaoHUaLiwu];
        [haohualiwuV removeFromSuperview];
        haohualiwuV.expensiveGiftCount = nil;
        haohualiwuV = nil;
    }
    [_userAnimation removeAnimation];
    liansongliwubottomview.top = statusbarHeight+200;
    if (_onsaleView) {
        [_onsaleView removeFromSuperview];
    }
    if (_paintedShowRegion) {
        [_paintedShowRegion destroyPaitend];
    }
    [self desTroyDaytaskView];
    _turntableView.hidden = YES;
    [self removeShouhuView];
    [self destroyLinkMicAskTimer];
    [self userSendLinkDisconnectSocket];
    [self stopUserLinkView];
    [self destroyPkView];
}

#pragma mark - 打赏结束

#pragma mark - 公屏聊天处点击回调
-(void)roomChatCallBack:(NSString *)eventType andEventInfo:(NSDictionary *)eventDic {
    
    if ([eventType isEqual:@"房间聊天-用户信息"] && self.playCtrEvent) {
        [_chatTool.chatTF resignFirstResponder];
        //@{@"id":model.userID,@"name":model.userName}
        self.playCtrEvent(@"控制-信息卡", eventDic);
    }
    
}
#pragma mark - 用户列表回调
-(void)userListCallBack:(NSString *)eventType andEventInfo:(NSDictionary *)eventDic{
    if ([eventType isEqual:@"用户列表-用户信息"] && self.playCtrEvent) {
        [_chatTool.chatTF resignFirstResponder];
        //@{@"id":model.userID,@"name":model.userName}
        self.playCtrEvent(@"控制-信息卡", eventDic);
    }
}

#pragma mark - 聊天工具栏回调
-(void)chatToolCallBack:(NSString *)eventType andContent:(NSString *)eventContent{
    //去发送socke；
    if ([eventType isEqual:@"直播聊天-发送"] && self.playCtrEvent) {
        self.playCtrEvent(@"控制-直播聊天", @{@"ct":eventContent});
    }
    
}
#pragma mark - 商品简介-展示/隐藏
-(void)goodsBriefIsShow:(BOOL)showBrief withInfo:(NSDictionary *)infoDic; {
    int youngSwitch = [YBYoungManager shareInstance].youngSwitch;
    if (youngSwitch == 1 && showBrief == YES) {
        return;
    }
    _goodsBriefView.hidden = !showBrief;
    _goodsBriefView.touserID = _liveUid;
    _goodsBriefView.goodsDic = infoDic;
    [UIView animateWithDuration:0.5 animations:^{
        if (showBrief) {
            _liveChatView.y = _window_height-ChatViewHeight-60-ShowDiff-GoodsBriefHeight;
        }else{
            _liveChatView.y = _window_height-ChatViewHeight-60-ShowDiff;
        }
    }];
}
#pragma mark - 每日任务、转盘代理
/** 点击图片回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index {
    NSLog(@"点击轮播----------index:%ld",index);
    if (index == 0) {
        //每日任务
        [self desTroyDaytaskView];
        _dayTaskView = [YBDayTaskView showRoomDayTask];
        _dayTaskView.liveuid = minstr(_liveUid);
        [_dayTaskView requestListData];
    }else {
        //转盘
        _turntableView.hidden = NO;
        _turntableView.zhuboMsg = _playDic;
        [_turntableView show];
    }
}

/** 图片滚动回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didScrollToIndex:(NSInteger)index {
    self.roomGamePage.currentPage = index;
}
#pragma mark - 转盘
- (void)turntableZhongjianla{
    //转盘和礼物不会同时展现,故这里可以不处理
}
#pragma mark - 守护
-(void)clickGuardBtn {
    
    if (!gShowView) {
        gShowView = [[guardShowView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andUserGuardMsg:_guardInfo andLiveUid:minstr([_playDic valueForKey:@"uid"])];
        gShowView.delegate = self;
        [_showUIView addSubview:gShowView];
    }
    [gShowView show];
}
- (void)buyOrRenewGuard{
    [self removeShouhuView];
    if (!guardView) {
        guardView = [[shouhuView alloc]init];
        guardView.liveUid = minstr([_playDic valueForKey:@"uid"]);
        guardView.stream = minstr([_playDic valueForKey:@"stream"]);
        guardView.delegate = self;
        guardView.guardType = minstr([_guardInfo valueForKey:@"type"]);
        [_showUIView addSubview:guardView];
    }
    [guardView show];
}
- (void)removeShouhuView{
    if (guardView) {
        [guardView removeFromSuperview];
        guardView = nil;
    }
    if (gShowView) {
        [gShowView removeFromSuperview];
        gShowView = nil;
    }
   
}
- (void)buyShouhuSuccess:(NSDictionary *)dic{
    _guardInfo = dic;
    if (self.playCtrEvent) {
        self.playCtrEvent(@"控制-购买守护",dic);
    }
}
- (void)socketUpdateGuardMsg:(NSDictionary *)dic{
    
    [self updateVotes:minstr([dic valueForKey:@"votestotal"])];
    [self updateGuardNums:minstr([dic valueForKey:@"guard_nums"])];
    [self timerReloadList];
}

#pragma mark - set/get
- (UIImageView *)avatarEffect {
    if (!_avatarEffect) {
        _avatarEffect = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        _avatarEffect.contentMode = UIViewContentModeScaleAspectFill;
        _avatarEffect.clipsToBounds = YES;
        UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
        UIVisualEffectView *effectview = [[UIVisualEffectView alloc] initWithEffect:blur];
        effectview.frame = CGRectMake(0, 0,_window_width,_window_height);
        [_avatarEffect addSubview:effectview];
    }
    return _avatarEffect;
}

- (UIScrollView *)horScrollView {
    if (!_horScrollView) {
        _horScrollView = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        _horScrollView.backgroundColor = UIColor.clearColor;
        _horScrollView.contentSize = CGSizeMake(_window_width*2, 0);
        [_horScrollView setContentOffset:CGPointMake(_window_width, 0) animated:NO];
        _horScrollView.pagingEnabled = YES;
        _horScrollView.showsHorizontalScrollIndicator = NO;
        _horScrollView.bounces = NO;
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tapGes)];
        tap.delegate = self;
        [_horScrollView addGestureRecognizer:tap];
        
        _showUIView = [[UIView alloc]initWithFrame:CGRectMake(_window_width, 0, _window_width, _window_height)];
        _showUIView.backgroundColor = UIColor.clearColor;
        [_horScrollView addSubview:_showUIView];
        
        //手绘礼物
        [_showUIView addSubview:self.paintedShowRegion];
        //连送礼物显示区域
        liansongliwubottomview = [[UIView alloc]init];
        liansongliwubottomview.userInteractionEnabled = NO;
        liansongliwubottomview.backgroundColor = UIColor.clearColor;
        [_showUIView addSubview:liansongliwubottomview];
        liansongliwubottomview.frame = CGRectMake(0, statusbarHeight + 200,_window_width,140);
        
    }
    return _horScrollView;
}

- (UIView *)hostInfoView {
    if (!_hostInfoView) {
        _hostInfoView = [[UIView alloc]init];
        _hostInfoView.layer.cornerRadius = 17;
        _hostInfoView.layer.masksToBounds = YES;
        _hostInfoView.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        
        _hostAvatarBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _hostAvatarBtn.layer.masksToBounds = YES;
        _hostAvatarBtn.layer.cornerRadius = 15;
        [_hostAvatarBtn.imageView setContentMode:UIViewContentModeScaleAspectFill];
        _hostAvatarBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
        _hostAvatarBtn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
        [_hostAvatarBtn addTarget:self action:@selector(clickHostAvatarBtn) forControlEvents:UIControlEventTouchUpInside];
        [_hostInfoView addSubview:_hostAvatarBtn];
        [_hostAvatarBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(_hostInfoView);
            make.left.equalTo(_hostInfoView).offset(2);
            make.width.height.mas_equalTo(30);
        }];
        
        _hostNameL = [[UILabel alloc]init];
        _hostNameL.font = SYS_Font(13);
        _hostNameL.textColor = RGB_COLOR(@"#ffffff", 1);
        [_hostInfoView addSubview:_hostNameL];
        [_hostNameL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(_hostAvatarBtn.mas_centerY).multipliedBy(0.65);
            make.left.equalTo(_hostAvatarBtn.mas_right).offset(5);
        }];
        
        _hostIDL = [[UILabel alloc]init];
        _hostIDL.font = SYS_Font(10);
        _hostIDL.textColor = RGB_COLOR(@"#f0f0f0", 1);
        [_hostInfoView addSubview:_hostIDL];
        [_hostIDL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(_hostNameL);
            make.centerY.equalTo(_hostAvatarBtn.mas_centerY).multipliedBy(1.53);
        }];
        
        _followBtn = [[UIButton alloc]init];
        [_followBtn setTitle:YZMsg(@"关注") forState:0];
        _followBtn.titleLabel.font = SYS_Font(10);
        _followBtn.layer.cornerRadius = 12;
        _followBtn.layer.masksToBounds = YES;
        _followBtn.backgroundColor = Pink_Cor;
        _followBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 8, 0, 8);
        [_followBtn addTarget:self action:@selector(clickFollowBtn) forControlEvents:UIControlEventTouchUpInside];
        [_hostInfoView addSubview:_followBtn];
        [_followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(0);//默认宽度为0,进房间后更新
            make.height.mas_equalTo(24);
            make.centerY.equalTo(_hostAvatarBtn);
            make.left.equalTo(_hostNameL.mas_right).offset(6);
        }];
    }
    return _hostInfoView;
}

- (UIButton *)hostVotesBtn {
    if (!_hostVotesBtn) {
        _hostVotesBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        //无点击事件
        [_hostVotesBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        _hostVotesBtn.titleLabel.font = SYS_Font(10);
        _hostVotesBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 11, 0, 11);
        _hostVotesBtn.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        _hostVotesBtn.layer.masksToBounds = YES;
        _hostVotesBtn.layer.cornerRadius = 11;
        _hostVotesBtn.hidden = YES;
    }
    return _hostVotesBtn;
}
- (UIButton *)hostGuardBtn {
    if (!_hostGuardBtn) {
        _hostGuardBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _hostGuardBtn.titleLabel.font = SYS_Font(10);
        _hostGuardBtn.layer.masksToBounds = YES;
        _hostGuardBtn.layer.cornerRadius = 11;
        [_hostGuardBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        _hostGuardBtn.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        [_hostGuardBtn setTitle:YZMsg(@"守护 虚位以待") forState:0];
        [_hostGuardBtn setImage:[UIImage imageNamed:@"pub_white_right"] forState:0];
        [_hostGuardBtn addTarget:self action:@selector(clickGuardBtn) forControlEvents:UIControlEventTouchUpInside];
        _hostGuardBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 8, 0, 5);
    }
    return _hostGuardBtn;;
}
- (SDCycleScrollView *)roomGameCycle {
    if (!_roomGameCycle) {
        
        _roomGameCycle = [[SDCycleScrollView alloc]init];
        _roomGameCycle.backgroundColor = [UIColor clearColor];
        _roomGameCycle.bannerImageViewContentMode = UIViewContentModeScaleAspectFit;
        _roomGameCycle.delegate = self;
        _roomGameCycle.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
        _roomGameCycle.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        _roomGameCycle.autoScrollTimeInterval = 3.0;
        _roomGameCycle.currentPageDotColor = [UIColor whiteColor];
        _roomGameCycle.pageDotColor = [[UIColor whiteColor] colorWithAlphaComponent:0.4];
        _roomGameCycle.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
        _roomGameCycle.imageURLStringsGroup = _roomGameArray;
        
    }
    return _roomGameCycle;
}
- (YBPageControl *)roomGamePage {
    if (!_roomGamePage) {
        _roomGamePage = [[YBPageControl alloc]init];
        _roomGamePage.numberOfPages = _roomGameArray.count;
        _roomGamePage.currentPage = 0;
        _roomGamePage.pageIndicatorTintColor = [UIColor grayColor];
        _roomGamePage.currentPageIndicatorTintColor = [UIColor whiteColor];
//        _roomGamePage.transform = CGAffineTransformScale(CGAffineTransformIdentity, 0.7, 0.7);
        _roomGamePage.userInteractionEnabled = NO;
    }
    return _roomGamePage;
}
- (turntableView *)turntableView {
    if (!_turntableView) {
        _turntableView = [[turntableView alloc]init];
        _turntableView.zhuboMsg = _playDic;
        _turntableView.delegate = self;
    }
    return _turntableView;
}
- (YBUserListView *)userListView {
    if (!_userListView) {
        YBWeakSelf;
        _userListView = [[YBUserListView alloc]init];
        _userListView.backgroundColor = UIColor.clearColor;
        if (![PublicObj checkNull:_liveUid] && ![PublicObj checkNull:_liveStream]) {
            _userListView.liveUid = minstr(_liveUid);
            _userListView.liveStream = minstr(_liveStream);
        }
        _userListView.listEvent = ^(NSString *eventStr, NSDictionary *eventDic) {
            [weakSelf userListCallBack:eventStr andEventInfo:eventDic];
        };
    }
    return _userListView;
}

- (YBLiveChatView *)liveChatView {
    if (!_liveChatView) {
        YBWeakSelf;
        _liveChatView = [[YBLiveChatView alloc]initWithFrame:CGRectMake(10,_window_height-ChatViewHeight-60-ShowDiff, ChatViewWidth, ChatViewHeight)];
        _liveChatView.backgroundColor = UIColor.clearColor;
        _liveChatView.liveChatEvent = ^(NSString *eventStr, NSDictionary *eventDic) {
            [weakSelf roomChatCallBack:eventStr andEventInfo:eventDic];
        };
    }
    return _liveChatView;
}
- (YBGoodsBriefView *)goodsBriefView {
    if (!_goodsBriefView) {
        _goodsBriefView = [[YBGoodsBriefView alloc]initWithFrame:CGRectMake(10, _window_height-GoodsBriefHeight-60-ShowDiff, GoodsBriefWidth, GoodsBriefHeight)];
        _goodsBriefView.backgroundColor = UIColor.clearColor;
        _goodsBriefView.hidden = YES;
    }
    return _goodsBriefView;
}
- (YBUserEnterAnimation *)userAnimation {
    if (!_userAnimation) {
        _userAnimation = [[YBUserEnterAnimation alloc]init];
    }
    return _userAnimation;
}
- (UIButton *)linkCtrBtn {
    if (!_linkCtrBtn) {
        _linkCtrBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_linkCtrBtn addTarget:self action:@selector(clickLinkCtrBtn:) forControlEvents:UIControlEventTouchUpInside];
        [_linkCtrBtn setTitleColor:RGB_COLOR(@"#dddddd", 1) forState:0];
        [_linkCtrBtn setTitleColor:RGB_COLOR(@"#dddddd", 1) forState:UIControlStateSelected];
        [_linkCtrBtn setTitle:YZMsg(@"连麦互动") forState:0];
        [_linkCtrBtn setTitle:YZMsg(@"结束连麦") forState:UIControlStateSelected];
        _linkCtrBtn.titleLabel.font = SYS_Font(8);
        [_linkCtrBtn setImage:[UIImage imageNamed:@"直播-连控彩"] forState:0];
        [_linkCtrBtn setImage:[UIImage imageNamed:@"直播-连控彩"] forState:UIControlStateSelected];
        _linkCtrBtn.layer.cornerRadius = 8;
        _linkCtrBtn.layer.masksToBounds = YES;
        _linkCtrBtn.backgroundColor = RGB_COLOR(@"#000000", 0.4);
    }
    return _linkCtrBtn;
}
- (UIView *)botBtnFunView {
    if (!_botBtnFunView) {
        _botBtnFunView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-50-ShowDiff, _window_width, 40)];
        CGFloat btnW = 30;
        
        UIButton *keyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [keyBtn addTarget:self action:@selector(clickKeyBtn) forControlEvents:UIControlEventTouchUpInside];
        [keyBtn setImage:[UIImage imageNamed:@"live_聊天"] forState:0];
        [_botBtnFunView addSubview:keyBtn];
        [keyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.left.equalTo(_botBtnFunView.mas_left).offset(10);
        }];
        
        _closeLiveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_closeLiveBtn addTarget:self action:@selector(clikcCloseLiveBtn) forControlEvents:UIControlEventTouchUpInside];
        [_closeLiveBtn setImage:[UIImage imageNamed:@"live_关闭"] forState:0];
        [_botBtnFunView addSubview:_closeLiveBtn];
        [_closeLiveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_botBtnFunView.mas_right).offset(-10);
        }];
        
        _shareBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_shareBtn addTarget:self action:@selector(clikcShareBtn) forControlEvents:UIControlEventTouchUpInside];
        [_shareBtn setImage:[UIImage imageNamed:@"live_分享"] forState:0];
        [_botBtnFunView addSubview:_shareBtn];
        [_shareBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_closeLiveBtn.mas_left).offset(-10);
        }];
        
        _msgBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_msgBtn addTarget:self action:@selector(clikcMsgBtn) forControlEvents:UIControlEventTouchUpInside];
        [_msgBtn setImage:[UIImage imageNamed:@"live_私信"] forState:0];
        [_botBtnFunView addSubview:_msgBtn];
        [_msgBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_shareBtn.mas_left).offset(-10);
        }];
        
        _redPoint = [[UILabel alloc]init];
        _redPoint.layer.masksToBounds = YES;
        _redPoint.layer.cornerRadius = 4;
        _redPoint.backgroundColor = UIColor.redColor;
        [_botBtnFunView addSubview:_redPoint];
        _redPoint.hidden = YES;
        [_redPoint mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(8);
            make.centerY.equalTo(_msgBtn.mas_top);
            make.right.equalTo(_msgBtn.mas_right).offset(-4);
        }];
        
        _cartBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cartBtn addTarget:self action:@selector(clikcCartBtn) forControlEvents:UIControlEventTouchUpInside];
        [_cartBtn setBackgroundImage:[UIImage imageNamed:@"live_车底"] forState:0];
        [_cartBtn setImage:[UIImage imageNamed:@"live_车"] forState:0];
        [_botBtnFunView addSubview:_cartBtn];
        _cartBtn.hidden = YES;
        [_cartBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(btnW);
            make.width.mas_equalTo(0);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_msgBtn.mas_left);
        }];
        
        _giftBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_giftBtn addTarget:self action:@selector(clikcGiftBtn) forControlEvents:UIControlEventTouchUpInside];
        [_giftBtn setImage:[UIImage imageNamed:@"live_礼物"] forState:0];
        [_botBtnFunView addSubview:_giftBtn];
        [_giftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(btnW);
            make.centerY.equalTo(_botBtnFunView);
            make.right.equalTo(_cartBtn.mas_left).offset(-10);
        }];
        
    }
    return _botBtnFunView;
}
-(void)removeGoodsBtnAnimation {
    if (_cartBtn.hidden == YES) {
        return;
    }
    [_cartBtn.layer removeAllAnimations];
}
- (void)showGoodsBtnAnimaition{
    if (_cartBtn.hidden == YES) {
        return;
    }
    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    animation.duration = 0.7;
    animation.repeatCount = 99999999;
    animation.autoreverses = YES;
    animation.fromValue = [NSNumber numberWithFloat:0.7];
    animation.toValue = [NSNumber numberWithFloat:1.1];
    [_cartBtn.imageView.layer addAnimation:animation forKey:nil];
}
- (YBChatToolBar *)chatTool {
    if (!_chatTool) {
        YBWeakSelf;
        _chatTool = [[YBChatToolBar alloc]initWithFrame:CGRectMake(0, _window_height-44, _window_width, 44)];
        _chatTool.hidden = YES;
        _chatTool.chatToolEvnt = ^(NSString *eventType, NSString *eventContent) {
            [weakSelf chatToolCallBack:eventType andContent:eventContent];
        };
    }
    return _chatTool;
}
-(void)tapGes {
    if ([_chatTool.chatTF isFirstResponder] || _keyBoardIsShow) {
        [_chatTool.chatTF resignFirstResponder];
        [[XGGAppDelegate sharedAppDelegate].topViewController.view endEditing:YES];
    }else {
        //点亮
        [self socketLightAnimation];
        if (self.playCtrEvent) {
            self.playCtrEvent(@"控制-点亮", @{});
        }
    }
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch;{
    if ([touch.view isDescendantOfView:_botBtnFunView]
        ||[touch.view isDescendantOfView:_liveChatView]
        ||[touch.view isDescendantOfView:_userListView]
        ||[touch.view isDescendantOfView:_hostInfoView]
        ||[touch.view isDescendantOfView:_chatTool]
        ||[touch.view isDescendantOfView:_roomGameCycle]) {
        return NO;
    }
    return YES;
}
- (RKShowPaintedView *)paintedShowRegion {
    if (!_paintedShowRegion) {
        _paintedShowRegion = [[RKShowPaintedView alloc]init];
        //_paintedShowRegion.backgroundColor = UIColor.redColor;
    }
    return _paintedShowRegion;
}
-(void)desTroyDaytaskView {
    if (_dayTaskView) {
        [_dayTaskView dismissView];
        _dayTaskView = nil;
    }
}
@end
