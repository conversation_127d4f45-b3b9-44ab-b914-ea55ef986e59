//
//  YBPlayCtrlView.h
//  YBVideo
//
//  Created by YB007 on 2019/12/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "YBChatToolBar.h"

typedef void (^PlayCtrBlock)(NSString *eventStr,NSDictionary *eventDic);

@interface YBPlayCtrlView : UIView

@property(nonatomic,copy)PlayCtrBlock playCtrEvent;
@property(nonatomic,assign)BOOL keyBoardIsShow;                 //键盘正在显示
@property(nonatomic,strong)UIScrollView *horScrollView;         //水平滑动Scroll
@property(nonatomic,strong)NSDictionary *playDic;               //主播信息dic
@property(nonatomic,strong)UIImageView *avatarEffect;           //背景模糊图

@property(nonatomic,strong)YBChatToolBar *chatTool;             //直播聊天工具条
@property(nonatomic,strong)UIView *botBtnFunView;               //底部功能键(发言、礼物、私信、分享、关闭)

@property(nonatomic,strong)NSDictionary *guardInfo;             //守护信息
@property(nonatomic,strong)UIButton *linkCtrBtn;                //连麦互动


/** 进房间更新用户列表、映票 */
-(void)enterroomUpdateInfo:(NSDictionary *)roomDic;

/** 用户点亮 */
-(void)socketLightAnimation;

/** 发言 */
-(void)socketChatDic:(NSDictionary *)chatDic;

/** 用户离开 */
-(void)socketUserLeave:(NSDictionary *)leaveDic;

/** 用户进入 */
-(void)socketUserEnter:(NSDictionary *)enterDic;

/** 送礼物 */
-(void)socketSendGift:(NSDictionary *)giftDic;

/** 展示主播介绍的商品 */
-(void)socketOnSale:(NSDictionary *)onSaleDic;

/** 计时器刷新列表 */
-(void)timerReloadList;

/** 私信未读数 */
-(void)updateUnread:(int)unread;

#pragma mark - 礼物
//销毁
-(void)destroyCtrSubView;

/** 更新关注按钮状态 */
-(void)updateFollowShow:(BOOL)isShow;

/** 切换房间清理上个房间的聊天记录 */
-(void)clearChatList;

/** 移除、展示购物车动画 */
-(void)removeGoodsBtnAnimation;
- (void)showGoodsBtnAnimaition;

/** socket更新购买守护信息 */
- (void)socketUpdateGuardMsg:(NSDictionary *)dic;

/** socket主播同意-拒绝连麦 */
-(void)socketHostIsAgree:(BOOL)isAgree andSocDic:(NSDictionary *)socDic;
/** socket主播下麦用户 */
-(void)socketHostDisconnect:(NSDictionary *)linkDic;
/** socket主播忙碌 */
-(void)socketHostBusy:(NSDictionary *)busyDic;
/** socket主播超时 */
-(void)socketHostTimeout:(NSDictionary *)timeoutDic;

/** 主播-主播:对方主播id */
-(void)showToHostInfoWithId:(NSString *)toHostId;
-(void)destroyPkTohostInfo;
/** PK 开始 */
-(void)socketLinkPKStart:(NSDictionary *)pkDic;
/** PK 结果 */
-(void)socketLinkPKReult:(NSDictionary *)pkDic;
/** PK 进度 */
-(void)socketLinkPKProgress:(NSDictionary *)pkDic;
-(void)destroyPkView;

/** 键盘弹起事件: 目前只处理 PK和连麦 的界面相对位置不变*/
-(void)keyBoardNoticeIsShow:(BOOL)isShow andHeight:(CGFloat)height;
@end


