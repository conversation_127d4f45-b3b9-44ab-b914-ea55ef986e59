//
//  YBSocketPlay.m
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBSocketPlay.h"
#import <SocketIO/SocketIO-Swift.h>

@interface YBSocketPlay()
{
    NSDictionary *_hostDic;
}
@property(nonatomic,strong)SocketManager *socketManager;
@property(nonatomic,strong)SocketIOClient *socketClient;
@end

@implementation YBSocketPlay

static YBSocketPlay*playSocketManeger = nil;

+(instancetype)playSocketManeger {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        playSocketManeger = [[super allocWithZone:NULL]init];
    });
    return playSocketManeger;
}

+(instancetype)allocWithZone:(struct _NSZone *)zone {
    return [self playSocketManeger];
}

#pragma mark - 进房间、连socket
-(void)enterRoomAndConnectSocket:(NSDictionary *)conDic complete:(SocketPlayBlock)complete {
    //列表上传递的主播信息
    _hostDic = conDic;
    YBWeakSelf;
    NSDictionary *postDic = @{@"liveuid":minstr([_hostDic valueForKey:@"uid"]),@"stream":minstr([_hostDic valueForKey:@"stream"])};
    [YBNetworking postWithUrl:@"Live.enterRoom" Dic:postDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            [weakSelf playConnectSocket:infoDic];
            if (complete) {
                complete(@"0",infoDic);
            }
        }else {
            if (complete) {
                complete(msg,@{});
            }
        }
    } Fail:^(id fail) {
        if (complete) {
            complete(YZMsg(@"进入房间失败"),@{});
        }
    }];
}

-(void)playConnectSocket:(NSDictionary *)conDic{
    NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:conDic];
    [m_dic addEntriesFromDictionary:_hostDic];
    //列表传递主播信息+enterroom返回的信息合并
    _hostDic = [NSDictionary dictionaryWithDictionary:m_dic];
    
    NSString *socketUrl = minstr([_hostDic valueForKey:@"chatserver"]);
    NSString *liveStream = minstr([_hostDic valueForKey:@"stream"]);
    _socketManager = [[SocketManager alloc]initWithSocketURL:[NSURL URLWithString:socketUrl] config:@{@"log": @NO, @"compress": @YES}];
    _socketClient = _socketManager.defaultSocket;
    NSArray *cur = @[@{@"username":[Config getOwnNicename],
                       @"uid":[Config getOwnID],
                       @"token":[Config getOwnToken],
                       @"roomnum":minstr([_hostDic valueForKey:@"uid"]),
                       @"stream":liveStream,
                       @"lang":[YBLanguageTools serviceLang],
    }];
    [_socketClient connect];
    [_socketClient on:@"connect" callback:^(NSArray* data, SocketAckEmitter* ack) {
        [_socketClient emit:@"conn" with:cur];
        NSLog(@"socket链接");
    }];
    [_socketClient on:@"disconnect" callback:^(NSArray* data, SocketAckEmitter* ack) {
        NSLog(@"socket.io disconnect---%@",data);
    }];
    [_socketClient on:@"error" callback:^(NSArray* data, SocketAckEmitter* ack) {
        NSLog(@"socket.io error -- %@",data);
    }];
    
    [_socketClient on:@"conn" callback:^(NSArray* data, SocketAckEmitter* ack) {
        NSLog(@"进入房间");
    }];
    YBWeakSelf;
    [_socketClient on:@"broadcastingListen" callback:^(NSArray* data, SocketAckEmitter* ack) {
        /*
        if ([[data[0] firstObject] isEqual:@"stopplay"]) {
            if (weakSelf.roomCloseByAdmin) {
                weakSelf.roomCloseByAdmin(@"用户-超管关播", @{});
            }
            return ;
        }
        */
        for (NSString *path in data[0]) {
            NSDictionary *jsonArray = [path JSONValue];
            NSDictionary *msg = [[jsonArray valueForKey:@"msg"] firstObject];
            NSString *retcode = [NSString stringWithFormat:@"%@",[jsonArray valueForKey:@"retcode"]];
            NSString *method = [msg valueForKey:@"_method_"];
            if ([retcode isEqual:@"409002"]) {
                [MBProgressHUD showError:YZMsg(@"你已被禁言")];
                return;
            }
            [weakSelf getmessage:msg andMethod:method];
        }
    }];
}
-(void)getmessage:(NSDictionary *)msg andMethod:(NSString *)method{
    NSLog(@"收到socekt:%@",msg);
    if ([method isEqual:Soc_SendMsg]) {
        //文本消息
        NSString *msgtype = minstr([msg valueForKey:@"msgtype"]);
        NSString *action = minstr([msg valueForKey:@"action"]);
        if([msgtype isEqual:@"2"]) {
            //默认-聊天消息
            NSString *titleColor = @"0";
            NSString *ct = minstr([msg valueForKey:@"ct"]);
            if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([msg valueForKey:@"ct_en"])]) {
                ct = minstr([msg valueForKey:@"ct_en"]);
            }
            NSString *uname = minstr([msg valueForKey:@"uname"]);
            NSString *uid = minstr([msg valueForKey:@"uid"]);
            NSString *userType = minstr([msg valueForKey:@"usertype"]);
            NSString *guardType = minstr([msg valueForKey:@"guard_type"]);
            NSString *isAnchor = @"0";
            if (![PublicObj checkNull:minstr([msg valueForKey:@"heart"])]) {
                //说明是点亮消息
                titleColor = [@"light" stringByAppendingFormat:@"%@",[msg valueForKey:@"heart"]];
                if (self.userLight) {
                    self.userLight(@"用户-用户点亮", @{});
                }
            }
            NSDictionary *chatDic = @{@"userName":uname,
                                      @"contentChat":ct,
                                      @"id":uid,
                                      @"titleColor":titleColor,
                                      @"usertype":userType,
                                      @"isAnchor":isAnchor,
                                      @"guard_type":guardType,
            };
            if (self.userSendMsg) {
                self.userSendMsg(@"用户-收到发言", chatDic);
            }
        }
        if([msgtype isEqual:@"0"]) {
            NSString *action = minstr([msg valueForKey:@"action"]);
            if ([action isEqual:@"1"] && self.userLeave) {
                //用户离开.离开的用户 id 在 ct 里边 'id'
                self.userLeave(@"用户-用户离开", msg);
            }
            if ([action isEqual:@"0"] && self.userEnter) {
                //用户进入.离开的用户 id 在 ct 里边 'id'
                self.userEnter(@"用户-用户进入", msg);
            }
        }
        if ([msgtype isEqual:@"1"] && [action isEqual:@"18"]) {
            if (self.liveOff) {
                self.liveOff(@"用户-直播关闭", @{});
            }
        }
        
    }
    else if ([method isEqual:Soc_SystemNot] || [method isEqual:Soc_ShutUpUser]){
        //系统消息、禁言
        NSString *msgtype = minstr([msg valueForKey:@"msgtype"]);
        NSString *action = minstr([msg valueForKey:@"action"]);
        NSString *touid = minstr([msg valueForKey:@"touid"]);
        
        NSString *showCt = minstr([msg valueForKey:@"ct"]);
        if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([msg valueForKey:@"ct_en"])]) {
            showCt = minstr([msg valueForKey:@"ct_en"]);
        }
        
        if([msgtype isEqual:@"4"] && [action isEqual:@"13"] && [touid isEqual:[Config getOwnID]]) {
            //关注
            [self showAlert:showCt];
        }
        if ([msgtype isEqual:@"4"] && [action isEqual:@"1"] && [touid isEqual:[Config getOwnID]]){
            //禁言
            [self showAlert:showCt];
        }
        NSDictionary *chatDic = @{@"userName":YZMsg(@"直播间消息"),
                                  @"contentChat":showCt,
                                  @"id":@"",
                                  @"titleColor":@"firstlogin",
                                  @"usertype":@"",
                                  @"isAnchor":@"0",
        };
        if (self.systemMsg) {
            self.systemMsg(@"用户-系统消息", chatDic);
        }
    }
    else if ([method isEqual:Soc_setAdmin]){
        NSString *msgtype = minstr([msg valueForKey:@"msgtype"]);
        NSString *touid = minstr([msg valueForKey:@"touid"]);
        NSString *usertype = @"";
        
        NSString *showCt = minstr([msg valueForKey:@"ct"]);
        if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([msg valueForKey:@"ct_en"])]) {
            showCt = minstr([msg valueForKey:@"ct_en"]);
        }
        
        if ([msgtype isEqual:@"1"] && [touid isEqual:[Config getOwnID]]) {
            //设置-取消管理
            [self showAlert:showCt];
        }
        if ([touid isEqual:[Config getOwnID]]) {
            if ([minstr([msg valueForKey:@"action"]) isEqual:@"0"]) {
                usertype = @"0";
            }else {
                usertype = @"40";
            }
        }
        NSDictionary *chatDic = @{@"userName":YZMsg(@"直播间消息"),
                                  @"contentChat":showCt,
                                  @"id":@"",
                                  @"titleColor":@"firstlogin",
                                  @"usertype":usertype,
                                  @"isAnchor":@"0",
        };
        if (self.setAdmin) {
            self.setAdmin(@"用户-管理员", chatDic);
        }
    }
    else if ([method isEqual:Soc_KickUser]){
        NSString *touid = minstr([msg valueForKey:@"touid"]);
        if ([touid isEqual:[Config getOwnID]] && self.kickUser) {
            self.kickUser(@"用户-被踢", @{});
            [self showAlert:YZMsg(@"你已被踢出房间")];
        }
        NSString *showCt = minstr([msg valueForKey:@"ct"]);
        if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([msg valueForKey:@"ct_en"])]) {
            showCt = minstr([msg valueForKey:@"ct_en"]);
        }
        NSDictionary *chatDic = @{@"userName":YZMsg(@"直播间消息"),
                                  @"contentChat":showCt,
                                  @"id":@"",
                                  @"titleColor":@"firstlogin",
                                  @"usertype":@"",
                                  @"isAnchor":@"0",
        };
        if (self.systemMsg) {
            self.systemMsg(@"用户-踢人消息", chatDic);
        }
    }
    else if ([method isEqual:Soc_SendGift]){
        //送礼物
        if ([minstr([msg valueForKey:@"ifpk"]) isEqual:@"1"] && self.linkPKProgress) {
            self.linkPKProgress(@"用户-PK进度", msg);
        }
        if (self.sendGift && [minstr([msg valueForKey:@"roomnum"]) isEqual:minstr([_hostDic valueForKey:@"uid"])]) {
            self.sendGift(@"用户-收到礼物", msg);
        }
    }
    else if ([method isEqual:Soc_light]){
        //点亮
        NSString *msgtype = [msg valueForKey:@"msgtype"];
        NSString *action = [msg valueForKey:@"action"];
        if([msgtype isEqual:@"0"] && [action isEqual:@"2"] && self.userLight){
            self.userLight(@"用户-用户点亮", @{});
        }
    }
    else if ([method isEqual:Soc_stopLive]){
        //超管关播
        if (self.roomCloseByAdmin) {
            self.roomCloseByAdmin(@"用户-超管关播", @{});
        }
    }
    else if ([method isEqual:Soc_StartEndLive]){
        //直播结束
        NSString *action = minstr([msg valueForKey:@"action"]);
        if (self.liveOff && [action isEqual:@"18"]) {
            self.liveOff(@"用户-直播关闭", @{});
        }
    }
    else if ([method isEqual:Soc_disconnect]){
        //断开链接
        if (self.userLeave) {
            //用户离开.离开的用户 id 在 ct 里边 'id'
            self.userLeave(@"用户-用户离开", msg);
        }
    }
    else if ([method isEqual:Soc_goodsLiveShow]){
        //在售商品 action:1-展示 0-隐藏
        if (self.onSaleMsg) {
            self.onSaleMsg(@"用户-在售", msg);
        }
    }
    else if ([method isEqual:Soc_BuyGuard]){
        //购买守护
        if (self.buyGuardMsg) {
            self.buyGuardMsg(@"用户-买守护", msg);
        }
        NSDictionary *chatDic = @{@"userName":YZMsg(@"直播间消息"),
                                  @"contentChat":[NSString stringWithFormat:@"%@ %@",[msg valueForKey:@"uname"],YZMsg(@"守护了主播")],
                                  @"id":@"",
                                  @"titleColor":@"firstlogin",
                                  @"usertype":@"",
                                  @"isAnchor":@"0",
        };
        if (self.systemMsg) {
            self.systemMsg(@"用户-系统消息", chatDic);
        }
    }
    else if ([method isEqual:Soc_ConnectVideo]){
        //用户-主播连麦
        int action = [minstr([msg valueForKey:@"action"]) intValue];
        // 1 有人发送连麦请求  2 主播接受连麦 3 主播拒绝连麦 4 用户推流，发送自己的播流地址 5 用户断开连麦 6 主播断开连麦 7 主播正忙碌 8 主播无响应
        NSString *touid = minstr([msg valueForKey:@"touid"]);
        switch (action) {
            case 2:{
                if (self.linkHostAgree && [touid isEqual:[Config getOwnID]]) {
                    self.linkHostAgree(@"用户-主播同意", msg);
                }
            }break;
            case 3:{
                if (self.linkHostUnAgree && [touid isEqual:[Config getOwnID]]) {
                    [MBProgressHUD showPop:YZMsg(@"主播拒绝了您的连麦请求")];
                    self.linkHostUnAgree(@"用户-主播拒绝", msg);
                }
            }break;
            case 4:{ /* 连麦混流的方式用户端不用处理此 action */ }break;
            case 5:{
                if (![minstr([msg valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
                    [MBProgressHUD showPop:[NSString stringWithFormat:@"%@%@",[msg valueForKey:@"uid"],YZMsg(@"已下麦")]];
                }
            }break;
            case 6:{
                if (self.linkHostDisconnect) {
                    self.linkHostDisconnect(@"用户-主播下麦用户", msg);
                }
                if ([touid isEqual:[Config getOwnID]]) {
                    [MBProgressHUD showPop:YZMsg(@"主播已把您下麦")];
                }else {
                    [MBProgressHUD showPop:[NSString stringWithFormat:@"%@%@",touid,YZMsg(@"已下麦")]];
                }
            }break;
            case 7:{
                if (self.linkHostBusy && [touid isEqual:[Config getOwnID]]) {
                    self.linkHostBusy(@"用户-主播忙碌", msg);
                    [MBProgressHUD showPop:YZMsg(@"主播正忙碌")];
                }
            }break;
            case 8:{
                if (self.linkHostTimeout && [touid isEqual:[Config getOwnID]]) {
                    self.linkHostTimeout(@"用户-主播超时", msg);
                    [MBProgressHUD showPop:YZMsg(@"当前主播暂时无法接通")];
                }
            }break;
            default:
                break;
        }
    }
    else if ([method isEqual:Soc_LiveConnect]){
        //主播-主播连麦
        int action = [minstr([msg valueForKey:@"action"]) intValue];
        //1：发起连麦；2；接受连麦；3:拒绝连麦；4：连麦成功通知；5.手动断开连麦;7:对方正忙碌 8:对方无响应
        switch (action) {
            case 4:{
                if (self.linkHostToHostStart) {
                    self.linkHostToHostStart(@"用户-主播和主播开始", msg);
                }
            }break;
            case 5:{
                if (self.linkHostToHostDisconnect) {
                    self.linkHostToHostDisconnect(@"用户-主播和主播结束", msg);
                }
            }break;
                
            default:
                break;
        }
    }
    else if ([method isEqual:Soc_LivePK]){
        //1：发起PK；2；接受PK；3:拒绝PK；4：PK成功通知；5.;7:对方正忙碌 8:对方无响应 9:PK结果
        int action = [minstr([msg valueForKey:@"action"]) intValue];
        switch (action) {
            case 4:{
                if (self.linkPKSuc) {
                    self.linkPKSuc(@"用户-PK开始", msg);
                }
            }break;
            case 9:{
                if (self.linkPKResult) {
                    self.linkPKResult(@"用户-PK结果", msg);
                }
            }break;
            default:
                break;
        }
    }
    
    
}
//提示框
-(void)showAlert:(NSString *)msg;{
    NSDictionary *contentDic = @{
                                @"title":YZMsg(@"提示"),
                                @"msg":msg,
                                @"left":@"",
                                @"right":YZMsg(@"确定")
    };
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        
    }];
}

#pragma mark - socket 断开
-(void)playDisconnectSocket {
    [_socketClient disconnect];
    [_socketClient off:@""];
    [_socketClient leaveNamespace];
    _socketClient = nil;
    _socketManager = nil;
}

#pragma mark - 发送公屏聊天
-(void)playSendMsg:(NSString *)eventStr otherDic:(NSDictionary *)otherDic {
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_":Soc_SendMsg,
                                        @"action":@"0",
                                        @"ct":eventStr,
                                        @"msgtype":@"2",
                                        @"isAnchor":@"0",
                                        @"uid":[Config getOwnID],
                                        @"uname":[Config getOwnNicename],
                                        @"usertype":minstr([otherDic valueForKey:@"usertype"]),
                                        @"guard_type":minstr([otherDic valueForKey:@"guard_type"]),
                                        }
                                    ],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 点亮 */
-(void)playSendLight:(NSString *)userType otherDic:(NSDictionary *)otherDic{
    NSInteger random = arc4random()%5;
    NSString *num = [NSString stringWithFormat:@"%ld",(long)random];
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_": Soc_SendMsg,
                                        @"action": @"0",
                                        @"ct": YZMsg(@"我点亮了"),
                                        @"ct_en": @"I'm lighted",
                                        @"msgtype": @"2",
                                        @"uid": [Config getOwnID],
                                        @"uname": [Config getOwnNicename],
                                        @"usertype":userType,
                                        @"heart":num,
                                        @"guard_type":minstr([otherDic valueForKey:@"guard_type"]),
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}

#pragma mark - /** 送礼物 */
-(void)playSendGift:(NSDictionary *)giftDic;{
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_": Soc_SendGift,
                                        @"action": @"0",
                                        @"ct":minstr([giftDic valueForKey:@"gifttoken"]),
                                        @"msgtype": @"1",
                                        @"uid": [Config getOwnID],
                                        @"uname": [Config getOwnNicename],
                                        @"evensend":minstr([giftDic valueForKey:@"lianfa"]),
                                        @"uhead":minstr([giftDic valueForKey:@"avatar"]),
                                        @"paintedPath":[giftDic valueForKey:@"paintedPath"],        //数组
                                        @"paintedWidth":[giftDic valueForKey:@"paintedWidth"],      //对象
                                        @"paintedHeight":[giftDic valueForKey:@"paintedHeight"],    //对象
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 主播、管理踢人 */
-(void)playSendKickUser:(NSDictionary *)kickDic {
    NSString *ct = [NSString stringWithFormat:@"%@ %@",[kickDic valueForKey:@"touname"],@"被踢出房间"];
    NSString *ct_en = [NSString stringWithFormat:@"%@ %@",[kickDic valueForKey:@"touname"],@"kicked out of room"];
    NSArray* msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_KickUser,
                                      @"action":@"2",
                                      @"ct":ct,
                                      @"ct_en":ct_en,
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"touid":minstr([kickDic valueForKey:@"touid"]),
                                      @"touname":minstr([kickDic valueForKey:@"touname"]),
                                      @"msgtype":@"4",
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [MBProgressHUD showError:YZMsg(@"踢人成功")];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 主播、管理员禁言 */
-(void)playSendShutUpUser:(NSDictionary *)shutUpDic {
    NSString *type = minstr([shutUpDic valueForKey:@"type"]);
    NSString *touname = minstr([shutUpDic valueForKey:@"touname"]);
    NSString *msg = [NSString stringWithFormat:@"%@ %@",touname,@"被本场禁言"];
    NSString *msg_en = [NSString stringWithFormat:@"%@ %@",touname,@"has been shup up in this live stream"];
    if ([type isEqual:@"0"]) {
        msg = [NSString stringWithFormat:@"%@ %@",touname,@"被永久禁言"];
        msg_en = [NSString stringWithFormat:@"%@ %@",touname,@"shut up permanently"];
    }
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_ShutUpUser,
                                      @"action":@"1",
                                      @"ct":msg,
                                      @"ct_en":msg_en,
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"touid":minstr([shutUpDic valueForKey:@"touid"]),
                                      @"touname":touname,
                                      @"msgtype":@"4",
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [MBProgressHUD showError:YZMsg(@"禁言成功")];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 关注主播 */
-(void)playSendAttention {
    NSString *msg = [NSString stringWithFormat:@"%@ %@",[Config getOwnNicename],@"关注了主播"];
    NSString *msg_en = [NSString stringWithFormat:@"%@ %@",[Config getOwnNicename],@"followed the anchor"];
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_SystemNot,
                                      @"action":@"13",
                                      @"ct":msg,
                                      @"ct_en":msg_en,
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"msgtype":@"4",
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}

#pragma mark - /** 超管关播 */
-(void)playSendStopLive:(NSString *)ct {
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_":Soc_stopLive,
                                        @"action": @"19",
                                        @"ct":ct,
                                        @"msgtype": @"1",
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 购买守护 */
-(void)playSendBuyGuardSuc:(NSDictionary *)guardInfo{
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_": Soc_BuyGuard,
                                        @"action":@"0",
                                        @"msgtype": @"0",
                                        @"ct":YZMsg(@"守护了主播"),
                                        @"uid":[Config getOwnID],
                                        @"uname":[Config getOwnNicename],
                                        @"uhead":[Config getUserAvatar],
                                        @"votestotal":minstr([guardInfo valueForKey:@"votestotal"]),
                                        @"guard_nums":minstr([guardInfo valueForKey:@"guard_nums"])
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 用户-主播连麦 */
/*
 1 有人发送连麦请求
 2 主播接受连麦
 3 主播拒绝连麦
 4 用户推流，发送自己的播流地址
 5 用户断开连麦
 6 主播断开连麦
 7 主播正忙碌
 8 主播无响应
 */
-(void)playSendUserHostLink{
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_": Soc_ConnectVideo,
                                        @"action":@"1",
                                        @"msgtype": @"10",
                                        @"uid":[Config getOwnID],
                                        @"uname":[Config getOwnNicename],
                                        @"uhead":[Config getUserAvatar],
                                        @"sex":[Config getUserSex]
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}

-(void)playSendUserLinkUrl:(NSString *)linkUrl; {
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_": Soc_ConnectVideo,
                                        @"action":@"4",
                                        @"msgtype": @"10",
                                        @"uid":[Config getOwnID],
                                        @"uname":[Config getOwnNicename],
                                        @"playurl":linkUrl,
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}
-(void)playSendUserDisconnect; {
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_": Soc_ConnectVideo,
                                        @"action":@"5",
                                        @"msgtype": @"10",
                                        @"uid":[Config getOwnID],
                                        @"uname":[Config getOwnNicename]
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];
}
@end
