//
//  YBSocketLive.h
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "YBSocketName.h"

/**
 * socEvent socket执行事件,这里用中文描述的
 * (新增Socket 可以创建SocketLiveBlock的对象来区分，也可使用socEvent来区分)  注:这里的"socEvent"只是用中文表述事件,不必翻译
 * socketDic 详情
 */
typedef void (^SocketLiveBlock)(NSString *socEvent,NSDictionary *socketDic);

@interface YBSocketLive : NSObject

+(instancetype)liveSocketManeger;

@property(nonatomic,copy)SocketLiveBlock checkLiveingStatus;        //检查开播状态
@property(nonatomic,copy)SocketLiveBlock superStopRoom;             //超管关播
@property(nonatomic,copy)SocketLiveBlock userLight;                 //用户点亮
@property(nonatomic,copy)SocketLiveBlock userSendMsg;               //发言
@property(nonatomic,copy)SocketLiveBlock userLeave;                 //用户离开
@property(nonatomic,copy)SocketLiveBlock userEnter;                 //用户进入
@property(nonatomic,copy)SocketLiveBlock systemMsg;                 //系统消息
@property(nonatomic,copy)SocketLiveBlock sendGift;                  //送礼物
@property(nonatomic,copy)SocketLiveBlock otherDeviceLogin;          //其他设备登陆
@property(nonatomic,copy)SocketLiveBlock buyGuardMsg;               //购买守护
@property(nonatomic,copy)SocketLiveBlock linkPlayMsg;               //收到连麦用户流信息
@property(nonatomic,copy)SocketLiveBlock linkUserDisconnect;        //连麦用户挂断
@property(nonatomic,copy)SocketLiveBlock linkHostToHostRequest;     //收到主播-主播连麦请求
@property(nonatomic,copy)SocketLiveBlock linkHostToHostMsg;         //收到主播-主播连麦成功
@property(nonatomic,copy)SocketLiveBlock linkHostToHostDisconnect;  //主播-主播挂断
@property(nonatomic,copy)SocketLiveBlock linkPKGetRequest;          //收到PK请求
@property(nonatomic,copy)SocketLiveBlock linkPKUnAgree;             //PK拒绝
@property(nonatomic,copy)SocketLiveBlock linkPKSuc;                 //服务端下发PK开始
@property(nonatomic,copy)SocketLiveBlock linkPKBusy;                //忙碌
@property(nonatomic,copy)SocketLiveBlock linkPKTimeout;             //超时
@property(nonatomic,copy)SocketLiveBlock linkPKResult;              //PK出结果
@property(nonatomic,copy)SocketLiveBlock linkPKProgress;            //PK-收到礼物

/** socket 链接 */
-(void)liveConnectSocket:(NSDictionary *)conDic;

/** socket 断开 */
-(void)liveDisconnectSocket;

/** 发送公屏聊天 */
-(void)liveSendMsg:(NSString *)eventStr;

/** 主播、管理踢人 */
-(void)liveSendKickUser:(NSDictionary *)kickDic;

/** 主播、管理员禁言 */
-(void)liveSendShutUpUser:(NSDictionary *)shutUpDic;

/** 来电话 */
-(void)liveSendPhoneCall:(NSString *)eventStr andEn:(NSString *)eventStr_en;;

/** 主播设置、取消管理 */
-(void)liveSendSetAdmin:(NSDictionary *)adminDic;

/** 在售商品-展示、隐藏 */
-(void)liveSendOnSaleShowHidden:(NSDictionary *)goodsDic;

/** 用户-主播连麦 */
///主播同意-拒绝
-(void)liveSendHostIsAgree:(BOOL)isAgree andTouid:(NSString *)touid;
///主播断开用户、主播断开对方主播
-(void)liveSendHostDisconnect:(NSDictionary *)userInfo;
///主播忙碌
-(void)liveSendHostBusyTouser:(NSString *)touid;
///主播无响应
-(void)liveSendHostTimeOut:(NSString *)touid;

/** 主播-主播 连麦 */
///主播-主播 发起
-(void)liveSendAnchorStartLink:(NSDictionary *)otherInfo andMyInfo:(NSDictionary *)myInfo;

-(void)liveSendAnchorCtrOfAcntion:(NSString *)action andExtDic:(NSDictionary *)extDic;

/** PK */
-(void)liveSendStartPk;
-(void)liveSendPkCtrAction:(NSString *)action;
@end


