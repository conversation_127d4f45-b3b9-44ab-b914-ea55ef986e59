//
//  YBSocketLive.m
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBSocketLive.h"
#import <SocketIO/SocketIO-Swift.h>
#import "YBLinkAlertView.h"
#import "YBAnchorPKAlert.h"
@interface YBSocketLive(){
    NSDictionary *_roomDic;
    BOOL _isBusy;
    int _linkMicAskLast;
    NSTimer *_linkMicAskTimer;                  //连麦请求中倒计时
    NSString *_linkRequestUid;
}
@property(nonatomic,strong)SocketManager *socketManager;
@property(nonatomic,strong)SocketIOClient *socketClient;
@property(nonatomic,strong)YBLinkAlertView *linkAlertView;
@property(nonatomic,strong)YBAnchorPKAlert *pkAlertView;
@property(nonatomic,assign)BOOL hostLinking;                //主播连麦中【包含用户-主播、主播-主播连麦】

@end

@implementation YBSocketLive

static YBSocketLive*liveSocketManeger = nil;

+(instancetype)liveSocketManeger {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        liveSocketManeger = [[super allocWithZone:NULL]init];
    });
    return liveSocketManeger;
}

+(instancetype)allocWithZone:(struct _NSZone *)zone {
    return [self liveSocketManeger];
}
#pragma mark - socket 链接
-(void)liveConnectSocket:(NSDictionary *)conDic {
    _linkMicAskLast = 10;
    _roomDic = conDic;
    _isBusy = NO;
    NSString *socketUrl = minstr([_roomDic valueForKey:@"chatserver"]);
    NSString *liveStream = minstr([_roomDic valueForKey:@"stream"]);
    _socketManager = [[SocketManager alloc]initWithSocketURL:[NSURL URLWithString:socketUrl] config:@{@"log": @NO, @"compress": @YES}];
    _socketClient = _socketManager.defaultSocket;
    NSArray *cur = @[@{@"username":[Config getOwnNicename],
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"roomnum":[Config getOwnID],
                          @"stream":liveStream,
                          @"lang":[YBLanguageTools serviceLang],
                          }];
    [_socketClient connect];
    [_socketClient on:@"connect" callback:^(NSArray* data, SocketAckEmitter* ack) {
        [_socketClient emit:@"conn" with:cur];
        NSLog(@"socket链接");
    }];
    YBWeakSelf;
    [_socketClient on:@"conn" callback:^(NSArray* data, SocketAckEmitter* ack) {
        NSLog(@"进入房间");
        if (_isBusy && weakSelf.checkLiveingStatus) {
            weakSelf.checkLiveingStatus(@"主播-检查状态", @{}); //这里的"主播-检查状态"只是用中文表述事件，不必翻译
        }
        _isBusy = YES;
    }];
    
    [_socketClient on:@"broadcastingListen" callback:^(NSArray* data, SocketAckEmitter* ack) {
        /*
        if([[data[0] firstObject] isEqual:@"stopplay"]) {
            NSLog(@"%@",[data[0] firstObject]);
            if (weakSelf.superStopRoom) {
                weakSelf.superStopRoom(@"主播-超管关播", @{});//这里的"主播-超管关播"只是用中文表述事件，不必翻译
            }
            NSDictionary *contentDic = @{@"title":@"",@"msg":YZMsg(@"涉嫌违规，被下播"),@"left":@"",@"right":YZMsg(@"确定")};
            [YBAlertView showAlertView:contentDic complete:^(int eventType) {
                
            }];
            return ;
        }
        */
        for (NSString *path in data[0]) {
            NSDictionary *jsonArray = [path JSONValue];
            NSDictionary *msg = [[jsonArray valueForKey:@"msg"] firstObject];
            NSString *retcode = [NSString stringWithFormat:@"%@",[jsonArray valueForKey:@"retcode"]];
            if ([retcode isEqual:@"409002"]) {
                [MBProgressHUD showError:YZMsg(@"你已被禁言")];
                return;
            }
            NSString *method = [msg valueForKey:@"_method_"];
            [weakSelf getmessage:msg andMethod:method];
        }
    }];
}
-(void)getmessage:(NSDictionary *)msg andMethod:(NSString *)method{
    NSLog(@"收到socekt:%@",msg);
    if ([method isEqual:Soc_SendMsg]) {
        //文本消息
        NSString *msgtype = [msg valueForKey:@"msgtype"];
        if([msgtype isEqual:@"2"]) {
            //默认-聊天消息
            NSString *titleColor = @"0";
            NSString *ct = minstr([msg valueForKey:@"ct"]);
            if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([msg valueForKey:@"ct_en"])]) {
                ct = minstr([msg valueForKey:@"ct_en"]);
            }
            NSString *uname = minstr([msg valueForKey:@"uname"]);
            NSString *uid = minstr([msg valueForKey:@"uid"]);
            NSString *userType = minstr([msg valueForKey:@"usertype"]);
            NSString *guardType = minstr([msg valueForKey:@"guard_type"]);
            NSString *isAnchor = @"0";
            if (![PublicObj checkNull:minstr([msg valueForKey:@"heart"])]) {
                //说明是点亮消息
                titleColor = [@"light" stringByAppendingFormat:@"%@",[msg valueForKey:@"heart"]];
                if (self.userLight) {
                    self.userLight(@"主播-用户点亮", @{});
                }
            }
            NSDictionary *chatDic = @{@"userName":uname,
                                      @"contentChat":ct,
                                      @"id":uid,
                                      @"titleColor":titleColor,
                                      @"usertype":userType,
                                      @"isAnchor":isAnchor,
                                      @"guard_type":guardType,
            };
            if (self.userSendMsg) {
                self.userSendMsg(@"主播-收到发言", chatDic);
            }
        }
        if([msgtype isEqual:@"0"]) {
            NSString *action = minstr([msg valueForKey:@"action"]);
            if ([action isEqual:@"1"] && self.userLeave) {
                //用户离开.离开的用户 id 在 ct 里边 'id'
                self.userLeave(@"主播-用户离开", msg);
                NSString *leaveuid = minstr([[msg valueForKey:@"ct"] valueForKey:@"id"]);
                if ((_linkAlertView && [_linkAlertView.applyUid isEqual:leaveuid]) || [leaveuid isEqual:_linkRequestUid]) {
                    _hostLinking = NO;
                    //退出的用户是发起连麦的人
                    [self destroyLinkMicAskTimer];
                    [self cancelLinkAlertShow];
                }
            }
            if ([action isEqual:@"0"] && self.userEnter) {
                //用户进入.离开的用户 id 在 ct 里边 'id'
                self.userEnter(@"主播-用户进入", msg);
            }
        }
    }
    else if ([method isEqual:Soc_SystemNot] || [method isEqual:Soc_KickUser] || [method isEqual:Soc_ShutUpUser] ||[method isEqual:Soc_setAdmin]){
        //系统消息、踢人、禁言、设置管理
        //在主播端这些类型的消息都只是在公屏显示一句话 即 [msg valueForKey:@"ct"]
        NSString *showCt = minstr([msg valueForKey:@"ct"]);
        if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([msg valueForKey:@"ct_en"])]) {
            showCt = minstr([msg valueForKey:@"ct_en"]);
        }
        NSDictionary *chatDic = @{@"userName":YZMsg(@"直播间消息"),
                                  @"contentChat":showCt,
                                  @"id":@"",
                                  @"titleColor":@"firstlogin",
                                  @"usertype":@"",
                                  @"isAnchor":@"",
        };
        if (self.systemMsg) {
            self.systemMsg(@"主播-系统消息", chatDic);
        }
    }
    else if ([method isEqual:Soc_SendGift]){
        NSLog(@"=====sendgift-get====:%@",msg);
        //送礼物
        if ([minstr([msg valueForKey:@"ifpk"]) isEqual:@"1"] && self.linkPKProgress) {
            self.linkPKProgress(@"主播-PK进度", msg);
        }
        if (self.sendGift && [minstr([msg valueForKey:@"roomnum"]) isEqual:[Config getOwnID]]) {
            self.sendGift(@"主播-收到礼物", msg);
        }
    }
    else if ([method isEqual:Soc_light]){
        //点亮
        NSString *msgtype = [msg valueForKey:@"msgtype"];
        NSString *action = [msg valueForKey:@"action"];
        if([msgtype isEqual:@"0"] && [action isEqual:@"2"] && self.userLight){
            self.userLight(@"主播-用户点亮", @{});
        }
    }
    else if ([method isEqual:Soc_stopLive]){
        //超管关播
        if (self.superStopRoom) {
            self.superStopRoom(@"主播-超管关播", @{});
        }
        // 警告框
        [YBLiveRoomAlertView showRoomWarning:msg];
    }
    else if ([method isEqual:Soc_StartEndLive]){
        //直播结束
        NSString *action = minstr([msg valueForKey:@"action"]);
        if ([action isEqual:@"19"] && self.otherDeviceLogin) {
            self.otherDeviceLogin(@"主播-其他设备登陆", @{});
        }
        if ([action isEqual:@"18"] && self.superStopRoom) {
            self.superStopRoom(@"主播-超管关播", @{});
        }
    }
    else if ([method isEqual:Soc_disconnect]){
        //断开链接
        NSString *action = [msg valueForKey:@"action"];
        if ([action isEqual:@"1"] && self.userLeave) {
            //用户离开.离开的用户 id 在 ct 里边 'id'
            self.userLeave(@"主播-用户离开", msg);
            NSString *leaveuid = minstr([[msg valueForKey:@"ct"] valueForKey:@"id"]);
            if ((_linkAlertView && [_linkAlertView.applyUid isEqual:leaveuid]) ||[leaveuid isEqual:_linkRequestUid]) {
                _hostLinking = NO;
                //退出的用户是发起连麦的人
                [self destroyLinkMicAskTimer];
                [self cancelLinkAlertShow];
            }
        }
    }
    else if ([method isEqual:Soc_BuyGuard]){
        //购买守护
        if (self.buyGuardMsg) {
            self.buyGuardMsg(@"主播-买守护", msg);
        }
        NSDictionary *chatDic = @{@"userName":YZMsg(@"直播间消息"),
                                  @"contentChat":[NSString stringWithFormat:@"%@ %@",[msg valueForKey:@"uname"],YZMsg(@"守护了主播")],
                                  @"id":@"",
                                  @"titleColor":@"firstlogin",
                                  @"usertype":@"",
                                  @"isAnchor":@"0",
        };
        if (self.systemMsg) {
            self.systemMsg(@"主播-系统消息", chatDic);
        }
    }
    else if ([method isEqual:Soc_ConnectVideo]){
        //用户-主播连麦
        // 1 有人发送连麦请求  2 主播接受连麦 3 主播拒绝连麦 4 用户推流，发送自己的播流地址 5 用户断开连麦 6 主播断开连麦 7 主播正忙碌 8 主播无响应
        int action = [minstr([msg valueForKey:@"action"]) intValue];
        switch (action) {
            case 1:{
                if (_linkMicAskLast != 10) {
                    [self liveSendHostBusyTouser:minstr([msg valueForKey:@"uid"])];
                    return;
                }
                if (_hostLinking) {
                    [self liveSendHostBusyTouser:minstr([msg valueForKey:@"uid"])];
                    return;
                }
                [self destroyLinkMicAskTimer];
                _linkMicAskLast = 10;
                _linkMicAskTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(linkMicAskEvent) userInfo:nil repeats:YES];
                _linkRequestUid = minstr([msg valueForKey:@"uid"]);
                [self showLinkAlert:msg];
            }break;
            case 4:{
                if ([_linkRequestUid isEqual:minstr([msg valueForKey:@"uid"])] && self.linkPlayMsg) {
                    self.linkPlayMsg(@"主播-连麦用户流信息", msg);
                }
            }break;
            case 5:{
                _hostLinking = NO;
                if (self.linkUserDisconnect) {
                    self.linkUserDisconnect(@"主播-连麦用户挂断", msg);
                }
            }break;
            default:
                break;
        }
    }
    else if ([method isEqual:Soc_LiveConnect]){
        //1：发起连麦；2；接受连麦；3:拒绝连麦；4：连麦成功通知；5.手动断开连麦;7:对方正忙碌 8:对方无响应
        int action = [minstr([msg valueForKey:@"action"]) intValue];
        switch (action) {
            case 1:{
                if (_linkMicAskLast != 10) {
                    [self liveSendAnchorCtrOfAcntion:@"7" andExtDic:msg];
                    return;
                }
                if (_hostLinking) {
                    [self liveSendAnchorCtrOfAcntion:@"7" andExtDic:msg];
                    return;
                }
                if (self.linkHostToHostRequest) {
                    self.linkHostToHostRequest(@"主播-收到连麦请求", msg);
                }
                [self destroyLinkMicAskTimer];
                _linkMicAskLast = 10;
                _linkMicAskTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(linkMicAskEvent) userInfo:nil repeats:YES];
                _linkRequestUid = minstr([msg valueForKey:@"uid"]);
                [self showLinkAlert:msg];
            }break;
            case 3:{
                [MBProgressHUD showPop:YZMsg(@"对方主播拒绝了你的连麦申请")];
                _hostLinking = NO;
            }break;
            case 4:{
                [MBProgressHUD showPop:YZMsg(@"对方主播接受了您的连麦请求，开始连麦")];
                if (self.linkHostToHostMsg) {
                    self.linkHostToHostMsg(@"主播-主播连麦成功", msg);
                }
                _hostLinking = YES;
                
            }break;
            case 5:{
                [MBProgressHUD showPop:YZMsg(@"连麦已断开")];
                if (self.linkHostToHostDisconnect) {
                    self.linkHostToHostDisconnect(@"主播-主播断开", msg);
                }
                _hostLinking = NO;
            }break;
            case 7:{
                [MBProgressHUD showPop:YZMsg(@"对方正忙碌")];
                _hostLinking = NO;
            }break;
            case 8:{
                [MBProgressHUD showPop:YZMsg(@"对方无响应")];
                _hostLinking = NO;
            }break;
                
            default:
                break;
        }
    }
    else if ([method isEqual:Soc_LivePK]){
        NSLog(@"=====livePK-get====:%@",msg);
        //1：发起PK；2；接受PK；3:拒绝PK；4：PK成功通知；5.;7:对方正忙碌 8:对方无响应 9:PK结果
        int action = [minstr([msg valueForKey:@"action"]) intValue];
        switch (action) {
            case 1:{
                if (_pkAlertView) {//理论不会出现这种情况
                    [self liveSendPkCtrAction:@"7"];
                    return;
                }
                if (self.linkPKGetRequest) {
                    self.linkPKGetRequest(@"主播-收到PK请求", msg);
                }
                [self showPKAlert:msg];
            }break;
            case 3:{
                [self cancelPKAlert];
                if (self.linkPKUnAgree) {
                    self.linkPKUnAgree(@"主播-PK拒绝", msg);
                }
                [MBProgressHUD showPop:YZMsg(@"对方主播拒绝了您的PK请求")];
            }break;
            case 4:{
                [self cancelPKAlert];
                if (self.linkPKSuc) {
                    self.linkPKSuc(@"主播-开始PK", msg);
                }
            }break;
            case 7:{
                [self cancelPKAlert];
                if (self.linkPKBusy) {
                    self.linkPKBusy(@"主播-PK忙碌", msg);
                }
                [MBProgressHUD showError:YZMsg(@"对方正忙碌")];
            }break;
            case 8:{
                [self cancelPKAlert];
                if (self.linkPKTimeout) {
                    self.linkPKTimeout(@"主播-PK超时", msg);
                }
                [MBProgressHUD showError:YZMsg(@"对方无响应")];
            }break;
            case 9:{
                if (self.linkPKResult) {
                    self.linkPKResult(@"主播-PK结果", msg);
                }
            }break;

            default:
                break;
        }
    }
    else if ([method isEqual:Soc_SystemWarning]) {
        /// 直播警告
        [YBLiveRoomAlertView showRoomWarning:msg];
    }
    
    
}
-(void)showPKAlert:(NSDictionary *)dic {
    [self cancelPKAlert];
    
    _pkAlertView = [[YBAnchorPKAlert alloc]initWithFrame:CGRectMake(_window_width*0.15, _window_height/2-(_window_width*0.7/52*34)/2, _window_width*0.7, _window_width*0.7/52*34) andIsStart:NO];
    [[UIApplication sharedApplication].delegate.window addSubview:_pkAlertView];
    YBWeakSelf
    _pkAlertView.anchorPkEvent = ^(AnchorPkAlertType pkAlertType) {
        [weakSelf pkAlertCallBack:pkAlertType];
    };
}
-(void)pkAlertCallBack:(AnchorPkAlertType)pkAlertType {
    [self cancelPKAlert];
    switch (pkAlertType) {
        case PkAlertType_unAgree:{
            [self liveSendPkCtrAction:@"3"];
            if (self.linkPKUnAgree) {
                self.linkPKUnAgree(@"主播-PK拒绝", @{});
            }
        }break;
        case PkAlertType_Agree:{
            [self liveSendPkCtrAction:@"2"];
        }break;
        case PkAlertType_TimeOut:{
            [self liveSendPkCtrAction:@"8"];
            if (self.linkPKTimeout) {
                self.linkPKTimeout(@"主播-PK超时", @{});
            }
        }default:
            break;
    }
}
-(void)cancelPKAlert {
    if (_pkAlertView) {
        [_pkAlertView removeTimer];
        [_pkAlertView removeFromSuperview];
        _pkAlertView = nil;
    }
}

- (void)showLinkAlert:(NSDictionary *)dic{
    [self cancelLinkAlertShow];
    _linkAlertView = [[YBLinkAlertView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andUserMsg:dic];
    _linkAlertView.timeL.text = [NSString stringWithFormat:@"%@(10)...",YZMsg(@"发起连麦请求")];
    [[UIApplication sharedApplication].delegate.window addSubview:_linkAlertView];
    [_linkAlertView show];
    YBWeakSelf;
    _linkAlertView.linkAlertEvent = ^(BOOL isAgree,BOOL isHostLink) {
        _linkMicAskLast = 10;
        [weakSelf destroyLinkMicAskTimer];
        if (isAgree) {
            //连麦请求-同意
            if (isHostLink) {
                //主播连麦-检查开播状态
                [weakSelf checkLinkLive:dic];
            }else {
                //用户连麦
                [weakSelf liveSendHostIsAgree:YES andTouid:minstr([dic valueForKey:@"uid"])];
                _hostLinking = YES;
            }
        }else{
            //连麦-拒绝
            if (isHostLink) {
                [weakSelf liveSendAnchorCtrOfAcntion:@"3" andExtDic:dic];
            }else {
                //用户连麦
                [weakSelf liveSendHostIsAgree:NO andTouid:minstr([dic valueForKey:@"uid"])];
            }
            _hostLinking = NO;
        }
    };
    
}
//检查主播状态
-(void)checkLinkLive:(NSDictionary *)checkDic {
    NSDictionary *postDic = @{@"stream":minstr([checkDic valueForKey:@"stream"]),@"uid_stream":minstr([_roomDic valueForKey:@"stream"])};
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Livepk.CheckLive" Dic:postDic Suc:^(int code, id info, NSString *msg) {
        _linkMicAskLast = 10;
        [weakSelf destroyLinkMicAskTimer];
        if (code == 0) {
            _hostLinking = YES;
            NSString *newPull = minstr([[info firstObject] valueForKey:@"pull"]);
            [weakSelf liveSendAnchorCtrOfAcntion:@"2" andExtDic:@{@"pull":newPull,@"uid":minstr([checkDic valueForKey:@"uid"])}];
        }else{
            _hostLinking = NO;
        }
    } Fail:^(id fail) {
        _linkMicAskLast = 10;
        [weakSelf destroyLinkMicAskTimer];
        _hostLinking = NO;
    }];
}
-(void)cancelLinkAlertShow {
    _linkMicAskLast = 10;
    if (_linkAlertView) {
        [_linkAlertView removeFromSuperview];
        _linkAlertView = nil;
    }
}
-(void)linkMicAskEvent {
    _linkMicAskLast -= 1;
    _linkAlertView.timeL.text = [NSString stringWithFormat:@"%@(%ds)...",YZMsg(@"发起连麦请求"),_linkMicAskLast];
    if (_linkMicAskLast <= 0) {
        if (_linkAlertView.isHostToHost) {
            [self liveSendAnchorCtrOfAcntion:@"8" andExtDic:@{@"uid":_linkRequestUid}];
        }else {
            [self liveSendHostTimeOut:_linkRequestUid];
        }
        [self destroyLinkMicAskTimer];
        [self cancelLinkAlertShow];
    }
}
-(void)destroyLinkMicAskTimer {
    if (_linkMicAskTimer) {
        [_linkMicAskTimer invalidate];
        _linkMicAskTimer = nil;
    }
}
#pragma mark - socket 断开
-(void)liveDisconnectSocket {
    [_socketClient disconnect];
    [_socketClient off:@""];
    [_socketClient leaveNamespace];
    _socketClient = nil;
    _socketManager = nil;
    _hostLinking = NO;
    
    [self cancelLinkAlertShow];
    [self cancelPKAlert];
    [self destroyLinkMicAskTimer];
}
#pragma mark - /** 发送公屏聊天 */
-(void)liveSendMsg:(NSString *)eventStr {
    NSArray *msgData =@[
                        @{
                            @"msg": @[
                                    @{
                                        @"_method_": Soc_SendMsg,
                                        @"action": @"0",
                                        @"msgtype": @"2",
                                        @"isAnchor":@"1",
                                        @"uname": [Config getOwnNicename],
                                        @"uid": [Config getOwnID],
                                        @"ct":eventStr,
                                        @"usertype":@"50",
                                        }
                                    ],
                            @"retcode": @"000000",
                            @"retmsg": @"OK"
                            }
                        ];
    [_socketClient emit:@"broadcast" with:msgData];

}
#pragma mark - /** 主播、管理踢人 */
-(void)liveSendKickUser:(NSDictionary *)kickDic {
    NSString *ct = [NSString stringWithFormat:@"%@ %@",[kickDic valueForKey:@"touname"],@"被踢出房间"];
    NSString *ct_en = [NSString stringWithFormat:@"%@ %@",[kickDic valueForKey:@"touname"],@"kicked out of room"];
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_KickUser,
                                      @"action":@"2",
                                      @"ct":ct,
                                      @"ct_en":ct_en,
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"touid":minstr([kickDic valueForKey:@"touid"]),
                                      @"touname":minstr([kickDic valueForKey:@"touname"]),
                                      @"msgtype":@"4",
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [MBProgressHUD showError:YZMsg(@"踢人成功")];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 主播、管理员禁言 */
-(void)liveSendShutUpUser:(NSDictionary *)shutUpDic {
    NSString *type = minstr([shutUpDic valueForKey:@"type"]);
    NSString *touname = minstr([shutUpDic valueForKey:@"touname"]);
    NSString *msg = [NSString stringWithFormat:@"%@ %@",touname,@"被本场禁言"];
    NSString *msg_en = [NSString stringWithFormat:@"%@ %@",touname,@"has been shup up in this live stream"];
    if ([type isEqual:@"0"]) {
        msg = [NSString stringWithFormat:@"%@ %@",touname,@"被永久禁言"];
        msg_en = [NSString stringWithFormat:@"%@ %@",touname,@"shut up permanently"];
    }
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_ShutUpUser,
                                      @"action":@"1",
                                      @"ct":msg,
                                      @"ct_en":msg_en,
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"touid":minstr([shutUpDic valueForKey:@"touid"]),
                                      @"touname":touname,
                                      @"msgtype":@"4",
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [MBProgressHUD showError:YZMsg(@"禁言成功")];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 来电话 */
-(void)liveSendPhoneCall:(NSString *)eventStr andEn:(NSString *)eventStr_en{
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_SystemNot,
                                      @"action":@"13",
                                      @"ct":eventStr,
                                      @"ct_en":eventStr_en,
                                      @"uid":@"",
                                      @"uname":YZMsg(@"直播间消息"),
                                      @"msgtype":@"4",
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}
#pragma mark - /** 主播设置、取消管理 */
-(void)liveSendSetAdmin:(NSDictionary *)adminDic {
    
    NSString *isAdmin = minstr([adminDic valueForKey:@"isadmin"]);
    NSString *touid = minstr([adminDic valueForKey:@"touid"]);
    NSString *touname = minstr([adminDic valueForKey:@"touname"]);
    NSString *ctMsg;
    NSString *ctMsg_en;
    if ([isAdmin isEqual:@"0"]) {
        ctMsg = @"被取消管理";
        ctMsg_en = @"Admin cancelled";
        [MBProgressHUD showPop:YZMsg(@"取消管理员成功")];
    }else {
        ctMsg = @"被设为管理";
        ctMsg_en = @"set as Admin";
        [MBProgressHUD showPop:YZMsg(@"设置管理员成功")];
    }
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_setAdmin,
                                      @"action":isAdmin,
                                      @"ct":[NSString stringWithFormat:@"%@ %@",touname,ctMsg],
                                      @"ct_en":[NSString stringWithFormat:@"%@ %@",touname,ctMsg_en],
                                      @"uid":[Config getOwnID],
                                      @"uname":YZMsg(@"直播间消息"),
                                      @"touid":touid,
                                      @"touname":touname,
                                      @"msgtype":@"1",
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}

/** 在售商品-展示、隐藏 */
-(void)liveSendOnSaleShowHidden:(NSDictionary *)goodsDic;{
    //action 1-展示 0-隐藏
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_goodsLiveShow,
                                      @"action":minstr([goodsDic valueForKey:@"action"]),
                                      @"msgtype":@"0",
                                      @"goods_name":minstr([goodsDic valueForKey:@"name"]),
                                      @"goods_old_price":minstr([goodsDic valueForKey:@"old_price"]),
                                      @"goods_price":minstr([goodsDic valueForKey:@"price"]),
                                      @"goods_thumb":minstr([goodsDic valueForKey:@"thumb"]),
                                      @"goods_type":minstr([goodsDic valueForKey:@"type"]),
                                      @"goodsid":minstr([goodsDic valueForKey:@"id"]),
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}

#pragma mark - /** 用户-主播连麦 */
/*
 1 有人发送连麦请求
 2 主播接受连麦
 3 主播拒绝连麦
 4 用户推流，发送自己的播流地址
 5 用户断开连麦
 6 主播断开连麦
 7 主播正忙碌
 8 主播无响应
 */
-(void)liveSendHostIsAgree:(BOOL)isAgree andTouid:(NSString *)touid {
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_ConnectVideo,
                                      @"action": isAgree?@"2":@"3",
                                      @"msgtype": @"10",
                                      @"touid":touid,
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}
-(void)liveSendHostDisconnect:(NSDictionary *)userInfo{
    _hostLinking = NO;
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_ConnectVideo,
                                      @"action":@"6",
                                      @"msgtype": @"10",
                                      @"touid":minstr([userInfo valueForKey:@"uid"]),
                                      @"uname":minstr([userInfo valueForKey:@"uname"]),
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}
-(void)liveSendHostBusyTouser:(NSString *)touid; {
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_ConnectVideo,
                                      @"action": @"7",
                                      @"msgtype": @"10",
                                      @"touid":touid
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}
-(void)liveSendHostTimeOut:(NSString *)touid {
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_ConnectVideo,
                                      @"action": @"8",
                                      @"msgtype": @"10",
                                      @"touid":touid
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}

#pragma mark - /** 主播-主播 连麦 */

//1：发起连麦；2；接受连麦；3:拒绝连麦；4：连麦成功通知；5.手动断开连麦;7:对方正忙碌 8:对方无响应

///主播-主播 发起
-(void)liveSendAnchorStartLink:(NSDictionary *)otherInfo andMyInfo:(NSDictionary *)myInfo {
    _linkRequestUid = minstr([otherInfo valueForKey:@"uid"]);
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_LiveConnect,
                                      @"action":@"1",
                                      @"msgtype": @"0",
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"uhead":[Config getUserAvatar],
                                      @"sex":[Config getUserSex],
                                      @"pkuid":minstr([otherInfo valueForKey:@"uid"]),
                                      @"pkpull":minstr([myInfo valueForKey:@"pull"]),
                                      @"stream":minstr([_roomDic valueForKey:@"stream"]),
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}

-(void)liveSendAnchorCtrOfAcntion:(NSString *)action andExtDic:(NSDictionary *)extDic {
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_LiveConnect,
                                      @"action":action,
                                      @"msgtype": @"0",
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"uhead":[Config getUserAvatar],
                                      @"pkuid":minstr([extDic valueForKey:@"uid"]),
                                      @"pkpull":[PublicObj checkNull:minstr([extDic valueForKey:@"pull"])]?@"":minstr([extDic valueForKey:@"pull"]),
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}

#pragma mark - /** PK */
//1：发起PK；2；接受PK；3:拒绝PK；4：PK成功通知；5.;7:对方正忙碌 8:对方无响应 9:PK结果
-(void)liveSendStartPk {
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_LivePK,
                                      @"action":@"1",
                                      @"msgtype": @"0",
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"uhead":[Config getUserAvatar],
                                      @"sex":[Config getUserSex],
                                      @"pkuid":_linkRequestUid,
                                      @"stream":minstr([_roomDic valueForKey:@"stream"]),
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
}
-(void)liveSendPkCtrAction:(NSString *)action {
    NSArray *msgData = @[
                        @{
                            @"msg":
                                @[@{
                                      @"_method_":Soc_LivePK,
                                      @"action":action,
                                      @"msgtype": @"0",
                                      @"uid":[Config getOwnID],
                                      @"uname":[Config getOwnNicename],
                                      @"uhead":[Config getUserAvatar],
                                      @"sex":[Config getUserSex],
                                      @"pkuid":_linkRequestUid,
                                }],
                            @"retcode":@"000000",
                            @"retmsg":@"OK"}];
    [_socketClient emit:@"broadcast" with:msgData];
    
    NSLog(@"====PK发送同意拒绝:===%@",msgData);
}

@end
