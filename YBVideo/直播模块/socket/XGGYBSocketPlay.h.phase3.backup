//
//  YBSocketPlay.h
//  YBVideo
//
//  Created by YB007 on 2019/11/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "YBSocketName.h"

/**
 * socEvent socket执行事件,这里用中文描述的
 * (新增Socket 可以创建SocketPlayBlock的对象来区分，也可使用socEvent来区分)  注:这里的"socEvent"只是用中文表述事件,不必翻译
 * socketDic 详情
 */
typedef void (^SocketPlayBlock)(NSString *socEvent,NSDictionary *socketDic);

@interface YBSocketPlay : NSObject

+(instancetype)playSocketManeger;

@property(nonatomic,copy)SocketPlayBlock roomCloseByAdmin;          //被超管关播
@property(nonatomic,copy)SocketPlayBlock userLight;                 //用户点亮
@property(nonatomic,copy)SocketPlayBlock userSendMsg;               //发言
@property(nonatomic,copy)SocketPlayBlock userLeave;                 //用户离开
@property(nonatomic,copy)SocketPlayBlock userEnter;                 //用户进入
@property(nonatomic,copy)SocketPlayBlock liveOff;                   //直播关闭
@property(nonatomic,copy)SocketPlayBlock systemMsg;                 //系统消息
@property(nonatomic,copy)SocketPlayBlock setAdmin;                  //设置-取消管理
@property(nonatomic,copy)SocketPlayBlock kickUser;                  //踢人
@property(nonatomic,copy)SocketPlayBlock sendGift;                  //送礼物
@property(nonatomic,copy)SocketPlayBlock onSaleMsg;                 //在售商品-展示、隐藏
@property(nonatomic,copy)SocketPlayBlock buyGuardMsg;               //购买守护
@property(nonatomic,copy)SocketPlayBlock linkHostAgree;             //主播同意
@property(nonatomic,copy)SocketPlayBlock linkHostUnAgree;           //主播拒绝
@property(nonatomic,copy)SocketPlayBlock linkHostDisconnect;        //主播下麦用户
@property(nonatomic,copy)SocketPlayBlock linkHostBusy;              //主播忙碌
@property(nonatomic,copy)SocketPlayBlock linkHostTimeout;           //主播超时
@property(nonatomic,copy)SocketPlayBlock linkHostToHostStart;       //主播-主播开始
@property(nonatomic,copy)SocketPlayBlock linkHostToHostDisconnect;  //主播-主播结束
@property(nonatomic,copy)SocketPlayBlock linkPKSuc;                 //服务端下发PK开始
@property(nonatomic,copy)SocketPlayBlock linkPKResult;              //PK出结果
@property(nonatomic,copy)SocketPlayBlock linkPKProgress;            //PK-收到礼物



/** 进房间 socket 链接 */
-(void)enterRoomAndConnectSocket:(NSDictionary *)conDic complete:(SocketPlayBlock)complete;

/** socket 断开 */
-(void)playDisconnectSocket;

/** 发送公屏聊天 */
-(void)playSendMsg:(NSString *)eventStr otherDic:(NSDictionary *)otherDic;

/** 点亮 */
-(void)playSendLight:(NSString *)userType otherDic:(NSDictionary *)otherDic;

/** 送礼物 */
-(void)playSendGift:(NSDictionary *)giftDic;

/** 主播、管理踢人 */
-(void)playSendKickUser:(NSDictionary *)kickDic;

/** 主播、管理员禁言 */
-(void)playSendShutUpUser:(NSDictionary *)shutUpDic;

/** 关注主播 */
-(void)playSendAttention;

/** 超管关播 */
-(void)playSendStopLive:(NSString *)ct;

/** 购买守护 */
-(void)playSendBuyGuardSuc:(NSDictionary *)guardInfo;

/** 用户-主播连麦 */
///请求连麦
-(void)playSendUserHostLink;
///发送用户连麦地址
-(void)playSendUserLinkUrl:(NSString *)linkUrl;
///用户主动断开
-(void)playSendUserDisconnect;

@end


