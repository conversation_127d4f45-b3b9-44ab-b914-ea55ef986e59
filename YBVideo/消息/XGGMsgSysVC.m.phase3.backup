//
//  MsgSysVC.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/8/2.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MsgSysVC.h"
#import "MsgSysModel.h"
#import "MsgSysCell.h"
#import "PubH5.h"

@interface MsgSysVC ()<UITableViewDelegate,UITableViewDataSource>
{
    int _paging;
}
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)NSMutableArray *dataArray;
@property(nonatomic,strong)NSArray *sysModels;

@end

@implementation MsgSysVC

-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self pullData];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = _isWhiteStyle?[UIColor whiteColor]: Normal_Color;
    
    self.titleL.text = _listModel.unameStr;
    
    self.dataArray = [NSMutableArray array];
    self.sysModels = [NSArray array];
    _paging = 1;
    
    [self.view addSubview:self.tableView];
}

#pragma mark -
- (NSArray *)sysModels {
    
    NSMutableArray *m_array = [NSMutableArray array];
    for (NSDictionary *dic in _dataArray) {
        MsgSysModel *model = [MsgSysModel modelWithDic:dic lisModel:_listModel];
        [m_array addObject:model];
    }
    _sysModels = m_array;
    return _sysModels;
}

-(void)pullData {
    NSString *url;
    if ([_listModel.uidStr isEqual:@"dsp_admin_1"]) {
        //官方
        url = [NSString stringWithFormat:@"Message.officialLists&p=%d",_paging];
    }else{
        //系统
        url = [NSString stringWithFormat:@"Message.systemnotifyLists&uid=%@&token=%@&p=%d",[Config getOwnID],[Config getOwnToken],_paging];
    }
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_tableView.mj_footer endRefreshing];
        [_tableView.mj_header endRefreshing];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging == 1) {
                [_dataArray removeAllObjects];
            }
            if (infoA.count <=0 ) {
                [_tableView.mj_footer endRefreshingWithNoMoreData];
            }else{
                [_dataArray addObjectsFromArray:infoA];
            }
            if (_dataArray.count <=0) {
                [PublicView showTextNoData:_tableView text1:@"" text2:YZMsg(@"暂无数据") centerY:0.8];
            }else{
                [PublicView hiddenTextNoData:_tableView];
            }
            [_tableView reloadData];
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [_tableView.mj_footer endRefreshing];
        [_tableView.mj_header endRefreshing];
    }];

}
-(void)refreshFooter {
    _paging +=1;
    [self pullData];
}
#pragma mark - UITableViewDelegate、UITableViewDataSource

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    
    return self.sysModels.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    MsgSysCell *cell = [MsgSysCell cellWithTab:tableView andIndexPath:indexPath];
    cell.model = _sysModels[indexPath.row];
    cell.isWhiteStyle = _isWhiteStyle;
    cell.selectedBackgroundView = [[UIImageView alloc]initWithImage:[PublicObj getImgWithColor:SelCell_Col]];
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    MsgSysModel *model = _sysModels[indexPath.row];
    if ([model.uidStr isEqual:@"dsp_admin_1"]) {
        PubH5 *h5 = [[PubH5 alloc]init];
        h5.url = model.urlStr;
        [self.navigationController pushViewController:h5 animated:YES];
    }
    
}

#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height - 64-statusbarHeight-ShowDiff)style:UITableViewStylePlain];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = _isWhiteStyle?[UIColor whiteColor]: Normal_Color;
        
        //先设置预估行高
        _tableView.estimatedRowHeight = 200;
        //再设置自动计算行高
        _tableView.rowHeight = UITableViewAutomaticDimension;
        
        YBWeakSelf;
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            _paging = 1;
            [weakSelf pullData];
        }];
        
        MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(refreshFooter)];
        _tableView.mj_footer = footer;
        [footer setTitle:YZMsg(@"数据加载中...") forState:MJRefreshStateRefreshing];
        [footer setTitle:@"" forState:MJRefreshStateIdle];//YZMsg(@"没有更多了哦~")
        [footer setTitle:@"" forState:MJRefreshStateNoMoreData];//YZMsg(@"没有更多了哦~")
        footer.stateLabel.font = [UIFont systemFontOfSize:15.0f];
        
    }
    return _tableView;
}

#pragma mark - 导航

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    
}

- (void)reloadSystemView{
    [_tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(self.view);
    }];
    _paging = 1;
    [self pullData];
}
- (void)hideSmallView{
    if (self.block) {
        self.block(0);
    }
}
- (void)imSmallViewReload{
    /*
    UIView *smallNavi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 35)];
    smallNavi.backgroundColor = RGB_COLOR(@"#f9fafb", 1);;
    [self.view addSubview:smallNavi];
    
    UIButton *btn = [UIButton buttonWithType:0];
    btn.frame = CGRectMake(0, 0, 35, 35);
    [btn setImage:[UIImage imageNamed:@"gray_back"] forState:0];
    btn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
    [btn addTarget:self action:@selector(hideSmallView) forControlEvents:UIControlEventTouchUpInside];
    [smallNavi addSubview:btn];
    UILabel *titleLabel = [[UILabel alloc]initWithFrame:CGRectMake(35, 0, _window_width-70, 35)];
    titleLabel.font = [UIFont systemFontOfSize:13];
    titleLabel.textColor= RGB_COLOR(@"#636464", 1);
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.text = YZMsg(@"系统消息");
    [smallNavi addSubview:titleLabel];
    _tableView.frame = CGRectMake(0, 35, _window_width, _window_height*0.4-35);
    */
    
    [_tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(self.view);
    }];
    _paging = 1;
    [self pullData];
    
}
@end
