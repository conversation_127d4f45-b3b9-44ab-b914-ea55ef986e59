//
//  MessageListModel.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MessageListModel.h"
#import "JCHATStringUtils.h"


@implementation MessageListModel

- (instancetype)initWithDic:(NSDictionary *)dic {
    self = [super init];
    if (self) {
        
        _uidStr = [NSString stringWithFormat:@"%@",[dic valueForKey:@"id"]];
        _unameStr = [NSString stringWithFormat:@"%@",[dic valueForKey:@"user_nickname"]];
        _iconStr = [NSString stringWithFormat:@"%@",[dic valueForKey:@"avatar"]];
        _conversation = [dic valueForKey:@"conversation"];
        _isAtt = minstr([dic valueForKey:@"utot"]);
        if ([PublicObj checkNull:minstr([dic valueForKey:@"utot"])]) {
            _isAtt = minstr([dic valueForKey:@"isattent"]);
        }
        _unReadStr = [NSString stringWithFormat:@"%@",_conversation.unreadCount];
        if (_conversation.latestMessage.timestamp != nil ) {
            double time = [_conversation.latestMessage.timestamp doubleValue];
            _timeStr = [JCHATStringUtils getFriendlyDateString:time forConversation:YES];
        } else {
            _timeStr = @"";
        }
        _contentStr = _conversation.latestMessageContentText;
        if ([_contentStr isEqual:@"[图片]"] ||
            [_contentStr isEqual:@"[语音]"] ||
            [_contentStr isEqual:@"[位置]"]) {
            NSString *subS = [_contentStr stringByReplacingOccurrencesOfString:@"[" withString:@""];
            subS = [subS stringByReplacingOccurrencesOfString:@"]" withString:@""];
            _contentStr = [NSString stringWithFormat:@"[%@]",YZMsg(subS)];
        }
        //消息撤回处理
        if (_conversation.latestMessage && (_conversation.latestMessage.contentType == kJMSGContentTypePrompt)) {
            JMSGMessage *latesMessage = _conversation.latestMessage;
            _contentStr = YZMsg(@"你撤回了一条消息");
            if (latesMessage.isReceived) {
                _contentStr = YZMsg(@"对方撤回了一条消息");
            }
        }else if (([_uidStr isEqual:@"dsp_admin_1"] || [_uidStr isEqual:@"dsp_admin_2"]) && [dic valueForKey:@"last_msg"]) {
            _contentStr = minstr([dic valueForKey:@"last_msg"]);
            _timeStr = minstr([dic valueForKey:@"last_time"]);
        }else{
            JMSGMessage *latesMessage = _conversation.latestMessage;
            if (latesMessage.contentType == kJMSGContentTypeCustom) {
                JMSGAbstractContent *jmsContent =latesMessage.content;
                
                JMSGCustomContent *textContent = (JMSGCustomContent *)jmsContent;
                NSDictionary *lastDic = textContent.customDictionary;
                NSLog(@"22222222222=======:%@", lastDic);
                if ([minstr([lastDic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
                    _contentStr = YZMsg(@"你关注了对方，快跟Ta聊聊天吧～");
                }else{
                    _contentStr = YZMsg(@"对方已关注你，快跟Ta聊聊天吧～");
                }
            }
        }
        
    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)dic {
    return [[self alloc]initWithDic:dic];
}
@end
