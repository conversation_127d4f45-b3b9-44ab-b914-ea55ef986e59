//
//  MessageVC.m
//  iphoneLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MessageListVC.h"

#import "MessageHeaderV.h"
#import "MessageListCell.h"
#import "MessageListModel.h"

@interface MessageListVC ()<UITableViewDelegate,UITableViewDataSource>

@property(nonatomic,assign)int paging;
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)MessageHeaderV *headerV;
@property(nonatomic,strong)NSArray *models;
@property(nonatomic,strong)NSMutableArray *dataArray;

@end

@implementation MessageListVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBar.hidden = YES;
    
    self.dataArray = [NSMutableArray array];
    
    [self creatNavi];
    
    [self.view addSubview:self.tableView];
    
    
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    
}
#pragma mark - 数据更新
- (NSArray *)models {
    if (!_models) {
        NSMutableArray *m_array = [NSMutableArray array];
        for (NSDictionary *dic in _dataArray) {
            MessageModel *model = [MessageModel modelWithDic:dic];
            [m_array addObject:model];
        }
        _models = m_array;
    }
    return _models;
}
-(void)pullData {
    
    
}

#pragma mark - 点击事件

-(void)msgClickEvent:(NSString *)type {
   
    if ([type isEqual:@"粉丝"]) {
        NSLog(@"==粉丝==");
    }else if ([type isEqual:@"赞"]){
        NSLog(@"==赞==");
    }else if ([type isEqual:@"@我的"]){
        NSLog(@"==@我的==");
    }else{
        //评论
        NSLog(@"==评论==");
    }
}

#pragma mark - UITableViewDelegate、UITableViewDataSource
//删除
-(void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath{
    
    //[self.tableView reloadData];
    
}
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return _window_width/4;
    }
    return 0.01;
}
-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        _headerV = [[[NSBundle mainBundle]loadNibNamed:@"MessageHeaderV" owner:nil options:nil]objectAtIndex:0];
        _headerV.frame = CGRectMake(0, 0, _window_width, _window_width/4);
        _headerV.backgroundColor = Black_Cor;
        _headerV.headerBgV.backgroundColor = CellRow_Cor;
        _headerV.fansBtn = [PublicObj setUpImgDownText:_headerV.fansBtn space:15];
        _headerV.zanBtn = [PublicObj setUpImgDownText:_headerV.zanBtn space:15];
        _headerV.aiTeBtn = [PublicObj setUpImgDownText:_headerV.aiTeBtn space:15];
        _headerV.commentBtn = [PublicObj setUpImgDownText:_headerV.commentBtn space:15];
        WeakSelf;
        _headerV.msgEvent = ^(NSString *type) {
            [weakSelf msgClickEvent:type];
        };
        return _headerV;
    }
    return nil;
}
-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    
    return 0.01;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 80;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    
    return self.models.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    MessageCell *cell = [MessageCell cellWithTab:tableView andIndexPath:indexPath];
   
    cell.model = _models[indexPath.row];
    
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
}

#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height - 64-statusbarHeight-ShowDiff)style:UITableViewStylePlain];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = Black_Cor;
        
        WeakSelf;
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            weakSelf.paging = 1;
            [weakSelf pullData];
        }];
        
        _tableView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            weakSelf.paging +=1;
            [weakSelf pullData];
        }];
        
    }
    return _tableView;
}

#pragma mark - 导航
-(void)creatNavi {
    YBNavi *navi = [[YBNavi alloc]init];
    navi.leftHidden = YES;
    navi.rightHidden = NO;
    navi.haveImgR = YES;
    [navi ybNaviLeft:^(id btnBack) {
        
    } andRightName:@"msg_linkman" andRight:^(id btnBack) {
        NSLog(@"选择联系人");
    } andMidTitle:@"关注"];
    [self.view addSubview:navi];
}

@end
