//
//  MessageHeaderV.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void (^MsgBlock)(NSString *type);

@interface MessageHeaderV : UIView

@property(nonatomic,copy)MsgBlock msgEvent;

@property (weak, nonatomic) IBOutlet UIView *headerBgV;
@property (weak, nonatomic) IBOutlet UIButton *fansBtn;
@property (weak, nonatomic) IBOutlet UIButton *zanBtn;
@property (weak, nonatomic) IBOutlet UIButton *aiTeBtn;
@property (weak, nonatomic) IBOutlet UIButton *commentBtn;

@property (weak, nonatomic) IBOutlet UILabel *fansPoint;
@property (weak, nonatomic) IBOutlet UILabel *zanPoint;
@property (weak, nonatomic) IBOutlet UILabel *atPoint;
@property (weak, nonatomic) IBOutlet UILabel *comPoint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *fanPointWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *zanPointWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *atPointWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *comPointWidth;


- (IBAction)clickFansBtn:(UIButton *)sender;
- (IBAction)clickZanBtn:(UIButton *)sender;
- (IBAction)clickAiTeBtn:(UIButton *)sender;
- (IBAction)clickCommentBtn:(UIButton *)sender;

-(void)clearRedPoint;

@end
