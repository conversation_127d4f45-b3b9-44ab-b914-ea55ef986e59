//
//  MsgSysCell.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/8/3.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MsgSysCell.h"

@implementation MsgSysCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}
-(void)setIsWhiteStyle:(BOOL)isWhiteStyle {
    _isWhiteStyle = isWhiteStyle;
    if (_isWhiteStyle) {
        self.backgroundColor = UIColor.whiteColor;
        self.contentView.backgroundColor = UIColor.whiteColor;
        self.lineL.backgroundColor = RGB_COLOR(@"#f4f5f6", 1);
        self.titleL.textColor = RGB_COLOR(@"#000000", 1);
        self.flagIV.hidden = YES;
    }else{
        self.backgroundColor = CellRow_Cor;
        self.contentView.backgroundColor = CellRow_Cor;
    }
}
+(MsgSysCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    MsgSysCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MsgSysCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"MsgSysCell" owner:nil options:nil]objectAtIndex:0];
    }
    return cell;
}

- (void)setModel:(MsgSysModel *)model {
    _model = model;
    
    [_iconIV sd_setImageWithURL:[NSURL URLWithString:_model.iconStr]];
    _titleL.text = _model.titleStr;
    [_flagIV setImage:[UIImage imageNamed:getImagename(@"msg_gov")]];
    if ([_model.uidStr isEqual:@"dsp_admin_1"]) {
        _briefL.text = _model.briefStr;
    }else{
        _briefL.text = _model.contentStr;
    }
    _timeL.text = _model.timeStr;
    
}

@end
