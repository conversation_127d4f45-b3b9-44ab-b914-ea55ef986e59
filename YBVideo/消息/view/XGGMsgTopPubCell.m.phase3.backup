//
//  MsgTopPubCell.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/24.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MsgTopPubCell.h"

@implementation MsgTopPubCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    _iconBtn.layer.masksToBounds = YES;
    _iconBtn.layer.cornerRadius = _iconBtn.width/2;
    _iconBtn.imageView.clipsToBounds = YES;
    [_iconBtn.imageView setContentMode:UIViewContentModeScaleAspectFill];
    _iconBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
    _iconBtn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
    
    [_coverBtn.imageView setContentMode:UIViewContentModeScaleAspectFill];
    _coverBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
    _coverBtn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}
+(MsgTopPubCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    MsgTopPubCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MsgTopPubCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"MsgTopPubCell" owner:nil options:nil]objectAtIndex:0];
        
    }
    return cell;
}


- (void)setModel:(MsgTopPubModel *)model {
    _model = model;
    
    [_iconBtn sd_setImageWithURL:[NSURL URLWithString:_model.iconStr] forState:0];
    [_coverBtn sd_setImageWithURL:[NSURL URLWithString:_model.coverStr] forState:0];
    _timeL.text = _model.timeStr;
    if ([_model.pageVC isEqual:@"赞"]) {
        if ([_model.typeStr isEqual:@"0"]) {
             _contentL.text = [NSString stringWithFormat:@"%@ %@",_model.unameStr,YZMsg(@"赞了您的评论")];
        }else{
             _contentL.text = [NSString stringWithFormat:@"%@ %@",_model.unameStr,YZMsg(@"赞了您的作品")];
        }
        NSMutableAttributedString *attStr=[[NSMutableAttributedString alloc]initWithString:_contentL.text];
        [attStr addAttribute:NSForegroundColorAttributeName value:[UIColor whiteColor] range:NSMakeRange(0, _model.unameStr.length)];
        _contentL.attributedText = attStr;
    }else if ([_model.pageVC isEqual:@"@我的"]){
        NSString *video_title;
        if (_model.videoTitleStr.length <= 0) {
            video_title = YZMsg(@"无标题");
        }else if(_model.videoTitleStr.length <5){
            video_title = _model.videoTitleStr;
        }else {
            video_title = [_model.videoTitleStr stringByReplacingCharactersInRange:NSMakeRange(5, _model.videoTitleStr.length-5) withString:@"..."];
        }
        if ([_model.typeStr isEqual:@"1"]) {
            //1-上热门@
            _contentL.text = [NSString stringWithFormat:@"%@ %@",_model.unameStr,YZMsg(@"将您的作品投放至热门，快去看看吧")];
        }else {
            //0-评论@
            _contentL.text = [NSString stringWithFormat:@"%@ %@ %@ %@",_model.unameStr,YZMsg(@"在"),video_title,YZMsg(@"的评论中@了你")];
        }
                
        NSMutableAttributedString *attStr=[[NSMutableAttributedString alloc]initWithString:_contentL.text];
        [attStr addAttribute:NSForegroundColorAttributeName value:[UIColor whiteColor] range:NSMakeRange(0, _model.unameStr.length)];
        _contentL.attributedText = attStr;
    }else{//评论
        _contentL.textColor = [UIColor whiteColor];
        NSString *showDes = YZMsg(@"评论了您的作品");
        _contentL.text = [NSString stringWithFormat:@"%@ %@\n%@",_model.unameStr,showDes,_model.commentConStr];
        NSMutableAttributedString *attStr=[[NSMutableAttributedString alloc]initWithString:_contentL.text];
        [attStr addAttribute:NSForegroundColorAttributeName value:RGB_COLOR(@"#8c8c8c", 1) range:NSMakeRange(_model.unameStr.length,showDes.length+2)];
        _contentL.attributedText = attStr;
    }
    
}

- (IBAction)clickIconBtn:(UIButton *)sender {
    if ([self.delegatge respondsToSelector:@selector(iconClickUid:)]) {
        [self.delegatge iconClickUid:_model.uidStr];
    }
}

- (IBAction)clickCoverBtn:(UIButton *)sender {
    if ([self.delegatge respondsToSelector:@selector(coverClickVideoid:)]) {
        [self.delegatge coverClickVideoid:_model.videoidStr];
    }
    
}
@end
