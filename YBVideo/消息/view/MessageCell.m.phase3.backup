//
//  MessageCell.m
//  iphoneLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MessageListCell.h"
#import "MessageListModel.h"

@implementation MessageListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+(MessageListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    MessageListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MessageCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"MessageCell" owner:nil options:nil]objectAtIndex:0];
        cell.backgroundColor = CellRow_Cor;
    }
    return cell;
}

-(void)setModel:(MessageModel *)model {
    _model = model;
    [_iconIV sd_setImageWithURL:[NSURL URLWithString:_model.iconStr]];
    //官方标识
    if ([_model.uidStr isEqual:@""]) {
        _iconTag.hidden = NO;
    }else{
        _iconTag.hidden = YES;
    }
    _nameL.text = _model.unameStr;
    _detailL.text = _model.contentStr;
    _timeL.text = _model.timeStr;
    int num = [_model.unReadStr intValue];
    if (num > 0) {
        _redPoint.hidden = NO;
    }else{
        _redPoint.hidden = YES;
    }
    
}

@end
