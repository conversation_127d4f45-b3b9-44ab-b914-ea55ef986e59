#import "chatmessageCell.h"
//#import "chatmessageModel.h"
#import "UIImage+Resize.h"
#import "SDWebImage/UIButton+WebCache.h"
#import "UIImageView+WebCache.h"
#import "OrderDetailVC.h"
#import "BuyerRefundDetailVC.h"
#import "WaitSendGoodsVC.h"
#import "RefundDetailVC.h"
#import "OtherSellOrderDetailVC.h"
@interface chatmessageCell ()
{
    UILabel *_timeL;
    UIButton *_textBTN;
    UIButton *_iconBTN;
}
@end
@implementation chatmessageCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _timeL = [[UILabel alloc] init];
        _timeL.textAlignment = NSTextAlignmentCenter;
        _timeL.font = [UIFont systemFontOfSize:4];
        _timeL.textColor = Normal_TextColor;
        _timeL.font = [UIFont systemFontOfSize:16];
//        _timeL.hidden = YES;
        _textBTN = [[UIButton alloc] init];
        _textBTN.titleLabel.numberOfLines = 0;
        _textBTN.titleLabel.font = [UIFont systemFontOfSize:16];
        //设置有效的显示范围
        _textBTN.contentEdgeInsets = UIEdgeInsetsMake(10,15,10,15);
//        _textBTN.backgroundColor = [UIColor colorWithPatternImage:[UIImage imageNamed:@"order_messageBack"]];
        _textBTN.titleLabel.textColor = [UIColor whiteColor];
        _iconBTN = [[UIButton alloc] init];
        _iconBTN.layer.masksToBounds = YES;
        _iconBTN.layer.cornerRadius = 20;
        
        [self.contentView addSubview:_timeL];
        [self.contentView addSubview:_textBTN];
        [self.contentView addSubview:_iconBTN];
        self.backgroundColor=Normal_BackColor;
    }
    return self;
}
-(void)setOrdermodel:(OrderMessageModel *)ordermodel{
    _ordermodel = ordermodel;
    
    //设置数据
    [self setorderCellData];
    //设置坐标
    [self setorderCellFrame];

}
+(chatmessageCell *)cellWithTableView:(UITableView *)tableView
{
    static NSString *identifier = @"messageCell";
    chatmessageCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
         cell = [[chatmessageCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }
    return cell;
}

-(void)setorderCellFrame
{
    _textBTN.frame = _ordermodel.textR;
    _timeL.frame = _ordermodel.timeR;
    _iconBTN.frame = _ordermodel.iconR;
}
-(void)setorderCellData
{
    _timeL.text = _ordermodel.time;
    if ([_ordermodel.is_commission isEqual:@"0"]) {
        NSString *clickText = YZMsg(@"点击查看");
        NSString *name =[NSString stringWithFormat:@"%@，%@",_ordermodel.text,clickText];
        NSMutableAttributedString *contentStr = [[NSMutableAttributedString alloc] initWithString:name];
        NSRange redRange = NSMakeRange(name.length-clickText.length, clickText.length);
        [contentStr addAttributes:@{NSForegroundColorAttributeName:RGB(247,205,123),NSFontAttributeName:[UIFont systemFontOfSize:14]} range:redRange];
        [_textBTN setAttributedTitle:contentStr forState:0];

    }else{
        NSString *name =[NSString stringWithFormat:@"%@",_ordermodel.text];
        NSMutableAttributedString *contentStr = [[NSMutableAttributedString alloc] initWithString:name];
        [_textBTN setAttributedTitle:contentStr forState:0];
    }
    [_iconBTN sd_setImageWithURL:[NSURL URLWithString:_ordermodel.icon] forState:UIControlStateNormal placeholderImage:[UIImage imageNamed:@"bg1"]];
    [_textBTN setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [_textBTN setBackgroundImage:[UIImage resizableImage:@"order_messageBack"] forState:UIControlStateNormal];
    [_textBTN addTarget:self action:@selector(goOrderDetail) forControlEvents:UIControlEventTouchUpInside];
}
-(void)goOrderDetail{
    if ([_ordermodel.is_commission isEqual:@"1"]) {
        return;
    }
    //0 买家。1 卖家
    if ([_ordermodel.type isEqual:@"1"]) {
        SellOrderModel *orderModel = [SellOrderModel modelWithDic:@{}];
        orderModel.idStr =_ordermodel.orderid;
        if ([_ordermodel.status isEqual:@"1"]) {
            WaitSendGoodsVC *wait = [[WaitSendGoodsVC alloc]init];
            wait.orderModel = orderModel;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:wait animated:YES];
        }else if ([_ordermodel.status isEqual:@"5"]){
            RefundDetailVC *refund = [[RefundDetailVC alloc]init];
            refund.orderModel = orderModel;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:refund animated:YES];
        } else{
            OtherSellOrderDetailVC *other = [[OtherSellOrderDetailVC alloc]init];
            other.orderModel = orderModel;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:other animated:YES];
        }

    }else{
        if ([_ordermodel.status isEqual:@"5"]) {
            BuyerRefundDetailVC *refund = [[BuyerRefundDetailVC alloc]init];
            refund.orderId = _ordermodel.orderid;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:refund animated:YES];

        }else{
            OrderDetailVC *detail = [[OrderDetailVC alloc]init];
            detail.orderId =_ordermodel.orderid;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:detail animated:YES];

        }

    }
}
@end
