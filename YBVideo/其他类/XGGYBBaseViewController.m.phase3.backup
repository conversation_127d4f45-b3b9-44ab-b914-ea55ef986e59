//
//  YBBaseViewController.m
//  YBVideo
//
//  Created by YB007 on 2019/11/5.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBBaseViewController.h"

@interface YBBaseViewController ()

@end

@implementation YBBaseViewController

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [self setNeedsStatusBarAppearanceUpdate];
    [self popGestureChange:self enable:NO];
}
- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent;
}

-(void)popGestureChange:(UIViewController *)vc enable:(BOOL)enable{
    if ([vc.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
        //遍历所有的手势
        for (UIGestureRecognizer *popGesture in vc.navigationController.interactivePopGestureRecognizer.view.gestureRecognizers) {
            popGesture.enabled = enable;
        }
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.interactivePopGestureRecognizer.delegate = nil;
    self.navigationController.navigationBar.hidden = YES;
    if ([[UIDevice currentDevice] systemVersion].floatValue >= 11.0) {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    self.view.backgroundColor = Normal_Color;
    
    [self creatNavi];
}

- (void)creatNavi{
    
    _naviView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
    _naviView.backgroundColor = UIColor.clearColor;
    [self.view addSubview:_naviView];
    
    _subNavi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _naviView.height)];
    _subNavi.backgroundColor = CellRow_Cor;
    [_naviView addSubview:_subNavi];
    
    _leftBtn = [UIButton buttonWithType:0];
    _leftBtn.titleLabel.font = SYS_Font(15);
    [_leftBtn setTitleColor:[UIColor whiteColor] forState:0];
    _leftBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
    [_leftBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
    [_leftBtn addTarget:self action:@selector(clickNaviLeftBtn) forControlEvents:UIControlEventTouchUpInside];
    [_naviView addSubview:_leftBtn];
    
    _titleL = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2-130, 34+statusbarHeight, 260, 20)];
    _titleL.font = NaviTitle_Font;
    _titleL.textColor = NaviTitle_Color;
    _titleL.textAlignment = NSTextAlignmentCenter;
    //_titleL加到 _subNavi 做个人中心滑动隐藏效果
    [_subNavi addSubview:_titleL];
    
    _rightBtn = [UIButton buttonWithType:0];
    [_rightBtn addTarget:self action:@selector(clickNaviRightBtn) forControlEvents:UIControlEventTouchUpInside];
    _rightBtn.hidden = YES;
    _rightBtn.titleLabel.font = SYS_Font(15);
    [_rightBtn setTitleColor:Pink_Cor forState:0];
    [_naviView addSubview:_rightBtn];
    [_rightBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.height.equalTo(_titleL);
        make.right.equalTo(_naviView).offset(-10);
        make.left.greaterThanOrEqualTo(_naviView.mas_centerX).offset(50);
    }];
    _naviLine = [PublicObj lineViewWithFrame:CGRectMake(0, _subNavi.height-1, _window_width, 1) andColor:RGB_COLOR(@"#2d2a3f", 1) andView:_subNavi];
    _naviLine.hidden = YES;
    //全部已读
    _rightLeftBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [_rightLeftBtn addTarget:self action:@selector(clickNaviRLBtn) forControlEvents:UIControlEventTouchUpInside];
    _rightLeftBtn.hidden = YES;
    _rightLeftBtn.titleLabel.font = SYS_Font(12);
    [_rightLeftBtn setTitleColor:[UIColor whiteColor] forState:0];
    [_naviView addSubview:_rightLeftBtn];
    [_rightLeftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.height.equalTo(_titleL);
        make.right.equalTo(_rightBtn.mas_left).offset(-5);
    }];
    
}


- (void)clickNaviLeftBtn{
    [self dismissViewControllerAnimated:YES completion:nil];
    [self.navigationController popViewControllerAnimated:YES];
}
- (void)clickNaviRightBtn {
    
}
-(void)clickNaviRLBtn {
    
}
@end
