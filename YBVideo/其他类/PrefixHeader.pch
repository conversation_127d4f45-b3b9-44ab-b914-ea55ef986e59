#ifndef PrefixHeader_pch
#define PrefixHeader_pch

#ifdef __OBJC__

#import "MBProgressHUD+MJ.h"
#import "XGGcityDefault.h"
#import "UIImageView+WebCache.h"
//#import "UIImage+AFNetworking.h"
#import "UIButton+WebCache.h"
#import "SBJson.h"
#import "Config.h"
#import "common.h"
#import "XGGPower.h"
#import "Masonry.h"
#import "UIView+AdditionsX12.h"
#import "UIImage+RKColor.h"
#import "XGGAppDelegate.h"
#import "DspLoginVC.h"
#import "AppDelegate.h"
#import <JPUSHService.h>
#import <JMessage/JMessage.h>
#import "JChatConstants.h"
#import "BGSetting.h"
#import "PublicObj.h"
#import "PublicView.h"
#import "YBNetworking.h"
#import "MyTextField.h"
#import "MyTextView.h"
#import "UIView+LBExtension.h"
#import <SDWebImage/UIImage+GIF.h>
#import <SDWebImage/SDWebImageManager.h>
#import <SDWebImage/SDWebImageDownloader.h>
#import <MJRefresh/MJRefresh.h>
#import <IQKeyboardManager/IQKeyboardManager.h>
#import "XGGRKLBSManager.h"
#import "YBBaseViewController.h"
#import "YBAlertView.h"
#import "YBPageControl.h"
#import "YBLanguageTools.h"
#import "StorageConfig.h"
#import "YBInviteCode.h"
#import "PubH5.h"
#import "RKActionSheet.h"
#import "RKKeepAlive.h"
#import "RKSysAccess.h"
#import "YBDayTaskManager.h"
#import "YBLiveRoomAlertView.h"
#import "YBButton.h"
#import "TZImagePickerController.h"
#import <JKCategories.h>
#import <UIScrollView+EmptyDataSet.h>
#import "YBYoungManager.h"
#import "YBNavigationController.h"
#import "THeader.h"
#import "YBImManager.h"
#import "YBMessageManager.h"
#import "GDYTranslateTool.h"
#import <ImSDK_Plus/ImSDK_Plus.h>

#import "XGGNetworkManager.h"
#import "XGGNetworkUtils.h"
#import "XGGURLConstant.h"

#endif

// 环境变量-第三方配置
#if DEBUG
#import "DebugHeader.h"
#else
#import "ReleaseHeader.h"
#endif


#define YBWeakSelf __weak typeof(self) weakSelf = self;
#define minstr(a) [NSString stringWithFormat:@"%@",a]
#define strFormat(str)              ([PublicObj checkNull:[NSString stringWithFormat:@"%@",str]] \
                                    ? @"" : [NSString stringWithFormat:@"%@",str])


//rk_1-31 是否加版权展示 0-不展示 1-展示
#define showCopyright 0
#define copyrightDes @"Copyright©2013-2020,XXX All rights reserved"

//腾讯SDK 最大、最小录制时间 去 VideoRecordProcessView.h 配置
//#define MAX_RECORD_TIME             15
//#define MIN_RECORD_TIME             5
//短视频录制时长控制
#define live_MAX_RECORD_TIME             15
#define live_MIN_RECORD_TIME             5

#define ios8 [[UIDevice currentDevice].systemVersion floatValue] <9
#define iOS11Later ([UIDevice currentDevice].systemVersion.floatValue >= 11.0f)
///通知
//青少年模式时间到了
#define ybYoungModelTimeEndEvent    @"young_model_time_end"
// 青少年模式开关状态改变
#define ybYoungModelSwitchChage     @"young_model_switch_change"

//@规则
#define kATRegular @"@[\\u4e00-\\u9fa5\\w\\-\\_]+ "
//emoji规则
#define emojiPattern @"\\[\\w+\\]"

//rk_1-30
#define DoubleClickRefreshNot @"DoubleClickRefreshNot"

//颜色
#define Pink_Cor RGB_COLOR(@"#EA377F", 1)
#define Pink_Cor_Dis RGB_COLOR(@"#EA377F", 0.5)
#define Orange_Cor RGB_COLOR(@"#FF6131", 1)
#define Normal_Color RGB_COLOR(@"#110D24", 1)
#define Normal_TextColor  RGB(150, 150, 150)
#define Normal_BackColor RGB(17, 13, 35)
#define Normal_SubColor RGB(24, 21, 43)

#define RGB(r,g,b)          [UIColor colorWithRed:(r)/255.f \
green:(g)/255.f \
blue:(b)/255.f \
alpha:1.f]
#define RGBA(r, g, b, a)    [UIColor colorWithRed:(r)/255.0f green:(g)/255.0f blue:(b)/255.0f alpha:a]

#define CellRow_Cor RGB_COLOR(@"#15102C", 1)
#define SelCell_Col RGB_COLOR(@"#1c1733", 1)

#define GrayText RGB_COLOR(@"#969696", 1)
#define Line_Cor RGB_COLOR(@"#ffffff", 0.06)
#define AtCol RGB_COLOR(@"#f2e50b", 1)

//Tab
#define Tab_bgc RGB_COLOR(@"#111111", 1)
#define Tab_nor RGB_COLOR(@"#dcdcdc", 1)
#define Tab_sel RGB_COLOR(@"#ffffff", 1)
#define ybNodataCol RGB_COLOR(@"#969696", 1)
#define ybPopBgCol  RGB_COLOR(@"#000000", 0.4)
//图片
#define FOREGROUND_STAR_IMAGE_NAME @"start_select_评价"
#define BACKGROUND_STAR_IMAGE_NAME @"start_normal_评价"

#define Comm_FOREGROUND_STAR_IMAGE_NAME @"comm_star_实心"
#define Comm_BACKGROUND_STAR_IMAGE_NAME @"comm_star_空心"

#define video_FOREGROUND_STAR_IMAGE_NAME @"videocontent_评价sel"
#define video_BACKGROUND_STAR_IMAGE_NAME @"videocontent_评价normal"

//字体
#define SYS_Font(a) [UIFont systemFontOfSize:(a)]
#define ybNodataFont [UIFont systemFontOfSize:13]

//导航
#define NaviBg_Color RGB_COLOR(@"#15102c", 1)
#define NaviTitle_Color [UIColor whiteColor]
#define NaviTitle_Font [UIFont boldSystemFontOfSize:17]

//语言
#define YZMsg(key) [[YBLanguageTools shareInstance] getStringForKey:key withTable:@"InfoPlist"]
#define CurrentLanguage @"will_show_language"
#define getImagename(a) [NSString stringWithFormat:@"%@%@",a,[common getLanguage]]
#define lagType [[NSUserDefaults standardUserDefaults] objectForKey:@"will_show_language"]
#define ZH_CN @"zh-Hans"
#define EN @"en"

//
#define _pageBarWidth  _window_width *0.65

#define _window_width  [UIScreen mainScreen].bounds.size.width
#define _window_height [UIScreen mainScreen].bounds.size.height

#define RGB_COLOR(_STR_,a) ([UIColor colorWithRed:[[NSString stringWithFormat:@"%lu", strtoul([[_STR_ substringWithRange:NSMakeRange(1, 2)] UTF8String], 0, 16)] intValue] / 255.0 green:[[NSString stringWithFormat:@"%lu", strtoul([[_STR_ substringWithRange:NSMakeRange(3, 2)] UTF8String], 0, 16)] intValue] / 255.0 blue:[[NSString stringWithFormat:@"%lu", strtoul([[_STR_ substringWithRange:NSMakeRange(5, 2)] UTF8String], 0, 16)] intValue] / 255.0 alpha:a])

#define UIColorFromRGB(rgbValue) [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16))/255.0 \
green:((float)((rgbValue & 0xFF00) >> 8))/255.0 \
blue:((float)(rgbValue & 0xFF))/255.0 \
alpha:1.0]

//iPhone X
#define IS_BIG_SCREEN (IS_IPHONE && SCREEN_MAX_LENGTH > 667.0)
#define IS_IPHONE_XS_MAX (IS_IPHONE && SCREEN_MAX_LENGTH == 896.0)
#define iPhoneX _window_width >= 375.f
#define ShowDiff (iPhoneX ? 34: 0)
#define statusbarHeight ([[UIApplication sharedApplication] statusBarFrame].size.height-20)

#define topStatusBarHeight          (iPhoneX ? 44.0 : 20.0)
#define naviContentHeight           44.0
#define naviHight                   (topStatusBarHeight + naviContentHeight)
#define tabBarSafeSpace             (iPhoneX ? 34.0 : 0.0)
#define tabBarContentHeight         49.0
#define tabBarHeight                (tabBarSafeSpace + tabBarContentHeight)
//音视频通话缓存key
#define ybUnexpectedDealTime            5
#define ybAudioVideoCallWaitting        10
#define ybAudioVideoCallCtrKey          @"audio_video_call_user_enter"
#define ybIsStartCallKey                @"isStartCall"
// 呼叫方呼叫超时 显示是否预约弹窗状态
#define ybIsSubscribeWaitting           @"issSubscribeWaitting"
//进入房间缓存key
#define ybMatchRoomCtrKey               @"match_room_user_enter"
//IM呼叫者id
#define ybImCallingUid                  @"CallingUid"
//正在聊天的id
#define ybImChatingUid                  @"messageingUserID"
// IM主动刷新
#define ybImNeedRefresh                 @"imNeedRefresh"
// IM私信未读数量改变
#define ybImUnreadChange                @"ybImUnreadChange"
// 邀请、退出、撤回
#define ybImConveEvent                  @"ybImConveEvent"
// IM小窗高度改变
#define ybImSamllChange                 @"ybImSamllChangeEvent"
// IM C2C 顶部关注
#define ybImC2CFollow                   @"ybImC2CFollow"


#define IS_IPAD (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad)
#define IS_IPHONE (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone)
#define IS_RETINA ([[UIScreen mainScreen] scale] >= 2.0)
#define SCREEN_WIDTH ([[UIScreen mainScreen] bounds].size.width)
#define SCREEN_HEIGHT ([[UIScreen mainScreen] bounds].size.height)
#define SCREEN_MAX_LENGTH (MAX(SCREEN_WIDTH, SCREEN_HEIGHT))
#define SCREEN_MIN_LENGTH (MIN(SCREEN_WIDTH, SCREEN_HEIGHT))
#define IS_IPHONE_4_OR_LESS (IS_IPHONE && SCREEN_MAX_LENGTH < 568.0)
#define IS_IPHONE_5 (IS_IPHONE && SCREEN_MAX_LENGTH == 568.0)
#define IS_IPHONE_6 (IS_IPHONE && SCREEN_MAX_LENGTH == 667.0)
#define IS_IPHONE_6P (IS_IPHONE && SCREEN_MAX_LENGTH == 736.0)
#define IS_IPHONE_X (IS_IPHONE && SCREEN_MAX_LENGTH >= 812.f)

#if DEBUG
#define NSLog(FORMAT, ...) fprintf(stderr,"[%s:%d行] %s\n",[[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], __LINE__, [[NSString stringWithFormat:FORMAT, ##__VA_ARGS__] UTF8String]);
#else
#define NSLog(FORMAT, ...) nil
#endif

#endif /* PrefixHeader_pch */
