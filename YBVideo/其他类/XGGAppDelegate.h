
#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,YBAppLifeCycle) {
    APPLifeCycle_Default,           //默认
    APPLifeCycle_EnterForeground,   //进入前台
    APPLifeCycle_EnterBackground,   //进入后台
    APPLifeCycle_WillTerminate,     //杀进程
};
typedef void(^YBAppLifeCycleBlock) (YBAppLifeCycle lifeCycleType);

@interface AppDelegate : NSObject

@property(nonatomic,copy)YBAppLifeCycleBlock lifeCycleEvent;

/** 极光数据库是否正在升级 */
@property (assign, nonatomic)BOOL isDBMigrating;

@property(nonatomic,strong)NSString *jgRegistrationID;

#pragma mark - 音视频操作 开始

@property(nonatomic,strong)NSString *videoTitle;

@property(nonatomic,strong)UIImage *originalCover;
@property(nonatomic,strong)NSString *cosFinishCoverPath;

@property(nonatomic,strong)NSString *originalVdieoPath;
@property(nonatomic,strong)NSString *cosFinishVideoPath;

@property(nonatomic,strong)NSString *originalMKVideoPath;
@property(nonatomic,strong)NSString *cosFinishMKVideoPath;

@property(nonatomic,assign)CGFloat originalVideoPro;    //原始视频进度
@property(nonatomic,assign)CGFloat originalCoverPro;    //封面进度
@property(nonatomic,assign)CGFloat mkVideoPro;          //水印视频进度
@property(nonatomic,assign)int uploadNum;               //上传数量【2或3个】
@property(nonatomic,assign)CGFloat allUploadPro;        //总上传进度

@property(nonatomic,strong)NSString *is_userad;//是否是用户广告视频0否1是
@property(nonatomic,strong)NSString *userad_url;//用户广告视频链接

@property(nonatomic,strong)NSString *topicID;
@property(nonatomic,strong)NSString *videoClassID;
@property(nonatomic,strong)NSString *musicID;
@property(nonatomic,strong)NSString *shopJsonStr;       //废弃--改为goodsID
@property(nonatomic,strong)NSString *goodsID;
@property(nonatomic,strong)NSString *videoChargeNum;
@property (nonatomic, assign) BOOL allowOrentitaionRotation;

-(void)startUpload;

#pragma mark - 音视频操作 结束

-(void)uploadSuc:(NSDictionary *)dic;
@end

