//
//  sproutCommon.h
//  yunbaolive
//
//  Created by IOS1 on 2019/10/22.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface sproutCommon : NSObject
+ (void)saveSproutMessage:(NSDictionary *)dic;
+ (int)getYBskin_whiting;
+ (int)getYBskin_smooth;
+ (int)getYBskin_tenderness;
+ (int)getYBeye_brow;
+ (int)getYBbig_eye;
+ (int)getYBeye_length;
+ (int)getYBeye_corner;
+ (int)getYBeye_alat;
+ (int)getYBface_lift;
+ (int)getYBface_shave;
+ (int)getYBmouse_lift;
+ (int)getYBnose_lift;
+ (int)getYBchin_lift;
//+ (int)getskin_whiting;
+ (int)getYBforehead_lift;
+ (int)getYBlengthen_noseLift;

+(void)getMHValueFromService;
+(void)updataMHValueToService;
@end

NS_ASSUME_NONNULL_END
