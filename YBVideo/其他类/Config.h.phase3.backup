

#import <Foundation/Foundation.h>

@interface Config : NSObject

//登陆统一存储
+(void)saveUnified:(NSDictionary *)dic;
//统一清除
+(void)clearUnified;

#pragma mark - 用户ID
+(void)saveOwnID:(NSString *)save;
+(NSString *)getOwnID;

#pragma mark - 用户token
+(void)saveOwnToken:(NSString *)save;
+(NSString *)getOwnToken;

#pragma mark - 用户昵称
+(void)saveOwnNicename:(NSString *)save;
+(NSString *)getOwnNicename;

#pragma mark - 用户签名
+(void)saveOwnSignature:(NSString *)save;
+(NSString *)getOwnSignature;

#pragma mark - 年龄
+(void)saveUserAge:(NSString *)save;
+(NSString *)getUserAge;

#pragma mark - 金币
+(void)saveUserCoin:(NSString *)save;
+(NSString *)getUserCoin;

#pragma mark - 头像
+(void)saveUserAvatar:(NSString *)save;
+(NSString *)getUserAvatar;
+(void)saveUserAvatarThumb:(NSString *)save;
+(NSString *)getUserAvatarThumb;

#pragma mark - 生日
+(void)saveUserBirth:(NSString *)save;
+(NSString *)getUserBirth;

#pragma mark - 性别
+(void)saveUserSex:(NSString *)save;
+(NSString *)getUserSex;

#pragma mark - 邀请码
+(void)saveUserCode:(NSString *)save;
+(NSString *)getUserCode;

#pragma mark - 用户地址(注意不是定位地址，而是个人中心编辑的个人地址)
+(void)saveUserProvince:(NSString *)save;
+(NSString *)getUserProvince;
+(void)saveUserCity:(NSString *)save;
+(NSString *)getUserCity;
+(void)saveUserArea:(NSString *)save;
+(NSString *)getUserArea;
+(void)saveUserHomeTown:(NSString *)save;
+(NSString *)getUserHomeTown;

#pragma mark - 首次登陆
+(void)saveisreg:(NSString *)isregs;
+(NSString *)getreg;

 #pragma mark - 当从个人中心删除了视频时做个标记当返回推荐时候重新请求数据
+(void)saveSignOfDelVideo:(NSString *)str;
+(NSString *)getSignOfDelVideo;

#pragma makr - 保存提现dic
+(void)saveCashAccount:(NSDictionary *)save;
+(NSDictionary *)getCashAccount;
+(void)removeCashAccount;

#pragma mark -是否第一次签到
+(void)saveFirstBonus:(NSString *)save;
+(NSString *)getFirstBonus;

+(NSString *)lgetUserSign;
#pragma mark - 未读消息基数
+(void)saveImUnreadNum:(int)save;
+(int)getImUnreadNum;

@end
