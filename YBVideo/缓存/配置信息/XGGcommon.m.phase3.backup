#import "common.h"

NSString *const  app_ios = @"app_ios";
NSString *const  ios_shelves = @"ios_shelves";
NSString *const  ipa_ver = @"ipa_ver";
NSString *const  maintain_switch = @"maintain_switch";
NSString *const  maintain_tips = @"maintain_tips";
NSString *const  private_letter_nums = @"private_letter_nums";
NSString *const  private_letter_switch = @"private_letter_switch";
NSString *const  share_type = @"share_type";
NSString *const  video_share_title = @"video_share_title";
NSString *const  video_share_des = @"video_share_des";
NSString *const  live_share_title = @"share_title";
NSString *const  live_share_des = @"share_des";
NSString *const  live_wx_siteurl = @"wx_siteurl";
NSString *const  video_audit_switch = @"video_audit_switch";
NSString *const  name_votes = @"name_votes";
NSString *const  name_coin = @"name_coin";
NSString *const  agent_share_des = @"agent_share_des";
NSString *const  agent_share_title = @"agent_share_title";
NSString *const  YB_tisdk_key = @"sprout_key";
NSString *const  YB_tisdk_appid = @"sprout_appid";
NSString *const  YB_auth_switch = @"auth_islimit";

NSString *const  qiniu_domain = @"qiniu_domain";
NSString *const  shop_system_name = @"shop_system_name";

NSString *const  liveclass = @"liveclass";

NSString *const service_switch = @"service_switch";
NSString *const service_url = @"service_url";
NSString *const award_switch = @"award_switch";

@implementation common

+(void)saveComUnified:(NSDictionary *)dic {
    
//    [sproutCommon saveSproutMessage:dic];
    
    NSString *yb_app_ios = [NSString stringWithFormat:@"%@",[dic valueForKey:@"app_ios"]];
    NSString *yb_ios_shelves = [NSString stringWithFormat:@"%@",[dic valueForKey:@"ios_shelves"]];
    NSString *yb_ipa_ver = [NSString stringWithFormat:@"%@",[dic valueForKey:@"ipa_ver"]];
    NSString *yb_maintain_switch = [NSString stringWithFormat:@"%@",[dic valueForKey:@"maintain_switch"]];
    NSString *yb_maintain_tips = [NSString stringWithFormat:@"%@",[dic valueForKey:@"maintain_tips"]];
    NSString *yb_private_letter_nums = [NSString stringWithFormat:@"%@",[dic valueForKey:@"private_letter_nums"]];
    NSString *yb_private_letter_switch = [NSString stringWithFormat:@"%@",[dic valueForKey:@"private_letter_switch"]];
    NSArray *yb_share_type ;
    if ([[dic valueForKey:@"share_type"] isKindOfClass:[NSArray class]]) {
       yb_share_type = [NSArray arrayWithArray:[dic valueForKey:@"share_type"]];
    }else{
        yb_share_type = @[];
    }
    NSString *yb_video_share_title = [NSString stringWithFormat:@"%@",[dic valueForKey:@"video_share_title"]];
    NSString *yb_video_share_des = [NSString stringWithFormat:@"%@",[dic valueForKey:@"video_share_des"]];
    NSString *yb_live_share_title = [NSString stringWithFormat:@"%@",[dic valueForKey:@"share_title"]];
    NSString *yb_live_share_des = [NSString stringWithFormat:@"%@",[dic valueForKey:@"share_des"]];
    NSString *yb_live_wx_siteurl = [NSString stringWithFormat:@"%@",[dic valueForKey:@"wx_siteurl"]];
    NSString *yb_video_audit_switch = [NSString stringWithFormat:@"%@",[dic valueForKey:@"video_audit_switch"]];
    NSString *yb_name_votes = [NSString stringWithFormat:@"%@",[dic valueForKey:@"name_votes"]];
    NSString *yb_name_coin = [NSString stringWithFormat:@"%@",[dic valueForKey:@"name_coin"]];
    NSString *ybagent_share_des = [NSString stringWithFormat:@"%@",[dic valueForKey:@"agent_share_des"]];
    NSString *yb_agent_share_title = [NSString stringWithFormat:@"%@",[dic valueForKey:@"agent_share_title"]];
    // 萌颜授权相关
    NSString *YB_sprout_key = [NSString stringWithFormat:@"%@",[dic valueForKey:@"sprout_key_ios"]];
    NSString *YB_sprout_appid = [NSString stringWithFormat:@"%@",[dic valueForKey:@"sprout_appid_ios"]];
//    if ([[NSBundle mainBundle].bundleIdentifier isEqual:@"chatsifieds.app"]||
//        [[NSBundle mainBundle].bundleIdentifier isEqual:@"com.yunbao.1v1"]||
//        [[NSBundle mainBundle].bundleIdentifier isEqual:@"com.my.minshengji.dsp"]) {
//        YB_sprout_key = [PublicObj decrypt:YB_sprout_key];
//        YB_sprout_appid = [PublicObj decrypt:YB_sprout_appid];
//    }
    NSString *auth_switch = minstr([dic valueForKey:@"auth_islimit"]);
    
    NSString *yb_shop_system_name = minstr([dic valueForKey:@"shop_system_name"]);
    NSString *yb_qiniu_domain = minstr([dic valueForKey:@"qiniu_domain"]);

    NSArray *liveClassArr = [dic valueForKey:@"liveclass"];
//    YB_sprout_key = @"e6ed81d408d08db5280937e8d007394c";
    
    /// 客服
    NSString *yb_service_switch = minstr([dic valueForKey:@"service_switch"]);
    NSString *yb_service_url = minstr([dic valueForKey:@"service_url"]);
   
    //视频奖励云票开关
    NSString *yb_award_switch = minstr([dic valueForKey:@"watch_video_award_switch"]);

    
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    [userDefaults setObject:yb_app_ios forKey:app_ios];
    [userDefaults setObject:yb_ios_shelves forKey:ios_shelves];
    [userDefaults setObject:yb_ipa_ver forKey:ipa_ver];
    [userDefaults setObject:yb_maintain_switch forKey:maintain_switch];
    [userDefaults setObject:yb_maintain_tips forKey:maintain_tips];
    [userDefaults setObject:yb_private_letter_nums forKey:private_letter_nums];
    [userDefaults setObject:yb_private_letter_switch forKey:private_letter_switch];
    [userDefaults setObject:yb_share_type forKey:share_type];
    [userDefaults setObject:yb_video_share_title forKey:video_share_title];
    [userDefaults setObject:yb_video_share_des forKey:video_share_des];
    [userDefaults setObject:yb_live_share_title forKey:live_share_title];
    [userDefaults setObject:yb_live_share_des forKey:live_share_des];
    [userDefaults setObject:yb_live_wx_siteurl forKey:live_wx_siteurl];
    [userDefaults setObject:yb_video_audit_switch forKey:video_audit_switch];
    [userDefaults setObject:yb_name_votes forKey:name_votes];
    [userDefaults setObject:yb_name_coin forKey:name_coin];
    [userDefaults setObject:yb_agent_share_title forKey:agent_share_title];
    [userDefaults setObject:ybagent_share_des forKey:agent_share_des];
    [userDefaults setObject:YB_sprout_key forKey:YB_tisdk_key];
    [userDefaults setObject:YB_sprout_appid forKey:YB_tisdk_appid];
    [userDefaults setObject:auth_switch forKey:YB_auth_switch];

    [userDefaults setObject:yb_shop_system_name forKey:shop_system_name];
    [userDefaults setObject:yb_qiniu_domain forKey:qiniu_domain];
    [userDefaults setObject:liveClassArr forKey:liveclass];
    
    [userDefaults setObject:yb_service_switch forKey:service_switch];
    [userDefaults setObject:yb_service_url forKey:service_url];
    [userDefaults setObject:yb_award_switch forKey:award_switch];

    [userDefaults synchronize];
}
#pragma mark - iOS下载地址
+(NSString *)app_ios{
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* app_ioss = [userDefaults objectForKey: app_ios];
    return app_ioss;
}

#pragma mark - iOS上架版本号
+(NSString *)ios_shelves{
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* ios_shelvess = [userDefaults objectForKey: ios_shelves];
    return ios_shelvess;
}

#pragma mark - iOS最新版本号
+(NSString *)ipa_ver{
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* ipa_vers = [userDefaults objectForKey: ipa_ver];
    return ipa_vers;
}

#pragma mark - 维护开关
+(NSString *)maintain_switch {
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString *maintain_switchs = [userDefaults objectForKey:maintain_switch];
    return maintain_switchs;
}
+(NSString *)maintain_tips {
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString *maintain_tipss = [userDefaults objectForKey: maintain_tips];
    return maintain_tipss;
}

#pragma mark - 私信限制
+(NSString *)private_letter_nums {
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString *private_letter_numss = [userDefaults objectForKey: private_letter_nums];
    return private_letter_numss;
}
+(NSString *)private_letter_switch {
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString *private_letter_switchs = [userDefaults objectForKey: private_letter_switch];
    return private_letter_switchs;
}

#pragma mark - 分享类型
+(NSArray  *)share_type{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSArray *share_typess = [userDefaults objectForKey:share_type];
    return share_typess;
}
+(NSString *)video_share_title{
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* share_titles = [userDefaults objectForKey: video_share_title];
    return share_titles;
}
+(NSString *)video_share_des{
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* share_titles = [userDefaults objectForKey: video_share_des];
    return share_titles;
}
+(NSString *)live_share_title {
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* share_titles = [userDefaults objectForKey: live_share_title];
    return share_titles;
}
+(NSString *)live_share_des {
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* share_titles = [userDefaults objectForKey: live_share_des];
    return share_titles;
}
+(NSString *)live_wx_siteurl {
    NSUserDefaults *userDefaults = [[NSUserDefaults alloc]init ];
    NSString* share_titles = [userDefaults objectForKey: live_wx_siteurl];
    return share_titles;
}
#pragma mark - 后台审核开关
+(NSString *)getAuditSwitch {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *auditSwitch = [userDefaults objectForKey:video_audit_switch];
    return auditSwitch;
}

#pragma mark - 视频奖励云票开关
+(NSString *)getVideoAwardSwitch{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *auditSwitch = [userDefaults objectForKey:award_switch];
    return auditSwitch;
}

//#pragma mark - 萌颜参数 0梦颜  1-腾讯
//+(void)saveIsTXfiter:(NSString *)save {
//    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
//    [userDefaults setObject:save forKey:@"rk_ti_tx_fiter"];
//    [userDefaults synchronize];
//}
//+(NSString *)getIsTXfiter {
//    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
//    NSString *getStr = [userDefaults objectForKey:@"rk_ti_tx_fiter"];
//    return getStr;
//}

//搜索历史
+(void)saveHistory:(NSArray *)array {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:array forKey:@"search_history"];
    [userDefaults synchronize];
}
+(NSArray *)getHistoryArray {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSArray *share_typess = [userDefaults objectForKey:@"search_history"];
    return share_typess;
}


/** 服务端使用字段（app每次启动传值1，分页>1传值0） */
+(void)saveIsStart:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"com_isstart"];
    [userDefaults synchronize];
}
+(NSString *)getIsStart {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"com_isstart"];
    return getStr;
}
+(NSString *)name_coin{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *name = [userDefaults objectForKey:name_coin];
    return name;
}
+(NSString *)name_votes{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *name = [userDefaults objectForKey:name_votes];
    return name;
}
+(NSString *)agent_share_title{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *name = [userDefaults objectForKey:agent_share_title];
    return name;
}
+(NSString *)agent_share_des{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *name = [userDefaults objectForKey:agent_share_des];
    return name;
}
+(NSString *)getTISDKKey{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *name = [userDefaults objectForKey:YB_tisdk_key];
    return name;
}
+(NSString *)getTISDKAppid{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *name = [userDefaults objectForKey:YB_tisdk_appid];
    return name;
}
#pragma mark - 认证
+(NSString *)getAuthSwitch {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *name = [userDefaults objectForKey:YB_auth_switch];
    return name;
}
//预留,默认返回1
+(NSString *)letter_switch {
    return @"1";
}
/** 视频分类 */
+(void)saveVideoClass:(NSArray *)array {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:array forKey:@"yb_video_class"];
    [userDefaults synchronize];
}
+(NSArray *)getVideoClass {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSArray *share_typess = [userDefaults objectForKey:@"yb_video_class"];
    return share_typess;
}
/** 获取语言状态 */
+(NSString *)getLanguage;{
    if ([lagType isEqual:ZH_CN]) {
        return @"";
    }else{
        return @"_en";
    }
    
}
/** 私信音效开关：默认开 */
+(void)saveMsgVoiceSwitch:(BOOL)save;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    //不要存bool,转为字符串
    [userDefaults setObject:[NSString stringWithFormat:@"%d",save] forKey:@"yb_msg_voice"];
    [userDefaults synchronize];
}
+(BOOL)getMsgVoiceSwitch;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL value;
    //默认开
    if (![userDefaults objectForKey:@"yb_msg_voice"]) {
        value = YES;
    }else {
        value = [minstr([userDefaults objectForKey:@"yb_msg_voice"]) boolValue];
    }
    return value;
}
//zl----待修改
+(NSString *)qiniu_domain{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *sprout_keyss = [userDefaults objectForKey:qiniu_domain];
    return sprout_keyss;
}
+(NSString *)shop_system_name{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *sprout_keyss = [userDefaults objectForKey:shop_system_name];
    return sprout_keyss;
}
+(NSArray *)liveclass{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSArray *liveclasss = [userDefaults objectForKey:liveclass];
    return liveclasss;
}

+(int)getServiceSwitch{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    int getVal = [minstr([userDefaults objectForKey:service_switch]) intValue];
    return getVal;
}
+(NSString *)getServiceUrl {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getVal = [userDefaults objectForKey:service_url];
    return getVal;
}
@end
