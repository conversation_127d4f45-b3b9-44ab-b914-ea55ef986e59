//
//  common.h
//  YBVideo
//
//  Created by 王敏欣 on 2017/1/18.
//  Copyright © 2017年 cat. All rights reserved.
//
#import <Foundation/Foundation.h>

#import "sproutCommon.h"

@interface common : NSObject

//统一保存后台设置
+(void)saveComUnified:(NSDictionary *)dic;

#pragma mark - iOS下载地址
+(NSString *)app_ios;

#pragma mark - iOS上架版本号
+(NSString *)ios_shelves;

#pragma mark - iOS最新版本号
+(NSString *)ipa_ver;

#pragma mark - 维护开关
+(NSString *)maintain_switch;
+(NSString *)maintain_tips;

#pragma mark - 私信限制
+(NSString *)private_letter_nums;
+(NSString *)private_letter_switch;

#pragma mark - 分享类型 短视频+直播
+(NSArray  *)share_type;
+(NSString *)video_share_title;
+(NSString *)video_share_des;
+(NSString *)live_share_title;
+(NSString *)live_share_des;
+(NSString *)live_wx_siteurl;//暂时使用下载链接

#pragma mark - 后台审核开关
+(NSString *)getAuditSwitch;

#pragma mark - 美颜key
+(NSString *)getTISDKKey;
+(NSString *)getTISDKAppid;

#pragma mark - 认证
+(NSString *)getAuthSwitch;

//#pragma mark - 萌颜参数
//+(void)saveIsTXfiter:(NSString *)save;
//+(NSString *)getIsTXfiter;

//搜索历史
+(void)saveHistory:(NSArray *)array;
+(NSArray *)getHistoryArray;

/** 服务端使用字段（app每次启动传值1，分页>1传值0） */
+(void)saveIsStart:(NSString *)save;
+(NSString *)getIsStart;

+(NSString *)name_coin;
+(NSString *)name_votes;

#pragma mark - 邀请分享
+(NSString *)agent_share_title;
+(NSString *)agent_share_des;

//预留,默认返回1
+(NSString *)letter_switch;

/** 视频分类 */
+(void)saveVideoClass:(NSArray *)array;
+(NSArray *)getVideoClass;

/** 获取语言状态 */
+(NSString *)getLanguage;

/** 私信音效开关：默认开 */
+(void)saveMsgVoiceSwitch:(BOOL)save;
+(BOOL)getMsgVoiceSwitch;

+(NSString *)qiniu_domain;
+(NSString *)shop_system_name;

+(NSArray *)liveclass;

/** 客服开关 */
+(int)getServiceSwitch;
+(NSString *)getServiceUrl;

+(NSString *)getVideoAwardSwitch;
@end
