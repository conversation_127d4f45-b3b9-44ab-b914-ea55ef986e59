//
//  XGGPower.h
//  YBVideo
//
//  Created by YB007 on 2019/11/27.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XGGPower : NSObject

//统一存储
+(void)saveUnified:(NSDictionary *)dic;
//统一清除
+(void)clearUnified;

/**  是否可直播  0 否  1 是 */
+(NSString *)getLiveStatus;

/** 开播受限提示语 */
+(NSString *)getLiveMsg;

/** 是否可发布视频  0 否  1 是 */
+(NSString *)getVideoStatus;

/** 发布视频受限 提示语 */
+(NSString *)getVideoMsg;

/** 是否可发布长视频  0 否 1 是 */
+(NSString *)getCanTakeLongVideo;
+(void)saveCanTakeLongVideo:(NSString *)save;

/** vip开关  0关闭 1打开 */
+(NSString *)getVipSwitch;

/** 发布视频时 是否可以设定价格  0  否  1  是 */
+(NSString *)getVideoCharge;

/** 发布视频时是否可发布商品 0 否 1 是 */
+(NSString *)getIsShop;

/*isad  是否可发布视频 0 否 1 是*/
+(NSString *)getIsad;


#pragma mark - 直播封禁提示
/// 禁用状态
+(void)saveBanLiveStatus:(BOOL)save;
+(BOOL)getBanLiveStatus;
/// 禁用时间
+(void)saveBanLiveMsg:(NSString *)save;
+(NSString *)getBanLiveMsg;


@end

NS_ASSUME_NONNULL_END
