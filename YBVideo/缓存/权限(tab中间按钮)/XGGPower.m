//
//  XGGPower.m
//  YBVideo
//
//  Created by YB007 on 2019/11/27.
//  Copyright © 2019 cat. All rights reserved.
//

#import "XGGPower.h"

NSString * const YBLiveStatus = @"yb_live_status";
NSString * const YBLiveMsg = @"yb_live_msg";
NSString * const YBVideoStatus = @"yb_video_status";
NSString * const YBVideoMsg = @"yb_video_msg";
NSString * const YBVideoCharge = @"yb_setvideo_charge";
NSString * const YBIsShop = @"yb_isshop";
NSString * const YBLongVideoStatus = @"long_video_status";
NSString * const YBVipSwitch = @"vip_switch";
NSString * const YBISADSwitch = @"isad_switch";


@implementation XGGPower
//统一存储
+(void)saveUnified:(NSDictionary *)dic {
    NSString *yb_live_status = minstr([dic valueForKey:@"live_status"]);
    NSString *yb_live_msg = minstr([dic valueForKey:@"live_msg"]);
    NSString *yb_video_status = minstr([dic valueForKey:@"video_status"]);
    NSString *yb_video_msg = minstr([dic valueForKey:@"video_msg"]);
    NSString *yb_video_charge = minstr([dic valueForKey:@"setvideo_charge"]);
    NSString *yb_is_sop = minstr([dic valueForKey:@"isshop"]);
    NSString *yb_long_video_status = minstr([dic valueForKey:@"long_video_status"]);
    NSString *yb_vip_switch = minstr([dic valueForKey:@"vip_switch"]);
    NSString *yb_isad_switch = minstr([dic valueForKey:@"isad"]);
    
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:yb_live_status forKey:YBLiveStatus];
    [userDefaults setObject:yb_live_msg forKey:YBLiveMsg];
    [userDefaults setObject:yb_video_status forKey:YBVideoStatus];
    [userDefaults setObject:yb_video_msg forKey:YBVideoMsg];
    [userDefaults setObject:yb_video_charge forKey:YBVideoCharge];
    [userDefaults setObject:yb_is_sop forKey:YBIsShop];
    [userDefaults setObject:yb_long_video_status forKey:YBLongVideoStatus];
    [userDefaults setObject:yb_vip_switch forKey:YBVipSwitch];
    [userDefaults setObject:yb_isad_switch forKey:YBISADSwitch];

    [userDefaults synchronize];
    
}
//统一清除
+(void)clearUnified {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults removeObjectForKey:YBLiveStatus];
    [userDefaults removeObjectForKey:YBLiveMsg];
    [userDefaults removeObjectForKey:YBVideoStatus];
    [userDefaults removeObjectForKey:YBVideoMsg];
    [userDefaults removeObjectForKey:YBVideoCharge];
    [userDefaults removeObjectForKey:YBIsShop];
    [userDefaults removeObjectForKey:YBLongVideoStatus];
    [userDefaults removeObjectForKey:YBVipSwitch];
    [userDefaults removeObjectForKey:YBISADSwitch];

    [userDefaults synchronize];
}

+(NSString *)getLiveStatus {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBLiveStatus];
    return getStr;
}
+(NSString *)getLiveMsg {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBLiveMsg];
    return getStr;
}
+(NSString *)getVideoStatus {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBVideoStatus];
    return getStr;
}
+(NSString *)getVideoMsg {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBVideoMsg];
    return getStr;
}
+(NSString *)getCanTakeLongVideo {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBLongVideoStatus];
    return getStr;
}
+(void)saveCanTakeLongVideo:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:YBLongVideoStatus];
    [userDefaults synchronize];
}
+(NSString *)getVipSwitch {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBVipSwitch];
    return getStr;
}
+(NSString *)getVideoCharge {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBVideoCharge];
    return getStr;
}
+(NSString *)getIsShop {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBIsShop];
    return getStr;
}
+(NSString *)getIsad {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:YBISADSwitch];
    return getStr;
}


#pragma mark - 直播封禁提示
/// 禁用状态
+(void)saveBanLiveStatus:(BOOL)save;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:save forKey:@"yb_live_ban_status"];
    [userDefaults synchronize];
}
+(BOOL)getBanLiveStatus;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    BOOL getVal = [userDefaults boolForKey:@"yb_live_ban_status"];
    return getVal;
}
/// 禁用时间
+(void)saveBanLiveMsg:(NSString *)save;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"yb_live_ban_msg"];
    [userDefaults synchronize];
}
+(NSString *)getBanLiveMsg;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getVal = [userDefaults objectForKey:@"yb_live_ban_msg"];
    return getVal;
}
@end
