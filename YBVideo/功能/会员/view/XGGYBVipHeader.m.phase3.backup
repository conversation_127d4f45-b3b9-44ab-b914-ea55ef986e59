//
//  YBVipHeader.m
//  YBVideo
//
//  Created by YB007 on 2019/11/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBVipHeader.h"
#import "UIImage+RKImgCategory.h"

@implementation YBVipHeader

- (void)awakeFromNib {
    [super awakeFromNib];
    //会员用户享有平台尊贵特权---会员特权
    _topDesL.text = YZMsg(@"会员用户享有平台尊贵特权");
    _botTitleL.text = YZMsg(@"会员特权");
    
    
}
-(void)setSubView {
   [_botTitleView layoutIfNeeded];
   
   [_iconIV sd_setImageWithURL:[NSURL URLWithString:minstr([Config getUserAvatar])]];
   _nameL.text = [Config getOwnNicename];
   
   [_buyVipBtn setBackgroundImage:[UIImage rk_gradientColorImageFromColors:@[RGB_COLOR(@"#f1d7b2", 1),RGB_COLOR(@"#e0b77a", 1)] gradientType:RKGradientTypeLeftToRight imgSize:_buyVipBtn.size]];
   
   UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:_botTitleView.bounds  byRoundingCorners:UIRectCornerTopRight|UIRectCornerTopLeft cornerRadii:CGSizeMake(20, 20)];
   CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
   maskLayer.frame = _botTitleView.bounds;
   maskLayer.path = maskPath.CGPath;
   _botTitleView.layer.mask = maskLayer;
     
}
- (IBAction)clickBuyVipBtn:(id)sender {
    
    if (self.vipEvent) {
        self.vipEvent(@"1");
    }
    
}


@end
