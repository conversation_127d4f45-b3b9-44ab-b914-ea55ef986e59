//
//  YBVipHeader.h
//  YBVideo
//
//  Created by YB007 on 2019/11/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>


typedef void (^YBVipHeaderBlock)(NSString *eventStr);



@interface YBVipHeader : UIView

@property(nonatomic,copy)YBVipHeaderBlock vipEvent;

@property (weak, nonatomic) IBOutlet UIView *topView;
@property (weak, nonatomic) IBOutlet UIView *botView;

@property (weak, nonatomic) IBOutlet UIImageView *iconIV;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UILabel *timeL;
@property (weak, nonatomic) IBOutlet UIButton *buyVipBtn;

@property (weak, nonatomic) IBOutlet UIView *botTitleView;


@property (weak, nonatomic) IBOutlet UILabel *topDesL;

@property (weak, nonatomic) IBOutlet UILabel *botTitleL;

-(void)setSubView;

@end


