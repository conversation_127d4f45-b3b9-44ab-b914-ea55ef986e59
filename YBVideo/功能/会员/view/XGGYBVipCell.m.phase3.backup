//
//  YBVipCell.m
//  YBVideo
//
//  Created by YB007 on 2019/11/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBVipCell.h"

@implementation YBVipCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+(YBVipCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index {
    YBVipCell *cell = [table dequeueReusableCellWithIdentifier:@"YBVipCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"YBVipCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.backgroundColor = [UIColor whiteColor];
    cell.contentView.backgroundColor = [UIColor whiteColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

@end
