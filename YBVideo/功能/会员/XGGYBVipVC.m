//
//  YBVipVC.m
//  YBVideo
//
//  Created by YB007 on 2019/11/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBVipVC.h"
#import "YBVipCell.h"
#import "YBVipHeader.h"
#import "vipBuyView.h"
#import "YBRechargeType.h"
@interface YBVipVC ()<UITableViewDelegate,UITableViewDataSource>
{
    NSDictionary *_infoDic;
    NSArray *_vipLimiteArray;
    vipBuyView *buyView;
}
@property(nonatomic,strong)YBVipHeader *headerView;
@property(nonatomic,strong)UITableView *tableView;

@end

@implementation YBVipVC

-(void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self pullData];
}
- (void)clickNaviLeftBtn {
    [super clickNaviLeftBtn];
    [[YBRechargeType chargeManeger]removePayNotice];
}
-(void)dealloc {
    [[YBRechargeType chargeManeger]removePayNotice];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    //支付监听
    [[YBRechargeType chargeManeger]addPayNotice];
    self.titleL.text = YZMsg(@"会员中心");
    self.subNavi.backgroundColor = [UIColor clearColor];
    self.view.backgroundColor = [UIColor whiteColor];
    
    _infoDic = [NSDictionary dictionary];
    _vipLimiteArray = [NSArray array];
    
    [self.view addSubview:self.tableView];
    if (@available(iOS 11.0,*)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    [self.view bringSubviewToFront:self.naviView];
    
    [self.tableView reloadData];
}
-(void)pullData {
    [YBNetworking postWithUrl:@"User.getVip" Dic:@{@"type":@"1",@"version_ios":[PublicObj getAppBuild]} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            _infoDic = [info firstObject];
            NSDictionary *userInfo = [_infoDic valueForKey:@"userinfo"];
            NSDictionary *vipInfo = [userInfo valueForKey:@"vipinfo"];
            NSString *isVip = minstr([vipInfo valueForKey:@"isvip"]);
            NSString *vipEndTime = [NSString stringWithFormat:@"%@%@",[vipInfo valueForKey:@"vip_endtime"],YZMsg(@"到期")];
            if ([isVip isEqual:@"1"]) {
                [XGGPower saveCanTakeLongVideo:@"1"];
                _headerView.timeL.text = vipEndTime;
                [_headerView.buyVipBtn setTitle:YZMsg(@"续费VIP") forState:0];
            }else {
                _headerView.timeL.text = YZMsg(@"暂未开通会员");
                [_headerView.buyVipBtn setTitle:YZMsg(@"立即开通") forState:0];
            }
            
            _vipLimiteArray = [NSArray arrayWithArray:[_infoDic valueForKey:@"equity_lists"]];
            
            [_tableView reloadData];
            
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
#pragma mark - UITableViewDelegate、UITableViewDataSource
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    
    return (_window_width*280/375 + 110);
}
-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    
    return self.headerView;
}
-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    
    return 0;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return nil;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 60;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    
    return _vipLimiteArray.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBVipCell *cell = [YBVipCell cellWithTab:tableView index:indexPath];
    NSDictionary *subDic = _vipLimiteArray[indexPath.row];
    [cell.flagIV sd_setImageWithURL:[NSURL URLWithString:minstr([subDic valueForKey:@"thumb"])]];
    cell.nameL.text = minstr([subDic valueForKey:@"title"]);
    cell.infoL.text = minstr([subDic valueForKey:@"des"]);
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
}

#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height)style:UITableViewStyleGrouped];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.bounces = NO;
        _tableView.contentInset = UIEdgeInsetsMake(0, 0, ShowDiff, 0);
        
    }
    return _tableView;
}
- (YBVipHeader *)headerView {
    if (!_headerView) {
        _headerView = [[[NSBundle mainBundle]loadNibNamed:@"YBVipHeader" owner:nil options:nil]objectAtIndex:0];
        _headerView.frame = CGRectMake(0, 0, _window_width, (_window_width*280/375 + 110));
        [_headerView setSubView];
        YBWeakSelf;
        _headerView.vipEvent = ^(NSString *eventStr) {
            //开通
            [weakSelf buyVipPop];
        };
        YBButton *youngBtn = [PublicObj youngAlertBtn];
        [_headerView addSubview:youngBtn];
        [youngBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.lessThanOrEqualTo(_headerView);
            make.centerX.equalTo(_headerView);
            make.top.equalTo(_headerView.buyVipBtn.mas_bottom).offset(8);
        }];
    }
    return _headerView;
}

#pragma mark - 购买

-(void)buyVipPop {
    if (!buyView) {
        buyView = [[vipBuyView alloc]initWithMsg:_infoDic];
        [self.view addSubview:buyView];
    }
    YBWeakSelf;
    buyView.block = ^{
        [weakSelf pullData];
    };
    [buyView show];
    [self.view bringSubviewToFront:buyView];
}

@end
