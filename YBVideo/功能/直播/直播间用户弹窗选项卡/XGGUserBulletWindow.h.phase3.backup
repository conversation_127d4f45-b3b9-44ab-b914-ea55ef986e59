//
//  UserBulletWindow.h
//  yunbaolive
//
//  Created by IOS1 on 2019/6/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol UserBulletWindowDelegate <NSObject>

@optional;
- (void)removeButtleView;
-(void)socketShutUp:(NSString *)name andID:(NSString *)ID andType:(NSString *)type;
-(void)socketkickuser:(NSString *)name andID:(NSString *)ID;
-(void)pushZhuYe:(NSString *)IDS;
-(void)siXin:(NSString *)icon andName:(NSString *)name andID:(NSString *)ID andIsatt:(NSString *)isatt;
-(void)setAdminSuccess:(NSString *)isadmin andName:(NSString *)name andID:(NSString *)ID;
-(void)adminList;
-(void)superAdmin:(NSString *)state andBanMsg:(NSString *)banMsg;
//-(void)setLabel:(NSString *)touid;
-(void)doReportAnchor:(NSString *)touid;

@end
@interface UserBulletWindow : UIView
- (instancetype)initWithUserID:(NSString *)touid andIsAnchor:(BOOL)isanchor andAnchorID:(NSString *)liveid;
@property (nonatomic,weak) id<UserBulletWindowDelegate> delegate;
@property (nonatomic,strong) NSString *stream;

-(void)destroySubs;
@end

NS_ASSUME_NONNULL_END
