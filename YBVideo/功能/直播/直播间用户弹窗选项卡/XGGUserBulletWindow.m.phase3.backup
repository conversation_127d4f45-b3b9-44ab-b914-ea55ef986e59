//
//  UserBulletWindow.m
//  yunbaolive
//
//  Created by IOS1 on 2019/6/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "UserBulletWindow.h"
#import "userLevelView.h"
#import "CSActionSheet.h"
#import "CSActionPicker.h"
#import "YBLiveRoomAlertView.h"

@implementation UserBulletWindow{
    NSString *toUid;
    NSString *liveUid;
    BOOL isAnchor;
    UIView *moveView;
    UIButton *leftButton;
    UIButton *guanliButton;
    UIImageView *iconImgView;
    UILabel *nameLabel;
    UIImageView *sexImgView;
    UILabel *idLabel;
    UILabel *cityLabel;
    
    UILabel *signLabel;
    userLevelView *userLevel;
    userLevelView *anchorLevel;
    UIView *impressView;
    UIButton *addImpressBtn;
    UILabel *fansLabel;
    UILabel *attentLabel;
    UILabel *payLabel;
    UILabel *incomeLabel;
    UIButton *attionButton;
    UIButton *msgButton;
    UIButton *homeButton;

    NSArray *guanliArrays;
    CSActionSheet *_myActionSheet;
    NSString *userName;
    NSString *avatar_str;
    
    /// 封禁选择
    NSArray *_banOptionArray;
    YBLiveRoomAlertView *_banAlert;
    NSString *_banSelId;
}
#define wordColor RGB_COLOR(@"#323232", 1)
- (void)closebtnClick{
    [UIView animateWithDuration:0.2 animations:^{
        moveView.y = _window_height;
    }completion:^(BOOL finished) {
        [self.delegate removeButtleView];
    }];
}
- (instancetype)initWithUserID:(NSString *)touid andIsAnchor:(BOOL)isanchor andAnchorID:(NSString *)liveid{
    self = [super init];
    self.frame = CGRectMake(0, 0, _window_width, _window_height);
    isAnchor = isanchor;
    toUid = touid;
    liveUid = liveid;
    if (self) {
        [self creatUI];
        [self requestData];
    }
    return self;
}
- (void)creatUI{
    moveView = [[UIView alloc]init];
    [self addSubview:moveView];
    UIView *backView = [[UIView alloc]init];
    backView.backgroundColor = [UIColor whiteColor];
    backView.clipsToBounds = YES;
    [moveView addSubview:backView];
    //判断自己还是主播来确定frame
    CGFloat viewHeight;
    if (isAnchor) {
        if ([toUid isEqual:[Config getOwnID]]) {
            viewHeight = (250+ShowDiff);
        }else{
            viewHeight = (345+ShowDiff)-45;
        }
    }else{
        if ([toUid isEqual:liveUid]) {
            viewHeight = (345+ShowDiff)-45;    //减去的45是等级背景高度
        }else{
            viewHeight = (345+ShowDiff)-45;
        }
    }
    moveView.frame = CGRectMake(0, _window_height, _window_width, viewHeight);

    backView.frame = CGRectMake(0, 35, _window_width, moveView.height-35);
    backView.layer.mask = [PublicObj setViewLeftTop:20 andRightTop:20 andView:backView];
    //透明关闭按钮
    UIButton *closebtn = [UIButton buttonWithType:0];
    closebtn.frame = CGRectMake(0, 0, _window_width, _window_height- (moveView.height));
    [closebtn addTarget:self action:@selector(closebtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:closebtn];
    
    UIImageView *backImgview = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_width * 0.53)];
    backImgview.image = [UIImage imageNamed:@"bullet_back"];
    backImgview.userInteractionEnabled = YES;
    [backView addSubview:backImgview];
    
    iconImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width/2-35, 0, 70, 70)];
    iconImgView.layer.cornerRadius = 35;
    iconImgView.layer.masksToBounds = YES;
    iconImgView.layer.borderColor = [UIColor whiteColor].CGColor;
    iconImgView.layer.borderWidth = 2;
    [moveView addSubview:iconImgView];
    
    leftButton = [UIButton buttonWithType:0];
    leftButton.frame = CGRectMake(10, 10, 40, 30);
    [leftButton addTarget:self action:@selector(leftButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [leftButton setTitle:YZMsg(@"举报") forState:0];
    [leftButton setTitleColor:RGB_COLOR(@"#646464", 1) forState:0];
    leftButton.titleLabel.font = [UIFont systemFontOfSize:11];
    
    [backView addSubview:leftButton];
    leftButton.hidden = YES;
    guanliButton = [UIButton buttonWithType:0];
    guanliButton.frame = CGRectMake(10, 10, 40, 30);
    [guanliButton addTarget:self action:@selector(gunanliButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [guanliButton setImage:[UIImage imageNamed:@"userMsg_set"] forState:0];
    guanliButton.imageEdgeInsets = UIEdgeInsetsMake(5, 10, 5, 10);
    [backView addSubview:guanliButton];
    guanliButton.hidden = YES;
    
    //姓名
    nameLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, 40, _window_width, 20)];
    nameLabel.font = [UIFont boldSystemFontOfSize:15];
    nameLabel.textColor = wordColor;
    [backView addSubview:nameLabel];
    
    //性别
    sexImgView = [[UIImageView alloc]initWithFrame:CGRectMake(nameLabel.right+3, nameLabel.top+2.5, 15, 15)];
    [backView addSubview:sexImgView];
    
    //ID
    idLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, nameLabel.bottom+7, _window_width/2-15, 20)];
    idLabel.textAlignment = NSTextAlignmentRight;
    idLabel.font = [UIFont systemFontOfSize:12];
    idLabel.textColor = wordColor;
    [backView addSubview:idLabel];
    
    [PublicObj lineViewWithFrame:CGRectMake(_window_width/2-0.5, idLabel.top+3, 1, 14) andColor:wordColor andView:backView];
    
    UIImageView *addressImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width/2+15, idLabel.top + 5, 10, 10)];
    addressImgView.image = [UIImage imageNamed:@"bullet_定位"];
    addressImgView.contentMode = UIViewContentModeScaleAspectFit;
    [backView addSubview:addressImgView];
    
    //城市
    cityLabel = [[UILabel alloc]initWithFrame:CGRectMake(addressImgView.right+5, idLabel.top, _window_width/2-40, 20)];
    cityLabel.font = [UIFont systemFontOfSize:12];
    cityLabel.textColor = wordColor;
    [backView addSubview:cityLabel];

    //用户等级
    userLevel = [[userLevelView alloc]initWithFrame:CGRectMake(_window_width/2-115, idLabel.bottom+10, 100, 45)];
    userLevel.height = 0;
    userLevel.nameL.text = @"";//YZMsg(@"用户等级");
    [backView addSubview:userLevel];
    
    //主播等级
    anchorLevel = [[userLevelView alloc]initWithFrame:CGRectMake(_window_width/2+15, idLabel.bottom+10, 100, 45)];
    anchorLevel.height = 0;
    anchorLevel.nameL.text = @"";//YZMsg(@"主播等级");
    [backView addSubview:anchorLevel];

    //签名
    signLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, userLevel.bottom+20, _window_width, 20)];
    signLabel.font = [UIFont systemFontOfSize:12];
    signLabel.textColor = RGB_COLOR(@"#646464", 1);
    signLabel.textAlignment = NSTextAlignmentCenter;
    [backView addSubview:signLabel];

    if ([toUid isEqual:liveUid]) {
        impressView = [[UIView alloc]initWithFrame:CGRectMake(0, signLabel.bottom, _window_width, 62)];
        impressView.height = 0;
        [backView addSubview:impressView];
        if (!isAnchor) {
            addImpressBtn = [UIButton buttonWithType:0];
            addImpressBtn.frame = CGRectMake(impressView.width/2-37.5, 18, 75, 26);
            [addImpressBtn setBackgroundColor:RGB_COLOR(@"#F0F0F0", 1)];
            [addImpressBtn setTitle:YZMsg(@"添加印象") forState:0];
            [addImpressBtn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
            addImpressBtn.titleLabel.font = [UIFont systemFontOfSize:11];
            addImpressBtn.layer.cornerRadius = 13;
            addImpressBtn.layer.masksToBounds = YES;
            [addImpressBtn addTarget:self action:@selector(addImpressBtnClick) forControlEvents:UIControlEventTouchUpInside];
            [impressView addSubview:addImpressBtn];
            addImpressBtn.hidden = YES;
        }
    }
//    NSArray *array = @[YZMsg(@"关注"),YZMsg(@"粉丝"),[NSString stringWithFormat:@"%@%@",YZMsg(@"送出"),[common name_coin]],[NSString stringWithFormat:@"%@%@",YZMsg(@"收入"),[common name_votes]]];
    for (int i = 0; i < 4; i ++) {
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, impressView.height==0 ? signLabel.bottom+25 : impressView.bottom + 8, _window_width/4, 46)];
        label.centerX = _window_width / 8 * (i * 2 + 1);
        label.textColor = wordColor;
        label.numberOfLines = 2;
        label.font = [UIFont systemFontOfSize:15];
        label.textAlignment = NSTextAlignmentCenter;
        [backView addSubview:label];
        switch (i) {
            case 0:
                attentLabel = label;
                break;
            case 1:
                fansLabel = label;
                break;
            case 2:
                payLabel = label;
                break;
            case 3:
                incomeLabel = label;
                break;

            default:
                break;
        }
    }
    [PublicObj lineViewWithFrame:CGRectMake(0, attentLabel.bottom+8, _window_width, 1) andColor:RGB_COLOR(@"#f0f0f0f", 1) andView:backView];
    if (isAnchor && [toUid isEqual:liveUid]) {
        
    }else{
        NSArray *array;
        if (isAnchor) {
            if ([[common letter_switch] isEqual:@"1"]) {
                array = @[@"关注",@"私信"];
            }else{
                array = @[@"关注"];

            }
        }else{
            if ([toUid isEqual:[Config getOwnID]]) {
                array = @[@"主页"];
            }else{
                if ([[common letter_switch] isEqual:@"1"]) {
                    array = @[@"关注",@"私信",@"主页"];
                }else{
                    array = @[@"关注",@"主页"];
                }
            }
        }
        for (int i = 0; i < array.count; i ++) {
            UIButton *button = [UIButton buttonWithType:0];
            button.frame = CGRectMake(i * _window_width/array.count, attentLabel.bottom+8, _window_width/array.count, 50);
            [button setImage:[UIImage imageNamed:[NSString stringWithFormat:@"bullet-%@",array[i]]] forState:0];
            [button setTitle:[NSString stringWithFormat:@" %@",YZMsg(array[i])] forState:0];
            button.titleLabel.font = [UIFont systemFontOfSize:12];
            if ([array[i] isEqual:@"关注"]) {
                attionButton = button;
                [button setTitleColor:Orange_Cor forState:0];
            }else{
                [button setTitleColor:wordColor forState:0];
                if ([array[i] isEqual:@"主页"]) {
                    homeButton = button;
                }else{
                    msgButton = button;
                }
            }
            [button addTarget:self action:@selector(bottomButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            [backView addSubview:button];
        }
    }
    
    [UIView animateWithDuration:0.2 animations:^{
        moveView.y = _window_height - viewHeight;
    }];
    
}
#pragma mark ============请求数据=============

- (void)requestData{
    guanliArrays = [NSArray array];
    NSDictionary *getPop = @{
                             @"uid":[Config getOwnID],
                             @"touid":toUid,
                             @"liveuid":liveUid
                             };
    [YBNetworking postWithUrl:@"Live.getPop" Dic:getPop Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *singleUserDic = [info firstObject];
            // 禁用选择
            _banOptionArray = [NSArray arrayWithArray:[singleUserDic valueForKey:@"banlong"]];
            //头像
            avatar_str = minstr([singleUserDic valueForKey:@"avatar"]);
            [iconImgView sd_setImageWithURL:[NSURL URLWithString:avatar_str] placeholderImage:[UIImage imageNamed:@"bg1"]];
            //ID
            idLabel.text = [NSString stringWithFormat:@"ID:%@",[singleUserDic valueForKey:@"id"]];
            signLabel.text = minstr([singleUserDic valueForKey:@"signature"]);
            //姓名
            userName = minstr([singleUserDic valueForKey:@"user_nickname"]);
            nameLabel.text = userName;
            CGFloat nameW = [PublicObj sizeWithString:userName andFont:[UIFont boldSystemFontOfSize:15]].width;
            nameLabel.x = _window_width/2-nameW/2-9;
            nameLabel.width = nameW;
            //性别
            NSString *sex = [NSString stringWithFormat:@"%@",[singleUserDic valueForKey:@"sex"]];
            if ([sex isEqual:@"1"]) {
                sexImgView.image = [UIImage imageNamed:@"bullet-男"];
            } else {
                sexImgView.image = [UIImage imageNamed:@"bullet-女"];//性别
            }
            sexImgView.x = nameLabel.right+3;
            //位置
            if ([[singleUserDic valueForKey:@"city"] isEqual:[NSNull null]] || [[singleUserDic valueForKey:@"city"] isEqual:@"null"] || [[singleUserDic valueForKey:@"city"] isEqual:@"(null)"] || [singleUserDic valueForKey:@"city"] == NULL || [singleUserDic valueForKey:@"city"] == nil) {
                cityLabel.text = YZMsg(@"好像在火星");
            }
            else{
                cityLabel.text = [NSString stringWithFormat:@"%@",[singleUserDic valueForKey:@"city"]];//地址
            }
            /*
            //用户等级
            NSString *uLevel = minstr([singleUserDic valueForKey:@"level"]);
            NSDictionary *levelDic = [common getUserLevelMessage:uLevel];
            userLevel.levelLabel.text = uLevel;
            [userLevel.backImgView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"bg"])]];
            //主播等级
            NSString *anLevel = minstr([singleUserDic valueForKey:@"level_anchor"]);
            NSDictionary *levelDic1 = [common getAnchorLevelMessage:anLevel];
            anchorLevel.levelLabel.text = anLevel;
            [anchorLevel.backImgView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic1 valueForKey:@"bg"])]];
            
            
            //印象
            NSArray * yinxiangLabel = [singleUserDic valueForKey:@"label"];
            CGFloat speace;
            if (!isAnchor) {
                speace = (_window_width - (yinxiangLabel.count + 1)*75)/(yinxiangLabel.count + 2);
            }else{
                speace = (_window_width - yinxiangLabel.count*75)/(yinxiangLabel.count + 1);
            }
            for (int i = 0; i < yinxiangLabel.count; i ++) {
                UIView *view = [[UIView alloc]initWithFrame:CGRectMake(speace + i *(75+speace), 18, 75, 26)];
                view.layer.cornerRadius = 13;
                view.layer.masksToBounds = YES;
                view.clipsToBounds = YES;
                [impressView addSubview:view];
                UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, 75, 26)];
                label.font = [UIFont systemFontOfSize:11];
                label.textColor = [UIColor whiteColor];
                label.text = minstr([yinxiangLabel[i] valueForKey:@"name"]);
                label.textAlignment = NSTextAlignmentCenter;
                [view addSubview:label];
                [PublicObj addGradientWithFromColor:RGB_COLOR(minstr([yinxiangLabel[i] valueForKey:@"colour"]), 1) andEndColor:RGB_COLOR(minstr([yinxiangLabel[i] valueForKey:@"colour2"]), 1) andView:label];
                
                if (!isAnchor && [toUid isEqualToString:liveUid] && i == yinxiangLabel.count - 1) {
                    addImpressBtn.x = view.right+speace;
                }
            }
            */
            if ([minstr([singleUserDic valueForKey:@"consumption"]) intValue] > 10000) {
                NSString *attString = [NSString stringWithFormat:@"%.1f%@\n%@",[minstr([singleUserDic valueForKey:@"consumption"]) intValue]/10000.0,YZMsg(@"万"),[NSString stringWithFormat:@"%@%@",YZMsg(@"送出"),[common name_coin]]];
                [payLabel setAttributedText:[self fuwenben:attString andChangeStr:[NSString stringWithFormat:@"%@%@",YZMsg(@"送出"),[common name_coin]]]];
            }else{
                NSString *str = [NSString stringWithFormat:@"%@\n%@",minstr([singleUserDic valueForKey:@"consumption"]),[NSString stringWithFormat:@"%@%@",YZMsg(@"送出"),[common name_coin]]];
                
                [payLabel setAttributedText:[self fuwenben:str andChangeStr:[NSString stringWithFormat:@"%@%@",YZMsg(@"送出"),[common name_coin]]]];
            }
            payLabel.textAlignment = NSTextAlignmentCenter;
            if ([minstr([singleUserDic valueForKey:@"fans"]) intValue] > 10000) {
                NSString *attString = [NSString stringWithFormat:@"%.1f%@\n%@",[minstr([singleUserDic valueForKey:@"fans"]) intValue]/10000.0,YZMsg(@"万"),YZMsg(@"粉丝")];
                [fansLabel setAttributedText:[self fuwenben:attString andChangeStr:YZMsg(@"粉丝")]];
            }else{
                NSString *attString = [NSString stringWithFormat:@"%@\n%@",minstr([singleUserDic valueForKey:@"fans"]) ,YZMsg(@"粉丝")];
                [fansLabel setAttributedText:[self fuwenben:attString andChangeStr:YZMsg(@"粉丝")]];
            }
            fansLabel.textAlignment = NSTextAlignmentCenter;
            if ([minstr([singleUserDic valueForKey:@"votestotal"]) intValue] > 10000) {
                NSString *attString = [NSString stringWithFormat:@"%.1f%@\n%@",[minstr([singleUserDic valueForKey:@"votestotal"]) intValue]/10000.0,YZMsg(@"万"),[NSString stringWithFormat:@"%@%@",YZMsg(@"收入"),[common name_votes]]];
                [incomeLabel setAttributedText:[self fuwenben:attString andChangeStr:[NSString stringWithFormat:@"%@%@",YZMsg(@"收入"),[common name_votes]]]];
            }else{
                NSString *attString = [NSString stringWithFormat:@"%@\n%@",minstr([singleUserDic valueForKey:@"votestotal"]) ,[NSString stringWithFormat:@"%@%@",YZMsg(@"收入"),[common name_votes]]];
                [incomeLabel setAttributedText:[self fuwenben:attString andChangeStr:[NSString stringWithFormat:@"%@%@",YZMsg(@"收入"),[common name_votes]]]];
            }
            incomeLabel.textAlignment = NSTextAlignmentCenter;
            if ([minstr([singleUserDic valueForKey:@"follows"]) intValue] > 10000) {
                NSString *attString = [NSString stringWithFormat:@"%.1f%@\n%@",[minstr([singleUserDic valueForKey:@"follows"]) intValue]/10000.0,YZMsg(@"万"),YZMsg(@"关注")];
                [attentLabel setAttributedText:[self fuwenben:attString andChangeStr:YZMsg(@"关注")]];
            }else{
                NSString *attString = [NSString stringWithFormat:@"%@\n%@",minstr([singleUserDic valueForKey:@"follows"]),YZMsg(@"关注")];
                [attentLabel setAttributedText:[self fuwenben:attString andChangeStr:YZMsg(@"关注")]];
            }
            attentLabel.textAlignment = NSTextAlignmentCenter;
            
            NSString *isattention = [NSString stringWithFormat:@"%@",[singleUserDic valueForKey:@"isattention"]];
            //判断关注
            if ([isattention isEqual:@"0"]) {
                [attionButton setTitle:YZMsg(@"关注") forState:UIControlStateNormal];
                [attionButton setImage:[UIImage imageNamed:@"bullet-关注"] forState:0];
                [attionButton setTitleColor:Pink_Cor forState:UIControlStateNormal];
                //_forceBtn.enabled = YES;
            }
            else{
                [attionButton setTitle:YZMsg(@"已关注") forState:UIControlStateNormal];
                [attionButton setImage:[UIImage imageNamed:@"bullet-已关注"] forState:0];
                [attionButton setTitleColor:RGB_COLOR(@"#323232", 1) forState:UIControlStateNormal];
                //_forceBtn.enabled = NO;
            }
            //判断管理 操作显示，0表示自己，30表示普通用户，40表示管理员，501表示主播设置管理员，502表示主播取消管理员，60表示超管管理主播
            NSString *action = [NSString stringWithFormat:@"%@",[singleUserDic valueForKey:@"action"]];
            
            if ([action isEqual:@"0"]) {
                leftButton.hidden = YES;
                guanliButton.hidden = YES;
                //自己
            }else if ([action isEqual:@"30"]){
                leftButton.hidden = NO;
                guanliButton.hidden = YES;
                //普通用户
            }else{
                leftButton.hidden = YES;
                guanliButton.hidden = NO;
                if ([action isEqual:@"40"]){
                    guanliArrays = @[YZMsg(@"踢人"),YZMsg(@"本场禁言"),YZMsg(@"永久禁言")];
                    //管理员
                }else if ([action isEqual:@"501"]){
                    guanliArrays = @[YZMsg(@"踢人"),YZMsg(@"本场禁言"),YZMsg(@"永久禁言"),YZMsg(@"设为管理"),YZMsg(@"管理员列表")];
                    //主播设置管理员
                }else if ([action isEqual:@"502"]){
                    guanliArrays = @[YZMsg(@"踢人"),YZMsg(@"本场禁言"),YZMsg(@"永久禁言"),YZMsg(@"取消管理"),YZMsg(@"管理员列表")];
                    //主播取消管理员
                }else if ([action isEqual:@"60"]){
                    //超管管理主播
                    guanliArrays = @[YZMsg(@"关闭直播"),YZMsg(@"封禁直播间"),YZMsg(@"禁用账户")];
                }
            }
            
        }
    } Fail:^(id fail) {
        
    }];
    

}
- (NSMutableAttributedString *)fuwenben:(NSString *)str andChangeStr:(NSString *)nameStr{
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc]initWithString:str];
    [attStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:10] range:NSMakeRange(str.length-nameStr.length, nameStr.length)];
    [attStr addAttribute:NSStrokeColorAttributeName value:RGB_COLOR(@"#646464", 1) range:NSMakeRange(str.length-nameStr.length, nameStr.length)];
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 8;
    [attStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, str.length)];

    return [attStr copy];
}

#pragma mark - 封禁选择

-(void)showBanOption {
    [self destroySubs];
    _banAlert = [YBLiveRoomAlertView showBanOption:_banOptionArray];
    YBWeakSelf;
    _banAlert.banEvent = ^(NSString *selId) {
        [weakSelf selBanId:selId];
    };
}
-(void)selBanId:(NSString *)selId {
    _banSelId = selId;
    [self superCloseRoom:@"1" andBanSelId:selId];
}
-(void)destroySubs; {
    if (_banAlert) {
        [_banAlert removeFromSuperview];
        _banAlert = nil;
    }
    
}
#pragma mark ============举报设置按钮=============
- (void)leftButtonClick{
    if (_delegate && [_delegate respondsToSelector:@selector(doReportAnchor:)]) {
        [self.delegate doReportAnchor:toUid];
    }
//    [self closebtnClick];
}
- (void)gunanliButtonClick{
    [self doGuanLi];
}
//这是弹窗的事件
-(void)doGuanLi{
    
    if (_myActionSheet) {
        [_myActionSheet removeFromSuperview];
        _myActionSheet = nil;
    }
    CGSize winsize = [UIScreen mainScreen].bounds.size;
    CGFloat x;
    x = 0;
    _myActionSheet = [[CSActionSheet alloc] initWithFrame:CGRectMake(x,0, winsize.width, winsize.height) titles:guanliArrays cancal:YZMsg(@"取消") normal_color:RGB_COLOR(@"#323232", 1) highlighted_color:RGB_COLOR(@"#323232", 1) tips:nil tipsColor:[UIColor whiteColor] cellBgColor:[UIColor whiteColor] cellLineColor:RGB_COLOR(@"#969696", 1)];
    
    [self.superview addSubview:_myActionSheet];
    [_myActionSheet setCancalLabelColor:RGB_COLOR(@"#969696", 1) highlightedColor:RGB_COLOR(@"#969696", 1)];
    [_myActionSheet showView:^(int index, id sender) {
        NSString *title = guanliArrays[index-1];
        if ([title isEqual:YZMsg(@"踢人")]) {
            [self kickuser];
        }
        if ([title isEqual:YZMsg(@"本场禁言")]) {
            [self jinyan:@"1"];
        }
        if ([title isEqual:YZMsg(@"永久禁言")]) {
            [self jinyan:@"0"];
        }
        
        if ([title isEqual:YZMsg(@"关闭直播")]) {
            [self superStopRoom];
        }
        if ([title isEqual:YZMsg(@"封禁直播间")]) {
            
            //[self superCloseRoom:@"1"];
            [self showBanOption];
        }
        if ([title isEqual:YZMsg(@"设为管理")]) {
            [self setAdmin];;
        }
        if ([title isEqual:YZMsg(@"取消管理")]) {
            [self setAdmin];;
        }
        if ([title isEqual:YZMsg(@"管理员列表")]) {
            [self adminLIst];
        }
        if ([title isEqual:YZMsg(@"禁用账户")]) {
            [self superCloseRoom:@"2" andBanSelId:@""];
        }
        
        CSActionSheet *view1 = (CSActionSheet*)sender;
        [view1 hideView];
    }close:^(id sender) {
           CSActionSheet *view1 = (CSActionSheet*)sender;
           if (view1) {
               [view1 removeFromSuperview];
               view1 = nil;
           }
    }];
    
}
//超管管理主播
-(void)superStopRoom{
    //关闭当前直播
    NSDictionary *setadmin = @{
                               @"liveuid":liveUid,
                               @"type":@"0",
                               };
    [YBNetworking postWithUrl:@"Live.superStopRoom" Dic:setadmin Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            [MBProgressHUD showError:[[info firstObject] valueForKey:@"msg"]];
            [self.delegate superAdmin:@"0" andBanMsg:minstr([infoDic valueForKey:@"banmsg"])];
        }
        [self closebtnClick];
    } Fail:^(id fail) {
        
    }];
}
-(void)superCloseRoom:(NSString *)type andBanSelId:(NSString *)banSelId{
    //关闭当前直播
    NSDictionary *setadmin = @{
                               @"liveuid":liveUid,
                               @"type":type,
                               @"banlong":banSelId,
                               };
    [YBNetworking postWithUrl:@"Live.superStopRoom" Dic:setadmin Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [MBProgressHUD showError:msg];
            NSDictionary *infoDic = [info firstObject];
            [self.delegate superAdmin:@"1"andBanMsg:minstr([infoDic valueForKey:@"banmsg"])];
            
        }
        [self closebtnClick];
    } Fail:^(id fail) {
        
    }];
}
-(void)adminLIst{
    [self.delegate adminList];
    [self closebtnClick];

}
-(void)setAdmin{
    NSDictionary *setadmin = @{
                               @"liveuid":liveUid,
                               @"touid":toUid,
                               };
    [YBNetworking postWithUrl:@"Live.setAdmin" Dic:setadmin Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSString *isadmin = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"isadmin"]];
            [self.delegate setAdminSuccess:isadmin andName:userName andID:toUid];
        }
        [self closebtnClick];
    } Fail:^(id fail) {
        
    }];
    
}
//踢人
-(void)kickuser{
    
    NSDictionary *kickuser = @{
                               @"liveuid":liveUid,
                               @"touid":toUid,
                               };
    [YBNetworking postWithUrl:@"Live.kicking" Dic:kickuser Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            if ([self.delegate respondsToSelector:@selector(socketkickuser:andID:)]) {
                [self.delegate socketkickuser:userName andID:toUid];
            }
            [self closebtnClick];

        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];

}
//禁言
-(void)jinyan:(NSString *)type{
    //  User.setShutUp
    
    NSDictionary *shutup = @{
                             @"liveuid":liveUid,
                             @"touid":toUid,
                             @"type":type,
                             @"stream":[type intValue] == 0 ? @"0":_stream
                             };
    [YBNetworking postWithUrl:@"Live.setShutUp" Dic:shutup Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            if ([self.delegate respondsToSelector:@selector(socketkickuser:andID:)]) {
                [self.delegate socketShutUp:userName andID:toUid andType:type];
            }
            [self closebtnClick];

        }
        else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
    
}

#pragma mark ============添加印象=============
- (void)addImpressBtnClick{
//    [self.delegate setLabel:toUid];
    [self closebtnClick];
}
#pragma mark ============底部按钮点击=============
- (void)bottomButtonClick:(UIButton *)sender{
    if (sender == attionButton) {
        //关注
        [self forceBtnClick];
    }
    if (sender == msgButton) {
        // 私信
        [self messageBtnClick];
        [self closebtnClick];
    }
    if (sender == homeButton) {
        //主页
        [self homeBtnClick];
        [self closebtnClick];

    }
}
//设置取消关注
-(void)forceBtnClick{
    
    // User.setAttentionAnchor
    NSDictionary *attent = @{
                             @"touid":toUid
                             };
    [YBNetworking postWithUrl:@"User.setAttent" Dic:attent Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *subdic = [info firstObject];
            if ([toUid isEqualToString:liveUid]) {
                [[NSNotificationCenter defaultCenter] postNotificationName:@"reloadLiveplayAttion" object:subdic];
            }
            NSString *isattention = [NSString stringWithFormat:@"%@",[subdic valueForKey:@"isattent"]];
            //判断关注
            if ([isattention isEqual:@"0"]) {
                [attionButton setTitle:[NSString stringWithFormat:@"%@",YZMsg(@"关注")] forState:UIControlStateNormal];
                [attionButton setImage:[UIImage imageNamed:@"bullet-关注"] forState:0];
                [attionButton setTitleColor:Pink_Cor forState:UIControlStateNormal];
            }
            else{
                [attionButton setTitle:[NSString stringWithFormat:@"%@",YZMsg(@"已关注")] forState:UIControlStateNormal];
                [attionButton setImage:[UIImage imageNamed:@"bullet-已关注"] forState:0];
                [attionButton setTitleColor:RGB_COLOR(@"#323232", 1) forState:UIControlStateNormal];
            }
        }
    } Fail:^(id fail) {
        
    }];
    
}
-(void)homeBtnClick{
    [self.delegate pushZhuYe:toUid];
}
-(void)messageBtnClick{
    if ([attionButton.titleLabel.text isEqual:[NSString stringWithFormat:@"%@",YZMsg(@"关注")]]) {
        [self.delegate siXin:avatar_str andName:userName andID:toUid andIsatt:@"0"];
    }else{
        [self.delegate siXin:avatar_str andName:userName andID:toUid andIsatt:@"1"];
    }
    
}

@end
