//
//  userLevelView.m
//  yunbaolive
//
//  Created by IOS1 on 2019/6/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "userLevelView.h"

@implementation userLevelView

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self) {
        [self creatUI];
    }
    return self;

}
- (void)creatUI{
    _backImgView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, self.width, self.height)];
    [self addSubview:_backImgView];

    _levelImgView = [[UIImageView alloc]initWithFrame:CGRectMake(5, 9, 28, 28)];
    [self addSubview:_levelImgView];
    _nameL = [[UILabel alloc]initWithFrame:CGRectMake(_levelImgView.right+7, 8, self.width-40, 12)];
    _nameL.font = [UIFont systemFontOfSize:10];
    _nameL.textColor = [UIColor whiteColor];
    [self addSubview:_nameL];
    _levelLabel = [[UILabel alloc]initWithFrame:CGRectMake(_nameL.left, _nameL.bottom+7, self.width-40, 12)];
    _levelLabel.font = [UIFont boldSystemFontOfSize:13];
    _levelLabel.textColor = [UIColor whiteColor];
    [self addSubview:_levelLabel];
}
@end
