//
//  guardListCell.m
//  yunbaolive
//
//  Created by <PERSON> on 2018/11/12.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "guardListCell.h"

@implementation guardListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
- (void)setModel:(guardListModel *)model{
    _model = model;
    if ([_model.type isEqual:@"1"]) {
        _typeImgView.image = [UIImage imageNamed:getImagename(@"chat_shou_month")];
    }else{
        _typeImgView.image = [UIImage imageNamed:getImagename(@"chat_shou_year")];
    }
    [_iconImgView sd_setImageWithURL:[NSURL URLWithString:_model.avatar_thumb] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
    _nameL.text = _model.user_nickname;
    _votesL.text = _model.contribute;
    _yingpiaoL.text = [common name_votes];
    if ([_model.sex isEqual:@"1"]) {
        _sexImgView.image = [UIImage imageNamed:@"bullet-男"];
    }else{
        _sexImgView.image = [UIImage imageNamed:@"bullet-女"];
    }
    /*
    NSDictionary *levelDic = [common getUserLevelMessage:_model.level];
    [_levelImgView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb"])]];
    */
}
@end
