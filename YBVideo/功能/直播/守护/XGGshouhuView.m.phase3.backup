//
//  shouhuView.m
//  yunbaolive
//
//  Created by Boom on 2018/8/9.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "shouhuView.h"
#import "grardButton.h"
#import "guardAlertView.h"
@implementation shouhuView{
    UIView *whiteView;
    NSMutableArray *btnArray;
    int selectIndex;
    UILabel *coinLabel;
    UIButton *buyBtn;
    NSDictionary *infoDic;
    NSMutableArray *privilegeArray;
    NSMutableArray *privilegeTitleArray;
    NSMutableArray *privilegeDesArray;
    guardAlertView *gAlert;
    UIImageView *headerImgView;
    UIImageView *moveView;
}

-(instancetype)init{
    self = [super init];
    if (self) {
        self.frame = CGRectMake(0, 0, _window_width, _window_height);
        self.backgroundColor = [UIColor clearColor];
        privilegeArray = [NSMutableArray array];
        privilegeTitleArray = [NSMutableArray array];
        privilegeDesArray = [NSMutableArray array];

        btnArray = [NSMutableArray array];
        selectIndex = 0;
        [self requestData];
    }
    return self;
}
- (void)creatUI:(NSDictionary *)subDic{
    UIButton *hidebtn = [UIButton buttonWithType:0];
    hidebtn.frame = CGRectMake(0, 0, _window_width, _window_height-360-ShowDiff);
    [hidebtn addTarget:self action:@selector(hideBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:hidebtn];
    whiteView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, 360+ShowDiff)];
    whiteView.backgroundColor = [UIColor whiteColor];
    whiteView.layer.mask = [PublicObj setViewLeftTop:20 andRightTop:20 andView:whiteView];
    whiteView.clipsToBounds = YES;
    [self addSubview:whiteView];
    
    headerImgView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 80)];
    headerImgView.image = [UIImage imageNamed:@"guard_lowwwwwww"];
    headerImgView.contentMode = UIViewContentModeScaleToFill;
    headerImgView.clipsToBounds = YES;
    headerImgView.userInteractionEnabled = YES;
    [whiteView addSubview:headerImgView];
    
    
    
    NSArray *arr = [subDic valueForKey:@"list"];
    for (int i = 0 ; i < arr.count ; i++) {
        
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(_window_width/arr.count*i, 0, _window_width/arr.count, headerImgView.height);
        [btn setTitle:[NSString stringWithFormat:@"%@\n%@%@",minstr([arr[i] valueForKey:@"name"]),minstr([arr[i] valueForKey:@"coin"]),[common name_coin]] forState:0];
        btn.titleLabel.font = [UIFont systemFontOfSize:14];
        btn.titleLabel.textAlignment = NSTextAlignmentCenter;
        [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
        [btn setTitleColor:[[UIColor whiteColor] colorWithAlphaComponent:0.4] forState:0];
        [btn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.tag = 201889+i;
        btn.titleLabel.numberOfLines = 0;
        [btnArray addObject:btn];
        [headerImgView addSubview:btn];
        if (i==0) {
            btn.selected = YES;
            moveView = [[UIImageView alloc]initWithFrame:CGRectMake(btn.centerX-7, btn.height-7, 14, 7)];
            moveView.image = [UIImage imageNamed:@"guard_sanjiao"];
            [headerImgView addSubview:moveView];
        }else{
            btn.selected = NO;
        }

//        CGFloat widthhhh = 0;
//
//        if (i == arr.count - 1 ) {
//            widthhhh = 20;
//        }
//        grardButton *btn = [[grardButton alloc] initWithFrame:CGRectMake(20+90*i, label1.bottom, 85+widthhhh, 40)];
//        btn.tag = 201889+i;
//        btn.coinL.text = minstr([arr[i] valueForKey:@"coin"]);
//        btn.nameL.text = minstr([arr[i] valueForKey:@"name"]);
//
//        if (i==0) {
//            btn.selected = YES;
//        }else{
//            btn.selected = NO;
//        }
//        if (i == arr.count - 1){
//            [btn setBackgroundImage:[UIImage imageNamed:@"guard_select_s_2"] forState:UIControlStateSelected];
//            [btn setBackgroundImage:[UIImage imageNamed:@"guard_select_n_2"] forState:UIControlStateNormal];
//        }else{
//            [btn setBackgroundImage:[UIImage imageNamed:@"guard_select_s"] forState:UIControlStateSelected];
//            [btn setBackgroundImage:[UIImage imageNamed:@"guard_select_n"] forState:UIControlStateNormal];
//        }
//        [btn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
//        [btnArray addObject:btn];
//        [whiteView addSubview:btn];
    }
    
//    UIImageView *lineImgView =[[UIImageView alloc]initWithFrame:CGRectMake(_window_width*0.05, whiteView.height*0.25, _window_width*0.9, 1)];
//    lineImgView.image = [UIImage imageNamed:@"jimo_shouhu_xuxian"];
//    [whiteView addSubview:lineImgView];
//
//    UILabel *label2 = [[UILabel alloc]initWithFrame:CGRectMake(10, lineImgView.bottom, _window_width*0.8, whiteView.height*0.12)];
//    label2.text = YZMsg(@"守护特权");
//    label2.font = [UIFont systemFontOfSize:15];
//    label2.textColor = RGB_COLOR(@"#626364", 1);
//
//    [whiteView addSubview:label2];

    NSArray *privilege = [subDic valueForKey:@"privilege"];
    NSArray *firstArr = [[[subDic valueForKey:@"list"] firstObject] valueForKey:@"privilege"];

    for (int i = 0; i < privilege.count; i++) {
        UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 80+i*57.5, _window_width, 57.5)];
        [whiteView addSubview:view];
        NSDictionary *itemDic = privilege[i];
        UIImageView *imgView = [[UIImageView alloc]init];
        imgView.contentMode = UIViewContentModeScaleAspectFit;
        [view addSubview:imgView];
        [imgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(view).offset(10);
            make.centerY.equalTo(view);
            make.width.height.equalTo(view.mas_height).multipliedBy(0.55);
        }];
        UILabel *titleLabel = [[UILabel alloc]init];
        titleLabel.font = [UIFont systemFontOfSize:13];
        titleLabel.text = minstr([itemDic valueForKey:@"title"]);
        [view addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(imgView.mas_right).offset(8);
            make.top.equalTo(imgView);
        }];
        
        UILabel *contentLabel = [[UILabel alloc]init];
        contentLabel.font = SYS_Font(12);
        contentLabel.textColor = [UIColor grayColor];
        contentLabel.text = minstr([itemDic valueForKey:@"des"]);
        [view addSubview:contentLabel];
        [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(imgView.mas_right).offset(8);
            make.bottom.equalTo(imgView);
        }];

        if (i < firstArr.count) {
            if (i == 0) {
                [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([itemDic valueForKey:@"thumb_g"])]];
            }else{
                [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([itemDic valueForKey:@"thumb_c"])]];
            }
            titleLabel.textColor = RGB_COLOR(@"#646464", 1);
            contentLabel.textColor = RGB_COLOR(@"#646464", 1);
        }else{
            [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([itemDic valueForKey:@"thumb_g"])]];
            titleLabel.textColor = RGB_COLOR(@"#c8c8c8", 1);
            contentLabel.textColor = RGB_COLOR(@"#c8c8c8", 1);
        }
        [privilegeArray addObject:imgView];
        [privilegeTitleArray addObject:titleLabel];
        [privilegeDesArray addObject:contentLabel];

    }
    UIView *lineView = [[UIView alloc]initWithFrame:CGRectMake(0, 310, _window_width, 1)];
    lineView.backgroundColor = RGB_COLOR(@"#F0F0F0", 1);
    [whiteView addSubview:lineView];
    
    coinLabel = [[UILabel alloc]init];
    coinLabel.font = [UIFont systemFontOfSize:14];
    coinLabel.textColor = RGB_COLOR(@"#646464", 1);
    coinLabel.attributedText = [self coinLabel:minstr([subDic valueForKey:@"coin"])];
    [whiteView addSubview:coinLabel];
    [coinLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(whiteView).offset(10);
        make.bottom.equalTo(whiteView).offset(-ShowDiff);
        make.height.mas_equalTo(49);
    }];
    buyBtn = [UIButton buttonWithType:0];
    [buyBtn addTarget:self action:@selector(buyBtnClick) forControlEvents:UIControlEventTouchUpInside];
    buyBtn.layer.cornerRadius = 15;
    buyBtn.layer.masksToBounds = YES;
    [buyBtn setTitle:YZMsg(@"立即开通") forState:0];
    buyBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [whiteView addSubview:buyBtn];
    [buyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(whiteView).offset(-10);
        make.centerY.equalTo(coinLabel);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(90);
    }];
    [buyBtn layoutIfNeeded];
    [PublicObj addGradientWithFromColor:RGB_COLOR(@"#872ae7", 1) andEndColor:RGB_COLOR(@"#cb16e1", 1) andView:buyBtn direction:0];

    [self show];
}
- (NSMutableAttributedString *)coinLabel:(NSString *)coin{
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc]init];
    NSAttributedString *str1 = [[NSAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@%@：",YZMsg(@"我的"),[common name_coin]]];
    [attStr appendAttributedString:str1];
//    NSTextAttachment *xoinAttchment = [[NSTextAttachment alloc]init];
//    xoinAttchment.bounds = CGRectMake(0, -2, 13, 13);//设置frame
//    xoinAttchment.image = [UIImage imageNamed:@"logFirst_钻石"];//设置图片
//    NSAttributedString *coinString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(xoinAttchment)];
//    [attStr appendAttributedString:coinString];
    NSMutableAttributedString *str2 = [[NSMutableAttributedString alloc]initWithString:coin];
    [str2 addAttribute:NSForegroundColorAttributeName value:Pink_Cor range:NSMakeRange(0, [coin length])];//RGB_COLOR(@"#6e52f2", 1)-紫色
    [attStr appendAttributedString:str2];
    return  attStr;
}
- (void)hideBtnClick{
    [self.delegate removeShouhuView];
}
- (void)btnClick:(grardButton *)sender{
    if (sender.selected) {
        return;
    }
    sender.selected = YES;
    selectIndex = (int)sender.tag - 201889;
    for (grardButton *btn in btnArray) {
        if (sender != btn) {
            btn.selected = NO;
        }
    }
    moveView.centerX = sender.centerX;
    if (sender.tag == 201891) {
        headerImgView.image = [UIImage imageNamed:@"guard_height"];
    }else{
        headerImgView.image = [UIImage imageNamed:@"guard_lowwwwwww"];
    }
    NSArray *privilege = [infoDic valueForKey:@"privilege"];
    NSDictionary *selectDiccc = [infoDic valueForKey:@"list"][selectIndex];
    NSArray *firstArr = [selectDiccc valueForKey:@"privilege"];
    for (int i = 0; i < privilege.count; i ++) {
        NSDictionary *itemDic = privilege[i];
        UIImageView *imgView = privilegeArray[i];
        UILabel *titleLabel = privilegeTitleArray[i];
        UILabel *contentLabel = privilegeDesArray[i];
        if (i < firstArr.count) {
            if([minstr([selectDiccc valueForKey:@"type"]) isEqual:@"2"]){
                [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([itemDic valueForKey:@"thumb_c"])]];
            }else{
                if (i == 0) {
                    [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([itemDic valueForKey:@"thumb_g"])]];
                }else{
                    [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([itemDic valueForKey:@"thumb_c"])]];
                }
            }
            titleLabel.textColor = RGB_COLOR(@"#646464", 1);
            contentLabel.textColor = RGB_COLOR(@"#646464", 1);
        }else{
            [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([itemDic valueForKey:@"thumb_g"])]];
            titleLabel.textColor = RGB_COLOR(@"#c8c8c8", 1);
            contentLabel.textColor = RGB_COLOR(@"#c8c8c8", 1);
        }

    }


}
- (void)show{
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.frame = CGRectMake(0, _window_height-360-ShowDiff, _window_width, 360+ShowDiff);
    }];
}
- (void)hideAlert{
    [gAlert removeFromSuperview];
    gAlert = nil;
}
- (void)buyBtnClick{
//    if ([_guardType isEqual:@"0"]) {
//
////        [self goBuy];
//    }else{
        if ([_guardType isEqual:@"1"] && selectIndex == 2) {
            gAlert = [[guardAlertView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andType:1 andMsg:YZMsg(@"您当前为主播的月守护，开通年守护将覆盖您的月守护剩余时长，是否开通？")];
            __weak shouhuView *wSelf = self;
            gAlert.block = ^(BOOL isSure) {
                if (isSure) {
                    [wSelf goBuy];
                }else{
                    [wSelf hideAlert];
                }
            };
            [self addSubview:gAlert];

        }else if ([_guardType isEqual:@"2"] && selectIndex != 2) {
            gAlert = [[guardAlertView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andType:0 andMsg:YZMsg(@"您当前为主播的年守护\n无法开通7天/月守护")];
            __weak shouhuView *wSelf = self;
            gAlert.block = ^(BOOL isSure) {
                if (isSure) {
                    [wSelf goBuy];
                }else{
                    [wSelf hideAlert];
                }
            };
            [self addSubview:gAlert];

        }else{
            NSString *msg;
            switch (selectIndex) {
                case 0:
                    msg = [NSString stringWithFormat:@"%@%@%@，%@",YZMsg(@"您将花费"),minstr([[infoDic valueForKey:@"list"][selectIndex] valueForKey:@"coin"]),[common name_coin],YZMsg(@"为主播开通7天守护")];
                    break;
                case 1:
                    msg = [NSString stringWithFormat:@"%@%@%@，%@",YZMsg(@"您将花费"),minstr([[infoDic valueForKey:@"list"][selectIndex] valueForKey:@"coin"]),[common name_coin],YZMsg(@"为主播开通月守护")];
                    break;
                case 2:
                    msg = [NSString stringWithFormat:@"%@%@%@，%@",YZMsg(@"您将花费"),minstr([[infoDic valueForKey:@"list"][selectIndex] valueForKey:@"coin"]),[common name_coin],YZMsg(@"为主播开通年守护")];
                    break;
                    
                default:
                    break;
            }
            gAlert = [[guardAlertView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andType:1 andMsg:msg];
            __weak shouhuView *wSelf = self;
            gAlert.block = ^(BOOL isSure) {
                if (isSure) {
                    [wSelf goBuy];
                }else{
                    [wSelf hideAlert];
                }
            };
            [self addSubview:gAlert];

        }

//    }

}
- (void)goBuy{
    NSDictionary *parameterDic = @{
                                   @"liveuid":_liveUid,
                                   @"guardid":minstr([[infoDic valueForKey:@"list"][selectIndex] valueForKey:@"id"]),
                                   @"stream":_stream
                                   };
    
    [YBNetworking postWithUrl:@"Guard.BuyGuard" Dic:parameterDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infos = [info firstObject];
            [self.delegate buyShouhuSuccess:infos];
            [self hideBtnClick];
            
        }
        [MBProgressHUD showError:msg];
    } Fail:^(id fail) {
        [MBProgressHUD showError:YZMsg(@"网络错误")];
    }];

}
- (void)requestData{
    
    [YBNetworking postWithUrl:@"Guard.GetList" Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            infoDic = [info firstObject];
            [self creatUI:infoDic];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
#pragma mark ================ alertview ===============
//- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
//    if (buttonIndex == 1) {
//        [self.delegate pushCoinV];
//    }
//}
@end
