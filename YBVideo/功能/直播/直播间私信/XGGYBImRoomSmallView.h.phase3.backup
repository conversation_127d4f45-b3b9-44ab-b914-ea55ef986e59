//
//  YBImRoomSmallView.h
//  iphoneLive
//
//  Created by YB007 on 2022/6/17.
//  Copyright © 2022 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,ImSmallType) {
    ImSmall_C2C,
    ImSmall_List,
};
typedef NS_ENUM(NSInteger,ImCtrType) {
    ImCtr_Dismiss,
};
typedef void (^ImCtrBlock)(ImCtrType type,NSDictionary *ctrDic);

@interface YBImRoomSmallView : UIView

+(instancetype)showImListWithLiveUid:(NSString *)liveuid;

@property(nonatomic,assign)ImSmallType imSmallType;
@property(nonatomic,copy)ImCtrBlock imCtrEvent;

#pragma mark - 列表
//-(void)pullListData;

#pragma mark - C2C
-(void)goChatWithC2CWithConv:(TConversationCellData *)conversation;


@end


