//
//  YBGoodsBriefView.m
//  YBVideo
//
//  Created by YB007 on 2020/8/31.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBGoodsBriefView.h"
#import "YBGoodsInfoVC.h"
#import "OutsideGoodsDetailVC.h"
#import "CommodityDetailVC.h"
@interface YBGoodsBriefView()

@property(nonatomic,strong)UIView *bgView;
@property(nonatomic,strong)UIImageView *thumbIV;
@property(nonatomic,strong)UILabel *titleL;
@property(nonatomic,strong)UILabel *priceL;
@property(nonatomic,strong)UILabel *oldPriceL;
@property(nonatomic,strong)UILabel *oldPLL;
@property(nonatomic,strong)UIButton *infoBtn;

@end

@implementation YBGoodsBriefView


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        
        [self addSubview:self.bgView];
        
    }
    return self;
}

- (void)setGoodsDic:(NSDictionary *)goodsDic {
    _goodsDic = goodsDic;

    [_thumbIV sd_setImageWithURL:[NSURL URLWithString:minstr([_goodsDic valueForKey:@"goods_thumb"])]];
    _titleL.text = minstr([_goodsDic valueForKey:@"goods_name"]);
    _priceL.text = minstr([_goodsDic valueForKey:@"goods_price"]);
    _oldPriceL.hidden = _oldPLL.hidden = YES;
//    NSString *oldPrice = minstr([_goodsDic valueForKey:@"goods_old_price"]);
//    if (![PublicObj checkNull:oldPrice]) {
    if ([minstr([_goodsDic valueForKey:@"goods_type"]) isEqual:@"1"]) {
        _oldPriceL.hidden = _oldPLL.hidden = NO;
        _oldPriceL.text = minstr([_goodsDic valueForKey:@"goods_old_price"]);
    }
    
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, self.width, self.height)];
        _bgView.backgroundColor = UIColor.whiteColor;
        _bgView.layer.cornerRadius = 5;
        _bgView.layer.masksToBounds = YES;
        
        _thumbIV = [[UIImageView alloc]init];
        _thumbIV.layer.cornerRadius = 2;
        _thumbIV.layer.masksToBounds = YES;
        _thumbIV.contentMode = UIViewContentModeScaleAspectFill;
        _thumbIV.clipsToBounds = YES;
        [_bgView addSubview:_thumbIV];
        [_thumbIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_bgView.mas_left).offset(8);
            make.height.mas_equalTo(_bgView.mas_height).offset(-20);
            make.centerY.equalTo(_bgView);
            make.width.equalTo(_thumbIV.mas_height);
        }];
        
        _titleL = [[UILabel alloc]init];
        _titleL.font = SYS_Font(12);
        _titleL.textColor = RGB_COLOR(@"#323232", 1);
        _titleL.numberOfLines = 2;
        [_bgView addSubview:_titleL];
        [_titleL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_thumbIV.mas_right).offset(8);
            make.top.equalTo(_thumbIV.mas_top).offset(2);
            make.right.equalTo(_bgView.mas_right).offset(-8);
        }];
        
        _priceL = [[UILabel alloc]init];
        _priceL.font = [UIFont boldSystemFontOfSize:13];
        _priceL.textColor = Pink_Cor;
        [_bgView addSubview:_priceL];
        [_priceL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_titleL.mas_left);
            make.bottom.equalTo(_thumbIV.mas_bottom).offset(-2);
        }];
        
        _oldPriceL = [[UILabel alloc]init];
        _oldPriceL.font = SYS_Font(11);
        _oldPriceL.textColor = RGB_COLOR(@"#969696", 1);
        [_bgView addSubview:_oldPriceL];
        [_oldPriceL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_priceL.mas_right).offset(10);
            make.bottom.equalTo(_priceL.mas_bottom);
        }];
        
        _oldPLL = [[UILabel alloc]init];
        _oldPLL.backgroundColor = _oldPriceL.textColor;
        [_bgView addSubview:_oldPLL];
        [_oldPLL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(_oldPriceL.mas_width).offset(5);
            make.centerY.centerX.equalTo(_oldPriceL);
            make.height.equalTo(@1);
        }];
        
        _infoBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_infoBtn setTitle:YZMsg(@"去看看") forState:0];
        _infoBtn.titleLabel.font = SYS_Font(12);
        [_infoBtn setTitleColor:Pink_Cor forState:0];
        _infoBtn.layer.borderWidth = 1;
        _infoBtn.layer.cornerRadius = 12;
        _infoBtn.layer.borderColor = Pink_Cor.CGColor;
        _infoBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 10);
        [_infoBtn addTarget:self action:@selector(clickInfoBtn) forControlEvents:UIControlEventTouchUpInside];
        [_bgView addSubview:_infoBtn];
        [_infoBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(_priceL);
            make.height.mas_equalTo(24);
            make.right.equalTo(_bgView.mas_right).offset(-10);
        }];
    }
    return _bgView;
}

-(void)clickInfoBtn {
    NSLog(@"goods--------：%@",_goodsDic);
    [PublicObj checkGoodsExistenceWithID:minstr([_goodsDic valueForKey:@"goodsid"]) Existence:^(int code, NSString *msg) {
        if (code ==0) {
            if ([minstr([_goodsDic valueForKey:@"goods_type"]) isEqual:@"1"]) {
                OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
                detail.goodsID = minstr([_goodsDic valueForKey:@"goodsid"]);
                detail.liveUid= _touserID;
                [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];

            }else{
                CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                detail.goodsID = minstr([_goodsDic valueForKey:@"goodsid"]);
                detail.backTags = @"live";
                detail.liveUid= _touserID;
                [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];

            }

        }else{
            [MBProgressHUD showError:msg];

        }
    }];

    
//    YBGoodsInfoVC *infoVC = [[YBGoodsInfoVC alloc]init];
//    infoVC.infoType = InfoEnterType_OnSale;
//    infoVC.touserID = _touserID;
//    infoVC.goodsID = minstr([_goodsDic valueForKey:@"goodsid"]);
//    [[XGGAppDelegate sharedAppDelegate]pushViewController:infoVC animated:YES];
}
@end
