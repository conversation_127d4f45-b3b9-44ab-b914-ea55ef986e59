//
//  YBUserEnterAnimation.m
//  YBVideo
//
//  Created by YB007 on 2019/12/3.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBUserEnterAnimation.h"
#import "UIView+LBExtension.h"
@interface YBUserEnterAnimation()

@property(nonatomic,strong)UIImageView *vipimage;
@property(nonatomic,strong)UIImageView *userMoveImageV;     //进入动画背景
@property(nonatomic,strong)UIView *msgView;                 //显示用户信息

@property(nonatomic,assign)int  isUserMove;                 // 限制用户进入动画
@property(nonatomic,strong)NSMutableArray *userLogin;       //用户进入数组，存放动画

@end

@implementation YBUserEnterAnimation

- (instancetype)init {
    self = [super init];
    if (self) {
        _isUserMove = 0;
        _userLogin = [NSMutableArray array];
    }
    return self;
}

-(void)addUserMove:(NSDictionary *)msg{
    
    if (msg) {
        [_userLogin addObject:msg];
    }
    if(_isUserMove == 0){
        [self userLoginOne];
    }
}
-(void)userLoginOne{
    
    if (_userLogin.count == 0 || _userLogin == nil) {
        return;
    }
    NSDictionary *Dic = [_userLogin firstObject];
    [_userLogin removeObjectAtIndex:0];
    [self userPlar:Dic];
}
/*
 vip_type vip预留功能，目前只有守护
 */
-(void)userPlar:(NSDictionary *)dic{
    _isUserMove = 1;
    _userMoveImageV = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width + 20,0, _window_width*0.8,40)];
    [_userMoveImageV setImage:[UIImage imageNamed:@"userlogin_Back"]];
    [self addSubview:_userMoveImageV];
    _msgView = [[UIView alloc]initWithFrame:CGRectMake(-_window_width, 0, _window_width, 40)];
    _msgView.backgroundColor = [UIColor clearColor];
    [self addSubview:_msgView];
    
    NSDictionary *ct = [dic valueForKey:@"ct"];
    
    UIImageView *iconImgView = [[UIImageView alloc]initWithFrame:CGRectMake(5, 5, 30, 30)];
    iconImgView.layer.cornerRadius = 15.0;
    iconImgView.layer.masksToBounds  =YES;
    iconImgView.layer.borderColor = [UIColor whiteColor].CGColor;
    iconImgView.layer.borderWidth = 1;
    [iconImgView sd_setImageWithURL:[NSURL URLWithString:minstr([ct valueForKey:@"avatar"])] placeholderImage:[UIImage imageNamed:@"bg1"]];
    [_msgView addSubview:iconImgView];
    
    UIImageView *starImgView = [[UIImageView alloc]initWithFrame:CGRectMake(34, 19, 8, 8)];
    starImgView.image = [UIImage imageNamed:@"loginStar"];
    [_msgView addSubview:starImgView];
    
    //
    UILabel *nameL = [[UILabel alloc]initWithFrame:CGRectMake(iconImgView.right + 3,5,_window_width*0.8-40,30)];
    nameL.textColor = [UIColor whiteColor];
    nameL.font = [UIFont systemFontOfSize:13];
    
    NSMutableAttributedString *noteStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ %@",[ct valueForKey:@"user_nickname"],YZMsg(@"进入了直播间")]];
    
    NSAttributedString *speaceString = [[NSAttributedString  alloc]initWithString:@" "];
    
    //vip
    NSTextAttachment *vipAttchment = [[NSTextAttachment alloc]init];
    vipAttchment.bounds = CGRectMake(0, -2, 30, 15);//设置frame
    vipAttchment.image = [UIImage imageNamed:@"chat_vip"];//设置图片
    NSAttributedString *vipString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(vipAttchment)];
    
    //守护
    NSTextAttachment *shouAttchment = [[NSTextAttachment alloc]init];
    shouAttchment.bounds = CGRectMake(0, -2, 15, 15);//设置frame
    shouAttchment.image = [UIImage imageNamed:getImagename(@"chat_shou_month")];//设置图片
    NSAttributedString *shouString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(shouAttchment)];
    
    NSTextAttachment *yearAttchment = [[NSTextAttachment alloc]init];
    yearAttchment.bounds = CGRectMake(0, -2, 15, 15);//设置frame
    yearAttchment.image = [UIImage imageNamed:getImagename(@"chat_shou_year")];//设置图片
    NSAttributedString *yearString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(yearAttchment)];
    
    if ([minstr([ct valueForKey:@"vip_type"])isEqual:@"1"]) {
        //插入VIP图标
        [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
        [noteStr insertAttributedString:vipString atIndex:0];//插入到第几个下标
    }
    if ([minstr([ct valueForKey:@"guard_type"]) isEqual:@"1"]) {
        //插入月守护
        [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
        [noteStr insertAttributedString:shouString atIndex:0];//插入到第几个下标
    }
    if ([minstr([ct valueForKey:@"guard_type"]) isEqual:@"2"]) {
        //插入年守护
        [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
        [noteStr insertAttributedString:yearString atIndex:0];//插入到第几个下标
    }
    
    [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
    
    [nameL setAttributedText:noteStr];
    
    [_msgView addSubview:nameL];
    CGMutablePathRef path = CGPathCreateMutable();
    //CGPathAddArc函数是通过圆心和半径定义一个圆，然后通过两个弧度确定一个弧线。注意弧度是以当前坐标环境的X轴开始的。
    //需要注意的是由于ios中的坐标体系是和Quartz坐标体系中Y轴相反的，所以iOS UIView在做Quartz绘图时，Y轴已经做了Scale为-1的转换，因此造成CGPathAddArc函数最后一个是否是顺时针的参数结果正好是相反的，也就是说如果设置最后的参数为1，根据参数定义应该是顺时针的，但实际绘图结果会是逆时针的！
    //严格的说，这个方法只是确定一个中心点后，以某个长度作为半径，以确定的角度和顺逆时针而进行旋转，半径最低设置为1，设置为0则动画不会执行
    
    CGPathAddArc(path, NULL, iconImgView.centerX, iconImgView.centerY, 16, 0,M_PI * 2, 0);
    
    CAKeyframeAnimation * animation = [CAKeyframeAnimation animationWithKeyPath:@"position"];
    animation.path = path;
    CGPathRelease(path);
    animation.duration = 3;
    animation.repeatCount = 1;
    animation.autoreverses = NO;
    animation.rotationMode =kCAAnimationRotateAuto;
    animation.fillMode =kCAFillModeForwards;
    
    [UIView animateWithDuration:0.5 animations:^{
        _userMoveImageV.x = 80;
        _msgView.x = 80;
    }completion:^(BOOL finished) {
        [starImgView.layer addAnimation:animation forKey:nil];
    }];
    
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:1.5 animations:^{
            _userMoveImageV.x = 10;
            _msgView.x = 10;
            
        }] ;
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:0.5 animations:^{
            _userMoveImageV.x = -_window_width;
            _msgView.x = _window_width;
        } completion:^(BOOL finished) {
            [_userMoveImageV removeFromSuperview];
            _userMoveImageV = nil;
            [_msgView removeFromSuperview];
            _msgView = nil;
            _isUserMove = 0;
            if (_userLogin.count >0) {
                [self addUserMove:nil];
            }
        }];
        
    });
}
-(void)removeAnimation {
    if (_userMoveImageV) {
        [_userMoveImageV removeFromSuperview];
        _userMoveImageV = nil;
    }
    if (_msgView) {
        [_msgView removeFromSuperview];
        _msgView = nil;
    }
}
@end
