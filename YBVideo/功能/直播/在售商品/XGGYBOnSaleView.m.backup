//
//  YBOnSaleView.m
//  YBVideo
//
//  Created by YB007 on 2020/8/31.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBOnSaleView.h"

@interface YBOnSaleView()<UITableViewDelegate,UITableViewDataSource,UIGestureRecognizerDelegate>
{
    int _paging;
    NSString *_titlePre;
}
@property(nonatomic,assign)BOOL isAnchor;
@property(nonatomic,strong)NSString *anchorID;
@property(nonatomic,strong)UIView *bgView;
@property(nonatomic,strong)UILabel *titleL;
@property(nonatomic,strong)UIButton *addGoodsBtn;
@property(nonatomic,strong)UITableView *tableView;

@property(nonatomic,strong)NSMutableArray *dataArray;

@end

@implementation YBOnSaleView


+(instancetype)showSaleWithAnchor:(BOOL)isAnchor andAnchorID:(NSString *)anchorID{
    
    YBOnSaleView *onSaleView = [[YBOnSaleView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    [[UIApplication sharedApplication].delegate.window addSubview:onSaleView];
    onSaleView.isAnchor = isAnchor;
    onSaleView.anchorID = anchorID;
    [onSaleView createUI];
    
    return onSaleView;
}

-(void)createUI {
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(dissmissView)];
    tap.delegate = self;
    [self addGestureRecognizer:tap];
    
    _titlePre = YZMsg(@"在售商品");
    _paging = 1;
    self.dataArray = [NSMutableArray array];
    
    _bgView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-280-ShowDiff, _window_width, 280+ShowDiff)];
    _bgView.backgroundColor = UIColor.whiteColor;
    [self addSubview:_bgView];
    
    UIView *topNav = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 50)];
    topNav.backgroundColor = UIColor.whiteColor;
    [_bgView addSubview:topNav];
    
    _titleL = [[UILabel alloc]init];
    _titleL.font = SYS_Font(14);
    _titleL.text = [NSString stringWithFormat:@"%@ 0",_titlePre];
    _titleL.textColor =[UIColor blackColor];// RGB_COLOR(@"#323232", 1);
    [topNav addSubview:_titleL];
    [_titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(topNav);
    }];
    UILabel *topLineL = [[UILabel alloc]init];
    topLineL.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
    [topNav addSubview:topLineL];
    [topLineL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.bottom.centerX.equalTo(topNav);
        make.height.mas_equalTo(1);
    }];
    
    _addGoodsBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [_addGoodsBtn setImage:[UIImage imageNamed:@"在售-加"] forState:0];
    [_addGoodsBtn setTitle:YZMsg(@"添加商品") forState:0];
    [_addGoodsBtn setTitleColor:Pink_Cor forState:0];
    _addGoodsBtn.titleLabel.font = SYS_Font(12);
    _addGoodsBtn.contentEdgeInsets = UIEdgeInsetsMake(5, 10, 5, 10);
    [_addGoodsBtn addTarget:self action:@selector(clickAddBtn) forControlEvents:UIControlEventTouchUpInside];
    [topNav addSubview:_addGoodsBtn];
    [_addGoodsBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(topNav.mas_right).offset(-5);
        make.centerY.equalTo(topNav);
    }];
    [topNav layoutIfNeeded];
    _addGoodsBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -5, 0, 0);
    _addGoodsBtn.hidden = YES;
    if (_isAnchor) {
        _addGoodsBtn.hidden = NO;
    }
    [_bgView addSubview:self.tableView];
    
    [self pullData];
    
}
-(void)clickAddBtn {
    //10-26调整要求返回消失在售列表,要做展示操作只能手动再次点开[注:注释 [self dissmissView] 添加操作后会刷新]
    [self dissmissView];
    if (self.onSaleEvent) {
        self.onSaleEvent(OnSaleType_addGoods,@{});
    }
}

#pragma mark -
-(void)pullData {
    if ([PublicObj checkNull:_anchorID]) {
        return;
    }
    [YBNetworking postWithUrl:@"Shop.getSale" Dic:@{@"liveuid":_anchorID,@"p":@(_paging)} Suc:^(int code, id info, NSString *msg) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            _titleL.text = [NSString stringWithFormat:@"%@ %@",_titlePre,minstr([infoDic valueForKey:@"nums"])];;
            NSArray *listA = [NSArray arrayWithArray:[infoDic valueForKey:@"list"]];
            if (_paging == 1) {
                [_dataArray removeAllObjects];
            }
            if (listA.count <= 0) {
                [_tableView.mj_footer endRefreshingWithNoMoreData];
            }else{
                [_dataArray addObjectsFromArray:listA];
            }
            if (_dataArray.count <= 0 ) {
                [PublicView showTextNoData:_tableView text1:YZMsg(@"当前暂无在售商品") text2:YZMsg(@"点击右上角添加商品到购物车") centerY:0.5];
            }else {
                [PublicView hiddenTextNoData:_tableView];
            }
            [_tableView reloadData];
            
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
    }];
    
    
}
#pragma mark - UITableViewDelegate、UITableViewDataSource

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return nil;
}
- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForFooterInSection:(NSInteger)section {
    return 0;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 100;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    
    return _dataArray.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    
    YBOnSaleCell *cell = [YBOnSaleCell cellWithTab:tableView index:indexPath];
    cell.isAnchor = _isAnchor;

    NSDictionary *subDic = _dataArray[indexPath.row];
    cell.dataDic = subDic;
    YBWeakSelf;
    cell.saleCellEnvent = ^(OnSaleType ctrType) {
        weakSelf.onSaleEvent(ctrType,subDic);
        if (ctrType == OnSaleType_Default || ctrType == OnSaleType_Info) {
            [weakSelf dissmissView];
        }
    };
    return cell;
}


-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
}

#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,50, _window_width, _bgView.height-50)style:UITableViewStylePlain];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = UIColor.whiteColor;
        YBWeakSelf;
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            _paging = 1;
            [weakSelf pullData];
        }];
        _tableView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            _paging +=1;
            [weakSelf pullData];
        }];
        _tableView.contentInset = UIEdgeInsetsMake(0, 0, ShowDiff, 0);
    }
    return _tableView;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch;{
   
    if ([touch.view isDescendantOfView:self.bgView]) {
        return NO;
    }
    return YES;
}
-(void)dissmissView {
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}
@end
