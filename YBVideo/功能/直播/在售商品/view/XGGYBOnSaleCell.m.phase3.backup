//
//  YBOnSaleCell.m
//  YBVideo
//
//  Created by YB007 on 2020/8/31.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBOnSaleCell.h"

@implementation YBOnSaleCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    [_anchorShowBtn setTitle:YZMsg(@"展示") forState:0];
    [_anchorDelBtn setTitle:YZMsg(@"移除") forState:0];
    [_userCtrBtn setTitle:YZMsg(@"去看看") forState:0];
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}


+(YBOnSaleCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index {
    YBOnSaleCell *cell = [table dequeueReusableCellWithIdentifier:@"YBOnSaleCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"YBOnSaleCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.backgroundColor = UIColor.whiteColor;
    cell.contentView.backgroundColor = UIColor.whiteColor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

- (void)setIsAnchor:(BOOL)isAnchor{
    _isAnchor = isAnchor;
    _anchorCtrView.hidden = !_isAnchor;
    _userCtrBtn.hidden = _isAnchor;
}

- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    [_thumbIV sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])]];
    _titleL.text = minstr([_dataDic valueForKey:@"name"]);
    _priceL.text = [NSString stringWithFormat:@"￥ %@",[_dataDic valueForKey:@"price"]];
    NSString *oldP = [NSString stringWithFormat:@"￥%@",[_dataDic valueForKey:@"old_price"]];
    _oldPriceL.hidden = _oldPL.hidden = YES;
//    if (![PublicObj checkNull:oldP]) {
    if ([minstr([_dataDic valueForKey:@"type"]) isEqual:@"1"]) {
        _oldPriceL.hidden = _oldPL.hidden = NO;
        _oldPriceL.text = oldP;
    }
    if (_isAnchor) {
        if ([minstr([_dataDic valueForKey:@"type"]) isEqual:@"2"]) {
            _oldPriceL.hidden = NO;
            _oldPriceL.text = [NSString stringWithFormat:YZMsg(@"佣 ¥%@"),minstr([_dataDic valueForKey:@"commission"])];
            _oldPriceL.textColor = Pink_Cor;
        }

    }
    //1-展开  0-非展开
    int live_isshow = [minstr([_dataDic valueForKey:@"live_isshow"]) intValue];
    _anchorShowBtn.selected = live_isshow;
    
}

- (IBAction)clikcAnchorShowBtn:(UIButton *)sender {
    sender.selected = !sender.selected;
    if (self.saleCellEnvent) {
        self.saleCellEnvent(sender.selected?OnSaleType_Show:OnSaleType_Hidden);
    }
}
- (IBAction)clickAnchorDelBtn:(id)sender {
    if (self.saleCellEnvent) {
        self.saleCellEnvent(OnSaleType_Delete);
    }
}

- (IBAction)clikcUserCtrBtn:(id)sender {
    if (self.saleCellEnvent) {
        self.saleCellEnvent(OnSaleType_Info);
    }
}


@end
