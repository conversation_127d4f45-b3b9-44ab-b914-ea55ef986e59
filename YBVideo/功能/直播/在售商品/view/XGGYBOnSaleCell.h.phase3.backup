//
//  YBOnSaleCell.h
//  YBVideo
//
//  Created by YB007 on 2020/8/31.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,OnSaleType) {
    OnSaleType_Default,
    OnSaleType_addGoods,
    OnSaleType_Show,
    OnSaleType_Hidden,
    OnSaleType_Delete,
    OnSaleType_Info,
};

typedef void (^OnSaleCellBlock)(OnSaleType ctrType);

@interface YBOnSaleCell : UITableViewCell

@property (weak, nonatomic) IBOutlet UIImageView *thumbIV;
@property (weak, nonatomic) IBOutlet UILabel *titleL;
@property (weak, nonatomic) IBOutlet UILabel *priceL;
@property (weak, nonatomic) IBOutlet UILabel *oldPriceL;
@property (weak, nonatomic) IBOutlet UILabel *oldPL;
@property (weak, nonatomic) IBOutlet UIView *anchorCtrView;
@property (weak, nonatomic) IBOutlet UIButton *anchorShowBtn;
@property (weak, nonatomic) IBOutlet UIButton *anchorDelBtn;
@property (weak, nonatomic) IBOutlet UIButton *userCtrBtn;

@property(nonatomic,copy)OnSaleCellBlock saleCellEnvent;

@property(nonatomic,assign)BOOL isAnchor;
@property(nonatomic,strong)NSDictionary *dataDic;

+(YBOnSaleCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index;


@end

