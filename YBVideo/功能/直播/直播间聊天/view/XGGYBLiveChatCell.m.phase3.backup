//
//  YBLiveChatCell.m
//  YBVideo
//
//  Created by YB007 on 2019/12/3.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveChatCell.h"

#import "SDWebImageManager.h"


@implementation YBLiveChatCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.contentView.backgroundColor = UIColor.clearColor;
    self.backgroundColor = UIColor.clearColor;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
}

- (void)setModel:(YBLiveChatModel *)model {
    _model = model;
    
    _model = model;
    if ([_model.titleColor isEqual:@"userLogin"]) {
        _chatLabel.text = [NSString stringWithFormat:@"%@ %@",_model.userName,_model.contentChat];
    }else if ([_model.titleColor isEqual:@"redbag"]){
        _chatLabel.text = [NSString stringWithFormat:@"%@%@",_model.userName,_model.contentChat];
    }else{
        _chatLabel.text = [NSString stringWithFormat:@"%@:%@",_model.userName,_model.contentChat];
    }
    NSMutableAttributedString *noteStr = [[NSMutableAttributedString alloc] initWithString:self.chatLabel.text attributes:nil];
    
    _chatLabel.font = [UIFont systemFontOfSize:14];
    
    //入场消息 开播警告
    if ([_model.titleColor isEqual:@"firstlogin"]) {
        self.chatView.backgroundColor = RGB_COLOR(@"#000000", 0.3);
        
        _chatLabel.textColor = Pink_Cor;
        noteStr = [[NSMutableAttributedString alloc] initWithString:_model.contentChat attributes:nil];
    }else if ([_model.titleColor isEqual:@"redbag"]){
        _chatLabel.textColor = [UIColor whiteColor];
        _chatLabel.font = [UIFont boldSystemFontOfSize:14];
        self.chatView.backgroundColor = RGB_COLOR(@"#f7501d", 0.9);
    }
    else{
        self.chatView.backgroundColor = RGB_COLOR(@"#000000", 0.3);
        NSAttributedString *speaceString = [[NSAttributedString  alloc]initWithString:@" "];
        //超管
        NSTextAttachment *adminAttchment = [[NSTextAttachment alloc]init];
        adminAttchment.bounds = CGRectMake(0, -2, 20, 15);//设置frame
        adminAttchment.image = [UIImage imageNamed:getImagename(@"chat_admin")];//设置图片
        NSAttributedString *adminString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(adminAttchment)];
        //vip
        NSTextAttachment *vipAttchment = [[NSTextAttachment alloc]init];
        vipAttchment.bounds = CGRectMake(0, -2, 30, 15);//设置frame
        vipAttchment.image = [UIImage imageNamed:@"chat_vip"];//设置图片
        NSAttributedString *vipString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(vipAttchment)];
        //守护
        NSTextAttachment *shouAttchment = [[NSTextAttachment alloc]init];
        shouAttchment.bounds = CGRectMake(0, -2, 15, 15);//设置frame
        shouAttchment.image = [UIImage imageNamed:getImagename(@"chat_shou_month")];//设置图片
        NSAttributedString *shouString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(shouAttchment)];
        
        NSTextAttachment *yearAttchment = [[NSTextAttachment alloc]init];
        yearAttchment.bounds = CGRectMake(0, -2, 15, 15);//设置frame
        yearAttchment.image = [UIImage imageNamed:getImagename(@"chat_shou_year")];//设置图片
        NSAttributedString *yearString = [NSAttributedString attributedStringWithAttachment:(NSTextAttachment *)(yearAttchment)];
       //用户名
        NSRange redRange = NSMakeRange(0, _model.userName.length+1);
        if ([_model.titleColor isEqual:@"userLogin"]){
            self.chatLabel.textColor = RGB_COLOR(@"#c7c9c7", 1);
        }else{
            self.chatLabel.textColor = [UIColor whiteColor];
        }
        if ([_model.isAnchor isEqual:@"1"]) {
            [noteStr addAttribute:NSForegroundColorAttributeName value:Pink_Cor range:redRange];
        }else{
            [noteStr addAttribute:NSForegroundColorAttributeName value:Pink_Cor range:redRange];
        }
        
        /*
         0 青蛙
         1 猴子
         2 小红花
         3 小黄花
         4 心
         */
        if ([_model.titleColor isEqual:@"light0"])//青蛙
        {
            self.chatLabel.textColor = RGB_COLOR(@"#c7c9c7", 1);
            
            // 添加表情
            NSTextAttachment *attch = [[NSTextAttachment alloc] init];
            // 表情图片
            attch.image = [UIImage imageNamed:@"plane_heart_cyan.png"];
            // 设置图片大小
            attch.bounds = CGRectMake(0,-4,17,17);
            NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
            [noteStr appendAttributedString:string];
        }
        else if ([_model.titleColor isEqual:@"light1"])//猴子
        {
            self.chatLabel.textColor = RGB_COLOR(@"#c7c9c7", 1);
            
            // 添加表情
            NSTextAttachment *attch = [[NSTextAttachment alloc] init];
            // 表情图片
            attch.image = [UIImage imageNamed:@"plane_heart_pink.png"];
            // 设置图片大小
            attch.bounds = CGRectMake(0,-4,17,17);
            NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
            [noteStr appendAttributedString:string];
        }
        else if ([_model.titleColor isEqual:@"light2"])//小红花
        {
            self.chatLabel.textColor = RGB_COLOR(@"#c7c9c7", 1);
            
            // 添加表情
            NSTextAttachment *attch = [[NSTextAttachment alloc] init];
            // 表情图片
            attch.image = [UIImage imageNamed:@"plane_heart_red.png"];
            // 设置图片大小
            attch.bounds = CGRectMake(0,-4,17,17);
            NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
            [noteStr appendAttributedString:string];
        }
        else if ([_model.titleColor isEqual:@"light3"])//小黄花
        {
            self.chatLabel.textColor = RGB_COLOR(@"#c7c9c7", 1);
            
            // 添加表情
            NSTextAttachment *attch = [[NSTextAttachment alloc] init];
            // 表情图片
            attch.image = [UIImage imageNamed:@"plane_heart_yellow.png"];
            // 设置图片大小
            attch.bounds = CGRectMake(0,-4, 17, 17);
            NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
            [noteStr appendAttributedString:string];
        }
        else if ([_model.titleColor isEqual:@"light4"])//心
        {
            self.chatLabel.textColor = RGB_COLOR(@"#c7c9c7", 1);
            
            // 添加表情
            NSTextAttachment *attch = [[NSTextAttachment alloc] init];
            // 表情图片
            attch.image = [UIImage imageNamed:@"plane_heart_heart"];
            // 设置图片大小
            attch.bounds = CGRectMake(0,-4, 17, 17);
            NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
            [noteStr appendAttributedString:string];
        }
        
        //插入守护
        if ([_model.guard_type isEqual:@"1"]) {
            [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
            [noteStr insertAttributedString:shouString atIndex:0];//插入到第几个下标
        }
        if ([_model.guard_type isEqual:@"2"]) {
            [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
            [noteStr insertAttributedString:yearString atIndex:0];//插入到第几个下标
        }
        
        //插入管理图标
        if ([_model.isAdmin isEqual:@"1"]) {
            [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
            [noteStr insertAttributedString:adminString atIndex:0];//插入到第几个下标
        }
        //插入VIP图标
        if ([_model.vip_type isEqual:@"1"]) {
            [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
            [noteStr insertAttributedString:vipString atIndex:0];//插入到第几个下标
        }
        [noteStr insertAttributedString:speaceString atIndex:0];//插入到第几个下标
        
    }
    [self.chatLabel setAttributedText:noteStr];
    
}

@end
