//
//  YBLiveChatView.h
//  YBVideo
//
//  Created by YB007 on 2019/11/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

#define ChatViewWidth _window_width*0.75 - 15
#define ChatViewHeight _window_height*0.3

typedef void (^LiveChatBlock)(NSString *eventStr,NSDictionary *eventDic);

@interface YBLiveChatView : UIView

@property(nonatomic,copy)LiveChatBlock liveChatEvent;
//刷新消息
-(void)reloadMsg:(NSDictionary *)dic;

/** 切换房间清理上个房间的聊天记录 */
-(void)clearChatList;
@end


