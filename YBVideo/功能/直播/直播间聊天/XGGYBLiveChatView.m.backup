//
//  YBLiveChatView.m
//  YBVideo
//
//  Created by YB007 on 2019/11/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveChatView.h"
#import "YBLiveChatModel.h"
#import "YBLiveChatCell.h"

@interface YBLiveChatView()<UITableViewDelegate,UITableViewDataSource>
{
    BOOL _canScrollToBottom;
    CAGradientLayer *_gradient;
}
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)NSMutableArray *chatModels;
@property(nonatomic,strong)NSMutableArray *chatListArray;
@end

@implementation YBLiveChatView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _canScrollToBottom = YES;
        _chatListArray = [NSMutableArray array];
        _chatModels = [NSMutableArray array];
        
        [self addSubview:self.tableView];
        
        self.layer.shadowColor = UIColor.blackColor.CGColor;
        self.layer.shadowOffset = CGSizeMake(0, -5);
        self.layer.shadowOpacity = 0.6;
        self.layer.shadowRadius = 5;
        self.clipsToBounds = NO;
        
    }
    return self;
}
-(NSArray *)chatModels{
    NSMutableArray *array = [NSMutableArray array];
    for (NSDictionary *dic in _chatListArray) {
        YBLiveChatModel *model = [YBLiveChatModel modelWithDic:dic];
        [array addObject:model];
    }
    _chatModels = [array mutableCopy];
    return _chatModels;
}
//刷新消息
-(void)reloadMsg:(NSDictionary *)dic {
    
    [_chatListArray addObject:dic];
    if (_chatListArray.count>30) {
        [_chatListArray removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    
    if (_canScrollToBottom) {
        NSUInteger sectionCount = [_tableView numberOfSections];
        if (sectionCount) {
            NSUInteger rowCount = [_tableView numberOfRowsInSection:0];
            if (rowCount) {
                NSUInteger ii[2] = {sectionCount-1, 0};
                NSIndexPath* indexPath = [NSIndexPath indexPathWithIndexes:ii length:2];
                [_tableView scrollToRowAtIndexPath:indexPath atScrollPosition:UITableViewScrollPositionBottom animated:YES];
            }
        }
    }
}
/** 切换房间清理上个房间的聊天记录 */
-(void)clearChatList {
    [_chatListArray removeAllObjects];
    [self.tableView reloadData];
}
#pragma mark - UITableViewDelegate、UITableViewDataSource

-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    
    return 5;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 5)];
    view.backgroundColor = [UIColor clearColor];
    return view;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return UITableViewAutomaticDimension;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.chatModels.count;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return 1;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBLiveChatCell *cell = [tableView dequeueReusableCellWithIdentifier:@"YBLiveChatCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"YBLiveChatCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.model = _chatModels[indexPath.section];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    YBLiveChatModel *model = _chatModels[indexPath.section];
    if ([model.userName isEqual:YZMsg(@"直播间消息")]) {
        return;
    }
    if (self.liveChatEvent) {
        self.liveChatEvent(@"房间聊天-用户信息", @{@"id":model.userID,@"name":model.userName});
    }
    
}
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView{
    _canScrollToBottom = NO;
}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    _canScrollToBottom = YES;
}
#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,0, self.width, self.height)style:UITableViewStylePlain];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = UIColor.clearColor;
        _tableView.estimatedRowHeight = 80.0;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
        _tableView.showsVerticalScrollIndicator = NO;
    }
    return _tableView;
}
@end
