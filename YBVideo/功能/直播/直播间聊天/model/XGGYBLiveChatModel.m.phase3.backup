//
//  YBLiveChatModel.m
//  YBVideo
//
//  Created by YB007 on 2019/12/3.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveChatModel.h"


@implementation YBLiveChatModel


- (instancetype)initWithDic:(NSDictionary *)dic {
    self = [super init];
    if (self) {
        
        self.titleColor  = minstr([dic valueForKey:@"titleColor"]);
        self.userName = minstr([dic valueForKey:@"userName"]);
        self.contentChat = minstr([dic valueForKey:@"contentChat"]);
        self.userID = minstr([dic valueForKey:@"id"]);
        self.vip_type = minstr([dic valueForKey:@"vip_type"]);
        self.guard_type = minstr([dic valueForKey:@"guard_type"]);
        if ([minstr([dic valueForKey:@"usertype"]) isEqual:@"40"]) {
            self.isAdmin = @"1";
        }else{
            self.isAdmin = @"0";
        }
        self.isAnchor = minstr([dic valueForKey:@"isAnchor"]);
        
    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)dic {
    return [[self alloc]initWithDic:dic];
}

@end
