//
//  YBLiveChatModel.h
//  YBVideo
//
//  Created by YB007 on 2019/12/3.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>



@interface YBLiveChatModel : NSObject

@property(nonatomic,strong)NSString *titleColor;
@property(nonatomic,strong)NSString *userName;
@property(nonatomic,strong)NSString *contentChat;
@property(nonatomic,strong)NSString *vip_type;
@property(nonatomic,strong)NSString *userID;
@property(nonatomic,strong)NSString *isAdmin;
@property(nonatomic,strong)NSString *isAnchor;
@property(nonatomic,strong)NSString *guard_type;

- (instancetype)initWithDic:(NSDictionary *)dic;
+(instancetype)modelWithDic:(NSDictionary *)dic;
@end


