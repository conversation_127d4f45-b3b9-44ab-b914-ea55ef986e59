#import "adminLists.h"
#import "adminCell.h"
#import "fansModel.h"
@interface adminLists ()<UITableViewDelegate,UITableViewDataSource,adminCellDelegate>
@property(nonatomic,strong)NSArray *fansmodels;
@property(nonatomic,strong)NSArray *allArray;
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)NSString *addAdmins;

@end
@implementation adminLists


-(NSArray *)fansmodels{
    NSMutableArray *array = [NSMutableArray array];
    for (NSDictionary *dic in self.allArray) {
        fansModel *model = [fansModel modelWithDic:dic];
        [array addObject:model];
    }
    _fansmodels = array;
    return _fansmodels;
}
-(void)viewDidLoad{
    [super viewDidLoad];
    
    self.titleL.text = YZMsg(@"管理员列表");
    
    self.allArray = [NSArray array];
    self.fansmodels = [NSArray array];
    self.view.backgroundColor = [UIColor whiteColor];
    self.tableView = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStyleGrouped];
    self.tableView.contentInset = UIEdgeInsetsMake(0, 0, ShowDiff, 0);
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.bounces = NO;
    [self.view addSubview:self.tableView];
    [self listMessage];
    
}
-(void)listMessage{
    
    NSDictionary *subdic = @{
                             @"liveuid":[Config getOwnID]
                             };
    
    [YBNetworking postWithUrl:@"Live.getAdminList" Dic:subdic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            self.allArray = [[info firstObject] valueForKey:@"list"];//关注信息复制给数据源
            self.addAdmins = [[info firstObject] valueForKey:@"total"];//总的管理员
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.tableView reloadData];
            });
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
   
}

-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, _window_width, 30)];
    label.backgroundColor = [UIColor whiteColor];
    label.textColor = [UIColor blackColor];
    label.font = [UIFont systemFontOfSize:15];
    label.text = [NSString stringWithFormat:@"    %@（%ld/%@）",YZMsg(@"当前管理员"),self.allArray.count,self.addAdmins];
    return label;
}
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 30;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.allArray.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    adminCell *cell = [adminCell cellWithTableView:tableView];
    cell.delegate = self;
    fansModel *model = self.fansmodels[indexPath.row];
    cell.model = model;
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    //弹窗
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
}
-(void)doGuanzhu:(NSString *)st{
    //关注
    [MBProgressHUD showMessage:@""];
    NSString *url = [NSString stringWithFormat:@"User.setAttention&uid=%@&showid=%@&token=%@",[Config getOwnID],st,[Config getOwnToken]];
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
    
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 50;
}
- (void)delateAdminUser:(fansModel *)model{
    
    YBWeakSelf;
    NSDictionary *contentDic = @{@"title":YZMsg(@"温馨提示"),@"msg":[NSString stringWithFormat:@"%@%@%@？",YZMsg(@"是否确定取消"),model.name,YZMsg(@"的管理员身份")],@"left":YZMsg(@"关闭"),@"right":YZMsg(@"确定")};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        [weakSelf clickSureBtn:model];
    }];
    
    
}

-(void)clickSureBtn:(fansModel *)model {
    NSDictionary *setadmin = @{
                               @"liveuid":[Config getOwnID],
                               @"touid":model.uid,
                               };
    [YBNetworking postWithUrl:@"Live.setAdmin" Dic:setadmin Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSString *isadmin = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"isadmin"]];
            [self.delegate setAdminSuccess:isadmin andName:model.name andID:model.uid];
            [self listMessage];
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];
}


@end
