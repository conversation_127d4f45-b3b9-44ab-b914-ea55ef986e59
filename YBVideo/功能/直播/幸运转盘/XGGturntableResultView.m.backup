//
//  turntableResultView.m
//  yunbaolive
//
//  Created by IOS1 on 2019/8/24.
//  Copyright © 2019 cat. All rights reserved.
//

#import "turntableResultView.h"
@interface turntableResultView (){
    NSArray *resultArray;
    UIImageView *whiteView;
}

@end

@implementation turntableResultView

- (instancetype)initWithResultArray:(NSArray *)array{
    self = [super init];
    if (self) {
        self.frame = CGRectMake(0, 0, _window_width, _window_height);
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        resultArray = array;
        [self creatUI];
    }
    return self;
}
- (void)creatUI{
    whiteView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width*0.1, (_window_height-_window_width*0.88)/2, _window_width*0.8, _window_width*0.75)];
    whiteView.layer.cornerRadius = 20.0;
    whiteView.layer.masksToBounds = YES;
    whiteView.image = [UIImage imageNamed:@"turntable_中奖背景"];
    whiteView.userInteractionEnabled = YES;
    [self addSubview:whiteView];
    
    UIImageView *titleImgView = [[UIImageView alloc]init];
    titleImgView.image = [UIImage imageNamed:getImagename(@"turntable_恭喜中奖")];
    [whiteView addSubview:titleImgView];
    [titleImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(whiteView);
        make.centerY.equalTo(whiteView).multipliedBy(0.246);
        make.height.equalTo(whiteView).multipliedBy(0.08);
        make.width.equalTo(whiteView).multipliedBy(0.257);
    }];
    CGFloat viewWidth = whiteView.width/4;
    UIView *backView = [[UIView alloc]init];
    [whiteView addSubview:backView];
    [backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(whiteView);
        make.centerY.equalTo(whiteView).multipliedBy(0.857);
        make.width.mas_equalTo(resultArray.count > 4 ? viewWidth*4 : viewWidth*resultArray.count);
        make.height.mas_equalTo(resultArray.count > 4 ? viewWidth*2 : viewWidth);
    }];
    [backView layoutIfNeeded];
    for (int i = 0; i < resultArray.count; i ++) {
        NSDictionary *dic = resultArray[i];
        UIView *view = [[UIView alloc]initWithFrame:CGRectMake(viewWidth*(i%4), (i/4) * viewWidth, viewWidth, viewWidth)];
        [backView addSubview:view];
        UILabel *label = [[UILabel alloc]init];
        label.textColor = [UIColor whiteColor];
        label.font = [UIFont boldSystemFontOfSize:10];
        if ([minstr([dic valueForKey:@"type"]) isEqual:@"1"]) {
            label.text = [NSString stringWithFormat:@"%@%@X%@",[common name_coin],minstr([dic valueForKey:@"type_val"]),minstr([dic valueForKey:@"nums"])];
        }else{
            label.text = [NSString stringWithFormat:@"%@X%@",minstr([dic valueForKey:@"name"]),minstr([dic valueForKey:@"nums"])];

        }
        label.textAlignment = NSTextAlignmentCenter;
        label.numberOfLines = 0;
        label.lineBreakMode = NSLineBreakByWordWrapping;
        [view addSubview:label];
        UIImageView *imageV = [[UIImageView alloc]init];
//        imageV.backgroundColor = WPFRandomColor;
        [imageV sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"thumb"])]];
        imageV.contentMode = UIViewContentModeScaleAspectFit;
        [view addSubview:imageV];
        [imageV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(view);
            make.width.height.equalTo(view).multipliedBy(0.5);
        }];
        
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.bottom.width.equalTo(view);
            make.top.equalTo(imageV.mas_bottom);
            make.centerX.equalTo(view);
            make.width.equalTo(view).multipliedBy(0.7);
        }];

    }
    UIButton *sureBtn = [UIButton buttonWithType:0];
    [sureBtn setTitle:YZMsg(@"确定") forState:0];
    sureBtn.titleLabel.font = [UIFont boldSystemFontOfSize:15];
    [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [sureBtn setBackgroundImage:[UIImage imageNamed:@"turntable_sure"] forState:0];
    [whiteView addSubview:sureBtn];
    [sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(whiteView).multipliedBy(1.6857);
        make.centerX.equalTo(whiteView);
        make.height.equalTo(whiteView).multipliedBy(0.1);
        make.width.equalTo(sureBtn.mas_height).multipliedBy(2.5);
    }];
    UILabel *bottomLabel = [[UILabel alloc]init];
    bottomLabel.textColor = [[UIColor whiteColor] colorWithAlphaComponent:0.5];
    bottomLabel.font = [UIFont systemFontOfSize:10];
    bottomLabel.text = YZMsg(@"获得的礼物将存放在礼物背包中");
    [whiteView addSubview:bottomLabel];
    [bottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(whiteView);
        make.top.equalTo(sureBtn.mas_bottom);
        make.bottom.equalTo(whiteView);
    }];
    
    [whiteView layoutIfNeeded];
    sureBtn.layer.cornerRadius = sureBtn.height/2;
    sureBtn.layer.masksToBounds = YES;

    whiteView.transform = CGAffineTransformMakeScale(0.1,0.1);
    
    [self show];
}
- (void)show{
    
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.transform = CGAffineTransformMakeScale(1,1);
    }];
}
- (void)sureBtnClick{
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.transform = CGAffineTransformMakeScale(0.1,0.1);
    } completion:^(BOOL finished) {
        self.hidden =  YES;
        [self removeFromSuperview];
    }];
    
}

@end
