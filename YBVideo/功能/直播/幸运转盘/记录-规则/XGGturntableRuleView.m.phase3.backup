//
//  turntableRuleView.m
//  yunbaolive
//
//  Created by IOS1 on 2019/8/24.
//  Copyright © 2019 cat. All rights reserved.
//

#import "turntableRuleView.h"
#import <WebKit/WebKit.h>
@implementation turntableRuleView{
    UIView *whiteView;
    WKWebView *_webView;
}

-(instancetype)init{
    self = [super init];
    if (self) {
        self.frame = CGRectMake(0, 0, _window_width, _window_height);
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        [self creatUI];
    }
    return self;
}
- (void)creatUI{
    whiteView = [[UIView alloc]initWithFrame:CGRectMake(_window_width*0.1, (_window_height-_window_width*0.88)/2, _window_width*0.8, _window_width*0.88)];
    whiteView.backgroundColor = [UIColor whiteColor];
    whiteView.layer.cornerRadius = 10.0;
    whiteView.layer.masksToBounds = YES;
    [self addSubview:whiteView];
    UILabel *titleL = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, whiteView.width, whiteView.width*0.2)];
    titleL.text = YZMsg(@"游戏规则");
    titleL.font = [UIFont boldSystemFontOfSize:15];
    titleL.textAlignment = NSTextAlignmentCenter;
    [whiteView addSubview:titleL];
    
    UIButton *sureBtn = [UIButton buttonWithType:0];
    sureBtn.frame = CGRectMake(0, whiteView.height-whiteView.width/6, whiteView.width, whiteView.width/6);
    [sureBtn setTitle:YZMsg(@"确定") forState:0];
    sureBtn.titleLabel.font = [UIFont boldSystemFontOfSize:15];
    [sureBtn setTitleColor:[UIColor blackColor] forState:0];
    [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [whiteView addSubview:sureBtn];
    
    _webView = [[WKWebView alloc]init];
    _webView.backgroundColor = [UIColor whiteColor];
    NSString *paths = [h5url stringByAppendingString:@"/portal/page/index?id=152"];//id=35
    paths = [paths stringByAppendingFormat:@"&lang=%@",[YBLanguageTools serviceLang]];
    NSURL *url = [NSURL URLWithString:paths];
    NSURLRequest *request = [NSURLRequest requestWithURL:url];
    [_webView loadRequest:request];
    _webView.frame = CGRectMake(20, titleL.bottom, whiteView.width-40, sureBtn.top-(titleL.bottom)-1);
    [whiteView addSubview:_webView];
    [PublicObj lineViewWithFrame:CGRectMake(0, _webView.bottom, whiteView.width, 1) andColor:RGB_COLOR(@"#f0f0f0", 1) andView:whiteView];
    whiteView.transform = CGAffineTransformMakeScale(0.1,0.1);

    [self show];
}
- (void)show{
    
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.transform = CGAffineTransformMakeScale(1,1);
    }];
}
- (void)sureBtnClick{
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.transform = CGAffineTransformMakeScale(0.1,0.1);
    } completion:^(BOOL finished) {
        self.hidden =  YES;
    }];

}
@end
