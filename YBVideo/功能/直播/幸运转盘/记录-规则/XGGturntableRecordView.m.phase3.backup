//
//  turntableRecordView.m
//  yunbaolive
//
//  Created by IOS1 on 2019/6/10.
//  Copyright © 2019 cat. All rights reserved.
//

#import "turntableRecordView.h"
#import "turntableRecordCell.h"

@implementation turntableRecordView{
    UIView *whiteView;
    UITableView *listTable;
    NSMutableArray *listArray;
    int page;
}
- (void)sureBtnClick{
    [UIView animateWithDuration:0.3 animations:^{
        whiteView.transform = CGAffineTransformMakeScale(0.1,0.1);
    } completion:^(BOOL finished) {
        self.hidden =  YES;
    }];
    
}

- (void)show{
    [UIView animateWithDuration:0.3 animations:^{
        [UIView animateWithDuration:0.3 animations:^{
            whiteView.transform = CGAffineTransformMakeScale(1,1);
        }];
    }];
    
}
-(instancetype)init{
    if (self = [super init]) {
        self.frame = CGRectMake(0, 0, _window_width, _window_height);
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        listArray = [NSMutableArray array];
        page = 1;
        [self creatUI];
        [self requestData];

    }
    return self;
}
- (void)creatUI{
    whiteView = [[UIView alloc]initWithFrame:CGRectMake(_window_width*0.1, (_window_height-_window_width*0.88)/2, _window_width*0.8, _window_width*0.88)];
    whiteView.backgroundColor = [UIColor whiteColor];
    whiteView.layer.cornerRadius = 10.0;
    whiteView.layer.masksToBounds = YES;
    [self addSubview:whiteView];
    UILabel *titleL = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, whiteView.width, whiteView.width*0.2)];
    titleL.text = YZMsg(@"中奖记录");
    titleL.font = [UIFont boldSystemFontOfSize:15];
    titleL.textAlignment = NSTextAlignmentCenter;
    [whiteView addSubview:titleL];
    
    UIButton *sureBtn = [UIButton buttonWithType:0];
    sureBtn.frame = CGRectMake(0, whiteView.height-whiteView.width/6, whiteView.width, whiteView.width/6);
    [sureBtn setTitle:YZMsg(@"确定") forState:0];
    sureBtn.titleLabel.font = [UIFont boldSystemFontOfSize:15];
    [sureBtn setTitleColor:[UIColor blackColor] forState:0];
    [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [whiteView addSubview:sureBtn];

    listTable = [[UITableView alloc]initWithFrame:CGRectMake(10, titleL.bottom, whiteView.width-20, sureBtn.top-(titleL.bottom)-1) style:0];
    listTable.separatorStyle = 0;
    listTable.delegate = self;
    listTable.dataSource = self;
    listTable.backgroundColor = [UIColor whiteColor];
    [whiteView addSubview:listTable];
    listTable.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
        page = 1;
        [self requestData];
    }];
    listTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
        page ++;
        [self requestData];

    }];
    [PublicObj lineViewWithFrame:CGRectMake(0, listTable.bottom, whiteView.width, 1) andColor:RGB_COLOR(@"#f0f0f0", 1) andView:whiteView];

    whiteView.transform = CGAffineTransformMakeScale(0.1,0.1);

    [self show];
    
}
- (void)requestData{
    
    [YBNetworking postWithUrl:@"Turntable.getWin" Dic:@{@"p":@(page)} Suc:^(int code, id info, NSString *msg) {
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];
        
        if (code == 0) {
            if (page == 1) {
                [listArray removeAllObjects];
            }
            [listArray addObjectsFromArray:info];
            [listTable reloadData];
        }
        if ([info count] == 0) {
            [listTable.mj_footer endRefreshingWithNoMoreData];
        }
        if(listArray.count < 1){
            [PublicView showTextNoData:listTable text1:@"" text2:YZMsg(@"暂无数据") centerY:0.8];
        }else{
            [PublicView hiddenTextNoData:listTable];
        }
    } Fail:^(id fail) {
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];
    }];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return listArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    turntableRecordCell *cell = [tableView dequeueReusableCellWithIdentifier:@"turntableRecordCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"turntableRecordCell" owner:nil options:nil] lastObject];
    }
    cell.nameL.text = [NSString stringWithFormat:@"%ld",indexPath.row + 1];
    NSDictionary *dic = listArray[indexPath.row];
    cell.giftNumL.text = [NSString stringWithFormat:@"x%@",minstr([dic valueForKey:@"nums"])];
    cell.timeL.text = minstr([dic valueForKey:@"addtime"]);
    [cell.giftImgV sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"thumb"])]];
    return cell;

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
@end
