//
//  YBDayTaskCell.m
//  YBVideo
//
//  Created by YB007 on 2020/10/9.
//  Copyright © 2020 cat. All rights reserved.
//


#import "YBDayTaskCell.h"

@implementation YBDayTaskCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}
+(YBDayTaskCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath {
    YBDayTaskCell *cell = [tableView dequeueReusableCellWithIdentifier:@"YBDayTaskCell"];
     if (!cell) {
             cell = [[[NSBundle mainBundle]loadNibNamed:@"YBDayTaskCell" owner:nil options:nil]objectAtIndex:0];
     }
     return cell;

}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(void)setDataDic:(NSDictionary *)dataDic
{
    _dataDic = dataDic;
    // state 0代表未达标  1代表可领取  2代表已领取
    self.titleLb.text = minstr([dataDic valueForKey:@"title"]);
    self.contentLb.text = minstr([dataDic valueForKey:@"tip_m"]);
    if ([minstr([dataDic valueForKey:@"state"]) isEqual:@"0"]) {
        [self.stateBtn setTitle:YZMsg(@"未达成") forState:0];
        [self.stateBtn setTitleColor:RGB_COLOR(@"#cdcdcd", 1) forState:0];
        [self.contentView layoutIfNeeded];
        [PublicObj addGradientWithFromColor:RGB_COLOR(@"#000000", 0) andEndColor:RGB_COLOR(@"#000000", 0) andView:self.stateBtn direction:1];
        self.stateBtn.userInteractionEnabled = NO;
    }else if ([minstr([dataDic valueForKey:@"state"]) isEqual:@"1"]){
        [self.stateBtn setTitle:YZMsg(@"可领取") forState:0];
        [self.stateBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        [self.contentView layoutIfNeeded];
        [PublicObj addGradientWithFromColor:RGB_COLOR(@"#cb16e1", 1) andEndColor:RGB_COLOR(@"#872ae7", 1) andView:self.stateBtn direction:1];
        self.stateBtn.userInteractionEnabled = YES;
    }else if ([minstr([dataDic valueForKey:@"state"]) isEqual:@"2"]){
        [self.stateBtn setTitle:YZMsg(@"已领取") forState:0];
        [self.stateBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        [self.contentView layoutIfNeeded];
        [PublicObj addGradientWithFromColor:RGB_COLOR(@"#e3e2e2", 1) andEndColor:RGB_COLOR(@"#cacaca", 1) andView:self.stateBtn direction:1];
        self.stateBtn.userInteractionEnabled = NO;
    }
}
- (IBAction)stateBtnClick:(UIButton *)sender {
    if ([self.delegate respondsToSelector:@selector(stateBtnClick:)]) {
        [self.delegate stateBtnClick:_dataDic];
    }
}
@end
