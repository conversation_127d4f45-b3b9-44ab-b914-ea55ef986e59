//
//  YBDayTaskCell.h
//  YBVideo
//
//  Created by YB007 on 2020/10/9.
//  Copyright © 2020 cat. All rights reserved.
//


#import <UIKit/UIKit.h>

@protocol dailyCellDelegate <NSObject>

-(void)stateBtnClick:(NSDictionary *)data;

@end

@interface YBDayTaskCell : UITableViewCell
@property (strong, nonatomic) IBOutlet UILabel *titleLb;
@property (strong, nonatomic) IBOutlet UILabel *contentLb;
@property (strong, nonatomic) IBOutlet UIButton *stateBtn;
@property (strong, nonatomic) IBOutlet UILabel *lineLb;

@property (nonatomic, assign)id<dailyCellDelegate>delegate;
@property (strong, nonatomic) NSDictionary *dataDic;
+(YBDayTaskCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end

