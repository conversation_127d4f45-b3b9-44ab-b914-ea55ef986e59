//
//  YBDayTaskVC.m
//  YBVideo
//
//  Created by YB007 on 2020/10/9.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBDayTaskVC.h"
#import "YBDayTaskCell.h"
@interface YBDayTaskVC ()<UITableViewDataSource, UITableViewDelegate,dailyCellDelegate>
{
    UIImageView *backImg;
    UILabel *tapLb;
    UIView *backView;
}
@property (nonatomic, strong)UITableView *listTable;
@property (nonatomic, strong)NSArray *infoArray;

@end

@implementation YBDayTaskVC

-(void)requestListData{
    [YBNetworking postWithUrl:@"User.seeDailyTasks" Dic:@{@"liveuid":@"0",@"islive":@"0"} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSArray *infoA = [info objectAtIndex:0];
            NSArray *list =[infoA valueForKey:@"list"];
            _infoArray = list;
            [self.listTable reloadData];
            tapLb.text = minstr([infoA valueForKey:@"tip_m"]);
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    /*
    _infoArray = @[@{@"title":@"这个标题呢",@"tip_m":@"副标题呢副标题呢副标题呢副标题呢副标题呢副标题呢",@"state":@"0"},
                   @{@"title":@"这个标题呢",@"tip_m":@"副标题呢",@"state":@"1"},
                   @{@"title":@"这个标题呢",@"tip_m":@"副标题呢",@"state":@"2"},];
    */
    [self.listTable reloadData];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"每日任务");
    _infoArray = [NSArray array];
    
    [self createUI];
    
    [self requestListData];
}
-(void)createUI{
    backImg = [[UIImageView alloc]init];
    backImg.frame = CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight);
    backImg.image = [UIImage imageNamed:@"日任-页面背景"];
    backImg.userInteractionEnabled = YES;
    [self.view addSubview:backImg];
    
    UIImageView *titleImg = [[UIImageView alloc]init];
    titleImg.image = [UIImage imageNamed:getImagename(@"日任-标题1")];
    [backImg addSubview:titleImg];
    [titleImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(backImg).offset(30);
        make.centerX.equalTo(backImg);
        make.height.mas_equalTo(76);
        make.width.mas_equalTo(248);
    }];
    
    
    backView = [[UIView alloc]init];
    backView.backgroundColor = [UIColor whiteColor];
    backView.layer.cornerRadius = 10;
    backView.layer.masksToBounds = YES;
    [backImg addSubview:backView];
    [backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(backImg).offset(22);
        make.right.equalTo(backImg).offset(-22);
        make.top.equalTo(titleImg.mas_bottom).offset(50);
        make.bottom.equalTo(backImg).offset(-100-ShowDiff);
    }];
    
    _listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 0, 0, 0) style:UITableViewStylePlain];
    _listTable.dataSource = self;
    _listTable.delegate = self;
    _listTable.showsVerticalScrollIndicator = NO;
    _listTable.separatorStyle= UITableViewCellSeparatorStyleNone;
    [backView addSubview:_listTable];
    if (IS_BIG_SCREEN) {
        [_listTable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(backView).offset(-50);
            make.top.equalTo(backView).offset(40);
            make.left.right.equalTo(backView);
        }];
    }else{
        [_listTable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.left.right.equalTo(backView);
            make.top.equalTo(backView).offset(10);
        }];
    }
    
    UIImageView *tapImg = [[UIImageView alloc]init];
    tapImg.image = [UIImage imageNamed:getImagename(@"日任-标题2")];
    [backImg addSubview:tapImg];
    [tapImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(backView.mas_top).offset(21);
        make.centerX.equalTo(backImg);
        make.height.mas_equalTo(38);
        make.width.mas_equalTo(188);
    }];

    
    tapLb = [[UILabel alloc]init];
    tapLb.font = [UIFont systemFontOfSize:12];
    tapLb.textColor = RGB_COLOR(@"#FFFFFF", 0.52);
    tapLb.numberOfLines = 0;
    tapLb.lineBreakMode = NSLineBreakByWordWrapping;
    [backImg addSubview:tapLb];
    [tapLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(backView);
        make.top.equalTo(backView.mas_bottom).offset(20);
        
    }];
    
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return 5;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return _listTable.height/5;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBDayTaskCell *cell = [YBDayTaskCell cellWithTab:tableView andIndexPath:indexPath];
    cell.dataDic = _infoArray[indexPath.row];
    cell.delegate = self;
    if (indexPath.row == _infoArray.count -1) {
        cell.lineLb.hidden = YES;
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
-(void)stateBtnClick:(NSDictionary *)data{
    YBWeakSelf;
    NSDictionary *dic = @{
        @"uid":[Config getOwnID],
        @"token":[Config getOwnToken],
        @"taskid":minstr([data valueForKey:@"id"])
    };
    [YBNetworking postWithUrl:@"User.receiveTaskReward" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD showError:msg];
        if (code == 0) {
            [weakSelf requestListData];
        }
    } Fail:^(id fail) {
        
    }];

}

@end
