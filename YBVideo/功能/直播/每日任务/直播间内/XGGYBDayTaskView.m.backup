//
//  YBDayTaskView.m
//  YBVideo
//
//  Created by YB007 on 2020/10/9.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBDayTaskView.h"
#import "YBDayTaskCell.h"
@interface YBDayTaskView()<UITableViewDelegate,UITableViewDataSource,dailyCellDelegate> {
    UILabel *tapLb;
}
@property(nonatomic,strong)UITableView *listTable;
@property (nonatomic, strong)NSArray *infoArray;

@end

@implementation YBDayTaskView


+(instancetype)showRoomDayTask {
    YBDayTaskView *view = [[YBDayTaskView alloc]init];
    [view setUpView];
    
    return view;
}
-(void)requestListData{
        
    [YBNetworking postWithUrl:@"User.seeDailyTasks" Dic:@{@"liveuid":_liveuid,@" islive":@"1"} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            self.backgroundColor = RGB_COLOR(@"#000000", 0.51);
            NSArray *infoA = [info objectAtIndex:0];
            NSArray *list =[infoA valueForKey:@"list"];
            _infoArray = list;
            [self.listTable reloadData];
            tapLb.text = minstr([infoA valueForKey:@"tip_m"]);
        }
    } Fail:^(id fail) {
        
    }];

}
-(void)setUpView{
    self.infoArray = [NSArray array];
    self.frame = CGRectMake(0, 0, _window_width, _window_height);
    [[UIApplication sharedApplication].delegate.window addSubview:self];
    
    
    UIImageView *backImg = [[UIImageView alloc]init];
    backImg.frame = CGRectMake(23, 101, _window_width*0.88, _window_width*0.88 *1.57);
    backImg.image = [UIImage imageNamed:@"日任-房间背景"];
    backImg.userInteractionEnabled = YES;
    [self addSubview:backImg];
    backImg.center = self.center;
    
    UIButton *closeBtn = [UIButton buttonWithType:0];
    closeBtn.frame = CGRectMake(backImg.right-30, backImg.frame.origin.y-40, 30, 30);
    [closeBtn setBackgroundImage:[UIImage imageNamed:@"日任-关闭"] forState:0];
    [closeBtn addTarget:self action:@selector(dismissView) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:closeBtn];
    
    UIImageView *titleImg = [[UIImageView alloc]init];
    titleImg.image = [UIImage imageNamed:getImagename(@"日任-标题1")];
    [backImg addSubview:titleImg];
    [titleImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(backImg).offset(30);
        make.centerX.equalTo(backImg);
        make.height.mas_equalTo(76);
        make.width.mas_equalTo(248);
    }];
    
    
    UIView *backView = [[UIView alloc]init];
    backView.backgroundColor = [UIColor whiteColor];
    backView.layer.cornerRadius = 5;
    backView.layer.masksToBounds = YES;
    [backImg addSubview:backView];
    [backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(backImg).offset(22);
        make.right.equalTo(backImg).offset(-22);
        make.top.equalTo(titleImg.mas_bottom).offset(50);
        make.bottom.equalTo(backImg).offset(-80);
    }];
    
    _listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 0, 0, 0) style:UITableViewStylePlain];
    _listTable.dataSource = self;
    _listTable.delegate = self;
    _listTable.showsVerticalScrollIndicator = NO;
    _listTable.separatorStyle= UITableViewCellSeparatorStyleNone;
    [backView addSubview:_listTable];
    [_listTable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(backView);
        make.top.equalTo(backView).offset(10);
    }];
    
    UIImageView *tapImg = [[UIImageView alloc]init];
    tapImg.image = [UIImage imageNamed:getImagename(@"日任-标题2")];
    [backImg addSubview:tapImg];
    [tapImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(backView.mas_top).offset(21);
        make.centerX.equalTo(backImg);
        make.height.mas_equalTo(38);
        make.width.mas_equalTo(188);
    }];

    
    tapLb = [[UILabel alloc]init];
    tapLb.font = [UIFont systemFontOfSize:10];
    tapLb.textColor = RGB_COLOR(@"#FFFFFF", 0.52);
    tapLb.numberOfLines = 0;
    tapLb.lineBreakMode = NSLineBreakByWordWrapping;
    [backImg addSubview:tapLb];
    [tapLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(backView);
        make.top.equalTo(backView.mas_bottom).offset(10);
    }];
    
    
    
}

-(void)dismissView;{
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 5;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    YBDayTaskCell *cell = [YBDayTaskCell cellWithTab:tableView andIndexPath:indexPath];
    cell.dataDic = _infoArray[indexPath.row];
    cell.delegate = self;
    if (indexPath.row == _infoArray.count -1) {
        cell.lineLb.hidden = YES;
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
-(void)stateBtnClick:(NSDictionary *)data {
    YBWeakSelf;
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"taskid":minstr([data valueForKey:@"id"])
    };
    [YBNetworking postWithUrl:@"User.receiveTaskReward" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD showPop:msg];
        if (code == 0) {
            [weakSelf requestListData];
        }
    } Fail:^(id fail) {
        
    }];

}
@end
