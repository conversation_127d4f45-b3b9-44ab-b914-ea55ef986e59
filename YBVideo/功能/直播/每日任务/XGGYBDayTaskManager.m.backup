//
//  YBDayTaskManager.m
//  YBVideo
//
//  Created by YB007 on 2020/10/13.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBDayTaskManager.h"

@interface YBDayTaskManager()

@end

static YBDayTaskManager *_taskManager = nil;

@implementation YBDayTaskManager


+(instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _taskManager = [[super allocWithZone:NULL]init];
    });
    return _taskManager;
}
+ (instancetype)allocWithZone:(struct _NSZone *)zone{
    return [self shareInstance];
}

/** 每日任务:开始、结束观看视频 */
-(void)taskOfWatchVideoStart{
    [self pubPull:@"Video.startWatchVideo"];
}
-(void)taskOfWatchVideoEnd{
    [self pubPull:@"Video.endWatchVideo"];
}

/** 每日任务:分享直播间 */
-(void)taskOfShareRoom{
    [self pubPull:@"Live.shareLiveRoom"];
}

-(void)pubPull:(NSString *)url {
    if ([[Config getOwnID] intValue] <= 0) {
        NSLog(@"task-游客");
        return;
    }
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        NSLog(@"task-url:%@-%@",url,info);
    } Fail:^(id fail) {
        
    }];
}

@end
