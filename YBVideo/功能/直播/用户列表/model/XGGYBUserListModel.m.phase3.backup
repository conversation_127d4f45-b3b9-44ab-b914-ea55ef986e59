//
//  YBUserListModel.m
//  YBVideo
//
//  Created by YB007 on 2019/12/3.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBUserListModel.h"

@implementation YBUserListModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        _iconName = minstr([dic valueForKey:@"avatar"]);
        _userID =minstr([dic valueForKey:@"id"]);
        _user_nickname = minstr([dic valueForKey:@"user_nickname"]);
        _signature = minstr([dic valueForKey:@"signature"]);
        _sex = minstr([dic valueForKey:@"sex"]);
        _level = @"";//minstr([dic valueForKey:@"level"]);
        _contribution = minstr([dic valueForKey:@"contribution"]);
        if ([_city isEqual:[NSNull null]] || _city == NULL || _city == nil || [_city isEqual:@"(null)"]) {
            _city = YZMsg(@"定位在火星");
        }
        else{
            _city = [dic valueForKey:@"city"];
        }
        _vip_thumb = [dic valueForKey:@"vip_thumb"];
        _vip_type = [dic valueForKey:@"vip_type"];
        _guard_type = minstr([dic valueForKey:@"guard_type"]);
    }
    return self;
    
}
+(instancetype)modelWithDic:(NSDictionary *)dic {
    
    return   [[self alloc]initWithDic:dic];
}
@end
