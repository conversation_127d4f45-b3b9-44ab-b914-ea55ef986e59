//
//  YBUserListView.m
//  YBVideo
//
//  Created by YB007 on 2019/11/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBUserListView.h"
#import "YBUserListModel.h"
#import "YBUserListCell.h"
@interface YBUserListView()<UICollectionViewDelegate,UICollectionViewDataSource>
{
    int _userCount; //预留
}
@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,strong)NSMutableArray *listArray;
@property(nonatomic,strong)NSMutableArray *listModel;

@end

@implementation YBUserListView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _listArray = [NSMutableArray array];
        _listModel = [NSMutableArray array];
        [self addSubview:self.collectionView];
    }
    return self;
}
- (void)layoutSubviews {
    [super layoutSubviews];
    [_collectionView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(self);
    }];
}
- (NSMutableArray *)listModel {
    NSMutableArray *array = [NSMutableArray array];
       for (NSDictionary *dic in _listArray) {
           YBUserListModel *model = [YBUserListModel modelWithDic:dic];
           [array addObject:model];
       }
       _listModel = [array mutableCopy];
       return _listModel;
}

#pragma mark - 用户第一次进房间、请求僵尸粉数组赋值
-(void)updateListCount:(NSArray *)listArray {
    [_listArray addObjectsFromArray:listArray];
    _userCount = (int)_listArray.count;
}
#pragma mark - 用户进入、离开 
-(void)userEventOfType:(UserEventType)eventType andInfo:(NSDictionary *)eventDic {
    if (eventType == UserEvent_Enter) {
        //进入
        _userCount +=1;
        
        NSString *ID = [[eventDic valueForKey:@"ct"] valueForKey:@"id"];
           [_listArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                   for (NSDictionary *dic in _listArray) {
                       int a = [[dic valueForKey:@"id"] intValue];
                       int bsss = [ID intValue];
                       if ([[dic valueForKey:@"id"] isEqual:ID] || a == bsss) {
                           [_listArray removeObject:dic];
                           break;
                       }
                   }
           }];
           NSDictionary *subdic = [eventDic valueForKey:@"ct"];
           [self.listArray addObject:subdic];
           [_collectionView reloadData];
    }else {
        //离开
        _userCount -=1;
        if (_userCount <=0) {
            _userCount = 0;
        }
        
        NSDictionary *SUBdIC =[eventDic valueForKey:@"ct"];
           NSString *ID = [SUBdIC valueForKey:@"id"];
           [_listArray enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
               for (NSDictionary *dic in _listArray) {
                   if ([[dic valueForKey:@"id"] isEqual:ID]) {
                       [_listArray removeObject:dic];
                       [_collectionView reloadData];
                       return ;
                   }
               }
           }];
    }
}
#pragma mark - /** 计时器刷新 */
-(void)timerReloadList {
    if ([PublicObj checkNull:_liveUid] || [PublicObj checkNull:_liveStream]) {
        [MBProgressHUD showPop:YZMsg(@"缺少信息")];
        return;
    }
    NSDictionary *postDic = @{
                                @"liveuid":_liveUid,
                                @"stream":_liveStream,
    };
    
    [YBNetworking postWithUrl:@"Live.getUserLists" Dic:postDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSArray *infos = [info firstObject];
            NSArray *list = [infos valueForKey:@"userlist"];
            if ([list isEqual:[NSNull null]]) {
                return ;
            }
            [_listArray removeAllObjects];
            [_listArray addObjectsFromArray:list];
            [_collectionView reloadData];
        }
    } Fail:^(id fail) {
        
    }];
       
}

-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.listModel.count;
}
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 0;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section{
    return 0;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    YBUserListCell *cell = (YBUserListCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"YBUserListCell" forIndexPath:indexPath];
    YBUserListModel *model = _listModel[indexPath.row];
    cell.model = model;
    cell.backgroundColor = [UIColor clearColor];
    if (indexPath.row < 3 && [model.contribution intValue]>0) {
        cell.kuang.image = [UIImage imageNamed:[NSString stringWithFormat:@"userlist_no%ld",indexPath.row+1]];
    }else{
        cell.kuang.image = [UIImage new];
    }
    return cell;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    YBUserListModel *model = _listModel[indexPath.row];
    NSString *ID = model.userID;
    NSDictionary *subdic  = [NSDictionary dictionaryWithObjects:@[ID,model.user_nickname] forKeys:@[@"id",@"name"]];
    if (self.listEvent) {
        self.listEvent(@"用户列表-用户信息", subdic);
    }
}
-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    return CGSizeMake(40,40);
}
-(UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0,5,0,5);
}
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *flowlayoutt = [[UICollectionViewFlowLayout alloc]init];
        flowlayoutt.itemSize = CGSizeMake(40,40);
        flowlayoutt.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0,self.width,self.height) collectionViewLayout:flowlayoutt];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        [_collectionView registerClass:[YBUserListCell class] forCellWithReuseIdentifier:@"YBUserListCell"];
        _collectionView.backgroundColor = [UIColor clearColor];
    }
    return _collectionView;
}
@end
