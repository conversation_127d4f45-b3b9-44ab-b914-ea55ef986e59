//
//  YBUserListCell.m
//  YBVideo
//
//  Created by YB007 on 2019/12/3.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBUserListCell.h"
#import "SDWebImage/UIButton+WebCache.h"
#import "UIImageView+WebCache.h"
@implementation YBUserListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}
-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self) {
        _imageV = [[UIImageView alloc]initWithFrame:CGRectMake(6.2,10.6,27.6,27.6)];
        _imageV.layer.masksToBounds = YES;
        _imageV.layer.cornerRadius = 13.8;
        [self.contentView addSubview:_imageV];
        
        _kuang = [[UIImageView alloc]initWithFrame:CGRectMake(0,0,40,40)];
        _kuang.center = self.contentView.center;
        [self.contentView addSubview:_kuang];

        _levelimage = [[UIImageView alloc]initWithFrame:CGRectMake(23,27,13,13)];
        _levelimage.layer.masksToBounds = YES;
        _levelimage.layer.cornerRadius = 6.5;
        _levelimage.contentMode = UIViewContentModeScaleAspectFit;
        [self.contentView addSubview:_levelimage];
    }
    return self;
}
-(void)setModel:(YBUserListModel *)model{
    _model = model;
    [_imageV sd_setImageWithURL:[NSURL URLWithString:_model.iconName] placeholderImage:[UIImage imageNamed:@"bg1"]];
    /*
    if ([_model.guard_type isEqual:@"0"]) {
        NSDictionary *levelDic = [common getUserLevelMessage:_model.level];
        [_levelimage sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb_mark"])]];
    }else if ([_model.guard_type isEqual:@"1"]){
        _levelimage.image = [UIImage imageNamed:@"chat_shou_month"];
    }else if ([_model.guard_type isEqual:@"2"]){
        _levelimage.image = [UIImage imageNamed:@"chat_shou_year"];
    }
     */
    if ([_model.guard_type isEqual:@"1"]){
        _levelimage.hidden = NO;
        _levelimage.image = [UIImage imageNamed:getImagename(@"chat_shou_month")];
    }else if ([_model.guard_type isEqual:@"2"]){
        _levelimage.hidden = NO;
        _levelimage.image = [UIImage imageNamed:getImagename(@"chat_shou_year")];
    }else {
        _levelimage.hidden = YES;
    }
}
+(YBUserListCell *)collectionview:(UICollectionView *)collectionview andIndexpath:(NSIndexPath *)indexpath{
    YBUserListCell *cell = [collectionview dequeueReusableCellWithReuseIdentifier:@"YBUserListCell" forIndexPath:indexpath];
    if (!cell) {
        cell = [[NSBundle mainBundle]loadNibNamed:@"YBUserListCell" owner:self options:nil].lastObject;
    }
    return cell;
}
@end
