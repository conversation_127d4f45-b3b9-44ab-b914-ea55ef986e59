//
//  YBLanguageTools.m
//  SwitchLanguage
//
//  Created by Rookie on 2017/8/24.
//  Copyright © 2017年 Rookie. All rights reserved.
//

#import "YBLanguageTools.h"
#import "YBTabBarController.h"
#import <UIKit/UIKit.h>
//#import "ShowMessageVC.h"

static YBLanguageTools *shareTool = nil;

@interface YBLanguageTools()

@property(nonatomic,strong)NSBundle *bundle;
@property(nonatomic,copy)NSString *language;

@end

@implementation YBLanguageTools

+(id)shareInstance {
    @synchronized (self) {
        if (shareTool == nil) {
            shareTool = [[YBLanguageTools alloc]init];
        }
    }
    return shareTool;
}
+(instancetype)allocWithZone:(struct _NSZone *)zone {
    if (shareTool == nil) {
        shareTool = [super allocWithZone:zone];
    }
    return shareTool;
}

-(NSString *)getStringForKey:(NSString *)key withTable:(NSString *)table {
    if (self.bundle) {
        return NSLocalizedStringFromTableInBundle(key, table, self.bundle, @"");
    }
    return NSLocalizedStringFromTable(key, table, @"");
}

-(void)resetLanguage:(NSString *)language withFrom:(NSString *)appdelegate{
    if ([language isEqualToString:self.language]) {
        return;
    }
    if ([language isEqualToString:@"kor"]) {
        language = @"ko";
    }
    if ([language isEqualToString:@"en"] || [language isEqualToString:@"zh-Hans"] || [language isEqualToString:@"ko"]) {
        NSString *path = [[NSBundle mainBundle]pathForResource:language ofType:@"lproj"];
        self.bundle = [NSBundle bundleWithPath:path];
    }
    self.language = language;
    
    [[NSUserDefaults standardUserDefaults] setObject:language forKey:CurrentLanguage];
    [[NSUserDefaults standardUserDefaults]synchronize];
    if (![appdelegate isEqualToString:@"appdelegate"]) {
        [self resetRootViewController];
    }
    
    ///
    NSMutableArray *userDefaultLanguages = [[NSUserDefaults standardUserDefaults]objectForKey:@"AppleLanguages"];
    NSLog(@"rk-language-tool-get:%@",userDefaultLanguages);
    NSString *langStr = @"zh-Hans";
    if (![lagType isEqual:ZH_CN]) {
        langStr = @"en";
    }
    [[NSUserDefaults standardUserDefaults] setObject:[NSArray arrayWithObjects:langStr,nil] forKey:@"AppleLanguages"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    NSMutableArray *userDefaultLanguages1 = [[NSUserDefaults standardUserDefaults]objectForKey:@"AppleLanguages"];
    NSLog(@"rk-language-tool-set:%@",userDefaultLanguages1);
    
}
-(void)resetRootViewController {
    
    [PublicObj resetVC:[[YBTabBarController alloc]initWithAlert:NO]];
    
//    [root changeLanguage];
    
}
+(NSString *)serviceLang;{
    if ([lagType isEqual:ZH_CN]) {
        return @"zh-cn";
    }
    return @"en";
}
- (BOOL)isChinese{
    NSArray*languages = [NSLocale preferredLanguages];
    NSString*currentLanguage = [languages objectAtIndex:0];
    if([currentLanguage containsString:@"zh-Han"]){
        return YES;
    }
    return NO;
}

@end
