//
//  YBDestroyCell.m
//  YBVideo
//
//  Created by YB007 on 2020/6/22.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBDestroyCell.h"

@implementation YBDestroyCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    [_stateBtn setTitle:YZMsg(@"未通过") forState:0];
    [_stateBtn setTitleColor:Pink_Cor forState:0];
    [_stateBtn setImage:[UIImage imageNamed:@"注销-未"] forState:0];
    
    [_stateBtn setTitle:YZMsg(@"已通过") forState:UIControlStateSelected];
    [_stateBtn setTitleColor:RGB_COLOR(@"#ffffff", 0.61) forState:UIControlStateSelected];
    [_stateBtn setImage:[UIImage imageNamed:@"注销-通"] forState:UIControlStateSelected];
    
    BOOL isBoldText = [PublicObj isBlodText];
    if (isBoldText) {
        [self layoutIfNeeded];
        CGFloat www = _stateBtn.width;
        [_stateBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(www*1.1);
        }];
    }
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    
}


+(YBDestroyCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index {
    YBDestroyCell *cell = [table dequeueReusableCellWithIdentifier:@"YBDestroyCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"YBDestroyCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.backgroundColor = CellRow_Cor;
    cell.contentView.backgroundColor = CellRow_Cor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    _titleL.text = minstr([_dataDic valueForKey:@"title"]);
    _contentL.text = minstr([_dataDic valueForKey:@"content"]);
    int isOk = [minstr([_dataDic valueForKey:@"is_ok"]) intValue];
    _stateBtn.selected = isOk;
    
}

@end
