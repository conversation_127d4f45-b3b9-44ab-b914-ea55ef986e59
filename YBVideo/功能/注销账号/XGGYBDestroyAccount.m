//
//  YBDestroyAccount.m
//  YBVideo
//
//  Created by YB007 on 2020/6/22.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBDestroyAccount.h"

#import "YBDestroyCell.h"
#import "YBDestroySureVC.h"
@interface YBDestroyAccount ()<UITableViewDelegate,UITableViewDataSource>

@property(nonatomic,strong)UIView *stateView;
@property(nonatomic,strong)NSArray *dataArray;
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)UIButton *nextBtn;
@end

@implementation YBDestroyAccount

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self pullData];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"注销条件");
    
    self.dataArray = [NSArray array];
    
    [self.view addSubview:self.stateView];
    
    [self.view addSubview:self.nextBtn];
    [_nextBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(40);
        make.centerX.equalTo(self.view);
        make.width.equalTo(self.view.mas_width).offset(-40);
        make.bottom.equalTo(self.view.mas_bottom).offset(-ShowDiff-20);
    }];
    [self changeNextBtn:NO];
    
    [self.view addSubview:self.tableView];
    [_tableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.top.equalTo(_stateView.mas_bottom);
        make.bottom.equalTo(_nextBtn.mas_top).offset(-10);
    }];
}

- (UIView *)stateView {
    if (!_stateView) {
        _stateView = [[UIView alloc]initWithFrame:CGRectMake(0, self.naviView.bottom, _window_width, 40)];
        _stateView.backgroundColor = UIColor.clearColor;
        
        UILabel *stateL = [[UILabel alloc]init];
        stateL.text = YZMsg(@"满足以下条件,才能注销当前账户");
        stateL.textColor = RGB_COLOR(@"#ffffff", 0.61);
        stateL.font = [UIFont boldSystemFontOfSize:15];
        stateL.numberOfLines = 0;
        
        [_stateView addSubview:stateL];
        
        [stateL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_stateView.mas_left).offset(15);
            make.centerY.equalTo(_stateView);
            make.height.lessThanOrEqualTo(_stateView);
            make.right.lessThanOrEqualTo(_stateView.mas_right).offset(-15);
        }];
        
    }
    return _stateView;
}

- (UIButton *)nextBtn {
    if (!_nextBtn) {
        _nextBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_nextBtn setTitle:YZMsg(@"下一步") forState:0];
        _nextBtn.backgroundColor = Pink_Cor;
        _nextBtn.layer.cornerRadius = 20;
        _nextBtn.layer.masksToBounds = YES;
        _nextBtn.titleLabel.font = SYS_Font(16);
        [_nextBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        [_nextBtn addTarget:self action:@selector(clickNextBtn) forControlEvents:UIControlEventTouchUpInside];
    }
    return _nextBtn;
}
-(void)changeNextBtn:(BOOL)enabled {
    _nextBtn.userInteractionEnabled = enabled;
    if (enabled) {
        _nextBtn.alpha = 1;
    }else{
        _nextBtn.alpha = 0.5;
    }
}

-(void)clickNextBtn {
    YBDestroySureVC *sureVC = [[YBDestroySureVC alloc]init];
    [[XGGAppDelegate sharedAppDelegate]pushViewController:sureVC animated:YES];
}

#pragma mark -
-(void)pullData {
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Login.getCancelCondition" Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *canDestroy = minstr([infoDic valueForKey:@"can_cancel"]);
            if ([canDestroy isEqual:@"1"]) {
                [weakSelf changeNextBtn:YES];
            }
            _dataArray = [NSArray arrayWithArray:[infoDic valueForKey:@"list"]];
            [_tableView reloadData];
            
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
#pragma mark - UITableViewDelegate、UITableViewDataSource

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    
    return _dataArray.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBDestroyCell *cell = [YBDestroyCell cellWithTab:tableView index:indexPath];
    cell.dataDic = _dataArray[indexPath.row];
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
}

#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,_stateView.bottom, _window_width, _window_height - 64-statusbarHeight-_stateView.height-ShowDiff-40-60)style:UITableViewStylePlain];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = Normal_Color;
        _tableView.bounces = NO;
        _tableView.estimatedRowHeight = 100;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
        
    }
    return _tableView;
}

@end
