//
//  YBVideoReportVC.m
//  YBVideo
//
//  Created by YB007 on 2020/6/20.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBVideoReportVC.h"
#import "YBReportCell.h"
@interface YBVideoReportVC ()<UITableViewDelegate,UITableViewDataSource,UITextViewDelegate>{
    
    
    NSDictionary *selctDic;
    NSInteger selctCount;
    MyTextView *jubaoTextView;
    CGFloat textHeight;
    UILabel *placeLabel;
    UILabel *headerLabel;
    
    UIColor *bg_corlor;
    NSString *_inputDes;
}

@property(nonatomic,strong)NSMutableArray *dataArr;
@property(nonatomic,strong)UITableView *table;

@end

@implementation YBVideoReportVC

-(void)doReturn{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self requetData];
    
    [IQKeyboardManager sharedManager].enable = YES;
}
-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [IQKeyboardManager sharedManager].enable = NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    _inputDes = @"";
    self.titleL.text = YZMsg(@"举报");
    
    self.dataArr = [NSMutableArray array];
    
    bg_corlor = Normal_Color;
    
    _table = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-ShowDiff) style:UITableViewStyleGrouped];
    _table.delegate = self;
    _table.dataSource = self;
    _table.separatorStyle = UITableViewCellSeparatorStyleNone;
    _table.backgroundColor = bg_corlor;
    selctCount = 100000;
    textHeight = 0.0;
    self.view.backgroundColor = bg_corlor;
    [self.view addSubview:_table];
    

}
- (void)requetData{
    
    NSString *url= @"Video.getReportContentlist";
    
    YBWeakSelf;
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            weakSelf.dataArr = [NSMutableArray array];
            weakSelf.dataArr = [NSMutableArray arrayWithArray:info];
            [weakSelf.table reloadData];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    if (!headerLabel) {
        headerLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, _window_width, 40)];
        headerLabel.text = [NSString stringWithFormat:@"   %@",YZMsg(@"选择举报理由")];
        headerLabel.textColor = Pink_Cor;
        headerLabel.font = [UIFont systemFontOfSize:13];
        headerLabel.backgroundColor = CellRow_Cor;
    }
    return headerLabel;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 40;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 80+110)];
    view.backgroundColor = bg_corlor;

    jubaoTextView = [[MyTextView alloc]initWithFrame:CGRectMake(10, 30, _window_width-20,90)];
    jubaoTextView.delegate = self;
    jubaoTextView.layer.masksToBounds = YES;
    jubaoTextView.layer.cornerRadius = 5.0;
    jubaoTextView.font = SYS_Font(13);
    jubaoTextView.text = _inputDes;
    [view addSubview:jubaoTextView];
    
    jubaoTextView.backgroundColor = CellRow_Cor;
    
    jubaoTextView.textColor = RGB_COLOR(@"#969696", 1);
    jubaoTextView.placeholder = YZMsg(@"请填写举报理由");
    jubaoTextView.placeholderColor = RGB_COLOR(@"#969696", 1);
    
    UIButton *btn = [UIButton buttonWithType:0];
    btn.frame = CGRectMake(20, jubaoTextView.bottom+10, _window_width-40, 40);
    btn.layer.masksToBounds = YES;
    btn.layer.cornerRadius = 20.0;
    [btn setTitleColor:Pink_Cor forState:0];
    [btn setTitle:YZMsg(@"提交") forState:0];
    
    [btn addTarget:self action:@selector(dojubao) forControlEvents:UIControlEventTouchUpInside];
    [view addSubview:btn];
    
    //20-5-19 只有短视频并且选择了其他选项时才可以输入内容
    if ([minstr([selctDic valueForKey:@"id"]) isEqual:@"-1"]) {
        jubaoTextView.hidden = NO;
    }else{
        jubaoTextView.hidden = YES;
    }
    return view;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 80+110;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return _dataArr.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBReportCell *cell = [tableView dequeueReusableCellWithIdentifier:@"jubaoCell"];
    if (!cell) {
        cell = [[YBReportCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"jubaoCell"];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    cell.isVideo = YES;
    cell.leftLabel.text = [_dataArr[indexPath.row] valueForKey:@"name"];
    
    if (indexPath.row == selctCount) {
        cell.rightImage.image = [UIImage imageNamed:@"下一步"];
    }else{
        cell.rightImage.image = [UIImage new];
    }
    cell.leftLabel.textColor = RGB_COLOR(@"#969696", 1);
    cell.cellLineL.backgroundColor = Line_Cor;
    cell.backgroundColor = CellRow_Cor;
    cell.contentView.backgroundColor = CellRow_Cor;
    return cell;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self hideKeboard];
    selctDic = [NSDictionary dictionaryWithDictionary:_dataArr[indexPath.row]];
    selctCount = indexPath.row;
    _inputDes = jubaoTextView.text;
    [_table reloadData];
}
- (void)dojubao{
    [self hideKeboard];
    if (selctCount == 100000) {
        [MBProgressHUD showError:YZMsg(@"选择举报理由")];
        return;
    }
    NSString *content = [NSString stringWithFormat:@"%@",minstr([selctDic valueForKey:@"name"])];
    if ([minstr([selctDic valueForKey:@"id"]) isEqual:@"-1"]) {
        //其他
        content = [NSString stringWithFormat:@"%@ %@",minstr([selctDic valueForKey:@"name"]),jubaoTextView.text];
    }
    NSString *url = @"Video.report";
    NSDictionary *parameterDic = @{
        @"uid":[Config getOwnID],
        @"videoid":_dongtaiId,
        @"token":[Config getOwnToken],
        @"content":content,
    };
    
    [YBNetworking postWithUrl:url Dic:parameterDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            _inputDes = @"";
            [MBProgressHUD showError:YZMsg(@"举报成功")];
            [UIView animateWithDuration:0.5 animations:^{
                [self doReturn];
            }];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
    
}
- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if (![text isEqualToString:@""]) {
        //placeLabel.hidden = YES;
    }
    
    if ([text isEqualToString:@""] && range.location == 0 && range.length == 1) {
        //placeLabel.hidden = NO;
    }
    
    
    return YES;
}
- (void)hideKeboard{
    [jubaoTextView resignFirstResponder];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

@end
