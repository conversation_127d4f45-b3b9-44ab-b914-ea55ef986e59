//
//  BusinessCardVC.m
//  YBVideo
//
//  Created by ybRRR on 2021/2/25.
//  Copyright © 2021 cat. All rights reserved.
//

#import "BusinessCardVC.h"
#import "YBShareView.h"

@interface BusinessCardVC ()
{
    UIImageView *backImg;

    UIImageView *qrImgView;
    UILabel *nameLb;//昵称
    UILabel *idLb;  //id
    UILabel *codeLb;//邀请码
    
    NSString *hrefStr;
    UIImage *shareImage;
}
@end

@implementation BusinessCardVC
-(void)requestCodeData{
    NSDictionary *dic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken]};
    [YBNetworking postWithUrl:@"Agent.getCode" Dic:dic Suc:^(int code, id info, NSString *msg) {
        NSDictionary *infos = [info firstObject];
        codeLb.text =minstr([infos valueForKey:@"code"]);
        hrefStr = minstr([info valueForKey:@"href"]);
        [qrImgView sd_setImageWithURL:[NSURL URLWithString:minstr([infos valueForKey:@"qr"])]];
        } Fail:^(id fail) {
            
        }];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"我的名片");
    self.rightBtn.hidden = NO;
    [self.rightBtn setImage:[UIImage imageNamed:@"code_share"] forState:0];
    [self creatUI];
    [self requestCodeData];
}
-(void)clickNaviRightBtn{
    shareImage = [self getImage:backImg];

    //分享
    NSData *imageData = UIImagePNGRepresentation(shareImage);

    NSDictionary *shareDic = @{@"href":hrefStr,@"shareImage":imageData,@"shareTitle":YZMsg(@"扫描二维码，加我好友")};
    [YBShareView showShareWithType:RKShareType_card parameter:shareDic commplete:^(int codeEvent, NSString *nums) {

    }];

}
-(void)creatUI{
    backImg = [[UIImageView alloc]init];
    backImg.frame = CGRectMake(16, 64+statusbarHeight+20, _window_width-32, _window_height*0.6);
    backImg.image = [UIImage imageNamed:@"card_bg"];
    backImg.backgroundColor = Normal_BackColor;
    [self.view addSubview:backImg];
    
    qrImgView = [[UIImageView alloc]init];
    qrImgView.frame = CGRectMake((backImg.width-backImg.width *0.35)/2, backImg.height/2/2-(backImg.width *0.35/2), backImg.width *0.35, backImg.width *0.35);
    qrImgView.backgroundColor = [UIColor lightGrayColor];
    
    [backImg addSubview:qrImgView];
    
    UIImageView *lineImg = [[UIImageView alloc]init];
    lineImg.frame = CGRectMake(backImg.width *0.2, backImg.height *0.6, backImg.width *0.6, 5);
    lineImg.image = [UIImage imageNamed:@"card_line"];
    [backImg addSubview:lineImg];
    
    idLb = [[UILabel alloc]init];
    idLb.frame = CGRectMake(0, lineImg.top-30, backImg.width, 20);
    idLb.font = [UIFont systemFontOfSize:14];
    idLb.text = [NSString stringWithFormat:@"ID:%@",[Config getOwnID]];
    idLb.textColor = [UIColor blackColor];
    idLb.textAlignment = NSTextAlignmentCenter;
    [backImg addSubview:idLb];
    
    nameLb =[[UILabel alloc]init];
    nameLb.frame = CGRectMake(0, idLb.top-30, backImg.width, 20);
    nameLb.font = [UIFont boldSystemFontOfSize:16];
    nameLb.text = [Config getOwnNicename];
    nameLb.textColor = [UIColor blackColor];
    nameLb.textAlignment = NSTextAlignmentCenter;
    [backImg addSubview:nameLb];
    
    UILabel *codeTitle = [[UILabel alloc]init];
    codeTitle.frame = CGRectMake(0, lineImg.bottom+10, backImg.width, 20);
    codeTitle.font = [UIFont systemFontOfSize:14];
    codeTitle.text = YZMsg(@"邀请码");
    codeTitle.textColor = [UIColor blackColor];
    codeTitle.textAlignment = NSTextAlignmentCenter;
    [backImg addSubview:codeTitle];
    
    codeLb = [[UILabel alloc]init];
    codeLb.frame = CGRectMake(0, codeTitle.bottom+5, backImg.width, 20);
    codeLb.font = [UIFont boldSystemFontOfSize:16];
    codeLb.text = @"CRHDKSL";
    codeLb.textColor = [UIColor blackColor];
    codeLb.textAlignment = NSTextAlignmentCenter;
    [backImg addSubview:codeLb];
    
    UILabel *tips = [[UILabel alloc]init];
    tips.frame = CGRectMake(0, codeLb.bottom+10, backImg.width, 20);
    tips.font = [UIFont systemFontOfSize:14];
    tips.text = YZMsg(@"扫描二维码，加我好友");
    tips.textColor = [UIColor blackColor];
    tips.textAlignment = NSTextAlignmentCenter;
    [backImg addSubview:tips];
    

    UIView *iconView = [[UIView alloc]init];
    [backImg addSubview:iconView];
    [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(backImg.mas_centerX);
        make.top.equalTo(tips.mas_bottom).offset(20);
        make.height.mas_equalTo(26);
    }];
    UIImageView *iconImg = [[UIImageView alloc]init];
    iconImg.image = [PublicObj getAppIcon];
    iconImg.layer.cornerRadius = 5;
    iconImg.layer.masksToBounds = YES;
    [iconView addSubview:iconImg];
    [iconImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(25);
        make.left.equalTo(iconView.mas_left);
        make.top.equalTo(iconView.mas_top);
    }];

    UILabel *appName = [[UILabel alloc]init];
    appName.font = [UIFont boldSystemFontOfSize:16];
    appName.textColor = [UIColor blackColor];
    appName.text =[PublicObj getAppName];
    [iconView addSubview:appName];
    [appName mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconImg.mas_right).offset(5);
        make.centerY.equalTo(iconImg.mas_centerY);
        make.right.equalTo(iconView.mas_right);
    }];
    
    UIButton *saveBtn = [UIButton buttonWithType:0];
    saveBtn.frame = CGRectMake(_window_width/2-40, _window_height *0.85, 70, 70);
    [saveBtn setImage:[UIImage imageNamed:@"card_dowloadBtn"] forState:0];
    [saveBtn setTitle:YZMsg(@"保存到相册") forState:0];
    saveBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    saveBtn = [PublicObj setUpImgDownText:saveBtn];
    [saveBtn addTarget:self action:@selector(saveBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:saveBtn];
}
-(void)saveBtnClick{
    shareImage = [self getImage:backImg];
    [[PHPhotoLibrary sharedPhotoLibrary]performChanges:^{
        [PHAssetChangeRequest creationRequestForAssetFromImage:shareImage];
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        if (error) {
            NSLog(@"%@",@"保存失败");
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD showError:YZMsg(@"保存失败")];
            });
        } else {
            NSLog(@"%@",@"保存成功");
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD showError:YZMsg(@"保存成功")];
            });
        }
    }];
}
- (UIImage *)getImage:(UIView *)shareView
{
    
    UIGraphicsBeginImageContextWithOptions(CGSizeMake(shareView.frame.size.width,shareView.frame.size.height ), NO, 0.0); //currentView 当前的view  创建一个基于位图的图形上下文并指定大小为
    
     [shareView.layer renderInContext:UIGraphicsGetCurrentContext()];
     //      renderInContext呈现接受者及其子范围到指定的上下文
     UIImage *viewImage = UIGraphicsGetImageFromCurrentImageContext();//返回一个基于当前图形上下文的图片
    
     UIGraphicsEndImageContext();//移除栈顶的基于当前位图的图形上下文
    
//     UIImageWriteToSavedPhotosAlbum(viewImage, nil, nil, nil);//然后将该图片保存到图片图
    
     return viewImage;
    
}

@end
