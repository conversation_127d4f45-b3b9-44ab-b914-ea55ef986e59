
//
//  topicDetailsVC.m
//  YBVideo
//
//  Created by IOS1 on 2019/7/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import "topicDetailsVC.h"
#import "topicVideoCell.h"
#import "YBGetVideoObj.h"

@interface topicDetailsVC ()<UICollectionViewDelegate,UICollectionViewDataSource>{
    int page;
    NSMutableArray *infoArray;
}
@property (nonatomic,strong) UICollectionView *topicCollectView;

@end

@implementation topicDetailsVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = Normal_Color;
    infoArray = [NSMutableArray array];
    page = 1;
    
    [self requestData];
}
- (void)creatUI:(NSDictionary *)dic andNums:(NSString *)num{
    UIView *headerView = [[UIView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, 130)];
    headerView.backgroundColor = Normal_Color;
    [self.view addSubview:headerView];
    UIImageView *iconImgV = [[UIImageView alloc]initWithFrame:CGRectMake(20, 20, 90, 90)];
    iconImgV.layer.cornerRadius = 10;
    iconImgV.layer.masksToBounds = YES;
    [iconImgV sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"thumb"])]];
    [headerView addSubview:iconImgV];
    
    UIImageView *jingIngV = [[UIImageView alloc]initWithFrame:CGRectMake(iconImgV.right+15, iconImgV.top+1, 16, 16)];
    jingIngV.image = [UIImage imageNamed:@"video_topic"];
    [headerView addSubview:jingIngV];
    
    UILabel *nameL = [[UILabel alloc]initWithFrame:CGRectMake(jingIngV.right+5, iconImgV.top, headerView.width-(jingIngV.right+5)-15, 18)];
    nameL.text = minstr([dic valueForKey:@"name"]);
    nameL.textColor = [UIColor whiteColor];
    [headerView addSubview:nameL];
    
    UILabel *contentL = [[UILabel alloc]init];
    contentL.numberOfLines = 3;
    contentL.textColor = RGB_COLOR(@"#969696", 1);
    contentL.font = [UIFont systemFontOfSize:13];
    contentL.text = minstr([dic valueForKey:@"des"]);
    [headerView addSubview:contentL];
    [contentL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(jingIngV);
        make.centerY.equalTo(iconImgV);
        make.right.equalTo(headerView).offset(-15);
    }];
    UILabel *numL = [[UILabel alloc]initWithFrame:CGRectMake(jingIngV.left, iconImgV.bottom-13, nameL.width, 13)];
    numL.textColor = RGB_COLOR(@"#969696", 1);
    numL.font = [UIFont systemFontOfSize:10];
    numL.text = [NSString stringWithFormat:YZMsg(@"共%@个视频"),num];
    if ([lagType isEqual:EN]) {
        numL.text = [NSString stringWithFormat:@"Videos:%@",num];
    }
    [headerView addSubview:numL];
    
    [self.view addSubview:self.topicCollectView];
    
}
- (UICollectionView *)topicCollectView{
    if (!_topicCollectView) {
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionVertical;
        flow.itemSize = CGSizeMake((_window_width-2)/3, (_window_width-2)/3 * 1.33);
        flow.minimumLineSpacing = 1;
        flow.minimumInteritemSpacing = 1;
        _topicCollectView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight+130, _window_width, _window_height-(64+statusbarHeight+130)) collectionViewLayout:flow];
        [_topicCollectView registerNib:[UINib nibWithNibName:@"topicVideoCell" bundle:nil] forCellWithReuseIdentifier:@"topicVideoCELL"];
        _topicCollectView.delegate =self;
        _topicCollectView.dataSource = self;
        _topicCollectView.mj_footer  = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
            page ++;
            [self requestData];
        }];
        
        _topicCollectView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            page = 1;
            [self requestData];
            
        }];
        

    }
    return _topicCollectView;
}
- (void)requestData{
    
    
    [YBNetworking postWithUrl:@"Label.GetLabel" Dic:@{@"uid":[Config getOwnID],@"labelid":_labelID,@"p":@(page)} Suc:^(int code, id info, NSString *msg) {
        [_topicCollectView.mj_header endRefreshing];
        [_topicCollectView.mj_footer endRefreshing];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSDictionary *uiDic = [infoDic valueForKey:@"label"];
            [self creatUI:uiDic andNums:minstr([infoDic valueForKey:@"count"])];
            NSArray *list = [infoDic valueForKey:@"list"];
            if (page == 1) {
                [infoArray removeAllObjects];
            }
            [infoArray addObjectsFromArray:list];
        }
        [_topicCollectView reloadData];
    } Fail:^(id fail) {
        [_topicCollectView.mj_header endRefreshing];
        [_topicCollectView.mj_footer endRefreshing];
    }];

}
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return infoArray.count;
}

-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    topicVideoCell *cell = (topicVideoCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"topicVideoCELL" forIndexPath:indexPath];
    NSDictionary *subdic = infoArray[indexPath.row];
    cell.model = [[NearbyVideoModel alloc] initWithDic:subdic];

    return cell;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    
    NSString *vidoeID = minstr([infoArray[indexPath.row] valueForKey:@"id"]);
    [YBGetVideoObj lookManeger].fromWhere = @"topicDetailsVC";
    [YBGetVideoObj lookManeger].videoID = vidoeID;
    [YBGetVideoObj lookManeger].playIndex = (int)indexPath.row;
    [YBGetVideoObj lookManeger].videoList = [infoArray mutableCopy];
    [YBGetVideoObj lookManeger].paging = page;
    NSString *baseUrl = [NSString stringWithFormat:@"Label.GetLabel&labelid=%@",_labelID];
    [YBGetVideoObj lookManeger].baseUrl = baseUrl;
    [[YBGetVideoObj lookManeger]goLookVC];

}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
