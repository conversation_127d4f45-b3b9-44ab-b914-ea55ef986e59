//
//  JCHATMessageContentView.m
//  JChat
//
//  Created by HuminiOS on 15/11/2.
//  Copyright © 2015年 HXHG. All rights reserved.
//

#import "JCHATMessageContentView.h"
#import "JChatConstants.h"
#import <QMapKit/QMapKit.h>

static NSInteger const textMessageContentTopOffset = 10;
static NSInteger const textMessageContentRightOffset = 15;

@interface JCHATMessageContentView()
{
    QMapView *_mapView;
}
@property(nonatomic,strong)UIButton *loctionTapBtn;
@end

@implementation JCHATMessageContentView

- (id)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self != nil) {
        [self attachTapHandler];
        
    }
    return self;
}

- (id)init {
    self = [super init];
    if (self != nil) {
        _textContent = [UILabel new];
        _textContent.numberOfLines = 0;
        _textContent.textColor = [UIColor whiteColor];
        _textContent.backgroundColor = [UIColor clearColor];
        
        _infoL = [UILabel new];
        _infoL.numberOfLines = 0;
        _infoL.textColor = [UIColor whiteColor];
        _infoL.backgroundColor = [UIColor clearColor];
        _infoL.font = SYS_Font(13);
        
        _voiceConent = [UIImageView new];
        _isReceivedSide = NO;
        
        _mapIV = [[UIImageView alloc]init];
        [self addSubview:_mapIV];
        
        _imageViewAnntation = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 50, 50)];
        //NSString *b_path = [[NSBundle mainBundle]pathForResource:@"QMapKit.bundle" ofType:nil];
        //NSString *img_path = [b_path stringByAppendingPathComponent:@"images/greenPin_lift.png"];
        //_imageViewAnntation.image = [UIImage imageWithContentsOfFile:img_path];
        _imageViewAnntation.image = [UIImage imageNamed:@"location_current"];
        _imageViewAnntation.backgroundColor = [UIColor clearColor];
        [self addSubview:_imageViewAnntation];
        
        [self addSubview:_textContent];
        [self addSubview:_infoL];
        [self addSubview:_voiceConent];
        
        _mapView = [[QMapView alloc]init];
        _mapView.zoomEnabled = NO;
        [_mapView setShowsUserLocation:NO];
        [_mapView setShowsScale:NO];
        [_mapView setScrollEnabled:NO];
        
        _loctionTapBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _loctionTapBtn.backgroundColor = UIColor.clearColor;
        [_loctionTapBtn addTarget:self action:@selector(clickLocationTap) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:_loctionTapBtn];
        _loctionTapBtn.hidden = YES;
        [_loctionTapBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(self.mas_width).offset(-20);
            make.height.equalTo(self.mas_height).offset(10);
            make.centerX.centerY.equalTo(self);
        }];
    }
    return self;
}
-(void)clickLocationTap {
    if (_message.contentType == kJMSGContentTypeLocation) {
        JMSGLocationContent *locationContent = (JMSGLocationContent *)_message.content;
        NSData *mix_data = [locationContent.address dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary *mix_dic = [NSJSONSerialization JSONObjectWithData:mix_data options:NSJSONReadingAllowFragments error:nil];
        NSString *endName = YZMsg(@"终点位置");
        if ([mix_dic isKindOfClass:[NSDictionary class]]) {
            endName = [NSString stringWithFormat:@"%@",[mix_dic valueForKey:@"name"]];
        }
        NSLog(@"点击了定位=lng:%@==lat:%@",locationContent.longitude,locationContent.latitude);
        [[RKLBSManager shareManager]showNavigationsWithLat:locationContent.latitude lng:locationContent.longitude endName:endName];
    }else{
        NSLog(@"点击了非定位");
    }
}
- (void)setMessageContentWith:(JMSGMessage *)message{
    [self setMessageContentWith:message handler:nil];
}
- (void)setMessageContentWith:(JMSGMessage *)message handler:(void (^)(NSUInteger))block {
    BOOL isReceived = [message isReceived];
    _message = message;
    UIImageView *maskView = nil;
    UIImage *maskImage = nil;
    if (isReceived) {
        maskImage = [UIImage imageNamed:@"chat_bg_other"];
        //发送地理位置要求背景色为白色
        if (message.contentType == kJMSGContentTypeLocation) {
            maskImage = [UIImage imageNamed:@"chat_bg_location_other"];
        }
    } else {
        maskImage = [UIImage imageNamed:@"chat_bg_my"];
        if (message.contentType == kJMSGContentTypeLocation) {
            maskImage = [UIImage imageNamed:@"chat_bg_location_my"];
        }
    }
    //maskImage = [maskImage resizableImageWithCapInsets:UIEdgeInsetsMake(28, 20, 28, 20)];
    maskImage = [maskImage resizableImageWithCapInsets:UIEdgeInsetsMake(30, 20, 30, 20)];

//    [self setImage:maskImage];
    /*
    maskView = [UIImageView new];
    maskView.image = maskImage;
    [maskView setFrame:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
    self.layer.mask = maskView.layer;
    */
    self.contentMode = UIViewContentModeScaleToFill;
    
    _textContent.textAlignment = NSTextAlignmentLeft;
    _textContent.textColor = [UIColor whiteColor];
    self.backgroundColor = [UIColor clearColor];
    _loctionTapBtn.hidden = YES;
    switch (message.contentType) {
        case kJMSGContentTypeText:{
            [self setImage:maskImage];
            _voiceConent.hidden = YES;
            _textContent.hidden = NO;
            _infoL.hidden = YES;
            _mapIV.hidden = YES;
            _imageViewAnntation.hidden = YES;
            if (isReceived) {
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset + 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, self.frame.size.height- 2 * textMessageContentTopOffset)];
            } else {
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset - 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, self.frame.size.height- 2 * textMessageContentTopOffset)];
            }
            _textContent.text = ((JMSGTextContent *)message.content).text;
            
            //匹配表情文字
            NSString *pattern = emojiPattern;
            NSArray *resultArr =   [self machesWithPattern:pattern andStr:_textContent.text];
            if (!resultArr) return;
            NSUInteger lengthDetail = 0;
            NSMutableAttributedString *attstr = [[NSMutableAttributedString alloc]initWithString:_textContent.text];
            //遍历所有的result 取出range
            for (NSTextCheckingResult *result in resultArr) {
                //取出图片名
                NSString *imageName =   [_textContent.text substringWithRange:NSMakeRange(result.range.location, result.range.length)];
                
                if ([lagType isEqual:EN]) {
                    if ([imageName isEqual:@"[互粉]"] ||
                        [imageName isEqual:@"[关注]"] ||
                        [imageName isEqual:@"[粉]"] ||
                        [imageName isEqual:@"[给力]"]) {
                        imageName = [imageName stringByAppendingFormat:@"_en"];
                    }
                }
                
                NSLog(@"--------%@",imageName);
                NSTextAttachment *attach = [[NSTextAttachment alloc] init];
                UIImage *emojiImage = [UIImage imageNamed:imageName];
                NSAttributedString *imageString;
                if (emojiImage) {
                    attach.image = emojiImage;
                    attach.bounds = CGRectMake(0, -5, 21, 21);
                    imageString =   [NSAttributedString attributedStringWithAttachment:attach];
                }else{
                    imageString =   [[NSMutableAttributedString alloc]initWithString:imageName];
                }
                //图片附件的文本长度是1
                NSLog(@"emoj===%zd===size-w:%f==size-h:%f",imageString.length,imageString.size.width,imageString.size.height);
                NSUInteger length = attstr.length;
                NSRange newRange = NSMakeRange(result.range.location - lengthDetail, result.range.length);
                [attstr replaceCharactersInRange:newRange withAttributedString:imageString];

                lengthDetail += length - attstr.length;
            }
            //更新到label上
            _textContent.attributedText = attstr;
        }break;
            
        case kJMSGContentTypeImage: {
            _voiceConent.hidden = YES;
            _textContent.hidden = YES;
            _infoL.hidden = YES;
            _mapIV.hidden = YES;
            _imageViewAnntation.hidden = YES;
            self.contentMode = UIViewContentModeScaleAspectFill;
            [(JMSGImageContent *)message.content thumbImageData:^(NSData *data, NSString *objectId, NSError *error) {
                if (error == nil) {
                    if (data != nil) {
                        [self setImage:[UIImage imageWithData:data]];
                    } else {
                        [self setImage:[UIImage imageNamed:@"receiveFail"]];
                    }
                } else {
                    [self setImage:[UIImage imageNamed:@"receiveFail"]];
                }
                if (block) {
                    NSData *imageData = UIImagePNGRepresentation(self.image);
                    block(imageData.length);
                }
            }];
        }break;
        case kJMSGContentTypeVoice: {
            [self setImage:maskImage];
            _textContent.hidden = YES;
            _infoL.hidden = YES;
            _voiceConent.hidden = NO;
            _mapIV.hidden = YES;
            _imageViewAnntation.hidden = YES;
            if (isReceived) {//height = 50
                [_voiceConent setFrame:CGRectMake(20, 15, 20, 20)];
                [_voiceConent setImage:[UIImage imageNamed:@"ReceiverVoiceNodePlaying"]];
            } else {
                [_voiceConent setFrame:CGRectMake(self.frame.size.width - 40, 15, 20, 20)];
                [_voiceConent setImage:[UIImage imageNamed:@"SenderVoiceNodePlaying"]];
            }
            [((JMSGVoiceContent *)message.content) voiceData:^(NSData *data, NSString *objectId, NSError *error) {
                if (error == nil) {
                    if (block) {
                        block(data.length);
                    }
                } else {
                    DDLogDebug(@"get message voiceData fail with error %@",error);
                }
            }];
        }break;
        case kJMSGContentTypeFile: {
            _voiceConent.hidden = YES;
            _textContent.hidden = NO;
            _infoL.hidden = YES;
            _mapIV.hidden = YES;
            _imageViewAnntation.hidden = YES;
            self.contentMode = UIViewContentModeScaleAspectFit;
            [self setImage:[UIImage imageNamed:@"file_message_bg"]];
            
            JMSGFileContent *fileContent = (JMSGFileContent *)message.content;
            _textContent.text = fileContent.fileName;
            _textContent.textAlignment = NSTextAlignmentRight;
            if (isReceived) {
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset + 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, self.frame.size.height- 2 * textMessageContentTopOffset)];
            } else {
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset - 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, self.frame.size.height- 2 * textMessageContentTopOffset)];
            }
        }break;
        case kJMSGContentTypeLocation: {
            _loctionTapBtn.hidden = NO;
            [self setImage:maskImage];
            _voiceConent.hidden = YES;
            _textContent.hidden = NO;
            _infoL.hidden = NO;
            _mapIV.hidden = NO;
            _imageViewAnntation.hidden = NO;
            self.contentMode = UIViewContentModeScaleAspectFit;
            //[self setImage:[UIImage imageNamed:@"location_address"]];
            
            JMSGLocationContent *locationContent = (JMSGLocationContent *)message.content;
            
            NSData *mix_data = [locationContent.address dataUsingEncoding:NSUTF8StringEncoding];
            NSDictionary *mix_dic = [NSJSONSerialization JSONObjectWithData:mix_data options:NSJSONReadingAllowFragments error:nil];
            if ([mix_dic isKindOfClass:[NSDictionary class]]) {
                _textContent.text = [NSString stringWithFormat:@"%@",[mix_dic valueForKey:@"name"]];
                _infoL.text = [NSString stringWithFormat:@"%@",[mix_dic valueForKey:@"info"]];
            }else {
                _textContent.text = locationContent.address;
            }
            _textContent.textColor  = RGB_COLOR(@"#323232", 1);
            _infoL.textColor = RGB_COLOR(@"#646464", 1);
            
            if (isReceived) {
                self.backgroundColor = RGB_COLOR(@"#ffffff", 1);
                
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset + 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, 20)];
                [_infoL setFrame:CGRectMake(textMessageContentRightOffset + 5, _textContent.bottom, self.frame.size.width - 2 * textMessageContentRightOffset, 15)];
            } else {
                self.backgroundColor = RGB_COLOR(@"#ffffff", 1);
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset - 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, 20)];
                [_infoL setFrame:CGRectMake(textMessageContentRightOffset - 5, _textContent.bottom, self.frame.size.width - 2 * textMessageContentRightOffset, 15)];
            }
            [_mapIV setFrame:CGRectMake(0, _infoL.bottom, self.width, self.height-textMessageContentTopOffset-35)];
            /*
            [_mapView setFrame:CGRectMake(0, _infoL.bottom, self.width, self.height-textMessageContentTopOffset-35)];
            CLLocationCoordinate2D center;
            center.latitude = [locationContent.latitude doubleValue];
            center.longitude = [locationContent.longitude doubleValue];
            [_mapView setCenterCoordinate:center zoomLevel:16.01 animated:NO];
            [_mapView takeSnapshotInRect:_mapView.bounds withCompletionBlock:^(UIImage *resultImage, CGRect rect) {
                [_mapIV setImage:resultImage];
            }];
            */
            NSString *url = @"https://apis.map.qq.com/ws/staticmap/v2/";
            NSDictionary *postDic = @{@"key":TencentKey,
                                      @"scale":@"1",
                                      @"size":[NSString stringWithFormat:@"%.f*%.f",_mapIV.width,_mapIV.height],
                                      @"center":[NSString stringWithFormat:@"%@,%@",locationContent.latitude,locationContent.longitude],
                                      @"zoom":@"16"};
            url = [url stringByAppendingFormat:@"?%@",[YBNetworking getStrWithDic:postDic]];
            [_mapIV sd_setImageWithURL:[NSURL URLWithString:url]];
            _imageViewAnntation.center = _mapIV.center;
            
        }break;
        case kJMSGContentTypeUnknown:
            [self setImage:maskImage];
            _voiceConent.hidden = YES;
            _textContent.hidden = NO;
            _mapIV.hidden = YES;
            _imageViewAnntation.hidden = YES;
            if (isReceived) {
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset + 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, self.frame.size.height- 2 * textMessageContentTopOffset)];
            } else {
                [_textContent setFrame:CGRectMake(textMessageContentRightOffset - 5, textMessageContentTopOffset, self.frame.size.width - 2 * textMessageContentRightOffset, self.frame.size.height- 2 * textMessageContentTopOffset)];
            }
            _textContent.text = st_receiveUnknowMessageDes;
            break;
        case kJMSGContentTypeCustom:
//            [self setImage:maskImage];
//            _voiceConent.hidden = YES;
//            _textContent.hidden = NO;
//            _infoL.hidden = YES;
//            _mapIV.hidden = YES;
//            _imageViewAnntation.hidden = YES;
//            [_textContent setFrame:CGRectMake(0, textMessageContentTopOffset, self.frame.size.width , self.frame.size.height- 2 * textMessageContentTopOffset)];
//            _textContent.text = @"12123123123213123";
            break;
        default:
            break;
    }
    
}

- (NSArray <NSTextCheckingResult *> *)machesWithPattern:(NSString *)pattern  andStr:(NSString *)str
{
    NSError *error = nil;
    NSRegularExpression *expression = [NSRegularExpression regularExpressionWithPattern:pattern options:0 error:&error];
    if (error)
    {
        NSLog(@"正则表达式创建失败");
        return nil;
    }
    return [expression matchesInString:str options:0 range:NSMakeRange(0, str.length)];
}


- (BOOL)canBecomeFirstResponder{
    return YES;
}

-(void)attachTapHandler{
    self.userInteractionEnabled = YES;  //用户交互的总开关
    UILongPressGestureRecognizer *touch = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(handleTap:)];
    touch.minimumPressDuration = 0.5;
    [self addGestureRecognizer:touch];
}
//rk20201030
- (void)menuViewDidHide:(NSNotification*)notification {
    //重置，不影响原有的响应链
    self.passRKTextView.overrideNextResponder = nil;
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIMenuControllerDidHideMenuNotification object:nil];
}
#pragma mark - 消息撤回
-(void)handleTap:(UIGestureRecognizer*) recognizer {
    if (recognizer.state != UIGestureRecognizerStateBegan) {
        return;
    }
//    [self becomeFirstResponder];
    //rk20201030 - UIMenuController和键盘并存
    if(self.passRKTextView.isFirstResponder){
        self.passRKTextView.overrideNextResponder = self;
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(menuViewDidHide:) name:UIMenuControllerDidHideMenuNotification object:nil];
    }else {
        [self becomeFirstResponder];
    }
    BOOL isReceived = [_message isReceived];
    
    UIMenuController *menuC = [UIMenuController sharedMenuController];
    UIMenuItem *copyItem = [[UIMenuItem alloc]initWithTitle:YZMsg(@"复制") action:@selector(clickCopyItem)];
    UIMenuItem *retractItem = [[UIMenuItem alloc]initWithTitle:YZMsg(@"撤回") action:@selector(clickRetractItem)];
    NSMutableArray *addItems = [NSMutableArray array];
    if (_message.contentType == kJMSGContentTypeText) {
        [addItems addObject:copyItem];
    }
    if (!isReceived) {
        //自己的消息
        [addItems addObject:retractItem];
    }
    if (addItems.count>0) {
        [menuC setMenuItems:[NSArray arrayWithArray:addItems]];
        [menuC setTargetRect:self.frame inView:self.superview];
        [menuC setMenuVisible:YES animated:YES];
    }
}
-(void)clickCopyItem {
    __block UIPasteboard *pboard = [UIPasteboard generalPasteboard];
    switch (_message.contentType) {
        case kJMSGContentTypeText:{
            JMSGTextContent *textContent = (JMSGTextContent *)_message.content;
            pboard.string = textContent.text;
            [MBProgressHUD showPop:YZMsg(@"复制成功")];
        }break;
        default:
            break;
    }
}
-(void)clickRetractItem {
    NSTimeInterval time = [[NSDate date] timeIntervalSince1970];
    long long int currentTime = (long long int)time;
    long long msgTime = [_message.timestamp longLongValue]/1000;
    //NSLog(@"====:%@======u:%lld=====n:%lld",_message.timestamp,currentTime,msgTime);
    if (currentTime > (msgTime+2*60)) {
        [MBProgressHUD showPop:YZMsg(@"该消息发送时间已超过两分钟,无法撤回")];
    }else {
        [[NSNotificationCenter defaultCenter] postNotificationName:kRetractMsg object:_message];
    }
}

/*
-(void)handleTap:(UIGestureRecognizer*) recognizer {
    [self becomeFirstResponder];
    [[UIMenuController sharedMenuController] setTargetRect:self.frame inView:self.superview];
    [[UIMenuController sharedMenuController] setMenuVisible:YES animated: YES];
}
#pragma mark - 长按选项
-(BOOL)canPerformAction:(SEL)action withSender:(id)sender {
    if (_message.contentType == kJMSGContentTypeVoice) {
        return action == @selector(delete:);
    }
    return (action == @selector(copy:) || action == @selector(delete:));
    return NO;
}

-(void)copy:(id)sender {
    __block UIPasteboard *pboard = [UIPasteboard generalPasteboard];
    switch (_message.contentType) {
        case kJMSGContentTypeText:
        {
            JMSGTextContent *textContent = (JMSGTextContent *)_message.content;
            pboard.string = textContent.text;
        }
            break;
            
        case kJMSGContentTypeImage:
        {
            JMSGImageContent *imgContent = (JMSGImageContent *)_message.content;
            [imgContent thumbImageData:^(NSData *data, NSString *objectId, NSError *error) {
                if (data == nil || error) {
                    UIWindow *myWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
                    //[MBProgressHUD showMessage:@"获取图片错误" view:myWindow];
                    [MBProgressHUD showMessage:YZMsg(@"获取图片错误") toView:myWindow];
                    return ;
                }
                pboard.image = [UIImage imageWithData:data];
            }];
        }
            break;
            
        case kJMSGContentTypeVoice:
            break;
        case kJMSGContentTypeUnknown:
            break;
        default:
            break;
    }
    
}

-(void)delete:(id)sender {
    [[NSNotificationCenter defaultCenter] postNotificationName:kDeleteMessage object:_message];
}
 */
@end
