//
//  JCHATSendMessageViewController.m
//  JPush IM
//
//  Created by Apple on 14/12/26.
//  Copyright (c) 2014年 Apple. All rights reserved.
//

#import "JCHATConversationViewController.h"
#import "MJPhoto.h"
#import "MJPhotoBrowser.h"
#import "JCHATFileManager.h"
#import "JCHATShowTimeCell.h"

#import "AppDelegate.h"
#import "UIImage+ResizeMagick.h"

#import <MobileCoreServices/UTCoreTypes.h>
#import <JMessage/JMSGConversation.h>
#import "JCHATStringUtils.h"

#import <UIKit/UIPrintInfo.h>
#import "JCHATLoadMessageTableViewCell.h"
#import "JCHATSendMsgManager.h"

#import <AssetsLibrary/AssetsLibrary.h>

#import <Photos/Photos.h>

#import "TencentLocationVC.h"

#import "twEmojiView.h"
#import "ASRView.h"
#import "PubH5.h"
#import "YBCenterVC.h"
#import <TZImagePickerController/TZImagePickerController.h>

@interface JCHATConversationViewController ()<twEmojiViewDelegate,TouchMsgTabDelegate,TZImagePickerControllerDelegate,UIGestureRecognizerDelegate> {
    
@private
    BOOL isNoOtherMessage;
    NSInteger messageOffset;
    NSMutableArray *_imgDataArr;
    //    JMSGConversation *_conversation;
    NSMutableDictionary *_allMessageDic;    //缓存所有的message model
    NSMutableArray *_allmessageIdArr;       //按序缓存后有的messageId， 于allMessage 一起使用
    NSMutableArray *_userArr;
    UIButton *_rightBtn;
    NSMutableDictionary *_refreshAvatarUsersDic;
    
    twEmojiView *_emojiV;
    ASRView *_asrView;
    BOOL _asrMoveUp;
    
    UIView *followView;
    
    //图片多选
    BOOL _sendImgLimit;
    int _didSendCount;
    NSArray *_didSelImgA;
    
    NSString *isfirstAttent;
}

@end

@implementation JCHATConversationViewController


- (void)viewWillAppear:(BOOL)animated {
    DDLogDebug(@"Event - viewWillAppear");
    [super viewWillAppear:animated];
    [IQKeyboardManager sharedManager].enable = NO;
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
    
    [self.toolBarContainer.toolbar drawRect:self.toolBarContainer.toolbar.frame];
    
    kWEAKSELF
    [_conversation refreshTargetInfoFromServer:^(id resultObject, NSError *error) {
        DDLogDebug(@"refresh nav right button");
        kSTRONGSELF
        //[strongSelf.navigationController setNavigationBarHidden:NO];
        // 禁用 iOS7 返回手势
        if ([strongSelf.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
            strongSelf.navigationController.interactivePopGestureRecognizer.enabled = YES;
        }
        
        if (strongSelf.conversation.conversationType == kJMSGConversationTypeGroup) {
            //[strongSelf updateGroupConversationTittle:nil];
        } else {
            strongSelf.title = [resultObject title];
        }
        [_messageTableView reloadData];
    }];
    
}

- (void)viewWillDisappear:(BOOL)animated {
    DDLogDebug(@"Event - viewWillDisappear");
    [super viewWillDisappear:animated];
    
    [IQKeyboardManager sharedManager].enable = YES;
    
    [_conversation clearUnreadCount];
    [[JCHATAudioPlayerHelper shareInstance] stopAudio];
    [[JCHATAudioPlayerHelper shareInstance] setDelegate:nil];
}


- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBar.hidden = YES;
    self.automaticallyAdjustsScrollViewInsets = NO;
    
    _asrMoveUp = NO;
    _refreshAvatarUsersDic = [NSMutableDictionary dictionary];
    _allMessageDic = [NSMutableDictionary dictionary];
    _allmessageIdArr = [NSMutableArray array];
    _imgDataArr = [NSMutableArray array];
    DDLogDebug(@"Action - viewDidLoad");
    
    self.rightBtn.hidden = NO;
    [self.rightBtn setImage:[UIImage imageNamed:@"center_more"] forState:0];
    self.titleL.text = _userModel.unameStr;
    
    self.messageTabTop.constant = 64+statusbarHeight;
    self.messageTableView.touchDelegate = self;
    if (@available(iOS 11.0,*)) {
        self.messageTableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    self.titleL.text = _userModel.unameStr;
    
    [self setupView];
    [self addNotification];
    [self addDelegate];
    [self getGroupMemberListWithGetMessageFlag:YES];
    
    if (![_userModel.isAtt isEqual:@"1"]) {
        [self creatFollowView];
    }
}
- (void)creatFollowView{
    followView = [[UIView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, 40)];
    followView.backgroundColor = _isWhiteStyle?[UIColor whiteColor]:CellRow_Cor;
    [self.view addSubview:followView];
    UIButton *fCloseBtn = [UIButton buttonWithType:0];
    fCloseBtn.frame = CGRectMake(5, 5, 30, 30);
    [fCloseBtn setImage:[UIImage imageNamed:@"提示关闭"] forState:0];
    fCloseBtn.imageEdgeInsets = UIEdgeInsetsMake(8, 8, 8, 8);
    [fCloseBtn addTarget:self action:@selector(removeFollowView) forControlEvents:UIControlEventTouchUpInside];
    [followView addSubview:fCloseBtn];
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(fCloseBtn.right, 0, _window_width/2, 40)];
    label.font = [UIFont systemFontOfSize:12];
    label.text = YZMsg(@"点击关注，可及时看到对方动态");
    label.textColor = RGB_COLOR(@"#828282", 1);
    [followView addSubview:label];
    UIButton *followBtn = [UIButton buttonWithType:0];
    followBtn.frame = CGRectMake(_window_width-54, 10, 44, 20);
    [followBtn setTitle:YZMsg(@"关注") forState:0];
    followBtn.layer.cornerRadius = 10;
    followBtn.layer.masksToBounds = YES;
    followBtn.titleLabel.font = SYS_Font(12);
    followBtn.layer.borderColor = Pink_Cor.CGColor;
    followBtn.layer.borderWidth = 1;
    [followBtn setTitleColor:Pink_Cor forState:0];
    [followBtn addTarget:self action:@selector(doFollow) forControlEvents:UIControlEventTouchUpInside];
    [followView addSubview:followBtn];
    // 闪出关注的距离
    //_messageTableView.contentInset = UIEdgeInsetsMake(40, 0, 0, 0);
}
- (void)removeFollowView{
    [followView removeFromSuperview];
    followView = nil;
    
    //_messageTableView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
}
- (void)doFollow{

    YBWeakSelf;
    NSDictionary *dic = @{
        @"uid":[Config getOwnID],
        @"touid":_userModel.uidStr
    };

    [YBNetworking postWithUrl:@"User.isFirstAttent" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infos = [info firstObject];
            //is_first   1 第一次关注   0 不是第一次关注
            isfirstAttent = minstr([infos valueForKey:@"is_first"]);
            [weakSelf setAttent];

            }
        } Fail:^(id fail) {

    }];
}
-(void)setAttent{
    NSString *postUrl = @"User.setAttent";
    NSDictionary *postDic = @{
        @"touid":_userModel.uidStr
    };
    [YBNetworking postWithUrl:postUrl Dic:postDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [MBProgressHUD showError:YZMsg(@"关注对方成功")];
            if (self.block) {
                self.block(1);
            }
            [self removeFollowView];
            if ([isfirstAttent isEqual:@"1"]) {
                //发送自定义消息
                
                NSDictionary *infoDic = @{@"uid":[Config getOwnID],@"touid":_userModel.uidStr,@"type":@"follow"};
                JMSGMessage *message = nil;
                JMSGCustomContent *customFormat = [[JMSGCustomContent alloc]initWithCustomDictionary:infoDic];
                message = [_conversation createMessageWithContent:customFormat];
                
                JMSGOptionalContent *option = [[JMSGOptionalContent alloc] init];
                option.noSaveNotification = YES;
                [_conversation sendMessage:message optionalContent:option];
                
                [self addmessageShowTimeData:message.timestamp];
                JCHATChatModel *model =[[JCHATChatModel alloc] init];
                [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
                [self addMessage:model];

            }
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];

}

- (void)updateGroupConversationTittle:(JMSGGroup *)newGroup {
    JMSGGroup *group;
    if (newGroup == nil) {
        group = self.conversation.target;
    } else {
        group = newGroup;
    }
    
    if ([group.name isEqualToString:@""]) {
        self.title = @"群聊";
    } else {
        self.title = group.name;
    }
    self.title = [NSString stringWithFormat:@"%@(%lu)",self.title,(unsigned long)[group.memberArray count]];
    [self getGroupMemberListWithGetMessageFlag:NO];
    if (self.isConversationChange) {
        [self cleanMessageCache];
        [self getPageMessage];
        self.isConversationChange = NO;
    }
}

- (void)viewDidLayoutSubviews {
    DDLogDebug(@"Event - viewDidLayoutSubviews");
    [self scrollToBottomAnimated:NO];
}


#pragma mark --释放内存
- (void)dealloc {
    DDLogDebug(@"Action -- dealloc");
    //  [[NSNotificationCenter defaultCenter] removeObserver:self name:kAlertToSendImage object:self];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self.toolBarContainer.toolbar.textView removeObserver:self forKeyPath:@"contentSize"];
    //remove delegate
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kAlertToSendImage object:self];
    [JMessage removeDelegate:self withConversation:_conversation];
}

- (void)addtoolbar {
    self.toolBarContainer.toolbar.frame = CGRectMake(0, 0, kApplicationWidth, 45);
    [self.toolBarContainer addSubview:self.toolBarContainer.toolbar];
}
#pragma mark - 子控件
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch;{
    if ([touch.view isDescendantOfView:_emojiV] ||
        [touch.view isDescendantOfView:_toolBarContainer] ||
        [touch.view isDescendantOfView:_moreViewContainer]) {
        return NO;
    }
    return YES;
}
- (void)setupView {
    
    [_conversation clearUnreadCount];
    
    UITapGestureRecognizer *gesture =[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapClick:)];
    gesture.delegate = self;
    [self.view addGestureRecognizer:gesture];
    
    [self.view setBackgroundColor:RGB_COLOR(@"#201c36", 1)];
    _toolBarToBottomConstrait.constant = 0+ShowDiff;
    _toolBarContainer.toolbar.delegate = self;
    [_toolBarContainer.toolbar setUserInteractionEnabled:YES];
    self.toolBarContainer.toolbar.textView.text = [[JCHATSendMsgManager ins] draftStringWithConversation:_conversation];
    _messageTableView.userInteractionEnabled = YES;
    _messageTableView.showsVerticalScrollIndicator = NO;
    _messageTableView.delegate = self;
    _messageTableView.dataSource = self;
    _messageTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _messageTableView.backgroundColor = _isWhiteStyle?RGB_COLOR(@"#f4f5f6", 1):Normal_Color;
    
    _moreViewContainer.moreView.delegate = self;
    _moreViewContainer.moreView.backgroundColor = _isWhiteStyle?[UIColor whiteColor]:Normal_Color;
    if (_isWhiteStyle) {
        self.view.backgroundColor = [UIColor whiteColor];
        _toolBarContainer.toolbar.backgroundColor = [UIColor whiteColor];
        _toolBarContainer.toolbar.textView.backgroundColor = RGB_COLOR(@"#f4f5f6", 1);
    }
    //表情
    _emojiV = [[twEmojiView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, EmojiHeight+ShowDiff)];
    _emojiV.delegate = self;
    [self.view addSubview:_emojiV];
}

- (void)getGroupMemberListWithGetMessageFlag:(BOOL)getMesageFlag {
    if (self.conversation && self.conversation.conversationType == kJMSGConversationTypeGroup) {
        JMSGGroup *group = nil;
        group = self.conversation.target;
        _userArr = [NSMutableArray arrayWithArray:[group memberArray]];
        [self isContantMeWithUserArr:_userArr];
        if (getMesageFlag) {
            [self getPageMessage];
        }
    } else {
        if (getMesageFlag) {
            [self getPageMessage];
        }
        [self hidenDetailBtn:NO];
    }
}

- (void)isContantMeWithUserArr:(NSMutableArray *)userArr {
    BOOL hideFlag = YES;
    for (NSInteger i =0; i< [userArr count]; i++) {
        JMSGUser *user = [userArr objectAtIndex:i];
        if ([user.username isEqualToString:[JMSGUser myInfo].username]) {
            hideFlag = NO;
            break;
        }
    }
    if (!hideFlag) {
        [self reloadAllCellAvatarImage];
    }
    [self hidenDetailBtn:hideFlag];
}

- (void)hidenDetailBtn:(BOOL)flag {
    [_rightBtn setHidden:flag];
}

- (void)setTitleWithUser:(JMSGUser *)user {
    self.title = _conversation.title;
}

#pragma mark --JMessageDelegate
- (void)onSendMessageResponse:(JMSGMessage *)message error:(NSError *)error {
    DDLogDebug(@"Event - sendMessageResponse");
    
    if (message != nil) {
        NSLog(@"发送的 Message:  %@",message);
    }
    [self relayoutTableCellWithMessage:message];
    
    if (error != nil) {
        DDLogDebug(@"Send response error - %@", error);
        [_conversation clearUnreadCount];
        NSString *alert = [JCHATStringUtils errorAlert:error];
        if (alert == nil) {
            alert = [error description];
        }
        [MBProgressHUD hideHUDForView:self.view animated:YES];
        //[MBProgressHUD showMessage:alert view:self.view];
        //[MBProgressHUD showMessage:alert toView:self.view];
        [MBProgressHUD showPop:alert];
        return;
    }
    
    JCHATChatModel *model = _allMessageDic[message.msgId];
    if (!model) {
        return;
    }
}


#pragma mark - 消息撤回
//撤回消息-重置声音播放和图片放大
-(void)retractResetVoiceOrImg {
    if ([[JCHATAudioPlayerHelper shareInstance] isPlaying]) {
        [[JCHATAudioPlayerHelper shareInstance] stopAudio];
    }
    if ([[[XGGAppDelegate sharedAppDelegate] topViewController] isKindOfClass:[MJPhotoBrowser class]]) {
        MJPhotoBrowser *pBrowser = (MJPhotoBrowser *)[[XGGAppDelegate sharedAppDelegate] topViewController];
        [pBrowser retractMsgDismissBrowser];
    };
    
}
- (void)onReceiveMessageRetractEvent:(JMSGMessageRetractEvent *)retractEvent;{
    NSLog(@"----:%@-%@",retractEvent,retractEvent.retractMessage);
    [self retractResetVoiceOrImg];
    [self retractMsgReplace:retractEvent.retractMessage];
}
-(void)retractMsgReplace:(JMSGMessage *)jMessage {
    NSString *retractMsgId = jMessage.msgId;
    JCHATChatModel *model = [_allMessageDic objectForKey:retractMsgId];
    [model setChatModelWith:jMessage conversationType:_conversation userModel:_userModel];
    
    [_allMessageDic removeObjectForKey:retractMsgId];
    [_allMessageDic setObject:model forKey:model.message.msgId];
    
    //如果撤回的是图片图片数组要处理一下
    NSMutableArray *need_del_array = [NSMutableArray array];
    for (JCHATChatModel *imgModel in _imgDataArr) {
        if ([imgModel.message.msgId isEqual:model.message.msgId]) {
            [need_del_array addObject:imgModel];
        }
    }
    [_imgDataArr removeObjectsInArray:need_del_array];
    
    [_messageTableView reloadData];
    [self scrollToBottomAnimated:NO];
}
#pragma mark - 收到消息
- (void)onReceiveMessage:(JMSGMessage *)message error:(NSError *)error {
    
    if (message != nil) {
    }
    if (error != nil) {
        JCHATChatModel *model = [[JCHATChatModel alloc] init];
        [model setErrorMessageChatModelWithError:error];
        [self addMessage:model];
        return;
    }
    
    if (![self.conversation isMessageForThisConversation:message]) {
        return;
    }
    
    //ray-----
//    if (message.contentType == kJMSGContentTypeCustom) {
//        return;
//    }
    DDLogDebug(@"Event - receiveMessageNotification");
    
    kWEAKSELF
    JCHATMAINTHREAD((^{
        kSTRONGSELF
        if (!message) {
            DDLogWarn(@"get the nil message .");
            return;
        }
        
        //if (_allMessageDic[message.msgId] != nil) {
        //  DDLogDebug(@"该条消息已加载");
        //  return;
        //}
        
        if (message.contentType == kJMSGContentTypeEventNotification) {
            if (((JMSGEventContent *)message.content).eventType == kJMSGEventNotificationRemoveGroupMembers
                && ![((JMSGGroup *)_conversation.target) isMyselfGroupMember]) {
                //            [strongSelf setupNavigation];
            }
        }
        
        if (_conversation.conversationType == kJMSGConversationTypeSingle) {
        } else if (![((JMSGGroup *)_conversation.target).gid isEqualToString:((JMSGGroup *)message.target).gid]){
            return;
        }
        
        JCHATChatModel *model = [_allMessageDic objectForKey:message.msgId];
        if (model) {// 说明已经加载，说明可能是同步下来的多媒体消息，下载完成，然后再次收到就去刷新
            model.message = message;
            [strongSelf refreshCellMessageMediaWithChatModel:model];
        }else{
            
            NSString *firstMsgId = [_allmessageIdArr firstObject];
            JCHATChatModel *firstModel = [_allMessageDic objectForKey:firstMsgId];
            if (message.timestamp < firstModel.message.timestamp) {
                // 比数组中最老的消息时间都小的，无需加入界面显示，下次翻页时会加载
                return ;
            }
            
            model = [[JCHATChatModel alloc] init];
            [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
            if (message.contentType == kJMSGContentTypeImage) {
                [_imgDataArr addObject:model];
            }
            model.photoIndex = [_imgDataArr count] -1;
            [strongSelf addmessageShowTimeData:message.timestamp];
            [strongSelf addMessage:model];
            
            BOOL isHaveCache = NO;
            NSString *key = [NSString stringWithFormat:@"%@_%@",message.fromUser.username,message.fromUser.appKey];
            NSMutableArray *messages = _refreshAvatarUsersDic[key];
            if (messages) {
                isHaveCache = YES;
                [messages addObject:message];
            }else{
                messages = [NSMutableArray array];
                [messages addObject:message];
            }
            if (messages.count > 10) {
                [messages removeObjectAtIndex:0];
            }
            [_refreshAvatarUsersDic setObject:messages forKey:key];
            
            [strongSelf chcekReceiveMessageAvatarWithReceiveNewMessage:message];
            //if (!isHaveCache) {
            //  [strongSelf performSelector:@selector(chcekReceiveMessageAvatarWithReceiveNewMessage:) withObject:message afterDelay:1.5];
            //}
        }
    }));
}

- (void)onReceiveMessageDownloadFailed:(JMSGMessage *)message {
    if (![self.conversation isMessageForThisConversation:message]) {
        return;
    }
    
    DDLogDebug(@"Event - receiveMessageNotification");
    JCHATMAINTHREAD((^{
        if (!message) {
            DDLogWarn(@"get the nil message .");
            return;
        }
        
        if (_conversation.conversationType == kJMSGConversationTypeSingle) {
        } else if (![((JMSGGroup *)_conversation.target).gid isEqualToString:((JMSGGroup *)message.target).gid]){
            return;
        }
        
        JCHATChatModel *model = [_allMessageDic objectForKey:message.msgId];
        if (model) {// 说明已经加载，说明可能是同步下来的多媒体消息，下载完成，然后再次收到就去刷新
            model.message = message;
            [self refreshCellMessageMediaWithChatModel:model];
        }else{
            model = [[JCHATChatModel alloc] init];
            [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
            if (message.contentType == kJMSGContentTypeImage) {
                [_imgDataArr addObject:model];
            }
            model.photoIndex = [_imgDataArr count] -1;
            [self addmessageShowTimeData:message.timestamp];
            [self addMessage:model];
        }
        
    }));
}
//同步离线消息
- (void)onSyncOfflineMessageConversation:(JMSGConversation *)conversation
                         offlineMessages:(NSArray<__kindof JMSGMessage *> *)offlineMessages {
    DDLogDebug(@"Action -- onSyncOfflineMessageConversation:offlineMessages:");
    
    if (conversation.conversationType != self.conversation.conversationType) {
        return ;
    }
    BOOL isThisConversation = NO;
    if (conversation.conversationType == kJMSGConversationTypeSingle) {
        JMSGUser *user1 = (JMSGUser *)conversation.target;
        JMSGUser *user2 = (JMSGUser *)self.conversation.target;
        if ([user1.username isEqualToString:user2.username] &&
            [user1.appKey isEqualToString:user2.appKey]) {
            isThisConversation = YES;
        }
    }else{
        JMSGGroup *group1 = (JMSGGroup *)conversation.target;
        JMSGGroup *group2 = (JMSGGroup *)conversation.target;
        if ([group1.gid isEqualToString:group2.gid]) {
            isThisConversation = YES;
        }
    }
    
    if (!isThisConversation) {
        return ;
    }
    
    NSMutableArray *pathsArray = [NSMutableArray array];
    NSMutableArray *allSyncMessages = [NSMutableArray arrayWithArray:offlineMessages];
    for (int i = 0; i< allSyncMessages.count; i++) {
        JMSGMessage *message = allSyncMessages[i];
        JCHATChatModel *model = [[JCHATChatModel alloc] init];
        [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
        if (message.contentType == kJMSGContentTypeImage) {
            [_imgDataArr addObject:model];
        }
        model.photoIndex = [_imgDataArr count] -1;
        
        [_allMessageDic setObject:model forKey:model.message.msgId];
        [_allmessageIdArr addObject:model.message.msgId];
        
        NSIndexPath *path = [NSIndexPath indexPathForRow:[_allmessageIdArr count]-1 inSection:0];
        [pathsArray addObject:path];
    }
    if (pathsArray.count) {
        [_messageTableView beginUpdates];
        [_messageTableView insertRowsAtIndexPaths:pathsArray withRowAnimation:UITableViewRowAnimationNone];
        [_messageTableView endUpdates];
        [self scrollToEnd];
    }
}
//同步漫游消息
- (void)onSyncRoamingMessageConversation:(JMSGConversation *)conversation {
    DDLogDebug(@"Action -- onSyncRoamingMessageConversation:");
    
    if (conversation.conversationType != self.conversation.conversationType) {
        return ;
    }
    BOOL isThisConversation = NO;
    if (conversation.conversationType == kJMSGConversationTypeSingle) {
        JMSGUser *user1 = (JMSGUser *)conversation.target;
        JMSGUser *user2 = (JMSGUser *)self.conversation.target;
        if ([user1.username isEqualToString:user2.username] &&
            [user1.appKey isEqualToString:user2.appKey]) {
            isThisConversation = YES;
        }
    }
    if (!isThisConversation) {
        return ;
    }
    isNoOtherMessage = NO;
    messageOffset = 0;
    [_imgDataArr removeAllObjects];
    [_userArr removeAllObjects];
    
    [_allMessageDic removeAllObjects];
    [_allmessageIdArr removeAllObjects];
    [_imgDataArr removeAllObjects];
    
    [self getGroupMemberListWithGetMessageFlag:YES];
}

- (void)onGroupInfoChanged:(JMSGGroup *)group {
    //  [self updateGroupConversationTittle:group];
}

- (void)relayoutTableCellWithMessage:(JMSGMessage *) message{
    DDLogDebug(@"relayoutTableCellWithMessage: msgid:%@",message.msgId);
    if ([message.msgId isEqualToString:@""]) {
        return;
    }
    
    JCHATChatModel *model = _allMessageDic[message.msgId];
    if (model) {
        model.message = message;
        [_allMessageDic setObject:model forKey:message.msgId];
    }
    
    NSInteger index = [_allmessageIdArr indexOfObject:message.msgId];
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:index inSection:0];
    
    
    
    if (model.message.contentType == kJMSGContentTypeCustom) {
        JMSGMessage *latesMessage  = model.message;
            JCHATShowTimeCell *cell = [_messageTableView cellForRowAtIndexPath:indexPath];
            
            JMSGAbstractContent *jmsContent =latesMessage.content;
            
            JMSGCustomContent *textContent = (JMSGCustomContent *)jmsContent;
            NSDictionary *lastDic = textContent.customDictionary;
            NSLog(@"22222222222=======:%@", lastDic);
            NSString *_lastMsgStr;
            if ([minstr([lastDic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
                _lastMsgStr = YZMsg(@"你关注了对方，快跟Ta聊聊天吧～");
            }else{
                _lastMsgStr = YZMsg(@"对方已关注你，快跟Ta聊聊天吧～");

            }
            cell.messageTimeLabel.text  =_lastMsgStr;
    }else{
        JCHATMessageTableViewCell *tableviewcell = [_messageTableView cellForRowAtIndexPath:indexPath];
        tableviewcell.model = model;
        [tableviewcell layoutAllView];

    }

    
    
    //[_messageTableView reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil] withRowAnimation:UITableViewRowAnimationFade];
}

#pragma mark --获取对应消息的索引
- (NSInteger )getIndexWithMessageId:(NSString *)messageID {
    for (NSInteger i=0; i< [_allmessageIdArr count]; i++) {
        NSString *getMessageID = _allmessageIdArr[i];
        if ([getMessageID isEqualToString:messageID]) {
            return i;
        }
    }
    return 0;
}

- (bool)checkDevice:(NSString *)name {
    NSString *deviceType = [UIDevice currentDevice].model;
    DDLogDebug(@"deviceType = %@", deviceType);
    NSRange range = [deviceType rangeOfString:name];
    return range.location != NSNotFound;
}

#pragma mark -- 清空消息缓存
- (void)cleanMessageCache {
    [_allMessageDic removeAllObjects];
    [_allmessageIdArr removeAllObjects];
    [self.messageTableView reloadData];
}

#pragma mark --添加message
- (void)addMessage:(JCHATChatModel *)model {
    if (model.isTime) {
        [_allMessageDic setObject:model forKey:model.timeId];
        [_allmessageIdArr addObject:model.timeId];
        [self addCellToTabel];
        return;
    }
    [_allMessageDic setObject:model forKey:model.message.msgId];
    [_allmessageIdArr addObject:model.message.msgId];
    [self addCellToTabel];
}

NSInteger sortMessageType(id object1,id object2,void *cha) {
    JMSGMessage *message1 = (JMSGMessage *)object1;
    JMSGMessage *message2 = (JMSGMessage *)object2;
    if([message1.timestamp integerValue] > [message2.timestamp integerValue]) {
        return NSOrderedDescending;
    } else if([message1.timestamp integerValue] < [message2.timestamp integerValue]) {
        return NSOrderedAscending;
    }
    return NSOrderedSame;
}

- (void)AlertToSendImage:(NSNotification *)notification {
    UIImage *img = notification.object;
    //[self prepareImageMessage:img];
    [self sendImage:img isMult:NO];
}
#pragma mark - 撤回
-(void)userRetractMessage:(NSNotification *)notification {
    JMSGMessage *message = notification.object;
    NSLog(@"撤回===%@",message.msgId);
    [_conversation retractMessage:message completionHandler:^(id resultObject, NSError *error) {
        NSLog(@"===ch:%@-%@",resultObject,error);
        NSLog(@"撤回===2%@",resultObject);
        if (error != nil) {
            //[MBProgressHUD showPop:YZMsg(@"该消息发送时间已超过两分钟,无法撤回")];
        }else {
            [self retractMsgReplace:(JMSGMessage *)resultObject];
        }
    }];
    
}
- (void)deleteMessage:(NSNotification *)notification {
    JMSGMessage *message = notification.object;
    [_conversation deleteMessageWithMessageId:message.msgId];
    [_allMessageDic removeObjectForKey:message.msgId];
    [_allmessageIdArr removeObject:message.msgId];
    [_messageTableView loadMoreMessage];
}

#pragma mark - 排序conversation
- (NSMutableArray *)sortMessage:(NSMutableArray *)messageArr {
    NSArray *sortResultArr = [messageArr sortedArrayUsingFunction:sortMessageType context:nil];
    return [NSMutableArray arrayWithArray:sortResultArr];
}

- (void)getPageMessage {
    DDLogDebug(@"Action - getAllMessage");
    [self cleanMessageCache];
    NSMutableArray * arrList = [[NSMutableArray alloc] init];
    [_allmessageIdArr addObject:[[NSObject alloc] init]];
    
    messageOffset = messagefristPageNumber;
    [arrList addObjectsFromArray:[[[_conversation messageArrayFromNewestWithOffset:@0 limit:@(messageOffset)] reverseObjectEnumerator] allObjects]];
    if ([arrList count] < messagefristPageNumber) {
        isNoOtherMessage = YES;
        [_allmessageIdArr removeObjectAtIndex:0];
    }
    
    for (NSInteger i=0; i< [arrList count]; i++) {
        JMSGMessage *message = [arrList objectAtIndex:i];
        JCHATChatModel *model = [[JCHATChatModel alloc] init];
        [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
        if (message.contentType == kJMSGContentTypeImage) {
            [_imgDataArr addObject:model];
            model.photoIndex = [_imgDataArr count] - 1;
        }
        
        [self dataMessageShowTime:message.timestamp];
        [_allMessageDic setObject:model forKey:model.message.msgId];
        [_allmessageIdArr addObject:model.message.msgId];
    }
    [_messageTableView reloadData];
    [self scrollToBottomAnimated:NO];
}

- (void)flashToLoadMessage {
    NSMutableArray * arrList = @[].mutableCopy;
    NSArray *newMessageArr = [_conversation messageArrayFromNewestWithOffset:@(messageOffset) limit:@(messagePageNumber)];
    [arrList addObjectsFromArray:newMessageArr];
    if ([arrList count] < messagePageNumber) {// 判断还有没有新数据
        isNoOtherMessage = YES;
        [_allmessageIdArr removeObjectAtIndex:0];
    }
    
    messageOffset += messagePageNumber;
    for (NSInteger i = 0; i < [arrList count]; i++) {
        JMSGMessage *message = arrList[i];
        JCHATChatModel *model = [[JCHATChatModel alloc] init];
        [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
        
        if (message.contentType == kJMSGContentTypeImage) {
            [_imgDataArr insertObject:model atIndex:0];
            model.photoIndex = [_imgDataArr count] - 1;
        }
        
        [_allMessageDic setObject:model forKey:model.message.msgId];
        [_allmessageIdArr insertObject:model.message.msgId atIndex: isNoOtherMessage?0:1];
        [self dataMessageShowTimeToTop:message.timestamp];// FIXME:
    }
    
    [_messageTableView loadMoreMessage];
}

- (JMSGUser *)getAvatarWithTargetId:(NSString *)targetId {
    
    for (NSInteger i=0; i<[_userArr count]; i++) {
        JMSGUser *user = [_userArr objectAtIndex:i];
        if ([user.username isEqualToString:targetId]) {
            return user;
        }
    }
    return nil;
}

- (XHVoiceRecordHelper *)voiceRecordHelper {
    if (!_voiceRecordHelper) {
        WEAKSELF
        _voiceRecordHelper = [[XHVoiceRecordHelper alloc] init];
        
        _voiceRecordHelper.maxTimeStopRecorderCompletion = ^{
            DDLogDebug(@"已经达到最大限制时间了，进入下一步的提示");
            __strong __typeof(weakSelf)strongSelf = weakSelf;
            [strongSelf finishRecorded];
        };
        
        _voiceRecordHelper.peakPowerForChannel = ^(float peakPowerForChannel) {
            __strong __typeof(weakSelf)strongSelf = weakSelf;
            strongSelf.voiceRecordHUD.peakPower = peakPowerForChannel;
        };
        
        _voiceRecordHelper.maxRecordTime = kVoiceRecorderTotalTime;
    }
    return _voiceRecordHelper;
}

- (XHVoiceRecordHUD *)voiceRecordHUD {
    if (!_voiceRecordHUD) {
        _voiceRecordHUD = [[XHVoiceRecordHUD alloc] initWithFrame:CGRectMake(0, 0, 140, 140)];
    }
    return _voiceRecordHUD;
}

- (void)backClick {
    if ([[JCHATAudioPlayerHelper shareInstance] isPlaying]) {
        [[JCHATAudioPlayerHelper shareInstance] stopAudio];
    }
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)pressVoiceBtnToHideKeyBoard {///!!!
    [self.toolBarContainer.toolbar.textView resignFirstResponder];
    _toolBarHeightConstrait.constant = 45;
    [self dropToolBar];
}

- (void)switchToTextInputMode {
    UITextView *inputview = self.toolBarContainer.toolbar.textView;
    [inputview becomeFirstResponder];
    [self layoutAndAnimateMessageInputTextView:inputview];
}

#pragma mark -调用相册
- (void)photoClick {
    
    _didSendCount = 0;
    _sendImgLimit = NO;
    
    TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.iconThemeColor = Pink_Cor;
    imagePC.naviBgColor = CellRow_Cor;
    imagePC.oKButtonTitleColorNormal = Pink_Cor;
    imagePC.oKButtonTitleColorDisabled = Pink_Cor_Dis;
    imagePC.allowTakePicture = NO;
    imagePC.allowTakeVideo = NO;
    imagePC.allowPickingVideo = NO;
    imagePC.showSelectedIndex = YES;
    imagePC.allowCrop = NO;
    imagePC.allowPickingOriginalPhoto = NO;
    imagePC.minImagesCount = 1;
    imagePC.maxImagesCount = 9;
    imagePC.modalPresentationStyle = 0;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self presentViewController:imagePC animated:YES completion:nil];
    });
    
    /*
     ALAssetsLibrary *lib = [[ALAssetsLibrary alloc] init];
     [lib enumerateGroupsWithTypes:ALAssetsGroupSavedPhotos usingBlock:^(ALAssetsGroup *group, BOOL *stop) {
     JCHATPhotoPickerViewController *photoPickerVC = [[JCHATPhotoPickerViewController alloc] init];
     photoPickerVC.photoDelegate = self;
     [self presentViewController:photoPickerVC animated:YES completion:NULL];
     } failureBlock:^(NSError *error) {
     
     UIAlertController *alertC = [UIAlertController alertControllerWithTitle:YZMsg(@"没有相册权限") message:YZMsg(@"请到设置页面获取相册权限") preferredStyle:UIAlertControllerStyleAlert];
     UIAlertAction *cancelA = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleCancel handler:nil];
     [alertC addAction:cancelA];
     [self presentViewController:alertC animated:YES completion:nil];
     }];
     */
}
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    
    NSMutableArray *imgArray = [NSMutableArray array];
    for (UIImage *img in photos) {
        UIImage *scaleImg = [PublicObj scaleImage:img scaleToSize:[PublicObj getObjSize:img.size]];
        [imgArray addObject:scaleImg];
    }
    _didSelImgA = [NSArray arrayWithArray:imgArray];
    [self goSendImg];
    
    [self dropToolBarNoAnimate];
}

-(void)goSendImg{
    if (_didSendCount < _didSelImgA.count && !_sendImgLimit) {
        [self sendImage:_didSelImgA[_didSendCount] isMult:YES];
    }
}

-(void)sendImage:(UIImage *)sendImg isMult:(BOOL)isMult{
    NSString *url = [NSString stringWithFormat:@"User.checkBlack&uid=%@&token=%@&touid=%@&content=",[Config getOwnID],[Config getOwnToken],_userModel.uidStr];
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *isattent = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isattent"]];
            NSString *t2u = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"t2u"]];
            NSString *sendConten = minstr([infoDic valueForKey:@"content"]);
            NSString *p_switch = [NSString stringWithFormat:@"%@",[common private_letter_switch]];
            int p_nums = [[NSString stringWithFormat:@"%@",[common private_letter_nums]] intValue];
            //我发送的条数
            NSMutableArray *frends_cont = [NSMutableArray array];
            for (int i = 0; i < _allmessageIdArr.count; i++) {
                NSString *msgid = [_allmessageIdArr objectAtIndex:i];
                JCHATChatModel *model = [_allMessageDic objectForKey:msgid];
                if ([model.uidStr isEqual:[Config getOwnID]]) {
                    [frends_cont addObject:model];
                }
            }
            if ([t2u isEqual:@"0"]) {
                //已发送+将要发送的图片数量 不能大于最大消息数量
                if ([isattent isEqual:@"0"] && [p_switch isEqual:@"1"] && frends_cont.count >= p_nums) {
                    [MBProgressHUD showPop:[NSString stringWithFormat:@"%@%d%@",YZMsg(@"对方未关注你,最多只能发送"),p_nums,YZMsg(@"条信息")]];
                    if (isMult) {
                        _sendImgLimit = YES;
                    }
                }else {
                    //可以发送
                    [self prepareImageMessage:sendImg];
                    if (isMult) {
                        //继续下一张
                        _didSendCount ++;
                        [self goSendImg];
                    }
                }
            }else {
                [MBProgressHUD showPop:YZMsg(@"对方暂时拒绝接收您的消息")];
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}

#pragma mark -- 调用相机
- (void)cameraClick {
    UIImagePickerController *picker = [[UIImagePickerController alloc] init];
    
    if([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        picker.sourceType = UIImagePickerControllerSourceTypeCamera;
        NSString *requiredMediaType = ( NSString *)kUTTypeImage;
        NSArray *arrMediaTypes=[NSArray arrayWithObjects:requiredMediaType,nil];
        [picker setMediaTypes:arrMediaTypes];
        picker.showsCameraControls = YES;
        picker.modalTransitionStyle = UIModalTransitionStyleCoverVertical;
        picker.editing = YES;
        picker.delegate = self;
        picker.modalPresentationStyle = 0;
        [self presentViewController:picker animated:YES completion:nil];
    }
}

#pragma mark - 语音输入
-(void)voiceInputClick {
    NSLog(@"语音输入");
    YBWeakSelf;
    if (!_asrView) {
        _asrView = [[ASRView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, _window_height) callBack:^(NSString *type, NSString *content) {
            if ([type isEqual:@"返回"]) {
                NSLog(@"返回");
                [weakSelf asrBack];
            }else if ([type isEqual:@"发送"]){
                NSLog(@"发送");
                [weakSelf asrSend:content];
            }
        }];
        [self.view addSubview:_asrView];
    }
    
    [UIView animateWithDuration:0.25 animations:^{
        _asrView.frame = CGRectMake(0, 0, _window_width, _window_height);
        _moreViewHeight.constant = ASRHeight-_toolBarHeightConstrait.constant;
        [self scrollToEnd];
    }];
    
}
-(void)asrBack {
    [_asrView.textView resignFirstResponder];
    [UIView animateWithDuration:0.25 animations:^{
        _asrView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
        _moreViewHeight.constant = kMoreHeight+ShowDiff;
        [self scrollToEnd];
    }];
}
-(void)asrSend:(NSString *)content {
    [self sendText:content];
}

#pragma mark - 位置信息
-(void)locationClick {
    TencentLocationVC *locationVC = [[TencentLocationVC alloc]init];
    YBWeakSelf;
    locationVC.locationEvent = ^(NSDictionary *locDic) {
        [weakSelf prepareLocation:locDic];
    };
    [self.navigationController pushViewController:locationVC animated:YES];
}
-(void)prepareLocation:(NSDictionary *)dic{
    
    NSNumber *latitude = @([[NSString stringWithFormat:@"%@",[dic valueForKey:@"latitude"]] doubleValue]);
    NSNumber *longitude = @([[NSString stringWithFormat:@"%@",[dic valueForKey:@"longitude"]] doubleValue]);
    NSString *address = [dic valueForKey:@"address"];
    
    
    JMSGLocationContent *locationContent = [[JMSGLocationContent alloc]initWithLatitude:latitude longitude:longitude scale:@(1) address:address];
    
    [self checkBlack:locationContent voice:@"" textContent:@""];
    
    //JMSGMessage *message = nil;
    //message = [_conversation createMessageWithContent:locationContent];
    //[_conversation sendMessage:message];
    //[self addmessageShowTimeData:message.timestamp];
    //[model setChatModelWith:message conversationType:_conversation userModel:_userModel];
    //[self addMessage:model];
    
}


#pragma mark - HMPhotoPickerViewController Delegate 发送图片
- (void)JCHATPhotoPickerViewController:(JCHATPhotoSelectViewController *)PhotoPickerVC selectedPhotoArray:(NSArray *)selected_photo_array {
    
    NSString *url = [NSString stringWithFormat:@"User.checkBlack&uid=%@&token=%@&touid=%@&content=",[Config getOwnID],[Config getOwnToken],_userModel.uidStr];
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *isattent = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isattent"]];
            NSString *t2u = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"t2u"]];
            NSString *sendConten = minstr([infoDic valueForKey:@"content"]);
            NSString *p_switch = [NSString stringWithFormat:@"%@",[common private_letter_switch]];
            int p_nums = [[NSString stringWithFormat:@"%@",[common private_letter_nums]] intValue];
            //我发送的条数
            NSMutableArray *frends_cont = [NSMutableArray array];
            for (int i = 0; i < _allmessageIdArr.count; i++) {
                NSString *msgid = [_allmessageIdArr objectAtIndex:i];
                JCHATChatModel *model = [_allMessageDic objectForKey:msgid];
                if ([model.uidStr isEqual:[Config getOwnID]]) {
                    [frends_cont addObject:model];
                }
            }
            
            if ([t2u isEqual:@"0"]) {
                //已发送+将要发送的图片数量 不能大于最大消息数量
                if ([isattent isEqual:@"0"] && [p_switch isEqual:@"1"] && (frends_cont.count+selected_photo_array.count) > p_nums) {
                    [MBProgressHUD showPop:[NSString stringWithFormat:@"%@%d%@",YZMsg(@"对方未关注你,最多只能发送"),p_nums,YZMsg(@"条信息")]];
                }else {
                    //可以发送
                    for (UIImage *image in selected_photo_array) {
                        [self prepareImageMessage:image];
                    }
                }
            }else {
                [MBProgressHUD showPop:YZMsg(@"对方暂时拒绝接收您的消息")];
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
    
    [self dropToolBarNoAnimate];
}
#pragma mark - UIImagePickerController Delegate
//相机,相册Finish的代理
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info {
    NSString *mediaType = [info objectForKey:UIImagePickerControllerMediaType];
    
    if ([mediaType isEqualToString:@"public.movie"]) {
        [self dismissViewControllerAnimated:YES completion:nil];
        [MBProgressHUD showMessage:YZMsg(@"不支持视频发送") toView:self.view];
        return;
    }
    UIImage *image;
    image = [info objectForKey:UIImagePickerControllerOriginalImage];
    //  [self prepareImageMessage:image];
    [self sendImage:image isMult:NO];
    [self dropToolBarNoAnimate];
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- 发送图片
- (void)prepareImageMessage:(UIImage *)img {
    DDLogDebug(@"Action - prepareImageMessage");
    img = [img resizedImageByWidth:upLoadImgWidth];
    
    JMSGMessage* message = nil;
    JCHATChatModel *model = [[JCHATChatModel alloc] init];
    JMSGImageContent *imageContent = [[JMSGImageContent alloc] initWithImageData:UIImagePNGRepresentation(img)];
    if (imageContent) {
        message = [_conversation createMessageWithContent:imageContent];
        [[JCHATSendMsgManager ins] addMessage:message withConversation:_conversation];
        [self addmessageShowTimeData:message.timestamp];
        [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
        [_imgDataArr addObject:model];
        model.photoIndex = [_imgDataArr count] - 1;
        [model setupImageSize];
        [self addMessage:model];
    }
}

#pragma mark --
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark --add Delegate
- (void)addDelegate {
    [JMessage addDelegate:self withConversation:self.conversation];
}

#pragma mark --加载通知
- (void)addNotification{
    //给键盘注册通知
    NSNotificationCenter *notiCenter = [NSNotificationCenter defaultCenter];
    [notiCenter addObserver:self selector:@selector(inputKeyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    [notiCenter addObserver:self selector:@selector(inputKeyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];
    [notiCenter addObserver:self selector:@selector(cleanMessageCache) name:kDeleteAllMessage object:nil];
    [notiCenter addObserver:self selector:@selector(AlertToSendImage:) name:kAlertToSendImage object:nil];
    [notiCenter addObserver:self selector:@selector(userRetractMessage:) name:kRetractMsg object:nil];
    [notiCenter addObserver:self selector:@selector(deleteMessage:) name:kDeleteMessage object:nil];
    [self.toolBarContainer.toolbar.textView addObserver:self forKeyPath:@"contentSize" options:NSKeyValueObservingOptionNew context:nil];
    self.toolBarContainer.toolbar.textView.delegate = self;
}

#pragma mark --发送文本
- (void)sendText:(NSString *)text {
    [self prepareTextMessage:text];
}

- (void)perform {
    _moreViewHeight.constant = 0;
    _toolBarToBottomConstrait.constant = 0+ShowDiff;
}

#pragma mark --返回下面的位置
- (void)dropToolBar {
    if (self.naviView.hidden) {
        [UIView animateWithDuration:0.25 animations:^{//0.3
            self.view.frame = CGRectMake(0, _window_height - _window_height*0.4, _window_width, _window_height*0.4);
            _emojiV.frame = CGRectMake(0, _window_height, _window_width, EmojiHeight);
            _toolBarContainer.toolbar.faceBtn.selected = NO;
        }];
        return;
    }
    _barBottomFlag =YES;
    _previousTextViewContentHeight = 36;
    _toolBarContainer.toolbar.addButton.selected = NO;
    _toolBarContainer.toolbar.faceBtn.selected = NO;
    //  [_messageTableView reloadData];
    [UIView animateWithDuration:0.25 animations:^{//0.3
        _toolBarToBottomConstrait.constant = 0+ShowDiff;
        _emojiV.frame = CGRectMake(0, _window_height, _window_width, EmojiHeight+ShowDiff);
        _moreViewHeight.constant = 0;
    }];
    
}

- (void)dropToolBarNoAnimate {
    _barBottomFlag =YES;
    _previousTextViewContentHeight = 36;
    _toolBarContainer.toolbar.addButton.selected = NO;
    _toolBarContainer.toolbar.faceBtn.selected = NO;
    //[_messageTableView reloadData];
    _toolBarToBottomConstrait.constant = 0+ShowDiff;
    _emojiV.frame = CGRectMake(0, _window_height, _window_width, EmojiHeight+ShowDiff);
    _moreViewHeight.constant = 0;
}
#pragma mark - 键盘
- (void)inputKeyboardWillShow:(NSNotification *)notification{
    
    //获取键盘的高
    NSDictionary *userInfo = [notification userInfo];
    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    CGFloat height = keyboardRect.origin.y;
    CGFloat newHeight = _window_height - height;
    if (self.naviView.hidden) {
        [UIView animateWithDuration:0.25 animations:^{//0.3
            _toolBarToBottomConstrait.constant = 0;
            self.view.frame = CGRectMake(0,_window_height*0.2, _window_width, _window_height*0.8 - newHeight);
            _toolBarContainer.toolbar.faceBtn.selected = NO;
        }];
        return;
    }
    
    _toolBarContainer.toolbar.addButton.selected = NO;
    _toolBarContainer.toolbar.faceBtn.selected = NO;
    _barBottomFlag=NO;
    
    CGRect keyBoardFrame = [[[notification userInfo] objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat animationTime = [[[notification userInfo] objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    if (_asrView.textView.isFirstResponder == YES) {
        [UIView animateWithDuration:0.25 animations:^{
            _asrMoveUp = YES;
            _asrView.frame = CGRectMake(0, -keyBoardFrame.size.height, _window_width, _window_height);
            _moreViewHeight.constant = keyBoardFrame.size.height-_toolBarHeightConstrait.constant;
        }];
    }else{
        [UIView animateWithDuration:animationTime animations:^{
            _moreViewHeight.constant = keyBoardFrame.size.height;
            _toolBarToBottomConstrait.constant = 0;
            [self.view layoutIfNeeded];
        }];
    }
    [self scrollToEnd];//!
}

- (void)inputKeyboardWillHide:(NSNotification *)notification {
    CGFloat animationTime = [[[notification userInfo] objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    kWEAKSELF
    if (_asrMoveUp == YES) {
        [UIView animateWithDuration:0.25 animations:^{
            _asrMoveUp = NO;
            _asrView.frame = CGRectMake(0, 0, _window_width, _window_height);
            _moreViewHeight.constant = ASRHeight-_toolBarHeightConstrait.constant;
        }];
    }else{
        [UIView animateWithDuration:animationTime animations:^{
            _moreViewHeight.constant = 0;
            _toolBarToBottomConstrait.constant = 0+ShowDiff;
            [weakSelf.view layoutIfNeeded];
        }];
    }
    if (self.naviView.hidden) {
        [UIView animateWithDuration:0.25 animations:^{//0.1
            self.view.frame = CGRectMake(0, _window_height - _window_height*0.4, _window_width, _window_height*0.4);
        }];
    }
    [self scrollToBottomAnimated:NO];
    [self dropToolBar];
}
#pragma mark -- 更多
- (void)pressMoreBtnClick:(UIButton *)btn {
    _barBottomFlag=NO;
    [_toolBarContainer.toolbar.textView resignFirstResponder];
    [UIView animateWithDuration:0.25 animations:^{//0.3
        _toolBarToBottomConstrait.constant = 0;
        _emojiV.frame = CGRectMake(0, _window_height, _window_width, EmojiHeight+ShowDiff);
        _moreViewHeight.constant = kMoreHeight+ShowDiff;
        [_messageTableView layoutIfNeeded];
        [_toolBarContainer layoutIfNeeded];
        [_moreViewContainer layoutIfNeeded];
    }];
    [_toolBarContainer.toolbar switchToolbarToTextMode];
    
}

- (void)noPressmoreBtnClick:(UIButton *)btn {
    [self dropToolBar];
    //_emojiV.frame = CGRectMake(0, _window_height, _window_width, EmojiHeight);
    [_toolBarContainer.toolbar.textView becomeFirstResponder];
}
#pragma mark - 表情弹起、落下
-(void)pressFaceBtnClick:(UIButton *)btn {
    [_toolBarContainer.toolbar.textView resignFirstResponder];
    if (self.naviView.hidden) {
        [UIView animateWithDuration:0.25 animations:^{//0.3
            self.view.frame = CGRectMake(0,_window_height*0.2, _window_width, _window_height*0.8 - EmojiHeight);
            _emojiV.frame = CGRectMake(0, _window_height-EmojiHeight, _window_width, EmojiHeight);
            
        }];
        return ;
    }
    _barBottomFlag=NO;
    [_toolBarContainer.toolbar.textView resignFirstResponder];
    [UIView animateWithDuration:0.25 animations:^{//0.3
        _toolBarToBottomConstrait.constant = EmojiHeight+ShowDiff;
        _moreViewHeight.constant = 0;
        _emojiV.frame = CGRectMake(0, _window_height-EmojiHeight-ShowDiff, _window_width, EmojiHeight+ShowDiff);
        [_messageTableView layoutIfNeeded];
        [_toolBarContainer layoutIfNeeded];
        [_moreViewContainer layoutIfNeeded];
    }];
    [_toolBarContainer.toolbar switchToolbarToTextMode];
}
-(void)noPressFaceBtnClick:(UIButton *)btn {
    //_toolBarToBottomConstrait.constant = 0;
    //[_toolBarContainer layoutIfNeeded];
    //_emojiV.frame = CGRectMake(0, _window_height, _window_width, EmojiHeight);
    [self dropToolBar];
    [_toolBarContainer.toolbar.textView becomeFirstResponder];
}

#pragma mark - Emoji 代理
-(void)sendimage:(NSString *)str {
    if ([str isEqual:@"msg_del"]) {
        [_toolBarContainer.toolbar.textView deleteBackward];
    }else {
        [_toolBarContainer.toolbar.textView insertText:str];
    }
}

-(void)clickSendEmojiBtn {
    
    [self prepareTextMessage:_toolBarContainer.toolbar.textView.text];
    _toolBarContainer.toolbar.textView.text = @"";
}

#pragma mark ----发送文本消息

- (void)prepareTextMessage:(NSString *)text {
    [[JCHATSendMsgManager ins] updateConversation:_conversation withDraft:@""];
    NSString *allText = [text stringByReplacingOccurrencesOfString:@" " withString:@""];
    if (allText.length<1) {
        [MBProgressHUD showPop:YZMsg(@"消息不能为空")];
        return;;
    }
    
    
    JMSGTextContent *textContent = [[JMSGTextContent alloc] initWithText:text];
    
    [self checkBlack:textContent voice:@"" textContent:text];
    
    //JMSGMessage *message = nil;
    //message = [_conversation createMessageWithContent:textContent];//!
    //[_conversation sendMessage:message];
    //[self addmessageShowTimeData:message.timestamp];
    //[model setChatModelWith:message conversationType:_conversation userModel:_userModel];
    //[self addMessage:model];
}

#pragma mark -- 刷新对应的
- (void)addCellToTabel {
    NSIndexPath *path = [NSIndexPath indexPathForRow:[_allmessageIdArr count]-1 inSection:0];
    [_messageTableView beginUpdates];
    [_messageTableView insertRowsAtIndexPaths:@[path] withRowAnimation:UITableViewRowAnimationNone];
    [_messageTableView endUpdates];
    [self scrollToEnd];
}

#pragma mark ---比较和上一条消息时间超过5分钟之内增加时间model
- (void)addmessageShowTimeData:(NSNumber *)timeNumber{
    NSString *messageId = [_allmessageIdArr lastObject];
    JCHATChatModel *lastModel = _allMessageDic[messageId];
    NSTimeInterval timeInterVal = [timeNumber longLongValue];
    
    if ([_allmessageIdArr count] > 0 && lastModel.isTime == NO) {
        
        NSDate* lastdate = [NSDate dateWithTimeIntervalSince1970:[lastModel.messageTime longLongValue]/1000];
        NSDate* currentDate = [NSDate dateWithTimeIntervalSince1970:timeInterVal/1000];
        NSTimeInterval timeBetween = [currentDate timeIntervalSinceDate:lastdate];
        if (fabs(timeBetween) > interval) {
            [self addTimeData:timeInterVal];
        }
    } else if ([_allmessageIdArr count] == 0) {//首条消息显示时间
        [self addTimeData:timeInterVal];
    } else {
        DDLogDebug(@"不用显示时间");
    }
}

#pragma mark ---比较和上一条消息时间超过5分钟之内增加时间model
- (void)dataMessageShowTime:(NSNumber *)timeNumber{
    NSString *messageId = [_allmessageIdArr lastObject];
    JCHATChatModel *lastModel = _allMessageDic[messageId];
    NSTimeInterval timeInterVal = [timeNumber longLongValue];
    
    if ([_allmessageIdArr count]>0 && lastModel.isTime == NO) {
        NSDate* lastdate = [NSDate dateWithTimeIntervalSince1970:[lastModel.messageTime longLongValue]/1000];
        NSDate* currentDate = [NSDate dateWithTimeIntervalSince1970:timeInterVal/1000];
        NSTimeInterval timeBetween = [currentDate timeIntervalSinceDate:lastdate];
        if (fabs(timeBetween) > interval) {
            JCHATChatModel *timeModel =[[JCHATChatModel alloc] init];
            timeModel.timeId = [self getTimeId];
            timeModel.isTime = YES;
            timeModel.messageTime = @(timeInterVal);
            timeModel.contentHeight = [timeModel getTextHeight];//!
            [_allMessageDic setObject:timeModel forKey:timeModel.timeId];
            [_allmessageIdArr addObject:timeModel.timeId];
        }
    } else if ([_allmessageIdArr count] ==0) {//首条消息显示时间
        JCHATChatModel *timeModel =[[JCHATChatModel alloc] init];
        timeModel.timeId = [self getTimeId];
        timeModel.isTime = YES;
        timeModel.messageTime = @(timeInterVal);
        timeModel.contentHeight = [timeModel getTextHeight];//!
        [_allMessageDic setObject:timeModel forKey:timeModel.timeId];
        [_allmessageIdArr addObject:timeModel.timeId];
    } else {
        DDLogDebug(@"不用显示时间");
    }
}

- (void)dataMessageShowTimeToTop:(NSNumber *)timeNumber{
    NSString *messageId = [_allmessageIdArr lastObject];
    JCHATChatModel *lastModel = _allMessageDic[messageId];
    NSTimeInterval timeInterVal = [timeNumber longLongValue];
    if ([_allmessageIdArr count]>0 && lastModel.isTime == NO) {
        NSDate* lastdate = [NSDate dateWithTimeIntervalSince1970:[lastModel.messageTime doubleValue]];
        NSDate* currentDate = [NSDate dateWithTimeIntervalSince1970:timeInterVal];
        NSTimeInterval timeBetween = [currentDate timeIntervalSinceDate:lastdate];
        if (fabs(timeBetween) > interval) {
            JCHATChatModel *timeModel =[[JCHATChatModel alloc] init];
            timeModel.timeId = [self getTimeId];
            timeModel.isTime = YES;
            timeModel.messageTime = @(timeInterVal);
            timeModel.contentHeight = [timeModel getTextHeight];
            [_allMessageDic setObject:timeModel forKey:timeModel.timeId];
            [_allmessageIdArr insertObject:timeModel.timeId atIndex: isNoOtherMessage?0:1];
        }
    } else if ([_allmessageIdArr count] ==0) {//首条消息显示时间
        JCHATChatModel *timeModel =[[JCHATChatModel alloc] init];
        timeModel.timeId = [self getTimeId];
        timeModel.isTime = YES;
        timeModel.messageTime = @(timeInterVal);
        timeModel.contentHeight = [timeModel getTextHeight];
        [_allMessageDic setObject:timeModel forKey:timeModel.timeId];
        [_allmessageIdArr insertObject:timeModel.timeId atIndex: isNoOtherMessage?0:1];
    } else {
        DDLogDebug(@"不用显示时间");
    }
}

- (void)addTimeData:(NSTimeInterval)timeInterVal {
    JCHATChatModel *timeModel =[[JCHATChatModel alloc] init];
    timeModel.timeId = [self getTimeId];
    timeModel.isTime = YES;
    timeModel.messageTime = @(timeInterVal);
    timeModel.contentHeight = [timeModel getTextHeight];//!
    [self addMessage:timeModel];
}

- (NSString *)getTimeId {
    NSString *timeId = [NSString stringWithFormat:@"%d",arc4random()%1000000];
    return timeId;
}

#pragma mark - 屏幕触摸事件
//代理
-(void)msgTableView:(UITableView *)tableView touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    if (_barBottomFlag==NO) {
        if (_asrView.isFirstResponder == YES) {
            [UIView animateWithDuration:0.25 animations:^{
                _asrView.frame = CGRectMake(0, 0, _window_width, _window_height);
                _moreViewHeight.constant = ASRHeight-_toolBarHeightConstrait.constant;
            }];
        }
        UITouch *touch = [[event allTouches] anyObject];
        CGPoint _touchPoint = [touch locationInView:self.view];
        if (YES == CGRectContainsPoint(_messageTableView.frame, _touchPoint)){
            [self.toolBarContainer.toolbar.textView resignFirstResponder];
            [self dropToolBar];
        }
    }
}

- (void)tapClick:(UIGestureRecognizer *)gesture {
    [self.toolBarContainer.toolbar.textView resignFirstResponder];
    [self dropToolBar];
}

#pragma mark --滑动至尾端
- (void)scrollToEnd {
    if ([_allmessageIdArr count] != 0) {
        [self.messageTableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:[_allmessageIdArr count]-1 inSection:0] atScrollPosition:UITableViewScrollPositionBottom animated:YES];
    }
}

#pragma mark - tableView datasoce
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (!isNoOtherMessage) {
        if (indexPath.row == 0) { //这个是第 0 行 用于刷新
            return 40;
        }
    }
    
    if (indexPath.row >= _allmessageIdArr.count) {
        DDLogDebug(@"1.index %ld beyond bounds %ld",indexPath.row,_allmessageIdArr.count);
        return 40;
    }
    NSString *messageId = _allmessageIdArr[indexPath.row];
    JCHATChatModel *model = _allMessageDic[messageId];
    if (model.isTime == YES) {
        return 31;
    }
    
    if (model.message.contentType == kJMSGContentTypeEventNotification) {
        return model.contentHeight + 17;
    }
    
    if (model.message.contentType == kJMSGContentTypeText) {
        if (model.message.content.extras && [[model.message.content.extras allKeys]containsObject:@"goodsid"]) {
            return _window_width*0.6 + 10;
        }else{
            return model.contentHeight + 17;
        }

    } else if (model.message.contentType == kJMSGContentTypeImage ||
               model.message.contentType == kJMSGContentTypeFile ||
               model.message.contentType == kJMSGContentTypeLocation) {
        if (model.imageSize.height == 0) {
            [model setupImageSize];
        }
        return model.imageSize.height < 44?59:model.imageSize.height + 14;
        
    } else if (model.message.contentType == kJMSGContentTypeVoice) {
        return 69;
    }  else {
        return 49;
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [_allmessageIdArr count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (!isNoOtherMessage) {
        if (indexPath.row == 0) {
            static NSString *cellLoadIdentifier = @"loadCell"; //name
            JCHATLoadMessageTableViewCell *cell = (JCHATLoadMessageTableViewCell *)[tableView dequeueReusableCellWithIdentifier:cellLoadIdentifier];
            
            if (cell == nil) {
                cell = [[JCHATLoadMessageTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellLoadIdentifier];
            }
            [cell startLoading];
            [self flashToLoadMessage];
            //          [self performSelector:@selector(flashToLoadMessage) withObject:nil afterDelay:0];
            return cell;
        }
    }
    if (indexPath.row >= _allmessageIdArr.count) {
        DDLogDebug(@"2.index %ld beyond bounds %ld",indexPath.row,_allmessageIdArr.count);
        return nil;
    }
    NSString *messageId = _allmessageIdArr[indexPath.row];
    if (!messageId) {
        DDLogDebug(@"messageId is nil%@",messageId);
        return nil;
    }
    
    JCHATChatModel *model = _allMessageDic[messageId];
    if (!model) {
        DDLogDebug(@"JCHATChatModel is nil%@", messageId);
        return nil;
    }
    
    if (model.isTime == YES || model.message.contentType == kJMSGContentTypeEventNotification || model.isErrorMessage
        || model.message.contentType == kJMSGContentTypePrompt) {
        //消息时间
        static NSString *cellIdentifier = @"timeCell";
        JCHATShowTimeCell *cell = (JCHATShowTimeCell *)[tableView dequeueReusableCellWithIdentifier:cellIdentifier];
        
        if (cell == nil) {
            cell = [[[NSBundle mainBundle] loadNibNamed:@"JCHATShowTimeCell" owner:nil options:nil] lastObject];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
        }
        
        if (model.isErrorMessage) {
            cell.messageTimeLabel.text = [NSString stringWithFormat:@"%@ %@:%ld",st_receiveErrorMessageDes,YZMsg(@"错误码"),model.messageError.code];
            return cell;
        }
        
        if (model.message.contentType == kJMSGContentTypeEventNotification) {
            cell.messageTimeLabel.text = [((JMSGEventContent *)model.message.content) showEventNotification];
        }else if (model.message.contentType == kJMSGContentTypePrompt) {
            NSString *alertMsg = YZMsg(@"你撤回了一条消息");
            if (model.message.isReceived) {
                alertMsg = YZMsg(@"对方撤回了一条消息");
            }
            cell.messageTimeLabel.text = alertMsg;
        }else {
            cell.messageTimeLabel.text = [JCHATStringUtils getFriendlyDateString:[model.messageTime longLongValue]];
        }
        return cell;
        
    } else {
        if (model.message.contentType == kJMSGContentTypeCustom) {
                JMSGMessage *latesMessage  = model.message;
                static NSString *cellIdentifier = @"timeCell";

                JCHATShowTimeCell *cell = (JCHATShowTimeCell *)[tableView dequeueReusableCellWithIdentifier:cellIdentifier];
                
                if (cell == nil) {
                    cell = [[[NSBundle mainBundle] loadNibNamed:@"JCHATShowTimeCell" owner:nil options:nil] lastObject];
                    cell.selectionStyle = UITableViewCellSelectionStyleNone;
                }

                JMSGAbstractContent *jmsContent =latesMessage.content;
                
                JMSGCustomContent *textContent = (JMSGCustomContent *)jmsContent;
                NSDictionary *lastDic = textContent.customDictionary;
                NSLog(@"22222222222=======:%@", lastDic);
                NSString *_lastMsgStr;
                if ([minstr([lastDic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
                    _lastMsgStr = YZMsg(@"你关注了对方，快跟Ta聊聊天吧～");
                }else{
                    _lastMsgStr = YZMsg(@"对方已关注你，快跟Ta聊聊天吧～");

                }
                cell.messageTimeLabel.text  =_lastMsgStr;
                return cell;
        }
        //消息内容
        static NSString *cellIdentifier = @"MessageCell";
        JCHATMessageTableViewCell *cell = (JCHATMessageTableViewCell *)[tableView dequeueReusableCellWithIdentifier:cellIdentifier];
        
        if (cell == nil) {
            cell = [[JCHATMessageTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
            cell.conversation = _conversation;
        }
        
        [cell setCellData:model delegate:self indexPath:indexPath];
        
        kWEAKSELF
        cell.messageTableViewCellRefreshMediaMessage = ^(JCHATChatModel *cellModel,BOOL isShouldRefresh){
            if (isShouldRefresh) {
                [weakSelf refreshCellMessageMediaWithChatModel:cellModel];
            }
        };
        //rk20201030
        cell.passRKTextView = self.toolBarContainer.toolbar.textView;
        return cell;
    }
}

#pragma mark - 检查并刷新消息图片图片
- (void)refreshCellMessageMediaWithChatModel:(JCHATChatModel *)model {
    DDLogDebug(@"Action - refreshCellMessageMediaWithChatModel:");
    
    if (!model) {
        return ;
    }
    if (!model.message || ![self.conversation isMessageForThisConversation:model.message]) {
        return ;
    }
    NSString *msgId = model.message.msgId;
    JMSGMessage *db_message = [self.conversation messageWithMessageId:msgId];
    if (!db_message || !db_message.msgId) {
        return ;
    }
    
    model.message = db_message;
    [_allMessageDic setObject:model forKey:model.message.msgId];
    //[_allmessageIdArr addObject:model.message.msgId];msgId 不会变化所以不用去修改
    
    // 1.method
    //[self.messageTableView reloadData];
    
    // 2.method
    //NSArray *cellArray = [_messageTableView visibleCells];
    //for (id temp in cellArray) {
    //    if ([temp isKindOfClass:[JCHATMessageTableViewCell class]]) {
    //        JCHATMessageTableViewCell *cell = (JCHATMessageTableViewCell *)temp;
    //            if ([cell.model.message.msgId isEqualToString:msgId]) {
    //            cell.model = model;
    //            [cell layoutAllView];
    //        }
    //     }
    //}
    // 3.在cell 里面刷新
}
#pragma mark - 检查并刷新头像
- (void)chcekReceiveMessageAvatarWithReceiveNewMessage:(JMSGMessage *)message {
    DDLogDebug(@"chcekReceiveMessageAvatarWithReceiveNewMessage:%@",message.serverMessageId);
    if (!message || !message.fromUser) {
        return ;
    }
    
    JMSGMessage *lastMessage = message;
    JMSGUser *fromUser = lastMessage.fromUser;
    [fromUser thumbAvatarData:^(NSData *data, NSString *objectId, NSError *error) {
        if (error == nil && [objectId isEqualToString:fromUser.username]) {
            if (data != nil) {
                NSUInteger lenght = data.length;
                [self refreshVisibleRowsAvatarWithNewMessage:lastMessage avatarDataLength:lenght];
            }
        }
    }];
    //NSString *key = [NSString stringWithFormat:@"%@_%@",message.fromUser.username,message.fromUser.appKey];
    //NSMutableArray *messages = _refreshAvatarUsersDic[key];
    //if (messages.count > 0) {
    //      JMSGMessage *lastMessage = [messages lastObject];
    //      JMSGUser *fromUser = lastMessage.fromUser;
    //      [fromUser thumbAvatarData:^(NSData *data, NSString *objectId, NSError *error) {
    //      if (error == nil && [objectId isEqualToString:fromUser.username]) {
    //          if (data != nil) {
    //              NSUInteger lenght = data.length;
    //              [self refreshVisibleRowsAvatarWithNewMessage:lastMessage avatarDataLength:lenght];
    //          }
    //      }
    //      [_refreshAvatarUsersDic removeObjectForKey:key];
    //  }];
    //}
}

- (void)refreshVisibleRowsAvatarWithNewMessage:(JMSGMessage *)message avatarDataLength:(NSUInteger)length {
    
    DDLogDebug(@"refreshVisibleRowsAvatarWithNewMessage::%@",message.serverMessageId);
    
    NSString *username_appkey = [NSString stringWithFormat:@"%@_%@",message.fromUser.username,message.fromUser.appKey];
    NSString *msgId = message.msgId;
    
    NSArray *indexPaths = [[_messageTableView indexPathsForVisibleRows] mutableCopy];
    NSMutableArray *reloadIndexPaths = [NSMutableArray array];
    for (int i = 0; i < indexPaths.count; i++) {
        NSIndexPath *indexPath = indexPaths[i];
        JCHATMessageTableViewCell *cell = [_messageTableView cellForRowAtIndexPath:indexPath];
        JCHATChatModel *cellModel = cell.model;
        JMSGUser *cellUser = cell.model.message.fromUser;
        NSString *key = [NSString stringWithFormat:@"%@_%@",cellUser.username,cellUser.appKey];
        
        if (![username_appkey isEqualToString:key]) {
            continue ;
        }
        if (cellModel.avatarDataLength != length) {
            JMSGMessage *dbMessage = [self.conversation messageWithMessageId:msgId];
            JCHATChatModel *model = [_allMessageDic objectForKey:msgId];
            model.message = dbMessage;
            [_allMessageDic setObject:model forKey:msgId];
            [reloadIndexPaths addObject:indexPath];
        }
    }
    
    if (reloadIndexPaths.count > 0) {
        [_messageTableView reloadRowsAtIndexPaths:reloadIndexPaths withRowAnimation:UITableViewRowAnimationFade];
    }
}

- (void)reloadAllCellAvatarImage {
    DDLogDebug(@"Action -reloadAllCellAvatarImage");
    
    for (int i = 0; i < _allmessageIdArr.count; i++) {
        NSString *msgid = [_allmessageIdArr objectAtIndex:i];
        JCHATChatModel *model = [_allMessageDic objectForKey:msgid];
        if (model.message.isReceived && !model.message.fromUser.avatar) {
            JMSGMessage *message = [self.conversation messageWithMessageId:msgid];
            model.message = message;
            [_allMessageDic setObject:model forKey:msgid];
        }
    }
    
    NSArray *cellArray = [_messageTableView visibleCells];
    for (id temp in cellArray) {
        if ([temp isKindOfClass:[JCHATMessageTableViewCell class]]) {
            JCHATMessageTableViewCell *cell = (JCHATMessageTableViewCell *)temp;
            if (cell.model.message.isReceived) {
                [cell reloadAvatarImage];
            }
        }
    }
}

#pragma mark -PlayVoiceDelegate

- (void)successionalPlayVoice:(UITableViewCell *)cell indexPath:(NSIndexPath *)indexPath {
    if ([_allmessageIdArr count] - 1 > indexPath.row) {
        NSString *messageId = _allmessageIdArr[indexPath.row + 1];
        JCHATChatModel *model = _allMessageDic[ messageId];
        
        if (model.message.contentType == kJMSGContentTypeVoice && model.message.flag) {
            JCHATMessageTableViewCell *voiceCell =(JCHATMessageTableViewCell *)[self.messageTableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:indexPath.row + 1 inSection:0]];
            [voiceCell playVoice];
        }
    }
}

- (void)setMessageIDWithMessage:(JMSGMessage *)message chatModel:(JCHATChatModel * __strong *)chatModel index:(NSInteger)index {
    [_allMessageDic removeObjectForKey:(*chatModel).message.msgId];
    [_allMessageDic setObject:*chatModel forKey:message.msgId];
    
    if ([_allmessageIdArr count] > index) {
        [_allmessageIdArr removeObjectAtIndex:index];
        [_allmessageIdArr insertObject:message.msgId atIndex:index];
    }
}

#pragma mark - 头像点击事件代理
- (void)selectHeadView:(JCHATChatModel *)model {
    if (_isWhiteStyle) {
        return;
    }
    if (!model.message.fromUser) {
        [MBProgressHUD showMessage:@"该用户为API用户" toView:self.view];
        return;
    }
    if ([model.uidStr isEqual:@"dsp_admin_1"]||[model.uidStr isEqual:@"dsp_admin_2"]) {
        return;
    }
    YBCenterVC *center = [[YBCenterVC alloc]init];
    if (![model.message isReceived]) {
        //自己
        center.otherUid =[Config getOwnID];
    }else {
        //别人
        center.otherUid = model.uidStr;
    }
    center.isChatPage = YES;
    center.isPush = YES;
    center.hidesBottomBarWhenPushed = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];
}

#pragma mark -连续播放语音
- (void)getContinuePlay:(UITableViewCell *)cell
              indexPath:(NSIndexPath *)indexPath {
    JCHATMessageTableViewCell *tempCell = (JCHATMessageTableViewCell *) cell;
    if ([_allmessageIdArr count] - 1 > indexPath.row) {
        NSString *messageId = _allmessageIdArr[indexPath.row + 1];
        JCHATChatModel *model = _allMessageDic[ messageId];
        if (model.message.contentType == kJMSGContentTypeVoice && [model.message.flag isEqualToNumber:@(0)] && [model.message isReceived]) {
            if ([[JCHATAudioPlayerHelper shareInstance] isPlaying]) {
                tempCell.continuePlayer = YES;
            }else {
                tempCell.continuePlayer = NO;
            }
        }
    }
}

#pragma mark 预览图片 PictureDelegate
//PictureDelegate
- (void)tapPicture:(NSIndexPath *)index tapView:(UIImageView *)tapView tableViewCell:(UITableViewCell *)tableViewCell {
    
    [self.toolBarContainer.toolbar.textView resignFirstResponder];
    JCHATMessageTableViewCell *cell =(JCHATMessageTableViewCell *)tableViewCell;
    NSInteger count = _imgDataArr.count;
    NSMutableArray *photos = [NSMutableArray arrayWithCapacity:count];
    for (int i = 0; i<count; i++) {
        JCHATChatModel *messageObject = [_imgDataArr objectAtIndex:i];
        MJPhoto *photo = [[MJPhoto alloc] init];
        photo.message = messageObject;
        photo.srcImageView = tapView; // 来源于哪个UIImageView
        [photos addObject:photo];
    }
    MJPhotoBrowser *browser = [[MJPhotoBrowser alloc] init];
    browser.currentPhotoIndex = [_imgDataArr indexOfObject:cell.model];
    //  browser.currentPhotoIndex = cell.model.photoIndex; // 弹出相册时显示的第一张图片是？
    browser.photos = photos; // 设置所有的图片
    browser.conversation =_conversation;
    [browser show];
}

#pragma mark --获取所有发送消息图片
- (NSArray *)getAllMessagePhotoImg {
    NSMutableArray *urlArr =[NSMutableArray array];
    for (NSInteger i=0; i<[_allmessageIdArr count]; i++) {
        NSString *messageId = _allmessageIdArr[i];
        JCHATChatModel *model = _allMessageDic[messageId];
        if (model.message.contentType == kJMSGContentTypeImage) {
            [urlArr addObject:((JMSGImageContent *)model.message.content)];
        }
    }
    return urlArr;
}
#pragma mark SendMessageDelegate

- (void)didStartRecordingVoiceAction {
    DDLogVerbose(@"Action - didStartRecordingVoice");
    [self startRecord];
}

- (void)didCancelRecordingVoiceAction {
    DDLogVerbose(@"Action - didCancelRecordingVoice");
    [self cancelRecord];
}

- (void)didFinishRecordingVoiceAction {
    DDLogVerbose(@"Action - didFinishRecordingVoiceAction");
    [self finishRecorded];
}

- (void)didDragOutsideAction {
    DDLogVerbose(@"Action - didDragOutsideAction");
    [self resumeRecord];
}

- (void)didDragInsideAction {
    DDLogVerbose(@"Action - didDragInsideAction");
    [self pauseRecord];
}

- (void)pauseRecord {
    [self.voiceRecordHUD pauseRecord];
}

- (void)resumeRecord {
    [self.voiceRecordHUD resaueRecord];
}

- (void)cancelRecord {
    WEAKSELF
    [self.voiceRecordHUD cancelRecordCompled:^(BOOL fnished) {
        __strong __typeof(weakSelf)strongSelf = weakSelf;
        strongSelf.voiceRecordHUD = nil;
    }];
    [self.voiceRecordHelper cancelledDeleteWithCompletion:^{
        
    }];
}

#pragma mark - Voice Recording Helper Method
- (void)startRecord {
    DDLogDebug(@"Action - startRecord");
    [self.voiceRecordHUD startRecordingHUDAtView:self.view];
    [self.voiceRecordHelper startRecordingWithPath:[self getRecorderPath]  andVideo:NO  StartRecorderCompletion:^{
    }];
}

- (void)finishRecorded {
    DDLogDebug(@"Action - finishRecorded");
    WEAKSELF
    [self.voiceRecordHUD stopRecordCompled:^(BOOL fnished) {
        __strong __typeof(weakSelf)strongSelf = weakSelf;
        strongSelf.voiceRecordHUD = nil;
    }];
    [self.voiceRecordHelper stopRecordingWithStopRecorderCompletion:^{
        __strong __typeof(weakSelf)strongSelf = weakSelf;
        [strongSelf SendMessageWithVoice:strongSelf.voiceRecordHelper.recordPath
                           voiceDuration:strongSelf.voiceRecordHelper.recordDuration];
    }];
}

#pragma mark - Message Send helper Method
#pragma mark --发送语音
- (void)SendMessageWithVoice:(NSString *)voicePath
               voiceDuration:(NSString*)voiceDuration {
    DDLogDebug(@"Action - SendMessageWithVoice");
    
    if ([voiceDuration integerValue]<0.5 || [voiceDuration integerValue]>60) {
        if ([voiceDuration integerValue]<0.5) {
            DDLogDebug(@"录音时长小于 0.5s");
        } else {
            DDLogDebug(@"录音时长大于 60s");
        }
        return;
    }
    
    JMSGVoiceContent *voiceContent = [[JMSGVoiceContent alloc] initWithVoiceData:[NSData dataWithContentsOfFile:voicePath]
                                                                   voiceDuration:[NSNumber numberWithInteger:[voiceDuration integerValue]]];
    [self checkBlack:voiceContent voice:voicePath textContent:@""];
    
    //JMSGMessage *voiceMessage = nil;
    //voiceMessage = [_conversation createMessageWithContent:voiceContent];
    //[_conversation sendMessage:voiceMessage];
    //[self addmessageShowTimeData:voiceMessage.timestamp];
    //[model setChatModelWith:voiceMessage conversationType:_conversation userModel:_userModel];
    //[JCHATFileManager deleteFile:voicePath];
    //[self addMessage:model];
}

#pragma mark - 检查拉黑(只过滤文本消息关键词)
-(void)checkBlack:(JMSGAbstractContent *)content voice:(NSString *)voicePath textContent:(NSString *)textConten{
    if (voicePath.length>0) {
        [JCHATFileManager deleteFile:voicePath];
    }
    
    NSString *url = [NSString stringWithFormat:@"User.checkBlack&uid=%@&token=%@&touid=%@&content=%@",[Config getOwnID],[Config getOwnToken],_userModel.uidStr,textConten];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *isattent = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isattent"]];
            NSString *t2u = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"t2u"]];
            NSString *sendConten = minstr([infoDic valueForKey:@"content"]);
            NSString *p_switch = [NSString stringWithFormat:@"%@",[common private_letter_switch]];
            int p_nums = [[NSString stringWithFormat:@"%@",[common private_letter_nums]] intValue];
            //我发送的条数
            NSMutableArray *frends_cont = [NSMutableArray array];
            for (int i = 0; i < _allmessageIdArr.count; i++) {
                NSString *msgid = [_allmessageIdArr objectAtIndex:i];
                JCHATChatModel *model = [_allMessageDic objectForKey:msgid];
                if ([model.uidStr isEqual:[Config getOwnID]]) {
                    [frends_cont addObject:model];
                }
            }
            
            if ([t2u isEqual:@"0"]) {
                if ([isattent isEqual:@"0"] && [p_switch isEqual:@"1"] && frends_cont.count >= p_nums) {
                    [MBProgressHUD showPop:[NSString stringWithFormat:@"%@%d%@",YZMsg(@"对方未关注你,最多只能发送"),p_nums,YZMsg(@"条信息")]];
                }else {
                    JMSGMessage *message = nil;
                    if (![PublicObj checkNull:textConten]) {
                        //说明是文本消息
                        JMSGTextContent *textContentFormat = [[JMSGTextContent alloc] initWithText:sendConten];
                        message = [_conversation createMessageWithContent:textContentFormat];
                    }else{
                        message = [_conversation createMessageWithContent:content];
                    }
                    //[_conversation sendMessage:message];
                    JMSGOptionalContent *option = [[JMSGOptionalContent alloc] init];
                    option.noSaveNotification = YES;
                    [_conversation sendMessage:message optionalContent:option];
                    [self addmessageShowTimeData:message.timestamp];
                    JCHATChatModel *model =[[JCHATChatModel alloc] init];
                    [model setChatModelWith:message conversationType:_conversation userModel:_userModel];
                    [self addMessage:model];
                }
            }else {
                [MBProgressHUD showPop:YZMsg(@"对方暂时拒绝接收您的消息")];
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}

#pragma mark - RecorderPath Helper Method
- (NSString *)getRecorderPath {
    NSString *recorderPath = nil;
    NSDate *now = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yy-MMMM-dd";
    recorderPath = [[NSString alloc] initWithFormat:@"%@/Documents/", NSHomeDirectory()];
    dateFormatter.dateFormat = @"yyyy-MM-dd-hh-mm-ss";
    recorderPath = [recorderPath stringByAppendingFormat:@"%@-MySound.ilbc", [dateFormatter stringFromDate:now]];
    return recorderPath;
}

#pragma mark - Key-value Observing
- (void)observeValueForKeyPath:(NSString *)keyPath
                      ofObject:(id)object
                        change:(NSDictionary *)change
                       context:(void *)context {
    if (self.barBottomFlag) {
        return;
    }
    if (object == self.toolBarContainer.toolbar.textView && [keyPath isEqualToString:@"contentSize"]) {
        [self layoutAndAnimateMessageInputTextView:object];
    }
}


#pragma mark - UITextView Helper Method
- (CGFloat)getTextViewContentH:(UITextView *)textView {
    if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 7.0) {
        return ceilf([textView sizeThatFits:textView.frame.size].height);
    } else {
        return textView.contentSize.height;
    }
}

#pragma mark - Layout Message Input View Helper Method

//计算input textfield 的高度
- (void)layoutAndAnimateMessageInputTextView:(UITextView *)textView {
    CGFloat maxHeight = [JCHATToolBar maxHeight];
    
    CGFloat contentH = [self getTextViewContentH:textView];
    
    BOOL isShrinking = contentH < _previousTextViewContentHeight;
    CGFloat changeInHeight = contentH - _previousTextViewContentHeight;
    
    if (!isShrinking && (_previousTextViewContentHeight == maxHeight || textView.text.length == 0)) {
        changeInHeight = 0;
    }
    else {
        changeInHeight = MIN(changeInHeight, maxHeight - _previousTextViewContentHeight);
    }
    if (changeInHeight != 0.0f) {
        kWEAKSELF
        [UIView animateWithDuration:0.25f
                         animations:^{
            [weakSelf setTableViewInsetsWithBottomValue:_messageTableView.contentInset.bottom + changeInHeight];
            
            [weakSelf scrollToBottomAnimated:NO];
            
            if (isShrinking) {
                if ([[[UIDevice currentDevice] systemVersion] floatValue] < 7.0) {
                    _previousTextViewContentHeight = MIN(contentH, maxHeight);
                }
                // if shrinking the view, animate text view frame BEFORE input view frame
                [_toolBarContainer.toolbar adjustTextViewHeightBy:changeInHeight];
            }
            
            if (!isShrinking) {
                if ([[[UIDevice currentDevice] systemVersion] floatValue] < 7.0) {
                    weakSelf.previousTextViewContentHeight = MIN(contentH, maxHeight);
                }
                // growing the view, animate the text view frame AFTER input view frame
                [weakSelf.toolBarContainer.toolbar adjustTextViewHeightBy:changeInHeight];
            }
        }
                         completion:^(BOOL finished) {
        }];
        JCHATMessageTextView *textview =_toolBarContainer.toolbar.textView;
        CGSize textSize = [JCHATStringUtils stringSizeWithWidthString:textview.text withWidthLimit:textView.frame.size.width withFont:[UIFont systemFontOfSize:st_toolBarTextSize]];
        CGFloat textHeight = textSize.height > maxHeight?maxHeight:textSize.height;
        _toolBarHeightConstrait.constant = (textHeight + 8)>=45?(textHeight + 8):45;//!
        self.previousTextViewContentHeight = MIN(contentH, maxHeight);
    }
    
    // Once we reached the max height, we have to consider the bottom offset for the text view.
    // To make visible the last line, again we have to set the content offset.
    if (self.previousTextViewContentHeight == maxHeight) {
        double delayInSeconds = 0.01;
        dispatch_time_t popTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayInSeconds * NSEC_PER_SEC));
        dispatch_after(popTime,
                       dispatch_get_main_queue(),
                       ^(void) {
            CGPoint bottomOffset = CGPointMake(0.0f, contentH - textView.bounds.size.height);
            [textView setContentOffset:bottomOffset animated:YES];
        });
    }
}

- (void)inputTextViewDidChange:(JCHATMessageTextView *)messageInputTextView {
    //rk_20-6-28 去掉草稿
    //[[JCHATSendMsgManager ins] updateConversation:_conversation withDraft:messageInputTextView.text];
}

- (void)scrollToBottomAnimated:(BOOL)animated {
    if (![self shouldAllowScroll]) return;
    
    NSInteger rows = [self.messageTableView numberOfRowsInSection:0];
    
    if (rows > 0) {
        [self.messageTableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:[_allmessageIdArr count]-1 inSection:0] atScrollPosition:UITableViewScrollPositionBottom animated:animated];
    }
}

#pragma mark - Previte Method

- (BOOL)shouldAllowScroll {
    //if (self.isUserScrolling) {
    //  if ([self.delegate respondsToSelector:@selector(shouldPreventScrollToBottomWhileUserScrolling)]
    //      && [self.delegate shouldPreventScrollToBottomWhileUserScrolling]) {
    //          return NO;
    //      }
    //  }
    
    return YES;
}

#pragma mark - Scroll Message TableView Helper Method

- (void)setTableViewInsetsWithBottomValue:(CGFloat)bottom {
    //UIEdgeInsets insets = [self tableViewInsetsWithBottomValue:bottom];
    //self.messageTableView.contentInset = insets;
    //self.messageTableView.scrollIndicatorInsets = insets;
}

- (UIEdgeInsets)tableViewInsetsWithBottomValue:(CGFloat)bottom {
    UIEdgeInsets insets = UIEdgeInsetsZero;
    if ([self respondsToSelector:@selector(topLayoutGuide)]) {
        insets.top = 64;
    }
    insets.bottom = bottom;
    return insets;
}

#pragma mark - XHMessageInputView Delegate

- (void)inputTextViewWillBeginEditing:(JCHATMessageTextView *)messageInputTextView {
    _textViewInputViewType = JPIMInputViewTypeText;
}

- (void)inputTextViewDidBeginEditing:(JCHATMessageTextView *)messageInputTextView {
    if (!_previousTextViewContentHeight)
        _previousTextViewContentHeight = [self getTextViewContentH:messageInputTextView];
}

- (void)inputTextViewDidEndEditing:(JCHATMessageTextView *)messageInputTextView;
{
    if (!_previousTextViewContentHeight)
        _previousTextViewContentHeight = [self getTextViewContentH:messageInputTextView];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}
#pragma mark - 拉黑
-(void)setBlack {
    NSString *url = [NSString stringWithFormat:@"User.setBlack&uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_userModel.uidStr];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSString *infoDic = [info firstObject];
            NSString *isBlakc = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isblack"]];
            if ([isBlakc isEqual:@"1"]) {
                [MBProgressHUD showPop:YZMsg(@"拉黑成功")];
                
            }else {
                [MBProgressHUD showPop:YZMsg(@"解除拉黑成功")];
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
}
//clickRightMore
-(void)clickNaviRightBtn {
    NSString *url = [NSString stringWithFormat:@"User.checkBlack&uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_userModel.uidStr];
    YBWeakSelf;
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *u2t = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"u2t"]];
            //u2t  0-未拉黑  1-已拉黑
            NSString *blackTitle = YZMsg(@"拉黑");
            if ([u2t isEqual:@"1"]) {
                blackTitle = YZMsg(@"解除拉黑");
            }
            [weakSelf showSheetView:blackTitle];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
    
}

-(void)showSheetView:(NSString *)blackTitle {
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"举报") complete:^{
        PubH5 *h5vc = [[PubH5 alloc]init];
        h5vc.url =[h5url stringByAppendingString:[NSString stringWithFormat:@"/appapi/userreport/index?uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_userModel.uidStr]];
        [weakSelf.navigationController pushViewController:h5vc animated:YES];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:blackTitle complete:^{
        [weakSelf setBlack];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];
}

#pragma mark - 导航
- (void)clickNaviLeftBtn {
    if ([[JCHATAudioPlayerHelper shareInstance] isPlaying]) {
        [[JCHATAudioPlayerHelper shareInstance] stopAudio];
    }
    [self.navigationController popViewControllerAnimated:YES];
}

//聊天小窗口
- (void)reloadSamllChtaView:(NSString *)isatt{
    self.naviView.hidden = YES;
    UIView *smallNavi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 35)];
    smallNavi.backgroundColor = _isWhiteStyle?RGB_COLOR(@"#f9fafb", 1):CellRow_Cor;
    [self.view addSubview:smallNavi];
    if ([isatt isEqual:@"1"]) {
        [followView removeFromSuperview];
        followView = nil;
    }else{
        if (followView) {
            followView.y = 35;
        }
    }
    [self.moreViewContainer.moreView removeFromSuperview];
    UIButton *btn = [UIButton buttonWithType:0];
    btn.frame = CGRectMake(0, 0, 35, 35);
    [btn setImage:[UIImage imageNamed:@"gray_back"] forState:0];
    btn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
    [btn addTarget:self action:@selector(hideSmallView) forControlEvents:UIControlEventTouchUpInside];
    [smallNavi addSubview:btn];
    UILabel *titleLabel = [[UILabel alloc]initWithFrame:CGRectMake(35, 0, _window_width-70, 35)];
    titleLabel.font = [UIFont systemFontOfSize:13];
    titleLabel.textColor= RGB_COLOR(@"#636464", 1);
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.text = _userModel.unameStr;
    [smallNavi addSubview:titleLabel];
    self.messageTabTop.constant = 35;
    _messageTableView.frame = CGRectMake(0, 0, _window_width, _window_height*0.4-80);
    [self getGroupMemberListWithGetMessageFlag:YES];
    self.toolBarContainer.toolbar.voideWidth.constant -= 30;
    self.toolBarContainer.toolbar.addBtnWidth.constant -= 30;
    [_emojiV removeFromSuperview];
    _emojiV = nil;
    _emojiV = [[twEmojiView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, EmojiHeight)];
    _emojiV.delegate = self;
    _emojiV.isWhiteStyle = _isWhiteStyle;
    [[UIApplication sharedApplication].delegate.window addSubview:_emojiV];
}

- (void)hideSmallView{
    [_asrView.textView resignFirstResponder];
    //[[NSNotificationCenter defaultCenter] removeObserver:self];
    //[self.toolBarContainer.toolbar.textView removeObserver:self forKeyPath:@"contentSize"];
    ////remove delegate
    //[[NSNotificationCenter defaultCenter] removeObserver:self name:kAlertToSendImage object:self];
    //[JMessage removeDelegate:self withConversation:_conversation];
    [self dropToolBar];
    if (self.block) {
        self.block(0);
    }
}

@end
