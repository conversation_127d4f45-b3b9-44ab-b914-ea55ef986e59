//
//  commCell.h
//  yunbaolive
//
//  Created by Boom on 2018/12/17.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "commentModel.h"
#import <YYWebImage/YYWebImage.h>
#import "commDetailCell.h"
#import <HPGrowingTextView/HPGrowingTextView.h>

typedef void (^ComCellBlock)(void);

@protocol commCellDelegate <NSObject>

-(void)pushDetails:(NSDictionary *)commentdic andCell:(id)comcell;//跳回复列表

-(void)makeLikeRloadList:(NSString *)commectid andLikes:(NSString *)likes islike:(NSString *)islike;
- (void)reloadCurCell:(commentModel *)model andIndex:(NSIndexPath *)curIndex andReplist:(NSArray *)list needRefresh:(BOOL)needRefresh;

- (void)playVoice:(commentModel *)model andCell:(id)comcell;

- (void)playDetailesVoice:(detailmodel *)model andCell:(commDetailCell *)comcell;
-(void)endEditOfGoHome;
-(void)longPressing;
@end

NS_ASSUME_NONNULL_BEGIN

@interface commCell : UITableViewCell<UITableViewDelegate,UITableViewDataSource>
@property (weak, nonatomic) IBOutlet UIView *longPressView;
@property (weak, nonatomic) IBOutlet UIImageView *iconImgView;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UILabel *authorL;//作者
@property (weak, nonatomic) IBOutlet UILabel *contentL;
@property (weak, nonatomic) IBOutlet UIButton *zanBtn;
@property (weak, nonatomic) IBOutlet UILabel *zanNumL;
@property (weak, nonatomic) IBOutlet UITableView *replyTable;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tableHeight;
@property(nonatomic,strong)NSMutableArray *replyArray;
@property(nonatomic,strong)UIButton *Reply_Button;//回复
@property(nonatomic,strong)UIView *replyBottomView;//回复

@property(nonatomic,strong)NSIndexPath *curIndex;//回复
@property (nonatomic,assign) BOOL isNoMore;//判断是不是没有更多了
@property(nonatomic,strong)NSString *authorID;//视频作者的id
@property(nonatomic,copy)NSString *videoid;//视频id
@property(nonatomic,strong)commentModel *model;
@property(nonatomic,assign)id<commCellDelegate>delegate;

@property (weak, nonatomic) IBOutlet UIView *voiceView;
@property (weak, nonatomic) IBOutlet UIImageView *voiceNorImgV;
@property (weak, nonatomic) IBOutlet UILabel *voiceTimeL;
@property (weak, nonatomic) IBOutlet YYAnimatedImageView *animationView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *voiceViewHeight;
@property(nonatomic,copy)ComCellBlock comCellEvent;
@property(nonatomic,strong)HPGrowingTextView *toolTextView;
- (void)requestData:(BOOL)flag andBtn:(UIButton *)replyBtn;

@end

NS_ASSUME_NONNULL_END
