//
//  commCell.m
//  yunbaolive
//
//  Created by Boom on 2018/12/17.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "commCell.h"
#import "commDetailCell.h"
#import "detailmodel.h"
#import "YBCenterVC.h"
@interface commCell ()<detailDelegate>
@property (nonatomic,strong) NSURL *anImgUrl;

@end

@implementation commCell{
    int page;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.contentView.backgroundColor = [UIColor clearColor];
    page = 1;
    _authorL.text = YZMsg(@"作者");
    
    //长按
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPress:)];
    [_replyTable addGestureRecognizer:longPress];
    
}
-(void)longPress:(UILongPressGestureRecognizer*)longPressGesture {
    if (_replyArray.count <= 0) {
        return;
    }
    if (_delegate && [_delegate respondsToSelector:@selector(longPressing)] && [_toolTextView isFirstResponder]) {
        [_delegate longPressing];
        return;
    }
    commDetailCell *cell;
    if (longPressGesture.state == UIGestureRecognizerStateBegan) {
        CGPoint point = [longPressGesture locationInView:_replyTable];
        NSIndexPath *currentIndexPath = [_replyTable indexPathForRowAtPoint:point];
        if (currentIndexPath) {
            cell = [_replyTable cellForRowAtIndexPath:currentIndexPath];
        }
        cell.longPressView.backgroundColor = RGB_COLOR(@"#ffffff", 0.1);
        [self showActionSheet:currentIndexPath model:cell.model];
    }
    if (longPressGesture.state == UIGestureRecognizerStateChanged || longPressGesture.state == UIGestureRecognizerStateEnded) {
        CGPoint point = [longPressGesture locationInView:_replyTable];
        NSIndexPath *currentIndexPath = [_replyTable indexPathForRowAtPoint:point];
        if (currentIndexPath) {
            cell = [_replyTable cellForRowAtIndexPath:currentIndexPath];
        }
        cell.longPressView.backgroundColor = UIColor.clearColor;
    }
    
}
-(void)showActionSheet:(NSIndexPath*)indexPath model:(detailmodel *)dModel{
    
    if ([[Config getOwnID] intValue]<=0 && self.comCellEvent) {
        self.comCellEvent();
        return;
    }
    
    NSDictionary *subdic = _replyArray[indexPath.row];
    NSDictionary *userinfo = [subdic valueForKey:@"userinfo"];
    BOOL isVoice = [dModel.isvoice boolValue];
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    if (!isVoice) {
        [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"复制") complete:^{
            dispatch_async(dispatch_get_main_queue(), ^{
                UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                pasteboard.string = [NSString stringWithFormat:@"@%@:%@",dModel.user_nickname,dModel.content];
                [MBProgressHUD showPop:YZMsg(@"复制成功")];
            });
        }];
    }
    BOOL haveDel = NO;
    if ([_authorID isEqual:[Config getOwnID]] || [minstr([userinfo valueForKey:@"id"]) isEqual:[Config getOwnID]]) {
        haveDel = YES;
        [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"删除") complete:^{
            [weakSelf delComments:subdic index:indexPath];
        }];
    }
    if (!isVoice || haveDel) {
        [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
        }];
        [sheet showSheet];
    }
    
}
#pragma mark - 删除评论接口
-(void)delComments:(NSDictionary *)subdic index:(NSIndexPath *)indexPath{
    NSString *commentid = minstr([subdic valueForKey:@"id"]);
    NSDictionary *userinfo = [subdic valueForKey:@"userinfo"];
    NSString *commentUid = minstr([userinfo valueForKey:@"id"]);
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Video.delComments" Dic:@{@"videoid":_videoid,@"commentid":commentid,@"commentuid":commentUid} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showPop:msg];
        if (code == 0) {
            [self requestData:YES andBtn:_model.replyMoreBtn];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}


- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(void)setAuthorID:(NSString *)authorID {
    _authorID = authorID;
}
- (NSURL *)anImgUrl{
    if (!_anImgUrl) {
        _anImgUrl = [[NSBundle mainBundle] URLForResource:@"voice_pinglun" withExtension:@"gif"];
    }
    return _anImgUrl;
}
- (void)setModel:(commentModel *)model{
    _model = model;
    _authorL.hidden = YES;
    if ([_authorID isEqual:_model.ID]) {
        _authorL.hidden = NO;
    }
    if ([_model.isvoice isEqual:@"1"]) {
        _voiceViewHeight.constant = 20;
        _voiceView.hidden = NO;
        _animationView.hidden = YES;
        _animationView.yy_imageURL = self.anImgUrl;
        _voiceTimeL.text = [NSString stringWithFormat:@"%@\"",model.voiceTime];
    }else{
        _voiceViewHeight.constant = 0;
        _voiceView.hidden = YES;
    }
    NSLog(@"_replyArray=%@",_model.replyList);
    _replyArray = _model.replyList.mutableCopy;
    [_iconImgView sd_setImageWithURL:[NSURL URLWithString:_model.avatar_thumb]];
    _nameL.text = _model.user_nickname;
//    _contentL.text = _model.content;
    _zanNumL.text = _model.likes;
    if ([_model.islike isEqual:@"1"]) {
        [_zanBtn setImage:[UIImage imageNamed:@"likecomment-click"] forState:0];
        _zanNumL.textColor = Pink_Cor;
    }else{
        [_zanBtn setImage:[UIImage imageNamed:@"likecomment"] forState:0];
        _zanNumL.textColor = RGB_COLOR(@"#828282", 1);
    }
    //匹配表情文字
    NSArray *resultArr  = [PublicObj machesWithPattern:emojiPattern andStr:_model.content];
    if (!resultArr) return;
    NSUInteger lengthDetail = 0;
    NSMutableAttributedString *attstr = [[NSMutableAttributedString alloc]initWithString:_model.content];
    //遍历所有的result 取出range
    for (NSTextCheckingResult *result in resultArr) {
        //取出图片名
        NSString *imageName =   [_model.content substringWithRange:NSMakeRange(result.range.location, result.range.length)];
        if ([lagType isEqual:EN]) {
            if ([imageName isEqual:@"[互粉]"] ||
                [imageName isEqual:@"[关注]"] ||
                [imageName isEqual:@"[粉]"] ||
                [imageName isEqual:@"[给力]"]) {
                imageName = [imageName stringByAppendingFormat:@"_en"];
            }
        }
        
        NSLog(@"--------%@",imageName);
        NSTextAttachment *attach = [[NSTextAttachment alloc] init];
        UIImage *emojiImage = [UIImage imageNamed:imageName];
        NSAttributedString *imageString;
        if (emojiImage) {
            attach.image = emojiImage;
            attach.bounds = CGRectMake(0, -2, 15, 15);
            imageString =   [NSAttributedString attributedStringWithAttachment:attach];
        }else{
            imageString =   [[NSMutableAttributedString alloc]initWithString:imageName];
        }
        //图片附件的文本长度是1
        NSLog(@"emoj===%zd===size-w:%f==size-h:%f",imageString.length,imageString.size.width,imageString.size.height);
        NSUInteger length = attstr.length;
        NSRange newRange = NSMakeRange(result.range.location - lengthDetail, result.range.length);
        [attstr replaceCharactersInRange:newRange withAttributedString:imageString];
        
        lengthDetail += length - attstr.length;
    }
    NSAttributedString *dateStr = [[NSAttributedString alloc]initWithString:[NSString stringWithFormat:@" %@",_model.datetime] attributes:@{NSForegroundColorAttributeName:RGB_COLOR(@"#969696", 1),NSFontAttributeName:[UIFont systemFontOfSize:11]}];
    
    [attstr appendAttributedString:dateStr];
    //不同字体居中对齐、基线偏移比率
    [attstr addAttribute:NSBaselineOffsetAttributeName value:@(0.36 * (14 - 11)) range:NSMakeRange(attstr.length - _model.datetime.length, _model.datetime.length)];
   
    //更新到label上
    [_replyTable reloadData];

    
    //给被@加颜色
    NSData *mix_data = [_model.at_info dataUsingEncoding:NSUTF8StringEncoding];
    NSArray *atArray = [NSJSONSerialization JSONObjectWithData:mix_data options:NSJSONReadingAllowFragments error:nil];
    if (atArray.count>0) {
        //有@
        for (int i=0; i<atArray.count; i++) {
            NSDictionary *u_dic = atArray[i];
            NSString *uname = [NSString stringWithFormat:@"@%@",[u_dic valueForKey:@"name"]];
            if ([_model.content containsString:uname]) {
                NSRange uRang = [_model.content rangeOfString:uname];
                [attstr addAttribute:NSForegroundColorAttributeName value:AtCol range:uRang];
            }
        }
    }
    
    _contentL.attributedText = attstr;

    if ([_model.replys intValue] > 0) {

        CGFloat HHHH = 0.0;
        for (NSDictionary *dic in _replyArray) {
            detailmodel *model = [[detailmodel alloc]initWithDic:dic];
            HHHH += model.rowH;
        }
        if ([_model.replys intValue] == 1) {
            _tableHeight.constant = HHHH;
            _replyTable.tableFooterView = nil;
        }else{
//            if (!_replyBottomView) {
                NSLog(@"===%d",page);
                _model.replayView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 100, 20)];
                _model.replayView.backgroundColor = CellRow_Cor;
                //回复
                UIButton * replyBtn = [UIButton buttonWithType:0];
                replyBtn.backgroundColor = [UIColor clearColor];
                replyBtn.titleLabel.textAlignment = NSTextAlignmentLeft;
                replyBtn.titleLabel.font = [UIFont systemFontOfSize:12];
                [replyBtn addTarget:self action:@selector(makeReply:) forControlEvents:UIControlEventTouchUpInside];
                BOOL showNum = NO;
                if (_model.replyList.count==1) {
                    showNum = YES;
                }
                [self changeMoreShowText:showNum andBtn:replyBtn];
                /*
                NSString *tempStr = [NSString stringWithFormat:@"展开%d条回复",[_model.replys intValue]>=2?([_model.replys intValue]-1):(1)];
                NSMutableAttributedString *attstr = [[NSMutableAttributedString alloc]initWithString:tempStr];
                [attstr addAttribute:NSForegroundColorAttributeName value:RGB_COLOR(@"#969696", 1) range:NSMakeRange(0, tempStr.length)];
                NSTextAttachment *attach = [[NSTextAttachment alloc] init];
                UIImage *image = [UIImage imageNamed:@"relpay_三角下.png"];
                NSAttributedString *imageString;
                if (image) {
                    attach.image = image;
                    attach.bounds = CGRectMake(0, -4, 15, 15);
                    imageString =   [NSAttributedString attributedStringWithAttachment:attach];
                    [attstr appendAttributedString:imageString];
                }
                [_Reply_Button setAttributedTitle:attstr forState:0];
                */
                NSMutableAttributedString *attstr2 = [[NSMutableAttributedString alloc]initWithString:YZMsg(@"收起")];
                [attstr2 addAttribute:NSForegroundColorAttributeName value:RGB_COLOR(@"#969696", 1) range:NSMakeRange(0, 2)];
                NSTextAttachment *attach2 = [[NSTextAttachment alloc] init];
                UIImage *image2 = [UIImage imageNamed:@"relpay_三角上.png"];
                NSAttributedString *imageString2;
                if (image2) {
                    attach2.image = image2;
                    attach2.bounds = CGRectMake(0, -4, 15, 15);
                    imageString2 =   [NSAttributedString attributedStringWithAttachment:attach2];
                    [attstr2 appendAttributedString:imageString2];
                }
                [replyBtn setAttributedTitle:attstr2 forState:UIControlStateSelected];
                [_model.replayView addSubview:replyBtn];
                //记录按钮属性
                _model.replyMoreBtn = replyBtn;
                [replyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.bottom.equalTo(_model.replayView);
                    make.left.equalTo(_model.replayView).offset(26);
                }];
            NSLog(@"list：%lu==replays：%@",(unsigned long)_model.replyList.count,_model.replys);
                //rk_3-2
                if ((_model.replyList.count % 10 != 0
                     && _model.replyList.count % 10 != 1
                     && _model.replyList.count % 10 != 2
                     && _model.replyList.count% 10 != 3)
                     || ((_model.replyList.count)== [_model.replys intValue])
                    ) {
                    replyBtn.selected = YES;
                }else{
                    replyBtn.selected = NO;
                }
//            replyBtn.selected = NO;
//            if (_model.replyList.count % 10 != 0) {
//                replyBtn.selected = YES;
//            }
//            if (_model.replyList.count == 2) {
//                replyBtn.selected = YES;
//            }
//            if (_model.replyList.count == 3) {
//                replyBtn.selected = YES;
//            }
            
//            }
            _replyTable.tableFooterView = _model.replayView;
            _tableHeight.constant = HHHH+20;
        }

    }else{
        _tableHeight.constant = 0;
        _replyTable.tableFooterView = nil;
    }


}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return _replyArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    commDetailCell *cell = [tableView dequeueReusableCellWithIdentifier:@"commDetailCELL"];
    if (!cell) {
        cell = [[commDetailCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"commDetailCell"];
    }
    detailmodel *model = [[detailmodel alloc]initWithDic:_replyArray[indexPath.row]];
    cell.authorID = _authorID;
    cell.model = model;
    cell.delegate = self;
    cell.selectionStyle  = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = CellRow_Cor;
    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    detailmodel *model = [[detailmodel alloc]initWithDic:_replyArray[indexPath.row]];
    return model.rowH;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    NSDictionary *subdic = _replyArray[indexPath.row];
    if ([[Config getOwnID] intValue]<=0 && self.comCellEvent) {
        self.comCellEvent();
        return;
    }
    [self.delegate pushDetails:subdic andCell:self];
}

- (void)makeReply:(UIButton *)replyBtn{
    if (replyBtn.selected) {
        NSDictionary *dic = [_replyArray firstObject];
        [_replyArray removeAllObjects];
        [_replyArray addObject:[dic mutableCopy]];
        _model.replyList = [_replyArray mutableCopy];
//        [_replyTable reloadData];
        replyBtn.selected = NO;
        [self changeMoreShowText:YES andBtn:replyBtn];
        [self.delegate reloadCurCell:_model andIndex:_curIndex andReplist:_replyArray needRefresh:YES];

    }else{
        
        if (_replyArray.count == 1) {
            page = 1;
        }else{
            page ++;
        }
        [self requestData:NO andBtn:replyBtn];
    }
}
-(void)changeMoreShowText:(BOOL)isFirstPage andBtn:(UIButton *)replyBtn{
    NSString *tempStr;
    if (isFirstPage) {
        tempStr  = [NSString stringWithFormat:@"%@%d%@",YZMsg(@"展开"),[_model.replys intValue]>=2?([_model.replys intValue]-1):(1),YZMsg(@"条回复")];
    }else{
        tempStr  = [NSString stringWithFormat:@"%@",YZMsg(@"展开更多回复")];
    }
    NSMutableAttributedString *attstr = [[NSMutableAttributedString alloc]initWithString:tempStr];
    [attstr addAttribute:NSForegroundColorAttributeName value:RGB_COLOR(@"#969696", 1) range:NSMakeRange(0, tempStr.length)];
    NSTextAttachment *attach = [[NSTextAttachment alloc] init];
    UIImage *image = [UIImage imageNamed:@"relpay_三角下.png"];
    NSAttributedString *imageString;
    if (image) {
        attach.image = image;
        attach.bounds = CGRectMake(0, -4, 15, 15);
        imageString =   [NSAttributedString attributedStringWithAttachment:attach];
        [attstr appendAttributedString:imageString];
    }
    [replyBtn setAttributedTitle:attstr forState:0];
}

- (void)requestData:(BOOL)flag andBtn:(UIButton *)replyBtn{
    NSString *last_replyid = @"0";
    NSDictionary *dic = [_replyArray lastObject];
    if ([dic isKindOfClass:[NSDictionary class]] && flag == NO) {
        last_replyid = [NSString stringWithFormat:@"%@",[dic valueForKey:@"id"]];
    }
    if (flag) {
        //有新回复，pag重置
        page = 1;
    }
    NSString *url = [NSString stringWithFormat:@"Video.getReplys&commentid=%@&p=%d&uid=%@&last_replyid=%@",_model.parentid,page,[Config getOwnID],last_replyid];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [self changeMoreShowText:NO andBtn:replyBtn];
            NSDictionary *infoDic = [info lastObject];
            if ([last_replyid isEqual:@"0"]) {
                //自己回复了评论，重置replys的值
                _model.replys = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"replys"]];
            }
            NSArray *infoA = [infoDic valueForKey:@"lists"];
            //rk_2-27
            //if (page == 1 && infoA.count>0) {
            //   [infoA removeObjectAtIndex:0];
            //}
            if (page == 1 && flag == YES) {
                [_replyArray removeAllObjects];
            }
            for (NSDictionary *dic in infoA) {
                [_replyArray addObject:[dic mutableCopy]];
            }
            _model.replyList = [_replyArray mutableCopy];
            //[_replyTable reloadData];
            //rk_3-2
            if (infoA.count <= 0 ) {
                replyBtn.selected = YES;
            }
            [self.delegate reloadCurCell:_model andIndex:_curIndex andReplist:_replyArray needRefresh:YES];
            
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];

}
- (void)endEditOfGoHome {
    [self endEditing:YES];
}
#pragma mark - 评论的回复的赞
-(void)refreshDtailDataWithId:(NSString *)commectid andLikes:(NSString *)likes islike:(NSString *)islike {
    int numbers = 0;
    for (int i=0; i<_model.replyList.count; i++) {
        NSMutableDictionary *subdic = _model.replyList[i];
        NSString *parentid = [NSString stringWithFormat:@"%@",[subdic valueForKey:@"id"]];
        if ([parentid isEqual:commectid]) {
            [subdic setObject:likes forKey:@"likes"];
            [subdic setObject:islike forKey:@"islike"];

            numbers = i;
            //更改源数据，不要刷新cell（点赞接口返回处，已经处理了当前页面显示数据，这里只需修改源数据即可）
            [self.delegate reloadCurCell:_model andIndex:[NSIndexPath indexPathForRow:numbers inSection:0] andReplist:_model.replyList needRefresh:NO];

            break;
        }

    }
}

- (IBAction)zanBtnClick:(id)sender {

    if ([[Config getOwnID] intValue]<=0 && self.comCellEvent) {
        self.comCellEvent();
        return;
    }
    if ([_model.ID isEqual:[Config getOwnID]]) {
        [MBProgressHUD showPop:YZMsg(@"不能给自己的评论点赞")];
        return;
    }
    //_bigbtn.userInteractionEnabled = NO;
    NSString *url = [NSString stringWithFormat:@"Video.addCommentLike&uid=%@&commentid=%@&token=%@",[Config getOwnID],_model.parentid,[Config getOwnToken]];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            //动画
            dispatch_async(dispatch_get_main_queue(), ^{
                [_zanBtn.imageView.layer addAnimation:[PublicObj bigToSmallRecovery] forKey:nil];
            });
            
            NSDictionary *infoDic = [info firstObject];
            NSString *islike = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"islike"]];
            NSString *likes = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"likes"]];
            
            _zanNumL.text = likes;
            if ([islike isEqual:@"1"]) {
                [_zanBtn setImage:[UIImage imageNamed:@"likecomment-click"] forState:0];
                _zanNumL.textColor = Pink_Cor;
            }else{
                [_zanBtn setImage:[UIImage imageNamed:@"likecomment"] forState:0];
                _zanNumL.textColor = RGB_COLOR(@"#828282", 1);
            }
            [self.delegate makeLikeRloadList:_model.parentid andLikes:likes islike:islike];
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
    
}
- (IBAction)playVoice:(id)sender {
    [self.delegate playVoice:_model andCell:self];
}
- (void)playVoice:(detailmodel *)model andCell:(id)comcell{
    [self.delegate playDetailesVoice:model andCell:comcell];
}
- (void)warningLogin{
    if (self.comCellEvent) {
         self.comCellEvent();
    }
}

- (IBAction)clickIconBtn:(id)sender {
    if ([[Config getOwnID] intValue]<=0 && self.comCellEvent) {
        self.comCellEvent();
        return;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(endEditOfGoHome)]) {
        [self.delegate endEditOfGoHome];
    }
    [[NSNotificationCenter defaultCenter]postNotificationName:@"goCenterNot" object:@"1"];
    YBCenterVC *center = [[YBCenterVC alloc]init];
    center.otherUid = _model.ID;
    center.isPush = YES;
    center.hidesBottomBarWhenPushed = YES;
    center.backEvent = ^{
        [[NSNotificationCenter defaultCenter]postNotificationName:@"goCenterNot" object:@"0"];
    };
    [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];
}

@end
