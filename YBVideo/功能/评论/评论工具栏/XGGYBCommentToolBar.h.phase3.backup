//
//  YBCommentToolBar.h
//  YBVideo
//
//  Created by YB007 on 2019/11/21.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "SelPeopleV.h"
#import "twEmojiView.h"
#import <HPGrowingTextView/HPGrowingTextView.h>
#import <ZFPlayer/ZFPlayer.h>

typedef void (^YBComToolBlock)(NSString *eventType,NSDictionary *eventDic);

@interface YBCommentToolBar : UIView

@property(nonatomic,copy)YBComToolBlock comToolEvent;

@property(nonatomic,assign)BOOL isPrepareEditing;
@property(nonatomic,strong)NSString *videoid;
@property(nonatomic,strong)NSString *hostid;
@property(nonatomic,strong)HPGrowingTextView *textField;
@property(nonatomic,strong)NSMutableArray *atArray;
@property(nonatomic,strong)UIButton *faceBtn;
@property(nonatomic,strong)UIButton *voiceBtn;
@property(nonatomic,strong)UIButton *voiceRecodeBtn;
@property(nonatomic,strong)NSString *voiceDution;
@property(nonatomic,strong)SelPeopleV *selV;
@property(nonatomic,strong)twEmojiView *emojiV;
@property (nonatomic, strong) ZFPlayerController *player;

/** 注意！！！
 *  vcType 0 观看页面(YBLookVideoVC)  1 评论页面(commentview)
 */
- (instancetype)initWithFrame:(CGRect)frame andVCType:(int)vcType superView:(UIView *)superView;
-(void)hiddenEmojiOrKeyBoard;
-(void)resetToolState;
@end


