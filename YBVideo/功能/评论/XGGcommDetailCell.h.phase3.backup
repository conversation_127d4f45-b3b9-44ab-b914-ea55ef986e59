//
//  commDetailCell.h
//  yunbaolive
//
//  Created by Boom on 2018/12/17.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "detailmodel.h"
#import <YYWebImage/YYWebImage.h>


@protocol detailDelegate <NSObject>

-(void)warningLogin;
//刷新评论的回复数据
-(void)refreshDtailDataWithId:(NSString *)commectid andLikes:(NSString *)likes islike:(NSString *)islike;
- (void)playVoice:(detailmodel *)model andCell:(id)comcell;
-(void)endEditOfGoHome;
@end

NS_ASSUME_NONNULL_BEGIN

@interface commDetailCell : UITableViewCell
@property(nonatomic,strong)UIView *longPressView;
@property(nonatomic,strong)UIImageView *iconIV;
@property (strong, nonatomic)  UILabel *nameL;
@property(nonatomic,strong)UILabel *authorL;//作者
@property(nonatomic,strong)UIButton *zanBtn;
@property(nonatomic,strong)UILabel *zanL;
@property (strong, nonatomic)  UILabel *contentL;
@property(nonatomic,strong)NSString *authorID;//视频作者的id
@property(nonatomic,strong)detailmodel *model;
@property(nonatomic,assign)id<detailDelegate>delegate;

@property (strong, nonatomic)  UIView *voiceView;
@property (strong, nonatomic)  UIImageView *voiceNorImgV;
@property (strong, nonatomic)  UILabel *voiceTimeL;
@property (strong, nonatomic)  YYAnimatedImageView *animationView;
@property (strong, nonatomic)  NSLayoutConstraint *voiceViewHeight;

@end

NS_ASSUME_NONNULL_END
