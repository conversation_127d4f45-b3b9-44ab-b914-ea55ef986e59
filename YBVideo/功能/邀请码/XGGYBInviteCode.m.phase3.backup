//
//  YBInviteCode.m
//  YBVideo
//
//  Created by YB007 on 2019/12/19.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBInviteCode.h"
#import "YBInvitationView.h"
#import "OpenInstallSDK.h"

@implementation YBInviteCode
+(void)saveAgentSwitch:(NSString *)save;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"dsp_agent_switch"];
    [userDefaults synchronize];
}
+(NSString *)getAgentSwitch;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"dsp_agent_switch"];
    return getStr;
}

+(void)saveHasAgent:(NSString *)save{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"dsp_has_agent"];
    [userDefaults synchronize];

}
+(NSString *)getHasAgent{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"dsp_has_agent"];
    return getStr;
}
+(void)saveAgentMust:(NSString *)save{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"dsp_agent_must"];
    [userDefaults synchronize];

}
+(NSString *)getAgentMust{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"dsp_agent_must"];
    return getStr;
}
+(void)saveCodeinstallSwitch:(NSString *)save{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"dsp_codeinstall_switch"];
    [userDefaults synchronize];

}
+(NSString *)getCodeinstallSwitch{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"dsp_codeinstall_switch"];
    return getStr;
}

#pragma mark - 邀请码填写
+(void)checkAgent {
    if ([[Config getOwnID] intValue]<=0) {
        return;
    }
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Agent.checkAgent" Dic:@{} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *agent_switch = minstr([infoDic valueForKey:@"agent_switch"]);
            [weakSelf saveAgentSwitch:agent_switch];
//            int agent_must = [minstr([infoDic valueForKey:@"agent_must"]) intValue];
            NSString *has_agent = minstr([infoDic valueForKey:@"has_agent"]);
            [weakSelf saveHasAgent:has_agent];
            
            NSString *agent_must = minstr([infoDic valueForKey:@"agent_must"]);
            [weakSelf saveAgentMust:agent_must];
            
            NSString *codeinstall_switch =minstr([infoDic valueForKey:@"openinstall_switch"]);
            [weakSelf saveCodeinstallSwitch:codeinstall_switch];
            /*
             has_agent 是否填写过邀请码
             agent_switch 手填邀请码开关
             openinstall_switch openInstall 邀请码开关
             
             当has_agent=1.填写过邀请码，直接略过
             当has_agent !=1 判断codeinstall三方开关，当codeinstall开启时直接走免填SDK，当获取不到codeInstall的邀请码时，判断手填开关有没有开启，如果开启弹邀请码框，否则不弹。
             当codeinstall关闭时判断手填开关有没有开启，当不需要手填邀请码时直接略过，否则弹手填邀请码框
             
            */
            
            NSString *is_firstlogin = minstr([infoDic valueForKey:@"is_firstlogin"]);
            if (![is_firstlogin isEqual:@"1"] && ![agent_must isEqual:@"1"]) {
                return;
            }
            
            
            if ([has_agent isEqual:@"1"]|| [agent_switch isEqual:@"0"]) {
                [Config saveisreg:@"0"];
            }else{
                if([codeinstall_switch isEqual:@"1"]){
                    [Config saveisreg:@"0"];
                    [self showCodeInstall];
                }else{
                    if ([agent_switch isEqual:@"0"]) {
                        [Config saveisreg:@"0"];
                    }else{
                        if ([[self getAgentMust]isEqual:@"1"]) {
                            [self showInvitationView:YES];
                        }else{
                            [self showInvitationView:NO];

                        }
                    }
                    
                }

            }
        }
    } Fail:^(id fail) {
        
    }];
}
+(void)showInvitationView:(BOOL)isForce {
    YBInvitationView *invitationV = [[YBInvitationView alloc]initWithType:isForce];
    [[UIApplication sharedApplication].delegate.window addSubview:invitationV];
    [PublicObj layoutWindowPopLayer];
}

+(void)showCodeInstall{
    YBWeakSelf;
    [[OpenInstallSDK defaultManager] getInstallParmsCompleted:^(OpeninstallData*_Nullable appData) {
        //在主线程中回调
        if (appData.data) {//(动态安装参数)
           //e.g.如免填邀请码建立邀请关系、自动加好友、自动进入某个群组或房间等
            [weakSelf uploadInvitationV:minstr([appData.data valueForKey:@"code"])];
            [Config saveisreg:@"0"];
        }else {
            if ([[self getAgentSwitch] isEqual:@"0"]) {
                [Config saveisreg:@"0"];
            }else{
                if ([[self getAgentSwitch]isEqual:@"1"] && [[self getAgentMust]isEqual:@"1"]) {
                    [self showInvitationView:YES];
                }else{
                    [self showInvitationView:NO];

                }

            }
        }
        if (appData.channelCode) {//(通过渠道链接或二维码安装会返回渠道编号)
            //e.g.可自己统计渠道相关数据等
        }
        NSLog(@"OpenInstallSDK:\n动态参数：%@;\n渠道编号：%@",appData.data,appData.channelCode);
    }];
}
+(void)uploadInvitationV:(NSString *)codeStr{
    
    [YBNetworking postWithUrl:@"Agent.setAgent" Dic:@{@"agentcode":codeStr} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
        }
        [MBProgressHUD showPop:msg];
    } Fail:^(id fail) {
        
    }];
    
}

@end
