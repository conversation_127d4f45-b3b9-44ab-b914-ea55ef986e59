//
//  TConversationCell.m
//  UIKit
//
//  Created by kenneth<PERSON><PERSON> on 2018/9/14.
//  Copyright © 2018年 kennethmiao. All rights reserved.
//

#import "TConversationCell.h"
#import "THeader.h"
#import "YBCenterVC.h"

@implementation TConversationCellData

// 更新信息
-(void)updateUserInfo:(NSDictionary *)userInfo;{
    _userName = strFormat([userInfo valueForKey:@"user_nickname"]);
    _userHeader = strFormat([userInfo valueForKey:@"avatar"]);

    _isAtt = strFormat([userInfo valueForKey:@"isattent"]);
    _isVIP = strFormat([[userInfo valueForKey:@"vipinfo"] valueForKey:@"isvip"]);
    // admin重置时间和最后一条消息
    if ([_convId isEqual:@"dsp_admin_1"] || [_convId isEqual:@"dsp_admin_2"]) {
        _time = strFormat([userInfo valueForKey:@"last_time"]);
        _subTitle = strFormat([userInfo valueForKey:@"last_msg"]);
    }
    
}

@end

@interface TConversationCell ()
@property (nonatomic, strong) TConversationCellData *data;
@end

@implementation TConversationCell
- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    if(self){
        [self setupViews];
        [self defaultLayout];
    }
    return self;
}

+ (CGSize)getSize;
{
    return CGSizeMake(Screen_Width, TConversationCell_Height);
}

- (void)setData:(TConversationCellData *)data
{
    _data = data;
    if (![PublicObj checkNull:_data.userHeader]) {
        [_headImageView sd_setImageWithURL:[NSURL URLWithString:_data.userHeader]];
    }else{
        _headImageView.image = [UIImage imageNamed:_data.head];
    }
    if ([_data.isVIP isEqual:@"1"]) {
        _vipImageView.hidden = NO;
    }else{
        _vipImageView.hidden = YES;
    }
    _vipImageView.hidden = YES;
    
    if ([_data.convId containsString:@"dsp_admin_1"] && _isSmall == NO) {
        _officeFlagIV.hidden = NO;
        NSString *savaData = [[NSUserDefaults standardUserDefaults]objectForKey:@"sys_time_1"];
        if(savaData){
            int isNew = [PublicObj compareDate:savaData withDate:_data.time];
            NSLog(@"isnew--------:%d",isNew);
            if(isNew == 1){
                [_unReadView setNum:9999999];
            }else{
                [_unReadView setNum:_data.unRead];
            }
        }else{
            [_unReadView setNum:9999999];
        }
    }else if ([_data.convId containsString:@"dsp_admin_2"] && _isSmall == NO) {
        _officeFlagIV.hidden = NO;
        NSString *savaData = [[NSUserDefaults standardUserDefaults]objectForKey:@"sys_time_2"];
        if(savaData){
            int isNew = [PublicObj compareDate:savaData withDate:_data.time];
            NSLog(@"isnew--------:%d",isNew);
            if(isNew == 1){
                [_unReadView setNum:9999999];
            }else{
                [_unReadView setNum:_data.unRead];
            }
        }else{
            [_unReadView setNum:9999999];
        }
    }else{
        _officeFlagIV.hidden = YES;
        [_unReadView setNum:_data.unRead];

    }
    
    _timeLabel.text = _data.time;
    //_titleLabel.text = _data.title;
    _titleLabel.text = _data.userName;
    _subTitleLabel.text = _data.subTitle;
    
    [self defaultLayout];
}

- (void)setupViews
{
    self.backgroundColor = CellRow_Cor;
    
    _headImageView = [[UIImageView alloc] init];
    _headImageView.layer.masksToBounds = YES;
    _headImageView.layer.cornerRadius = 25;
    [self addSubview:_headImageView];
    UIButton *shadowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [shadowBtn addTarget:self action:@selector(clickIconBtn) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:shadowBtn];
    [shadowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(_headImageView);
    }];
    
    _officeFlagIV = [[UIImageView alloc]init];
    _officeFlagIV.image = [UIImage imageNamed:getImagename(@"msg_gov")];
    [self addSubview:_officeFlagIV];
    _officeFlagIV.hidden = YES;
    [_officeFlagIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(26);
        make.height.mas_equalTo(13);
        make.top.equalTo(_headImageView.mas_bottom).offset(-8);
        make.right.equalTo(_headImageView);
    }];
    
    _chatSmallBtn = [[UILabel alloc]init];
    _chatSmallBtn.text =YZMsg(@"私聊");
    _chatSmallBtn.font = [UIFont systemFontOfSize:14];
    _chatSmallBtn.textColor = UIColor.whiteColor;
    _chatSmallBtn.backgroundColor = Pink_Cor;
    _chatSmallBtn.layer.cornerRadius = 12;
    _chatSmallBtn.layer.masksToBounds = YES;
    _chatSmallBtn.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:_chatSmallBtn];
    _chatSmallBtn.hidden = YES;
    [_chatSmallBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(24);
        make.width.mas_equalTo(40);
        make.centerY.equalTo(_headImageView);
        make.right.equalTo(self.mas_right).offset(-10);
    }];
//    _chatSmallBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//    [_chatSmallBtn setTitle:YZMsg(@"私聊") forState:0];
//    _chatSmallBtn.titleLabel.font = SYS_Font(15);
//    _chatSmallBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 10);
//    [_chatSmallBtn setTitleColor:UIColor.whiteColor forState:0];
//    _chatSmallBtn.backgroundColor = Pink_Cor;
//    _chatSmallBtn.layer.cornerRadius = 12;
//    _chatSmallBtn.layer.masksToBounds = YES;
//    [self.contentView addSubview:_chatSmallBtn];
//    _chatSmallBtn.hidden = YES;
//    [_chatSmallBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.height.mas_equalTo(24);
//        make.centerY.equalTo(_headImageView);
//        make.right.equalTo(self.mas_right).offset(-10);
//    }];
    
    _timeLabel = [[UILabel alloc] init];
    _timeLabel.font = [UIFont systemFontOfSize:12];
    _timeLabel.textColor = RGB_COLOR(@"#969696", 1);
    _timeLabel.layer.masksToBounds = YES;
    [self addSubview:_timeLabel];

    _titleLabel = [[UILabel alloc] init];
    _titleLabel.font = [UIFont systemFontOfSize:16];
    _titleLabel.textColor = [UIColor whiteColor];
    _titleLabel.layer.masksToBounds = YES;
    [self addSubview:_titleLabel];
    
    
    _vipImageView = [[UIImageView alloc] init];
    _vipImageView.image = [UIImage imageNamed:@"vip"];
    [self addSubview:_vipImageView];

    _unReadView = [[TUnReadView alloc] init];
    [self addSubview:_unReadView];

    _subTitleLabel = [[UILabel alloc] init];
    _subTitleLabel.layer.masksToBounds = YES;
    _subTitleLabel.font = [UIFont systemFontOfSize:14];
    _subTitleLabel.textColor = RGB_COLOR(@"#969696", 1);;
    [self addSubview:_subTitleLabel];
    _rightImageView = [[UIImageView alloc] init];
    _rightImageView.image = [UIImage imageNamed:@"right_arrow"];
    [self addSubview:_rightImageView];

    UIView *lineV = [PublicObj lineViewWithFrame:CGRectMake(0, TConversationCell_Height-1, _window_width, 1) andColor:RGB_COLOR(@"#f5f5f5", 1) andView:self];
    lineV.hidden = YES;
    [self setSeparatorInset:UIEdgeInsetsMake(0, TConversationCell_Margin, 0, 0)];
}
-(void)clickIconBtn {
    if (_isSmall) {
        return;
    }
    if ([_data.convId isEqual:@"dsp_admin_1"]||[_data.convId isEqual:@"dsp_admin_2"]) {
        return;
    }
    YBCenterVC *center = [[YBCenterVC alloc]init];
    center.otherUid = _data.convId;
    center.isPush = YES;
    center.hidesBottomBarWhenPushed = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];
}
- (void)defaultLayout
{
    CGSize size = [TConversationCell getSize];
    _headImageView.frame = CGRectMake(TConversationCell_Margin, TConversationCell_Margin, size.height - TConversationCell_Margin * 2, size.height - TConversationCell_Margin * 2);
    if ([_data.title isEqual:YZMsg(@"预约")]) {
        _rightImageView.frame = CGRectMake(size.width-25, 25, 20, 20);
    }else{
        _rightImageView.frame = CGRectZero;
    }
    [_timeLabel sizeToFit];
    _timeLabel.frame = CGRectMake(size.width - TConversationCell_Margin - _timeLabel.frame.size.width, TConversationCell_Margin_Text, _timeLabel.frame.size.width, _timeLabel.frame.size.height);

    [_titleLabel sizeToFit];
    _titleLabel.frame = CGRectMake(_headImageView.frame.origin.x + _headImageView.frame.size.width + TConversationCell_Margin, TConversationCell_Margin_Text, size.width - _timeLabel.frame.size.width - _headImageView.frame.size.width - 4 * TConversationCell_Margin, _titleLabel.frame.size.height);
    if (_data.nameShouldCenter) {
        float topsss = size.height - _titleLabel.frame.size.height;
        _titleLabel.frame = CGRectMake(_headImageView.frame.origin.x + _headImageView.frame.size.width + TConversationCell_Margin, topsss/2, size.width - _timeLabel.frame.size.width - _headImageView.frame.size.width - 4 * TConversationCell_Margin, _titleLabel.frame.size.height);
    }
    CGFloat wwwwwww = [PublicObj widthOfString:_data.userName andFont:SYS_Font(16) andHeight:20];
    _vipImageView.frame = CGRectMake(_titleLabel.x + wwwwwww + 3, TConversationCell_Margin_Text + 2, 25, 15);

    _unReadView.frame = CGRectMake(size.width - TConversationCell_Margin - _unReadView.frame.size.width, size.height - TConversationCell_Margin_Text - _unReadView.frame.size.height, _unReadView.frame.size.width, _unReadView.frame.size.height);
    
    [_subTitleLabel sizeToFit];
    _subTitleLabel.frame = CGRectMake(_titleLabel.frame.origin.x, size.height - TConversationCell_Margin_Text - _subTitleLabel.frame.size.height, size.width - _headImageView.frame.size.width - 4 * TConversationCell_Margin - _unReadView.frame.size.width, _subTitleLabel.frame.size.height);
}

#pragma mark -
// 私信小窗改变UI
-(void)imsmallViewResetUI;{
    _isSmall = YES;
    self.backgroundColor = UIColor.whiteColor;
    _titleLabel.textColor = RGB_COLOR(@"#323232", 1);
}

-(void)checkImRecordWithHostid:(NSString *)liveuid;{
    if ([PublicObj checkNull:_data.subTitle] && [_data.convId isEqual:liveuid]) {
        _subTitleLabel.text = YZMsg(@"Hi～我是主播，快来和我聊天吧。");
        [_subTitleLabel sizeToFit];
        CGSize size = [TConversationCell getSize];
        _subTitleLabel.frame = CGRectMake(_titleLabel.frame.origin.x, size.height - TConversationCell_Margin_Text - _subTitleLabel.frame.size.height, size.width - _headImageView.frame.size.width - 4 * TConversationCell_Margin - 60, _subTitleLabel.frame.size.height);
        _chatSmallBtn.hidden = NO;
        _timeLabel.hidden = YES;
    }else{
        _chatSmallBtn.hidden = YES;
        _timeLabel.hidden = NO;
    }
}

@end
