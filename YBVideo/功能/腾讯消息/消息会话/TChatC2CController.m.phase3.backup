//
//  TChatC2CController.m
//  UIKit
//
//  Created by kennethmia<PERSON> on 2018/9/18.
//  Copyright © 2018年 kennethmiao. All rights reserved.
//

#import "TChatC2CController.h"
#import "THeader.h"
#import "TZImagePickerController.h"
#import "TImageMessageCell.h"
#import "TLocationCell.h"
#import "TGoodsCell.h"
#import <MobileCoreServices/MobileCoreServices.h>
#import "YBScrollImageView.h"
#import "YBGiftPage.h"
#import "TTextMessageCell.h"
#import "TencentLocationVC.h"
#import "ASRView.h"

@interface TChatC2CController () <TMessageControllerDelegate, TInputControllerDelegate,TZImagePickerControllerDelegate,UIImagePickerControllerDelegate,UINavigationControllerDelegate>{
    
    ASRView *_asrView;
    BOOL _asrMoveUp;
}

@property(nonatomic,strong)UIButton *hqs_voiceBtn;
@property(nonatomic,strong)UIButton *hqs_videoBtn;

@property(nonatomic,strong)UIView *topAttentView;

@end

@implementation TChatC2CController

- (void)viewDidLoad {
    [super viewDidLoad];
    /*
    id target = self.navigationController.interactivePopGestureRecognizer.delegate;
    UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:target action:nil];
    [self.view addGestureRecognizer:pan];
     */

    [[NSUserDefaults standardUserDefaults] setObject:_conversation.convId forKey:ybImChatingUid];

    self.titleL.text = _conversation.userName;
    self.rightBtn.hidden = NO;
    [self.rightBtn setImage:[UIImage imageNamed:@"center_more"] forState:UIControlStateNormal];
    [self setupViews];
    
    if (_conversation) {
        [_messageController setConversation:_conversation];
        _topAttentView.hidden = [_conversation.isAtt intValue];
    }
}

- (void)setupViews {
    //message
    _messageController = [[TMessageController alloc] init];
    _messageController.uiFrom = _uiFrom;
    _messageController.view.frame = CGRectMake(0, naviHight, _window_width, _window_height - TTextView_Height - Bottom_SafeHeight-(naviHight));
    _messageController.delegate = self;
    _messageController.tableView.backgroundColor = TMessageController_Background_Color;
    [self addChildViewController:_messageController];
    [self.view addSubview:_messageController.view];
    //[_messageController setConversation:_conversation];
    
    //input
    _inputController = [[TInputController alloc] init];
    _inputController.uiFrom = _uiFrom;
    _inputController.view.frame = CGRectMake(0, _window_height - TTextView_Height - Bottom_SafeHeight, _window_width, TTextView_Height + Bottom_SafeHeight);
    _inputController.delegate = self;
    [self addChildViewController:_inputController];
    [self.view addSubview:_inputController.view];
    
    [self.view addSubview:self.topAttentView];
    _topAttentView.hidden = YES;
    [_topAttentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        if (_uiFrom == UiFrom_Samll) {
            make.top.equalTo(self.view.mas_top).offset(0);
        }else{
            make.top.equalTo(self.naviView.mas_bottom).offset(0);
        }
        make.height.mas_equalTo(50);
    }];
    if (_uiFrom == UiFrom_Samll) {
        _messageController.tableView.backgroundColor = RGB_COLOR(@"#f4f5f6", 1);
        _topAttentView.backgroundColor = UIColor.whiteColor;
    }
}

- (void)doFollow{
    YBWeakSelf;
    NSDictionary *dic = @{
        @"uid":[Config getOwnID],
        @"touid":_conversation.convId,
    };
    [YBNetworking postWithUrl:@"User.isFirstAttent" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infos = [info firstObject];
            //is_first   1 第一次关注   0 不是第一次关注
            BOOL isfirstAttent = [minstr([infos valueForKey:@"is_first"]) boolValue];;
            [weakSelf setAttentFirst:isfirstAttent];
            }
        } Fail:^(id fail) {

    }];
}
-(void)setAttentFirst:(BOOL)isFirst {
    NSString *postUrl = @"User.setAttent";
    NSDictionary *postDic = @{
        @"touid":_conversation.convId,
    };
    YBWeakSelf;
    [YBNetworking postWithUrl:postUrl Dic:postDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [weakSelf clickFollowClose];
            [MBProgressHUD showError:YZMsg(@"关注对方成功")];
            [[NSNotificationCenter defaultCenter] postNotificationName:ybImC2CFollow object:@(1)];
            if (isFirst) {
                //发送自定义消息
                NSDictionary *infoDic = @{@"uid":[Config getOwnID],@"touid":_conversation.convId,@"method":@"follow"};
                NSData *data = [NSJSONSerialization dataWithJSONObject:infoDic options:NSJSONWritingPrettyPrinted error:nil];
                V2TIMCustomElem * custom_elem = [[V2TIMCustomElem alloc] init];
                [custom_elem setData:data];

                [[YBImManager shareInstance]sendV2CustomMsg:custom_elem andReceiver:_conversation.convId complete:^(BOOL isSuccess, V2TIMMessage *sendMsg, NSString *desc) {
                                    
                }];
            }
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];

}

- (void)doSetBlack{
    NSString *url = [NSString stringWithFormat:@"User.setBlack&uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_conversation.convId];
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSString *infoDic = [info firstObject];
            NSString *isBlakc = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isblack"]];
            if ([isBlakc isEqual:@"1"]) {
                [MBProgressHUD showPop:YZMsg(@"拉黑成功")];
            }else {
                [MBProgressHUD showPop:YZMsg(@"解除拉黑成功")];
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
   
}
- (void)clickNaviRightBtn{
    YBWeakSelf;
    NSString *url = [NSString stringWithFormat:@"User.checkBlack&uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_conversation.convId];
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *u2t = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"u2t"]];
            //u2t  0-未拉黑  1-已拉黑
            NSString *blackTitle = YZMsg(@"拉黑");
            if ([u2t isEqual:@"1"]) {
                blackTitle = YZMsg(@"解除拉黑");
            }
            [weakSelf showSheetView:blackTitle];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
-(void)showSheetView:(NSString *)blackTitle {
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"举报") complete:^{
        [weakSelf doReport];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:blackTitle complete:^{
        [weakSelf doSetBlack];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];
}

//举报
-(void)doReport{
    PubH5 *h5vc = [[PubH5 alloc]init];
    h5vc.url =[h5url stringByAppendingString:[NSString stringWithFormat:@"/appapi/userreport/index?uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_conversation.convId]];
    [self.navigationController pushViewController:h5vc animated:YES];
}

- (void)doReturn{
    [[YBImManager shareInstance]clearUnreadConvId:_conversation.convId sendNot:YES];
    [self.navigationController popViewControllerAnimated:YES];
}
- (void)inputControllerDidClickHqsGiftBtn{
    
}
- (void)inputController:(TInputController *)inputController didChangeHeight:(CGFloat)height {
    CGFloat navSpace = naviHight;
    if (_uiFrom == UiFrom_Samll) {
        navSpace = 0;
    }
//    __weak typeof(self) ws = self;
    if (height<50) {
        [self showVioceAndVideoBtn];
    }else{
        [self inputControllerHiddenVoiceVideo];
    }
    //NSLog(@"rk=========================22222====：%f====sm:%f",height,navSpace);
    [UIView animateWithDuration:0.25 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        CGRect msgFrame = _messageController.view.frame;
        msgFrame.size.height = self.view.frame.size.height - height-navSpace;
        //未知原因-19.6,这里重置一下起始位置
        msgFrame.origin.y = navSpace;
        _messageController.view.frame = msgFrame;
        CGRect inputFrame = _inputController.view.frame;
        inputFrame.origin.y = msgFrame.origin.y + msgFrame.size.height;
        inputFrame.size.height = height;
        _inputController.view.frame = inputFrame;
        [_messageController scrollToBottom:NO];
        //NSLog(@"rk=========================ani:%@",_messageController.view);
    } completion:^(BOOL finished) {
        //NSLog(@"rk=========================finishi\n===:%@\n===%@",_messageController.view,_inputController.view);
    }];
}
-(void)inputResetHeight {
    [_inputController reset];
    [self showVioceAndVideoBtn];
}
- (void)inputController:(TInputController *)inputController didSendMessage:(TMessageCellData *)msg {
    BOOL isText = NO;
    if ([msg isKindOfClass:[TTextMessageCellData class]]) {
        isText = YES;
    }
    [self checkBlack:msg imgIdx:0 isTextMsg:isText];
}

- (void)inputController:(TInputController *)inputController didSelectMoreAtIndex:(NSInteger)index{
    NSLog(@"----------%ld",index);
    [_inputController reset];
    [self showVioceAndVideoBtn];
    if (index == 0) {
        TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
        imagePC.allowCameraLocation = YES;
        imagePC.allowTakeVideo = NO;
        imagePC.allowPickingVideo = NO;
        imagePC.doneBtnTitleStr = YZMsg(@"发送");
        imagePC.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:imagePC animated:YES completion:nil];
    }else if (index == 1){
        //相机
        UIImagePickerController *picker = [[UIImagePickerController alloc] init];
        picker.sourceType = UIImagePickerControllerSourceTypeCamera;
        picker.cameraCaptureMode =UIImagePickerControllerCameraCaptureModePhoto;
        picker.delegate = self;
        picker.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:picker animated:YES completion:nil];
    }else if (index == 2){
        //语音
        [self voiceInputClick];
    }else if (index == 3){
        //位置
        [self locationClick];
    }
}

#pragma mark - 语音输入
-(void)voiceInputClick {
    NSLog(@"语音输入");
    YBWeakSelf;
    if (!_asrView) {
        _asrView = [[ASRView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, _window_height) callBack:^(NSString *type, NSString *content) {
            if ([type isEqual:@"返回"]) {
                NSLog(@"返回");
                [weakSelf asrBack];
            }else if ([type isEqual:@"发送"]){
                NSLog(@"发送");
                [weakSelf asrSend:content];
            }
        }];
        [self.view addSubview:_asrView];
    }
    [UIView animateWithDuration:0.25 animations:^{
        _asrView.frame = CGRectMake(0, 0, _window_width, _window_height);
    }];
}
-(void)asrBack {
    [_asrView.textView resignFirstResponder];
    [UIView animateWithDuration:0.25 animations:^{
        _asrView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
    }];
}
-(void)asrSend:(NSString *)content {
    TTextMessageCellData *data = [[TTextMessageCellData alloc] init];
    data.head = TUIKitResource(@"default_head");
    data.content = content;
    data.isSelf = YES;
    [self checkBlack:data imgIdx:0 isTextMsg:YES];
}

#pragma mark - 位置信息
-(void)locationClick {
    TencentLocationVC *locationVC = [[TencentLocationVC alloc]init];
    YBWeakSelf;
    locationVC.locationEvent = ^(NSDictionary *locDic) {
        [weakSelf prepareLocation:locDic];
    };
    [self.navigationController pushViewController:locationVC animated:YES];
}
-(void)prepareLocation:(NSDictionary *)dic{
    
    double latitude =  [strFormat([dic valueForKey:@"latitude"]) doubleValue];
    double longitude = [strFormat([dic valueForKey:@"longitude"]) doubleValue];
    NSString *address = [dic valueForKey:@"address"];
    
    TLocationCellData *locData = [[TLocationCellData alloc] init];
    locData.head = TUIKitResource(@"default_head");
    locData.isSelf = YES;
    locData.locDes = address;
    locData.lat = latitude;
    locData.lng = longitude;
    [self checkBlack:locData imgIdx:0 isTextMsg:NO];
}

- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    for (int i = 0;i < photos.count;i++) {
        UIImage *img = photos[i];
        [self sendImageMessage:img andIndex:i];
    }
}
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info {
    NSString *mediaType = [info objectForKey:UIImagePickerControllerMediaType];
    if([mediaType isEqualToString:(NSString *)kUTTypeImage]){
        UIImage *image = [info objectForKey:UIImagePickerControllerOriginalImage];
        UIImageOrientation imageOrientation=  image.imageOrientation;
        if(imageOrientation != UIImageOrientationUp) {
            UIGraphicsBeginImageContext(image.size);
            [image drawInRect:CGRectMake(0, 0, image.size.width, image.size.height)];
            image = UIGraphicsGetImageFromCurrentImageContext();
            UIGraphicsEndImageContext();
        }
        [self sendImageMessage:image andIndex:0];
    }
    [picker dismissViewControllerAnimated:YES completion:nil];
}
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker{
    [picker dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - TMessageControllerDelegate
- (void)didTapInMessageController:(TMessageController *)controller {
    [_inputController reset];
    [self showVioceAndVideoBtn];
}
-(void)showVioceAndVideoBtn {
    _hqs_voiceBtn.hidden = _hqs_videoBtn.hidden = NO;
}
-(void)inputControllerHiddenVoiceVideo {
    _hqs_voiceBtn.hidden = _hqs_videoBtn.hidden = YES;
}
- (void)didHideMenuInMessageController:(TMessageController *)controller {
    _inputController.textView.inputTextView.overrideNextResponder = nil;
}
- (BOOL)messageController:(TMessageController *)controller willShowMenuInCell:(TMessageCell *)cell {
    if([_inputController.textView.inputTextView isFirstResponder]){
        _inputController.textView.inputTextView.overrideNextResponder = cell;
        return YES;
    }
    return NO;
}
- (void)messageController:(TMessageController *)controller didSelectMessages:(NSMutableArray *)msgs atIndex:(NSInteger)index {
    TMessageCellData *currentdata = msgs[index];
    NSMutableArray *imgarr = [NSMutableArray array];
    NSInteger currentIndex = 0;
    for (int i = 0; i < msgs.count; i ++) {
        TMessageCellData *data = msgs[i];
        if([data isKindOfClass:[TImageMessageCellData class]]){
            TImageMessageCellData *imgData = (TImageMessageCellData *)data;
            [imgarr addObject:imgData.thumbImage];
        }
    }
    for (int k = 0; k < imgarr.count; k ++) {
        TImageMessageCellData *imgDatass = (TImageMessageCellData *)currentdata;
        UIImage *currentimg = imgarr[k];
        if (imgDatass.thumbImage == currentimg) {
            currentIndex = k;
            break;
        }
    }
    NSLog(@"==-=-=-=-=-=-=-=-=-=:::%@", imgarr);
    YBScrollImageView *imgView = [[YBScrollImageView alloc] initWithImageArray:imgarr andIndex:currentIndex andMine:NO andBlock:^(NSArray * _Nonnull array) {
    }];
    [imgView hideDelete];
    [[UIApplication sharedApplication].keyWindow addSubview:imgView];

}
/// 收到礼物IM
- (void)reciveGiftMessage:(NSDictionary *)giftRes{

}

#pragma mark - 发送消息
- (void)sendImageMessage:(UIImage *)image andIndex:(int)index; {
    [self checkBlack:image imgIdx:index isTextMsg:NO];
}

- (void)sendVideoMessage:(NSURL *)url {
    [_messageController sendVideoMessage:url];
}

- (void)sendFileMessage:(NSURL *)url {
    [_messageController sendFileMessage:url];
}

- (void)checkBlack:(id)datamsg imgIdx:(int)imgIdx isTextMsg:(BOOL)isText{
    __block id blockDatamsg = datamsg;
    NSString *oldContent = @"";
    if (isText) {
        TTextMessageCellData *cellData = (TTextMessageCellData*)datamsg;
        oldContent = cellData.content;
    }
    NSDictionary *postDic = @{
        @"touid":_conversation.convId,
        @"content":oldContent,
    };
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.checkBlack" Dic:postDic Suc:^(int code, id info, NSString *msg) {
        
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            // 字段无特殊意义
            int isletter = 1;//[minstr([infoDic valueForKey:@"isletter"])intValue];
            
            NSString *isattent = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isattent"]];
            NSString *t2u = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"t2u"]];
            NSString *sendConten = minstr([infoDic valueForKey:@"content"]);
            if (isText) {
                // 过滤后的字符重新赋值
                TTextMessageCellData *cellData = (TTextMessageCellData*)blockDatamsg;
                cellData.content = sendConten;
                blockDatamsg = cellData;
            }
            NSString *p_switch = [NSString stringWithFormat:@"%@",[common private_letter_switch]];
            int p_nums = [[NSString stringWithFormat:@"%@",[common private_letter_nums]] intValue];
            //我发送的条数
            int frends_cont = [_messageController sendMsgNums];
            if ([t2u isEqual:@"0"]) {
                if ([isattent isEqual:@"0"] && [p_switch isEqual:@"1"] && frends_cont >= p_nums) {
                    [MBProgressHUD showPop:[NSString stringWithFormat:@"%@%d%@",YZMsg(@"对方未关注你,最多只能发送"),p_nums,YZMsg(@"条信息")]];
                }else {
                    if (isletter == 0) {
                        NSString *letterAlert = minstr([infoDic valueForKey:@"isletter_msg"]);
                        [weakSelf gdyChatPayAlert:letterAlert dataMsg:blockDatamsg imgIdx:imgIdx];
                    }else{
                        [weakSelf canSendWithMsg:blockDatamsg imgIdx:imgIdx];
                    }
                }
            }else {
                [_inputController.textView clearInput];
                [_inputController.inputView resignFirstResponder];
                [MBProgressHUD showPop:YZMsg(@"对方暂时拒绝接收您的消息")];
            }
        }else {
            [_inputController.textView clearInput];
            [_inputController.inputView resignFirstResponder];
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}

-(void)gdyChatPayAlert:(NSString *)alertMsg dataMsg:(id)datamsg imgIdx:(int)imgIdx{
    [self.view endEditing:YES];
    NSDictionary *contentDic = @{
        @"title":YZMsg(@"提示"),
        @"msg":alertMsg,
        @"left":YZMsg(@"取消"),
        @"right":YZMsg(@"去打赏")
    };
    YBWeakSelf;
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            [weakSelf goReqServiceWithdataMsg:datamsg imgIdx:imgIdx];
        }
    }];
}
-(void)goReqServiceWithdataMsg:(id)datamsg imgIdx:(int)imgIdx {
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.addLetterCoin" Dic:@{@"touid":_conversation.convId} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD showError:msg];
        if (code == 0) {
            [weakSelf canSendWithMsg:datamsg imgIdx:imgIdx];
        }
    } Fail:^(id fail) {
    }];
}
// 发送消息/扣费费送出
-(void)canSendWithMsg:(id)datamsg imgIdx:(int)imgIdx {
    [_inputController.textView clearInput];
    if ([datamsg isKindOfClass:[UIImage class]]) {
        [_messageController sendImageMessage:datamsg andIndex:imgIdx];
    }else{
        [_messageController sendMessage:datamsg];
    }
}

- (void)dealloc{
    [[NSUserDefaults standardUserDefaults] setObject:@"0" forKey:ybImChatingUid];
}

#pragma mark -

- (UIView *)topAttentView {
    if (!_topAttentView) {
        _topAttentView = [[UIView alloc]init];
        _topAttentView.backgroundColor = Normal_Color;
        
        UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [closeBtn setImage:[UIImage imageNamed:@"提示关闭"] forState:0];
        [closeBtn addTarget:self action:@selector(clickFollowClose) forControlEvents:UIControlEventTouchUpInside];
        [_topAttentView addSubview:closeBtn];
        [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(40);
            make.centerY.equalTo(_topAttentView.mas_centerY);
            make.left.equalTo(_topAttentView.mas_left).offset(5);
        }];
        
        UILabel *desL = [[UILabel alloc]init];
        desL.text = YZMsg(@"点击关注，可及时看到对方动态");
        desL.font = SYS_Font(12);
        desL.textColor =  RGB_COLOR(@"#828282", 1);
        desL.numberOfLines = 2;
        [_topAttentView addSubview:desL];
        [desL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(closeBtn.mas_right).offset(5);
            make.centerY.equalTo(_topAttentView);
        }];
        
        UIButton *followBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        //YZMsg(@"关注-chattop")
        [followBtn setTitle:YZMsg(@"关注") forState:0];
        followBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 10);
        followBtn.layer.cornerRadius = 10;
        followBtn.layer.masksToBounds = YES;
        followBtn.titleLabel.font = SYS_Font(12);
        followBtn.layer.borderColor = Pink_Cor.CGColor;
        followBtn.layer.borderWidth = 1;
        [followBtn setTitleColor:Pink_Cor forState:0];
        
        [followBtn addTarget:self action:@selector(doFollow) forControlEvents:UIControlEventTouchUpInside];
        [_topAttentView addSubview:followBtn];
        [followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(_topAttentView.mas_centerY);
            make.right.equalTo(_topAttentView.mas_right).offset(-15);
            make.left.greaterThanOrEqualTo(desL.mas_right).offset(10);
            make.height.mas_equalTo(20);
        }];
        
        [desL setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        [followBtn setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];

    }
    return _topAttentView;;
}
-(void)clickFollowClose {
    _topAttentView.hidden = YES;
}

#pragma mark - 小窗
-(void)imSmallViewReload {
    if (_conversation) {
        [_messageController setConversation:_conversation];
        _topAttentView.hidden = [_conversation.isAtt intValue];
    }
    self.naviView.hidden = YES;
    [self changeSmallHeight];
    _topAttentView.top = 0;
    //NSLog(@"rk=========================follow：%@",_topAttentView);
}
-(void)changeSmallHeight {
    _messageController.view.frame = CGRectMake(0, 0, _window_width, self.view.height - TTextView_Height - Bottom_SafeHeight);
    _inputController.view.frame = CGRectMake(0, _messageController.view.bottom, _window_width, TTextView_Height + Bottom_SafeHeight);
   // NSLog(@"rk=========================111111\n===:%@\n===%@",_messageController.view,_inputController.view);
}

@end
