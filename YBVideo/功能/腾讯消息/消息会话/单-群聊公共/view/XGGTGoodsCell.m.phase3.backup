//
//  TGoodsCell.m
//  iphoneLive
//
//  Created by YB007 on 2022/6/15.
//  Copyright © 2022 cat. All rights reserved.
//

#import "TGoodsCell.h"
#import "OutsideGoodsDetailVC.h"
#import "CommodityDetailVC.h"

@implementation TGoodsCellData
@end

@interface TGoodsCell(){
    NSString *_goodsId;
    int _goodType;
}

@end

@implementation TGoodsCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
- (CGSize)getContainerSize:(TMessageCellData *)data {
   
    return CGSizeMake(_window_width*0.43, _window_width*0.6);
}

- (void)setData:(TGoodsCellData *)data {
    //set data
    [super setData:data];
    _goodsId = data.goodsId;
    NSDictionary *dic = @{
        @"uid":[Config getOwnID],
        @"token":[Config getOwnToken],
        @"goodsid":data.goodsId,
    };
    [YBNetworking postWithUrl:@"Shop.getGoodsInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSDictionary *goodsInfo = [infoDic valueForKey:@"goods_info"];
            NSArray * first = [goodsInfo valueForKey:@"thumbs_format"];
            if (first.count>0) {
                [_thumbIV sd_setImageWithURL:[NSURL URLWithString:minstr(first[0])]];
            }
            int goodsType = [strFormat([goodsInfo valueForKey:@"type"]) intValue];
            _goodType = goodsType;
            // 1外链商品 0 站内商品
            if (goodsType == 1) {
                _priceL.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), [goodsInfo valueForKey:@"present_price"]];
            }else{
                NSDictionary *subDic = [[goodsInfo valueForKey:@"specs_format"] firstObject];
                _priceL.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), [subDic valueForKey:@"price"]];
            }
            _nameL.text = strFormat([goodsInfo valueForKey:@"name"]);
            _desL.text = [NSString stringWithFormat:YZMsg(@"已售%@件"),[goodsInfo valueForKey:@"sale_nums"]];
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
    
}
- (void)setupViews {
    [super setupViews];

    UIView *bgView = [[UIView alloc]init];
    bgView.layer.cornerRadius = 5;
    bgView.layer.masksToBounds = YES;
    bgView.backgroundColor = UIColor.whiteColor;
    [super.container addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(super.container);
    }];
    
    _thumbIV = [[UIImageView alloc]init];
    _thumbIV.contentMode = UIViewContentModeScaleAspectFill;
    _thumbIV.clipsToBounds = YES;
    [bgView addSubview:_thumbIV];
    [_thumbIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.top.equalTo(bgView);
    }];
    
    _priceL = [[UILabel alloc]init];
    _priceL.font = SYS_Font(16);
    _priceL.textColor = Pink_Cor;
    [bgView addSubview:_priceL];
    [_priceL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(bgView.mas_left).offset(6);
        make.top.equalTo(_thumbIV.mas_bottom).offset(6);
        make.height.mas_equalTo(20);
    }];
    
    _nameL = [[UILabel alloc]init];
    _nameL.font = SYS_Font(14);
    _nameL.textColor = RGB_COLOR(@"#323232", 1);
    _nameL.numberOfLines = 0;
    [bgView addSubview:_nameL];
    [_nameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_priceL.mas_left);
        make.top.equalTo(_priceL.mas_bottom).offset(5);
        make.right.lessThanOrEqualTo(bgView.mas_right).offset(-10);
    }];
    
    _desL = [[UILabel alloc]init];
    _desL.font = SYS_Font(14);
    _desL.textColor = UIColor.grayColor;
    _desL.numberOfLines = 0;
    [bgView addSubview:_desL];
    [_desL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_priceL.mas_left);
        make.top.equalTo(_nameL.mas_bottom).offset(5);;
        make.right.lessThanOrEqualTo(bgView.mas_right).offset(-10);
        make.bottom.equalTo(bgView.mas_bottom).offset(-10);
    }];
    [_thumbIV setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisVertical];
    [_priceL setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisVertical];
    [_nameL setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisVertical];
    [_desL setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisVertical];
    
    UIButton *shadowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [shadowBtn addTarget:self action:@selector(clickGoods) forControlEvents:UIControlEventTouchUpInside];
    [bgView addSubview:shadowBtn];
    [shadowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(bgView);
    }];
    
}
-(void)clickGoods {
    
    [PublicObj checkGoodsExistenceWithID:_goodsId Existence:^(int code, NSString *msg) {
        if (code == 0) {
            if (_goodType == 1) {
                OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
                detail.goodsID = _goodsId;
                detail.liveUid = @"0";
                [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];
            }else{
                CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                detail.goodsID = _goodsId;
                detail.liveUid = @"0";
                detail.shareuid = super.data.senderUid;
                [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];
            }
        }else{
            [MBProgressHUD showError:msg];

        }
    }];
}
@end
