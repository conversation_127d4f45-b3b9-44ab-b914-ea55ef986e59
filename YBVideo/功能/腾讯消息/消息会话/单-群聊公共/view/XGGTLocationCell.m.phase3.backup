//
//  TLocationCell.m
//  iphoneLive
//
//  Created by YB007 on 2022/6/15.
//  Copyright © 2022 cat. All rights reserved.
//

#import "TLocationCell.h"

@implementation TLocationCellData
@end

@implementation TLocationCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (CGSize)getContainerSize:(TMessageCellData *)data {
   
    return CGSizeMake(237, 171);
}

- (void)setData:(TLocationCellData *)data {
    //set data
    [super setData:data];
    _cellData = data;
    NSData *mix_data = [data.locDes dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *mix_dic = [NSJSONSerialization JSONObjectWithData:mix_data options:NSJSONReadingAllowFragments error:nil];
    if ([mix_dic isKindOfClass:[NSDictionary class]]) {
        _titleL.text = [NSString stringWithFormat:@"%@",[mix_dic valueForKey:@"name"]];
        _desL.text = [NSString stringWithFormat:@"%@",[mix_dic valueForKey:@"info"]];
    }else {
        _titleL.text = data.locDes;
    }
    [_bgView layoutIfNeeded];
    NSString *url = @"https://apis.map.qq.com/ws/staticmap/v2/";
    NSDictionary *postDic = @{@"key":TencentKey,
                              @"scale":@"1",
                              @"size":[NSString stringWithFormat:@"%.f*%.f",_mapIV.width,_mapIV.height],
                              @"center":[NSString stringWithFormat:@"%f,%f",data.lat,data.lng],
                              @"zoom":@"16"};
    url = [url stringByAppendingFormat:@"?%@",[YBNetworking getStrWithDic:postDic]];
    [_mapIV sd_setImageWithURL:[NSURL URLWithString:url]];
    
}
- (void)setupViews {
    [super setupViews];
    
    _bgView = [[UIView alloc]init];
    _bgView.layer.cornerRadius = 5;
    _bgView.layer.masksToBounds = YES;
    _bgView.backgroundColor = UIColor.whiteColor;
    [super.container addSubview:_bgView];
    [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(super.container);
    }];
    
    _titleL = [[UILabel alloc]init];
    _titleL.font = SYS_Font(15);
    _titleL.textColor = RGB_COLOR(@"#323232", 1);
    [_bgView addSubview:_titleL];
    [_titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_bgView.mas_width).offset(-13);
        make.centerX.equalTo(_bgView);
        make.top.equalTo(_bgView.mas_top).offset(6);
        make.height.mas_equalTo(20);
    }];
    _desL = [[UILabel alloc]init];
    _desL.font = SYS_Font(13);
    _desL.textColor = RGB_COLOR(@"#646464", 1);
    [_bgView addSubview:_desL];
    [_desL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(_titleL);
        make.top.equalTo(_titleL.mas_bottom).offset(3);
        make.height.mas_equalTo(15);
    }];
    _mapIV = [[UIImageView alloc]init];
    _mapIV.contentMode = UIViewContentModeScaleAspectFill;
    _mapIV.clipsToBounds = YES;
    [_bgView addSubview:_mapIV];
    [_mapIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.bottom.equalTo(_bgView);
        make.top.equalTo(_desL.mas_bottom).offset(5);
    }];
    
    UIImageView *pointIV = [[UIImageView alloc] init];
    pointIV.image = [UIImage imageNamed:@"location_current"];
    pointIV.backgroundColor = [UIColor clearColor];
    [_bgView addSubview:pointIV];
    [pointIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(50);
        make.centerX.centerY.equalTo(_mapIV);
    }];
    UITapGestureRecognizer *topClick =[[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tipClickEvent)];
    [_bgView addGestureRecognizer:topClick];

}
-(void)tipClickEvent{
    
    NSData *mix_data = [_cellData.locDes dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *mix_dic = [NSJSONSerialization JSONObjectWithData:mix_data options:NSJSONReadingAllowFragments error:nil];
    NSString *addressStr = @"";
    if ([mix_dic isKindOfClass:[NSDictionary class]]) {
        addressStr = [NSString stringWithFormat:@"%@",[mix_dic valueForKey:@"name"]];
    }else {
        addressStr = _cellData.locDes;
    }

    [[RKLBSManager shareManager]showNavigationsWithLat:@(_cellData.lat)  lng:@(_cellData.lng ) endName:addressStr];

}
@end
