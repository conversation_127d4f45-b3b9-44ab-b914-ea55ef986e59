//
//  YBScrollImageView.m
//  YBHiMo
//
//  Created by YB007 on 2021/8/20.
//  Copyright © 2021 YB007. All rights reserved.
//

#import "YBScrollImageView.h"
#import "YBShowBigImageView.h"
#import "SDWebImageDownloader.h"

@interface YBScrollImageView ()<UIScrollViewDelegate>{
    
    NSInteger currentIndex;
    UILabel *indexLb;
    UIButton *deleteBtn;
    UIView *navi;
    BOOL isMine;
}
@property (nonatomic,copy) YBImageViewBlock returnBlock;

@end

@implementation YBScrollImageView
{
    UITapGestureRecognizer *tap;
    UIScrollView *backScrollV;
    NSMutableArray *imageArray;
    NSMutableArray *imgViewArray;

}
- (instancetype)initWithImageArray:(NSArray *)array andIndex:(NSInteger)index andMine:(BOOL)ismine andBlock:(nonnull YBImageViewBlock)block{
    self = [super init];
    isMine = ismine;
    imageArray = [array mutableCopy];
    currentIndex = index;
    self.returnBlock = block;
    self.frame = CGRectMake(0, 0, _window_width, _window_height);
    if (self) {
        self.userInteractionEnabled = YES;
        tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(showHideNavi)];
        [self addGestureRecognizer:tap];
        backScrollV = [[UIScrollView alloc]initWithFrame:CGRectMake(_window_width/2, _window_height/2, 0, 0)];
        backScrollV.backgroundColor = [UIColor blackColor];
        backScrollV.contentSize = CGSizeMake(_window_width*imageArray.count, 0);
        backScrollV.contentOffset = CGPointMake(_window_width * index, 0);
        backScrollV.delegate = self;
        backScrollV.pagingEnabled=YES;
        //设置最大伸缩比例
        backScrollV.maximumZoomScale=1;
        //设置最小伸缩比例
        backScrollV.minimumZoomScale=1;
        backScrollV.showsHorizontalScrollIndicator = NO;
        backScrollV.showsVerticalScrollIndicator = NO;

        [self addSubview:backScrollV];
        imgViewArray = [NSMutableArray array];
        for (int i = 0; i < imageArray.count; i++) {
            id imageContent = imageArray[i];
            YBShowBigImageView *imgV = [[YBShowBigImageView alloc]initWithFrame:CGRectMake(_window_width*i, 0, _window_width, _window_height)];
            if ([imageContent isKindOfClass:[UIImage class]]) {
                imgV.imageView.image = imageContent;
            }else if ([imageContent isKindOfClass:[NSString class]]){
                [imgV.imageView sd_setImageWithURL:[NSURL URLWithString:imageContent]];
            }else if ([imageContent isKindOfClass:[NSDictionary class]]){
                [imgV.imageView sd_setImageWithURL:[NSURL URLWithString:strFormat([imageContent valueForKey:@"thumb"])]];
            }
            
            [backScrollV addSubview:imgV];
            [imgViewArray addObject:imgV];
        }
        [self showBigView];
        [self creatNavi];
    }
    return self;
}
- (void)showBigView{
        [UIView animateWithDuration:0.2 animations:^{
            backScrollV.frame = CGRectMake(0, 0, _window_width, _window_height);
        }];
}
- (void)doreturn{
    [UIView animateWithDuration:0.2 animations:^{
        backScrollV.frame = CGRectMake(_window_width/2, _window_height/2, 0, 0);
    }completion:^(BOOL finished) {
        if (self.returnBlock) {
            self.returnBlock(imageArray);
        }
        [backScrollV removeFromSuperview];
        backScrollV = nil;
        [self removeFromSuperview];
    }];

}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    currentIndex = scrollView.contentOffset.x/_window_width;
    indexLb.text = [NSString stringWithFormat:@"%ld/%ld",currentIndex+1,imageArray.count];

}
-(void)creatNavi {
    
    navi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, statusbarHeight+64)];
    navi.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    [self addSubview:navi];
    
    
    UIButton *retrunBtn = [UIButton buttonWithType:0];
    retrunBtn.frame = CGRectMake(10, 25+statusbarHeight, 30, 30);
    [retrunBtn setImage:[UIImage imageNamed:@"personBack"] forState:0];
    [retrunBtn addTarget:self action:@selector(doreturn) forControlEvents:UIControlEventTouchUpInside];
    [navi addSubview:retrunBtn];
    
    UILabel *titleL = [[UILabel alloc]init];
    titleL.frame = CGRectMake(_window_width/2-40, 25+statusbarHeight, 80, 30);
    titleL.textColor = [UIColor whiteColor];
    titleL.font = [UIFont systemFontOfSize:16];
    titleL.textAlignment = NSTextAlignmentCenter;
    titleL.text = YZMsg(@"预览");
    [navi addSubview:titleL];

    
    indexLb = [[UILabel alloc]init];
    indexLb.frame = CGRectMake(_window_width-100, 22+statusbarHeight, 80, 30);
    indexLb.textColor = [UIColor whiteColor];
    indexLb.font = [UIFont systemFontOfSize:15];
    indexLb.textAlignment = NSTextAlignmentRight;
    indexLb.text = [NSString stringWithFormat:@"%ld/%ld",currentIndex+1,imageArray.count];
    indexLb.hidden = YES;
    [navi addSubview:indexLb];

    
}
-(void)deleteBtnClick{
    [self deleteSucess];
}
- (void)deleteSucess{
    [imageArray removeObjectAtIndex:currentIndex];
    if (imageArray.count == 0) {
        [self doreturn];
    }else{
        UIImageView *imgV = imgViewArray[currentIndex];
        [imgV removeFromSuperview];
        [imgViewArray removeObjectAtIndex:currentIndex];
        if (currentIndex == 0) {
            currentIndex = 0;
        }else{
            currentIndex -= 1;
        }
        indexLb.text = [NSString stringWithFormat:@"%ld/%ld",currentIndex+1,imageArray.count];
        backScrollV.contentSize = CGSizeMake(_window_width*imageArray.count, 0);
        [backScrollV setContentOffset:CGPointMake(_window_width*currentIndex, 0)];
        for (int i = 0; i < imgViewArray.count; i ++) {
            YBShowBigImageView *imgVVVV = imgViewArray[i];
            imgVVVV.x = _window_width * i;
        }
    }

}
- (void)showHideNavi{
    navi.hidden = !navi.hidden;
    if (deleteBtn) {
        deleteBtn.hidden = navi.hidden;
    }
    [self doreturn];
}
-(void)hideDelete{
    deleteBtn.hidden = YES;
}

@end
