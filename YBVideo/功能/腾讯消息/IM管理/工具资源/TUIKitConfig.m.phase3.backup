//
//  TUIKitConfig.m
//  TUIKit
//
//  Created by kennethmia<PERSON> on 2018/11/5.
//  Copyright © 2018年 kennethmiao. All rights reserved.
//

#import "TUIKitConfig.h"
#import "THeader.h"
#import "THelper.h"
#import "TFaceCell.h"
#import "TFaceView.h"
#import "TMoreCell.h"

@interface TUIKitConfig ()
@property (nonatomic, strong) NSMutableDictionary *resourceCache;
@property (nonatomic, strong) NSMutableDictionary *faceCache;
@property (nonatomic, strong) dispatch_queue_t decodeResourceQueue;
@property (nonatomic, strong) dispatch_queue_t decodeFaceQueue;

@end

@implementation TUIKitConfig

- (id)init {
    self = [super init];
    if(self){
        _decodeResourceQueue = dispatch_queue_create("tuikit.decoderesourcequeue", DISPATCH_QUEUE_SERIAL);
        _decodeFaceQueue = dispatch_queue_create("tuikit.decodefacequeue", DISPATCH_QUEUE_SERIAL);
        _resourceCache = [NSMutableDictionary dictionary];
        _faceCache = [NSMutableDictionary dictionary];
    }
    return self;
}

+ (id)defaultConfig {
    TUIKitConfig *config = [[TUIKitConfig alloc] init];
    if(config){
        config.msgCountPerRequest = 20;
        [config defaultResourceCache];
        [config defaultFace];
        [config defaultMore];
        [config groupMore];
    }
    return config;
}

- (void)defaultFace {
    NSMutableArray *faceGroups = [NSMutableArray array];
    //emoji group
    NSMutableArray *emojiFaces = [NSMutableArray array];
    /*
    NSArray *emojis = [NSArray arrayWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"YBFaceEmoji.bundle/emoji/emoji" ofType:@"plist"]];
    for (NSDictionary *dic in emojis) {
        TFaceCellData *data = [[TFaceCellData alloc] init];
        NSString *name = [dic objectForKey:@"face_name"];
        NSString *path = [NSString stringWithFormat:@"emoji/%@", name];
        data.name = name;
        data.path = YBFaceEmoji(path);
        [self addFaceToCache:data.path];
        [emojiFaces addObject:data];
    }
    */
    NSArray *emojis = @[@"[微笑]",@"[色]",@"[发呆]",@"[抽烟]",@"[抠鼻]",@"[哭]",@"[发怒]",@"[呲牙]",@"[睡]",@"[害羞]",@"[调皮]",@"[晕]",@"[衰]",@"[闭嘴]",@"[指点]",@"[关注]",@"[搞定]",@"[胜利]",@"[无奈]",@"[打脸]",@"[大笑]",@"[哈欠]",@"[害怕]",@"[喜欢]",@"[困]",@"[疑问]",@"[伤心]",@"[鼓掌]",@"[得意]",@"[捂嘴]",@"[惊恐]",@"[思考]",@"[吐血]",@"[卖萌]",@"[嘘]",@"[生气]",@"[尴尬]",@"[笑哭]",@"[口罩]",@"[斜眼]",@"[酷]",@"[脸红]",@"[大叫]",@"[眼泪]",@"[见钱]",@"[嘟]",@"[吓]",@"[开心]",@"[想哭]",@"[郁闷]",@"[互粉]",@"[赞]",@"[拜托]",@"[唇]",@"[粉]",@"[666]",@"[玫瑰]",@"[黄瓜]",@"[啤酒]",@"[无语]",@"[纠结]",@"[吐舌]",@"[差评]",@"[飞吻]",@"[再见]",@"[拒绝]",@"[耳机]",@"[抱抱]",@"[嘴]",@"[露牙]",@"[黄狗]",@"[灰狗]",@"[蓝狗]",@"[狗]",@"[脸黑]",@"[吃瓜]",@"[绿帽]",@"[汗]",@"[摸头]",@"[阴险]",@"[擦汗]",@"[瞪眼]",@"[疼]",@"[鬼脸]",@"[拇指]",@"[亲]",@"[大吐]",@"[高兴]",@"[敲打]",@"[加油]",@"[吐]",@"[握手]",@"[18禁]",@"[菜刀]",@"[威武]",@"[给力]",@"[爱心]",@"[心碎]",@"[便便]",@"[礼物]",@"[生日]",@"[喝彩]",@"[雷]"];
    
    for (NSString *emojiName in emojis) {
        TFaceCellData *data = [[TFaceCellData alloc] init];
        NSString *name = emojiName;
        NSString *path = emojiName;
        data.name = name;
        data.path = path;//YBFaceEmoji(path);
        //[self addFaceToCache:data.path];
        [emojiFaces addObject:data];
    }
    if(emojiFaces.count != 0){
        TFaceGroup *emojiGroup = [[TFaceGroup alloc] init];
        emojiGroup.groupIndex = 0;
        emojiGroup.groupPath = YBFaceEmoji(@"emoji/");
        emojiGroup.faces = emojiFaces;
        emojiGroup.rowCount = 3;
        emojiGroup.itemCountPerRow = 7;
        emojiGroup.needBackDelete = YES;
        emojiGroup.menuPath = YBFaceEmoji(@"emoji/menu");
        //[self addFaceToCache:emojiGroup.menuPath];
        [faceGroups addObject:emojiGroup];
        //[self addFaceToCache:YBFaceEmoji(@"msg_del")];
    }
    _faceGroups = faceGroups;
}

- (void)defaultMore {
    
    /**
     *  每行4个,两行的话,这里是纵向排序
     *
     *      xxxxxxxxxxxxxxxxxxx
     *      x
     *      x  0  2  4  6
     *      x  1  3
     *      x
     *      xxxxxxxxxxxxxxxxxx
     */
    
    NSMutableArray *moreMenus = [NSMutableArray array];
    
    TMoreCellData *picture = [[TMoreCellData alloc] init];
    picture.title = YZMsg(@"图片");
    picture.path = @"chat_tool_album";
    [moreMenus addObject:picture];

    TMoreCellData *camera = [[TMoreCellData alloc] init];
    camera.title = YZMsg(@"相机");
    camera.path = @"chat_tool_camera";
    [moreMenus addObject:camera];

    TMoreCellData *gift = [[TMoreCellData alloc] init];
    gift.title = YZMsg(@"语音");
    gift.path = @"chat_tool_v_input.png";
    [moreMenus addObject:gift];

    TMoreCellData *video = [[TMoreCellData alloc] init];
    video.title = YZMsg(@"位置");
    video.path = @"chat_tool_location.png";
    [moreMenus addObject:video];

    _moreMenus = moreMenus;
}

-(void)groupMore {
    
    /**
     *  每行4个,单行行的话,这里是横向排序
     *
     *      xxxxxxxxxxxxxxxxxxx
     *      x
     *      x  0  1  2  3
     *      x
     *      xxxxxxxxxxxxxxxxxx
     */
    
    NSMutableArray *moreMenus = [NSMutableArray array];
    TMoreCellData *picture = [[TMoreCellData alloc] init];
    picture.title = YZMsg(@"相册");
    picture.path = TUIKitResource(@"more_picture");
    [moreMenus addObject:picture];

    TMoreCellData *camera = [[TMoreCellData alloc] init];
    camera.title = YZMsg(@"拍摄");
    camera.path = TUIKitResource(@"more_camera");
    [moreMenus addObject:camera];
    
    _groutMoreMenus = moreMenus;
}

#pragma mark - resource
- (void)defaultResourceCache {
    //common
    [self addResourceToCache:TUIKitResource(@"more_normal")];
    [self addResourceToCache:TUIKitResource(@"more_pressed")];
    [self addResourceToCache:TUIKitResource(@"face_normal")];
    [self addResourceToCache:TUIKitResource(@"face_pressed")];
    [self addResourceToCache:TUIKitResource(@"keyboard_normal")];
    [self addResourceToCache:TUIKitResource(@"keyboard_pressed")];
    [self addResourceToCache:TUIKitResource(@"voice_normal")];
    [self addResourceToCache:TUIKitResource(@"voice_pressed")];
    //text msg
    [self addResourceToCache:TUIKitResource(@"sender_text_normal")];
    [self addResourceToCache:TUIKitResource(@"sender_text_pressed")];
    [self addResourceToCache:TUIKitResource(@"receiver_text_normal")];
    [self addResourceToCache:TUIKitResource(@"receiver_text_pressed")];
    //void msg
    [self addResourceToCache:TUIKitResource(@"sender_voice")];
    [self addResourceToCache:TUIKitResource(@"receiver_voice")];
    [self addResourceToCache:TUIKitResource(@"sender_voice_play_1")];
    [self addResourceToCache:TUIKitResource(@"sender_voice_play_2")];
    [self addResourceToCache:TUIKitResource(@"sender_voice_play_3")];
    [self addResourceToCache:TUIKitResource(@"receiver_voice_play_1")];
    [self addResourceToCache:TUIKitResource(@"receiver_voice_play_2")];
    [self addResourceToCache:TUIKitResource(@"receiver_voice_play_3")];
    //file msg
    [self addResourceToCache:TUIKitResource(@"msg_file")];
    //video msg
    [self addResourceToCache:TUIKitResource(@"play_normal")];
}


- (void)addResourceToCache:(NSString *)path {
    __weak typeof(self) ws = self;
//    dispatch_async(_decodeResourceQueue, ^{
//        UIImage *image = [UIImage imageNamed:path];
//        [ws.resourceCache setValue:image forKey:path];
//    });
    
    [THelper asyncDecodeImage:path queue:_decodeResourceQueue complete:^(NSString *key, UIImage *image) {
        [ws.resourceCache setValue:image forKey:key];
    }];
}

- (UIImage *)getResourceFromCache:(NSString *)path
{
    if(path.length == 0){
        return nil;
    }
    UIImage *image = [_resourceCache objectForKey:path];
    if(!image){
        image = [UIImage imageNamed:path];
    }
    return image;
}
    
- (void)addFaceToCache:(NSString *)path
{
    __weak typeof(self) ws = self;
//    dispatch_async(_decodeFaceQueue, ^{
//        UIImage *image = [UIImage imageNamed:path];
//        [ws.faceCache setValue:image forKey:path];
//    });
    
    [THelper asyncDecodeImage:path queue:_decodeFaceQueue complete:^(NSString *key, UIImage *image) {
        [ws.faceCache setValue:image forKey:key];
    }];
}
        
- (UIImage *)getFaceFromCache:(NSString *)path
{
    if(path.length == 0){
        return nil;
    }
    UIImage *image = [_faceCache objectForKey:path];
    if(!image){
        image = [UIImage imageNamed:path];
    }
    return image;
}



@end
