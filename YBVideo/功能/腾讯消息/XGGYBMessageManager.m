//
//  YBMessageManager.m
//  YBHiMo
//
//  Created by YB007 on 2021/9/14.
//  Copyright © 2021 YB007. All rights reserved.
//

#import "YBMessageManager.h"
#import "TChatC2CController.h"
//#import "YBMsgPageVC.h"

@implementation YBMessageManager

static YBMessageManager *_msgManager = nil;


+(instancetype)shareManager {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _msgManager = [[super allocWithZone:NULL]init];
    });
    return _msgManager;
}

+ (instancetype)allocWithZone:(struct _NSZone *)zone {
    return  [self shareManager];;
}

-(void)goChatListVC; {
    

    
}
// id、user_nickname、avatar
-(void)chatWithUser:(NSDictionary*)userInfo; {
    NSString *userId = strFormat([userInfo valueForKey:@"id"]);
    if ([PublicObj checkNull:userId]) {
        [MBProgressHUD showError:YZMsg(@"信息错误")];
        return;
    }
    /*
    NSString *userName = strFormat([userInfo valueForKey:@"user_nickname"]);
    if ([PublicObj checkNull:userName]) {
        userName = strFormat([userInfo valueForKey:@"user_nickname"]);
    }
    NSString *userAvatar = strFormat([userInfo valueForKey:@"avatar"]);
    if ([PublicObj checkNull:userId] || [PublicObj checkNull:userName] || [PublicObj checkNull:userAvatar]) {
        [MBProgressHUD showError:YZMsg(@"信息错误")];
        return;
    }
    */
    [YBNetworking getRelationWithTouid:userId finish:^(int code, NSDictionary *resDic) {
        if (code == 0) {
            NSString *uname = [NSString stringWithFormat:@"%@",[resDic valueForKey:@"user_nickname"]];
            NSString *icon = [NSString stringWithFormat:@"%@",[resDic valueForKey:@"avatar"]];
            NSString *isAtt = minstr([resDic valueForKey:@"isattention"]);
            NSDictionary *vipInfo = [resDic valueForKey:@"vipinfo"];
            NSString *isVip = minstr([vipInfo valueForKey:@"isvip"]);
            
            TConversationCellData *data = [[TConversationCellData alloc] init];
            data.convId = userId;
            data.convType = TConv_Type_C2C;
            data.title = uname;
            data.userHeader = icon;
            data.userName = uname;
            data.isAtt = isAtt;
            data.isVIP = isVip;
            TChatC2CController *chat = [[TChatC2CController alloc] init];
            chat.conversation = data;
            [[XGGAppDelegate sharedAppDelegate] pushViewController:chat animated:YES];
        }
    }];
}

-(void)getChatCellDataWithTouid:(NSString *)touid finish:(MsgCellDataBlock)finish {
    [YBNetworking getRelationWithTouid:touid finish:^(int code, NSDictionary *resDic) {
        if (code == 0) {
            NSString *uname = [NSString stringWithFormat:@"%@",[resDic valueForKey:@"user_nickname"]];
            NSString *icon = [NSString stringWithFormat:@"%@",[resDic valueForKey:@"avatar"]];
            NSString *isAtt = minstr([resDic valueForKey:@"isattention"]);
            NSDictionary *vipInfo = [resDic valueForKey:@"vipinfo"];
            NSString *isVip = minstr([vipInfo valueForKey:@"isvip"]);
            
            TConversationCellData *data = [[TConversationCellData alloc] init];
            data.convId = touid;
            data.convType = TConv_Type_C2C;
            data.title = uname;
            data.userHeader = icon;
            data.userName = uname;
            data.isAtt = isAtt;
            data.isVIP = isVip;
            if (finish) {
                finish(0,data);
            }
        }else{
            TConversationCellData *data = [[TConversationCellData alloc] init];
            if (finish) {
                finish(code,data);
            }
        }
    }];
}


@end
