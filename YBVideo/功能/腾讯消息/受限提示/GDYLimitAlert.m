//
//  GDYLimitAlert.m
//  iphoneLive
//
//  Created by YB007 on 2022/4/9.
//  Copyright © 2022 cat. All rights reserved.
//

#import "GDYLimitAlert.h"

@interface GDYLimitAlert()

@property(nonatomic,copy)LimitBlock limitEvent;
@property(nonatomic,strong)NSDictionary *limitDic;

@property(nonatomic,strong)UIView *bgView;

@end


@implementation GDYLimitAlert

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch; {
    if ([touch.view isDescendantOfView:self.bgView]) {
        return NO;
    }
    return YES;
}
-(void)dissmissView {
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}

+(instancetype)showLimitWithDic:(NSDictionary *)limitDic complete:(LimitBlock)complete{
    
    GDYLimitAlert *view = [[GDYLimitAlert alloc]init];
    view.frame = CGRectMake(0, 0, _window_width, _window_height);
    view.limitDic = limitDic;
    view.limitEvent = complete;
    //[[XGGAppDelegate sharedAppDelegate].topViewController.view addSubview:view];
    [[UIApplication sharedApplication].delegate.window addSubview:view];
    [view createUI];
    
    return view;
}

-(void)createUI {
    
    /*
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(dissmissView)];
    tap.delegate = self;
    [self addGestureRecognizer:tap];
    */
    
    _bgView = [[UIView alloc]init];
    _bgView.backgroundColor = UIColor.whiteColor;
    _bgView.layer.cornerRadius = 10;
    _bgView.layer.masksToBounds = YES;
    [self addSubview:_bgView];
    [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mas_width).multipliedBy(0.7);
        make.centerX.equalTo(self);
        make.centerY.equalTo(self.mas_centerY).multipliedBy(0.9);
    }];
    
    UILabel *titleL = [[UILabel alloc]init];
    titleL.textColor = RGB_COLOR(@"#323232", 1);
    titleL.font = [UIFont boldSystemFontOfSize:16];
    titleL.numberOfLines = 0;
    titleL.text = minstr([_limitDic valueForKey:@"title"]);
    titleL.textAlignment = NSTextAlignmentCenter;
    [_bgView addSubview:titleL];
    [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.lessThanOrEqualTo(_bgView.mas_width).offset(-30);
        make.top.equalTo(_bgView.mas_top).offset(20);
        make.centerX.equalTo(_bgView);
    }];
    
    UILabel *contentL = [[UILabel alloc]init];
    contentL.textColor = RGB_COLOR(@"#323232", 1);
    contentL.font = SYS_Font(15);
    contentL.numberOfLines = 0;
    NSString *limitMsg = minstr([_limitDic valueForKey:@"msg"]);
    NSMutableAttributedString *m_att = [[NSMutableAttributedString alloc]initWithString:limitMsg];
    if ([limitMsg containsString:@":"]) {
        NSRange range = [limitMsg rangeOfString:@":"];
        int length = (int)limitMsg.length - (int)range.location - 1;
        NSRange newRange = NSMakeRange(range.location+1, length);
        [m_att addAttributes:@{NSForegroundColorAttributeName:Pink_Cor} range:newRange];
    }
    if ([limitMsg containsString:@"："]) {
        NSRange range = [limitMsg rangeOfString:@"："];
        int length = (int)limitMsg.length - (int)range.location - 1;
        NSRange newRange = NSMakeRange(range.location+1, length);
        [m_att addAttributes:@{NSForegroundColorAttributeName:Pink_Cor} range:newRange];
    }
    contentL.attributedText = m_att;
    contentL.textAlignment = NSTextAlignmentCenter;
    [_bgView addSubview:contentL];
    [contentL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.lessThanOrEqualTo(_bgView.mas_width).offset(-30);
        make.top.equalTo(titleL.mas_bottom).offset(15);
        make.centerX.equalTo(_bgView);
    }];
    
    UIButton *knowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [knowBtn setTitle:YZMsg(@"知道了") forState:0];
    knowBtn.backgroundColor = Pink_Cor;
    knowBtn.titleLabel.font = SYS_Font(13);
    [knowBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
    knowBtn.layer.cornerRadius = 5;
    knowBtn.layer.masksToBounds = YES;
    [knowBtn addTarget:self action:@selector(dissmissView) forControlEvents:UIControlEventTouchUpInside];
    [_bgView addSubview:knowBtn];
    [knowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_bgView.mas_width).multipliedBy(0.63);
        make.top.equalTo(contentL.mas_bottom).offset(20);
        make.height.mas_equalTo(30);
        make.centerX.equalTo(_bgView);
        make.bottom.equalTo(_bgView.mas_bottom).offset(-20);
    }];
    
}


@end
