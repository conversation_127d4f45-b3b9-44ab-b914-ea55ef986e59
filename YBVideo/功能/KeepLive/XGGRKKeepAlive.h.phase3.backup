//
//  RKKeepAlive.h
//  YBVideo
//
//  Created by YB007 on 2020/7/22.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <AVFoundation/AVFoundation.h>

static NSString *const kBgTaskName = @"com.rk.root777.KeepAlive";

@interface RKKeepAlive : NSObject

@property(nonatomic,assign)BOOL needKeepAliveInBackground;
@property(nonatomic,strong)AVAudioPlayer *player;

+(instancetype)sharedKeepInstance;

-(void)startAppLifeCycleMonitor;
@end


