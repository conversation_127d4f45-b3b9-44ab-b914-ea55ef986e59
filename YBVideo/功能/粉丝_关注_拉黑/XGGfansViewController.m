#import "fansViewController.h"
#import "fans.h"
#import "fansModel.h"
#import "YBCenterVC.h"
#import "AFNetworking.h"
@interface  fansViewController()<UITableViewDelegate,UITableViewDataSource,guanzhu>
{
    NSInteger a;
    int setvisfans;
    
    int _paging;
}

@property(nonatomic,strong)NSString *navTitle;
@property(nonatomic,strong)NSString *noData1;
@property(nonatomic,strong)NSString *noData2;

@property(nonatomic,strong)NSArray *fansmodels;
@property(nonatomic,strong)NSMutableArray *allArray;
@property(nonatomic,strong)UITableView *tableView;
@end
@implementation fansViewController
-(NSArray *)fansmodels{
    
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in self.allArray) {
        
        fansModel *model = [fansModel modelWithDic:dic];
        
        [array addObject:model];
    }
    _fansmodels = array;
    
    return _fansmodels;
}
- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    setvisfans = 1;
    [self request];
    
}
- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    setvisfans = 0;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    [self.view setBackgroundColor:Normal_Color];
    
    _paging = 1;
    
    if ([_fensiUid isEqual:[Config getOwnID]]) {
        _navTitle = YZMsg(@"我的粉丝");
        _noData1 = YZMsg(@"你还没有粉丝");
        _noData2 = YZMsg(@"赶快邀请小伙伴来关注你吧");
    }else{
        _navTitle = YZMsg(@"TA的粉丝");
        _noData1 = YZMsg(@"TA还没有任何粉丝");
        _noData2 = @"";
    }
    
    self.titleL.text = _navTitle;
    
    self.allArray = [NSMutableArray array];
    self.tableView =  [[UITableView alloc] initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height - 64-statusbarHeight) style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource  = self;
    self.tableView.backgroundColor = Normal_Color;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    
    YBWeakSelf;
    _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        _paging = 1;
        [weakSelf request];
    }];
    
    _tableView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
        _paging +=1;
        [weakSelf request];
    }];
    
    [self.view addSubview:self.tableView];
    [self.view setBackgroundColor:Normal_Color];
    
    //指示器显示
//    [PublicView indictorShow];
    
//    [self request];
    
    
}
-(void)request {
    
    [YBNetworking postWithUrl:@"User.getFansList" Dic:@{@"touid":self.fensiUid,@"p":@(_paging)} Suc:^(int code, id info, NSString *msg) {
        [_tableView.mj_footer endRefreshing];
        [_tableView.mj_header endRefreshing];
        if (code == 0) {
            if (_paging == 1) {
                [_allArray removeAllObjects];
            }
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (infoA.count<=0) {
                [_tableView.mj_footer endRefreshingWithNoMoreData];
            }else{
                [_allArray addObjectsFromArray:infoA];
            }
            
            if (_allArray.count == 0) {
                [PublicView showTextNoData:_tableView text1:_noData1 text2:_noData2 centerY:0.8];
            }else{
                [PublicView hiddenTextNoData:_tableView];
            }
            [_tableView reloadData];
            
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [_tableView.mj_footer endRefreshing];
        [_tableView.mj_header endRefreshing];
    }];
    
}
/*
-(void)requestOr
{
    
    AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
    NSString *url = [purl stringByAppendingFormat:@"?service=User.getFansList&uid=%@&touid=%@&p=%@",[Config getOwnID],self.fensiUid,@"1"];
    
    YBWeakSelf;
    
    [session GET:url parameters:nil progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        //NSLog(@"%@",responseObject);
        NSNumber *number = [responseObject valueForKey:@"ret"] ;
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [responseObject valueForKey:@"data"];
            NSNumber *code = [data valueForKey:@"code"];
            if([code isEqualToNumber:[NSNumber numberWithInt:0]])
            {
                NSArray *info = [data valueForKey:@"info"];
                self.allArray = info;//关注信息复制给数据源
                [self.tableView reloadData];
                //如果数据为空
                if (self.allArray.count == 0) {
                    
                    [PublicView showTextNoData:self.tableView text1:weakSelf.noData1 text2:weakSelf.noData2];
                }else{
                    [PublicView hiddenTextNoData:self.tableView];
                }
            }
            else{
                
                [PublicView showTextNoData:self.tableView text1:weakSelf.noData1 text2:weakSelf.noData2];
            }
        }
        else
        {
            [PublicView showTextNoData:self.tableView text1:weakSelf.noData1 text2:weakSelf.noData2];
        }
        //指示器消失
        [PublicView indictorHide];
    }
         failure:^(NSURLSessionDataTask *task, NSError *error)
     {
         
         //指示器消失
         [PublicView indictorHide];
         if (self.allArray.count == 0) {
             [PublicView showTextNoData:self.tableView text1:weakSelf.noData1 text2:weakSelf.noData2];
         }
     }];
    
}
*/
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.fansmodels.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    fans *cell = [fans cellWithTableView:tableView];
    cell.backgroundColor = CellRow_Cor;
    fansModel *model = self.fansmodels[indexPath.row];
    cell.model = model;
    cell.guanzhuDelegate = self;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 90;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    fansModel *model = _fansmodels[indexPath.row];
    YBCenterVC *center = [[YBCenterVC alloc]init];
    center.otherUid = model.uid;
    center.isPush = YES;
    //[self.navigationController pushViewController:center animated:YES];
    [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];
    
}
-(void)doGuanzhu:(NSString *)st{
    [self request];
}





@end
