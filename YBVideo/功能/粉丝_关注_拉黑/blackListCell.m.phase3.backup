//
//  BlackListCell.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/30.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "BlackListCell.h"

@implementation BlackListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    _iconBtn.layer.masksToBounds = YES;
    _iconBtn.layer.cornerRadius = _iconBtn.height/2;
    _iconBtn.userInteractionEnabled = NO;
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    
}


+(BlackListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    BlackListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BlackListCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"BlackListCell" owner:nil options:nil]objectAtIndex:0];
        cell.delBtn.layer.masksToBounds = YES;
        cell.delBtn.layer.cornerRadius = cell.delBtn.height/2;
        cell.delBtn.layer.borderWidth = 1;
        cell.delBtn.layer.borderColor = Pink_Cor.CGColor;
    }
    return cell;
}
- (void)setModel:(fansModel *)model {
    _model = model;
    [_iconBtn sd_setImageWithURL:[NSURL URLWithString:_model.icon] forState:0];
    _nameL.text = _model.name;
    _timeL.text = _model.time;
    [_delBtn setTitle:YZMsg(@"移除") forState:0];
    [_delBtn setTitleColor:Pink_Cor forState:0];
}

- (IBAction)clickIconBtn:(UIButton *)sender {
    //预留
}

- (IBAction)clickDelBtn:(UIButton *)sender {
    
    NSString *url = [NSString stringWithFormat:@"User.setBlack&uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_model.uid];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            if (self.blackEvent) {
                self.blackEvent(@"");
            }
            [MBProgressHUD showPop:YZMsg(@"移除成功")];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
}
@end
