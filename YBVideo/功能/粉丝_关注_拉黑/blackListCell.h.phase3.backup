//
//  BlackListCell.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/30.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "fansModel.h"

typedef void (^BlackBlock)(NSString *type);

@interface BlackListCell : UITableViewCell


@property (weak, nonatomic) IBOutlet UIButton *iconBtn;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UILabel *timeL;
@property (weak, nonatomic) IBOutlet UIButton *delBtn;
@property(nonatomic,strong)fansModel *model;

@property(nonatomic,copy)BlackBlock blackEvent;

- (IBAction)clickIconBtn:(UIButton *)sender;

- (IBAction)clickDelBtn:(UIButton *)sender;

+(BlackListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath;

@end
