//
//  fansModel.m
//  YBVideo
//
//  Created by cat on 16/4/1.
//  Copyright © 2016年 cat. All rights reserved.
//
#import "fansModel.h"
@implementation fansModel
-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.time = [NSString stringWithFormat:@"%@",[dic valueForKey:@"addtime"]];
        self.name = [NSString stringWithFormat:@"%@",[dic valueForKey:@"user_nickname"]];
        self.level = [NSString stringWithFormat:@"%@",[dic valueForKey:@"level"]];
        self.sex = [NSString stringWithFormat:@"%@",[dic valueForKey:@"sex"]];
        self.signature = [NSString stringWithFormat:@"%@",[dic valueForKey:@"signature"]];
        self.icon = [NSString stringWithFormat:@"%@",[dic valueForKey:@"avatar"]];
        self.uid = [NSString stringWithFormat:@"%@",[dic valueForKey:@"id"]];
        self.isattention = [NSString stringWithFormat:@"%@",[dic valueForKey:@"isattention"]];
        self.level_anchor = [NSString stringWithFormat:@"%@",[dic valueForKey:@"level_anchor"]];
        self.level_icon = [NSString stringWithFormat:@"%@",[dic valueForKey:@"level_icon"]];
        self.level_anchor_icon = [NSString stringWithFormat:@"%@",[dic valueForKey:@"level_anchor_icon"]];
        self.fansNum = [NSString stringWithFormat:@"%@",[dic valueForKey:@"fans"]];
    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)dic{
    return  [[self alloc]initWithDic:dic];
}
@end
