//
//  fans.m
//  YBVideo
//
//  Created by cat on 16/4/1.
//  Copyright © 2016年 cat. All rights reserved.
//

#import "fans.h"
#import "fansModel.h"
#import "fansViewController.h"
#import "SDWebImage/UIButton+WebCache.h"

#import "Config.h"
@implementation fans

-(void)drawRect:(CGRect)rect{
//    CGContextRef ctx = UIGraphicsGetCurrentContext();
//    CGContextSetLineWidth(ctx,1);
//    CGContextSetStrokeColorWithColor(ctx,[UIColor groupTableViewBackgroundColor].CGColor);
//    CGContextMoveToPoint(ctx,0,self.frame.size.height);
//    CGContextAddLineToPoint(ctx,(self.frame.size.width),self.frame.size.height);
//    CGContextStrokePath(ctx);
}

-(void)setModel:(fansModel *)model{
    _model = model;
    _nameL.text = _model.name;
    _signatureL.text = _model.signature;
    //性别 1男
     if ([[_model valueForKey:@"sex"] isEqual:@"1"])
    {
        self.sexL.image = [UIImage imageNamed:@"choice_sex_nanren"];
    }
    else
    {
        self.sexL.image = [UIImage imageNamed:@"choice_sex_nvren"];
    }
    //级别
    self.levelL.image = [UIImage imageNamed:[NSString stringWithFormat:@"host_%@",_model.level_anchor]];
    self.hostlevel.image = [UIImage imageNamed:[NSString stringWithFormat:@"leve%@",_model.level]];
    
//    [self.levelL sd_setImageWithURL:[NSURL URLWithString:_model.level_icon] placeholderImage:[UIImage imageNamed:@"leve1"]];
//    [self.hostlevel sd_setImageWithURL:[NSURL URLWithString:_model.level_anchor_icon] placeholderImage:[UIImage imageNamed:@"host_1"]];

    
    //头像
    [self.iconV sd_setBackgroundImageWithURL:[NSURL URLWithString:_model.icon]  forState:UIControlStateNormal placeholderImage:[UIImage imageNamed:@"bg1"]];
    self.iconV.layer.cornerRadius = 30;
    self.iconV.layer.masksToBounds = YES;
    self.IDL.text = [NSString stringWithFormat:@"ID:%@",_model.uid];
    self.fansNumL.text = [NSString stringWithFormat:@"%@:%@",YZMsg(@"粉丝"),_model.fansNum];
    //关注
//    [self.guanzhubtn setImage:[UIImage imageNamed:@"me_following(1)"] forState:UIControlStateSelected];
//    [self.guanzhubtn setImage:[UIImage imageNamed:@"me_follow(1)"] forState:UIControlStateNormal];
    
    self.guanzhubtn.layer.masksToBounds = YES;
    self.guanzhubtn.layer.cornerRadius = 13;
    self.guanzhubtn.layer.borderWidth = 1;
    if ([_model.isattention isEqual:@"0"]) {
        //self.guanzhubtn.selected = NO;
        self.guanzhubtn.layer.borderColor = Pink_Cor.CGColor;
        [self.guanzhubtn setTitle:YZMsg(@"关注") forState:0];
        [self.guanzhubtn setTitleColor:Pink_Cor forState:0];
    }else{
        self.guanzhubtn.layer.borderColor = RGB_COLOR(@"#4A4A52", 1).CGColor;
         [self.guanzhubtn setTitleColor:RGB_COLOR(@"#4A4A52", 1) forState:0];
        [self.guanzhubtn setTitle:YZMsg(@"已关注") forState:0];
        //self.guanzhubtn.selected = YES;
    }
}

+(fans *)cellWithTableView:(UITableView *)tv{
    fans *cell = [tv dequeueReusableCellWithIdentifier:@"a"];
    if (!cell) {
       cell = [[NSBundle mainBundle]loadNibNamed:@"fans" owner:self options:nil].lastObject;
    }
    return cell;
}
- (IBAction)gaunzhuBTN:(UIButton *)btn{
    if ([[Config getOwnID] intValue]<=0) {
        [PublicObj warnLogin];
        return;
    }
    if ([[Config getOwnID] isEqual:_model.uid]) {
        [MBProgressHUD showError:YZMsg(@"不能关注自己")];
            return;
    }
    
    NSString *url = [NSString stringWithFormat:@"User.setAttent&uid=%@&touid=%@&token=%@",[Config getOwnID],_model.uid,[Config getOwnToken]];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSString *infoDic = [info firstObject];
            NSString *isattent = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isattent"]];
            if ([isattent isEqual:@"0"]) {
                self.guanzhubtn.layer.borderColor = Pink_Cor.CGColor;
                [self.guanzhubtn setTitle:YZMsg(@"关注") forState:0];
                [self.guanzhubtn setTitleColor:Pink_Cor forState:0];
            }else{
                self.guanzhubtn.layer.borderColor = RGB_COLOR(@"#4A4A52", 1).CGColor;
                [self.guanzhubtn setTitleColor:RGB_COLOR(@"#4A4A52", 1) forState:0];
                [self.guanzhubtn setTitle:YZMsg(@"已关注") forState:0];
            }
            [self.guanzhuDelegate doGuanzhu:nil];
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
    
}
@end
