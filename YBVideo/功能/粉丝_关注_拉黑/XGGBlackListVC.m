//
//  BlackListVC.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/30.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "BlackListVC.h"

#import "fansModel.h"
#import "BlackListCell.h"
#import "YBCenterVC.h"

@interface BlackListVC ()<UITableViewDelegate,UITableViewDataSource>
{
    int _paging;
}
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)NSMutableArray *dateArray;
@property(nonatomic,strong)NSArray *models;

@end

@implementation BlackListVC

-(void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self pullData];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"黑名单");
    
    self.dateArray = [NSMutableArray array];
    _paging = 1;
    [self.view addSubview:self.tableView];
}

#pragma mark -
- (NSArray *)models {
    NSMutableArray *m_array = [NSMutableArray array];
    for (NSDictionary *dic in _dateArray) {
        fansModel *model = [fansModel modelWithDic:dic];
        [m_array addObject:model];
    }
    _models = m_array;
    return _models;
}
-(void)pullData {
    NSString *url = [NSString stringWithFormat:@"User.getBlackList&uid=%@&token=%@&touid=%@&p=%d",[Config getOwnID],[Config getOwnToken],[Config getOwnID],_paging];
    
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging == 1) {
                [_dateArray removeAllObjects];
            }
            if (infoA.count<=0) {
                [_tableView.mj_footer endRefreshingWithNoMoreData];
            }else {
                [_dateArray addObjectsFromArray:infoA];
            }
            if (_dateArray.count<=0) {
                [PublicView showTextNoData:_tableView text1:@"" text2:YZMsg(@"暂无数据") centerY:0.8];
            }else{
                [PublicView hiddenTextNoData:_tableView];
            }
            [_tableView reloadData];
            
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
#pragma mark - UITableViewDelegate、UITableViewDataSource

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 80;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    
    return self.models.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    BlackListCell *cell = [BlackListCell cellWithTab:tableView andIndexPath:indexPath];
    cell.model = _models[indexPath.row];
    cell.backgroundColor = CellRow_Cor;
    YBWeakSelf;
    cell.blackEvent = ^(NSString *type) {
        [weakSelf pullData];
    };
    cell.selectedBackgroundView = [[UIImageView alloc]initWithImage:[PublicObj getImgWithColor:SelCell_Col]];
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    fansModel *model = _models[indexPath.row];
    YBCenterVC *center = [[YBCenterVC alloc]init];
    center.otherUid = model.uid;
    center.isPush = YES;
    //[self.navigationController pushViewController:center animated:YES];
    [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];
}

#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height - 64-statusbarHeight-ShowDiff)style:UITableViewStylePlain];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = Normal_Color;
        
        YBWeakSelf;
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            _paging = 1;
            [weakSelf pullData];
        }];
        _tableView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            _paging +=1;
            [weakSelf pullData];
        }];
    }
    return _tableView;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    
}

#pragma mark - 导航

@end
