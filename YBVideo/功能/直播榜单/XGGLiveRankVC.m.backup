//
//  LiveRankVC.m
//  YBVideo
//
//  Created by ybRRR on 2021/2/27.
//  Copyright © 2021 cat. All rights reserved.
//

#import "LiveRankVC.h"
#import "LiveRankCell.h"
#import "YBCheckLiveObj.h"
#import "YBCenterVC.h"
@interface LiveRankVC ()<UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong)UITableView *rankTable;
@property (nonatomic, strong)NSMutableArray *dataArr;

@end

@implementation LiveRankVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"直播榜单");
    self.dataArr = [NSMutableArray array];

    [self.view addSubview:self.rankTable];
    [self getWeekShowLists];

}
-(void)getWeekShowLists{
    
    [YBNetworking postWithUrl:@"Home.getWeekShowLists" Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_rankTable.mj_header endRefreshing];
        if (code == 0) {
            _dataArr = info;
            [_rankTable reloadData];
            }
        } Fail:^(id fail) {
            
        }];
}

-(UITableView *)rankTable {
    if (!_rankTable) {
        //5个像素间隔
        _rankTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 75+statusbarHeight, _window_width, _window_height-75-statusbarHeight-ShowDiff) style:UITableViewStylePlain];
        _rankTable.delegate = self;
        _rankTable.dataSource = self;
        _rankTable.separatorStyle = UITableViewCellStyleDefault;
        _rankTable.backgroundColor = RGB_COLOR(@"#09051E", 1);
        _rankTable.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            [self getWeekShowLists];
        }];
    }
    return _rankTable;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _dataArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 50;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    LiveRankCell *cell = [LiveRankCell cellWithTab:tableView andIndexPath:indexPath];
    if (indexPath.row < 3) {
        cell.numImg.image = [UIImage imageNamed:[NSString stringWithFormat:@"live_rank%ld",indexPath.row+1]];
        cell.numLb.hidden = YES;
    }else{
        cell.numLb.hidden = NO;
        cell.numLb.text = [NSString stringWithFormat:@"%ld",indexPath.row+1];
    }
    cell.rankData = _dataArr[indexPath.row];
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    NSDictionary *rankDic = _dataArr[indexPath.row];
    if ([minstr([rankDic valueForKey:@"islive"]) isEqual:@"1"]) {
        [YBCheckLiveObj checkLiveManeger].liveUid = minstr([rankDic valueForKey:@"uid"]);
        [YBCheckLiveObj checkLiveManeger].liveStream = minstr([rankDic valueForKey:@"stream"]);
        [YBCheckLiveObj checkLiveManeger].currentIndex = 0;
        [YBCheckLiveObj checkLiveManeger].listArray = nil;
        [[YBCheckLiveObj checkLiveManeger] checkLiving];

    }else{
        YBCenterVC *center = [[YBCenterVC alloc]init];
        center.otherUid = minstr([rankDic valueForKey:@"uid"]);;
        center.isPush = YES;
        //[self.navigationController pushViewController:center animated:YES];
        [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];

    }

}
@end
