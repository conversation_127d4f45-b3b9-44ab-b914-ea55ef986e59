//
//  LiveRankCell.m
//  YBVideo
//
//  Created by ybRR<PERSON> on 2021/2/27.
//  Copyright © 2021 cat. All rights reserved.
//

#import "LiveRankCell.h"

@implementation LiveRankCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}
+(LiveRankCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
   LiveRankCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LiveRankCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"LiveRankCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.contentView.backgroundColor = CellRow_Cor;

    return cell;
}
-(void)setRankData:(NSDictionary *)rankData
{
    [self.headImg sd_setImageWithURL:[NSURL URLWithString:minstr([rankData valueForKey:@"avatar"])]];
    self.nameLb.text = minstr([rankData valueForKey:@"user_nickname"]);
    self.popularityLb.text =[NSString stringWithFormat:@"%@%@",minstr([rankData valueForKey:@"total"]),YZMsg(@"人气")];
}
@end
