//
//  YBGiftModel.h
//  YBVideo
//
//  Created by YB007 on 2019/8/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>



@interface YBGiftModel : NSObject

@property(nonatomic,strong)NSString *idStr;
@property(nonatomic,strong)NSString *typeStr;           //0-普通、1-豪华、2-涂鸦
@property(nonatomic,strong)NSString *markStr;           //0-普通 2-守护
@property(nonatomic,strong)NSString *giftNameStr;       //名称
@property(nonatomic,strong)NSString *needCoinStr;       //需要金币
@property(nonatomic,strong)NSString *needCoinNameStr;   //需要金币+名称
@property(nonatomic,strong)NSString *giftIconStr;       //头像
@property(nonatomic,strong)NSString *nums;              //背包礼物---礼物个数

- (instancetype)initWithDic:(NSDictionary *)dic;
+(instancetype)modelWithDic:(NSDictionary *)dic;
@end


