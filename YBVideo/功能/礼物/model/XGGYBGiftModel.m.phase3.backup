//
//  YBGiftModel.m
//  YBVideo
//
//  Created by YB007 on 2019/8/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBGiftModel.h"

@implementation YBGiftModel

- (instancetype)initWithDic:(NSDictionary *)dic
{
    self = [super init];
    if (self) {
        
        _idStr = minstr([dic valueForKey:@"id"]);
        _typeStr = minstr([dic valueForKey:@"type"]);
        _markStr = minstr([dic valueForKey:@"mark"]);
        _giftNameStr = minstr([dic valueForKey:@"giftname"]);
        _needCoinStr = minstr([dic valueForKey:@"needcoin"]);
        _needCoinNameStr = minstr([dic valueForKey:@"needcoin_name"]);
        _giftIconStr = minstr([dic valueForKey:@"gifticon"]);
        _nums = @"0";
        if ([dic valueForKey:@"nums"]) {
            _nums = minstr([dic valueForKey:@"nums"]);
        }
    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)dic;{
    return [[self alloc]initWithDic:dic];
}
@end
