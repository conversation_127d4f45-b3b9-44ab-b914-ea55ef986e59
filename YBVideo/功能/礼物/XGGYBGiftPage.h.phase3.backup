//
//  YBGiftPage.h
//  YBVideo
//
//  Created by YB007 on 2020/10/10.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "YBGiftView.h"
//#import "RKPaintedGiftView.h"
//#import "RKPaintedHeader.h"

@interface YBGiftPage : UIView

@property(nonatomic,copy)RewardGiftBlock giftEvent;
@property(nonatomic,assign)SendGiftType sendType;       //送出类型(视频、直播)
/**
 * sendType = SendGiftType_Video ==> 视频id
 * sendType = SendGiftType_Live ==> 主播id
 */
@property(nonatomic,strong)NSString *receiveID;
/**
 * sendType = SendGiftType_Live 需要此参数
 */
@property(nonatomic,strong)NSString *receiveStream;

@property(nonatomic,strong)RKPaintedGiftView *paintedRegion;//手绘礼物绘制区域


+(instancetype)showGiftViewComplete:(RewardGiftBlock)complete;
-(void)requestGiftData;
@end


