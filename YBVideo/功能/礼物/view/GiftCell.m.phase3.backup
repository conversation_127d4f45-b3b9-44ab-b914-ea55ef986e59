//
//  YBGiftCell.m
//  iphoneLive
//
//  Created by <PERSON><PERSON>007 on 2019/8/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBGiftCell.h"

@implementation YBGiftCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    // cell size:   62/375/4  =  0.661(比列)
    
}

- (void)setModel:(YBGiftModel *)model {
    _model = model;
    
    [_iconIV sd_setImageWithURL:[NSURL URLWithString:_model.giftIconStr]];
    _nameL.text = _model.giftNameStr;
    _coinNameL.text = _model.needCoinNameStr;
    
    
}

@end
