//
//  YBGiftCell.m
//  YBVideo
//
//  Created by YB007 on 2019/8/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBGiftCell.h"

@implementation YBGiftCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    // cell size:   62/375/4  =  0.661(比列)
    
}

- (void)setModel:(YBGiftModel *)model {
    _model = model;
    
    [_iconIV sd_setImageWithURL:[NSURL URLWithString:_model.giftIconStr]];
    _nameL.text = _model.giftNameStr;
    if (_typeOfGift == ShowTypeOfGift_Bag) {
        _coinNameL.text = [NSString stringWithFormat:@"%@%@",_model.nums,YZMsg(@"个")];
    }else {
        _coinNameL.text = _model.needCoinNameStr;
    }
    _giftTypeIV.hidden = YES;
    
    _rightTopL.hidden = NO;
    if ([_model.typeStr isEqual:@"1"]) {
        _rightTopL.text = YZMsg(@"豪");
    }else if([_model.typeStr isEqual:@"2"]){
        _rightTopL.text = YZMsg(@"涂");
    }else {
        _rightTopL.hidden = YES;
    }
    
    _leftTopL.hidden = YES;
    if ([_model.markStr isEqual:@"2"]) {
        _leftTopL.hidden = NO;
        _leftTopL.text = YZMsg(@"守");
    }
    
    /*
    _giftTypeIV.hidden = NO;
    if ([_model.typeStr isEqual:@"1"]) {
        [_giftTypeIV setImage:[UIImage imageNamed:@"gift_豪"]];
    }else if([_model.typeStr isEqual:@"2"]){
        [_giftTypeIV setImage:[UIImage imageNamed:@"gift_涂"]];
    }else {
        _giftTypeIV.hidden = YES;
    }
     */
    
}

@end
