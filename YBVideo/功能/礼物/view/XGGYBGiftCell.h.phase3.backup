//
//  YBGiftCell.h
//  YBVideo
//
//  Created by YB007 on 2019/8/20.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "YBGiftModel.h"

typedef NS_ENUM(NSInteger ,ShowTypeOfGift) {
    ShowTypeOfGift_Normal,  //普通礼物
    ShowTypeOfGift_Bag,     //背包礼物
};
@interface YBGiftCell : UICollectionViewCell

@property(nonatomic,assign)ShowTypeOfGift typeOfGift;
@property(nonatomic,strong)YBGiftModel *model;

@property (weak, nonatomic) IBOutlet UIImageView *iconIV;
@property (weak, nonatomic) IBOutlet UIImageView *flagIV;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UILabel *coinNameL;
@property (weak, nonatomic) IBOutlet UIImageView *giftTypeIV;   //隐藏
@property (weak, nonatomic) IBOutlet UILabel *leftTopL;         //左上角标记--守
@property (weak, nonatomic) IBOutlet UILabel *rightTopL;        //右上角标记--豪、涂


@end


