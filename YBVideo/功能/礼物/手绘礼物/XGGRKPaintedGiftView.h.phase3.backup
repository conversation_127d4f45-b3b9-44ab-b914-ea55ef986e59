//
//  RKPaintedGiftView.h
//  YBVideo
//
//  Created by YB007 on 2020/8/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void (^PaintedBlock)(void);

@interface RKPaintedGiftView : UIView

@property(nonatomic,copy)PaintedBlock paintedEvent;

@property(nonatomic,strong)NSString *giftNameStr;           //礼物名称
@property(nonatomic,strong)NSString *giftPathStr;           //礼物地址

@property(nonatomic,assign)BOOL showPainted;                //展示绘制区域
@property(nonatomic,strong)MASViewAttribute *painteLeftMas; //个数的左边【打赏礼物-右边开始】

-(CGSize)getPaintedRegion;
-(NSArray<NSString *> *)getPaintedPointArray;

@end


