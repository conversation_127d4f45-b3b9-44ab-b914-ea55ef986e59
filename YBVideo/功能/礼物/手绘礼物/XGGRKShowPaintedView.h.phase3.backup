//
//  RKShowPaintedView.h
//  YBVideo
//
//  Created by YB007 on 2020/8/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>



@interface RKShowPaintedView : UIView

@property(nonatomic,strong)NSString *giftPathStr;               //手绘礼物地址
@property(nonatomic,strong)NSArray *paintedPointArray;          //手绘礼物图形路径

@property(nonatomic,assign)CGFloat paintedWidth;                //发送方手绘礼物区域宽度
@property(nonatomic,assign)CGFloat paintedHeight;               //发送方手绘礼物区域高度

-(void)destroyPaitend;

@end



