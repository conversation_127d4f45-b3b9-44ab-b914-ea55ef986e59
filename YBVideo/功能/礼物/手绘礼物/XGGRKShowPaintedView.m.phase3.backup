//
//  RKShowPaintedView.m
//  YBVideo
//
//  Created by YB007 on 2020/8/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RKShowPaintedView.h"
#import "RKPaintedHeader.h"
@interface RKShowPaintedView() {
    int imgCount;
}
@property(nonatomic,strong)NSMutableArray<UIImageView *> *paintedGiftsArray;

@property(nonatomic,assign)CGFloat giftWidth;
@property(nonatomic,assign)CGFloat giftHeight;
@end

@implementation RKShowPaintedView


- (instancetype)init {
    self = [super init];
    if (self) {
        
        self.hidden = YES;
        imgCount = 0;
        _giftWidth = _giftHeight = _window_width/10;
        self.paintedGiftsArray = [NSMutableArray array];
    }
    return self;
}
- (void)setPaintedWidth:(CGFloat)paintedWidth{
    _paintedWidth = paintedWidth;
    [self resetSelfUI];
}
- (void)setPaintedHeight:(CGFloat)paintedHeight {
    _paintedHeight = paintedHeight;
    [self resetSelfUI];
}

-(void)resetSelfUI {
    if (_paintedWidth<=0) {
        _paintedWidth = 1;
    }
    if (_paintedHeight<=0) {
        _paintedHeight = 1;
    }
    CGFloat hhhhh = self.superview.width*_paintedHeight/_paintedWidth;
    [self mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.superview);
        make.width.equalTo(self.superview);
        make.height.mas_equalTo(hhhhh);
        make.top.equalTo(self.superview.mas_top).offset(statusbarHeight);
    }];
}

- (void)setPaintedPointArray:(NSArray *)paintedPointArray {
    _paintedPointArray = paintedPointArray;
    [_paintedGiftsArray removeAllObjects];
    [self layoutIfNeeded];
    self.hidden = NO;
    [self removeAllSubViews];
    if ([PublicObj checkNull:_giftPathStr] || _paintedPointArray.count<=0) {
        return;
    }
    for (NSDictionary* dic in _paintedPointArray) {
        //NSDictionary* dic = [self dictionaryWithJsonString:dicStr];
        //坐标转换
        CGFloat originX = [[dic objectForKey:@"x"] floatValue];
        CGFloat originY = [[dic objectForKey:@"y"] floatValue];
        if (_paintedWidth<=0) {
            _paintedWidth = 1;
        }
        if (_paintedHeight<=0) {
            _paintedHeight = 1;
        }
        
        CGFloat nowX = originX * self.width/_paintedWidth;
        CGFloat nowY = originY * self.height/_paintedHeight;
        
        CGPoint point = CGPointMake(nowX, nowY);
        UIImageView* imageView = [[UIImageView alloc] init];
        [imageView sd_setImageWithURL:[NSURL URLWithString:_giftPathStr]];
        [self addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker* make) {
            make.top.equalTo(self).offset(point.y - _giftWidth/2);
            make.left.equalTo(self).offset(point.x - _giftHeight/2);
            make.width.mas_equalTo(_giftWidth);
            make.height.mas_equalTo(_giftHeight);
        }];
        imageView.hidden = YES;
        [_paintedGiftsArray addObject:imageView];
        
    }
    
    [self showAnimation];
    
    //NSLog(@"00000000-----");
    
}
-(void)showAnimation {
    
    //NSLog(@"000000000");
    if (imgCount >= _paintedGiftsArray.count) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            imgCount = 0;
            [_paintedGiftsArray removeAllObjects];
            for (UIImageView *imgIV in _paintedGiftsArray) {
                [imgIV.layer removeAnimationForKey:@"animateLayer"];
            }
            //整体移除动画
            [UIView animateWithDuration:0.36 animations:^{
                self.layer.transform = CATransform3DMakeScale(2, 2, 1);
            } completion:^(BOOL finished) {
                self.layer.transform = CATransform3DIdentity;
                self.hidden = YES;
                [self removeAllSubViews];
            }];
            
        });
    }else {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(PaintedPerTime*0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self startAnimation:_paintedGiftsArray[imgCount]];
            imgCount ++;
            [self showAnimation];
        });
    }
    
}

-(void)startAnimation:(UIImageView *)imgIV {
    //NSLog(@"11111111111");
    imgIV.hidden = NO;
    
    //每一帧动画
    CAKeyframeAnimation* animation = [CAKeyframeAnimation animationWithKeyPath:@"transform"];
    animation.duration = PaintedPerTime*0.6;
    NSMutableArray *values = [NSMutableArray array];
    CGFloat fromValue = 1.5;
    CGFloat toValue = 1.0;
    int num = 10;
    for (int i = 0; i<num; i++) {
        //NSLog(@"rk==Cur:%f",fromValue);
        [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(fromValue, fromValue, fromValue)]];
        fromValue -= (fromValue-toValue)/num;
        if (fromValue == toValue) {
            break;
        }
    }
    animation.values = values;
    [imgIV.layer addAnimation:animation forKey:@"animateLayer"];
   
}
-(void)destroyPaitend; {
    self.hidden = YES;
    for (UIImageView *imgIV in _paintedGiftsArray) {
        [imgIV.layer removeAnimationForKey:@"animateLayer"];
    }
    _paintedPointArray = @[];
}

//- (NSDictionary* )dictionaryWithJsonString:(NSString* )jsonString {
//    if (jsonString == nil) {
//        return nil;
//    }
//    NSData* jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
//    NSError* err;
//    NSDictionary* dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:&err];
//    if (err) {
//        NSLog(@"json解析失败：%@",err);
//        return nil;
//    }
//    return dic;
//}
@end
