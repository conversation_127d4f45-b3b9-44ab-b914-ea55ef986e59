//
//  YBGiftView.m
//  YBVideo
//
//  Created by YB007 on 2019/8/19.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBGiftView.h"
#import "YBGiftCell.h"
#import "YBGiftModel.h"
#import "YBRechargeVC.h"

@interface CollectionCellWhite : UICollectionViewCell

@end

@implementation CollectionCellWhite

- (instancetype)initWithFrame:(CGRect)frame andPlayDic:(NSDictionary *)zhuboDic{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
    }
    return self;
}

@end

@interface YBGiftView()<UIGestureRecognizerDelegate,UICollectionViewDelegate,UICollectionViewDataSource> {
    NSArray *_dataArray;        //礼物+金币
    NSArray *_giftList;         //礼物
    NSString *_coinNum;         //金币数量
    NSInteger _pageCount;
    NSMutableArray *_selArray;
    BOOL isRight;
    NSTimer *_continueTimer;
    int _intervalTime;
    CGFloat _collectionHeght;
    int _perRowNums;
    int _rowNums;
    NSString *_methodUrl;
}
@property(nonatomic,strong)UILabel *coinL;
@property(nonatomic,strong)YBGiftModel *selModel;
@property(nonatomic,strong)NSArray *giftModels;
@property(nonatomic,strong)CABasicAnimation *animation;
/**
 *  新加礼物组功能: 父视图为 _bottomMixBg
 */
@property(nonatomic,strong)UIImageView *multipleIV;         //礼物组
@property(nonatomic,strong)NSArray *mutipleGiftA;
@property(nonatomic,strong)UIView *multipleGiftCountBg;     //礼物组数量选择背景
@property(nonatomic,strong)UIButton *multipleCountBtn;      //礼物组数量选择按钮
@property(nonatomic,strong)UIButton *multipleSendBtn;       //礼物组赠送按钮
@property(nonatomic,assign)ShowTypeOfGift typeOfGift;

@end

@implementation YBGiftView

- (void)awakeFromNib {
    [super awakeFromNib];
    
    //添加背包礼物，_giftTitleL被YBGiftPage中的tabbar代替
    _giftTitleL.text = YZMsg(@"打赏礼物");
    _giftTitleL.hidden = YES;
    _sendBtn.hidden = NO;
    _continueSendBtn.hidden = YES;
    _continueSendBtn.titleLabel.numberOfLines = 0;
    [_continueSendBtn setBackgroundImage:[UIImage imageNamed:@"gift_continus"] forState:0];
    [_sendBtn setBackgroundImage:[PublicObj getImgWithColor:RGB_COLOR(@"#dcdcdc", 1)] forState:0];
    [_sendBtn setBackgroundImage:[PublicObj getImgWithColor:Pink_Cor] forState:UIControlStateSelected];
    
    _perRowNums = 5;
    _rowNums = 2;
    _pageCount = 0;
    _intervalTime = 5;
    _selArray = [NSMutableArray array];
    
    //礼物组
    [_bottomMixBg addSubview:self.multipleIV];
    [_bottomMixBg bringSubviewToFront:self.continueSendBtn];
    [_multipleIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(_bottomMixBg);
        make.right.equalTo(_bottomMixBg).offset(-15);
        make.width.mas_equalTo(94);
        make.height.mas_equalTo(26);
    }];
    _mutipleGiftA = @[@"1314",@"520",@"100",@"88",@"66",@"10",@"1"];

}
- (NSArray *)giftModels {
    
//    if (!_giftModels) {
        NSMutableArray *m_array = [NSMutableArray array];
        for (NSDictionary*dic in _giftList) {
            YBGiftModel *model = [YBGiftModel modelWithDic:dic];
            [m_array addObject:model];
//        }
        _giftModels = [m_array copy];
    }
    return _giftModels;
}

+(instancetype)showTypeOfGiftViewComplete:(RewardGiftBlock)complete {
    YBGiftView *gV = [[[NSBundle mainBundle]loadNibNamed:@"YBGiftView" owner:nil options:nil]objectAtIndex:0];
    if (complete) {
        gV.giftEvent = ^(int eventCode,NSDictionary *giftDic) {
            complete(eventCode,giftDic);
        };
    }
    [gV setUpView];
    return gV;
}

-(void)setUpView{

    CGFloat subViewHeight = (_window_width*322/375+ShowDiff);
    if (IS_IPHONE_5) {
        subViewHeight = (_window_width*322/375+ShowDiff)+15;
    }
    self.frame = CGRectMake(0, _window_height-subViewHeight, _window_width, subViewHeight);
    
    NSLog(@"=:%@",self.bgView);
    _bgViewHeight.constant = subViewHeight;
    
    _bgView.backgroundColor = UIColor.clearColor;
    _collectionHeght = _window_width*220/375;
    [_bgView layoutIfNeeded];
    
    //
    _GiftLayout.itemCountPerRow = _perRowNums;
    _GiftLayout.rowCount = _rowNums;
    _GiftLayout.minimumLineSpacing = 0;
    _GiftLayout.minimumInteritemSpacing = 0;
    _GiftLayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    
    _giftCollectionView.delegate = self;
    _giftCollectionView.dataSource = self;
    [_giftCollectionView registerNib:[UINib nibWithNibName:@"YBGiftCell" bundle:nil] forCellWithReuseIdentifier:@"YBGiftCell"];
    [_giftCollectionView registerClass:[CollectionCellWhite class] forCellWithReuseIdentifier:@"CellWhite"];
   
}
-(void)requestGiftData:(NSString *)giftUrl {
    _methodUrl = giftUrl;
    _typeOfGift = ShowTypeOfGift_Normal;
    if ([giftUrl containsString:@"Backpack.getBackpack"]) {
        _typeOfGift = ShowTypeOfGift_Bag;
    }
    [MBProgressHUD showMessage:@""];
    [self pullData];
}
-(void)pullData {
    [YBNetworking postWithUrl:_methodUrl Dic:nil Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            _dataArray = [NSArray arrayWithArray:info];
            NSDictionary *dataDic = @{};
            if ([[_dataArray firstObject] isKindOfClass:[NSDictionary class]]) {
                dataDic = [_dataArray firstObject];
            }
            if (!_selArray) {
                _selArray = [NSMutableArray array];
            }
            if ([[dataDic valueForKey:@"giftlist"]isKindOfClass:[NSArray class]]) {
                _giftList = [NSArray arrayWithArray:[dataDic valueForKey:@"giftlist"]];
            }
            _coinNum = minstr([dataDic valueForKey:@"coin"]);
            _coinL.text = minstr(_coinNum);
            [Config saveUserCoin:_coinNum];
            _pageCount = _giftList.count;
            while (_pageCount % 8 !=0) {
                ++_pageCount;
            }
            for (int i = 0 ; i<_giftList.count; i++) {
                [_selArray addObject:@"0"];
            }
            if (_giftList.count > 0) {
                _giftPage.hidden = NO;
                if ((int)_giftList.count % (_perRowNums*_rowNums) == 0) {
                    _giftPage.numberOfPages = _giftList.count/(_perRowNums*_rowNums);
                }
                else{
                    _giftPage.numberOfPages = _giftList.count/(_perRowNums*_rowNums) + 1;
                }
            }else {
                _giftPage.hidden = YES;
            }
            [_giftCollectionView reloadData];
            if (_giftList.count <= 0) {
                [PublicView showTextNoData:_giftCollectionView text1:@"" text2:YZMsg(@"暂无数据") centerY:0.6];
            }else {
                [PublicView hiddenTextNoData:_giftCollectionView];
            }
            if (_typeOfGift == ShowTypeOfGift_Normal) {
                //金币列表
                [self setCoinListUI];
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return _pageCount;
}
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.item >= self.giftModels.count) {
        UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"CellWhite" forIndexPath:indexPath];
        return cell;
    } else {
        YBGiftCell *cell = (YBGiftCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"YBGiftCell" forIndexPath:indexPath];
        
        YBGiftModel *model = self.giftModels[indexPath.item];
        [collectionView selectItemAtIndexPath:indexPath animated:NO scrollPosition:UICollectionViewScrollPositionNone];
        cell.typeOfGift = _typeOfGift;
        cell.model = model;
        NSString *selFlag = [NSString stringWithFormat:@"%@",_selArray[indexPath.item]];
        
        if ([selFlag isEqual:@"1"]) {
            cell.flagIV.hidden = NO;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [cell.iconIV.layer addAnimation:self.animation forKey:nil];
            });
        }
        else{
            cell.flagIV.hidden = YES;
            [cell.iconIV.layer removeAllAnimations];
        }
        return cell;
    }
    
}
- (void)addAnimation:(NSIndexPath *)indexpath{
    for (YBGiftCell *cell in _giftCollectionView.visibleCells) {
        if ([[_giftCollectionView indexPathForCell:cell] isEqual:indexpath]) {
            [cell.iconIV.layer addAnimation:self.animation forKey:nil];
        }else{
            [cell.iconIV.layer removeAllAnimations];
        }
    }
    
}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView;{
    [_giftCollectionView reloadData];
}
-(CABasicAnimation *)animation{
    if (!_animation) {
        _animation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
        
        _animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
        _animation.duration = 0.88;
        _animation.repeatCount = 9999999;
        _animation.autoreverses = YES;
        _animation.fromValue = [NSNumber numberWithFloat:1];
        _animation.toValue = [NSNumber numberWithFloat:0.8];
    }
    return _animation;
}

-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.item>=self.giftModels.count) {
        return;
    }
    if (self.giftViewSwitchEvent) {
        self.giftViewSwitchEvent(self);
    }
    _sendBtn.selected = YES;
    [_selArray removeAllObjects];
    for (int i=0; i<self.giftModels.count; i++) {
        [_selArray addObject:@"0"];
    }
    [_selArray replaceObjectAtIndex:indexPath.item withObject:@"1"];
    _selModel = self.giftModels[indexPath.item];
    [self destroyTimer];
    _continueSendBtn.hidden = YES;
    
    //判断手绘礼物
    [self judesSelIsPaintedGift];
   
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.01 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [_giftCollectionView reloadData];
    });
    
}
-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    return CGSizeMake(_window_width/_perRowNums,(_collectionHeght-2)/_rowNums);
}
-(UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0,0,0,0);
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 0;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section{
    return 0;
}
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset{
    
    if(velocity.x>0){
        
        NSLog(@"右滑");
        isRight = YES;
    }else{
        
        NSLog(@"左滑");
        isRight = NO;
    }
    NSLog(@"scrollViewWillEndDragging");
    if (isRight) {
        _giftPage.currentPage+=1;
    }
    else{
        _giftPage.currentPage-=1;
    }
}
-(void)cancelGiftSel {
    if (_selArray.count <= 0 || !_selModel) {
        return;
    }
    [_selArray removeAllObjects];
    for (int i=0; i<self.giftModels.count; i++) {
        [_selArray addObject:@"0"];
    }
    _selModel = nil;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.01 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [_giftCollectionView reloadData];
    });
    [self destroyTimer];
    _continueSendBtn.hidden = YES;
    _sendBtn.selected = NO;
    _paintedRegion.showPainted = NO;
    _sendBtn.hidden = NO;
    _multipleIV.hidden = YES;
}
- (void)judesSelIsPaintedGift  {
    _coinNum = minstr([Config getUserCoin]);
    _coinL.text = _coinNum;
    if (!_selModel) {
        return;
    }
    //0-普通  1-豪华  2-涂鸦
    if ([_selModel.typeStr isEqual:@"2"]) {
        _sendBtn.hidden = NO;
        _multipleIV.hidden = YES;
        _paintedRegion.showPainted = YES;
        
        _paintedRegion.giftNameStr = _selModel.giftNameStr;
        _paintedRegion.giftPathStr = _selModel.giftIconStr;
        
    }else if ([_selModel.typeStr isEqual:@"1"]) {
        _sendBtn.hidden = NO;
        _multipleIV.hidden = YES;
        _paintedRegion.showPainted = NO;
    }else{
        _sendBtn.hidden = YES;
        _multipleIV.hidden = NO;
        _paintedRegion.showPainted = NO;
    }
}
-(void)dismiss {
    if (self.giftEvent) {
        self.giftEvent(-1, @{});
    }
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
    [self destroyTimer];
}

- (IBAction)clickCloseBtn:(UIButton *)sender {
    [self dismiss];
}

- (IBAction)clickSendBtn:(UIButton *)sender {
    
    if (_selModel && [_selModel.typeStr isEqual:@"2"] && [_paintedRegion getPaintedPointArray].count < PaintedMinNum) {
        NSString *msg = [NSString stringWithFormat:@"%@%d%@",YZMsg(@"最少绘制"),PaintedMinNum,YZMsg(@"个礼物")];
        [MBProgressHUD showPop:msg];
        return;
    }
    
    if (_sendType == SendGiftType_Video) {
        [self sendVideoGift];
    }else {
        [self sendLiveGift];
    }
}

- (IBAction)clickContinueSendBtn:(UIButton *)sender {
    if (_sendType == SendGiftType_Video) {
        [self sendVideoGift];
    }else {
        [self sendLiveGift];
    }
}

#pragma mark - 视频送礼物开始
-(void)sendVideoGift {
    if(!_selModel){
        return;
    }
    if ([PublicObj checkNull:_receiveID]) {
        [MBProgressHUD showPop:YZMsg(@"缺少礼物信息")];
        return;
    }
    
    NSString *lianfa = @"y";
    NSLog(@"发送了%@",_selModel.giftNameStr);
    NSString *sendCount;
    //0-普通  1-豪华  2-涂鸦
    if ([_selModel.typeStr isEqual:@"2"]) {
        lianfa = @"n";
        _continueSendBtn.hidden = YES;
        _multipleIV.hidden = YES;
        _sendBtn.hidden = NO;
        sendCount = [NSString stringWithFormat:@"%lu",(unsigned long)[_paintedRegion getPaintedPointArray].count];
    }else if ([_selModel.typeStr isEqual:@"1"]) {
        lianfa = @"n";
        _continueSendBtn.hidden = YES;
        _multipleIV.hidden = YES;
        _sendBtn.hidden = NO;
        sendCount = @"1";
    }else{
        /*
        _continueSendBtn.hidden = NO;
        _multipleIV.hidden = YES;
        _sendBtn.hidden = YES;
        _intervalTime = 5;
        [_continueSendBtn setTitle:[NSString stringWithFormat:@"%@\n %ds",YZMsg(@"连发"),_intervalTime] forState:UIControlStateNormal];
        if(_continueTimer == nil){
            _continueTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(continueSendInterval) userInfo:nil repeats:YES];
        }
        */
        sendCount = minstr(_multipleCountBtn.titleLabel.text);
    }
    NSDictionary *giftDic = @{
                              @"videoid":minstr(_receiveID),
                              @"giftid":_selModel.idStr,
                              @"giftcount":minstr(sendCount),
                              @"ispack":_typeOfGift == ShowTypeOfGift_Bag ?@"1":@"0",
                              };
    if ([lianfa isEqual:@"n"]) {
        [MBProgressHUD showMessage:@""];
    }
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Video.videoSendGift" Dic:giftDic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            if([_selModel.typeStr isEqual:@"0"]){
                [weakSelf startContinueSendTimer];
            }
            NSDictionary *infoDic = [info firstObject];
            _coinNum = minstr([infoDic valueForKey:@"coin"]);
            _coinL.text = _coinNum;
            [Config saveUserCoin:_coinNum];
            NSDictionary *giftInfoDic = [infoDic valueForKey:@"giftinfo"];

            NSMutableDictionary *dic = @{
                @"avatar":[Config getUserAvatar],
                @"nickname":[Config getOwnNicename],
                @"uid":[Config getOwnID],
                @"paintedPath":[_paintedRegion getPaintedPointArray],
                @"paintedWidth":@([_paintedRegion getPaintedRegion].width),
                @"paintedHeight":@([_paintedRegion getPaintedRegion].height),
            }.mutableCopy;
            [dic addEntriesFromDictionary:giftInfoDic];
            if ([_selModel.typeStr isEqual:@"2"]) {
                [weakSelf dismiss];
            }
            if (weakSelf.giftEvent) {
                weakSelf.giftEvent(0, dic);
            }
            if (_typeOfGift == ShowTypeOfGift_Bag) {
                [weakSelf pullData];
            }
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
    
}
#pragma mark - 视频送礼物结束
#pragma mark - 直播送礼物开始
-(void)sendLiveGift {
    if(!_selModel){
        return;
    }
    if ([PublicObj checkNull:_receiveID] || [PublicObj checkNull:_receiveStream]) {
        [MBProgressHUD showPop:YZMsg(@"缺少礼物信息")];
        return;
    }
    
    NSString *lianfa = @"y";
    NSLog(@"发送了%@",_selModel.giftNameStr);
    NSString *sendCount;
    //0-普通  1-豪华  2-涂鸦
    if ([_selModel.typeStr isEqual:@"2"]) {
        lianfa = @"n";
        _continueSendBtn.hidden = YES;
        _multipleIV.hidden = YES;
        _sendBtn.hidden = NO;
        sendCount = [NSString stringWithFormat:@"%lu",(unsigned long)[_paintedRegion getPaintedPointArray].count];
    }else if ([_selModel.typeStr isEqual:@"1"]) {
        lianfa = @"n";
        _continueSendBtn.hidden = YES;
        _multipleIV.hidden = YES;
        _sendBtn.hidden = NO;
        sendCount = @"1";
    }else{
        /*
        _continueSendBtn.hidden = NO;
        _multipleIV.hidden = YES;
        _sendBtn.hidden = YES;
        _intervalTime = 5;
        [_continueSendBtn setTitle:[NSString stringWithFormat:@"%@\n %ds",YZMsg(@"连发"),_intervalTime] forState:UIControlStateNormal];
        if(_continueTimer == nil){
            _continueTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(continueSendInterval) userInfo:nil repeats:YES];
        }
        */
        sendCount = minstr(_multipleCountBtn.titleLabel.text);
    }
    NSDictionary *giftDic = @{
                              @"liveuid":minstr(_receiveID),
                              @"stream":minstr(_receiveStream),
                              @"giftid":_selModel.idStr,
                              @"giftcount":minstr(sendCount),
                              @"ispack":_typeOfGift == ShowTypeOfGift_Bag ?@"1":@"0",
                              };
    if ([lianfa isEqual:@"n"]) {
        [MBProgressHUD showMessage:@""];
    }
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Live.sendGift" Dic:giftDic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            if([_selModel.typeStr isEqual:@"0"]){
                [weakSelf startContinueSendTimer];
            }
            NSDictionary *infoDic = [info firstObject];
            _coinNum = minstr([infoDic valueForKey:@"coin"]);
            _coinL.text = _coinNum;
            [Config saveUserCoin:_coinNum];

            NSMutableDictionary *dic = @{
                @"avatar":[Config getUserAvatar],
                @"nickname":[Config getOwnNicename],
                @"uid":[Config getOwnID],
                @"lianfa":lianfa,
                @"paintedPath":[_paintedRegion getPaintedPointArray],
                @"paintedWidth":@([_paintedRegion getPaintedRegion].width),
                @"paintedHeight":@([_paintedRegion getPaintedRegion].height),
            }.mutableCopy;
            [dic addEntriesFromDictionary:infoDic];
            if ([_selModel.typeStr isEqual:@"2"]) {
                [weakSelf dismiss];
            }
            if (weakSelf.giftEvent) {
                weakSelf.giftEvent(0, dic);
            }
            if (_typeOfGift == ShowTypeOfGift_Bag) {
                [weakSelf pullData];
            }
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
#pragma mark - 直播送礼物结束
-(void)startContinueSendTimer {
    _continueSendBtn.hidden = NO;
    _multipleIV.hidden = YES;
    _sendBtn.hidden = YES;
    _intervalTime = 5;
    [_continueSendBtn setTitle:[NSString stringWithFormat:@"%@\n %ds",YZMsg(@"连发"),_intervalTime] forState:UIControlStateNormal];
    if(_continueTimer == nil){
        _continueTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(continueSendInterval) userInfo:nil repeats:YES];
    }
    
}
-(void)continueSendInterval {
    _intervalTime-=1;
    [_continueSendBtn setTitle:[NSString stringWithFormat:@"%@\n %ds",YZMsg(@"连发"),_intervalTime] forState:UIControlStateNormal];
    if (_intervalTime == 0) {
        [self destroyTimer];
        _continueSendBtn.hidden = YES;
        _multipleIV.hidden = NO;
//        _sendBtn.hidden = NO;
    }
}
-(void)destroyTimer {
    if (_continueTimer) {
        [_continueTimer invalidate];
        _continueTimer = nil;
    }
}

-(void)setCoinListUI {

    _coinScroll.contentSize = CGSizeMake(_coinScroll.width, 0);

    if (!_coinL) {
        //图标
        UIImageView *coinIV = [[UIImageView alloc]init];
        [coinIV setImage:[UIImage imageNamed:@"礼物-金币"]];
        [_coinScroll addSubview:coinIV];
        [coinIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_coinScroll.mas_left).offset(10);
            make.centerY.equalTo(_coinScroll);
            make.width.height.mas_equalTo(15);
        }];
        
        //
        _coinL = [[UILabel alloc]init];
        _coinL.textColor = RGB_COLOR(@"#323232", 13);
        _coinL.font = SYS_Font(13);
        [_coinScroll addSubview:_coinL];
        [_coinL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(coinIV.mas_right).offset(5);
            make.centerY.equalTo(coinIV);
        }];
        
        //
        UIButton *chargeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [chargeBtn setTitle:YZMsg(@"充值") forState:UIControlStateNormal];
        [chargeBtn setTitleColor:Pink_Cor forState:0];
        chargeBtn.titleLabel.font = SYS_Font(13);
        [chargeBtn addTarget:self action:@selector(clikcChargeBtn) forControlEvents:UIControlEventTouchUpInside];
        [_coinScroll addSubview:chargeBtn];
        chargeBtn.contentEdgeInsets = UIEdgeInsetsMake(5, 8, 5, 8);
        [chargeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(coinIV);
            make.left.equalTo(_coinL.mas_right).offset(15);
        }];
    }
    
    _coinL.text = minstr(_coinNum);
    
}
-(void)clikcChargeBtn {
    [self dismiss];
    
    YBRechargeVC *chargeVC = [[YBRechargeVC alloc]init];
    [[XGGAppDelegate sharedAppDelegate]pushViewController:chargeVC animated:YES];
}

#pragma mark - 礼物组 开始
- (UIImageView *)multipleIV {
    if (!_multipleIV) {
        _multipleIV = [[UIImageView alloc]init];
        _multipleIV.image = [UIImage imageNamed:@"gift_send"];
        _multipleIV.userInteractionEnabled = YES;
        _multipleIV.hidden = YES;
        
        //选择数量按钮
        _multipleCountBtn = [UIButton buttonWithType:0];
        _multipleCountBtn.frame = CGRectMake(0, 0, 47, 26);
        [_multipleCountBtn setTitle:@"1" forState:0];
        [_multipleCountBtn setImage:[UIImage imageNamed:@"gift_top"] forState:0];
        [_multipleCountBtn addTarget:self action:@selector(multipleCountBtnClick) forControlEvents:UIControlEventTouchUpInside];
        _multipleCountBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        [_multipleCountBtn setTitleColor:Pink_Cor forState:0];
        _multipleCountBtn.titleEdgeInsets = UIEdgeInsetsMake(0,-_multipleCountBtn.imageView.image.size.width, 0, _multipleCountBtn.imageView.image.size.width);
        _multipleCountBtn.imageEdgeInsets = UIEdgeInsetsMake(0, 35, 0, 3);
        [_multipleIV addSubview:_multipleCountBtn];
        //发送按钮
        _multipleSendBtn = [UIButton buttonWithType:UIButtonTypeSystem];
        [_multipleSendBtn setTitle:YZMsg(@"赠送") forState:UIControlStateNormal];
        [_multipleSendBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_multipleSendBtn addTarget:self action:@selector(clickSendBtn:) forControlEvents:UIControlEventTouchUpInside];
        _multipleSendBtn.frame = CGRectMake(47,0,47,26);
        [_multipleIV addSubview:_multipleSendBtn];
        
    }
    return _multipleIV;
}

-(void)multipleCountBtnClick {
    if (!_multipleGiftCountBg) {
        _multipleGiftCountBg = [[UIView alloc]initWithFrame:CGRectMake(0, 0, self.width, self.height)];
        _multipleGiftCountBg.backgroundColor = [UIColor clearColor];
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideMultipleGiftCountBg)];
        [_multipleGiftCountBg addGestureRecognizer:tap];
        [self addSubview:_multipleGiftCountBg];
        UIImageView *imgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width-110, _multipleGiftCountBg.height-26*_mutipleGiftA.count-18-40-ShowDiff, 47, 26*_mutipleGiftA.count+18)];
        imgView.image = [UIImage imageNamed:@"gift_nums"];
        imgView.userInteractionEnabled = YES;
        [_multipleGiftCountBg addSubview:imgView];
        for (int i = 0; i < _mutipleGiftA.count; i ++) {
            UIButton *btn = [UIButton buttonWithType:0];
            btn.frame = CGRectMake(0, i*26, 47, 26);
            [btn setTitle:_mutipleGiftA[i] forState:0];
            [btn setTitleColor:Pink_Cor forState:0];
            btn.titleLabel.font = [UIFont systemFontOfSize:10];
            [btn addTarget:self action:@selector(selectGiftNum:) forControlEvents:UIControlEventTouchUpInside];
            [imgView addSubview:btn];
        }
    }else{
        _multipleGiftCountBg.hidden = NO;
    }
    [_multipleCountBtn setImage:[UIImage imageNamed:@"gift_down"] forState:0];
}
-(void)hideMultipleGiftCountBg {
    _multipleGiftCountBg.hidden = YES;
    [_multipleCountBtn setImage:[UIImage imageNamed:@"gift_top"] forState:0];
}
- (void)selectGiftNum:(UIButton *)sender{
    _multipleGiftCountBg.hidden = YES;
    [_multipleCountBtn setTitle:sender.titleLabel.text forState:0];
    [_multipleCountBtn setImage:[UIImage imageNamed:@"gift_top"] forState:0];
}
#pragma mark - 礼物组 结束

@end
