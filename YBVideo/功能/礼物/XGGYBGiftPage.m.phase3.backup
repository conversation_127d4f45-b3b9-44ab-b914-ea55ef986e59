//
//  YBGiftPage.m
//  YBVideo
//
//  Created by YB007 on 2020/10/10.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBGiftPage.h"

#import "TYTabPagerBar.h"
#import "TYPagerView.h"

@interface YBGiftPage()<TYPagerViewDataSource,TYPagerViewDelegate,TYTabPagerBarDataSource,TYTabPagerBarDelegate,UIGestureRecognizerDelegate>

@property(nonatomic,strong)TYTabPagerBar *tabBar;
@property(nonatomic,strong)TYPagerView *pageView;
@property(nonatomic,strong)NSArray *pageData;

@property(nonatomic,strong)YBGiftView *normalGiftView;
@property(nonatomic,strong)YBGiftView *bagGiftView;

@end

@implementation YBGiftPage

+(instancetype)showGiftViewComplete:(RewardGiftBlock)complete {
    YBGiftPage *gV = [[YBGiftPage alloc]init];
    gV.giftEvent = complete;
    [gV setUpView];
    return gV;
}

-(void)requestGiftData{
    [_normalGiftView requestGiftData:@"Live.getGiftList"];
    [_bagGiftView requestGiftData:@"Backpack.getBackpack"];
}

-(void)setUpView{
    self.frame = [UIScreen mainScreen].bounds;
    
    UITapGestureRecognizer *tagGes = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(dismiss)];
    tagGes.delegate = self;
    [self addGestureRecognizer:tagGes];
    
    //注意这里的高度和YBGiftView中bgView高度[_bgViewHeight]相同
    CGFloat subViewHeight = (_window_width*322/375+ShowDiff);
    if (IS_IPHONE_5) {
        subViewHeight = (_window_width*322/375+ShowDiff)+15;
    }
    
    _pageView = [[TYPagerView alloc]init];
    _pageView.frame = CGRectMake(0, _window_height - subViewHeight, _window_width, subViewHeight);

    _pageView.layout.autoMemoryCache = NO;
    _pageView.dataSource = self;
    _pageView.delegate = self;
    [_pageView.layout registerClass:[UIView class] forItemWithReuseIdentifier:@"cellId"];
    _pageView.scrollView.bounces = NO;
    _pageView.backgroundColor = UIColor.whiteColor;
    [self addSubview:_pageView];
    
    _tabBar = [[TYTabPagerBar alloc]init];
    _tabBar.frame = CGRectMake(10, _window_height - subViewHeight, _window_width/2, 44);
    _tabBar.layout.barStyle = TYPagerBarStyleProgressElasticView;
    _tabBar.dataSource = self;
    _tabBar.delegate = self;
    _tabBar.layout.cellWidth = 60;
    _tabBar.layout.selectedTextColor = RGB_COLOR(@"#000000", 1);
    _tabBar.layout.normalTextColor = RGB_COLOR(@"#969696", 1);
    _tabBar.layout.progressColor = Pink_Cor;
    _tabBar.layout.progressHeight = 3;
    _tabBar.layout.progressRadius = 1.5;
    _tabBar.layout.progressVerEdging = 5;
    _tabBar.backgroundColor = UIColor.whiteColor;
    [_tabBar registerClass:[TYTabPagerBarCell class] forCellWithReuseIdentifier:[TYTabPagerBarCell cellIdentifier]];
    [self addSubview:_tabBar];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:_pageView.bounds  byRoundingCorners:UIRectCornerTopRight|UIRectCornerTopLeft cornerRadii:CGSizeMake(10, 10)];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = _pageView.bounds;
    maskLayer.path = maskPath.CGPath;
    _pageView.layer.mask = maskLayer;

    _pageData = @[YZMsg(@"打赏礼物"),YZMsg(@"我的背包")];
    YBWeakSelf;
    _normalGiftView = [YBGiftView showTypeOfGiftViewComplete:^(int eventCode,NSDictionary *giftDic) {
        if (eventCode == -1) {
            [weakSelf dismiss];
        }
        if (weakSelf.giftEvent) {
            weakSelf.giftEvent(eventCode, giftDic);
        }
    }];
    _normalGiftView.giftViewSwitchEvent = ^(id currentView) {
        [weakSelf cancelSelFlag:currentView];
    };
    _bagGiftView = [YBGiftView showTypeOfGiftViewComplete:^(int eventCode,NSDictionary *giftDic) {
        if (eventCode == -1) {
            [weakSelf dismiss];
        }
        if (weakSelf.giftEvent) {
            weakSelf.giftEvent(eventCode, giftDic);
        }
    }];
    _bagGiftView.giftViewSwitchEvent = ^(id currentView) {
        [weakSelf cancelSelFlag:currentView];
    };

    [_tabBar reloadData];
    [_pageView updateData];
    
    //添加手绘区域
    [self addSubview:self.paintedRegion];
    [_paintedRegion mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.width.mas_equalTo(_window_width);
        make.top.equalTo(self.mas_top).offset(statusbarHeight);
        make.bottom.equalTo(self.pageView.mas_top).offset(45);
    }];
    _normalGiftView.paintedRegion = _paintedRegion;
    _bagGiftView.paintedRegion = _paintedRegion;
    
    [self showGiftPage];
}
#pragma mark - 必要参数
- (void)setReceiveID:(NSString *)receiveID {
    _receiveID = receiveID;
    _normalGiftView.receiveID = _receiveID;
    _bagGiftView.receiveID = _receiveID;
}
- (void)setReceiveStream:(NSString *)receiveStream {
    _receiveStream = receiveStream;
    _normalGiftView.receiveStream = _receiveStream ? _receiveStream:@"";
    _bagGiftView.receiveStream = _receiveStream ? _receiveStream:@"";
    
}
- (void)setSendType:(SendGiftType)sendType {
    _sendType = sendType;
    _normalGiftView.sendType = _sendType;
    _bagGiftView.sendType = _sendType;
}

-(void)cancelSelFlag:(id)currentView {
    if (currentView == _normalGiftView) {
        [_bagGiftView cancelGiftSel];
    }else {
        [_normalGiftView cancelGiftSel];
    }
}
-(void)showGiftPage {
    [[UIApplication sharedApplication].delegate.window addSubview:self];
    
    [UIView animateWithDuration:0.3 animations:^{
        
    } completion:^(BOOL finished) {
        self.backgroundColor = RGB_COLOR(@"#000000", 0.4);
    }];
}
-(void)dismiss {
    if (self.giftEvent) {
        self.giftEvent(-1, @{});
    }
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch; {
    if (_paintedRegion.showPainted) {
        return NO;
    }
    if ([touch.view isDescendantOfView:self.pageView] || [touch.view isDescendantOfView:self.tabBar]) {
        return NO;
    }
    return YES;
}
#pragma mark - TYTabPagerBarDataSource
- (NSInteger)numberOfItemsInPagerTabBar {
    return _pageData.count;
}
- (UICollectionViewCell<TYTabPagerBarCellProtocol> *)pagerTabBar:(TYTabPagerBar *)pagerTabBar cellForItemAtIndex:(NSInteger)index {
    UICollectionViewCell<TYTabPagerBarCellProtocol> *cell = [pagerTabBar dequeueReusableCellWithReuseIdentifier:[TYTabPagerBarCell cellIdentifier] forIndex:index];
    cell.titleLabel.text = _pageData[index];
    return cell;
}

#pragma mark - TYTabPagerBarDelegate
- (CGFloat)pagerTabBar:(TYTabPagerBar *)pagerTabBar widthForItemAtIndex:(NSInteger)index {
    NSString *title = _pageData[index];
    return [pagerTabBar cellWidthForTitle:title];
}
- (void)pagerTabBar:(TYTabPagerBar *)pagerTabBar didSelectItemAtIndex:(NSInteger)index {
    [_pageView scrollToViewAtIndex:index animate:YES];
}

#pragma mark - TYPagerViewDataSource
- (NSInteger)numberOfViewsInPagerView {
    return _pageData.count;
}
- (UIView *)pagerView:(TYPagerView *)pagerView viewForIndex:(NSInteger)index prefetching:(BOOL)prefetching {
    if (index == 0) {
        return _normalGiftView;
    } else{
        return _bagGiftView;
    }
}

#pragma mark - TYPagerViewDelegate
- (void)pagerView:(TYPagerView *)pagerView willAppearView:(UIView *)view forIndex:(NSInteger)index {
    _paintedRegion.showPainted = NO;
    if (view == _normalGiftView) {
        [_normalGiftView judesSelIsPaintedGift];
    }
    if (view == _bagGiftView) {
        [_bagGiftView judesSelIsPaintedGift];
    }
}

- (void)pagerView:(TYPagerView *)pagerView willDisappearView:(UIView *)view forIndex:(NSInteger)index {
    //NSLog(@"---------willDisappearView:%ld",index);
    
}

- (void)pagerView:(TYPagerView *)pagerView transitionFromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex animated:(BOOL)animated {
    //NSLog(@"fromIndex:%ld, toIndex:%ld",fromIndex,toIndex);
    [_tabBar scrollToItemFromIndex:fromIndex toIndex:toIndex animate:animated];
}

- (void)pagerView:(TYPagerView *)pagerView transitionFromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex progress:(CGFloat)progress {
    //NSLog(@"fromIndex:%ld, toIndex:%ld progress%.3f",fromIndex,toIndex,progress);
    [_tabBar scrollToItemFromIndex:fromIndex toIndex:toIndex progress:progress];
}

#pragma mark - 手绘礼物
- (RKPaintedGiftView *)paintedRegion {
    if (!_paintedRegion) {
        _paintedRegion = [[RKPaintedGiftView alloc]init];
        YBWeakSelf;
        _paintedRegion.paintedEvent = ^{
            [weakSelf dismiss];
        };
    }
    return _paintedRegion;
}
@end
