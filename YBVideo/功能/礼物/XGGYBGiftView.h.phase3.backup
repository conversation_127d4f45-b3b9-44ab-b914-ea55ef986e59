//
//  YBGiftView.h
//  YBVideo
//
//  Created by YB007 on 2019/8/19.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "LWLCollectionViewHorizontalLayout.h"
#import "YBPageControl.h"
#import "RKPaintedGiftView.h"
#import "RKPaintedHeader.h"

typedef NS_ENUM(NSInteger,SendGiftType)
{
    SendGiftType_Video,     //给视频送礼物
    SendGiftType_Live,      //给直播主播送礼物
};
typedef void (^GiftViewSwitchBlock)(id currentView);
/**
 * eventCode -1关闭  0发送礼物  giftDic礼物信息
 */
typedef void (^RewardGiftBlock)(int eventCode,NSDictionary *giftDic);

@interface YBGiftView : UIView

@property(nonatomic,copy)RewardGiftBlock giftEvent;
@property(nonatomic,copy)GiftViewSwitchBlock giftViewSwitchEvent;
@property (weak, nonatomic) IBOutlet UIView *bgView;
@property (weak, nonatomic) IBOutlet UILabel *giftTitleL;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgViewHeight;
@property (weak, nonatomic) IBOutlet LWLCollectionViewHorizontalLayout *GiftLayout;
@property (weak, nonatomic) IBOutlet UICollectionView *giftCollectionView;
@property (weak, nonatomic) IBOutlet UIView *bottomMixBg;
@property (weak, nonatomic) IBOutlet UIScrollView *coinScroll;
@property (weak, nonatomic) IBOutlet UIButton *sendBtn;
@property (weak, nonatomic) IBOutlet UIButton *continueSendBtn;
@property (weak, nonatomic) IBOutlet YBPageControl *giftPage;

@property(nonatomic,assign)SendGiftType sendType;       //送出类型(视频、直播)

/**
 * sendType = SendGiftType_Video ==> 视频id
 * sendType = SendGiftType_Live ==> 主播id
 */
@property(nonatomic,strong)NSString *receiveID;

/**
 * sendType = SendGiftType_Live 需要此参数
 */
@property(nonatomic,strong)NSString *receiveStream;
@property(nonatomic,strong)RKPaintedGiftView *paintedRegion;

+(instancetype)showTypeOfGiftViewComplete:(RewardGiftBlock)complete;
-(void)requestGiftData:(NSString *)giftUrl;
-(void)cancelGiftSel;
-(void)judesSelIsPaintedGift;
@end


