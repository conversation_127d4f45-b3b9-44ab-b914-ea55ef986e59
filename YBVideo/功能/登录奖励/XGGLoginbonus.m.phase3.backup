//
//  Loginbonus.m
//  yunbaolive
//
//  Created by Rookie on 2017/4/1.
//  Copyright © 2017年 cat. All rights reserved.
//

#import "Loginbonus.h"
#import "LogFirstCell.h"
#import "LogFirstCell2.h"
#import <YYText/YYLabel.h>
#import <YYText/NSAttributedString+YYText.h>

static NSString* IDENTIFIER = @"collectionCell";

static NSString *IDENTIFIER2 = @"collectionCell2";

@interface Loginbonus ()<UICollectionViewDelegate,UICollectionViewDataSource>
{
    CADisplayLink *_link;
    LogFirstCell *selectCell;
    LogFirstCell2 *selectCell2;
    UIImageView *sevendayimageview;
    NSString *logDayCount;
    NSString *logis_bonus;

    NSString *logDay;
    NSArray *numArr ;
    UIView *whiteView;
    UIImageView *backImg;
    
    UIView *firtBackView;

}
@property (nonatomic,strong) NSArray *arrays;

@end

@implementation Loginbonus
//#define speace 8*_window_width/375
#define itemWidth 58
#define itemHeight 75
#define speace ((_window_width*0.96*0.7)-itemWidth*4)/4

/******************  登录奖励 ->  ********************/

-(instancetype)initWithFrame:(CGRect)frame AndNSArray:(NSArray *)arrays AndDay:(NSString *)day andDayCount:(NSString *)dayCount andIsBonus:(NSString *)isbonus{
    
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];

        _arrays = arrays;
        logDay = day;
        logDayCount = dayCount;
        logis_bonus = isbonus;
        firtBackView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        firtBackView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
        [self addSubview:firtBackView];
        [self firstLog:frame];
    }
    return self;
    
}

-(void)firstLog:(CGRect)frame {
    CGFloat height = _window_width *0.9*26/66.00+80+(_window_width *0.9*0.88 - 30)/4*(140/116.00)*2.5;

    backImg = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width*0.02, -_window_height, _window_width*0.96, height)];
    backImg.userInteractionEnabled = YES;
    backImg.image = [UIImage imageNamed:getImagename(@"loginbonus_bg")];
    [self addSubview:backImg];
    
    UIButton *btn = [UIButton buttonWithType:0];
    btn.frame = CGRectMake(backImg.width*0.8, 0, 30, 30);
    [btn setImage:[UIImage imageNamed:@"loginclose"] forState:0];
    [btn addTarget:self action:@selector(cancelLQ) forControlEvents:UIControlEventTouchUpInside];
    [backImg addSubview:btn];

    CGFloat fcW = backImg.width;
    CGFloat fcH = backImg.height;

    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.minimumLineSpacing = 5;
    layout.minimumInteritemSpacing =5;

    _firstCollection = [[UICollectionView alloc]initWithFrame:CGRectMake(fcW *0.15 , backImg.height*0.4, fcW*0.7, fcH *0.4) collectionViewLayout:layout];
    _firstCollection.dataSource = self;
    _firstCollection.delegate = self;

    UINib *nib = [UINib nibWithNibName:@"LogFirstCell" bundle:nil];
    [_firstCollection registerNib:nib forCellWithReuseIdentifier:IDENTIFIER];
    UINib *nib2 = [UINib nibWithNibName:@"LogFirstCell2" bundle:nil];
    [_firstCollection registerNib:nib2 forCellWithReuseIdentifier:IDENTIFIER2];
    _firstCollection.backgroundColor = [UIColor whiteColor];

    [backImg addSubview:_firstCollection];
    
    NSString *str = @"已连续签到999天";
    CGFloat strWidth = [PublicObj widthOfString:str andFont:[UIFont systemFontOfSize:12] andHeight:24];
//    UILabel *title = [[UILabel alloc]initWithFrame:CGRectMake(backImg.width/2-strWidth/2, _firstCollection.origin.y-35, strWidth, 24)];
    UILabel *title = [[UILabel alloc]initWithFrame:CGRectMake(backImg.width/2-strWidth/2, _firstCollection.top-35, strWidth, 24)];

    title.layer.cornerRadius = 13;
    title.layer.masksToBounds = YES;
    title.backgroundColor = [UIColor whiteColor];
    title.font = [UIFont systemFontOfSize:11];
    title.textColor = [UIColor blackColor];
    title.textAlignment = NSTextAlignmentCenter;
    title.backgroundColor = RGBA(255,46,140,0.1);
    [backImg addSubview:title];

    NSMutableAttributedString *textAtt = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@%@%@",YZMsg(@"已连续签到"),logDayCount,YZMsg(@"天")]];
    [textAtt addAttribute:NSForegroundColorAttributeName value:RGB_COLOR(@"#323232", 1) range:textAtt.yy_rangeOfAll];

    NSRange clickRange = [[textAtt string] rangeOfString:logDayCount];
    [textAtt yy_setTextHighlightRange:clickRange color:RGB_COLOR(@"#FF2E8C", 1) backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
    }];
    title.attributedText = textAtt;
    
    UILabel *tipslb = [[UILabel alloc]init];
//    tipslb.frame = CGRectMake(0, title.origin.y-28, backImg.width, 20);
    tipslb.frame = CGRectMake(0, title.top-28, backImg.width, 20);

    tipslb.textAlignment = NSTextAlignmentCenter;
    tipslb.text = YZMsg(@"连续签到领取额外奖励");
    tipslb.font = [UIFont boldSystemFontOfSize:16];
    tipslb.textColor = RGB(255,88,120);
    [backImg addSubview:tipslb];
    
    UIButton *receiveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    CGFloat btnW = backImg.width*0.6;
    CGFloat btnH = 40;
    CGFloat btnX = backImg.width*0.2;
    CGFloat btnY = 10;
    receiveBtn.frame = CGRectMake(btnX, _firstCollection.bottom+5, btnW, btnH);
    receiveBtn.backgroundColor =RGBA(198,63,243,1);// Normal_Color;
    [receiveBtn addTarget:self action:@selector(clickReceiveBtn) forControlEvents:UIControlEventTouchUpInside];
    [receiveBtn setTitle:YZMsg(@"立即签到") forState:UIControlStateNormal];
    receiveBtn.titleLabel.textColor = [UIColor whiteColor];
    receiveBtn.layer.cornerRadius = 20;
    receiveBtn.layer.masksToBounds = YES;
//    [receiveBtn setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];
    receiveBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [backImg addSubview:receiveBtn];
    if ([logis_bonus isEqual:@"1"]) {
        receiveBtn.backgroundColor =RGBA(191,191,191,1);// Normal_Color;
        [receiveBtn setTitle:YZMsg(@"已签到") forState:UIControlStateNormal];
        receiveBtn.userInteractionEnabled = NO;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.8 animations:^{
                backImg.frame = CGRectMake(_window_width*0.02, _window_height*0.2, _window_width*0.96, height);
            }];
        });
    });

    
}
- (void)showLogSucessAnimation{
    [backImg removeFromSuperview];
    backImg = nil;
    UIImageView *lightImageView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width*0.25, _window_height/2-_window_width*0.125-50, _window_width*0.5, _window_width*0.5)];
    lightImageView.image = [UIImage imageNamed:@"logFirst_背景"];
    [self addSubview:lightImageView];
    
    CABasicAnimation* rotationAnimation;
    rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.toValue = [NSNumber numberWithFloat: M_PI * 2.0 ];
    rotationAnimation.duration = 2;
    rotationAnimation.cumulative = YES;
    rotationAnimation.repeatCount = 9999;
    [lightImageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];
    
    //放大效果，并回到原位
    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    //速度控制函数，控制动画运行的节奏
    animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    animation.duration = 0.5;       //执行时间
    animation.repeatCount = 1;      //执行次数
    animation.autoreverses = NO;    //完成动画后会回到执行动画之前的状态
    animation.fromValue = [NSNumber numberWithFloat:0.2];   //初始伸缩倍数
    animation.toValue = [NSNumber numberWithFloat:1.2];     //结束伸缩倍数
    
    
    
    
    UIImageView *headerImgView = [[UIImageView alloc]initWithFrame:CGRectMake(lightImageView.left, lightImageView.top-lightImageView.width/3, lightImageView.width, lightImageView.width/3)];
    headerImgView.image = [UIImage imageNamed:getImagename(@"logFirst_成功")];
    [self addSubview:headerImgView];
    
    
    UIImageView *coinImageView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width*0.375, lightImageView.width*0.25+lightImageView.top, _window_width*0.25, _window_width*0.25)];
    coinImageView.image = [UIImage imageNamed:@"logFirst_钻石"];
    [self addSubview:coinImageView];
    
    [headerImgView.layer addAnimation:animation forKey:nil];
    [coinImageView.layer addAnimation:animation forKey:nil];
    
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, coinImageView.bottom+5, _window_width, 22)];
    label.textColor = [UIColor whiteColor];
    label.font = [UIFont boldSystemFontOfSize:20];
    label.textAlignment = NSTextAlignmentCenter;
    NSDictionary *subdic = _arrays[[logDay intValue]-1];
    label.text = [NSString stringWithFormat:@"+ %@",minstr([subdic valueForKey:@"coin"])];
    [self addSubview:label];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [label.layer addAnimation:animation forKey:nil];
        
    });
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:0.5 animations:^{
            self.transform = CGAffineTransformMakeScale(0.01, 0.01);
            [UIView animateWithDuration:0.8 animations:^{
                firtBackView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
                [firtBackView removeFromSuperview];
                firtBackView = nil;
            }];


        } completion:^(BOOL finished) {
            if ([_delegate respondsToSelector:@selector(removeView:)]) {
                [_delegate removeView:nil];
            }
        }];
        
    });

}
-(void)clickReceiveBtn {
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.getBonus" Dic:nil Suc:^(int code, id info, NSString *msg) {
            if (code == 0) {
                [weakSelf showLogSucessAnimation];
            }else{
                [MBProgressHUD showError:msg];
            }
        } Fail:^(id fail) {
            [MBProgressHUD showError:YZMsg(@"网络错误")];
        }];
}
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return _arrays.count;
}

-(UICollectionViewCell*)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    UICollectionViewCell *cell;
    
    if (indexPath.row  == 6) {
        LogFirstCell2 *cell2 = [collectionView dequeueReusableCellWithReuseIdentifier:IDENTIFIER2 forIndexPath:indexPath];
        
        cell = cell2;
        
    } else {
        
        LogFirstCell *cell1 = [collectionView dequeueReusableCellWithReuseIdentifier:IDENTIFIER forIndexPath:indexPath];
        
        cell1.layer.cornerRadius = 3;
        cell1.layer.masksToBounds = YES;
        NSDictionary *subdic = _arrays[indexPath.row];
//        cell1.numL.text = [NSString stringWithFormat:@"%@%@%@",YZMsg(@"第"),minstr([subdic valueForKey:@"day"]),YZMsg(@"天")];
        cell1.numL.text = [NSString stringWithFormat:YZMsg(@"第%@天"),minstr([subdic valueForKey:@"day"])];
        if (indexPath.item <= [logDay integerValue]-1) {
            cell1.bgIV2.backgroundColor = RGBA(255, 247, 250, 0.8);
            cell1.numL.alpha = 0.6;
        }else{
            cell1.contentView.backgroundColor = RGBA(255, 46, 140, 0.1);
        }
        //判断第几天
        if (indexPath.item == [logDay integerValue]-1) {
            //动画
            selectCell = cell1;
        }
        cell = cell1;
    }
    return cell;
}

- (void)keepRatate {
    if ([logDay integerValue] == 7) {
        selectCell2.bgIV2.transform = CGAffineTransformRotate(selectCell2.bgIV2.transform, M_PI_4 * 0.02);
    }else {
        selectCell.bgIV2.transform = CGAffineTransformRotate(selectCell.bgIV2.transform, M_PI_4 * 0.02);
    }
}

-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    if (indexPath.row == 6) {
        return CGSizeMake(itemWidth*2, itemHeight);
    }else {
        return CGSizeMake(itemWidth, itemHeight);

    }
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    NSLog(@"%zi",indexPath.item);
}
- (void)cancelLQ{
    [self.delegate removeView:nil];
}
/******************  <- 登录奖励  ********************/




@end
