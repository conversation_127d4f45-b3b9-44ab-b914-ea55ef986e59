//
//  AdvertManagerVC.m
//  YBVideo
//
//  Created by ybRRR on 2021/12/17.
//  Copyright © 2021 cat. All rights reserved.
//

#import "AdvertManagerVC.h"
#import <MobileCoreServices/MobileCoreServices.h>
#import "YBStorageObj.h"
#import "MyAdvertVC.h"
@interface AdvertManagerVC ()<TZImagePickerControllerDelegate,UINavigationControllerDelegate,UIImagePickerControllerDelegate,UITextViewDelegate>
{
    UITextField *_titleField;
    UITextField *_phoneField;
    MyTextView *_authText;
    UILabel *_authLb;
    UILabel *_authNumLb;
    
    UIButton *subBtn;
    NSDictionary *infosDic;
    NSArray *placeHolderArr;
    
    NSString *picFirstStr;
    NSString *picSecondStr;
    UIButton *deleteBtn;
    
    UILabel *zzLb;
    
    UIView *backWallView;
    NSMutableArray *backWallArr;
    NSMutableArray *upWallImgArr;//上传用标题数组

}
@end

@implementation AdvertManagerVC

-(void)getAdvertiserInfo{
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.getAdvertiserInfo" Dic:@{@"uid":[Config getOwnID],@"token":[Config getOwnToken]} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            infosDic = [info firstObject];
        }
        [weakSelf createBaseUI];
        if ([minstr([infosDic valueForKey:@"has_auth"]) isEqual:@"1"]) {
            NSDictionary *auth_info = [infosDic valueForKey:@"auth_info"];
            picFirstStr= minstr([auth_info valueForKey:@"img1_format"]);
            picSecondStr= minstr([auth_info valueForKey:@"img2_format"]);
            
            if (picFirstStr.length > 0) {
                [backWallArr addObject:picFirstStr];
            }
            if (picSecondStr.length > 0) {
                [backWallArr addObject:picSecondStr];
            }

            if ([minstr([auth_info valueForKey:@"status"]) isEqual:@"1"]) {
                self.rightBtn.hidden = NO;
                [self.rightBtn setTitle:YZMsg(@"我的广告") forState:0];
                [self.rightBtn setTitleColor:UIColor.whiteColor forState:0];
            }else if ([minstr([auth_info valueForKey:@"status"]) isEqual:@"-1"]){
                [backWallArr addObject:@""];
            }
            [self setAuthInfo:auth_info];
            [weakSelf addZZView];


        }else{
            [backWallArr addObject:@""];
            [weakSelf addZZView];
        }

        } Fail:^(id fail) {
            
        }];
}
-(void)setAuthInfo:(NSDictionary *)authDic{
    //0 认证等待审核中   1  已通过  -1  已拒绝
    NSString *status = minstr([authDic valueForKey:@"status"]);
    if ([status isEqual:@"0"] ||[status isEqual:@"1"]) {
        _titleField.userInteractionEnabled = NO;
        _phoneField.userInteractionEnabled = NO;
        _authText.userInteractionEnabled = NO;

        _authText.hidden = YES;
        _authNumLb.hidden = YES;
        _authLb.hidden = NO;
        zzLb.hidden = YES;
        _authLb.text =minstr([authDic valueForKey:@"auth_desc"]);
        [_authText mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view).offset(27);
            make.right.equalTo(self.view.mas_right).offset(-27);
            make.top.equalTo(_authLb.mas_bottom).offset(10);
            make.height.mas_equalTo(1);
        }];

    }

    _titleField.text = minstr([authDic valueForKey:@"username"]);
    _phoneField.text = minstr([authDic valueForKey:@"mobile"]);
    _authText.text =minstr([authDic valueForKey:@"auth_desc"]);
    _authNumLb.text = [NSString stringWithFormat:@"%lu/50",_authText.text.length];
}
-(void)subBtnClick{
    
    //判断背景墙图片有木有
    BOOL iswallImg = NO;
    for (int i = 0; i < backWallArr.count; i ++) {
        if ([backWallArr[i] isKindOfClass:[UIImage class]]) {
            iswallImg = YES;
            break;
        }else{
            NSString *imgUrl =backWallArr[i];
            if (imgUrl.length > 1) {
                iswallImg = YES;
                break;
            }
        }
    }
    if (_titleField.text.length < 1) {
        [MBProgressHUD showError:placeHolderArr[0]];
        return;
    }else if (_phoneField.text.length < 1){
        [MBProgressHUD showError:placeHolderArr[1]];
        return;
    }else if(_authText.text.length < 1){
        [MBProgressHUD showError:placeHolderArr[2]];
        return;
    }else if (!iswallImg){
        [MBProgressHUD showError:YZMsg(@"请上传资质图片")];
        return;
    }
    [MBProgressHUD showMessage:@""];
    YBWeakSelf;
    [[YBStorageObj storageManeger]getCOSType:^(int code) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (code == 0) {
                [weakSelf uploadAllImage];
            }
        });
    }];

}
-(void)uploadAllImage{
    YBWeakSelf;
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);

    upWallImgArr = [NSMutableArray array];

    for (int i = 0; i < backWallArr.count; i ++) {
        if ([backWallArr[i] isKindOfClass:[UIImage class]]) {
            UIImage *image =backWallArr[i];
            NSData *imageData = UIImagePNGRepresentation(image);
            if (!imageData) {
                [MBProgressHUD hideHUD];
                [MBProgressHUD showError:YZMsg(@"图片错误")];
                return;
            }

            dispatch_group_async(group, queue, ^{
                NSString *countImgStr = [NSString stringWithFormat:@"_backImg%d.png",i];
                NSString *imageName = [PublicObj getNameBaseCurrentTime:countImgStr];
                [[YBStorageObj storageManeger]yb_storageImg:image andName:imageName progress:^(CGFloat percent) {

                }complete:^(int code, NSString *key) {
                    [upWallImgArr addObject:minstr(key)];
                    dispatch_semaphore_signal(semaphore);
                }];
                dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
            });

        }else{
            NSString *imgthumb =backWallArr[i];
            if (imgthumb.length > 1) {
                NSArray *imgArr = [imgthumb componentsSeparatedByString:@"/"];
                imgthumb = [imgArr lastObject];
                [upWallImgArr addObject:imgthumb];
            }
        }

    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self setAdvertiser];
        });
        NSLog(@"任务完成执行");
    });

}

-(void)setAdvertiser{
    NSString *img1Str = @"";
    NSString *img2Str = @"";
    if (upWallImgArr.count > 1) {
        img1Str = upWallImgArr[0];
        img2Str = upWallImgArr[1];
    }else{
        img1Str = upWallImgArr[0];

    }
    NSDictionary *parDic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"username":_titleField.text,@"mobile":_phoneField.text,@"auth_desc":_authText.text,@"img1":img1Str,@"img2":img2Str};
    [YBNetworking postWithUrl:@"User.setAdvertiser" Dic:parDic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [MBProgressHUD showError:msg];
            if (code == 0) {
                [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
            }

        });
        } Fail:^(id fail) {
            
        }];
}
-(void)clickNaviRightBtn
{
    MyAdvertVC *myad = [[MyAdvertVC alloc]init];
    [[XGGAppDelegate sharedAppDelegate]pushViewController:myad animated:YES];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"广告管理");
    picFirstStr = @"";
    picSecondStr = @"";
    backWallArr = [NSMutableArray array];
    [self getAdvertiserInfo];
}

-(void)createBaseUI{
    
    placeHolderArr = @[YZMsg(@"请输入认证主体名称"),YZMsg(@"请输入联系方式"),YZMsg(@"请输入认证说明...")];
    
    UILabel *titleLb = [[UILabel alloc]init];
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = UIColor.whiteColor;
    titleLb.text = YZMsg(@"认证主体名称");
    [titleLb setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.view addSubview:titleLb];
    [titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(27);
        make.top.mas_equalTo(64+statusbarHeight+30);
        make.height.mas_equalTo(20);
    }];
    
    _titleField = [[UITextField alloc]init];
    _titleField.textColor = UIColor.whiteColor;
    _titleField.font =[UIFont systemFontOfSize:14];
    NSMutableAttributedString*holderString = [[NSMutableAttributedString alloc] initWithString:YZMsg(@"请输入认证主体名称") attributes:@{NSForegroundColorAttributeName:RGB(150,150,150)}];
    _titleField.attributedPlaceholder = holderString;
    [self.view addSubview:_titleField];
    [_titleField setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [_titleField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLb.mas_right).offset(10);
        make.centerY.equalTo(titleLb.mas_centerY);
        make.right.equalTo(self.view.mas_right).offset(-15);
        make.height.mas_equalTo(30);

    }];
    
    UIView *line1 = [[UIView alloc]init];
    line1.backgroundColor = RGB_COLOR(@"#2d2a3f", 1);// RGB(245, 245, 245);
    [self.view addSubview:line1];
    [line1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLb.mas_left);
        make.right.equalTo(self.view.mas_right).offset(-27);
        make.top.equalTo(titleLb.mas_bottom).offset(15);
        make.height.mas_equalTo(1);
    }];
    
    
    UILabel *pTitleLb = [[UILabel alloc]init];
    pTitleLb.font = [UIFont systemFontOfSize:14];
    pTitleLb.textColor = UIColor.whiteColor;
    pTitleLb.text = YZMsg(@"联系方式");
    [pTitleLb setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.view addSubview:pTitleLb];
    [pTitleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(27);
        make.top.equalTo(line1.mas_bottom).offset(15);
        make.height.mas_equalTo(20);
    }];

    
    _phoneField= [[UITextField alloc]init];
    _phoneField.textColor = UIColor.whiteColor;
    _phoneField.font =[UIFont systemFontOfSize:14];
    NSMutableAttributedString*phoneString = [[NSMutableAttributedString alloc] initWithString:YZMsg(@"请输入联系方式") attributes:@{NSForegroundColorAttributeName:RGB(150,150,150)}];
    _phoneField.attributedPlaceholder = phoneString;
    [self.view addSubview:_phoneField];
    [_phoneField setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [_phoneField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLb.mas_right).offset(10);
        make.centerY.equalTo(pTitleLb.mas_centerY);
        make.right.equalTo(self.view.mas_right).offset(-15);
        make.height.mas_equalTo(30);

    }];
    UIView *line2 = [[UIView alloc]init];
    line2.backgroundColor = RGB_COLOR(@"#2d2a3f", 1);// RGB(245, 245, 245);
    [self.view addSubview:line2];
    [line2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(pTitleLb.mas_left);
        make.right.equalTo(self.view.mas_right).offset(-27);
        make.top.equalTo(pTitleLb.mas_bottom).offset(15);
        make.height.mas_equalTo(1);
    }];

    UILabel *infoLb = [[UILabel alloc]init];
    infoLb.font = [UIFont systemFontOfSize:14];
    infoLb.textColor = UIColor.whiteColor;
    infoLb.text = YZMsg(@"认证说明");
    [infoLb setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.view addSubview:infoLb];
    [infoLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(27);
        make.top.equalTo(line2.mas_bottom).offset(15);
        make.height.mas_equalTo(30);
    }];

    _authLb = [[UILabel alloc]init];
    _authLb.font = [UIFont systemFontOfSize:14];
    _authLb.textColor = UIColor.whiteColor;
    _authLb.hidden = YES;
    _authLb.numberOfLines = 0;
    _authLb.lineBreakMode = NSLineBreakByWordWrapping;
    [self.view addSubview:_authLb];
    [_authLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_phoneField);
        make.top.equalTo(infoLb);
        make.right.equalTo(self.view.mas_right).offset(-10);
    }];
    
    _authText = [[MyTextView alloc]init];
    _authText.placeholder = YZMsg(@"请输入认证说明...");
    _authText.placeholderColor = RGB(150,150,150);
    _authText.font = [UIFont systemFontOfSize:14];
    _authText.backgroundColor = RGB(21,17,44);
    _authText.textColor = UIColor.whiteColor;
    _authText.delegate = self;
    [self.view addSubview:_authText];
    [_authText mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(pTitleLb.mas_left);
        make.right.equalTo(self.view.mas_right).offset(-27);
        make.top.equalTo(infoLb.mas_bottom).offset(10);
        make.height.mas_equalTo(108);
    }];
    
    _authNumLb = [[UILabel alloc]init];
    _authNumLb.textColor =RGB(150,150,150);
    _authNumLb.font = [UIFont systemFontOfSize:13];
    _authNumLb.text = @"0/50";
    [self.view addSubview:_authNumLb];
    [_authNumLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_authText.mas_right).offset(-5);
        make.bottom.equalTo(_authText.mas_bottom).offset(-5);
    }];
    
    
    zzLb = [[UILabel alloc]init];
    zzLb.font = [UIFont systemFontOfSize:14];
    zzLb.textColor = UIColor.whiteColor;
    zzLb.text = YZMsg(@"上传资质图片");
    [zzLb setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.view addSubview:zzLb];
    [zzLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(27);
        make.top.equalTo(_authText.mas_bottom).offset(15);
        make.height.mas_equalTo(30);
    }];
    
    backWallView = [[UIView alloc]init];
    backWallView.backgroundColor = UIColor.clearColor;
    [self.view addSubview:backWallView];
    [backWallView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(27);
        make.top.equalTo(zzLb.mas_bottom).offset(10);
        make.right.equalTo(self.view.mas_right).offset(-27);
        make.height.mas_equalTo(160);
    }];
    
    subBtn = [UIButton buttonWithType:0];
    [subBtn setTitleColor:UIColor.whiteColor forState:0];
    subBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [subBtn setBackgroundColor:Pink_Cor];
    subBtn.layer.cornerRadius =5;
    subBtn.layer.masksToBounds = YES;
    [subBtn setTitle:YZMsg(@"开始认证") forState:0];
    if ([minstr([infosDic valueForKey:@"has_auth"]) isEqual:@"1"]) {
        //0 认证等待审核中   1  已通过  -1  已拒绝
        NSString *status = minstr([[infosDic valueForKey:@"auth_info"] valueForKey:@"status"]);
        if ([status isEqual:@"0"]) {
            [subBtn setTitle:YZMsg(@"审核中") forState:0];
            [subBtn setBackgroundColor:[UIColor grayColor]];
            subBtn.userInteractionEnabled = NO;
        }else if ([status isEqual:@"-1"]){
            [subBtn setTitle:YZMsg(@"审核失败，请重新上传") forState:0];
        }else if ([status isEqual:@"1"]){
            subBtn.hidden = YES;
        }

    }
    [self.view addSubview:subBtn];
    
    [subBtn addTarget:self action:@selector(subBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [subBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(backWallView);
        make.right.equalTo(backWallView);
        make.height.mas_equalTo(44);
        make.centerX.equalTo(self.view);
        make.top.equalTo(backWallView.mas_bottom).offset(30);
    }];
}
-(void)picBtnClick:(UIButton *)sender{
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相机") complete:^{
        [weakSelf clickTake];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相册") complete:^{
        [weakSelf clickSel];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];

}
-(void)addZZView{
    [backWallView removeAllSubViews];
    for (int i = 0; i < backWallArr.count; i ++) {
        if (i >1) {
            return;
        }
        UIView *backView = [[UIView alloc]init];
        backView.backgroundColor =UIColor.clearColor;
        [backWallView addSubview:backView];
        if (i == 0) {
            [backView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.top.height.equalTo(backWallView);
                make.width.equalTo(backWallView.mas_width).multipliedBy(0.49);
            }];
        }else{
            [backView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.right.height.equalTo(backWallView);
                make.width.equalTo(backWallView.mas_width).multipliedBy(0.49);
            }];

        }
        
        UIView *ImgBack = [[UIView alloc]init];
        ImgBack.backgroundColor =RGB(21,17,44);
        [backView addSubview:ImgBack];
        [ImgBack mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.right.equalTo(backView);
            make.height.equalTo(backView.mas_height).multipliedBy(0.8);
        }];
        
        UIImageView *addImg = [[UIImageView alloc]init];
        addImg.image = [UIImage imageNamed:@"zizhiadd"];
        [ImgBack addSubview:addImg];
        [addImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(ImgBack);
            make.width.height.mas_equalTo(24);
        }];

        UILabel *tLb = [[UILabel alloc]init];
        tLb.font = [UIFont systemFontOfSize:14];
        tLb.textColor = RGB(150,150,150);
        tLb.text = YZMsg(@"资质图片");
        [backView addSubview:tLb];
        [tLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(backView.mas_centerX);
            make.top.equalTo(ImgBack.mas_bottom).offset(10);
        }];
        
        UIButton *picClickBtn= [UIButton buttonWithType:0];
        [picClickBtn addTarget:self action:@selector(picBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        picClickBtn.imageView.contentMode = UIViewContentModeScaleAspectFill;
        [backView addSubview:picClickBtn];
        [picClickBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.right.bottom.equalTo(ImgBack);
        }];
        BOOL haveImgBool = NO;
        if ([backWallArr[i] isKindOfClass:[UIImage class]]) {
            [picClickBtn setImage:backWallArr[i] forState:0];
            haveImgBool = YES;
            picClickBtn.userInteractionEnabled = NO;

        }else{
            NSString *imgUrl =backWallArr[i];
            if (imgUrl.length > 1) {
                [picClickBtn sd_setImageWithURL:[NSURL URLWithString:imgUrl] forState:0];
                haveImgBool = YES;
                picClickBtn.userInteractionEnabled = NO;
            }
        }
        UIButton *deleteBtn = [UIButton buttonWithType:0];
        [deleteBtn setBackgroundColor:RGB_COLOR(@"#FC3D3E", 1)];
        [deleteBtn setTitle:YZMsg(@"-") forState:0];
        [deleteBtn setTitleColor:UIColor.whiteColor forState:0];
        deleteBtn.titleLabel.font = [UIFont boldSystemFontOfSize:15];
        deleteBtn.layer.cornerRadius = 10;
        deleteBtn.layer.masksToBounds = YES;
        [deleteBtn addTarget:self action:@selector(deleteimgBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        deleteBtn.tag = 20000+i;
        [backView addSubview:deleteBtn];
        [deleteBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(ImgBack).offset(5);
            make.right.equalTo(ImgBack).offset(-5);
            make.width.height.mas_equalTo(20);
        }];
        if (haveImgBool) {
            deleteBtn.hidden = NO;
        }else{
            deleteBtn.hidden = YES;
        }

        if ([minstr([infosDic valueForKey:@"has_auth"]) isEqual:@"1"]) {
            NSDictionary *auth_info = [infosDic valueForKey:@"auth_info"];
            if (![minstr([auth_info valueForKey:@"status"]) isEqual:@"-1"]){
                deleteBtn.hidden = YES;
            }
        }
        
    }
}
-(void)deleteimgBtnClick:(UIButton *)sender{
    NSInteger tags =sender.tag-20000;
    NSMutableArray *arr =backWallArr;
    [arr removeObjectAtIndex:tags];
    backWallArr = arr;
    [self addZZView];
}

#pragma mark -   UIImagePickerControllerDelegate
//拍照
-(void)clickTake {
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.allowsEditing = YES;
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
    imagePickerController.allowsEditing = YES;
    imagePickerController.showsCameraControls = YES;
    imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    //imagePickerController.mediaTypes = @[(NSString *)kUTTypeImage];
    imagePickerController.modalPresentationStyle = 0;
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
//选照
-(void)clickSel {
    /*
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.allowsEditing = YES;
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    imagePickerController.allowsEditing = YES;
    imagePickerController.mediaTypes = @[(NSString *)kUTTypeImage];
    [UIApplication sharedApplication].statusBarHidden = YES;
    if (@available(iOS 11, *)) {
        UIScrollView.appearance.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentAutomatic;
    }
    imagePickerController.modalPresentationStyle = 0;
    [self presentViewController:imagePickerController animated:YES completion:nil];
    */
    TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.modalPresentationStyle = 0;
    imagePC.showSelectBtn = NO;
    imagePC.allowPickingOriginalPhoto = NO;
    imagePC.oKButtonTitleColorNormal = Pink_Cor;
    imagePC.allowTakePicture = NO;
    imagePC.allowTakeVideo = NO;
    imagePC.allowPickingVideo = NO;
    imagePC.allowPickingMultipleVideo = NO;
    imagePC.modalPresentationStyle = 0;
    imagePC.allowCrop = YES;
    imagePC.cropRect = CGRectMake(0, (_window_height-_window_width)/2, _window_width, _window_width);
    [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}

- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    UIImage* image = photos[0];
    [backWallArr insertObject:image atIndex:backWallArr.count-1];
    [self addZZView];
}

-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    if (@available(iOS 11, *)) {
        UIScrollView.appearance.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"]) {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        [backWallArr insertObject:image atIndex:backWallArr.count-1];
        [self addZZView];        
        [picker dismissViewControllerAnimated:YES completion:^{
            [UIApplication sharedApplication].statusBarHidden=NO;
        }];
        
    }
}
-(void)textViewDidChange:(UITextView *)textView
{
    NSString *toBeString = textView.text;
    NSString *lang = [[[UITextInputMode activeInputModes]firstObject] primaryLanguage]; // 键盘输入模式
    if ([lang isEqualToString:@"zh-Hans"]) { // 简体中文输入，包括简体拼音，健体五笔，简体手写
        UITextRange *selectedRange = [textView markedTextRange];//获取高亮部分
        UITextPosition *position = [textView positionFromPosition:selectedRange.start offset:0];
        //没有高亮选择的字，则对已输入的文字进行字数统计和限制
        if (!position) {
            //签名tf
            if (textView == _authText) {
                if (toBeString.length > 50) {
                    textView.text = [toBeString substringToIndex:50];
                    _authNumLb.text = [NSString stringWithFormat:@"%lu/50",textView.text.length];
                }else{
                    //signature_num.text = [NSString stringWithFormat:@"%lu/20",(20-toBeString.length)];
                    _authNumLb.text = [NSString stringWithFormat:@"%lu/50",toBeString.length];
                }
            }
        }else{
            //有高亮选择的字符串，则暂不对文字进行统计和限制
        }
    }else{
        // 中文输入法以外的直接对其统计限制即可，不考虑其他语种情况
        if (textView == _authText) {
            if (toBeString.length > 50) {
                textView.text = [toBeString substringToIndex:50];
                _authNumLb.text = [NSString stringWithFormat:@"%lu/50",textView.text.length];
            }else{
                _authNumLb.text = [NSString stringWithFormat:@"%lu/50",toBeString.length];
            }
        }
    }

}
@end
