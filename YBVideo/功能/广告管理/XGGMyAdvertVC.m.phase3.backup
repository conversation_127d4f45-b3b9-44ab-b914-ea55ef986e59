//
//  MyAdvertVC.m
//  YBVideo
//
//  Created by ybRRR on 2021/12/22.
//  Copyright © 2021 cat. All rights reserved.
//

#import "MyAdvertVC.h"
#import "MyAdvertCell.h"
#import "YBGetVideoObj.h"
@interface MyAdvertVC ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>
{
    int _page;
}
@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,strong)NSMutableArray *dataArr;
@end

@implementation MyAdvertVC

-(void)getUserAdVideos{
    [YBNetworking postWithUrl:@"Video.getUserAdVideos" Dic:@{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"p":@(_page)} Suc:^(int code, id info, NSString *msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
            if (code == 0) {
                NSArray *infos = info;
                if (_page == 1) {
                    [_dataArr removeAllObjects];
                }
                [_dataArr addObjectsFromArray:infos];
                
                if (_dataArr.count > 0) {
                    [PublicView hiddenTextNoData:_collectionView];

                }else{
                    [PublicView showTextNoData:_collectionView text1:YZMsg(@"暂无数据") text2:@"" centerY:0.8];
                }

                [_collectionView reloadData];
            }
        } Fail:^(id fail) {
            
        }];
}
-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:YES];
    [self getUserAdVideos];

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"我的广告");
    _page = 1;
    _dataArr = [NSMutableArray array];
    
    
    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    flow.itemSize = CGSizeMake(_window_width/3-2, (_window_width/3-2) * 1.4);
    flow.minimumLineSpacing = 2;
    flow.minimumInteritemSpacing = 2;
    self.collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-ShowDiff) collectionViewLayout:flow];
    [self.collectionView registerNib:[UINib nibWithNibName:@"MyAdvertCell" bundle:nil] forCellWithReuseIdentifier:@"MyAdvertCell"];
    self.collectionView.delegate =self;
    self.collectionView.dataSource = self;
    self.collectionView.mj_footer  = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        _page ++;
        [self getUserAdVideos];
    }];
    
    self.collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        _page = 1;
        [self getUserAdVideos];
        
    }];
    
    [self.view addSubview:self.collectionView];
    self.view.backgroundColor = Normal_Color;
    self.collectionView.backgroundColor = [UIColor clearColor];
}
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return _dataArr.count;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    
    NSString *basUrl = [NSString stringWithFormat:@"Video.getUserAdVideos&uid=%@&token=%@&p=%@",[Config getOwnID],[Config getOwnToken],@(_page)];

    NSDictionary *videoDic = _dataArr[indexPath.item];
    NSString *vidoeID = minstr([videoDic valueForKey:@"id"]);
    [YBGetVideoObj lookManeger].fromWhere = @"CenterVC";
    [YBGetVideoObj lookManeger].videoID = vidoeID;
    [YBGetVideoObj lookManeger].playIndex = (int)indexPath.item;
    [YBGetVideoObj lookManeger].videoList = [_dataArr mutableCopy];
    [YBGetVideoObj lookManeger].paging = _page;
    [YBGetVideoObj lookManeger].baseUrl = basUrl;
    [[YBGetVideoObj lookManeger]goLookVC];


}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    MyAdvertCell *cell = (MyAdvertCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"MyAdvertCell" forIndexPath:indexPath];
    cell.dataDic = _dataArr[indexPath.item];
    return cell;
}

@end
