//
//  UpHotCell.m
//  YBVideo
//
//  Created by ybRRR on 2021/3/1.
//  Copyright © 2021 cat. All rights reserved.
//

#import "UpHotCell.h"

@implementation UpHotCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.statuBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -5, 0, 5);
    
    _xiaoguoL.text = YZMsg(@"数据效果");
    _leibieL.text = YZMsg(@"数据类别");
    
    _gdy_tfjeL.text = YZMsg(@"投放金额");
    _gdy_tfsjL.text = YZMsg(@"投放时间");
    _gdy_playNumL.text = YZMsg(@"预计播放量");
    _gdy_RealTimeL.text = YZMsg(@"实际投放时间");
    _gdy_realPlayNUmL.text = YZMsg(@"实际播放量");
    _gdy_backNUmL.text = YZMsg(@"退回金额");
    [_aginBtn setTitle:YZMsg(@"再来一单") forState:0];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(UpHotCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
    UpHotCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UpHotCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"UpHotCell" owner:nil options:nil]objectAtIndex:0];
    }
    return cell;
}
-(void)setDataDic:(NSDictionary *)dataDic
{
    _dataDic  = dataDic;
    self.timeLb.text = minstr([dataDic valueForKey:@"addtime"]);
    [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"video_thumb"])]];
    self.moneyLb.text = minstr([dataDic valueForKey:@"money"]);
    self.timeLengthLb.text = minstr([dataDic valueForKey:@"length"]);
    self.wantNumLb.text = minstr([dataDic valueForKey:@"nums"]);
    self.realTimeLb.text = minstr([dataDic valueForKey:@"real_length"]);
    self.realPlayLb.text = minstr([dataDic valueForKey:@"real_play_num"]);
    self.returnCoinLb.text = minstr([dataDic valueForKey:@"return_coin"]);
}
-(void)setStatuStr:(NSString *)statuStr
{
    if ([statuStr isEqual:@"0"]) {
        [self.statuBtn setTitle:YZMsg(@"投放中") forState:0];
    }else{
        [self.statuBtn setTitle:YZMsg(@"已完成") forState:0];

    }

}
- (IBAction)aginBtnClick:(UIButton *)sender {
    if ([minstr([_dataDic valueForKey:@"videoid"]) isEqual:@"0"]) {
        [MBProgressHUD showError:YZMsg(@"视频已删除")];
        return;
    }
    if ([minstr([_dataDic valueForKey:@"refund_status"]) isEqual:@"0"]) {
        [MBProgressHUD showError:YZMsg(@"正在投放中\n不可继续投放")];
        return;
    }
    if ([self.delegate respondsToSelector:@selector(reUpOrderClick:)]) {
        [self.delegate reUpOrderClick:_dataDic];
    }
}
@end
