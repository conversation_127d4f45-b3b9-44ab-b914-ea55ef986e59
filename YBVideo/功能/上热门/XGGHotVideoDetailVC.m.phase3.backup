//
//  HotVideoDetailVC.m
//  YBVideo
//
//  Created by ybRRR on 2021/3/1.
//  Copyright © 2021 cat. All rights reserved.
//

#import "HotVideoDetailVC.h"
#import "UpHotCell.h"
#import <YCMenuView.h>
#import "addHotVideoVC.h"
@interface HotVideoDetailVC ()<UITableViewDelegate, UITableViewDataSource,upHotCellDelegate>
{
    UIButton *selectBtn;
    
    NSString *typeStr;
    int pageIndex;
}
@property (nonatomic, strong)UITableView *listTable;
@property(nonatomic,strong)UIView *tabHeaderView;
@property(nonatomic,strong)NSArray *selectArr;
@property(nonatomic, strong)NSMutableArray *dataArr;
@end

@implementation HotVideoDetailVC
- (void)viewWillAppear:(BOOL)animated{
    [UIApplication sharedApplication].statusBarStyle =  UIStatusBarStyleDefault;
}
- (void)viewWillDisappear:(BOOL)animated{
    [UIApplication sharedApplication].statusBarStyle =  UIStatusBarStyleLightContent;
}
-(void)getOrderList{
    NSDictionary *dic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"type":typeStr,@"p":@(pageIndex)};
    [YBNetworking postWithUrl:@"Popular.getOrderList" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [_listTable.mj_header endRefreshing];
        [_listTable.mj_footer endRefreshing];
        if (code == 0) {
            NSArray *listarr = info;
            if (pageIndex == 1) {
                [_dataArr removeAllObjects];
            }
            [_dataArr addObjectsFromArray:listarr];
            [_listTable reloadData];

            if (_dataArr.count > 0){
                [PublicView hiddenTextNoData:_listTable];
            } else{
                [PublicView showTextNoData:_listTable text1:@"" text2:YZMsg(@"暂无数据") centerY:0.8];
            }
        }
        } Fail:^(id fail) {
            [_listTable.mj_header endRefreshing];
            [_listTable.mj_footer endRefreshing];

        }];
}
-(void)creatNavi {
    UIView *navi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
    navi.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:navi];
    
    UIButton *left_btn = [UIButton buttonWithType:UIButtonTypeCustom];
    left_btn.frame = CGRectMake(10, 22+statusbarHeight, 40, 40);
    left_btn.contentEdgeInsets = UIEdgeInsetsMake(5, 5, 5, 5);
    [left_btn setImage:[UIImage imageNamed:@"pubBlack_back"] forState:0];
    [left_btn addTarget:self action:@selector(clickLeftBtn) forControlEvents:UIControlEventTouchUpInside];
    [navi addSubview:left_btn];

    UILabel *midLabel = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2-80, 22+statusbarHeight, 160, 40)];
    midLabel.textAlignment = NSTextAlignmentCenter;
    midLabel.textColor = RGB_COLOR(@"#323232", 1);
    midLabel.font = [UIFont boldSystemFontOfSize:17];
    midLabel.text = YZMsg(@"明细");
    [navi addSubview:midLabel];
    
}
- (void)clickLeftBtn{
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.naviView.hidden = YES;
    typeStr = @"0";
    pageIndex = 1;
    _dataArr = [NSMutableArray array];
    [self creatNavi];
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor =RGB_COLOR(@"#fafafa", 1);
    [self.view addSubview:self.listTable];
    [self getOrderList];
}
-(UITableView *)listTable {
    if (!_listTable) {
        //5个像素间隔
        _listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-ShowDiff) style:UITableViewStylePlain];
        _listTable.delegate = self;
        _listTable.dataSource = self;
        _listTable.separatorStyle = UITableViewCellStyleDefault;
        _listTable.backgroundColor = RGB_COLOR(@"#fafafa", 1);
        _listTable.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            pageIndex = 1;
            [self getOrderList];

        }];
        _listTable.mj_footer = [MJRefreshFooter footerWithRefreshingBlock:^{
            pageIndex +=1;
            [self getOrderList];
        }];
    }
    return _listTable;
}
-(UIView *)tabHeaderView {
    if (!_tabHeaderView) {
        _tabHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 50)];
        _tabHeaderView.backgroundColor = RGB_COLOR(@"#fafafa", 1);
        
        UILabel *title = [[UILabel alloc]initWithFrame:CGRectMake(15, 0, 100, 50)];
        title.font = SYS_Font(15);
        title.text = YZMsg(@"我的视频订单");
        title.textColor = [UIColor blackColor];
        [_tabHeaderView addSubview:title];
        
        selectBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        selectBtn.frame = CGRectMake(_window_width-90, 0, 75, 50);
        [selectBtn setTitle:YZMsg(@"未完成") forState:0];
        [selectBtn setImage:[UIImage imageNamed:@"hot_down"] forState:0];
        [selectBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, - selectBtn.imageView.image.size.width, 0, selectBtn.imageView.image.size.width)];
        [selectBtn setImageEdgeInsets:UIEdgeInsetsMake(0, selectBtn.titleLabel.bounds.size.width, 0, -selectBtn.titleLabel.bounds.size.width)];

        selectBtn.titleLabel.font = SYS_Font(15);
        [selectBtn setTitleColor:GrayText forState:0];
        selectBtn.contentHorizontalAlignment = NSTextAlignmentRight;
        [selectBtn addTarget:self action:@selector(selectBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [_tabHeaderView addSubview:selectBtn];
    }
    return _tabHeaderView;
}
-(UIView*)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return self.tabHeaderView;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 50;
}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _dataArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 220;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UpHotCell *cell = [UpHotCell cellWithTab:tableView andIndexPath:indexPath];
    cell.delegate = self;
    cell.dataDic = _dataArr[indexPath.row];
    cell.statuStr =typeStr;
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

-(void)selectBtnClick:(UIButton *)sender{
    // 创建YCMenuAction
    YCMenuAction *action = [YCMenuAction actionWithTitle:YZMsg(@"已完成") image:nil handler:^(YCMenuAction *action) {
        NSLog(@"点击了%@",action.title);
        [selectBtn setTitle:YZMsg(action.title) forState:0];
        typeStr = @"1";
        pageIndex = 1;
        [self getOrderList];

    }];
    YCMenuAction *action1 = [YCMenuAction actionWithTitle:YZMsg(@"未完成") image:nil handler:^(YCMenuAction *action) {
        NSLog(@"点击了%@",action.title);
        [selectBtn setTitle:YZMsg(action.title) forState:0];
        typeStr = @"0";
        pageIndex = 1;
        [self getOrderList];

    }];
    // 按钮（YCMenuAction）的集合
    self.selectArr = @[action,action1];

    // 创建YCMenuView(根据关联点或者关联视图)
    YCMenuView *view = [YCMenuView menuWithActions:self.selectArr width:120 relyonView:sender];
    
    // 自定义设置
    view.menuColor = [UIColor whiteColor];
    view.separatorColor = [UIColor clearColor];
    view.maxDisplayCount = 5;  // 最大展示数量（其他的需要滚动才能看到）
    view.offset = 0; // 关联点和弹出视图的偏移距离
    view.textColor = [UIColor grayColor];
    view.textFont = [UIFont boldSystemFontOfSize:14];
    view.menuCellHeight = 44;
    view.dismissOnselected = YES;
    view.dismissOnTouchOutside = YES;
    
    // 显示
    [view show];

}
-(void)reUpOrderClick:(NSDictionary *)dic
{
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Video.getVideo" Dic:@{@"videoid":minstr([dic valueForKey:@"videoid"]),@"mobileid":[PublicObj getDeviceUUID]} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            addHotVideoVC *hot = [[addHotVideoVC alloc]init];
            hot.videoInfo = infoDic;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:hot animated:YES];

        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];

    
}
@end
