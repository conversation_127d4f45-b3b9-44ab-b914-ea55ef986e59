//
//  UpHotCell.h
//  YBVideo
//
//  Created by ybRRR on 2021/3/1.
//  Copyright © 2021 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol upHotCellDelegate <NSObject>

-(void)reUpOrderClick:(NSDictionary *)dic;

@end

@interface UpHotCell : UITableViewCell
@property (weak, nonatomic) IBOutlet UILabel *timeLb;
@property (weak, nonatomic) IBOutlet UIImageView *thumbImg;
@property (weak, nonatomic) IBOutlet UILabel *moneyLb;
@property (weak, nonatomic) IBOutlet UILabel *timeLengthLb;
@property (weak, nonatomic) IBOutlet UILabel *wantNumLb;
@property (weak, nonatomic) IBOutlet UILabel *realTimeLb;
@property (weak, nonatomic) IBOutlet UILabel *realPlayLb;
@property (weak, nonatomic) IBOutlet UILabel *returnCoinLb;
@property (weak, nonatomic) IBOutlet UIButton *statuBtn;
@property (weak, nonatomic) IBOutlet UIButton *aginBtn;


@property (weak, nonatomic) IBOutlet UILabel *xiaoguoL;
@property (weak, nonatomic) IBOutlet UILabel *leibieL;



@property (weak, nonatomic) IBOutlet UILabel *gdy_tfjeL;
@property (weak, nonatomic) IBOutlet UILabel *gdy_tfsjL;
@property (weak, nonatomic) IBOutlet UILabel *gdy_playNumL;
@property (weak, nonatomic) IBOutlet UILabel *gdy_RealTimeL;
@property (weak, nonatomic) IBOutlet UILabel *gdy_realPlayNUmL;
@property (weak, nonatomic) IBOutlet UILabel *gdy_backNUmL;



@property (nonatomic, strong)NSDictionary *dataDic;
@property (nonatomic, strong)NSString *statuStr;

@property (nonatomic, assign)id<upHotCellDelegate>delegate;

+(UpHotCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end


