//
//  addHotVideoVC.m
//  YBVideo
//
//  Created by IOS1 on 2019/7/4.
//  Copyright © 2019 cat. All rights reserved.
//

#import "addHotVideoVC.h"

#import "AFNetworking.h"
#import "YBRechargeVC.h"
#import "HotVideoDetailVC.h"
@interface addHotVideoVC (){
    UIScrollView *backScrollView;
    NSMutableArray *moneyIteamArray;//金钱数组
    NSMutableArray *timeIteamArray;//时间数组
    NSMutableArray *payTypeArray;//支付方式数组
    UILabel *playNumsL;//预计提升播放量label
    UILabel *needMoneyL;//需要花费金钱
    UIButton *payButton;//支付按钮
    NSMutableArray *moneyArray;
    NSArray *payArray;
    NSArray *timeArray;
    
    UIView *enterMoneyView;
    UIView *moneyWhiteView;
    UITextField *moneyT;
    
    int baseCount;
    
    NSDictionary *selectPayDic;
    
    NSInteger selectMoney;
    int selectTime;
}
//支付宝
@property(nonatomic,copy)NSString *aliapp_key_ios;
@property(nonatomic,copy)NSString *aliapp_partner;
@property(nonatomic,copy)NSString *aliapp_seller_id;
//微信
@property(nonatomic,copy)NSString *wx_appid;

@end

@implementation addHotVideoVC
- (void)viewWillAppear:(BOOL)animated{
    [UIApplication sharedApplication].statusBarStyle =  UIStatusBarStyleDefault;
}
- (void)viewWillDisappear:(BOOL)animated{
    [UIApplication sharedApplication].statusBarStyle =  UIStatusBarStyleLightContent;
}
-(void)creatNavi {
    UIView *navi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
    navi.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:navi];
    
    UIButton *left_btn = [UIButton buttonWithType:UIButtonTypeCustom];
    left_btn.frame = CGRectMake(10, 22+statusbarHeight, 40, 40);
    left_btn.contentEdgeInsets = UIEdgeInsetsMake(5, 5, 5, 5);
    [left_btn setImage:[UIImage imageNamed:@"pubBlack_back"] forState:0];
    [left_btn addTarget:self action:@selector(clickLeftBtn) forControlEvents:UIControlEventTouchUpInside];
    [navi addSubview:left_btn];

    UILabel *midLabel = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2-80, 22+statusbarHeight, 160, 40)];
    midLabel.textAlignment = NSTextAlignmentCenter;
    midLabel.textColor = RGB_COLOR(@"#323232", 1);
    midLabel.font = [UIFont boldSystemFontOfSize:17];
    midLabel.text = YZMsg(@"上热门");
    [navi addSubview:midLabel];
    
    UIButton *right_btn = [UIButton buttonWithType:UIButtonTypeCustom];
    //right_btn.frame = CGRectMake(_window_width-50, 22+statusbarHeight, 40, 40);
    right_btn.contentEdgeInsets = UIEdgeInsetsMake(5, 5, 5, 5);
    [right_btn setTitle:YZMsg(@"明细") forState:0];
    [right_btn setTitleColor:[UIColor grayColor] forState:0];
    right_btn.titleLabel.font = [UIFont systemFontOfSize:14];
    [right_btn addTarget:self action:@selector(clickRightBtn) forControlEvents:UIControlEventTouchUpInside];
    [navi addSubview:right_btn];
    [right_btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(midLabel);
        make.right.equalTo(navi.mas_right).offset(-10);
    }];

}
- (void)clickLeftBtn{
    [self.navigationController popViewControllerAnimated:YES];
}
-(void)clickRightBtn{
    HotVideoDetailVC *detailVc = [[HotVideoDetailVC alloc]init];
    [[XGGAppDelegate sharedAppDelegate]pushViewController:detailVc animated:YES];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.naviView.hidden = YES;
    
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = [UIColor whiteColor];
    moneyIteamArray = [NSMutableArray array];
    timeIteamArray = [NSMutableArray array];
    payTypeArray = [NSMutableArray array];
    [self creatNavi];
    [self requestData];
}
- (void)requestData{
    
    [YBNetworking postWithUrl:@"Popular.getInfo" Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *dic = [info firstObject];
            baseCount = [minstr([dic valueForKey:@"base"]) intValue];
            //支付宝的信息
            _aliapp_key_ios = [dic valueForKey:@"aliapp_key"];
            _aliapp_partner = [dic valueForKey:@"aliapp_partner"];
            _aliapp_seller_id = [dic valueForKey:@"aliapp_seller_id"];
            //微信的信息
            _wx_appid = [dic valueForKey:@"wx_appid"];
            
            [self creatUI:dic];
        }else{
            [self clickLeftBtn];
        }
    } Fail:^(id fail) {
        [self clickLeftBtn];
    }];

}
- (void)creatUI:(NSDictionary *)dic{
    backScrollView = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-ShowDiff-50)];
    backScrollView.backgroundColor = RGB_COLOR(@"#fafafa", 1);
    [self.view addSubview:backScrollView];
//    NSArray *array = @[@"待推广视频",@"投放金额",@"投放时长",@"预计播放量提升",@"请选择支付方式",minstr([dic valueForKey:@"tips"])];
    NSArray *array = @[YZMsg(@"待推广视频"),YZMsg(@"投放金额"),YZMsg(@"投放时长"),YZMsg(@"预计播放量提升"),minstr([dic valueForKey:@"tips"])];
    MASViewAttribute *viewTop = backScrollView.mas_top;
    for (int i = 0; i < array.count; i ++) {
        UIView *view = [[UIView alloc]init];
        [backScrollView addSubview:view];
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(backScrollView);
            make.top.equalTo(viewTop);
            make.width.mas_equalTo(_window_width);
        }];
        UILabel *label = [[UILabel alloc]init];
        label.text = array[i];
        label.numberOfLines = 0;
        label.textColor = RGB_COLOR(@"#969696", 1);
        label.font = [UIFont systemFontOfSize:12];
        [view addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(view).offset(20);
            make.right.equalTo(view).offset(-20);
            make.top.equalTo(view).offset(14);
            make.bottom.equalTo(view).offset(-14);
        }];
        UIView *whiteView = [[UIView alloc]init];
        whiteView.backgroundColor = [UIColor whiteColor];
        [backScrollView addSubview:whiteView];
        moneyArray = [[dic valueForKey:@"moneylist"] mutableCopy];
        [moneyArray addObject:YZMsg(@"自定义")];
        timeArray = @[@"6",@"12",@"24"];
        payArray = [dic valueForKey:@"paylist"];

        selectMoney = [[moneyArray firstObject] integerValue];
        selectTime = 6;
        CGFloat whiteHeight = 0.0;
        if (i == 0) {
            whiteHeight = 100;
        }else if (i == 1){
            NSInteger count = moneyArray.count % 3 == 0 ? moneyArray.count / 3 : moneyArray.count / 3 + 1;
            whiteHeight = count * 55;
        }else if (i == 2 || i == 3){
            whiteHeight = 60;
        }else if (i == 4){
            whiteHeight = payArray.count * 50;
        }
        [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(view);
            make.top.equalTo(view.mas_bottom);
            make.height.mas_equalTo(whiteHeight);
        }];
        //视频信息
        if (i == 0) {
            UIImageView *thumbImgV = [[UIImageView alloc]init];
            thumbImgV.backgroundColor = RGB_COLOR(@"#FB483A", 1);
            thumbImgV.layer.cornerRadius = 5;
            thumbImgV.layer.masksToBounds = YES;
            thumbImgV.contentMode = UIViewContentModeScaleAspectFill;
            [thumbImgV sd_setImageWithURL:[NSURL URLWithString:minstr([_videoInfo valueForKey:@"thumb"])]];
            [whiteView addSubview:thumbImgV];
            [thumbImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(whiteView);
                make.left.equalTo(whiteView).offset(20);
                make.width.height.mas_equalTo(75);
            }];
            
            UILabel *titleL = [[UILabel alloc]init];
            titleL.font = [UIFont systemFontOfSize:14];
            if (minstr([_videoInfo valueForKey:@"title"]).length > 0) {
                titleL.textColor = RGB_COLOR(@"#323232", 1);
                titleL.text = minstr([_videoInfo valueForKey:@"title"]);
            }else{
                titleL.text = YZMsg(@"当前视频未填写视频标题");
                titleL.textColor = RGB_COLOR(@"#969696", 1);
            }
            titleL.numberOfLines = 2;
            [whiteView addSubview:titleL];
            [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(thumbImgV.mas_right).offset(12);
                make.top.equalTo(thumbImgV);
                make.right.equalTo(whiteView).offset(-20);
                make.height.equalTo(thumbImgV).multipliedBy(0.5);
            }];
            
            UILabel *nameL = [[UILabel alloc]init];
            nameL.font = [UIFont systemFontOfSize:12];
            nameL.textColor = RGB_COLOR(@"#969696", 1);
            NSDictionary *userInfoDic = [_videoInfo valueForKey:@"userinfo"];
            nameL.text = minstr([userInfoDic valueForKey:@"user_nickname"]);
            [whiteView addSubview:nameL];
            [nameL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(thumbImgV.mas_right).offset(12);
                make.top.equalTo(thumbImgV.mas_centerY);
                make.right.equalTo(whiteView).offset(-20);
            }];

            UILabel *timeL = [[UILabel alloc]init];
            timeL.font = [UIFont systemFontOfSize:10];
            timeL.textColor = RGB_COLOR(@"#969696", 1);
            timeL.text = minstr([_videoInfo valueForKey:@"addtime"]);
            [whiteView addSubview:timeL];
            [timeL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(thumbImgV.mas_right).offset(12);
                make.bottom.equalTo(thumbImgV);
                make.right.equalTo(whiteView).offset(-20);
            }];

        }
        if (i == 1 || i == 2) {
            NSArray *arr = i == 1 ? moneyArray : timeArray;
            CGFloat btnWidth = (_window_width - 76)/3;
            for (int j = 0; j < arr.count; j ++) {
                UIButton *btn = [UIButton buttonWithType:0];
                btn.layer.cornerRadius = 3;
                btn.layer.masksToBounds = YES;
                btn.layer.borderWidth = 1;
                btn.tag = i *1000 + j;
                [btn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
                [btn setTitleColor:RGB_COLOR(@"#FB483A", 1) forState:UIControlStateSelected];
                btn.titleLabel.font = [UIFont systemFontOfSize:13];
                [btn addTarget:self action:@selector(selectButtonClick:) forControlEvents:UIControlEventTouchUpInside];
                if (j == 0) {
                    btn.selected = YES;
                    btn.layer.borderColor = RGB_COLOR(@"#FB483A", 1).CGColor;
                }else{
                    btn.layer.borderColor = RGB_COLOR(@"#dcdcdc", 1).CGColor;
                }
                [whiteView addSubview:btn];
                [btn mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(whiteView).offset(20+j%3*(btnWidth + 18));
                    make.height.mas_equalTo(33);
                    make.top.equalTo(whiteView).offset(15+(j/3)*48);
                    make.width.mas_equalTo(btnWidth);
                }];
                if (i == 1) {
                    if (j == arr.count - 1) {
                        [btn setTitle:arr[j] forState:0];
                    }else{
                        [btn setTitle:[NSString stringWithFormat:@"%@",arr[j]] forState:0];
                        [btn setImage:[UIImage imageNamed:@"礼物-金币"] forState:0];
                        [btn setTitleEdgeInsets:UIEdgeInsetsMake(0, - btn.imageView.image.size.width-2, 0, btn.imageView.image.size.width)];
                        [btn setImageEdgeInsets:UIEdgeInsetsMake(0, btn.titleLabel.bounds.size.width+2, 0, -btn.titleLabel.bounds.size.width)];
                    }
                    [moneyIteamArray addObject:btn];
                }else{
                    [btn setTitle:[NSString stringWithFormat:@"%@%@",arr[j],YZMsg(@"小时")] forState:0];
                    [timeIteamArray addObject:btn];
                }
                
            }
        }
        if (i == 3) {
            playNumsL = [[UILabel alloc]init];
            playNumsL.text = [NSString stringWithFormat:@"%ld+",selectTime * selectMoney * baseCount];
            playNumsL.font = [UIFont boldSystemFontOfSize:20];
            [whiteView addSubview:playNumsL];
            [playNumsL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.equalTo(whiteView);
            }];
        }
        /*
        if (i == 4) {
            for (int j = 0; j < payArray.count; j ++) {
                UIButton *btn = [UIButton buttonWithType:0];
                [btn addTarget:self action:@selector(payTypeButtonClick:) forControlEvents:UIControlEventTouchUpInside];
                btn.tag = 3000 + j;
                [whiteView addSubview:btn];
                [btn mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(whiteView).offset(20);
                    make.right.equalTo(whiteView).offset(-20);
                    make.top.equalTo(whiteView).offset(j * 50);
                    make.height.mas_equalTo(50);
                }];
                UIImageView *payImgV = [[UIImageView alloc]init];
                [payImgV sd_setImageWithURL:[NSURL URLWithString:minstr([payArray[j] valueForKey:@"thumb"])]];
                [btn addSubview:payImgV];
                [payImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(btn);
                    make.centerY.equalTo(btn);
                    make.width.height.mas_equalTo(16);
                }];
                
                UILabel *nameL = [[UILabel alloc]init];
                nameL.text = minstr([payArray[j] valueForKey:@"name"]);
                nameL.font = [UIFont boldSystemFontOfSize:13];
                [btn addSubview:nameL];
                [nameL mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(payImgV.mas_right).offset(5);
                    make.centerY.equalTo(btn);
                }];

                UIImageView *selectImgV = [[UIImageView alloc]init];
                [selectImgV setImage:[UIImage imageNamed:@"hot_nor"]];
                [btn addSubview:selectImgV];
                [selectImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.right.equalTo(btn);
                    make.centerY.equalTo(btn);
                    make.width.height.mas_equalTo(16);
                }];
                [payTypeArray addObject:selectImgV];
                
                UIView *lineV = [[UIView alloc]init];
                lineV.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
                [btn addSubview:lineV];
                [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.bottom.right.equalTo(btn);
                    make.height.mas_equalTo(1);
                }];
                if ([minstr([payArray[j] valueForKey:@"id"]) isEqual:@"balance"]) {
                    UILabel *coinL = [[UILabel alloc]init];
                    coinL.text = [NSString stringWithFormat:@"¥%@",minstr([dic valueForKey:@"coin"])];
                    coinL.font = [UIFont boldSystemFontOfSize:13];
                    coinL.textColor = RGB_COLOR(@"#FB483A", 1);
                    [btn addSubview:coinL];
                    [coinL mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.left.equalTo(nameL.mas_right).offset(5);
                        make.centerY.equalTo(btn);
                    }];

                    if ([minstr([dic valueForKey:@"coin"]) integerValue] < [moneyArray[0] integerValue]) {
                        selectImgV.hidden = YES;
                    }
                }
            }
        }
        */
        viewTop = whiteView.mas_bottom;
        if (i == 4) {
            [backScrollView layoutIfNeeded];
            backScrollView.contentSize = CGSizeMake(0, view.bottom+5);
        }
    }
    //底部试图
    UIView *bottomView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-50-ShowDiff, _window_width, 50+ShowDiff)];
    bottomView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bottomView];
    UILabel *label1 = [[UILabel alloc]init];
    label1.text = YZMsg(@"待支付：");
    label1.font = [UIFont systemFontOfSize:13];
    [bottomView addSubview:label1];
    [label1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(bottomView);
        make.left.equalTo(bottomView).offset(20);
    }];
    needMoneyL = [[UILabel alloc]init];
    needMoneyL.text = [NSString stringWithFormat:@"%@",moneyArray[0]];
    needMoneyL.font = [UIFont systemFontOfSize:13];
    needMoneyL.textColor = RGB_COLOR(@"#FB483A", 1);
    [bottomView addSubview:needMoneyL];
    [needMoneyL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(bottomView);
        make.left.equalTo(label1.mas_right).offset(3);
    }];
    UIImageView *coinIV = [[UIImageView alloc]init];
    [coinIV setImage:[UIImage imageNamed:@"礼物-金币"]];
    [bottomView addSubview:coinIV];
    [coinIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@15);
        make.centerY.equalTo(needMoneyL);
        make.left.equalTo(needMoneyL.mas_right).offset(5);
    }];
    
    payButton = [UIButton buttonWithType:0];
//    [payButton setBackgroundColor:RGB_COLOR(@"#DCDCDC", 1)];
    [payButton setTitle:YZMsg(@"确认支付") forState:0];
    payButton.titleLabel.font = [UIFont systemFontOfSize:13];
//    payButton.userInteractionEnabled = NO;
    payButton.userInteractionEnabled = YES;
    [payButton setBackgroundColor:RGB_COLOR(@"#FB483A", 1)];
    
    [payButton addTarget:self action:@selector(payButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:payButton];
    
    [payButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.equalTo(bottomView);
        make.width.mas_equalTo(100);
        make.height.mas_equalTo(50);
    }];
}
- (void)selectButtonClick:(UIButton *)sender{
//    if (sender.selected) {
//        return;
//    }
    if (sender.tag < 2000) {
        // q金钱按钮
        if (sender == [moneyIteamArray lastObject]) {
            [self showEnterMoneyView];
        }else{
            selectMoney = [moneyArray[sender.tag-1000] integerValue];
            playNumsL.text = [NSString stringWithFormat:@"%ld+",selectMoney * selectTime * baseCount];
            needMoneyL.text = [NSString stringWithFormat:@"%ld",selectMoney];

            for (UIButton *btn in moneyIteamArray) {
                if (sender == btn) {
                    btn.selected = YES;
                    btn.layer.borderColor = RGB_COLOR(@"#FB483A", 1).CGColor;
                }else{
                    btn.selected = NO;
                    btn.layer.borderColor = RGB_COLOR(@"#dcdcdc", 1).CGColor;
                }
            }
        }
    }else{
        selectTime = [timeArray[sender.tag - 2000] intValue];
        playNumsL.text = [NSString stringWithFormat:@"%ld+",selectMoney * selectTime * baseCount];

        //时间n安妮
        for (UIButton *btn in timeIteamArray) {
            if (sender == btn) {
                btn.selected = YES;
                btn.layer.borderColor = RGB_COLOR(@"#FB483A", 1).CGColor;
            }else{
                btn.selected = NO;
                btn.layer.borderColor = RGB_COLOR(@"#dcdcdc", 1).CGColor;
            }
        }
    }
}
- (void)payTypeButtonClick:(UIButton *)sender{
    UIImageView *imgV = payTypeArray[sender.tag - 3000];
    if (imgV.hidden) {
        return;
    }
    selectPayDic = payArray[sender.tag - 3000];
    for (UIImageView *img in payTypeArray) {
        if (imgV == img) {
            img.image = [UIImage imageNamed:@"hot_pay_sel"];
        }else{
            img.image = [UIImage imageNamed:@"hot_nor"];
        }
    }
    payButton.userInteractionEnabled = YES;
    [payButton setBackgroundColor:RGB_COLOR(@"#FB483A", 1)];

}
- (void)payButtonClick{
    NSDictionary *userInfoDic = [_videoInfo valueForKey:@"userinfo"];
    NSString *nameStr = minstr([userInfoDic valueForKey:@"user_nickname"]);
    NSString *hotStr;
    hotStr= [NSString stringWithFormat:YZMsg(@"帮%@进行热门投放,是否继续?"),nameStr];
    if ([nameStr isEqual:[Config getOwnNicename]]) {
        hotStr = YZMsg(@"进行热门投放,是否继续?");
    }

    NSString *msg = [NSString stringWithFormat:@"%@%@[rich]%@",YZMsg(@"您将支付"),needMoneyL.text,hotStr];
    NSDictionary *contentDic = @{@"title":@"",@"msg":msg,@"left":YZMsg(@"取消"),@"right":YZMsg(@"确认支付"),@"richImg":@"礼物-金币"};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            [self doBalancePay];
        }
    }];
    
    
}
- (void)showEnterMoneyView{
    if (!enterMoneyView) {
        enterMoneyView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        enterMoneyView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.2];
        [self.view addSubview:enterMoneyView];
        UITapGestureRecognizer *tap1 = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideMoney)];
        [enterMoneyView addGestureRecognizer:tap1];
        moneyWhiteView = [[UIView alloc]initWithFrame:CGRectMake(_window_width * 0.18, _window_height, _window_width*0.64, 170)];
        moneyWhiteView.backgroundColor = [UIColor whiteColor];
        moneyWhiteView.layer.cornerRadius = 10.0;
        moneyWhiteView.layer.masksToBounds = YES;
        [enterMoneyView addSubview:moneyWhiteView];
        UITapGestureRecognizer *tap2 = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(nothingTap)];
        [moneyWhiteView addGestureRecognizer:tap2];

        UILabel *label = [[UILabel alloc]init];
        label.text = YZMsg(@"请输入要投放的金额");
        label.font = [UIFont systemFontOfSize:14];
        label.textColor = RGB_COLOR(@"#323232", 1);
        [moneyWhiteView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(moneyWhiteView);
            make.top.equalTo(moneyWhiteView);
            make.height.mas_equalTo(45);
        }];
        moneyT = [[UITextField alloc]init];
        moneyT.textAlignment = NSTextAlignmentCenter;
        moneyT.keyboardType = UIKeyboardTypeNumberPad;
        moneyT.layer.borderWidth = 1;
        moneyT.layer.borderColor = RGB_COLOR(@"#D2D2D2", 1).CGColor;
        [moneyWhiteView addSubview:moneyT];
        [moneyT mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(moneyWhiteView).offset(25);
            make.right.equalTo(moneyWhiteView).offset(-25);
            make.top.equalTo(label.mas_bottom);
            make.height.mas_equalTo(40);
        }];
        UILabel *label2 = [[UILabel alloc]init];
        label2.text = YZMsg(@"金额不能低于100，且为10的倍数");
        label2.font = [UIFont systemFontOfSize:11];
        label2.textColor = RGB_COLOR(@"#646464", 1);
        label2.numberOfLines = 0;
        [moneyWhiteView addSubview:label2];
        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(moneyT);
            make.top.equalTo(moneyT.mas_bottom).offset(5);
        }];
        
        UIButton *sureBtn = [UIButton buttonWithType:0];
        [sureBtn setBackgroundColor:RGB_COLOR(@"#FB483A", 1)];
        [sureBtn setTitle:YZMsg(@"确定") forState:0];
        sureBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [moneyWhiteView addSubview:sureBtn];
        [sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(moneyT);
            make.height.mas_equalTo(35);
            make.bottom.equalTo(moneyWhiteView).offset(-15);
        }];
        [enterMoneyView layoutIfNeeded];
    }
    enterMoneyView.hidden = NO;
    [UIView animateWithDuration:0.2 animations:^{
        moneyWhiteView.center = enterMoneyView.center;
    }];

}
- (void)hideMoney{
    [moneyT resignFirstResponder];
    [UIView animateWithDuration:0.2 animations:^{
        moneyWhiteView.y = _window_height;
    } completion:^(BOOL finished) {
        enterMoneyView.hidden = YES;
    }];

}
- (void)nothingTap{
    [moneyT resignFirstResponder];

}
- (void)sureBtnClick{
    if ([PublicObj checkNull:moneyT.text]) {
        [MBProgressHUD showError:YZMsg(@"金额不能为空")];
        return;
    }
    if ([moneyT.text intValue] < 100) {
        [MBProgressHUD showError:YZMsg(@"金额不能小于100")];
        return;
    }
    selectMoney = [moneyT.text integerValue]/10 * 10;
    playNumsL.text = [NSString stringWithFormat:@"%ld+",selectMoney * selectTime * baseCount];
    UIButton *sender = [moneyIteamArray lastObject];
    [sender setTitle:[NSString stringWithFormat:@"%ld",selectMoney] forState:0];
    [sender setImage:[UIImage imageNamed:@"礼物-金币"] forState:0];
    [sender setTitleEdgeInsets:UIEdgeInsetsMake(0, - sender.imageView.image.size.width-2, 0, sender.imageView.image.size.width)];
    [sender setImageEdgeInsets:UIEdgeInsetsMake(0, sender.titleLabel.bounds.size.width+2, 0, -sender.titleLabel.bounds.size.width)];
    
    needMoneyL.text = [NSString stringWithFormat:@" %ld",selectMoney];

    for (UIButton *btn in moneyIteamArray) {
        if (sender == btn) {
            btn.selected = YES;
            btn.layer.borderColor = RGB_COLOR(@"#FB483A", 1).CGColor;
        }else{
            btn.selected = NO;
            btn.layer.borderColor = RGB_COLOR(@"#dcdcdc", 1).CGColor;
        }
    }
    [self hideMoney];
}

- (void)doBalancePay{
    [MBProgressHUD showMessage:@""];
    
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"videoid":[_videoInfo valueForKey:@"id"],
                             @"length":@(selectTime),
                             @"money":@(selectMoney)
                             };
    [YBNetworking postWithUrl:@"Popular.BalancePay" Dic:subdic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            [MBProgressHUD showError:YZMsg(@"支付成功")];
            [self clickLeftBtn];
        }
        else if (code == 1005){
            [self balanceAlert];
        }
        else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}

-(void)balanceAlert {
    NSString *msg = [NSString stringWithFormat:@"%@",YZMsg(@"您账户余额不足,请充值后再次尝试")];
    NSDictionary *contentDic = @{@"title":@"",@"msg":msg,@"left":YZMsg(@"取消"),@"right":YZMsg(@"去充值"),@"richImg":@""};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            YBRechargeVC *chargeVC = [[YBRechargeVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:chargeVC animated:YES];
        }
    }];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
