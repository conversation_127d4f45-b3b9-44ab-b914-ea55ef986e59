//
//  YBShareView.h
//  YBVideo
//
//  Created by YB007 on 2019/8/29.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

#import <ShareSDK/ShareSDK.h>
#import <ShareSDK/ShareSDK+Base.h>

typedef NS_ENUM(NSInteger,RKShareType) {
    //注:无UI表示只调用 只调用 YBShareView 中的方法,其UI界面在当前页
    RKShareType_LookVdieo,          //视频界面分享
    RKShareType_Invite,             //个中-邀请好友分享
    RKShareType_LivePre,            //直播主播预览界面分享(无UI)
    RKShareType_Liveing,            //直播中分享
    RKShareType_VideoPublish,       //发布界面分享(无UI)-旧版等待发布完成
    
    //... 20-7-10新加
    RKShareType_VPMidPop,           //发布视频分享-新版发布完成
    
    //ray-21-3-13新加
    RKShareType_card,               //名片分享
};

/** 注意:
 * codeEvent0-分享成功  -1分享失败  1关闭界面  2举报 3删除 4合拍 5上热门 6开播预览分享-发布分享 7-收藏
 * nums-分享成功后分享总数
 */
typedef void (^ShareBlock)(int codeEvent,NSString *nums);

@interface YBShareView : UIView

@property(nonatomic,copy)ShareBlock shareEvent;

/** limitDic
 * video_status 0-不能合拍  1-能合拍
 * video_msg:不能合拍提示
 * limit_status 0-有限制   1-无限制
 */
@property(nonatomic,strong)NSDictionary *limitDic;

//底部分享变量
@property (weak, nonatomic) IBOutlet UIView *bgView;                            //底部
@property (weak, nonatomic) IBOutlet UILabel *titleL;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgViewHeight;          //270-底部显示默认高度

@property (weak, nonatomic) IBOutlet UICollectionView *shareCollectionView;     //底部
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *shareCollectionHeight; //默认110

@property (weak, nonatomic) IBOutlet UICollectionView *fucCollectionView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *fucCollectionHeight;   //110

//注:无UI表示只调用 只调用 YBShareView 中的方法,其UI界面在当前页
//fromType 0:视频界面分享 1:三级分销分享 2:直播主播预览界面分享(无UI) 3直播中分享 4发布界面分享(无UI)

#pragma mark - 有分享界面 (弹窗性质-底部)
+(instancetype)showShareWithType:(RKShareType)fromType parameter:(NSDictionary *)parameter commplete:(ShareBlock)complete;

#pragma mark - 无分享界面
+(instancetype)noShareUIManager;
-(void)executeShareWithType:(RKShareType)fromType shareType:(NSString *)shareType parameter:(NSDictionary *)parameter complete:(ShareBlock)complete;

#pragma mark - 有分享界面 (弹窗性质-中部)
//中部分享显示
@property (weak, nonatomic) IBOutlet UIView *midBgView;     //背景
@property (weak, nonatomic) IBOutlet UILabel *midDesL;      //描述
@property (weak, nonatomic) IBOutlet UIView *midTypeBg;     //三方图标背景

+(instancetype)showMidPopShareType:(RKShareType)fromType parameter:(NSDictionary *)parameter commplete:(ShareBlock)complete;


@end


