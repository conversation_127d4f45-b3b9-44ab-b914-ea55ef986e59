//
//  PublishShareV.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/25.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "PublishShareV.h"

#import <ShareSDK/ShareSDK.h>
#import <ShareSDK/ShareSDK+Base.h>

@interface PublishShareV()
{
    float WW;
    float HH;
    float scrollH;
    NSMutableArray *_canSelShareBtnA;
    NSString *_selTypeStr;
}
@property(nonatomic,strong)UILabel *desL;
@property(nonatomic,strong)UIScrollView *scrollView;

@property(nonatomic,strong)NSArray *shareArray;
@property(nonatomic,strong)NSArray *titleArray;

@end

@implementation PublishShareV

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        WW = frame.size.width;
        HH = frame.size.height;
        scrollH = HH - 30;
        _canSelShareBtnA = [NSMutableArray array];
        self.shareArray = [common share_type];
        _selTypeStr = @"qx";
        NSMutableArray *m_arr = [NSMutableArray array];
        for (NSString *share_title in _shareArray) {
            NSString *show_title;
            if ([share_title isEqual:@"wx"]) {
                show_title = YZMsg(@"微信");
            }else if ([share_title isEqual:@"wchat"]){
                show_title = YZMsg(@"朋友圈");
            }else if ([share_title isEqual:@"qzone"]){
                show_title = YZMsg(@"QQ空间");
            }else if ([share_title isEqual:@"qq"]){
                show_title = @"QQ";
            }else if ([share_title isEqual:@"facebook"]){
                show_title = @"Facebook";
            }else if ([share_title isEqual:@"twitter"]){
                show_title = YZMsg(@"推特");
            }
            [m_arr addObject:show_title];
        }
        _titleArray = [NSArray arrayWithArray:m_arr];
        
        [self addSubview:self.desL];
        [self addSubview:self.scrollView];
        
    }
    return self;
}

#pragma mark - 点击事件
-(void)clickBtn:(UIButton *)sender {
    if ([_selTypeStr isEqual:_shareArray[sender.tag-1000]]) {
        sender.selected = NO;
        _selTypeStr = @"qx";
    }else{
        sender.selected = YES;
        _selTypeStr = _shareArray[sender.tag-1000];
        for (UIButton *btn in _canSelShareBtnA) {
            if (btn != sender) {
                btn.selected = NO;
            }
        }
    }
    if (self.shareEvent) {
        self.shareEvent(_selTypeStr);
    }
}

#pragma mark - set/get
-(UILabel *)desL {
    if (!_desL) {
        _desL = [[UILabel alloc]initWithFrame:CGRectMake(15, 0, WW-30, 30)];
        _desL.text = YZMsg(@"分享至");
        /*
        if (self.shareArray.count>4) {
            _desL.text = YZMsg(@"分享至(左右滑动显示更多)");
        }
        */
        _desL.textColor = RGB_COLOR(@"#666666", 1);
        _desL.font = SYS_Font(15);
        _desL.backgroundColor = [UIColor clearColor];
    }
    return _desL;
}

-(UIScrollView *)scrollView {
    if (!_scrollView) {
        
        CGFloat btnW = (WW-30)/4;
        _scrollView = [[UIScrollView alloc]initWithFrame:CGRectMake(15, 30, WW-30, scrollH)];
        _scrollView.contentSize = CGSizeMake(btnW*_shareArray.count, scrollH);
        _scrollView.showsHorizontalScrollIndicator = NO;
        
        CGFloat x=0;
        for (int i=0; i<_shareArray.count; i++) {
            UIButton *btn = [UIButton buttonWithType:0];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"分享灰-%@",_shareArray[i]]] forState:0];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"分享-%@",_shareArray[i]]] forState:UIControlStateSelected];
            [btn addTarget:self action:@selector(clickBtn:) forControlEvents:UIControlEventTouchUpInside];
            [btn setTitle:_titleArray[i] forState:UIControlStateNormal];
            [btn setTitleColor:RGB_COLOR(@"#969696", 1) forState:UIControlStateNormal];
            btn.titleLabel.font = SYS_Font(11);
            btn.frame = CGRectMake(x,0,btnW,btnW);
            btn.selected = NO;
            btn.tag = 1000+i;
            x+=btnW;
            [_scrollView addSubview:btn];
            [_canSelShareBtnA addObject:btn];
            btn = [PublicObj setUpImgDownText:btn];
        }
        
    }
    return _scrollView;
}



@end
