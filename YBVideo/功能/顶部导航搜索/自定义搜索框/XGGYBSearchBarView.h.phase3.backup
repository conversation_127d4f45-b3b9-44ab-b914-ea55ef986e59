//
//  YBSearchBarView.h
//  YBVideo
//
//  Created by YB007 on 2020/6/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,RKSearchType) {
    R<PERSON>earch_Search,
    R<PERSON><PERSON><PERSON>_Cancle,
    R<PERSON>earch_ValueChange,
    RKSearch_BeginEditing,
};

typedef void (^YBSearchEventBlock)(RKSearchType searchType);

@interface YBSearchBarView : UIView

@property(nonatomic,copy)YBSearchEventBlock searchEvent;
@property(nonatomic,strong)MyTextField *searchTF;


@end


