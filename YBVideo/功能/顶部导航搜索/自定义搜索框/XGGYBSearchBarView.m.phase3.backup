//
//  YBSearchBarView.m
//  YBVideo
//
//  Created by YB007 on 2020/6/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBSearchBarView.h"


@interface YBSearchBarView()<UITextFieldDelegate>

@property(nonatomic,strong)UIButton *cancleBtn;
@property(nonatomic,strong)UIButton *clearButton;

@end

@implementation YBSearchBarView

- (instancetype)init {
    self = [super init];
    if (self) {
        [self createUI];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self createUI];
    }
    return self;
}


-(void)createUI {
    
    _searchTF = [[MyTextField alloc]init];
    _searchTF.placeholder = YZMsg(@"搜索用户|视频");
    _searchTF.font = SYS_Font(14);
    _searchTF.layer.cornerRadius = 18;
    _searchTF.layer.masksToBounds = YES;
    _searchTF.backgroundColor = RGB_COLOR(@"#201F37", 1);
    _searchTF.textColor = [UIColor whiteColor];
    _searchTF.clearButtonMode = UITextFieldViewModeWhileEditing;
    _searchTF.delegate = self;
    _searchTF.returnKeyType = UIReturnKeySearch;
    _searchTF.tintColor = UIColor.whiteColor;
    [_searchTF addTarget:self action:@selector(searchTFValueChange:) forControlEvents:UIControlEventEditingChanged];
    [self addSubview:_searchTF];
    
    UIView *leftView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 36, 36)];
    _searchTF.leftView = leftView;
    _searchTF.leftViewMode = UITextFieldViewModeAlways;
    UIImageView *img = [[UIImageView alloc]initWithFrame:CGRectMake(10, 10, 16, 16)];
    img.image = [UIImage imageNamed:@"搜索-搜索"];
    [leftView addSubview:img];
    
    UIView *rightView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 24, 36)];
    _clearButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_clearButton addTarget:self action:@selector(clickClearBtn) forControlEvents:UIControlEventTouchUpInside];
    _clearButton.frame = CGRectMake(0, 10, 16, 16);
    [_clearButton setImage:[UIImage imageNamed:@"搜索-清除"] forState:0];
    [rightView addSubview:_clearButton];
    _searchTF.rightView = rightView;
    _searchTF.rightViewMode = UITextFieldViewModeAlways;
    
    _cancleBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [_cancleBtn setTitle:YZMsg(@"取消") forState:0];
    [_cancleBtn addTarget:self action:@selector(clickSearchCancleBtn) forControlEvents:UIControlEventTouchUpInside];
    _cancleBtn.titleLabel.font = SYS_Font(16);
    [_cancleBtn setTitleColor:GrayText forState:0];
    [self addSubview:_cancleBtn];
    [_cancleBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_searchTF.mas_right).offset(8);
        make.right.equalTo(self.mas_right).offset(-20);
        make.height.centerY.equalTo(_searchTF);
        if ([lagType isEqual:ZH_CN]) {
            make.width.mas_equalTo(40);
        }else{
            make.width.mas_equalTo(60);
        }
    }];
    
    [_searchTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(20);
        make.height.mas_equalTo(36);
        make.centerY.equalTo(self);
    }];
    
    
}
- (void)textFieldDidBeginEditing:(UITextField *)textField;  {
    [self judgeClearBtnShow];
    if (self.searchEvent) {
        self.searchEvent(RKSearch_BeginEditing);
    }
}
-(void)clickSearchCancleBtn {
    [self judgeClearBtnShow];
    if (self.searchEvent) {
        self.searchEvent(RKSearch_Cancle);
    }
}
-(void)clickClearBtn {
    _searchTF.text = @"";
    [self judgeClearBtnShow];
    if (self.searchEvent) {
        self.searchEvent(RKSearch_ValueChange);
    }
}
-(void)searchTFValueChange:(UITextField *)textField {
    [self judgeClearBtnShow];
    if (self.searchEvent) {
        self.searchEvent(RKSearch_ValueChange);
    }
}
- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [self judgeClearBtnShow];
    if (self.searchEvent) {
        self.searchEvent(RKSearch_Search);
    }
    return YES;
}
-(void)judgeClearBtnShow {
    if (_searchTF.text.length > 0) {
        _clearButton.hidden = NO;
    }else{
        _clearButton.hidden = YES;
    }
}
@end
