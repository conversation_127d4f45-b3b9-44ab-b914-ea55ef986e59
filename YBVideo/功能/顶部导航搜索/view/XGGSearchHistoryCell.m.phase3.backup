//
//  SearchHistoryCell.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/27.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "SearchHistoryCell.h"

@implementation SearchHistoryCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    
}
+(SearchHistoryCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    SearchHistoryCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SearchHistoryCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"SearchHistoryCell" owner:nil options:nil]objectAtIndex:0];
        cell.backgroundColor = CellRow_Cor;
    }
    return cell;
}
- (IBAction)clickDelBtn:(UIButton *)sender {
    
    self.delEvent(@"");
    
}
@end
