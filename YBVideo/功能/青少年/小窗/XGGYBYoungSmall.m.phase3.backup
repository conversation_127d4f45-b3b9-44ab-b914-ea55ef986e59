//
//  YBYoungSmall.m
//  YBVideo
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBYoungSmall.h"

@interface YBYoungSmall()

@property(nonatomic,strong)NSString *alertStr;
@property(nonatomic,strong)UIView *bgView;

@end

@implementation YBYoungSmall

+(instancetype)showYoungPop:(NSString *)alert;{
    YBYoungSmall *view = [[YBYoungSmall alloc]init];
    view.frame = CGRectMake(0, 0, _window_width, _window_height);
    view.alertStr = alert;
    [view createUI];
    [[UIApplication sharedApplication].delegate.window addSubview:view];
    [PublicObj layoutWindowPopLayer];
    return view;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch; {
    if ([touch.view isDescendantOfView:self.bgView]) {
        return NO;
    }
    return YES;
}
-(void)dissmissView {
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}
-(void)createUI {
    /*
     UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(dissmissView)];
     tap.delegate = self;
     [self addGestureRecognizer:tap];
     */
    self.backgroundColor = ybPopBgCol;
    _bgView = [[UIView alloc]init];
    _bgView.backgroundColor = UIColor.whiteColor;
    _bgView.layer.cornerRadius = 10;
    _bgView.layer.masksToBounds = YES;
    [self addSubview:_bgView];
    [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.width.bottom.equalTo(self);
    }];
    
    //
    UIImageView *topIV = [[UIImageView alloc]init];
    topIV.image = [UIImage imageNamed:@"青少年-小窗"];
    [_bgView addSubview:topIV];
    [topIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(70);
        make.width.equalTo(topIV.mas_height).multipliedBy(359.0/177);
        make.top.equalTo(_bgView.mas_top).offset(20);
        make.centerX.equalTo(_bgView);
    }];
    
    //
    UILabel *contentL = [[UILabel alloc]init];
    contentL.font = SYS_Font(15);
    contentL.text = _alertStr;
    contentL.textColor = RGB_COLOR(@"#323232", 1);
    contentL.numberOfLines = 0;
    [_bgView addSubview:contentL];
    [contentL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_bgView.mas_width).offset(-40);
        make.top.equalTo(topIV.mas_bottom).offset(20);
        make.centerX.equalTo(_bgView);
    }];
    
    //
    UIButton *enterYoungBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [enterYoungBtn setTitle:YZMsg(@"进入青少年模式＞") forState:0];
    enterYoungBtn.titleLabel.font = SYS_Font(15);
    [enterYoungBtn setTitleColor:Pink_Cor forState:0];
    [enterYoungBtn addTarget:self action:@selector(clickEnterBtn) forControlEvents:UIControlEventTouchUpInside];
    [_bgView addSubview:enterYoungBtn];
    [enterYoungBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(_bgView);
        make.top.equalTo(contentL.mas_bottom).offset(10);
    }];
    
    //
    UIButton *knowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [knowBtn setTitle:YZMsg(@"我知道了") forState:0];
    knowBtn.titleLabel.font = SYS_Font(15);
    [knowBtn setTitleColor:UIColor.whiteColor forState:0];
    [knowBtn addTarget:self action:@selector(clickKnowBtn) forControlEvents:UIControlEventTouchUpInside];
    knowBtn.layer.cornerRadius = 22;
    knowBtn.layer.masksToBounds = YES;
    knowBtn.backgroundColor = Pink_Cor;
    [_bgView addSubview:knowBtn];
    [knowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_bgView.mas_width).multipliedBy(0.7);
        make.height.mas_equalTo(44);
        make.centerX.equalTo(_bgView);
        make.top.equalTo(enterYoungBtn.mas_bottom).offset(20);
        make.bottom.equalTo(_bgView.mas_bottom).offset(-25-ShowDiff);
    }];
    
    [_bgView layoutIfNeeded];
    [_bgView jk_setRoundedCorners:UIRectCornerTopLeft|UIRectCornerTopRight radius:10];
}
-(void)clickEnterBtn {
    [[YBYoungManager shareInstance] smallEnterYoungModel];
}
-(void)clickKnowBtn {
    [[YBYoungManager shareInstance] destroySamllPop];
}
@end
