//
//  YBYoungManager.h
//  YBVideo
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger,YoungFrom) {
    YoungFrom_Home,         // 首页
    YoungFrom_Center,       // 个人主页
};
typedef NS_ENUM(NSInteger,YoungMode) {
    YoungMode_First,        // 首次设置
    YoungMode_FirstReset,   // 首次设置后重设===>有个弹窗提示【重新设置  确定】
    YoungMode_Open,         // 开启
    YoungMode_Close,        // 关闭
};
@interface YBYoungManager : NSObject
// 青少年开关
@property(nonatomic,assign,readonly)BOOL youngSwitch;
// 是否需要展示开启弹窗【首次启动、登录后】
@property(nonatomic,assign)BOOL needShowPop;
// 青少年时长使用完毕或者到了禁止使用时间区间【YES-受限;NO-不受限】
@property(nonatomic,assign)BOOL youngBan;

+(instancetype)shareInstance;
/// 启动app检测
-(void)checkYoungStatus:(YoungFrom)youngFrom;
#pragma mark - 小窗
-(void)showYoungSmallPop;
-(void)destroySamllPop;
-(void)smallEnterYoungModel;
#pragma mark - 
/// 开启-关闭
-(void)changeYoungSwitch:(int)youngSwitch youngInfo:(NSDictionary *)youngInfo;
/// 前台
-(void)appActive;
/// 后台
-(void)appResignActive;
/// 杀进程
-(void)appKilled;

@end


