//
//  YBYoungModifyVC.m
//  YBVideo
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBYoungModifyVC.h"

#import "RKCodeInputView.h"

@interface YBYoungModifyVC ()

@property(nonatomic,strong)RKCodeInputView *civOld;     //旧密码
@property(nonatomic,strong)RKCodeInputView *civNew;     //新密码
@property(nonatomic,strong)RKCodeInputView *civSure;    //确认密码

@property(nonatomic,strong)YBButton *modifyBtn;

@end

@implementation YBYoungModifyVC

- (UIStatusBarStyle)preferredStatusBarStyle {
    if (@available(iOS 13.0,*)) {
        return UIStatusBarStyleDarkContent;
    }
    return UIStatusBarStyleDefault;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.titleL.text = YZMsg(@"修改密码");
    self.titleL.textColor = RGB_COLOR(@"#323232", 1);
    [self.leftBtn setImage:[UIImage imageNamed:@"pubBlack_back"] forState:0];
    self.subNavi.backgroundColor = UIColor.whiteColor;
    self.view.backgroundColor = UIColor.whiteColor;
    
    YBWeakSelf;
    NSArray *titleA = @[@"请输入当前密码",@"请输入新的密码",@"请确定新的密码"];
    MASViewAttribute *mas_top = self.naviView.mas_bottom;
    for (int i =0 ; i<titleA.count; i++) {
        UIView *itemV = [[UIView alloc]init];
        itemV.backgroundColor = UIColor.clearColor;
        [self.view addSubview:itemV];
        [itemV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
            make.left.equalTo(self.view.mas_left).offset(15);
            make.top.equalTo(mas_top).offset(20);
        }];
        mas_top = itemV.mas_bottom;
        
        UILabel *titL = [[UILabel alloc]init];
        titL.text = YZMsg(titleA[i]);
        titL.font = SYS_Font(15);
        titL.textColor = RGB_COLOR(@"#323232", 1);
        [itemV addSubview:titL];
        [titL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(itemV);
            make.height.mas_equalTo(20);
        }];
        
        RKCodeInputView *codeIV = [[RKCodeInputView alloc]init];
        codeIV.contentView.backgroundColor = UIColor.whiteColor;
        codeIV.secureTextEntry = YES;
        [itemV addSubview:codeIV];
        [codeIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(itemV.mas_width).multipliedBy(0.8);
            make.left.equalTo(itemV);
            make.height.mas_equalTo(40);
            make.top.equalTo(titL.mas_bottom).offset(10);
            make.bottom.equalTo(itemV);
        }];
        
        [itemV layoutIfNeeded];
        [codeIV updateSubViews];
        if (i == 0) {
            _civOld = codeIV;
            _civOld.becomeFirstRes = YES;
        }else if(i == 1){
            _civNew = codeIV;
        }else{
            _civSure = codeIV;
        }
        codeIV.changeEvent = ^{
            [weakSelf changeEvent];
        };
        __block RKCodeInputView *weakCodeIV = codeIV;
        codeIV.finishEvent = ^{
            [weakSelf inputFinish:weakCodeIV];
        };
    }
    
    _modifyBtn = [YBButton buttonWithType:UIButtonTypeCustom];
    _modifyBtn.titleLabel.font = SYS_Font(15);
    _modifyBtn.layer.cornerRadius = 5;
    _modifyBtn.layer.masksToBounds = YES;
    [_modifyBtn addTarget:self action:@selector(clickModifyBtn) forControlEvents:UIControlEventTouchUpInside];
    [_modifyBtn setTitleColor:RGB_COLOR(@"#7d7d7d", 1) forState:0];
    [_modifyBtn jk_setBackgroundColor:RGB_COLOR(@"#d9d9d9", 1) forState:0];
    [_modifyBtn setTitle:YZMsg(@"确定修改") forState:0];
    [_modifyBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:UIControlStateSelected];
    [_modifyBtn jk_setBackgroundColor:Pink_Cor forState:UIControlStateSelected];
    [_modifyBtn setTitle:YZMsg(@"确定修改") forState:UIControlStateSelected];
    _modifyBtn.enabled = NO;
    [self.view addSubview:_modifyBtn];
    [_modifyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.view.mas_width).offset(-30);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(46);
        make.bottom.equalTo(self.view.mas_bottom).offset(-60-ShowDiff);
    }];
    
}

-(void)changeEvent {
    if (_civOld.codeText.length>=4 &&
        _civNew.codeText.length>=4 &&
        _civSure.codeText.length>=4) {
        
        _modifyBtn.enabled = YES;
        _modifyBtn.selected = YES;
    }else{
        _modifyBtn.enabled = NO;
        _modifyBtn.selected = NO;
    }
}
-(void)inputFinish:(RKCodeInputView *)finishInput {
    if (finishInput == _civOld) {
        _civNew.becomeFirstRes = YES;
    }else if(finishInput == _civNew){
        _civSure.becomeFirstRes = YES;
    }
}

-(void)resetAll {
    [_civOld clearText];
    [_civNew clearText];
    [_civSure clearText];
    [_civNew cancelCursor];
    [_civSure cancelCursor];
    _civOld.becomeFirstRes = YES;
    [self changeEvent];
}
-(void)clickModifyBtn {
    /**
     * type(1设置密码并开启 2开启青少年模式 3关闭青少年模式 4修改密码 5重新设置密码)
     * pwd当前密码 newpwd 修改新密码 newspwd 确认新密码
     */
    NSDictionary *postDic = @{
        @"type":@"4",
        @"pwd":_civOld.codeText,
        @"newpwd":_civNew.codeText,
        @"newspwd":_civSure.codeText,
    };
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.setTeenagers" Dic:postDic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD showError:msg];
        if (code == 0) {
            [[XGGAppDelegate sharedAppDelegate] popViewController:YES];
        }else{
            [weakSelf resetAll];
        }
    } Fail:^(id fail) {
        [weakSelf resetAll];
    }];
}

@end
