//
//  YBYoungModeVC.m
//  YBVideo
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBYoungModeVC.h"

#import "YBYoungSetVC.h"
#import "YBYoungModifyVC.h"

@interface YBYoungModeVC ()

@property(nonatomic,strong)UILabel *topTitleL;
@property(nonatomic,strong)UIScrollView *midScrollView;
@property(nonatomic,strong)UIView *scrolllContent;
@property(nonatomic,strong)YBButton *youngSwitchBtn;
@property(nonatomic,strong)UIButton *youngModifyBtn;

@end

@implementation YBYoungModeVC


- (UIStatusBarStyle)preferredStatusBarStyle {
    if (@available(iOS 13.0,*)) {
        return UIStatusBarStyleDarkContent;
    }
    return UIStatusBarStyleDefault;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.titleL.text = YZMsg(@"青少年模式");
    self.titleL.textColor = RGB_COLOR(@"#323232", 1);
    [self.leftBtn setImage:[UIImage imageNamed:@"pubBlack_back"] forState:0];
    self.subNavi.backgroundColor = UIColor.whiteColor;
    self.view.backgroundColor = UIColor.whiteColor;
    
    UIImageView *topIV = [[UIImageView alloc]init];
    topIV.image = [UIImage imageNamed:@"青少年-大窗"];
    [self.view addSubview:topIV];
    [topIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.width.height.mas_equalTo(130);
        make.top.equalTo(self.naviView.mas_bottom).offset(20);
    }];
    
    _topTitleL = [[UILabel alloc]init];
    _topTitleL.font = [UIFont boldSystemFontOfSize:18];
    _topTitleL.textColor = RGB_COLOR(@"#323232", 1);
    _topTitleL.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:_topTitleL];
    [_topTitleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.height.mas_equalTo(30);
        make.top.equalTo(topIV.mas_bottom).offset(20);
    }];
    
    _midScrollView = [[UIScrollView alloc]init];
    _midScrollView.backgroundColor = UIColor.clearColor;
    _midScrollView.showsVerticalScrollIndicator = NO;
    [self.view addSubview:_midScrollView];
    [_midScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.top.equalTo(_topTitleL.mas_bottom).offset(20);
    }];
    
    _scrolllContent = [[UIView alloc]init];
    _scrolllContent.backgroundColor = UIColor.clearColor;
    [_midScrollView addSubview:_scrolllContent];
    [_scrolllContent mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(_window_width-40);
        make.left.equalTo(_midScrollView.mas_left).offset(20);
        make.top.equalTo(_midScrollView.mas_top).offset(5);
    }];
    
    _youngSwitchBtn = [YBButton buttonWithType:UIButtonTypeCustom];
    _youngSwitchBtn.titleLabel.font = SYS_Font(15);
    _youngSwitchBtn.layer.cornerRadius = 5;
    _youngSwitchBtn.layer.masksToBounds = YES;
    [_youngSwitchBtn setTitleColor:UIColor.whiteColor forState:0];
    [_youngSwitchBtn addTarget:self action:@selector(clickSwitchBtn) forControlEvents:UIControlEventTouchUpInside];
    _youngSwitchBtn.backgroundColor = Pink_Cor;
    [_youngSwitchBtn setTitle:YZMsg(@"开启青少年模式") forState:0];
    [_youngSwitchBtn setTitle:YZMsg(@"关闭青少年模式") forState:UIControlStateSelected];
    [self.view addSubview:_youngSwitchBtn];
    [_youngSwitchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.view.mas_width).offset(-80);
        make.centerX.equalTo(self.view);
        make.top.equalTo(_midScrollView.mas_bottom).offset(20);
        make.height.mas_equalTo(46);
    }];
    
    _youngModifyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _youngModifyBtn.titleLabel.font = SYS_Font(15);
    _youngModifyBtn.layer.cornerRadius = 5;
    _youngModifyBtn.layer.masksToBounds = YES;
    _youngModifyBtn.layer.borderColor = RGB_COLOR(@"#646464", 1).CGColor;
    _youngModifyBtn.layer.borderWidth = 1;
    [_youngModifyBtn setTitleColor:RGB_COLOR(@"#646464", 1) forState:0];
    [_youngModifyBtn addTarget:self action:@selector(clickModifyBtn) forControlEvents:UIControlEventTouchUpInside];
    _youngModifyBtn.backgroundColor = UIColor.whiteColor;
    [_youngModifyBtn setTitle:YZMsg(@"修改密码") forState:0];
    [self.view addSubview:_youngModifyBtn];
    [_youngModifyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(_youngSwitchBtn);
        make.height.mas_equalTo(0);
        make.top.equalTo(_youngSwitchBtn.mas_bottom).offset(10);
        make.bottom.equalTo(self.view.mas_bottom).offset(-60-ShowDiff);
    }];
    
    [self updateData];
}

-(void)updateData {
    if (_ispwd == 1) {
        [_youngModifyBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.height.equalTo(_youngSwitchBtn);
            make.height.equalTo(_youngSwitchBtn);
            make.top.equalTo(_youngSwitchBtn.mas_bottom).offset(10);
            make.bottom.equalTo(self.view.mas_bottom).offset(-60-ShowDiff);
        }];
    }
    _youngSwitchBtn.selected = _isstate;
    if (_isstate == 1) {
        _topTitleL.text = YZMsg(@"青少年模式已开启");
    }else{
        _topTitleL.text = YZMsg(@"青少年模式未开启");
    }
    
    [_scrolllContent removeAllSubViews];
    MASViewAttribute *mas_top = _scrolllContent.mas_top;
    for (int i = 0; i<_youngList.count; i++) {
        UIView *itemV = [[UIView alloc]init];
        itemV.backgroundColor = UIColor.clearColor;
        [_scrolllContent addSubview:itemV];
        [itemV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.equalTo(_scrolllContent);
            make.top.equalTo(mas_top).offset(2);
            make.height.mas_greaterThanOrEqualTo(40);
            if ((i+1) == _youngList.count) {
                make.bottom.equalTo(_scrolllContent.mas_bottom).offset(-5);
            }
        }];
        mas_top = itemV.mas_bottom;
        
        NSDictionary *subDic = _youngList[i];
        UIImageView *leftIV = [[UIImageView alloc]init];
        [leftIV sd_setImageWithURL:[NSURL URLWithString:minstr([subDic valueForKey:@"thumb"])]];
        [itemV addSubview:leftIV];
        [leftIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(itemV);
            make.width.height.mas_equalTo(20);
        }];
        UILabel *contentL = [[UILabel alloc]init];
        contentL.font = SYS_Font(15);
        contentL.textColor = RGB_COLOR(@"#5d5d5d", 1);
        contentL.numberOfLines = 0;
        contentL.text = minstr([subDic valueForKey:@"name"]);
        [itemV addSubview:contentL];
        [contentL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(leftIV.mas_right).offset(8);
            make.right.lessThanOrEqualTo(itemV.mas_right);
            make.top.equalTo(leftIV);
            make.bottom.equalTo(itemV.mas_bottom).offset(-20);
        }];
    }
    [_scrolllContent layoutIfNeeded];
    CGFloat maxHeight = CGRectGetHeight(_scrolllContent.frame);
    _midScrollView.contentSize = CGSizeMake(0, maxHeight);
    
}

-(void)clickSwitchBtn {
    YBYoungSetVC *setVC = [[YBYoungSetVC alloc]init];
    if (_ispwd == NO) {
        setVC.youngMode = YoungMode_First;
    }else if (_isstate == 1){
        setVC.youngMode = YoungMode_Close;
    }else{
        setVC.youngMode = YoungMode_Open;
    }
    [[XGGAppDelegate sharedAppDelegate] pushViewController:setVC animated:YES];
}
-(void)clickModifyBtn {
    YBYoungModifyVC *modifyVC = [[YBYoungModifyVC alloc]init];
    [[XGGAppDelegate sharedAppDelegate] pushViewController:modifyVC animated:YES];
}
@end
