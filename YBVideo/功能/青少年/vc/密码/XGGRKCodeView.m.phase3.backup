//
//  RKCodeView.m
//  YBSoul
//
//  Created by YB007 on 2021/3/9.
//

#import "RKCodeView.h"

@interface RKCodeView()

@property (nonatomic, strong) UILabel *codeLabel;
@property (nonatomic, strong) UIView *codeEdgeView;
@property (nonatomic, strong) UIView *cursor;

@end

@implementation RKCodeView

- (instancetype)init {
    self = [super init];
    if (self) {
        [self config];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self config];
    }
    return self;
}

- (void)config {
    
    self.userInteractionEnabled = NO;
    
    _codeEdgeView = [[UIView alloc] init];
    _codeEdgeView.userInteractionEnabled = NO;
    _codeEdgeView.backgroundColor = UIColor.clearColor;
    _codeEdgeView.layer.borderWidth = 1;
    _codeEdgeView.layer.borderColor = UIColor.clearColor.CGColor;
    _codeEdgeView.layer.cornerRadius = 5;
    _codeEdgeView.backgroundColor = RGB_COLOR(@"#f6f6f6", 1);
    [self addSubview:_codeEdgeView];
    
    _codeLabel = [[UILabel alloc] init];
    _codeLabel.font = [UIFont boldSystemFontOfSize:15];
    _codeLabel.textColor = UIColor.blackColor;
    [self addSubview:_codeLabel];
    
    //默认关闭
    _showCursor = NO;
}
- (void)setUiStyle:(CodeUIStyle)uiStyle {
    _uiStyle = uiStyle;
    if (_uiStyle == CodeUIStyle_Line) {
        _codeEdgeView.backgroundColor = UIColor.clearColor;
        _codeEdgeView.layer.borderColor = UIColor.whiteColor.CGColor;
    }else{
        _codeEdgeView.backgroundColor = RGB_COLOR(@"#f6f6f6", 1);
        _codeEdgeView.layer.borderColor = UIColor.clearColor.CGColor;
    }
}
- (void)setTextFont:(UIFont *)textFont {
    _textFont = textFont;
    _codeLabel.font = _textFont;
}
- (void)setTextCor:(UIColor *)textCor {
    _textCor = textCor;
    _codeLabel.textColor = _textCor;
}
- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self updateCodeView];
}
-(void)updateCodeView {
    
    CGFloat edgeSize = MIN(self.frame.size.width, self.frame.size.height);
    CGFloat edgeX = (self.frame.size.width - edgeSize)/2;
    CGFloat edgeY = (self.frame.size.height - edgeSize)/2;
    self.codeEdgeView.frame = CGRectMake(edgeX, edgeY, edgeSize, edgeSize);
    
    CGFloat codeHeight = edgeSize *0.8;
    CGFloat codeX = (self.frame.size.width - self.codeLabel.frame.size.width) / 2.0;
    CGFloat codeY = (self.frame.size.height - codeHeight)/2;
    self.codeLabel.frame = CGRectMake(codeX, codeY, self.codeLabel.frame.size.width, codeHeight);
    
    [self updateCursorFrame];
}
- (void)setText:(NSString *)text {
    _text = text;
    NSLog(@"real:%@",text);
    if (_text.length > 0) {
//        _codeEdgeView.layer.borderColor = UIColor.redColor.CGColor;
    } else {
//        _codeEdgeView.layer.borderColor = UIColor.grayColor.CGColor;
    }
    // 密文处理
    if (_secureTextEntry) {
        if (text.length>0) {
            _codeLabel.text = text;
            [self refreshCodeLabel];
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (text.length>0) {
                _codeLabel.text = @"●";
            }else{
                _codeLabel.text = @"";
            }
            [self refreshCodeLabel];
        });
    }else{
        _codeLabel.text = text;
        [self refreshCodeLabel];
    }
}
-(void)refreshCodeLabel {
    [self.codeLabel sizeToFit];
    [self setNeedsLayout];
    [self layoutIfNeeded];
}
- (void)updateCursorFrame {
    CGFloat x = 0;
    if (self.codeLabel.frame.size.width <= 0) {
        x = (self.frame.size.width - 1.6) / 2.0;
    } else {
        x = CGRectGetMaxX(self.codeLabel.frame);
    }
    CGFloat curHeight = self.frame.size.height *0.45;
    CGFloat curY = (self.frame.size.height - curHeight)/2;
    //_cursor.frame = CGRectMake(x, 10, 1.6, self.frame.size.height - 20);
    _cursor.frame = CGRectMake(x, curY, 1.6, curHeight);
}

- (void)setShowCursor:(BOOL)showCursor {
    
    if (_showCursor == YES && showCursor == YES) { //重复开始, 那么,什么也不做
    } else if (_showCursor == YES && showCursor == NO) { //原来是开始的, 现在要求关闭, 那么,就关闭
        [_cursor removeFromSuperview];
    } else if (_showCursor == NO && showCursor == YES) { //原来是关闭, 现在要求开始, 那么, 开始
        _cursor = [[UIView alloc] init];
        _cursor.userInteractionEnabled = NO;
        _cursor.backgroundColor = Pink_Cor;
        [self addSubview:_cursor];
        [self updateCursorFrame];
        _cursor.alpha = 0;
        [self animationOne:_cursor];
    } else if (_showCursor == NO && showCursor == NO) { //重复关闭
        [_cursor removeFromSuperview];
    }
    _showCursor = showCursor;
}

// 光标效果
- (void)animationOne:(UIView *)aView {
    [UIView animateWithDuration:0.1 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
        aView.alpha = 1;
    } completion:^(BOOL finished) {
        if (self.showCursor) {
            [self performSelector:@selector(animationTwo:) withObject:aView afterDelay:0.5];
        }
    }];
}

- (void)animationTwo:(UIView *)aView {
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        aView.alpha = 0;
    } completion:^(BOOL finished) {
        if (self.showCursor) {
            [self performSelector:@selector(animationOne:) withObject:aView afterDelay:0.1];
        }
    }];
}


@end
