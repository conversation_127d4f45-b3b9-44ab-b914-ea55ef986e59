//
//  YBYoungSetVC.m
//  YBVideo
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBYoungSetVC.h"
#import "RKCodeInputView.h"

@interface YBYoungSetVC ()

@property(nonatomic,strong)RKCodeInputView *codeInputView;  /// 输入框


@end

@implementation YBYoungSetVC

- (UIStatusBarStyle)preferredStatusBarStyle {
    if (@available(iOS 13.0,*)) {
        return UIStatusBarStyleDarkContent;
    }
    return UIStatusBarStyleDefault;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.titleL.textColor = RGB_COLOR(@"#323232", 1);
    [self.leftBtn setImage:[UIImage imageNamed:@"pubBlack_back"] forState:0];
    self.subNavi.backgroundColor = UIColor.whiteColor;
    self.view.backgroundColor = UIColor.whiteColor;
    
    UILabel *topTitleL = [[UILabel alloc]init];
    topTitleL.font = [UIFont boldSystemFontOfSize:18];
    topTitleL.textColor = RGB_COLOR(@"#323232", 1);
    topTitleL.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:topTitleL];
    [topTitleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.height.mas_equalTo(30);
        make.top.equalTo(self.naviView.mas_bottom).offset(20);
    }];
    
    _codeInputView = [[RKCodeInputView alloc]init];
    _codeInputView.contentView.backgroundColor = UIColor.whiteColor;
    _codeInputView.secureTextEntry = YES;
    _codeInputView.becomeFirstRes = YES;
    [self.view addSubview:_codeInputView];
    [_codeInputView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.view.mas_width).multipliedBy(0.6);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(40);
        make.top.equalTo(topTitleL.mas_bottom).offset(15);
    }];
    YBWeakSelf;
    _codeInputView.finishEvent = ^{
        //NSLog(@"input:%@",weakSelf.codeInputView.codeText);
        [weakSelf inputFinish];
    };
    // 更新布局
    [self.view layoutIfNeeded];
    [_codeInputView updateSubViews];
    
    if (_youngMode == YoungMode_First) {
        // 第一次
        self.titleL.text = YZMsg(@"设置密码");
        topTitleL.text = YZMsg(@"请设置新密码");
    }else{
        // 开启-关闭
        self.titleL.text = YZMsg(@"输入密码");
        topTitleL.text = YZMsg(@"请输入密码");
    }
}

-(void)inputFinish {
    int type = 0;
    int youngSwitch = 0;
    if (_youngMode == YoungMode_First) {
        // 第一次
        type = 1;
        youngSwitch = 1;
    }else if (_youngMode == YoungMode_FirstReset){
        // 首次设置后的重设
        type = 5;
        youngSwitch = 1;
    }else if (_youngMode == YoungMode_Open){
        // 开启
        type = 2;
        youngSwitch = 1;
    }else{
        // 关闭
        type = 3;
        youngSwitch = 0;
    }
    /**
     * type(1设置密码并开启 2开启青少年模式 3关闭青少年模式 4修改密码 5重新设置密码)
     * pwd当前密码 newpwd 修改新密码 newspwd 确认新密码
     */
    NSDictionary *postDic = @{
        @"type":@(type),
        @"pwd":_codeInputView.codeText,
    };
    YBWeakSelf;
    [YBNetworking postWithUrl:@"User.setTeenagers" Dic:postDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            /// isfirst isuser  msg
            NSDictionary *infoDic = [info firstObject];
            int isfirst = [minstr([infoDic valueForKey:@"isfirst"]) intValue];
            /// 是首次,提示是否要重设
            if (isfirst == 1) {
                [weakSelf showFirstAlertMsg:msg andUseinfo:infoDic];
            }else{
                [MBProgressHUD showError:msg];
                [weakSelf goRootVCYoungSwitch:youngSwitch useInfo:infoDic];
            }
        }else{
            [MBProgressHUD showError:msg];
            [_codeInputView clearText];
        }
    } Fail:^(id fail) {
    }];
}
-(void)showFirstAlertMsg:(NSString *)alertStr andUseinfo:(NSDictionary *)useInfo {
    NSDictionary *contentDic = @{
        @"title":YZMsg(@"提示"),
        @"msg":alertStr,
        @"left":YZMsg(@"重新设置"),
        @"right":YZMsg(@"确定"),
    };
    YBWeakSelf;
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (eventType == 0) {
                // 重新设置
                _youngMode = YoungMode_FirstReset;
                [_codeInputView clearText];
            }else{
                // 确定
                [weakSelf goRootVCYoungSwitch:1 useInfo:useInfo];
            }
        });
    }];
}

-(void)goRootVCYoungSwitch:(int)youngSwitch useInfo:(NSDictionary *)useInfo {
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[YBYoungManager shareInstance] changeYoungSwitch:youngSwitch youngInfo:useInfo];
        
        [self.navigationController popToRootViewControllerAnimated:YES];
        YBTabBarController *tabbar = [PublicObj currentTabbar];
        [tabbar selectController:0];
        [tabbar goForYouViewRefresh:YES];
    });
}
@end
