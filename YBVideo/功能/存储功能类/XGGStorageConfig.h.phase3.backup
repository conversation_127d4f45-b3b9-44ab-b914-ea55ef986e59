//
//  StorageConfig.h
//  YBVideo
//
//  Created by YB007 on 2019/12/7.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface StorageConfig : NSObject

/** 时间戳 */
+(void)saveResTime:(NSDate *)save;
+(NSDate *)getResTime;
+(void)clearResTime;

/** 存储类型 */
+(void)saveStorageType:(NSString *)save;
+(NSString *)getStorageType;

/** 七牛相关 */
+(void)saveQNZone:(NSString *)save;
+(NSString *)getQNZone;

+(void)saveQNToken:(NSString *)save;
+(NSString*)getQNToken;

+(void)saveQNDomain:(NSString *)save;
+(NSString *)getQNDomain;

/** 腾讯相关 */
+(void)saveTXRegion:(NSString *)save;
+(NSString *)getTXRegion;

+(void)saveTXBucket:(NSString *)save;
+(NSString *)getTXBucket;

+(void)saveTXAppID:(NSString *)save;
+(NSString *)getTXAppID;

/** 亚马逊相关 */
//存储桶
+(void)saveAwsBucket:(NSString *)save;
+(NSString *)getAwsBucket;
//区域
+(void)saveAwsRegion:(NSString *)save;
+(NSString *)getAwsRegion;
//标识
+(void)saveAwsIdentityPoolid:(NSString *)save;
+(NSString *)getAwsIdentityPoolid;

@end


