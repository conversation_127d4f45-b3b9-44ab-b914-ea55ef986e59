//
//  YBStorageObj.h
//  YBVideo
//
//  Created by YB007 on 2019/11/22.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void (^YBStoragePercentBlock)(CGFloat percent);

/** 存储类型:(七牛、腾讯) */
typedef void (^YBStorageTypeBlock)(int code);

/** 存储完成，code 0 成功 -1失败 key：存储后的链接 */
typedef void (^YBStorageFinishaBlock)(int code, NSString *key);

@interface YBStorageObj : NSObject

+(instancetype)storageManeger;

#pragma mark - 第一步:获取存储类型(tx 、qiniu)
-(void)getCOSType:(YBStorageTypeBlock)complete;

#pragma mark - 第二步:开始上传(tx 、qiniu)
//图片
-(void)yb_storageImg:(UIImage *)img andName:(NSString *)imgName progress:(YBStoragePercentBlock)ybprogress complete:(YBStorageFinishaBlock)complete;

//音、视频
-(void)yb_storageVideoOrVoice:(NSString *)filePath andName:(NSString *)fileName progress:(YBStoragePercentBlock)ybprogress complete:(YBStorageFinishaBlock)complete;
@end


