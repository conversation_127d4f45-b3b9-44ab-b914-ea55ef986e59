//
//  StorageConfig.m
//  YBVideo
//
//  Created by YB007 on 2019/12/7.
//  Copyright © 2019 cat. All rights reserved.
//

#import "StorageConfig.h"

@implementation StorageConfig


#pragma mark - 时间戳
+(void)saveResTime:(NSDate *)save; {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_savetime"];
    [userDefaults synchronize];
}
+(NSDate *)getResTime {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSDate *getDate = [userDefaults objectForKey:@"ybcos_savetime"];
    return getDate;
}
+(void)clearResTime {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults removeObjectForKey:@"ybcos_savetime"];
    [userDefaults synchronize];
}

#pragma mark - /** 存储类型 */
+(void)saveStorageType:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_type"];
    [userDefaults synchronize];
}
+(NSString *)getStorageType {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_type"];
    return getStr;
}

#pragma mark - /** 七牛相关 */
+(void)saveQNZone:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_qn_zone"];
    [userDefaults synchronize];
}
+(NSString *)getQNZone {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_qn_zone"];
    return getStr;
}
+(void)saveQNToken:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_qn_token"];
    [userDefaults synchronize];
}
+(NSString*)getQNToken {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_qn_token"];
    return getStr;
}
+(void)saveQNDomain:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_qn_domain"];
    [userDefaults synchronize];
}
+(NSString *)getQNDomain {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_qn_domain"];
    return getStr;
}

#pragma mark - /** 腾讯相关 */
+(void)saveTXRegion:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_tx_region"];
    [userDefaults synchronize];
}
+(NSString *)getTXRegion {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_tx_region"];
    return getStr;
}
+(void)saveTXBucket:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_tx_bucket"];
    [userDefaults synchronize];
}
+(NSString *)getTXBucket {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_tx_bucket"];
    return getStr;
}
+(void)saveTXAppID:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_tx_appid"];
    [userDefaults synchronize];
}
+(NSString *)getTXAppID {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_tx_appid"];
    return getStr;
}
#pragma mark -/** 亚马逊相关 */
+(void)saveAwsBucket:(NSString *)save;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_aws_bucket"];
    [userDefaults synchronize];
}
+(NSString *)getAwsBucket;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_aws_bucket"];
    return getStr;
}

+(void)saveAwsRegion:(NSString *)save {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_aws_region"];
    [userDefaults synchronize];
}
+(NSString *)getAwsRegion;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_aws_region"];
    return getStr;
}
+(void)saveAwsIdentityPoolid:(NSString *)save;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:save forKey:@"ybcos_aws_identitypoolid"];
    [userDefaults synchronize];
}
+(NSString *)getAwsIdentityPoolid;{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *getStr = [userDefaults objectForKey:@"ybcos_aws_identitypoolid"];
    return getStr;
}

@end
