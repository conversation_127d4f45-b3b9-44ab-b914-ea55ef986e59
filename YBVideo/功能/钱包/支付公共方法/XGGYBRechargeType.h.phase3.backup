//
//  YBRechargeType.h
//  YBVideo
//
//  Created by YB007 on 2019/8/22.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>


typedef NS_ENUM(NSInteger,RechargeType) {
    rechargeType_Vip,
    rechargeType_coin,
    rechargeType_Goods,
};
/**
 *  stateCode 状态0成功-1失败
 *  payType 0微信 、1支付宝  2、苹果
 *  msg 提示信息
 */

typedef void (^RechargeTypeBlock)(int stateCode ,int payType,NSString *msg);


@interface YBRechargeType : NSObject

+(instancetype)chargeManeger;

@property(nonatomic,copy)RechargeTypeBlock payEvent;

/**
 * 注意
 * 在支付页面的 viewdidload 方法注册监听
 * 在支付页面 dealloc 方法(或者返回方法中)移除通知
 */
-(void)addPayNotice;
-(void)removePayNotice;

//微信
@property(nonatomic,strong)NSString *wechatAppID;
/**
 *  url 获取订单地址
 *  parameter  获取订单所需参数
 */
-(void)selWechatPayUrl:(NSString *)url andParameter:(NSDictionary *)parameter complete:(RechargeTypeBlock)complete;


//支付宝
@property(nonatomic,strong)NSString *aliPayKey;
@property(nonatomic,strong)NSString *aliPayPartner;
@property(nonatomic,strong)NSString *aliPaySellerID;

@property(nonatomic,strong)NSString *aliCallBackUrl;     //阿里回调地址
/**
 *  url 获取订单地址
 *  parameter  获取订单所需参数
 */
-(void)selAliPayUrl:(NSString *)url andParameter:(NSDictionary *)parameter complete:(RechargeTypeBlock)comlete;


//苹果内购
@property(nonatomic,strong)NSString *productID;         //内购产品ID
@property(nonatomic,strong)NSString *appleCallBackUrl;  //苹果回调地址
/**
 *  url 获取订单地址
 *  parameter  获取订单所需参数
 */
-(void)selApplePayUrl:(NSString *)url andParameter:(NSDictionary *)parameter complete:(RechargeTypeBlock)complete;

//PayPal支付

-(void)selPayPalAndPatameter:(NSDictionary *)parameter rechargeType:(RechargeType)type complete:(RechargeTypeBlock)complete;

////paypal购买商品
//-(void)selPayPalBuyGoods:(NSDictionary *)goodsDic rechargeType:(RechargeType)type complete:(RechargeTypeBlock)complete;
@end


