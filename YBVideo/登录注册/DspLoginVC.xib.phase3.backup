<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="DspLoginVC">
            <connections>
                <outlet property="codeBtn" destination="HV8-6l-Ndk" id="gam-pP-9so"/>
                <outlet property="codeDesL" destination="6yb-Vh-5iX" id="vU6-Yv-gge"/>
                <outlet property="codeTF" destination="hqo-G8-36O" id="JiB-Qj-khD"/>
                <outlet property="countryBtn" destination="C0G-ue-GKn" id="xXH-HA-xKA"/>
                <outlet property="logReturnBtn" destination="Kpd-EQ-mqc" id="uOF-GZ-fEA"/>
                <outlet property="loginBtn" destination="kgZ-gX-EGo" id="Lcr-SK-jFz"/>
                <outlet property="midTitleL" destination="TK0-Ao-OXy" id="saB-TP-jo5"/>
                <outlet property="otherView" destination="TxE-lI-wcU" id="zW3-p3-VOR"/>
                <outlet property="phoneTF" destination="dUJ-ne-66g" id="rfy-Rs-TOo"/>
                <outlet property="platformView" destination="w0k-Bb-Vi8" id="uXb-pC-1DT"/>
                <outlet property="privateL" destination="Lx4-Uo-cy9" id="zbI-Od-bcG"/>
                <outlet property="topSpace" destination="fyI-Ww-xx8" id="Cvz-bX-IFy"/>
                <outlet property="topTitleL" destination="d5h-pJ-pKl" id="p7g-Ha-gUO"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
                <outlet property="xyBtn" destination="aVl-SL-scT" id="VC7-jd-Xag"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Kpd-EQ-mqc" userLabel="返回">
                    <rect key="frame" x="5" y="25" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="40" id="7wB-UZ-IN7"/>
                        <constraint firstAttribute="height" constant="40" id="Wvg-FD-5B7"/>
                    </constraints>
                    <state key="normal" image="pub_back.png"/>
                    <connections>
                        <action selector="clickBackBtn:" destination="-1" eventType="touchUpInside" id="qU2-sQ-ZnG"/>
                    </connections>
                </button>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="注册登录后体验更多精彩瞬间" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="d5h-pJ-pKl">
                    <rect key="frame" x="0.0" y="119" width="375" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="8ba-Cl-DU0"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <color key="textColor" red="0.96862745100000003" green="0.95294117649999999" blue="0.96862745100000003" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4pU-5C-1XQ" userLabel="手机号组合">
                    <rect key="frame" x="37.5" y="170" width="300" height="60"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="C0G-ue-GKn" userLabel="国家代码">
                            <rect key="frame" x="5" y="0.0" width="45" height="60"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="45" id="iVx-xn-kIP"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="18"/>
                            <state key="normal" title="+86">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <connections>
                                <action selector="clickCountryBtn:" destination="-1" eventType="touchUpInside" id="bws-c8-PcC"/>
                            </connections>
                        </button>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="code_down.png" translatesAutoresizingMaskIntoConstraints="NO" id="R9j-os-l95" userLabel="下拉">
                            <rect key="frame" x="50" y="15" width="30" height="30"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="30" id="137-ry-Cxp"/>
                                <constraint firstAttribute="width" constant="30" id="T4R-gF-aWX"/>
                            </constraints>
                        </imageView>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="输入手机号" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="dUJ-ne-66g" userLabel="手机号" customClass="MyTextField">
                            <rect key="frame" x="95" y="0.0" width="205" height="60"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" pointSize="18"/>
                            <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                        </textField>
                    </subviews>
                    <color key="backgroundColor" red="0.11372549019607843" green="0.098039215686274508" blue="0.18431372549019609" alpha="0.5" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="dUJ-ne-66g" firstAttribute="height" secondItem="4pU-5C-1XQ" secondAttribute="height" id="3H9-i0-14k"/>
                        <constraint firstAttribute="trailing" secondItem="dUJ-ne-66g" secondAttribute="trailing" id="7J5-od-Y6f"/>
                        <constraint firstItem="C0G-ue-GKn" firstAttribute="top" secondItem="4pU-5C-1XQ" secondAttribute="top" id="84f-lw-M0w"/>
                        <constraint firstItem="C0G-ue-GKn" firstAttribute="height" secondItem="4pU-5C-1XQ" secondAttribute="height" id="HxM-MX-lHb"/>
                        <constraint firstItem="dUJ-ne-66g" firstAttribute="centerY" secondItem="4pU-5C-1XQ" secondAttribute="centerY" id="JN7-ev-ysj"/>
                        <constraint firstAttribute="height" constant="60" id="OjD-pP-h6w"/>
                        <constraint firstItem="C0G-ue-GKn" firstAttribute="leading" secondItem="4pU-5C-1XQ" secondAttribute="leading" constant="5" id="ajJ-co-OxR"/>
                        <constraint firstItem="R9j-os-l95" firstAttribute="leading" secondItem="C0G-ue-GKn" secondAttribute="trailing" id="mZk-7E-Lar"/>
                        <constraint firstItem="dUJ-ne-66g" firstAttribute="leading" secondItem="R9j-os-l95" secondAttribute="trailing" constant="15" id="r7n-VO-ilb"/>
                        <constraint firstItem="R9j-os-l95" firstAttribute="centerY" secondItem="C0G-ue-GKn" secondAttribute="centerY" id="tUD-Sd-rDF"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="5"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sni-0u-nM9" userLabel="验证码组合">
                    <rect key="frame" x="37.5" y="235" width="180" height="60"/>
                    <subviews>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="输入验证码" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="hqo-G8-36O" userLabel="验证码" customClass="MyTextField">
                            <rect key="frame" x="10" y="0.0" width="170" height="60"/>
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" pointSize="18"/>
                            <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                        </textField>
                    </subviews>
                    <color key="backgroundColor" red="0.1137254902" green="0.098039215690000001" blue="0.1843137255" alpha="0.5" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="hqo-G8-36O" firstAttribute="centerY" secondItem="Sni-0u-nM9" secondAttribute="centerY" id="PsL-ER-QGv"/>
                        <constraint firstAttribute="trailing" secondItem="hqo-G8-36O" secondAttribute="trailing" id="Q6g-Hs-pqT"/>
                        <constraint firstItem="hqo-G8-36O" firstAttribute="leading" secondItem="Sni-0u-nM9" secondAttribute="leading" constant="10" id="jEh-0M-Mj3"/>
                        <constraint firstItem="hqo-G8-36O" firstAttribute="height" secondItem="Sni-0u-nM9" secondAttribute="height" id="qSA-06-2Mv"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="5"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HV8-6l-Ndk" userLabel="获取">
                    <rect key="frame" x="222.5" y="235" width="115" height="60"/>
                    <color key="backgroundColor" red="0.1137254902" green="0.098039215690000001" blue="0.1843137255" alpha="0.5" colorSpace="calibratedRGB"/>
                    <fontDescription key="fontDescription" type="system" pointSize="18"/>
                    <state key="normal" title="获取验证码">
                        <color key="titleColor" red="0.70588235294117641" green="0.70588235294117641" blue="0.70588235294117641" alpha="1" colorSpace="calibratedRGB"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="5"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="clickGetCodeBtn:" destination="-1" eventType="touchUpInside" id="iav-j2-CrQ"/>
                    </connections>
                </button>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="* 短信验证保障账户安全的同时短信费用将由平台支付" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6yb-Vh-5iX" userLabel="说明">
                    <rect key="frame" x="37.5" y="305" width="300" height="12"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="10"/>
                    <color key="textColor" red="0.71764705882352942" green="0.70196078431372544" blue="0.70196078431372544" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kgZ-gX-EGo" userLabel="登录">
                    <rect key="frame" x="37.5" y="351" width="300" height="50"/>
                    <color key="backgroundColor" red="0.91764705882352937" green="0.21568627450980393" blue="0.49803921568627452" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="50" id="g94-cn-Mvu"/>
                    </constraints>
                    <state key="normal" title="立即登录"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="5"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="clickPhoneLogin:" destination="-1" eventType="touchUpInside" id="HeO-gm-OwK"/>
                    </connections>
                </button>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TxE-lI-wcU" userLabel="使用登录方式组合">
                    <rect key="frame" x="0.0" y="475.5" width="375" height="50"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="其他登录方式" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TK0-Ao-OXy" userLabel="中标题">
                            <rect key="frame" x="118" y="16" width="139" height="18"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="139" id="8zw-5o-Ssz"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                            <color key="textColor" red="0.93725490199999995" green="0.93725490199999995" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ALW-SN-XuI" userLabel="左线">
                            <rect key="frame" x="20" y="24.5" width="78" height="1"/>
                            <color key="backgroundColor" red="0.43529411759999997" green="0.43529411759999997" blue="0.47450980390000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" identifier="1" id="Tda-bh-YUY"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SXf-xg-PwP" userLabel="右线">
                            <rect key="frame" x="277" y="24.5" width="78" height="1"/>
                            <color key="backgroundColor" red="0.43529411759999997" green="0.43529411759999997" blue="0.47450980390000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="50" id="7Bw-Ve-mzE"/>
                        <constraint firstItem="SXf-xg-PwP" firstAttribute="height" secondItem="ALW-SN-XuI" secondAttribute="height" id="TUR-iJ-7ES"/>
                        <constraint firstItem="ALW-SN-XuI" firstAttribute="leading" secondItem="TxE-lI-wcU" secondAttribute="leading" constant="20" id="Vtd-3N-scV"/>
                        <constraint firstItem="ALW-SN-XuI" firstAttribute="trailing" secondItem="TK0-Ao-OXy" secondAttribute="leading" constant="-20" id="Wcm-ND-oDK"/>
                        <constraint firstItem="SXf-xg-PwP" firstAttribute="centerY" secondItem="ALW-SN-XuI" secondAttribute="centerY" id="djB-kK-p9V"/>
                        <constraint firstItem="TK0-Ao-OXy" firstAttribute="centerY" secondItem="TxE-lI-wcU" secondAttribute="centerY" id="dwA-DN-nvQ"/>
                        <constraint firstItem="TK0-Ao-OXy" firstAttribute="centerX" secondItem="TxE-lI-wcU" secondAttribute="centerX" id="kLB-Va-I7A"/>
                        <constraint firstItem="SXf-xg-PwP" firstAttribute="width" secondItem="ALW-SN-XuI" secondAttribute="width" id="obf-zr-okH"/>
                        <constraint firstItem="ALW-SN-XuI" firstAttribute="centerY" secondItem="TK0-Ao-OXy" secondAttribute="centerY" id="vPp-h9-13H"/>
                        <constraint firstAttribute="trailing" secondItem="SXf-xg-PwP" secondAttribute="trailing" constant="20" id="xd5-EE-Xrj"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="w0k-Bb-Vi8" userLabel="登录平台">
                    <rect key="frame" x="0.0" y="530.5" width="375" height="60"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="60" id="DJJ-3O-8ma"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Lx4-Uo-cy9" userLabel="隐私+" customClass="YYLabel">
                    <rect key="frame" x="65.5" y="595.5" width="244" height="50"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="50" id="xyy-u7-Fzl"/>
                    </constraints>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aVl-SL-scT">
                    <rect key="frame" x="49.5" y="614.5" width="12" height="12"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="12" id="F8v-WD-fLo"/>
                        <constraint firstAttribute="height" constant="12" id="mKA-bI-huB"/>
                    </constraints>
                    <state key="normal" image="xieyi.png"/>
                    <connections>
                        <action selector="protolBtnClick:" destination="-1" eventType="touchUpInside" id="5fs-x1-8cv"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.066666666666666666" green="0.050980392156862744" blue="0.14117647058823529" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="d5h-pJ-pKl" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" id="023-mf-0sN"/>
                <constraint firstItem="6yb-Vh-5iX" firstAttribute="top" secondItem="Sni-0u-nM9" secondAttribute="bottom" constant="10" id="0LN-Di-1Pz"/>
                <constraint firstItem="Sni-0u-nM9" firstAttribute="width" secondItem="4pU-5C-1XQ" secondAttribute="width" multiplier="0.6" id="3h2-x0-FVt"/>
                <constraint firstItem="aVl-SL-scT" firstAttribute="centerY" secondItem="Lx4-Uo-cy9" secondAttribute="centerY" id="3hw-f9-0Zt"/>
                <constraint firstItem="kgZ-gX-EGo" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="6K3-4I-giP"/>
                <constraint firstItem="HV8-6l-Ndk" firstAttribute="centerY" secondItem="Sni-0u-nM9" secondAttribute="centerY" id="C6S-B4-7Mo"/>
                <constraint firstItem="6yb-Vh-5iX" firstAttribute="trailing" secondItem="HV8-6l-Ndk" secondAttribute="trailing" id="CKz-a1-gKR"/>
                <constraint firstItem="HV8-6l-Ndk" firstAttribute="trailing" secondItem="4pU-5C-1XQ" secondAttribute="trailing" id="CUN-Ho-FTJ"/>
                <constraint firstItem="TxE-lI-wcU" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="EVP-9K-1WE"/>
                <constraint firstItem="4pU-5C-1XQ" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" multiplier="0.6" id="Fjl-Mh-pCO"/>
                <constraint firstItem="Lx4-Uo-cy9" firstAttribute="top" secondItem="w0k-Bb-Vi8" secondAttribute="bottom" constant="5" id="G7B-9e-2Xq"/>
                <constraint firstItem="w0k-Bb-Vi8" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="KGD-Eh-DXV"/>
                <constraint firstItem="Lx4-Uo-cy9" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" multiplier="0.65" id="LHU-On-kD7"/>
                <constraint firstItem="HV8-6l-Ndk" firstAttribute="height" secondItem="Sni-0u-nM9" secondAttribute="height" id="Lwl-dQ-HyJ"/>
                <constraint firstItem="Lx4-Uo-cy9" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="OFK-jz-eNv"/>
                <constraint firstItem="Lx4-Uo-cy9" firstAttribute="leading" secondItem="aVl-SL-scT" secondAttribute="trailing" constant="4" id="RMV-li-QRh"/>
                <constraint firstItem="Sni-0u-nM9" firstAttribute="top" secondItem="4pU-5C-1XQ" secondAttribute="bottom" constant="5" id="Upo-di-0fB"/>
                <constraint firstItem="6yb-Vh-5iX" firstAttribute="leading" secondItem="Sni-0u-nM9" secondAttribute="leading" id="WHx-GL-v4G"/>
                <constraint firstItem="kgZ-gX-EGo" firstAttribute="top" secondItem="Sni-0u-nM9" secondAttribute="bottom" constant="56" id="bDQ-4B-Ow5"/>
                <constraint firstItem="4pU-5C-1XQ" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="fKa-G6-AXf"/>
                <constraint firstItem="Kpd-EQ-mqc" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" constant="25" id="fyI-Ww-xx8"/>
                <constraint firstItem="d5h-pJ-pKl" firstAttribute="bottom" secondItem="4pU-5C-1XQ" secondAttribute="top" constant="-30" id="h0d-LO-9UX"/>
                <constraint firstItem="4pU-5C-1XQ" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" multiplier="0.8" id="hl8-W0-b2b"/>
                <constraint firstItem="TxE-lI-wcU" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" id="iwZ-F3-icc"/>
                <constraint firstItem="w0k-Bb-Vi8" firstAttribute="top" secondItem="TxE-lI-wcU" secondAttribute="bottom" constant="5" id="jfO-ee-i5b"/>
                <constraint firstItem="Kpd-EQ-mqc" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" constant="5" id="mNS-vx-zwl"/>
                <constraint firstItem="d5h-pJ-pKl" firstAttribute="centerX" secondItem="4pU-5C-1XQ" secondAttribute="centerX" id="n2b-Sg-jU2"/>
                <constraint firstItem="HV8-6l-Ndk" firstAttribute="leading" secondItem="Sni-0u-nM9" secondAttribute="trailing" constant="5" id="ns1-s6-xjg"/>
                <constraint firstItem="w0k-Bb-Vi8" firstAttribute="width" secondItem="TxE-lI-wcU" secondAttribute="width" id="ogY-Zo-Vgz"/>
                <constraint firstItem="kgZ-gX-EGo" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" multiplier="0.8" id="pUO-2K-ycb"/>
                <constraint firstItem="Sni-0u-nM9" firstAttribute="leading" secondItem="4pU-5C-1XQ" secondAttribute="leading" id="rTi-ms-ask"/>
                <constraint firstItem="TxE-lI-wcU" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" multiplier="1.5" id="tvk-0A-M95"/>
                <constraint firstItem="Sni-0u-nM9" firstAttribute="height" secondItem="4pU-5C-1XQ" secondAttribute="height" id="wS3-Al-nf3"/>
            </constraints>
            <point key="canvasLocation" x="52" y="-37.331334332833585"/>
        </view>
    </objects>
    <resources>
        <image name="code_down.png" width="20" height="20"/>
        <image name="pub_back.png" width="20" height="20"/>
        <image name="xieyi.png" width="11" height="11"/>
    </resources>
</document>
