//
//  DspLoginVC.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/8.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "MyTextField.h"
#import "YBBaseViewController.h"
#import <YYText/YYText.h>

@interface DspLoginVC : YBBaseViewController

@property(nonatomic,assign)BOOL isOverdue;
@property(nonatomic,assign)BOOL showAlert;
/** 国家代码按钮（预留） */
@property (weak, nonatomic) IBOutlet UIButton *countryBtn;
/** 国家代码点击（预留） */
- (IBAction)clickCountryBtn:(UIButton *)sender;

@property (weak, nonatomic) IBOutlet UIButton *logReturnBtn;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topSpace;


@property (weak, nonatomic) IBOutlet MyTextField *phoneTF;
@property (weak, nonatomic) IBOutlet MyTextField *codeTF;

@property (weak, nonatomic) IBOutlet UIButton *codeBtn;
@property (weak, nonatomic) IBOutlet UIView *platformView;
@property (weak, nonatomic) IBOutlet UIView *otherView;

@property (nonatomic,strong)NSString *youke;
@property (weak, nonatomic) IBOutlet YYLabel *privateL;

@property (weak, nonatomic) IBOutlet UILabel *topTitleL;
@property (weak, nonatomic) IBOutlet UIButton *loginBtn;
@property (weak, nonatomic) IBOutlet UILabel *midTitleL;
@property (weak, nonatomic) IBOutlet UILabel *codeDesL;
@property (weak, nonatomic) IBOutlet UIButton *xyBtn;

- (IBAction)clickGetCodeBtn:(UIButton *)sender;
- (IBAction)clickPhoneLogin:(UIButton *)sender;

- (IBAction)clickBackBtn:(UIButton *)sender;


@end
