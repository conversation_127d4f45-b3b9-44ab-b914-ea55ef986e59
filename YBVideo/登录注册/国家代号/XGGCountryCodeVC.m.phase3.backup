//
//  CountryCodeVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/11/11.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CountryCodeVC.h"
#import "YBSearchBarView.h"
@interface CountryCodeVC ()<UITableViewDelegate,UITableViewDataSource>
{
    BOOL _isSearch;
}
@property (nonatomic, strong)NSArray *listArray;
@property (nonatomic, strong)UITableView *listTable;
@property (nonatomic, strong)UITableView *searchTableView;

@property (nonatomic, strong)NSMutableArray *sortedArrForArrays;
@property (nonatomic, strong) NSMutableArray *sectionHeadsKeys;
@property (nonatomic, strong) NSArray *searchArr;
@property (nonatomic, strong) YBSearchBarView *searchView;
@end

@implementation CountryCodeVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.titleL.text = YZMsg(@"手机号归属地");
    
    _sortedArrForArrays = [NSMutableArray array];
    _sectionHeadsKeys = [[NSMutableArray alloc] init];      //initialize a array to hold keys like A,B,C ...
    _searchArr = [NSArray array];
    [self addSearchBar];
    _listArray = [NSArray array];
    _listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight+58, _window_width, _window_height-64-statusbarHeight-58)];
    _listTable.delegate = self;
    _listTable.dataSource = self;
    _listTable.tableFooterView = [[UIView alloc] init];
    _listTable.sectionIndexColor = [UIColor grayColor];
    _listTable.backgroundColor = Normal_BackColor;
    [_listTable setSeparatorColor:Line_Cor];
    [self.view addSubview:_listTable];

    _searchTableView = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight+58, _window_width, _window_height-64-statusbarHeight-58)];
    _searchTableView.delegate = self;
    _searchTableView.dataSource = self;
    _searchTableView.tableFooterView = [[UIView alloc] init];
    _searchTableView.backgroundColor = Normal_BackColor;
    [_searchTableView setSeparatorColor:Line_Cor];
    [self.view addSubview:_searchTableView];
    _searchTableView.hidden = YES;
    
    [self requstDatalist];

}
#pragma mark - 输入框
- (void)addSearchBar {
    YBWeakSelf;
    UIView *bg = [[UIView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, statusbarHeight+58)];
    bg.backgroundColor =Normal_BackColor;// RGBA(238,238,238,1);
    [self.view addSubview:bg];
    
    _searchView = [[YBSearchBarView alloc]initWithFrame:CGRectMake(0,9, _window_width,40)];
    _searchView.searchTF.frame = CGRectMake(26, 2, _window_width-26*2, 36);
    _searchView.searchTF.placeholder = YZMsg(@"请输入地区名");
    _searchView.searchTF.backgroundColor =Normal_SubColor;// [UIColor whiteColor];
    _searchView.searchTF.textColor = UIColor.whiteColor;
//    _searchView.cancleBtn.hidden = YES;
    _searchView.searchEvent = ^(RKSearchType searchType) {
        [weakSelf searchEevnt:searchType];
    };
    [bg addSubview:_searchView];
}


-(void)requstDatalist{
    NSDictionary *dic = @{@"field":@""};
    
    [YBNetworking postWithUrl:@"Login.getCountrys" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSArray *infos = info;
            _listArray = infos;
            for (NSDictionary *listdic in _listArray) {
                [self.sortedArrForArrays addObject:[listdic valueForKey:@"title"]];
            }
            [self.listTable reloadData];
        }

        } Fail:^(id fail) {
            
        }];

}
-(void)searchList:(NSString *)searchText{
    NSDictionary *dic = @{@"field":searchText};
    
    [YBNetworking postWithUrl:@"Login.getCountrys" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSArray *infos = info;
            _searchArr = infos;
            [self.searchTableView reloadData];
        }

        } Fail:^(id fail) {
            
        }];

}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (tableView == _listTable) {
        NSArray *list = [self.listArray[section] valueForKey:@"lists"];
        return list.count;
    }else{
        return _searchArr.count;
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (tableView == _listTable) {
        return self.listArray.count;
    }else{
        return 1;
    }
}
-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UIView *headView = [[UIView alloc]init];
    headView.frame = CGRectMake(0, 0, _window_width, 30);
    headView.backgroundColor = Normal_BackColor;//[UIColor whiteColor];
    
    UILabel *titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(12, 10, 100, 20);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor whiteColor];
    [headView addSubview:titleLb];
    titleLb.text =  [[self.listArray objectAtIndex:section] valueForKey:@"title"];
    
    [PublicObj lineViewWithFrame:CGRectMake(15, 29, _window_width-30, 1) andColor:Line_Cor andView:headView];
    return headView;
}
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    if (tableView == _listTable) {
        return 30;
    }else{
        return 0;
    }
}
- (NSArray *)sectionIndexTitlesForTableView:(UITableView *)tableView {
    if (tableView == _listTable) {
        return self.sortedArrForArrays;
    }else
        return nil;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == _listTable) {
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"ss"];
        
        NSArray *listArr = [[_listArray objectAtIndex:indexPath.section] valueForKey:@"lists"];
        cell.backgroundColor = Normal_SubColor;
        cell.textLabel.text = [NSString stringWithFormat:@"%@",[listArr[indexPath.row]valueForKey:@"name"]];//listArr[indexPath.row]valueForKey:@"tel"]
        if ([lagType isEqual:EN]) {
            cell.textLabel.text = [NSString stringWithFormat:@"%@",[listArr[indexPath.row]valueForKey:@"name_en"]];
        }
        cell.textLabel.font = [UIFont systemFontOfSize:12];
        cell.textLabel.textColor = [UIColor whiteColor];
        return cell;

    }else{
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"ss"];
        
        cell.backgroundColor = Normal_SubColor;
        cell.textLabel.text = [NSString stringWithFormat:@"%@",[_searchArr[indexPath.row]valueForKey:@"name"]];//,[_searchArr[indexPath.row]valueForKey:@"tel"]
        if ([lagType isEqual:EN]) {
            cell.textLabel.text = [NSString stringWithFormat:@"%@",[_searchArr[indexPath.row]valueForKey:@"name_en"]];
        }
        cell.textLabel.font = [UIFont systemFontOfSize:12];
        cell.textLabel.textColor = [UIColor whiteColor];
        return cell;

    }
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (tableView == _listTable) {

        NSArray *listArr = [[_listArray objectAtIndex:indexPath.section] valueForKey:@"lists"];
        if (self.codeEvent) {
            self.codeEvent(listArr[indexPath.row]);
        }

    }else{
        if (self.codeEvent) {
            self.codeEvent(_searchArr[indexPath.row]);
        }

    }
    [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
}

#pragma mark - 搜索事件 s
-(void)searchEevnt:(RKSearchType)searchType {
    if (searchType == RKSearch_Search) {
        //搜索
        [self searchList:_searchView.searchTF.text];
    }
    if (searchType == RKSearch_ValueChange) {

        //输入框改变
        NSString *allText = [_searchView.searchTF.text stringByReplacingOccurrencesOfString:@" " withString:@""];
        if (allText.length > 0) {
            _searchTableView.hidden = NO;
            _isSearch = YES;
            [self searchList:_searchView.searchTF.text];
        }else{
            _isSearch = NO;

            _searchTableView.hidden = YES;
            [self searchList:_searchView.searchTF.text];
        }
    }
    if (searchType == RKSearch_BeginEditing) {
        //开始编辑
        if (_searchView.searchTF.text.length>0) {
            _isSearch = YES;
            _searchTableView.hidden = NO;
        }else{
            _isSearch = NO;
            _searchTableView.hidden = YES;
        }
    }
}
-(void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [_searchView.searchTF resignFirstResponder];
}
@end
