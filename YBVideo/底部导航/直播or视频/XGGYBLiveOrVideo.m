//
//  YBLiveOrVideo.m
//  YBVideo
//
//  Created by YB007 on 2019/11/27.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBLiveOrVideo.h"
#import "TCVideoRecordViewController.h"
#import "YBLiveVC.h"
#import "PubH5.h"
#import "YBVipVC.h"
typedef NS_ENUM(NSInteger,YBSelType){
    TypeSel_Live,
    TypeSel_Video,
};

@implementation YBLiveOrVideo

- (void)awakeFromNib {
    [super awakeFromNib];
    
    _closeBottom.constant = ShowDiff+18;
    
//    [_bgView layoutIfNeeded];
//
//    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:_bgView.bounds  byRoundingCorners:UIRectCornerTopRight|UIRectCornerTopLeft cornerRadii:CGSizeMake(10, 10)];
//    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
//    maskLayer.frame = _bgView.bounds;
//    maskLayer.path = maskPath.CGPath;
//    _bgView.layer.mask = maskLayer;
//
//    _videoBtn = [PublicObj setUpImgDownText:_videoBtn space:8];
//    _liveBtn = [PublicObj setUpImgDownText:_liveBtn space:8];

}

+(instancetype)showLiveOrVideoSel {
    YBLiveOrVideo *lv = [[[NSBundle mainBundle]loadNibNamed:@"YBLiveOrVideo" owner:nil options:nil]objectAtIndex:0];
    
    [lv setupView];
    
    return lv;
}
-(void)setupView {
    self.frame = [UIScreen mainScreen].bounds;
    
    [_bgView layoutIfNeeded];
    
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:_bgView.bounds  byRoundingCorners:UIRectCornerTopRight|UIRectCornerTopLeft cornerRadii:CGSizeMake(10, 10)];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = _bgView.bounds;
    maskLayer.path = maskPath.CGPath;
    _bgView.layer.mask = maskLayer;
    
    [_videoBtn setTitle:YZMsg(@"发布视频") forState:0];
    [_liveBtn setTitle:YZMsg(@"开启直播") forState:0];
    
    _videoBtn = [PublicObj setUpImgDownText:_videoBtn space:8];
    _liveBtn = [PublicObj setUpImgDownText:_liveBtn space:8];
    
    [[UIApplication sharedApplication].delegate.window addSubview:self];
    [UIView animateWithDuration:0.3 animations:^{
        
    } completion:^(BOOL finished) {
        self.backgroundColor = RGB_COLOR(@"#000000", 0.3);
    }];
}
-(void)dismiss {
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}
- (IBAction)clickVideoBtn:(id)sender {
    [self dismiss];
    NSString *videoLimit = minstr([XGGPower getVideoStatus]);
    NSString *videoMsg = minstr([XGGPower getVideoMsg]);
    if ([videoLimit isEqual:@"1"]) {
        TCVideoRecordViewController *rVC = [[TCVideoRecordViewController alloc]init];
        rVC.recordType = RecordType_Normal;
        [[XGGAppDelegate sharedAppDelegate] pushViewController:rVC animated:YES];
    }else {
//        [self showAlert:TypeSel_Video andAlertMsg:videoMsg];
        //[MBProgressHUD showPop:videoMsg];
        NSString *vipSwitch = minstr([XGGPower getVipSwitch]);
       if ([vipSwitch isEqual:@"0"]) {
           NSDictionary *contentDic = @{@"title":@"",
                                       @"msg":videoMsg,
                                       @"left":@"",
                                       @"right":YZMsg(@"确定")};
           [YBAlertView showAlertView:contentDic complete:^(int eventType) {
           }];

       }else{
           [self showAlert:TypeSel_Video andAlertMsg:videoMsg];
       }
               //[MBProgressHUD showPop:videoMsg];
    }
}
- (IBAction)clickLiveBtn:(id)sender {
    [self dismiss];
    NSString *liveLimit = minstr([XGGPower getLiveStatus]);
    NSString *liveMsg = minstr([XGGPower getLiveMsg]);
    if ([liveLimit isEqual:@"1"]) {
        [MBProgressHUD showMessage:@""];
        [YBNetworking postWithUrl:@"Live.getLiveParameters" Dic:@{} Suc:^(int code, id info, NSString *msg) {
            [MBProgressHUD hideHUD];
            if (code == 0) {
                NSDictionary *infoDic = [info firstObject];
                YBLiveVC *liveVC = [[YBLiveVC alloc]init];
                liveVC.pushSettingDic = [infoDic valueForKey:@"ios"];
                
                // 是否禁止开播
                BOOL isBan = minstr([infoDic valueForKey:@"liveban_score"]);
                [XGGPower saveBanLiveStatus:isBan];
                NSString *banStr = minstr([infoDic valueForKey:@"liveban_msg"]);
                [XGGPower saveBanLiveMsg:banStr];
                
                [[XGGAppDelegate sharedAppDelegate]pushViewController:liveVC animated:YES];
            }else {
                [MBProgressHUD showPop:msg];
            }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];
        }];
    }else {
        [self showAlert:TypeSel_Live andAlertMsg:liveMsg];
        //[MBProgressHUD showPop:liveMsg];
    }
}

-(void)showAlert:(YBSelType)selType andAlertMsg:(NSString *)alertMsg; {
   
    NSDictionary *contentDic = @{@"title":@"",
                                @"msg":alertMsg,
                                @"left":YZMsg(@"取消"),
                                @"right":(selType == TypeSel_Live)?YZMsg(@"权限说明"):YZMsg(@"开通会员")};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            NSString *url = [NSString stringWithFormat:@"%@/appapi/liveauthority/index?uid=%@&token=%@",h5url,[Config getOwnID],[Config getOwnToken]];
            
            if (selType == TypeSel_Video) {
                //url = [NSString stringWithFormat:@"%@/Appapi/Auth/index?uid=%@&token=%@",h5url,[Config getOwnID],[Config getOwnToken]];;
                YBVipVC *vipVC = [[YBVipVC alloc]init];
                [[XGGAppDelegate sharedAppDelegate]pushViewController:vipVC animated:YES];
            }else {
                PubH5 *h5VC = [[PubH5 alloc]init];
                h5VC.url = url;
                [[XGGAppDelegate sharedAppDelegate]pushViewController:h5VC animated:YES];
            }
        }
    }];
}

- (IBAction)clickCloseBtn:(id)sender {
    [self dismiss];
}

@end
