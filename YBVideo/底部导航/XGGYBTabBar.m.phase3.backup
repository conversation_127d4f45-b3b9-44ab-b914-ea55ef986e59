//
//  YBTabBar.m
//  WaWaJiClient
//
//  Created by Rookie on 2017/11/15.
//  Copyright © 2017年 zego. All rights reserved.
//

#import "YBTabBar.h"

@interface YBTabBar()

@property (nonatomic,weak) UIView *addBgview;
@property (nonatomic, weak) UIButton *addButton;

@end

@implementation YBTabBar

- (instancetype)initWithFrame:(CGRect)frame{
    if(self=[super initWithFrame:frame]){
        
        UIButton *addButton = [UIButton buttonWithType:UIButtonTypeCustom];
        CGRect frame = addButton.frame;
        //frame.size = addButton.currentBackgroundImage.size;
        frame.size = CGSizeMake(40, 40);
        addButton.frame = frame;
        
        UITabBarAppearance *appearance = [[UITabBarAppearance alloc] init];
        [appearance configureWithOpaqueBackground]; // 不透明背景，可换为 configureWithDefaultBackground
        appearance.backgroundColor = Tab_bgc; // 这里设置背景色
        
        NSDictionary *normalAttr = @{
            NSForegroundColorAttributeName: RGB_COLOR(@"#999999", 1),
            NSFontAttributeName: [UIFont systemFontOfSize:20 weight:UIFontWeightBold]
        };
        NSDictionary *selectedAttr = @{
            NSForegroundColorAttributeName: [UIColor whiteColor],
            NSFontAttributeName: [UIFont systemFontOfSize:22 weight:UIFontWeightBold]
        };

        appearance.stackedLayoutAppearance.normal.titleTextAttributes = normalAttr;
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = selectedAttr;

        // 隐藏图标颜色
        appearance.stackedLayoutAppearance.normal.iconColor = [UIColor clearColor];
        appearance.stackedLayoutAppearance.selected.iconColor = [UIColor clearColor];
        
        // 移动标题位置，x/y 调整
        appearance.stackedLayoutAppearance.normal.titlePositionAdjustment = UIOffsetMake(0, -10);
        appearance.stackedLayoutAppearance.selected.titlePositionAdjustment = UIOffsetMake(0, -10);

        
        // 取消顶部阴影线
        appearance.shadowImage = nil;
        appearance.shadowColor = nil;
        self.standardAppearance = appearance;
        if (@available(iOS 15.0, *)) {
            self.scrollEdgeAppearance = appearance;
        }
        
        
        
        //方式一 图片无文字
        [addButton setBackgroundImage:[UIImage imageNamed:@"tab_center"] forState:UIControlStateNormal];
        [addButton setBackgroundImage:[UIImage imageNamed:@"tab_center"] forState:UIControlStateHighlighted];
        
        [addButton addTarget:self action:@selector(publishClick) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:addButton];
        self.addButton = addButton;
        
    }
    return self;
}
/**
 * 中间按钮点击事件
 */
- (void)publishClick{
    if ([_tabbarDelegate respondsToSelector:@selector(centerBtnDidClicked)]) {
        [_tabbarDelegate centerBtnDidClicked];
    }
}
- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGFloat width = self.frame.size.width;
    CGFloat height = self.frame.size.height - ShowDiff;
   
    /**
     *  单独设置中间的按钮
     */
    self.addButton.center = CGPointMake(width * 0.5, height * 0.5);
//    self.addBgview.frame = CGRectMake(width*2/5, 0, width/5, height);
    CGFloat buttonY = 0;
    CGFloat buttonW = width / 5;
    CGFloat buttonH = height;
    NSInteger index = 0;
    for (UIControl *button in self.subviews) {
        if (![button isKindOfClass:[UIControl class]] || button == self.addButton) continue;
        /** 中间空出 */
        CGFloat buttonX = buttonW * ((index > 1) ? (index + 1) : index);
        /** 中间不空 */
//        CGFloat buttonX = buttonW * index;
        button.frame = CGRectMake(buttonX, buttonY, buttonW, buttonH);
        
        index++;
    }
}


@end
