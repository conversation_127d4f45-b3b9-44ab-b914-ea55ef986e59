//
//  XGGNetworkManager.h
//  YBVideo
//
//  Created by XGG on 2024/01/01.
//  Copyright © 2024年 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 成功回调Block
typedef void(^XGGSuccessBlock)(int code, id _Nullable info, NSString *msg);
// 失败回调Block
typedef void(^XGGFailureBlock)(NSError *error);
// 上传进度回调Block
typedef void(^XGGProgressBlock)(NSProgress *progress);

@interface XGGNetworkManager : NSObject

+ (instancetype)sharedManager;

#pragma mark - GET请求
/**
 GET请求
 @param url 请求地址
 @param params 请求参数
 @param success 成功回调
 @param failure 失败回调
 */
+ (void)getWithURL:(NSString *)url
            params:(NSDictionary * _Nullable)params
           success:(XGGSuccessBlock)success
           failure:(XGGFailureBlock _Nullable)failure;

#pragma mark - POST请求
/**
 POST请求
 @param url 请求地址
 @param params 请求参数
 @param success 成功回调
 @param failure 失败回调
 */
+ (void)postWithURL:(NSString *)url
             params:(NSDictionary * _Nullable)params
            success:(XGGSuccessBlock)success
            failure:(XGGFailureBlock _Nullable)failure;

#pragma mark - 文件上传
/**
 文件上传
 @param url 上传地址
 @param params 请求参数
 @param fileData 文件数据
 @param fileName 文件名
 @param mimeType 文件类型
 @param progress 上传进度回调
 @param success 成功回调
 @param failure 失败回调
 */
+ (void)uploadWithURL:(NSString *)url
               params:(NSDictionary * _Nullable)params
             fileData:(NSData *)fileData
             fileName:(NSString *)fileName
             mimeType:(NSString *)mimeType
             progress:(XGGProgressBlock _Nullable)progress
              success:(XGGSuccessBlock)success
              failure:(XGGFailureBlock _Nullable)failure;

@end

