//
//  XGGNetworkManager.m
//  YBVideo
//
//  Created by XGG on 2024/01/01.
//  Copyright © 2024年 cat. All rights reserved.
//

#import "XGGNetworkManager.h"
#import <AFNetworking/AFNetworking.h>

// 请求超时时间
static const NSTimeInterval XGGRequestTimeoutInterval = 15.0;
// 最大重试次数
static const NSInteger XGGMaxRetryCount = 1;

@interface XGGNetworkManager ()

@property (nonatomic, strong) AFHTTPSessionManager *sessionManager;

@end

@implementation XGGNetworkManager

+ (instancetype)sharedManager {
    static XGGNetworkManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[XGGNetworkManager alloc] init];
    });
    return manager;
}

- (instancetype)init {
    if (self = [super init]) {
        [self setupSessionManager];
    }
    return self;
}

#pragma mark - Private Methods

- (void)setupSessionManager {
    self.sessionManager = [AFHTTPSessionManager manager];
    self.sessionManager.requestSerializer.timeoutInterval = XGGRequestTimeoutInterval;
    
    // 设置请求头
    [self setupCommonHeaders];
    
    // 设置响应序列化器
    self.sessionManager.responseSerializer = [AFJSONResponseSerializer serializer];
    self.sessionManager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/json", @"text/javascript", @"text/html", @"text/plain", nil];
}

- (void)setupCommonHeaders {
    AFHTTPRequestSerializer *requestSerializer = self.sessionManager.requestSerializer;
    [requestSerializer setValue:@"application/x-www-form-urlencoded; charset=UTF-8" forHTTPHeaderField:@"Content-Type"];
    NSString *userAgent = [NSString stringWithFormat:@"%@/%@ (%@; iOS %@; Scale/%0.2f)",
                          [[NSBundle mainBundle] infoDictionary][(__bridge NSString *)kCFBundleExecutableKey] ?: [[NSBundle mainBundle] infoDictionary][(__bridge NSString *)kCFBundleIdentifierKey],
                          [[NSBundle mainBundle] infoDictionary][@"CFBundleShortVersionString"] ?: [[NSBundle mainBundle] infoDictionary][(__bridge NSString *)kCFBundleVersionKey],
                          [[UIDevice currentDevice] model],
                          [[UIDevice currentDevice] systemVersion],
                          [[UIScreen mainScreen] scale]];
    [requestSerializer setValue:userAgent forHTTPHeaderField:@"User-Agent"];
    [requestSerializer setValue:@"application/json" forHTTPHeaderField:@"Accept"];
    [requestSerializer setValue:@"zh-Hans-CN;q=1.0" forHTTPHeaderField:@"Accept-Language"];
    [requestSerializer setValue:@"gzip, deflate" forHTTPHeaderField:@"Accept-Encoding"];
}

- (NSMutableDictionary *)buildRequestParams:(NSDictionary *)params {
    NSMutableDictionary *requestParams = [[NSMutableDictionary alloc] init];
    
    if (params) {
        [requestParams addEntriesFromDictionary:params];
    }
    
    // 添加公共参数
    if (![self checkNull:[Config getOwnID]]) {
        [requestParams addEntriesFromDictionary:@{
            @"uid": [Config getOwnID],
            @"token": [Config getOwnToken]
        }];
    }
    
    NSString *serLang = [YBLanguageTools serviceLang];
    [requestParams addEntriesFromDictionary:@{
        @"version_ios": [PublicObj getAppBuild],
        @"mobileid": [PublicObj getDeviceUUID],
        @"device": [PublicObj iphoneType],
        @"lang": serLang
    }];
    
    return requestParams;
}

- (BOOL)checkNull:(id)obj {
    if (!obj || [obj isKindOfClass:[NSNull class]] || ([obj isKindOfClass:[NSString class]] && [(NSString *)obj length] == 0)) {
        return YES;
    }
    return NO;
}

- (NSString *)showErrorInfoWithStatusCode:(NSInteger)statusCode {
    NSString *message = nil;
    switch (statusCode) {
        case 401:
            message = @"未授权，请重新登录！";
            break;
        case 403:
            message = @"禁止访问！";
            break;
        case 404:
            message = @"请求的资源不存在！";
            break;
        case 500:
            message = @"服务器异常！";
            break;
        case 502:
            message = @"服务器网关错误！";
            break;
        case 503:
            message = @"服务器暂时不可用！";
            break;
        case -1001:
            message = @"网络请求超时，请稍后重试！";
            break;
        case -1002:
            message = @"不支持的URL！";
            break;
        case -1003:
            message = @"未能找到指定的服务器！";
            break;
        case -1004:
            message = @"服务器连接失败！";
            break;
        case -1005:
            message = @"连接丢失，请稍后重试！";
            break;
        case -1009:
            message = @"互联网连接似乎是离线！";
            break;
        case -1012:
            message = @"操作无法完成！";
            break;
        default:
            message = @"网络请求发生未知错误，请稍后再试！";
            break;
    }
    return message;
}

- (void)handleResponse:(id)responseObject 
               success:(XGGSuccessBlock)success 
               failure:(XGGFailureBlock)failure 
                   url:(NSString *)url 
                params:(NSDictionary *)params {
    
    if (!responseObject) {
        if (failure) {
            NSError *error = [NSError errorWithDomain:@"XGGNetworkManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"响应数据为空"}];
            failure(error);
        }
        return;
    }
    
    NSNumber *ret = [responseObject valueForKey:@"ret"];
    if ([ret isEqualToNumber:@(200)]) {
        // 请求成功
        NSDictionary *data = [responseObject valueForKey:@"data"];
        id info = [data valueForKey:@"info"];
        int code = [[data valueForKey:@"code"] intValue];
        NSString *msg = [NSString stringWithFormat:@"%@", [data valueForKey:@"msg"] ?: @""];
        
        if (success) {
            success(code, info, msg);
        }
        
        // 处理token过期
        if (code == 700) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD hideHUD];
                [PublicObj tokenExpired:msg];
            });
        }
        
        NSLog(@"XGG网络请求成功 - URL: %@, 响应: %@", url, responseObject);
    } else {
        // 请求失败
        NSString *errorMsg = [responseObject valueForKey:@"msg"] ?: @"未知错误";
        NSString *failureMessage = [NSString stringWithFormat:@"接口错误: %@ - %@\n%@", ret, url, errorMsg];
        
        if (success) {
            success(999, @[], failureMessage);
        }
        
        NSLog(@"XGG网络请求失败 - URL: %@, 错误: %@", url, failureMessage);
    }
}

#pragma mark - Request Methods with Retry

- (void)executeGETRequest:(NSString *)url 
                   params:(NSDictionary *)params 
                  success:(XGGSuccessBlock)success 
                  failure:(XGGFailureBlock)failure 
               retryCount:(NSInteger)retryCount {
    
    NSString *baseUrl = url;
    NSMutableDictionary *requestParams = [self buildRequestParams:params];
    
    [self.sessionManager GET:baseUrl parameters:requestParams headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        [self handleResponse:responseObject success:success failure:failure url:url params:params];
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (retryCount > 0) {
            NSLog(@"XGG网络请求重试 - URL: %@, 剩余重试次数: %ld", url, (long)retryCount);
            [self executeGETRequest:url params:params success:success failure:failure retryCount:retryCount - 1];
        } else {
            NSString *errorMessage = [self showErrorInfoWithStatusCode:error.code];
            NSLog(@"XGG网络请求最终失败 - URL: %@, 错误: %@", url, errorMessage);
            
            if (failure) {
                NSError *customError = [NSError errorWithDomain:@"XGGNetworkManager" 
                                                           code:error.code 
                                                       userInfo:@{NSLocalizedDescriptionKey: errorMessage}];
                failure(customError);
            }
        }
    }];
}

- (void)executePOSTRequest:(NSString *)url 
                    params:(NSDictionary *)params 
                   success:(XGGSuccessBlock)success 
                   failure:(XGGFailureBlock)failure 
                retryCount:(NSInteger)retryCount {
    
    NSString *baseUrl = url;
    NSMutableDictionary *requestParams = [self buildRequestParams:params];
    
    [self.sessionManager POST:baseUrl parameters:requestParams headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        [self handleResponse:responseObject success:success failure:failure url:url params:params];
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (retryCount > 0) {
            NSLog(@"XGG网络请求重试 - URL: %@, 剩余重试次数: %ld", url, (long)retryCount);
            [self executePOSTRequest:url params:params success:success failure:failure retryCount:retryCount - 1];
        } else {
            NSString *errorMessage = [self showErrorInfoWithStatusCode:error.code];
            NSLog(@"XGG网络请求最终失败 - URL: %@, 错误: %@", url, errorMessage);
            
            if (failure) {
                NSError *customError = [NSError errorWithDomain:@"XGGNetworkManager" 
                                                           code:error.code 
                                                       userInfo:@{NSLocalizedDescriptionKey: errorMessage}];
                failure(customError);
            }
        }
    }];
}

- (void)executeUploadRequest:(NSString *)url 
                      params:(NSDictionary *)params 
                    fileData:(NSData *)fileData 
                    fileName:(NSString *)fileName 
                    mimeType:(NSString *)mimeType 
                    progress:(XGGProgressBlock)progress 
                     success:(XGGSuccessBlock)success 
                     failure:(XGGFailureBlock)failure 
                  retryCount:(NSInteger)retryCount {
    
    NSString *baseUrl = url;
    NSMutableDictionary *requestParams = [self buildRequestParams:params];
    
    [self.sessionManager POST:baseUrl parameters:requestParams headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        [formData appendPartWithFileData:fileData name:@"file" fileName:fileName mimeType:mimeType];
    } progress:^(NSProgress * _Nonnull uploadProgress) {
        if (progress) {
            dispatch_async(dispatch_get_main_queue(), ^{
                progress(uploadProgress);
            });
        }
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        [self handleResponse:responseObject success:success failure:failure url:url params:params];
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (retryCount > 0) {
            NSLog(@"XGG文件上传重试 - URL: %@, 剩余重试次数: %ld", url, (long)retryCount);
            [self executeUploadRequest:url params:params fileData:fileData fileName:fileName mimeType:mimeType progress:progress success:success failure:failure retryCount:retryCount - 1];
        } else {
            NSString *errorMessage = [self showErrorInfoWithStatusCode:error.code];
            NSLog(@"XGG文件上传最终失败 - URL: %@, 错误: %@", url, errorMessage);
            
            if (failure) {
                NSError *customError = [NSError errorWithDomain:@"XGGNetworkManager" 
                                                           code:error.code 
                                                       userInfo:@{NSLocalizedDescriptionKey: errorMessage}];
                failure(customError);
            }
        }
    }];
}

#pragma mark - Public Methods

+ (void)getWithURL:(NSString *)url 
            params:(NSDictionary *)params 
           success:(XGGSuccessBlock)success 
           failure:(XGGFailureBlock)failure {
    
    [[XGGNetworkManager sharedManager] executeGETRequest:url 
                                                  params:params 
                                                 success:success 
                                                 failure:failure 
                                              retryCount:XGGMaxRetryCount];
}

+ (void)postWithURL:(NSString *)url 
             params:(NSDictionary *)params 
            success:(XGGSuccessBlock)success 
            failure:(XGGFailureBlock)failure {
    
    [[XGGNetworkManager sharedManager] executePOSTRequest:url 
                                                   params:params 
                                                  success:success 
                                                  failure:failure 
                                               retryCount:XGGMaxRetryCount];
}

+ (void)uploadWithURL:(NSString *)url 
               params:(NSDictionary *)params 
             fileData:(NSData *)fileData 
             fileName:(NSString *)fileName 
             mimeType:(NSString *)mimeType 
             progress:(XGGProgressBlock)progress 
              success:(XGGSuccessBlock)success 
              failure:(XGGFailureBlock)failure {
    
    [[XGGNetworkManager sharedManager] executeUploadRequest:url 
                                                     params:params 
                                                   fileData:fileData 
                                                   fileName:fileName 
                                                   mimeType:mimeType 
                                                   progress:progress 
                                                    success:success 
                                                    failure:failure 
                                                 retryCount:XGGMaxRetryCount];
}

@end
