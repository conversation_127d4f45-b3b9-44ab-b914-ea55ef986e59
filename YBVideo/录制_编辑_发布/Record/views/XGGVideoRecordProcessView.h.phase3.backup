//
//  VideoRecordProcessView.h
//  TXLiteAVDemo

#import <UIKit/UIKit.h>

//普通拍摄最大、最小时长
static CGFloat normal_max = 15.0;
static CGFloat normal_min = 5.0;
//VIP拍摄最大时长
static CGFloat vip_max = 60.0;

//zl----待修改
static CGFloat MAX_RECORD_TIME = 15.0; //默认15
static CGFloat MIN_RECORD_TIME = 5.0;

@interface VideoRecordProcessView : UIView

-(void)updateMinTime:(CGFloat)minTime andMaxTime:(CGFloat)maxTime;

-(void)update:(CGFloat)progress;

-(void)pause;

-(void)prepareDeletePart;

-(void)cancelDelete;

-(void)comfirmDeletePart;

-(void)deleteAllPart;
@end
