//
//  VideoRecordProcessView.m
//  TXLiteAVDemo

#import "VideoRecordProcessView.h"
//#import "UIView+AdditionsX12.h"

#define VIEW_PAUSE_WIDTH 2

@implementation VideoRecordProcessView
{
    UIView *    _processView;
    UIView *    _deleteView;
    CGSize      _viewSize;
    NSMutableArray * _pauseViewList;
    UIView * _minimumView;
}

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColorFromRGB(0XBBBBBB);
        _viewSize = frame.size;
        _processView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, _viewSize.height)];
        _processView.backgroundColor = Pink_Cor;
        [self addSubview:_processView];
                
        _pauseViewList = [NSMutableArray array];
    }
    return self;
}
-(void)updateMinTime:(CGFloat)minTime andMaxTime:(CGFloat)maxTime {
    if (maxTime <= 0 ) {
        maxTime = 1;
    }
    if (!_minimumView) {
        _minimumView = [[UIView alloc] initWithFrame:CGRectMake(self.width * minTime / maxTime, 0, 3, self.frame.size.height)];
        _minimumView.backgroundColor = Pink_Cor;
        [self addSubview:_minimumView];
    }
    _minimumView.x = self.width * minTime / maxTime-1.5;
}
-(void)update:(CGFloat)progress
{
    _processView.frame = CGRectMake(0, 0, _viewSize.width * progress, _viewSize.height);
}

-(void)pause
{
    UIView *pauseView = [[UIView alloc] initWithFrame:CGRectMake(_processView.right - VIEW_PAUSE_WIDTH, _processView.y, VIEW_PAUSE_WIDTH, _processView.height)];
    pauseView.backgroundColor = [UIColor whiteColor];
    [_pauseViewList addObject:pauseView];
    [self addSubview:pauseView];
}

-(void)prepareDeletePart
{
    if (_pauseViewList.count == 0) {
        return;
    }
    UIView *lastPauseView = [_pauseViewList lastObject];
    UIView *beforeLastPauseView = nil;
    if (_pauseViewList.count > 1) {
        beforeLastPauseView = [_pauseViewList objectAtIndex:_pauseViewList.count - VIEW_PAUSE_WIDTH];
    }

    _deleteView = [[UIView alloc] initWithFrame:CGRectMake(beforeLastPauseView.right, _processView.y, lastPauseView.left - beforeLastPauseView.right, _processView.height)];
    _deleteView.backgroundColor = [UIColor whiteColor];
    [self addSubview:_deleteView];
}

-(void)cancelDelete
{
    if (_deleteView) {
        [_deleteView removeFromSuperview];
    }
}

-(void)comfirmDeletePart
{
    UIView *lastPauseView = [_pauseViewList lastObject];
    if (lastPauseView) {
        [lastPauseView removeFromSuperview];
    }
    [_pauseViewList removeObject:lastPauseView];
    [_deleteView removeFromSuperview];
}

-(void)deleteAllPart
{
    for(UIView *view in _pauseViewList)
    {
        [view removeFromSuperview];
    }
    [_pauseViewList removeAllObjects];
    [_deleteView removeFromSuperview];
}
@end
