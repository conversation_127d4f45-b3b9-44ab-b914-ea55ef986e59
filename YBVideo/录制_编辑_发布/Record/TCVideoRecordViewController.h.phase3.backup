#import <UIKit/UIKit.h>
typedef NS_ENUM(NSInteger,RecordType)
{
    RecordType_Normal,
    RecordType_Chorus,
};

/**
 *  短视频录制VC
 */
@interface TCVideoRecordViewController : YBBaseViewController
{
    
}
-(void)onBtnCloseClicked;

@property(nonatomic,strong)NSString *musicPath;
@property(nonatomic,strong)NSString *musicID;    //选取音乐的ID
@property(nonatomic,assign)BOOL haveBGM;         //yes-开拍时候选择了音乐  no-未选择音乐直接开拍

@property(nonatomic,assign)RecordType recordType;//拍摄类型(普通拍摄--合拍  注意:拍摄同款属于普通拍摄)
@property(nonatomic,strong)NSString *mp4Path;

@property(nonatomic,assign)BOOL isTakeSame;     //拍摄同款

@end
