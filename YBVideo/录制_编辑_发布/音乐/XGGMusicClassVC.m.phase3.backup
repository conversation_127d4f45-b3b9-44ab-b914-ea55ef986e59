//
//  MusicClassVC.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/28.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MusicClassVC.h"

#import "MusicModel.h"
#import "MusicCell.h"
#import "NSString+StringSize.h"
#import "TCVideoRecordViewController.h"
#import "JCHATAlertViewWait.h"
#import <MediaPlayer/MediaPlayer.h>

@interface MusicClassVC ()<UITableViewDataSource,UITableViewDelegate,UISearchBarDelegate>
{
    int _paging;
    AVAudioPlayer *_musicPlayer;    //音乐播放器

    CGFloat _originVolume;          //记录系统音量
    MPVolumeView *_volumeView;
    UISlider *_volumeViewSlider;
    BOOL _changeVolume;
}
/** 记录 cell 是否打开 */
@property (nonatomic, assign) BOOL isOpen;
/** 记录当前点击的 cell 行数 */
@property (nonatomic, assign) int curRow;

@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)NSArray *models;
@property(nonatomic,strong)NSMutableArray *allArray;

@end

@implementation MusicClassVC
-(void)creatVoluem {
    if (_volumeViewSlider == nil) {
        _volumeView = [[MPVolumeView alloc] initWithFrame:CGRectMake(-100, -100, 40, 40)];
        [self.view addSubview:_volumeView];
        for (UIView* newView in _volumeView.subviews) {
            if ([newView.class.description isEqualToString:@"MPVolumeSlider"]){
                _volumeViewSlider = (UISlider*)newView;
                break;
            }
        }
    }
    [_volumeView setHidden:NO];
    [_volumeView setShowsRouteButton:YES];
    [_volumeView setShowsVolumeSlider:YES];
}
-(void)recoveryVolume{
    if (_changeVolume) {
        _changeVolume = NO;
        _volumeViewSlider.value = _originVolume ;
    }
}
-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self pullData];
}
-(void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if ([PublicObj getSysOutputVolume] < 0.2) {
        _changeVolume = YES;
        _originVolume = [AVAudioSession sharedInstance].outputVolume;
        _volumeViewSlider.value = 0.3;
    }
}
-(void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.naviView.hidden = YES;
    [self creatVoluem];
    [self creatNavi];
    
    _paging = 1;
    self.curRow = -1;
    self.allArray = [NSMutableArray array];
    self.models = [NSArray array];
    
    [self.view addSubview:self.tableView];
    self.view.backgroundColor = UIColor.whiteColor;
}
-(NSArray *)models{
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in _allArray) {
        MusicModel *model = [MusicModel modelWithDic:dic];
        [array addObject:model];
    }
    _models = array;
    return _models;
}

-(void)pullData {
    
    NSString *url = [NSString stringWithFormat:@"Music.music_list&classify=%@&uid=%@&p=%d",_class_id,[Config getOwnID],_paging];
    YBWeakSelf;
    
    [YBNetworking postWithUrl:url Dic:@{} Suc:^(int code, id info, NSString *msg) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
        if (code == 0 ) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging == 1) {
                [_allArray removeAllObjects];
            }
            if (infoA.count <=0) {
                [_tableView.mj_footer endRefreshingWithNoMoreData];
            }else{
                [_allArray addObjectsFromArray:infoA];
            }
            if (_allArray.count <= 0) {
                [PublicView showTextNoData:weakSelf.tableView text1:YZMsg(@"") text2:YZMsg(@"去看看其他音乐类型吧") centerY:0.8];
            }else{
                [PublicView hiddenTextNoData:weakSelf.tableView];
            }
            [_tableView reloadData];
        }else{
            [PublicView showTextNoData:weakSelf.tableView text1:YZMsg(@"") text2:YZMsg(@"去看看其他音乐类型吧") centerY:0.8];
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
    }];
}

#pragma mark - 音乐播放、暂停
-(void)playMusic:(NSString *)path currentCell:(MusicCell *)cell currentIndex:(NSIndexPath*)indexPath{
    NSLog(@"播放");
    
    if (self.curRow !=indexPath.row) {
        //点击过cell并且非同一行时候，清除掉上一个播放器
        [self stopMusic];
    }
    
    if (!_musicPlayer) {
        //创建音乐播放器
        NSError *error = nil;
        _musicPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:[NSURL fileURLWithPath:path] error:&error];
        //准备播放
        if ([PublicObj getSysOutputVolume]<0.2) {
            _musicPlayer.volume = 0.4;
        }
        [_musicPlayer prepareToPlay];
        //播放歌曲
        [_musicPlayer play];
        _musicPlayer.numberOfLoops = -1;
        [cell.StateBtn setImage:[UIImage imageNamed:@"music_pause"] forState:0];
    }else {
        if (_musicPlayer.isPlaying) {
            [_musicPlayer pause];
            [cell.StateBtn setImage:[UIImage imageNamed:@"music_play"] forState:0];
        }else {
            [_musicPlayer play];
            [cell.StateBtn setImage:[UIImage imageNamed:@"music_pause"] forState:0];
        }
    }
}
-(void)stopMusic {
    if (_musicPlayer) {
        [_musicPlayer stop];
        _musicPlayer = nil;
    }
}


#pragma mark - tableview  UITableViewDataSource   UITableViewDelegate
//删除
//-(void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath{
//    
//    MusicModel *model = _models[indexPath.row];
//    NSFileManager* fileManager=[NSFileManager defaultManager];
//    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
//    NSString *docDir = [paths objectAtIndex:0];
//    NSString *loadPath = [docDir stringByAppendingFormat:@"/*%@*%@*%@*%@.mp3",model.musicNameStr,model.singerStr,model.timeStr,model.songID];
//    BOOL blHave=[[NSFileManager defaultManager] fileExistsAtPath:loadPath];
//    if (blHave) {
//        [fileManager removeItemAtPath:loadPath error:nil];
//        [self.tableView reloadData];
//        [MBProgressHUD showPop:@"音乐缓存清除成功"];
//    }else{
//        [MBProgressHUD showPop:@"音乐缓存为空"];
//    }
//    [self stopMusic];
//    
//}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    
    return self.models.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    
    MusicCell *cell = [MusicCell cellWithTab:tableView andIndexPath:indexPath];
    
    //10-31添加
    (self.curRow == (int)indexPath.row && self.isOpen) ? (cell.startRecoedBtn.hidden = NO) : (cell.startRecoedBtn.hidden = YES);
    
    cell.backgroundColor = [UIColor whiteColor];
    MusicModel *model = _models[indexPath.row];
    cell.model = model;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    YBWeakSelf;
    //回调事件处理
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *docDir = [paths objectAtIndex:0];
    NSString *loadPath = [docDir stringByAppendingFormat:@"/*%@*%@*%@*%@.mp3",model.musicNameStr,model.singerStr,model.timeStr,model.songID];
    cell.recordEvent = ^(NSString *type) {
        //停止播放音乐
        [weakSelf stopMusic];
        //开拍之前()---开拍之后(编辑页面)
        if ([_fromWhere isEqual:@"edit"]) {
            [self recoveryVolume];
            //回调音频路径
            //先back 在dismis
            if (weakSelf.backEvent) {
                weakSelf.backEvent(@"分类音乐", loadPath, model.songID);
            }
            //[weakSelf.navigationController popViewControllerAnimated:NO];
            [weakSelf dismissViewControllerAnimated:YES completion:nil];
        }else{
            TCVideoRecordViewController *videoRecord = [TCVideoRecordViewController new];
            videoRecord.musicPath = loadPath;
            videoRecord.musicID = model.songID;
            videoRecord.haveBGM = YES;
            YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:videoRecord];
            nav.navigationBarHidden = YES;
            nav.modalPresentationStyle = 0;
            [self presentViewController:nav animated:YES completion:nil];
        }
    };
    __weak MusicCell *weakCell = cell;
    cell.rsEvent = ^(NSString *rs, NSString *erro) {
        [[JCHATAlertViewWait ins] hidenAll];
        if ([rs isEqual:@"sucess"]) {
            [self stopMusic];
            [weakSelf playMusic:loadPath currentCell:weakCell currentIndex:indexPath];
        }else{
            [MBProgressHUD showPop:erro];
        }
    };
    cell.selectedBackgroundView = [[UIImageView alloc]initWithImage:[PublicObj getImgWithColor:SelCell_Col]];
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    MusicCell *cell = (MusicCell *)[tableView cellForRowAtIndexPath:indexPath];
    MusicModel *model = _models[indexPath.row];
    if (self.curRow == (int)indexPath.row) {
        self.curRow = -99999;
        self.isOpen = NO;
        [self stopMusic];
        [tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
        return;
    }else{
        self.isOpen = YES;
    }
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *docDir = [paths objectAtIndex:0];
    NSString *loadPath = [docDir stringByAppendingFormat:@"/*%@*%@*%@*%@.mp3",model.musicNameStr,model.singerStr,model.timeStr,model.songID];
    NSFileManager *manager = [NSFileManager defaultManager];
    if ([manager fileExistsAtPath:loadPath]) {
        //已下载
        [self playMusic:loadPath currentCell:cell currentIndex:indexPath];
    }else{
        [[JCHATAlertViewWait ins] showInView];
        //下载歌曲
        [cell musicDownLoad];
    }
    //处理显示、隐藏开拍按钮
    if (self.curRow == (int)indexPath.row) {
        return;
    }
    MusicCell *lastCell = (MusicCell *)[tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:self.curRow inSection:0]];
    [lastCell.StateBtn setImage:[UIImage imageNamed:@"music_play"] forState:0];
    self.isOpen = YES;
    self.curRow = (int)indexPath.row;
    /** 刷新tableView，系统默认会有一个自带的动画 */
    [tableView beginUpdates];
    //10-31添加
    lastCell.startRecoedBtn.hidden = YES;
    cell.startRecoedBtn.hidden = NO;
    //(self.curRow == (int)indexPath.row && self.isOpen) ? (cell.startRecoedBtn.hidden = NO) : (cell.startRecoedBtn.hidden = YES);
    [tableView endUpdates];
    
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    //普通cell 80 播放音乐+50
    return (self.curRow == (int)indexPath.row && self.isOpen) ? 130 : 80;
}


- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    
}
#pragma mark - set/get
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-ShowDiff) style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.separatorStyle = UITableViewCellAccessoryNone;
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            _paging = 1;
            [self pullData];
        }];
        _tableView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            _paging +=1;
            [self pullData];
        }];
    }
    return _tableView;
}

#pragma mark - 导航
-(void)creatNavi {

    
    UIView *navi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
    navi.backgroundColor = Normal_Color;
    [self.view addSubview:navi];
    
    UIView *bgView = [[UIView alloc]initWithFrame:CGRectMake(0, statusbarHeight+20,_window_width ,44)];
    bgView.backgroundColor = [UIColor whiteColor];
    [navi addSubview:bgView];
    UIBezierPath * maskPath = [UIBezierPath bezierPathWithRoundedRect:bgView.bounds byRoundingCorners:UIRectCornerTopRight|UIRectCornerTopLeft cornerRadii:CGSizeMake(5, 5)];
    CAShapeLayer * maskLayer = [[CAShapeLayer alloc]init];
    maskLayer.frame = bgView.bounds;
    maskLayer.path = maskPath.CGPath;
    bgView.layer.mask = maskLayer;
    
    UIButton *leftBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [leftBtn setImage:[UIImage imageNamed:@"gray_back"] forState:0];
    leftBtn.frame = CGRectMake(0, 12+statusbarHeight, 50, 50);
    leftBtn.imageEdgeInsets = UIEdgeInsetsMake(20, 16, 12, 16);
    [leftBtn addTarget:self action:@selector(clickBackBtn) forControlEvents:UIControlEventTouchUpInside];
    [navi addSubview:leftBtn];
    
    CGFloat top_space = 22+statusbarHeight;
    if (IS_IPHONE_X) {
        top_space = 18+statusbarHeight;
    }
    UILabel *midL = [[UILabel alloc]initWithFrame:CGRectMake(50, top_space, _window_width-100, 40)];
    midL.text = _navi_title;
    midL.textColor = [UIColor blackColor];
    midL.font = SYS_Font(17);
    midL.textAlignment = NSTextAlignmentCenter;
    [navi addSubview:midL];
}
-(void)clickBackBtn {
    [self recoveryVolume];
    //停止播放音乐
    [self stopMusic];
    [self.navigationController popViewControllerAnimated:YES];
}
@end
