<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14460.31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14460.20"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" restorationIdentifier="MusicCell" selectionStyle="default" indentationWidth="10" rowHeight="137" id="KGk-i7-Jjw" customClass="MusicCell">
            <rect key="frame" x="0.0" y="0.0" width="341" height="137"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="341" height="136.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xGv-eh-3DM" userLabel="撑起开拍按钮顶部80的高度，方便点击时显示开拍按钮">
                        <rect key="frame" x="0.0" y="0.0" width="341" height="80"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="80" id="Yqn-EY-Fss"/>
                        </constraints>
                    </view>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="gIE-4U-PWs" userLabel="封面">
                        <rect key="frame" x="15" y="10" width="60" height="60"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="60" id="XNK-1l-k5U"/>
                            <constraint firstAttribute="width" secondItem="gIE-4U-PWs" secondAttribute="height" multiplier="1:1" id="ffB-Zc-T59"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="r8l-dg-Prc" userLabel="播放状态">
                        <rect key="frame" x="35" y="30" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="Tlh-EQ-0TN"/>
                            <constraint firstAttribute="width" secondItem="r8l-dg-Prc" secondAttribute="height" multiplier="1:1" id="fQk-yo-gTM"/>
                        </constraints>
                        <state key="normal" title="Button"/>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bmy-Jm-IiH" userLabel="音乐名称">
                        <rect key="frame" x="85" y="10" width="37.5" height="20"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mv2-6G-kLV" userLabel="歌手">
                        <rect key="frame" x="85" y="30" width="33" height="20"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.5490196078431373" green="0.5490196078431373" blue="0.5490196078431373" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MYS-hC-ZPE" userLabel="时间">
                        <rect key="frame" x="85" y="50" width="33" height="20"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.5490196078431373" green="0.5490196078431373" blue="0.5490196078431373" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jhu-Xv-EKO" userLabel="收藏">
                        <rect key="frame" x="296" y="25" width="30" height="30"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="30" id="GdU-Uh-75f"/>
                            <constraint firstAttribute="width" constant="30" id="LBX-HQ-h9G"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <state key="normal">
                            <color key="titleColor" red="0.91764705882352937" green="0.21568627450980393" blue="0.44313725490196076" alpha="1" colorSpace="calibratedRGB"/>
                        </state>
                        <connections>
                            <action selector="clickUseBtn:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="WDT-te-mpo"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qb1-pK-LUl" userLabel="line">
                        <rect key="frame" x="0.0" y="135.5" width="341" height="0.5"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.059999999999999998" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="0.5" id="sNC-i5-PH7"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="y53-MI-hwg" userLabel="开拍">
                        <rect key="frame" x="15" y="80" width="311" height="45"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="45" id="83v-kl-UcE"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <inset key="titleEdgeInsets" minX="2" minY="0.0" maxX="0.0" maxY="0.0"/>
                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2" maxY="0.0"/>
                        <state key="normal" title="确认使用并开拍" image="music_startrecord.png"/>
                        <connections>
                            <action selector="clickStartRecordBtn:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="pJ3-Z8-mIQ"/>
                        </connections>
                    </button>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="gIE-4U-PWs" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="21B-tz-AHr"/>
                    <constraint firstAttribute="trailing" secondItem="jhu-Xv-EKO" secondAttribute="trailing" constant="15" id="B6B-gt-XtR"/>
                    <constraint firstItem="Bmy-Jm-IiH" firstAttribute="leading" secondItem="gIE-4U-PWs" secondAttribute="trailing" constant="10" id="C9b-2S-SHi"/>
                    <constraint firstItem="gIE-4U-PWs" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="DhJ-hc-PNE"/>
                    <constraint firstItem="r8l-dg-Prc" firstAttribute="centerX" secondItem="gIE-4U-PWs" secondAttribute="centerX" id="EHh-U3-n97"/>
                    <constraint firstItem="Bmy-Jm-IiH" firstAttribute="width" relation="lessThanOrEqual" secondItem="H2p-sc-9uM" secondAttribute="width" multiplier="1/2" id="HBU-nK-CjY"/>
                    <constraint firstItem="xGv-eh-3DM" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" id="Hr1-CC-yhh"/>
                    <constraint firstItem="y53-MI-hwg" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="K4q-nA-N1W"/>
                    <constraint firstItem="jhu-Xv-EKO" firstAttribute="centerY" secondItem="gIE-4U-PWs" secondAttribute="centerY" id="K64-ge-XF3"/>
                    <constraint firstItem="Mv2-6G-kLV" firstAttribute="height" secondItem="Bmy-Jm-IiH" secondAttribute="height" id="ONp-R9-9Zi"/>
                    <constraint firstAttribute="bottom" secondItem="Qb1-pK-LUl" secondAttribute="bottom" constant="0.5" id="OTd-7W-w64"/>
                    <constraint firstItem="r8l-dg-Prc" firstAttribute="centerY" secondItem="gIE-4U-PWs" secondAttribute="centerY" id="PJW-Zq-ujJ"/>
                    <constraint firstItem="Mv2-6G-kLV" firstAttribute="width" relation="lessThanOrEqual" secondItem="H2p-sc-9uM" secondAttribute="width" multiplier="1/2" id="Rbj-ww-SgN"/>
                    <constraint firstItem="Bmy-Jm-IiH" firstAttribute="height" secondItem="gIE-4U-PWs" secondAttribute="height" multiplier="1/3" id="Vdi-c6-3pG"/>
                    <constraint firstItem="MYS-hC-ZPE" firstAttribute="height" secondItem="Mv2-6G-kLV" secondAttribute="height" id="bMg-6s-EM8"/>
                    <constraint firstItem="MYS-hC-ZPE" firstAttribute="width" relation="lessThanOrEqual" secondItem="H2p-sc-9uM" secondAttribute="width" multiplier="1/2" id="cOK-AT-lll"/>
                    <constraint firstItem="y53-MI-hwg" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" constant="-30" id="cTL-Z6-lbm"/>
                    <constraint firstItem="Bmy-Jm-IiH" firstAttribute="top" secondItem="gIE-4U-PWs" secondAttribute="top" id="ccP-wQ-IfN"/>
                    <constraint firstItem="MYS-hC-ZPE" firstAttribute="leading" secondItem="Mv2-6G-kLV" secondAttribute="leading" id="fNq-ub-K2D"/>
                    <constraint firstItem="xGv-eh-3DM" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="mCd-kc-GuI"/>
                    <constraint firstItem="Mv2-6G-kLV" firstAttribute="leading" secondItem="Bmy-Jm-IiH" secondAttribute="leading" id="n4P-g4-gMX"/>
                    <constraint firstItem="Mv2-6G-kLV" firstAttribute="top" secondItem="Bmy-Jm-IiH" secondAttribute="bottom" id="prZ-xa-KbT"/>
                    <constraint firstItem="y53-MI-hwg" firstAttribute="top" secondItem="xGv-eh-3DM" secondAttribute="bottom" id="rxY-jS-sEC"/>
                    <constraint firstItem="Qb1-pK-LUl" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="uGr-ww-D2t"/>
                    <constraint firstItem="Qb1-pK-LUl" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" id="uzI-dX-ffT"/>
                    <constraint firstItem="MYS-hC-ZPE" firstAttribute="top" secondItem="Mv2-6G-kLV" secondAttribute="bottom" id="xDs-Cs-zD4"/>
                    <constraint firstItem="xGv-eh-3DM" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="yXe-FV-xN3"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="StateBtn" destination="r8l-dg-Prc" id="Tr5-cW-ymD"/>
                <outlet property="bgIV" destination="gIE-4U-PWs" id="y89-7r-DRx"/>
                <outlet property="musicNameL" destination="Bmy-Jm-IiH" id="7ld-Xe-n6B"/>
                <outlet property="singerL" destination="Mv2-6G-kLV" id="B5v-0L-Xzc"/>
                <outlet property="startRecoedBtn" destination="y53-MI-hwg" id="1CH-SJ-BZ4"/>
                <outlet property="timeL" destination="MYS-hC-ZPE" id="zqT-sv-KJs"/>
                <outlet property="topBgV" destination="xGv-eh-3DM" id="CKq-0j-Q4y"/>
                <outlet property="useBtn" destination="jhu-Xv-EKO" id="Ai4-Gh-scJ"/>
            </connections>
            <point key="canvasLocation" x="49.5" y="107.5"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="music_startrecord.png" width="20" height="20"/>
    </resources>
</document>
