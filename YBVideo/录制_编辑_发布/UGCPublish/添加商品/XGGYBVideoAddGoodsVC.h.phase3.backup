//
//  YBVideoAddGoodsVC.h
//  YBVideo
//
//  Created by YB007 on 2019/11/28.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBBaseViewController.h"


//typedef void(^AddGoodsBlock)(NSDictionary *originDic,NSString *formatJson); //废弃
typedef void (^AddRefreshBlock)(void);

@interface YBVideoAddGoodsVC : YBBaseViewController

//@property (nonatomic,copy) AddGoodsBlock addGoodsEvent;   //废弃
@property(nonatomic,assign)BOOL isUpdateGoods;              //是否是修改
@property (nonatomic,strong) NSDictionary *goodsInfo;       //修改商品会用到
@property(nonatomic,copy)AddRefreshBlock addRefreshEvnet;

@end

