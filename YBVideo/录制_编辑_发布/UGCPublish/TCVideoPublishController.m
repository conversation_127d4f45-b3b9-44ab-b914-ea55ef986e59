
#import "TCVideoPublishController.h"
#import "UIView+CustomAutoLayout.h"
#import "TCVideoRecordViewController.h"

#import <TXLiteAVSDK_Professional/TXUGCRecord.h>
#import <TXLiteAVSDK_Professional/TXLivePlayer.h>
#import <AssetsLibrary/AssetsLibrary.h>
#import <AVFoundation/AVFoundation.h>

#import "PublishShareV.h"
#import "YBTabBarController.h"
#import "AFNetworking.h"
#import <TXLiteAVSDK_Professional/TXVideoEditer.h>

#import "videoTopicVC.h"
#import "YBVideoAddGoodsVC.h"
#import "YBVideoClassVC.h"
#import "YBSetChargeView.h"
#import "YBTakeSameVideoVC.h"
#import "YBStorageObj.h"
#import "YBShareView.h"
#import "TCVideoRecordViewController.h"
#import "TCVideoEditViewController.h"
#import "YBGoodsListVC.h"
#import "YBPublishCoverVC.h"
#import "RelationVideoGoodsVC.h"
@interface TCVideoPublishController()<TXLivePlayListener>

@property (nonatomic,strong) NSString *imagekey;
@property (nonatomic,strong) NSString *videokey;
//分享的视频id 和 截图
@property (nonatomic,strong) NSString *videoid;
@property (nonatomic,strong) NSString *image_thumb;

/** 背景scorll */
@property(nonatomic,strong)UIScrollView *bgScrollView;

/** 顶部组合：视频预览、视频描述 */
@property(nonatomic,strong)UIView   *topMix;

@property(nonatomic,strong)UIView  *videoPreview;               //视频预览
@property(nonatomic,strong)UIImageView *videoCoverIV;           //视频封面
@property(nonatomic,assign)int coverSelIndex;
@property(nonatomic,strong)MyTextView  *videoDesTV;             //视频描述
@property(nonatomic,strong) UILabel *wordsNumL;                 //字符统计
@property(nonatomic,strong) UIButton *isAdBtn;                 //广告按钮

/** 定位组合：图标、位置 */
@property(nonatomic,strong)UIView *locationV;

/** 分享平台组合 */
@property(nonatomic,strong)PublishShareV *platformV;

/** 发布按钮 */
@property(nonatomic,strong)UIButton *publishBtn;

//话题
@property(nonatomic,strong)UIView *topicView;
@property (nonatomic,strong) UILabel *topicLabel;
@property (nonatomic,strong) NSDictionary *topicDic;

//商品
@property(nonatomic,strong)UIView *shopView;
@property (nonatomic,strong) UILabel *shopLabel;
@property (nonatomic,strong) NSMutableDictionary *shopDic;
@property(nonatomic,strong)NSString *shopJsonStr;           //废弃--改为goodsID
@property(nonatomic,strong)NSString *goodsID;
//@property (nonatomic,strong) UIImage *goodsThumb;

//广告
@property(nonatomic,strong)UIView *adView;
@property(nonatomic,strong)UITextField *adTextView;

//分类
@property(nonatomic,strong)UIView *videoClassView;
@property(nonatomic,strong)UILabel *videoClassL;
@property(nonatomic,strong)NSDictionary *videoClassDic;

//收费
@property(nonatomic,strong)UIView *videoChargeView;
@property(nonatomic,strong)UILabel *videoChargeL;
@property(nonatomic,strong)NSString *videoChargeNum;

@end
@implementation TCVideoPublishController {
    NSString *sharetype;                        //分享类型
    NSString *mytitle;
    TXLivePlayer    *_livePlayer;
    TXUGCRecordResult   *_recordResult;
    BOOL            _isPublished;
    BOOL            _playEnable;
    
    NSString *_cosImgFinshPath;
    NSString *_cosVideoFinshPath;
    NSString *_cosWaterVFinshPath;
    
    BOOL _shareBack;
    CGFloat _allOrV;
     CGFloat _allOrC;
     CGFloat _allWaV;
    int _allNum;
}

- (instancetype)initWithPath:(NSString *)videoPath videoMsg:(TXVideoInfo *) videoMsg {
    TXUGCRecordResult *recordResult = [TXUGCRecordResult new];
    //recordResult.coverImage = videoMsg.coverImage;
    recordResult.coverImage = [TXVideoInfoReader getSampleImage:0.0 videoPath:videoPath];
    recordResult.videoPath = videoPath;
    return [self init:nil recordType:0 RecordResult:recordResult TCLiveInfo:nil];
    
}
- (instancetype)init:(id)videoRecorder recordType:(NSInteger)recordType RecordResult:(TXUGCRecordResult *)recordResult  TCLiveInfo:(NSDictionary *)liveInfo {
    self = [super init];
    if (self) {

        sharetype = @"qx";
        _recordResult = recordResult;
        
        _isPublished = NO;
        _playEnable  = YES;
        _coverSelIndex = 0;
        
        /*rk_200916
        _livePlayer  = [[TXLivePlayer alloc] init];
        _livePlayer.delegate = self;
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillEnterForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationDidEnterBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
        */
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
}
-(void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    /*rk_200916
    _playEnable = YES;
    if (_isPublished == NO) {
        [_livePlayer startPlay:_recordResult.videoPath type:PLAY_TYPE_LOCAL_VIDEO];
    }
    */
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [UIApplication sharedApplication].statusBarHidden = NO;
    /*rk_200916
    _playEnable = NO;
    [_livePlayer stopPlay];
     */
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    self.navigationController.interactivePopGestureRecognizer.delegate =nil;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(endBackground) name:UIApplicationWillEnterForegroundNotification object:nil];
    [UIApplication sharedApplication].statusBarHidden = NO;
    self.navigationController.navigationBarHidden = YES;
    self.view.backgroundColor = Normal_Color;
    self.titleL.text = YZMsg(@"发布视频");
    
    _cosImgFinshPath = @"";
    _cosVideoFinshPath = @"";
    _cosWaterVFinshPath = @"";
    
    _shopDic = @{}.mutableCopy;
    _shopJsonStr = @"";
    _goodsID = @"0";
    
    UITapGestureRecognizer *singleTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(closeKeyboard:)];
    singleTap.cancelsTouchesInView = NO;
    [self.view addGestureRecognizer:singleTap];
    
    [self.view addSubview:self.bgScrollView];
    if (@available(iOS 11.0,*)) {
        _bgScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    /*rk_200916
    [_livePlayer setupVideoWidget:CGRectZero containView:_videoPreview insertIndex:0];
    */
    [BGSetting getBgSettingUpdate:NO maintain:NO eventBack:nil];
    
}
-(void)endBackground{
    if (_shareBack) {
        [self root];
    }
}
//- (void)dealloc {
//    [[NSNotificationCenter defaultCenter]removeObserver:self];
//}
- (void)closeKeyboard:(UITapGestureRecognizer *)gestureRecognizer {
    [_videoDesTV resignFirstResponder];
    [_adTextView resignFirstResponder];
}

#pragma mark - 发布
- (void)clickPublishBtn {
    //写暂停
    [_livePlayer pause];
    
    //测试
    /*
    [self root];
    return;
    */
    
    NSString *is_userad;
    NSString *userad_url;
    if (_isAdBtn.isSelected == YES) {
        is_userad = @"1";
        userad_url = _adTextView.text;
        _goodsID = @"0";
    }else{
        is_userad = @"0";
        userad_url = @"";
    }
    
   _publishBtn.enabled = NO;
   [self.view endEditing:YES];
    mytitle = [NSString stringWithFormat:@"%@",_videoDesTV.text];//标题
    //上传
    
    AppDelegate *app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    app.videoTitle = mytitle;
    app.originalCover = _videoCoverIV.image ? _videoCoverIV.image : _recordResult.coverImage;
    app.originalVdieoPath = _recordResult.videoPath;
    app.originalMKVideoPath = _waterVideoPath;
    app.is_userad = is_userad;
    app.userad_url = userad_url;
    app.topicID = _topicDic ? minstr([_topicDic valueForKey:@"id"]):@"0";
    app.videoClassID = [PublicObj checkNull:minstr([_videoClassDic valueForKey:@"id"])]?@"0":minstr([_videoClassDic valueForKey:@"id"]);
    app.musicID = _musicID;
    //app.shopJsonStr = [PublicObj checkNull:_shopJsonStr]?@"":_shopJsonStr;
    app.goodsID = _goodsID;
    app.videoChargeNum = [PublicObj checkNull:_videoChargeNum]?@"0":_videoChargeNum;
    
    [app startUpload];
    
    [self root];
    
    /*
    YBWeakSelf;
    [[YBStorageObj storageManeger]getCOSType:^(int code) {
        if (code == 0) {
            [weakSelf startUpload];
        }
    }];
    */
    
    
}
/*
-(void)startUpload {
    _isPublished = YES;
    [MBProgressHUD showMessage:YZMsg(@"发布中")];
    
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    YBWeakSelf;
    _allOrV = 0.0;
    _allOrC = 0.0;
    _allWaV = 0.0;
    _allNum = 2;
    //原始图片
    dispatch_group_async(group, queue, ^{
        UIImage *saveImg = _recordResult.coverImage;
        if (!_recordResult.coverImage) {
            saveImg = [TXVideoInfoReader getSampleImage:0.0 videoPath:_recordResult.videoPath];
        }
        NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_origin.png"];
        [[YBStorageObj storageManeger]yb_storageImg:saveImg andName:imageName progress:^(CGFloat percent) {
            //NSLog(@"%@--<>:%f",imageName,percent);
            _allOrC = percent;
            [weakSelf ybUploadProgress];
        } complete:^(int code, NSString *key) {
            _cosImgFinshPath = minstr(key);
            dispatch_semaphore_signal(semaphore);
        }];
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    });
    
    //原始视频
    dispatch_group_async(group, queue, ^{
        NSString *videoName = [PublicObj getNameBaseCurrentTime:@"_origin.mp4"];
        NSString *videoPath = _recordResult.videoPath;
        [[YBStorageObj storageManeger]yb_storageVideoOrVoice:videoPath andName:videoName progress:^(CGFloat percent) {
            //NSLog(@"%@--<>:%f",videoName,percent);
            _allOrV = percent;
            [weakSelf ybUploadProgress];
        } complete:^(int code, NSString *key) {
            _cosVideoFinshPath = minstr(key);
            dispatch_semaphore_signal(semaphore);
        }];
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    });
    
    //水印视频
    if (_waterVideoPath) {
        _allNum = 3;
        dispatch_group_async(group, queue, ^{
            NSString *videoName = [PublicObj getNameBaseCurrentTime:@"_water.mp4"];
            [[YBStorageObj storageManeger]yb_storageVideoOrVoice:_waterVideoPath andName:videoName progress:^(CGFloat percent) {
                //NSLog(@"shui%@--<>:%f",videoName,percent);
                _allWaV = percent;
                [weakSelf ybUploadProgress];
            } complete:^(int code, NSString *key) {
                _cosWaterVFinshPath = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf requstAPPServceTitle:mytitle andVideo:_cosVideoFinshPath andImage:_cosImgFinshPath andWaterName:_cosWaterVFinshPath];
        });
        NSLog(@"任务完成执行");
    });
    
}

-(void)ybUploadProgress{
    
    NSLog(@"allll:%f",(_allOrC+_allOrV+_allWaV)/_allNum);
    
}
*/
#pragma mark - UITextViewDelegate
- (void)textViewDidChange:(UITextView*)textView {

    NSString *toBeString = textView.text;
    NSString *lang = [[[UITextInputMode activeInputModes]firstObject] primaryLanguage];
    if ([lang isEqualToString:@"zh-Hans"]) {
        UITextRange *selectedRange = [textView markedTextRange];
        UITextPosition *position = [textView positionFromPosition:selectedRange.start offset:0];
        if (!position) {
            if (toBeString.length > 50) {
                textView.text = [toBeString substringToIndex:50];
                _wordsNumL.text = [NSString stringWithFormat:@"%lu/50",(textView.text.length)];
            }else{
                _wordsNumL.text = [NSString stringWithFormat:@"%lu/50",(toBeString.length)];
            }
        }else{
            //有高亮选择的字符串，则暂不对文字进行统计和限制
        }
    }else{
        // 中文输入法以外的直接对其统计限制即可，不考虑其他语种情况
        if (toBeString.length > 50) {
            textView.text = [toBeString substringToIndex:50];
            _wordsNumL.text = [NSString stringWithFormat:@"%lu/50",(textView.text.length)];
        }else{
            _wordsNumL.text = [NSString stringWithFormat:@"%lu/50",(toBeString.length)];
        }
    }
    
}
/*rk_200916
- (void)applicationWillEnterForeground:(NSNotification *)noti {
    //temporary fix bug
    if ([self.navigationItem.title isEqualToString:YZMsg(@"发布中")])
        return;
    
    if (_isPublished == NO) {
        [_livePlayer startPlay:_recordResult.videoPath type:PLAY_TYPE_LOCAL_VIDEO];
    }
}

- (void)applicationDidEnterBackground:(NSNotification *)noti {
    [_livePlayer stopPlay];
    
}
#pragma mark TXLivePlayListener
-(void) onPlayEvent:(int)EvtID withParam:(NSDictionary*)param {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (EvtID == PLAY_EVT_PLAY_END && _playEnable) {
            [_livePlayer startPlay:_recordResult.videoPath type:PLAY_TYPE_LOCAL_VIDEO];
            return;
        }
    });

}
-(void) onNetStatus:(NSDictionary*) param {
    return;
}
*/
#pragma mark -
#pragma mark - 上传七牛或者腾讯云存储成功后把视频地址、封面地址反馈给自己的服务器
-(void)requstAPPServceTitle:(NSString *)myTile andVideo:(NSString *)video andImage:(NSString *)image andWaterName:(NSString *)water {
    _publishBtn.enabled = YES;
    _isPublished = NO;
    if ([PublicObj checkNull:_cosVideoFinshPath] || [PublicObj checkNull:_cosImgFinshPath] || (![PublicObj checkNull:_waterVideoPath] && [PublicObj checkNull:_cosWaterVFinshPath])) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showPop:YZMsg(@"发布失败")];
        return;
    }
    //写暂停
    [_livePlayer pause];
    __weak TCVideoPublishController *weakself = self;
    NSString *videoClassID = [PublicObj checkNull:minstr([_videoClassDic valueForKey:@"id"])]?@"0":minstr([_videoClassDic valueForKey:@"id"]);
    NSMutableDictionary *pullDic = @{
                              @"uid":[Config getOwnID],
                              @"token":[Config getOwnToken],
                              @"title":minstr(myTile),
                              @"href":minstr(video),
                              @"thumb":minstr(image),
                              @"lng":[NSString stringWithFormat:@"%@",[XGGcityDefault getLocationLng]],
                              @"lat":[NSString stringWithFormat:@"%@",[XGGcityDefault getLocationLat]],
                              @"city":[NSString stringWithFormat:@"%@",[XGGcityDefault getLocationCity]?[XGGcityDefault getLocationCity]:YZMsg(@"好像在火星")],
                              @"music_id":_musicID ? _musicID : @"0",
                              @"labelid":_topicDic ? minstr([_topicDic valueForKey:@"id"]):@"0",
                              //@"goodsinfo": [PublicObj checkNull:_shopJsonStr]?@"":_shopJsonStr,
                              @"goodsid":_goodsID,
                              @"href_w":water?water:@"",
                              @"classid":videoClassID,
                              @"coin":[PublicObj checkNull:_videoChargeNum]?@"0":_videoChargeNum,
                              }.mutableCopy;
    
    [YBNetworking postWithUrl:@"Video.setVideo" Dic:pullDic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [MBProgressHUD hideHUD];
            NSString *audit_switch = [NSString stringWithFormat:@"%@",[common getAuditSwitch]];
            if ([audit_switch isEqual:@"1"]) {
                [MBProgressHUD showSuccess:YZMsg(@"上传成功，请等待审核")];
            }else{
                [MBProgressHUD showSuccess:YZMsg(@"发布成功")];
            }
            
            BOOL isOk = [[NSFileManager defaultManager] removeItemAtPath:_recordResult.videoPath error:nil];
            if (_waterVideoPath) {
                [[NSFileManager defaultManager] removeItemAtPath:_waterVideoPath error:nil];
            }
            NSLog(@"%d shanchushanchushanchu",isOk);
            NSDictionary *infoDic = [info firstObject];
            _videoid = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"id"]];
            _image_thumb = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"thumb_s"]];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakself pushshare];
                //发布成功后刷新首页
                //[[NSNotificationCenter defaultCenter] postNotificationName:@"reloadlist" object:nil];
                weakself.publishBtn.enabled = NO;
            });
        }else {
            [MBProgressHUD hideHUD];
            [MBProgressHUD showError:msg];
            weakself.publishBtn.enabled = YES;
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:YZMsg(@"网络连接断开，视频上传失败")];
        weakself.publishBtn.enabled = YES;
    }];
}

#pragma mark - 分享
-(void)root {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        //[self.navigationController popToRootViewControllerAnimated:YES];
        if (_livePlayer) {
            [_livePlayer stopPlay];
            _livePlayer = nil;
        }
        for (UIViewController *vc in self.navigationController.childViewControllers) {
            if ([vc isKindOfClass:[TCVideoRecordViewController class]]||[vc isKindOfClass:[TCVideoEditViewController class]]) {
                [vc.view removeFromSuperview];
                [vc removeFromParentViewController];
                NSLog(@"rk20200118===释放VC:%@",vc);
            }
        }
        if (_isTakeSame == YES) {
            //拍同款返回到同款音乐视频列表
            BOOL findVC = NO;
            for (UIViewController *forVC in self.navigationController.childViewControllers) {
                if ([forVC isKindOfClass:[YBTakeSameVideoVC class]]) {
                    findVC = YES;
                    [self.navigationController popToViewController:forVC animated:YES];
                    break;
                }
            }
            if (findVC == NO) {
                [self goHomePageView];
            }
        }else {
            [self goHomePageView];
        }
//        NSLog(@"1031==vc1==%lu=====%@",self.navigationController.childViewControllers.count,self.navigationController.childViewControllers);
//        for (UIViewController *vc in self.navigationController.childViewControllers) {
//            [vc.view removeFromSuperview];
//            [vc removeFromParentViewController];
//        }
//    NSLog(@"1031==vc2==%lu=====%@",self.navigationController.childViewControllers.count,self.navigationController.childViewControllers);
//        [PublicObj resetVC:[[YBTabBarController alloc]initWithAlert:NO]];
    });
}
-(void)goHomePageView {
    [self.navigationController popToRootViewControllerAnimated:YES];
    YBTabBarController *tabbar = [PublicObj currentTabbar];
    [tabbar selectController:0];
    [tabbar goForYouViewRefresh:NO];
}
-(void)pushshare {
    if ([sharetype isEqual:@"qx"]) {
        [self root];
        return;
    }
    _shareBack = YES;
    YBWeakSelf;
    /*
    NSString *titles = _videoDesTV.text;
       if (_videoDesTV.text.length == 0) {
           titles = [NSString stringWithFormat:@"%@%@",[Config getOwnNicename],[common video_share_des]];
       }
    */
    NSString *shareDes = [common video_share_des];
    if (![PublicObj checkNull:_videoDesTV.text]) {
        shareDes = _videoDesTV.text;
    }
    NSDictionary *shareDic = @{@"id":_videoid,@"thumb_s":_image_thumb,@"publish_des":shareDes};
    [[YBShareView noShareUIManager]executeShareWithType:RKShareType_VideoPublish shareType:sharetype parameter:shareDic complete:^(int codeEvent, NSString *nums) {
        [weakSelf root];
    }];
}
#pragma mark - set/get

- (UIScrollView *)bgScrollView {
    if (!_bgScrollView) {
        _bgScrollView = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight)];
        _bgScrollView.contentInset = UIEdgeInsetsMake(0, 0, ShowDiff, 0);
        _bgScrollView.bounces = NO;
        //顶部视图：预览、描述
        [_bgScrollView addSubview:self.topMix];
        
        //定位
        [_bgScrollView addSubview:self.locationV];
        
        //话题
        [_bgScrollView addSubview:self.topicView];
        
        //分类
        [_bgScrollView addSubview:self.videoClassView];
        
        //价格
//        NSString *canSetP = minstr([XGGPower getVideoCharge]);
//        if ([canSetP isEqual:@"1"]) {
//            [_bgScrollView addSubview:self.videoChargeView];
//        }
        //广告链接
        NSString *isad = minstr([XGGPower getIsad]);
        if ([isad isEqual:@"1"]) {
            [_bgScrollView addSubview:self.adView];
            self.adView.hidden = YES;
        }
        
        //商品
        NSString *isShop = minstr([XGGPower getIsShop]);
        if ([isShop isEqual:@"1"]) {
            [_bgScrollView addSubview:self.shopView];
        }
        /*
        //分享平台
        [_bgScrollView addSubview:self.platformV];
        if ([common share_type].count==0) {
            self.platformV.hidden = YES;
        }
        */
        //发布
        [_bgScrollView addSubview:self.publishBtn];
        
        [_bgScrollView layoutIfNeeded];
        CGFloat maxY = CGRectGetMaxY(_publishBtn.frame)+10;
        if (maxY < _window_height-64-statusbarHeight-ShowDiff) {
            maxY = _window_height-64-statusbarHeight-ShowDiff;
        }
        _bgScrollView.contentSize = CGSizeMake(0, maxY);
        
    }
    return _bgScrollView;
}

-(UIView *)topMix {
    if (!_topMix) {
        _topMix = [[UIView alloc] initWithFrame:CGRectMake(15, 10, _window_width-30, 180)];
        _topMix.backgroundColor = RGB_COLOR(@"#15102C", 1);
        
        //视频预览-封面
        _videoPreview = [[UIView alloc] initWithFrame:CGRectMake(15, 15, 100, 150)];
        _videoPreview.layer.cornerRadius = 3;
        _videoPreview.layer.masksToBounds = YES;
        
        _videoCoverIV = [[UIImageView alloc]init];
        _videoCoverIV.image = _recordResult.coverImage;
        _videoCoverIV.contentMode = UIViewContentModeScaleAspectFill;
        _videoCoverIV.clipsToBounds = YES;
        [_videoPreview addSubview:_videoCoverIV];
        [_videoCoverIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(_videoPreview);
        }];
        UIButton *coverBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        coverBtn.backgroundColor = RGB_COLOR(@"#000000", 0.5);
        coverBtn.titleLabel.font = SYS_Font(13);
        [coverBtn setTitle:YZMsg(@"添加封面") forState:0];
        [coverBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
        coverBtn.contentEdgeInsets = UIEdgeInsetsMake(3, 0, 3, 0);
        [coverBtn addTarget:self action:@selector(clickCovderBtn) forControlEvents:UIControlEventTouchUpInside];
        [_videoPreview addSubview:coverBtn];
        [coverBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.bottom.equalTo(_videoPreview);
        }];
        
        //视频描述
        _videoDesTV = [[MyTextView alloc] initWithFrame:CGRectMake(_videoPreview.right+10, 15, _topMix.width-_videoPreview.width - 35, _videoPreview.height)];
        _videoDesTV.backgroundColor = [UIColor clearColor];
        _videoDesTV.delegate = self;
        _videoDesTV.layer.borderColor = _topMix.backgroundColor.CGColor;
        _videoDesTV.font = SYS_Font(16);
        _videoDesTV.textColor = RGB_COLOR(@"#969696", 1);
        _videoDesTV.placeholder = YZMsg(@"添加视频描述~");
        _videoDesTV.placeholderColor = RGB_COLOR(@"#969696", 1);
        
        _wordsNumL = [[UILabel alloc] initWithFrame:CGRectMake(_videoDesTV.right-50, _videoDesTV.bottom-12, 50, 12)];
        _wordsNumL.text = @"0/50";
        _wordsNumL.textColor = RGB_COLOR(@"#969696", 1);
        _wordsNumL.font = [UIFont systemFontOfSize:12];
        _wordsNumL.backgroundColor =[UIColor clearColor];
        _wordsNumL.textAlignment = NSTextAlignmentRight;
        
        _isAdBtn = [UIButton buttonWithType:0];
        _isAdBtn.frame = CGRectMake(_videoPreview.right+10, _videoDesTV.bottom-20, 45, 19);
        [_isAdBtn setImage:[UIImage imageNamed:getImagename(@"ad_关闭")] forState:UIControlStateNormal];
        [_isAdBtn setImage:[UIImage imageNamed:getImagename(@"ad_开启")] forState:UIControlStateSelected];
        [_isAdBtn addTarget:self action:@selector(isadBtnClick) forControlEvents:UIControlEventTouchUpInside];
        if ([[XGGPower getIsad] isEqual:@"1"]) {
            _isAdBtn.hidden = NO;
        }else{
            _isAdBtn.hidden = YES;
        }
        
        [_topMix addSubview:_videoPreview];
        [_topMix addSubview:_videoDesTV];
        [_topMix addSubview:_wordsNumL];
        [_topMix addSubview:_isAdBtn];
    }
    return _topMix;
}

-(UIView *)locationV {
    if (!_locationV) {
        //显示定位
        _locationV = [[UIView alloc]initWithFrame:CGRectMake(15, _topMix.bottom+5, _window_width-30, 50)];
        _locationV.backgroundColor = RGB_COLOR(@"#15102C", 1);
        
        UIImageView *imageloca = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"pink_location"]];
        imageloca.contentMode = UIViewContentModeScaleAspectFit;
        imageloca.frame = CGRectMake(15,_locationV.height/2-7.5,15,15);
        [_locationV addSubview:imageloca];
        
        UILabel *locationlabels = [[UILabel alloc]initWithFrame:CGRectMake(imageloca.right+5, 0, _window_width-50, 50)];
        locationlabels.font = SYS_Font(15);
        locationlabels.text = [NSString stringWithFormat:@"%@",[XGGcityDefault getLocationCity]?[XGGcityDefault getLocationCity]:YZMsg(@"好像在火星")];
        locationlabels.textColor = RGB_COLOR(@"#666666", 1);
        
        [_locationV addSubview:locationlabels];
    }
    return _locationV;
}


-(UIView *)topicView {
    if (!_topicView) {
        //话题
        _topicView = [[UIView alloc]initWithFrame:CGRectMake(15, _locationV.bottom+5, _window_width-30, 50)];
        _topicView.backgroundColor = RGB_COLOR(@"#15102C", 1);
        
        UIImageView *imageloca = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"video_topic"]];
        imageloca.contentMode = UIViewContentModeScaleAspectFit;
        imageloca.frame = CGRectMake(15,_locationV.height/2-7.5,15,15);
        [_topicView addSubview:imageloca];
        
        _topicLabel = [[UILabel alloc]initWithFrame:CGRectMake(imageloca.right+5, 0, _window_width-50, 50)];
        _topicLabel.font = SYS_Font(15);
        _topicLabel.text = YZMsg(@"话题标签");
        _topicLabel.textColor = RGB_COLOR(@"#666666", 1);
        [_topicView addSubview:_topicLabel];
        
        UIImageView *rightImgV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"askWhite_jiantou"]];
        rightImgV.contentMode = UIViewContentModeScaleAspectFit;
        rightImgV.frame = CGRectMake(_locationV.width-20,_locationV.height/2-7.5,15,15);
        [_topicView addSubview:rightImgV];
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(0, 0, _topicView.width, _topicView.height);
        [btn addTarget:self action:@selector(addTopicClick) forControlEvents:UIControlEventTouchUpInside];
        [_topicView addSubview:btn];
    }
    return _topicView;
}
- (UIView *)videoClassView {
    if (!_videoClassView) {
        _videoClassView = [[UIView alloc]initWithFrame:CGRectMake(15, _topicView.bottom+5, _window_width-30, 50)];
        _videoClassView.backgroundColor = RGB_COLOR(@"#15102C", 1);
        
        UIImageView *imageloca = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"视频-分类"]];
        imageloca.contentMode = UIViewContentModeScaleAspectFit;
        imageloca.frame = CGRectMake(15,_locationV.height/2-7.5,15,15);
        [_videoClassView addSubview:imageloca];
        
        _videoClassL = [[UILabel alloc]initWithFrame:CGRectMake(imageloca.right+5, 0, _window_width-50, 50)];
        _videoClassL.font = SYS_Font(15);
        _videoClassL.text = YZMsg(@"视频分类");
        _videoClassL.textColor = RGB_COLOR(@"#666666", 1);
        [_videoClassView addSubview:_videoClassL];
        
        UIImageView *rightImgV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"askWhite_jiantou"]];
        rightImgV.contentMode = UIViewContentModeScaleAspectFit;
        rightImgV.frame = CGRectMake(_locationV.width-20,_locationV.height/2-7.5,15,15);
        [_videoClassView addSubview:rightImgV];
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(0, 0, _videoClassView.width, _videoClassView.height);
        [btn addTarget:self action:@selector(clikcVideoClassBtn) forControlEvents:UIControlEventTouchUpInside];
        [_videoClassView addSubview:btn];
    }
    return _videoClassView;
}
- (UIView *)videoChargeView {
    if (!_videoChargeView) {
        return nil;
        _videoChargeView = [[UIView alloc]initWithFrame:CGRectMake(15, _videoClassView.bottom+5, _window_width-30, 50)];
        _videoChargeView.backgroundColor = RGB_COLOR(@"#15102C", 1);
        
        UIImageView *imageloca = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"视频-收费"]];
        imageloca.contentMode = UIViewContentModeScaleAspectFit;
        imageloca.frame = CGRectMake(15,_locationV.height/2-7.5,15,15);
        [_videoChargeView addSubview:imageloca];
        
        _videoChargeL = [[UILabel alloc]initWithFrame:CGRectMake(imageloca.right+5, 0, _window_width-50, 50)];
        _videoChargeL.font = SYS_Font(15);
        _videoChargeL.text = YZMsg(@"设置收费");
        _videoChargeL.textColor = RGB_COLOR(@"#666666", 1);
        [_videoChargeView addSubview:_videoChargeL];
        _videoChargeNum = @"0";
        
        UIImageView *rightImgV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"askWhite_jiantou"]];
        rightImgV.contentMode = UIViewContentModeScaleAspectFit;
        rightImgV.frame = CGRectMake(_locationV.width-20,_locationV.height/2-7.5,15,15);
        [_videoChargeView addSubview:rightImgV];
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(0, 0, _videoChargeView.width, _videoChargeView.height);
        [btn addTarget:self action:@selector(clikcVideoChargeBtn) forControlEvents:UIControlEventTouchUpInside];
        [_videoChargeView addSubview:btn];
    }
    return _videoChargeView;
}

-(UIView *)shopView {
    if (!_shopView) {
        //商品
        _shopView = [[UIView alloc]initWithFrame:CGRectMake(15, _videoChargeView ? _videoChargeView.bottom+5:_videoClassView.bottom+5, _window_width-30, 50)];
        _shopView.backgroundColor = RGB_COLOR(@"#15102C", 1);
        
        UIImageView *imageloca = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"video_shop"]];
        imageloca.contentMode = UIViewContentModeScaleAspectFit;
        imageloca.frame = CGRectMake(15,_locationV.height/2-7.5,15,15);
        [_shopView addSubview:imageloca];
        
        _shopLabel = [[UILabel alloc]initWithFrame:CGRectMake(imageloca.right+5, 0, _shopView.width-50, 50)];
        _shopLabel.font = SYS_Font(15);
        _shopLabel.text = YZMsg(@"添加商品");
        _shopLabel.textColor = RGB_COLOR(@"#666666", 1);
        [_shopView addSubview:_shopLabel];
        
        UIImageView *rightImgV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"askWhite_jiantou"]];
        rightImgV.contentMode = UIViewContentModeScaleAspectFit;
        rightImgV.frame = CGRectMake(_locationV.width-20,_locationV.height/2-7.5,15,15);
        [_shopView addSubview:rightImgV];
        
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(0, 0, _shopView.width, _shopView.height);
        [btn addTarget:self action:@selector(addShopClick) forControlEvents:UIControlEventTouchUpInside];
        [_shopView addSubview:btn];

    }
    return _shopView;
}
-(UIView *)adView{
    if (!_adView) {
        _adView =[[UIView alloc]initWithFrame:CGRectMake(15, _videoChargeView ? _videoChargeView.bottom+5:_videoClassView.bottom+5, _window_width-30, 50)];
        _adView.backgroundColor = RGB_COLOR(@"#15102C", 1);
        
        UIImageView *imageloca = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"publish_add"]];
        imageloca.contentMode = UIViewContentModeScaleAspectFit;
        imageloca.frame = CGRectMake(15,_locationV.height/2-7.5,15,15);
        [_adView addSubview:imageloca];
        
        UILabel *adLb = [[UILabel alloc]initWithFrame:CGRectMake(imageloca.right+5, 0, 80, 50)];
        adLb.font = SYS_Font(15);
        adLb.text = YZMsg(@"广告链接");
        adLb.textColor = RGB_COLOR(@"#666666", 1);
        [_adView addSubview:adLb];

        _adTextView = [[UITextField alloc]init];
        _adTextView.frame = CGRectMake(adLb.right, 0, _adView.width-adLb.right-10, 50);
        NSMutableAttributedString*phoneString = [[NSMutableAttributedString alloc] initWithString:YZMsg(@"请输入广告链接") attributes:@{NSForegroundColorAttributeName:RGB(150,150,150)}];
        _adTextView.attributedPlaceholder = phoneString;
        _adTextView.font = [UIFont systemFontOfSize:14];
        _adTextView.textColor = UIColor.whiteColor;
        _adTextView.backgroundColor = UIColor.clearColor;
        [_adView addSubview:_adTextView];
        

    }
    return _adView;
}
- (PublishShareV *)platformV {
    if (!_platformV) {
        
        _platformV = [[PublishShareV alloc]initWithFrame:CGRectMake(15,((_shopView) ? (_shopView.bottom+15): (_videoChargeView?(_videoChargeView.bottom+15):(_videoClassView.bottom+15))), _window_width-30, _window_width/4+30)];
        _platformV.backgroundColor = [UIColor clearColor];
        _platformV.shareEvent = ^(NSString *type) {
            sharetype = type;
            NSLog(@"share:%@",type);
        };
    }
    return _platformV;
}

-(UIButton *)publishBtn {
    if (!_publishBtn) {
        _publishBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        _publishBtn.frame = CGRectMake(30, _platformV.bottom+20, _window_width-60, 50);
        _publishBtn.frame = CGRectMake(30, ((_shopView) ? (_shopView.bottom+20): (_videoChargeView?(_videoChargeView.bottom+20):(_videoClassView.bottom+20))), _window_width-60, 50);
        [_publishBtn setTitle:YZMsg(@"确认发布") forState:0];
        [_publishBtn setTitleColor:[UIColor whiteColor] forState:0];
        _publishBtn.backgroundColor = Pink_Cor;
        _publishBtn.layer.masksToBounds = YES;
        _publishBtn.layer.cornerRadius = 5;
        [_publishBtn addTarget:self action:@selector(clickPublishBtn) forControlEvents:UIControlEventTouchUpInside];
    }
    return _publishBtn;
}

#pragma mark - 导航

- (void)clickNaviLeftBtn {
    //这里重写父类 不要写 super
    
    YBWeakSelf;
    NSDictionary *contentDic = @{@"title":YZMsg(@"提示"),@"msg":YZMsg(@"是否放弃发布此条视频"),@"left":YZMsg(@"取消"),@"right":YZMsg(@"确定")};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            [weakSelf doSureClikc];
        }
    }];
}

-(void)doSureClikc {
    [[NSFileManager defaultManager] removeItemAtPath:_recordResult.videoPath error:nil];
    if (_waterVideoPath) {
        [[NSFileManager defaultManager] removeItemAtPath:_waterVideoPath error:nil];
    }
    [self root];
}
#pragma mark -添加广告
-(void)isadBtnClick{
    _isAdBtn.selected = !_isAdBtn.selected;
    if (_isAdBtn.selected) {
        self.adView.hidden = NO;
        [_bgScrollView bringSubviewToFront:self.adView];
        if (!_shopView) {
            _publishBtn.frame = CGRectMake(30, ((_adView) ? (_adView.bottom+20): (_videoChargeView?(_videoChargeView.bottom+20):(_videoClassView.bottom+20))), _window_width-60, 50);
        }

    }else{
        self.adView.hidden = YES;
        if (!_shopView) {
            _publishBtn.frame = CGRectMake(30, ((_videoChargeView?(_videoChargeView.bottom+20):(_videoClassView.bottom+20))), _window_width-60, 50);
        }else{
            [_bgScrollView bringSubviewToFront:self.shopView];

        }
    }
}
#pragma mark -标签
- (void)addTopicClick{
    videoTopicVC *topic = [[videoTopicVC alloc]init];
    YBWeakSelf;
    topic.block = ^(NSDictionary * _Nonnull dic) {
        weakSelf.topicDic = dic;
        weakSelf.topicLabel.text = minstr([dic valueForKey:@"name"]);
    };
    topic.modalPresentationStyle = 0;
    [self presentViewController:topic animated:YES completion:nil];
}
#pragma mark - 视频分类
-(void)clikcVideoClassBtn {
    
    YBVideoClassVC *videoClass = [[YBVideoClassVC alloc]init];
    YBWeakSelf;
    videoClass.videoClassEvent = ^(NSDictionary * _Nonnull dic) {
        weakSelf.videoClassDic = dic;
        weakSelf.videoClassL.text = minstr([dic valueForKey:@"title"]);
    };
    videoClass.modalPresentationStyle = 0;
    [self presentViewController:videoClass animated:YES completion:nil];
    
}
#pragma mark - 收费
-(void)clikcVideoChargeBtn {
    YBWeakSelf;
    [YBSetChargeView showInputView:_videoChargeNum complete:^(NSString * _Nonnull priceStr) {
        weakSelf.videoChargeNum = priceStr;
        weakSelf.videoChargeL.text = [NSString stringWithFormat:@"%@%@",priceStr,[common name_coin]];
    }];
}
#pragma mark - 商品
- (void)addShopClick{
    YBWeakSelf;
    /*
    YBVideoAddGoodsVC *goodsVC = [[YBVideoAddGoodsVC alloc]init];
    goodsVC.goodsInfo = _shopDic;
    goodsVC.addGoodsEvent = ^(NSDictionary * _Nonnull originDic, NSString * _Nonnull formatJson) {
        weakSelf.shopLabel.text = minstr([originDic valueForKey:@"name"]);
//        weakSelf.goodsThumb = [originDic valueForKey:@"thumb"];
        weakSelf.shopDic = originDic.mutableCopy;
        weakSelf.shopJsonStr = formatJson;
    };
    [[XGGAppDelegate sharedAppDelegate]pushViewController:goodsVC animated:YES];
    */
    
//    YBGoodsListVC *gVC = [[YBGoodsListVC alloc]init];
//    gVC.enterType = GoodsEnterType_Publish;
//    gVC.touserID = [Config getOwnID];
//    gVC.selGoodsList = @[_shopDic];
//    gVC.publishEvent = ^(BOOL isSel, NSDictionary *goodsDic) {
//        if (isSel) {
//            weakSelf.shopLabel.text = minstr([goodsDic valueForKey:@"name"]);
//            weakSelf.shopDic = goodsDic.mutableCopy;
//            weakSelf.goodsID = minstr([goodsDic valueForKey:@"id"]);
//        }else {
//            weakSelf.shopLabel.text = YZMsg(@"添加商品");
//            weakSelf.shopDic = @{}.mutableCopy;
//            weakSelf.goodsID = @"0";
//        }
//    };
//    [[XGGAppDelegate sharedAppDelegate]pushViewController:gVC animated:YES];
    RelationVideoGoodsVC *vc = [[RelationVideoGoodsVC alloc]init];
    vc.selectID = _goodsID;
    vc.block = ^(NSString * _Nonnull goodsName, NSString * _Nonnull goodsid, NSString * _Nonnull type) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.goodsID = goodsid;
            _shopLabel.text = goodsName;
//            Goodstype = type;
        });

    };
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

}
#pragma mark - 选择封面
-(void)clickCovderBtn {
    if (!_videoAsset && [PublicObj checkNull:_recordResult.videoPath]) {
        [MBProgressHUD showPop:YZMsg(@"未获取到封面")];
        return;
    }
    YBPublishCoverVC *pvc = [[YBPublishCoverVC alloc]init];
    pvc.selIndex = _coverSelIndex;
//    pvc.videoAsset = _videoAsset;
    pvc.videoPath = _recordResult.videoPath;
    pvc.coverEvent = ^(int selIndex, UIImage *selImage) {
        _coverSelIndex = selIndex;
        _videoCoverIV.image = selImage;
    };
    pvc.modalPresentationStyle = 0;
    [self presentViewController:pvc animated:YES completion:nil];
    
}

- (void)dealloc {
    [_livePlayer removeVideoWidget];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [[AFNetworkReachabilityManager sharedManager] stopMonitoring];
}

@end
