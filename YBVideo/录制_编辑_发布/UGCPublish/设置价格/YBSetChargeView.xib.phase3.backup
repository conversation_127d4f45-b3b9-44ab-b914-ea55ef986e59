<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="YBSetChargeView">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="li8-n0-xFP" userLabel="bgView">
                    <rect key="frame" x="52.5" y="211" width="270" height="178"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="请输入收费金额" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="60R-4R-jqa" userLabel="标题">
                            <rect key="frame" x="81.5" y="18" width="107.5" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="Nrw-xc-XBw"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                            <color key="textColor" red="0.19607843137254902" green="0.19607843137254902" blue="0.19607843137254902" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Zar-bw-g0y" userLabel="取消">
                            <rect key="frame" x="235" y="10" width="30" height="30"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="30" id="KRR-N7-Qd4"/>
                                <constraint firstAttribute="width" constant="30" id="wS2-V3-c5G"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <inset key="contentEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                            <state key="normal" image="screen_close.png">
                                <color key="titleColor" red="0.91764705879999997" green="0.2156862745" blue="0.49803921569999998" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="clickCancleBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="cgK-EP-0Rr"/>
                            </connections>
                        </button>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="nz9-l6-1TD" userLabel="tf" customClass="MyTextField">
                            <rect key="frame" x="35" y="50" width="200" height="40"/>
                            <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="6t6-5B-8Qv"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="5"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                    <color key="value" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </textField>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="*收取金额不可为小数" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zQC-ag-QWU" userLabel="提示语">
                            <rect key="frame" x="35" y="100" width="97" height="12"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="12" id="9fN-5d-twj"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="10"/>
                            <color key="textColor" red="0.58823529411764708" green="0.58823529411764708" blue="0.58823529411764708" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SB1-ta-R6Z" userLabel="线">
                            <rect key="frame" x="0.0" y="132" width="270" height="1"/>
                            <color key="backgroundColor" red="0.86274509803921573" green="0.86274509803921573" blue="0.86274509803921573" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="aOj-cq-CJT"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Z7T-ET-Csu" userLabel="确认">
                            <rect key="frame" x="0.0" y="133" width="270" height="45"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="45" id="cI2-D7-hMv"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <state key="normal" title="确认">
                                <color key="titleColor" red="0.91764705879999997" green="0.2156862745" blue="0.49803921569999998" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="clickSureBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="Zo3-Bo-xss"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="Z7T-ET-Csu" firstAttribute="top" secondItem="SB1-ta-R6Z" secondAttribute="bottom" id="4b2-YO-fLL"/>
                        <constraint firstAttribute="bottom" secondItem="Z7T-ET-Csu" secondAttribute="bottom" id="8WM-ON-6h7"/>
                        <constraint firstItem="Z7T-ET-Csu" firstAttribute="centerX" secondItem="li8-n0-xFP" secondAttribute="centerX" id="9aJ-Jv-AWs"/>
                        <constraint firstItem="Zar-bw-g0y" firstAttribute="top" secondItem="li8-n0-xFP" secondAttribute="top" constant="10" id="GUU-Ek-FcU"/>
                        <constraint firstItem="60R-4R-jqa" firstAttribute="centerX" secondItem="li8-n0-xFP" secondAttribute="centerX" id="H20-Yd-TD9"/>
                        <constraint firstItem="nz9-l6-1TD" firstAttribute="centerX" secondItem="li8-n0-xFP" secondAttribute="centerX" id="Mtk-YE-tRq"/>
                        <constraint firstItem="zQC-ag-QWU" firstAttribute="top" secondItem="nz9-l6-1TD" secondAttribute="bottom" constant="10" id="Noy-cI-wis"/>
                        <constraint firstAttribute="trailing" secondItem="Zar-bw-g0y" secondAttribute="trailing" constant="5" id="XHV-sD-7cb"/>
                        <constraint firstItem="SB1-ta-R6Z" firstAttribute="width" secondItem="li8-n0-xFP" secondAttribute="width" id="ZEe-Ub-cXo"/>
                        <constraint firstItem="SB1-ta-R6Z" firstAttribute="top" secondItem="zQC-ag-QWU" secondAttribute="bottom" constant="20" id="ZPC-E5-tSQ"/>
                        <constraint firstItem="Z7T-ET-Csu" firstAttribute="width" secondItem="li8-n0-xFP" secondAttribute="width" id="ZZT-5F-KR4"/>
                        <constraint firstItem="nz9-l6-1TD" firstAttribute="top" secondItem="60R-4R-jqa" secondAttribute="bottom" constant="12" id="aFJ-aD-G1W"/>
                        <constraint firstItem="zQC-ag-QWU" firstAttribute="leading" secondItem="nz9-l6-1TD" secondAttribute="leading" id="ld0-qK-qCf"/>
                        <constraint firstItem="SB1-ta-R6Z" firstAttribute="centerX" secondItem="li8-n0-xFP" secondAttribute="centerX" id="qe5-NK-q4u"/>
                        <constraint firstItem="nz9-l6-1TD" firstAttribute="width" secondItem="li8-n0-xFP" secondAttribute="width" multiplier="0.74" id="tnh-20-11P"/>
                        <constraint firstItem="60R-4R-jqa" firstAttribute="top" secondItem="li8-n0-xFP" secondAttribute="top" constant="18" id="uT9-DU-nR1"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="li8-n0-xFP" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" multiplier="0.9" id="M6t-c4-UWh"/>
                <constraint firstItem="li8-n0-xFP" firstAttribute="width" secondItem="iN0-l3-epB" secondAttribute="width" multiplier="0.72" id="cx5-r7-aPe"/>
                <constraint firstItem="li8-n0-xFP" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="eAc-GI-5pl"/>
            </constraints>
            <connections>
                <outlet property="bgView" destination="li8-n0-xFP" id="b7j-2b-vGZ"/>
                <outlet property="chargeTF" destination="nz9-l6-1TD" id="6CF-kZ-l49"/>
                <outlet property="sureBtn" destination="Z7T-ET-Csu" id="dY5-xr-SmG"/>
                <outlet property="tipsL" destination="zQC-ag-QWU" id="95M-cv-IDI"/>
                <outlet property="titleL" destination="60R-4R-jqa" id="0Nd-lP-fGG"/>
            </connections>
            <point key="canvasLocation" x="139" y="115"/>
        </view>
    </objects>
    <resources>
        <image name="screen_close.png" width="14" height="14"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
