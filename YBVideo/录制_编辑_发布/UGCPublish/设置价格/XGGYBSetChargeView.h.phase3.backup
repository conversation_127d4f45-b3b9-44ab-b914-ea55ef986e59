//
//  YBSetChargeView.h
//  YBVideo
//
//  Created by YB007 on 2019/11/27.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^YBSetChargeBlock)(NSString *priceStr);

@interface YBSetChargeView : UIView
@property (weak, nonatomic) IBOutlet UIView *bgView;

@property(nonatomic,copy)YBSetChargeBlock chargeEvent;

@property (weak, nonatomic) IBOutlet MyTextField *chargeTF;


@property (weak, nonatomic) IBOutlet UILabel *titleL;

@property (weak, nonatomic) IBOutlet UILabel *tipsL;

@property (weak, nonatomic) IBOutlet UIButton *sureBtn;


+(instancetype)showInputView:(NSString *)price complete:(YBSetChargeBlock)complete;

@end

NS_ASSUME_NONNULL_END
