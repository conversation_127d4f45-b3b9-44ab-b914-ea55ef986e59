//
//  YBPublishCoverVC.m
//  YBVideo
//
//  Created by YB007 on 2020/9/16.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBPublishCoverVC.h"
#import <TXLiteAVSDK_Professional/TXVideoEditer.h>

@interface YBPublishCoverVC ()
{
    BOOL _isContinue;
    int _imageNum;
    NSMutableArray *_imageList;
    NSMutableArray *_btnArray;
}

@property(nonatomic,strong)UIImageView *topCoverIV;
@property(nonatomic,strong)UIScrollView *scrollView;
@property(nonatomic,strong)UIView *botView;

@end

@implementation YBPublishCoverVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.naviView.hidden = YES;
    
    self.view.backgroundColor = [UIColor blackColor];
    
    [self.view addSubview:self.topCoverIV];
    [self.view addSubview:self.scrollView];
    [self.view addSubview:self.botView];
    
    [_topCoverIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.view.mas_width).offset(-80);
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.view.mas_top).offset(statusbarHeight+40);
    }];
    [_scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(70);
        make.width.equalTo(self.view.mas_width).offset(-20);
        make.centerX.equalTo(self.view);
        make.top.equalTo(_topCoverIV.mas_bottom).offset(17);
    }];
    [_botView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
        make.top.equalTo(_scrollView.mas_bottom).offset(15);
        make.bottom.equalTo(self.view.mas_bottom).offset(-ShowDiff-10);
        make.width.centerX.equalTo(self.view);
    }];
    
    _btnArray = [NSMutableArray array];
    
    NSURL *avUrl = [NSURL fileURLWithPath:_videoPath];
    self.videoAsset = [AVAsset assetWithURL:avUrl];
    
}

- (void)setVideoAsset:(AVAsset *)videoAsset {
    _videoAsset = videoAsset;
    [self getVdieoCoverList];
}

-(void)getVdieoCoverList {
    //显示微缩图列表
    _imageList = [NSMutableArray new];
    _imageNum = 12;
    _isContinue = YES;
    [TXVideoInfoReader getSampleImages:_imageNum videoAsset:_videoAsset progress:^BOOL(int number, UIImage *image) {
        if (!_isContinue || !image) {
            return NO;
        }else{
            dispatch_async(dispatch_get_main_queue(), ^{
                if (!_isContinue) {
                    return;
                }
                if (number == 1) {
                    for (int i = 0; i < _imageNum; i++) {
                        [_imageList addObject:image];
                    }
                    [self setUpListUI];
                } else {
                    if (_imageList.count == 0) {
                        for (int i = 0; i < _imageNum; i++) {
                            [_imageList addObject:image];
                        }
                        [self setUpListUI];
                    }
                    _imageList[number-1] = image;
                }
            });
            return YES;
        }
    }];
}

-(void)setUpListUI {
    NSLog(@"=======:%@===%lu",_imageList,(unsigned long)_imageList.count);
    [_scrollView removeAllSubViews];
    [_btnArray removeAllObjects];
    _scrollView.contentSize = CGSizeMake(50*_imageList.count, 0);
    MASViewAttribute *leftMas = _scrollView.mas_left;
    for (int i=0; i<_imageList.count; i++) {
        UIButton *coverBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        coverBtn.imageView.contentMode = UIViewContentModeScaleAspectFill;
        coverBtn.imageView.clipsToBounds = YES;
        coverBtn.imageEdgeInsets = UIEdgeInsetsMake(1, 1, 1, 1);
        [coverBtn setImage:_imageList[i] forState:0];
        coverBtn.tag = 10086+i;
        [coverBtn addTarget:self action:@selector(clickCoverList:) forControlEvents:UIControlEventTouchUpInside];
        [_scrollView addSubview:coverBtn];
        [_btnArray addObject:coverBtn];
        [coverBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(leftMas);
            make.width.mas_equalTo(50);
            make.height.mas_equalTo(70);
            make.centerY.equalTo(_scrollView);
        }];
        leftMas = coverBtn.mas_right;
        coverBtn.layer.borderWidth = 2;
        coverBtn.layer.borderColor = UIColor.clearColor.CGColor;
        if (_selIndex == i) {
            coverBtn.layer.borderColor = Pink_Cor.CGColor;
            _topCoverIV.image = _imageList[i];
        }
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        for (int i=0; i<_imageList.count; i++) {
            /*
            UIImage *image = _imageList[i];
            CGFloat fixelW = CGImageGetWidth(image.CGImage);
            CGFloat fixelH = CGImageGetHeight(image.CGImage);
            NSLog(@"rkrate:======:%f====%f:=====%.2f",fixelW,fixelH,fixelH/fixelW);
            */
            UIButton *cBtn = _btnArray[i];
            [cBtn setImage:_imageList[i] forState:0];
        }
        _topCoverIV.image = _imageList[_selIndex];
    });
    
}
-(void)clickCoverList:(UIButton *)sender {
    for (UIButton *btn in _btnArray) {
        btn.layer.borderColor = UIColor.clearColor.CGColor;
    }
    sender.layer.borderColor = Pink_Cor.CGColor;
    _selIndex = (int)sender.tag-10086;
    _topCoverIV.image = _imageList[_selIndex];
}

- (UIImageView *)topCoverIV {
    if (!_topCoverIV) {
        _topCoverIV = [[UIImageView alloc]init];
        _topCoverIV.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _topCoverIV;;
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc]init];
    }
    return _scrollView;
}
- (UIView *)botView {
    if (!_botView) {
        _botView = [[UIView alloc]init];
        
        UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [closeBtn addTarget:self action:@selector(clickCloseBtn) forControlEvents:UIControlEventTouchUpInside];
        [closeBtn setImage:[UIImage imageNamed:@"封面-关闭"] forState:0];
        [_botView addSubview:closeBtn];
        [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(40);
            make.left.equalTo(_botView.mas_left).offset(20);
            make.centerY.equalTo(_botView);
        }];
        
        UILabel *titleL = [[UILabel alloc]init];
        titleL.font = SYS_Font(14);
        titleL.textColor = [UIColor whiteColor];
        titleL.text = YZMsg(@"封面");
        [_botView addSubview:titleL];
        [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.centerY.equalTo(_botView);
        }];
        
        UIButton *finishBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [finishBtn addTarget:self action:@selector(clickFinishBtn) forControlEvents:UIControlEventTouchUpInside];
        [finishBtn setImage:[UIImage imageNamed:@"封面-完成"] forState:0];
        [_botView addSubview:finishBtn];
        [finishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(40);
            make.right.equalTo(_botView.mas_right).offset(-20);
            make.centerY.equalTo(_botView);
        }];
        
    }
    return _botView;
}
-(void)clickCloseBtn {
    [self dismissViewControllerAnimated:YES completion:nil];
}
-(void)clickFinishBtn {
    if (_selIndex >= _imageList.count) {
        [MBProgressHUD showPop:YZMsg(@"未获取到封面")];
        return;
    }
    if (self.coverEvent) {
        self.coverEvent(_selIndex, _imageList[_selIndex]);
    }
    [self dismissViewControllerAnimated:YES completion:nil];
}
@end
