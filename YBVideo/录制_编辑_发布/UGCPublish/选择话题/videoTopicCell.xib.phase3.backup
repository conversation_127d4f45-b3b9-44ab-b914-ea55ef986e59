<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14868" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14824"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="videoTopicCELL" id="KGk-i7-Jjw" customClass="videoTopicCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="video_topic.png" translatesAutoresizingMaskIntoConstraints="NO" id="IVN-KK-D8D">
                        <rect key="frame" x="15" y="15.5" width="13" height="13"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="IVN-KK-D8D" secondAttribute="height" multiplier="1:1" id="nBh-Rl-ie0"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Br1-VT-mDT">
                        <rect key="frame" x="15" y="43" width="290" height="1"/>
                        <color key="backgroundColor" white="1" alpha="0.059999999999999998" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="61l-5o-6ve"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="we" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HJK-jL-GSO">
                        <rect key="frame" x="36" y="13" width="19.5" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <color key="backgroundColor" red="0.082352941176470587" green="0.062745098039215685" blue="0.17254901960784313" alpha="1" colorSpace="calibratedRGB"/>
                <constraints>
                    <constraint firstItem="Br1-VT-mDT" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="PdB-2N-nJz"/>
                    <constraint firstItem="IVN-KK-D8D" firstAttribute="leading" secondItem="Br1-VT-mDT" secondAttribute="leading" id="VAa-Sf-WfK"/>
                    <constraint firstItem="IVN-KK-D8D" firstAttribute="height" secondItem="H2p-sc-9uM" secondAttribute="height" multiplier="0.3" id="c3b-3s-L1j"/>
                    <constraint firstItem="IVN-KK-D8D" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="hqX-z8-Vjm"/>
                    <constraint firstItem="HJK-jL-GSO" firstAttribute="centerY" secondItem="IVN-KK-D8D" secondAttribute="centerY" id="kW0-4s-kYy"/>
                    <constraint firstItem="HJK-jL-GSO" firstAttribute="leading" secondItem="IVN-KK-D8D" secondAttribute="trailing" constant="8" id="lOY-Iw-7bk"/>
                    <constraint firstAttribute="bottom" secondItem="Br1-VT-mDT" secondAttribute="bottom" id="yOb-1d-xPn"/>
                    <constraint firstAttribute="trailing" secondItem="Br1-VT-mDT" secondAttribute="trailing" constant="15" id="ybC-NF-6FF"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="imgView" destination="IVN-KK-D8D" id="x9P-Oz-D5Z"/>
                <outlet property="topicTitleL" destination="HJK-jL-GSO" id="Tdq-yr-1ms"/>
            </connections>
            <point key="canvasLocation" x="-85" y="73"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="video_topic.png" width="12" height="12"/>
    </resources>
</document>
