//
//  videoTopicVC.m
//  YBVideo
//
//  Created by IOS1 on 2019/7/2.
//  Copyright © 2019 cat. All rights reserved.
//

#import "videoTopicVC.h"
#import "HXSearchBar.h"
#import "videoTopicCell.h"

@interface videoTopicVC ()<UISearchBarDelegate,UITableViewDataSource,UITableViewDelegate>{
    HXSearchBar *searchBars;
    NSMutableArray *allArray;
    int p;
}
@property (nonatomic,strong) UITableView *topicTableView;

@end

@implementation videoTopicVC
//添加搜索条
- (void)addSearchBar {
    UIView *bg = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, statusbarHeight+70)];
    bg.backgroundColor = CellRow_Cor;
    [self.view addSubview:bg];
    //加上 搜索栏
    searchBars = [[HXSearchBar alloc] initWithFrame:CGRectMake(10,10+statusbarHeight, self.view.frame.size.width -20,60)];
    searchBars.backgroundColor = CellRow_Cor;
    searchBars.delegate = self;
    //输入框提示
    searchBars.placeholder = YZMsg(@"搜索话题标签");
    //光标颜色
    searchBars.cursorColor = [UIColor whiteColor];
    //TextField
    searchBars.searchBarTextField.layer.cornerRadius = 18;
    searchBars.searchBarTextField.layer.masksToBounds = YES;
    //searchBars.searchBarTextField.layer.borderColor = [UIColor grayColor].CGColor;
    //searchBars.searchBarTextField.layer.borderWidth = 1.0;
    searchBars.searchBarTextField.backgroundColor = RGB_COLOR(@"#201F37", 1);
    searchBars.searchBarTextField.textColor = [UIColor whiteColor];
    searchBars.searchBarTextField.font = [UIFont systemFontOfSize:14];
    //清除按钮图标
    //searchBar.clearButtonImage = [UIImage imageNamed:@"demand_delete"];
    UIButton *clearBtn = [searchBars.searchBarTextField valueForKey:@"_clearButton"];
    [clearBtn addTarget:self action:@selector(clickClearBtn) forControlEvents:UIControlEventTouchUpInside];
    //去掉取消按钮灰色背景
    searchBars.hideSearchBarBackgroundImage = YES;
    [searchBars becomeFirstResponder];
    [bg addSubview:searchBars];
    
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(15, bg.bottom, _window_width-15, 50)];
    label.text = YZMsg(@"所有标签");
    label.textColor = RGB_COLOR(@"#959595", 1);
    label.font = [UIFont systemFontOfSize:12];
    [self.view addSubview:label];
}
- (void)clickClearBtn{
    [self requestData];

}
#pragma mark - 搜索代理
- (void)searchBarTextDidBeginEditing:(UISearchBar *)searchBar {
    HXSearchBar *sear = (HXSearchBar *)searchBar;
    sear.cancleButton.backgroundColor = [UIColor clearColor];
    [sear.cancleButton setTitle:YZMsg(@"取消") forState:UIControlStateNormal];
    [sear.cancleButton setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
    sear.cancleButton.titleLabel.font = [UIFont systemFontOfSize:16];
    
}
//文字改变
-(void)searchBar:(UISearchBar *)searchBar textDidChange:(NSString *)searchText{
    if (searchText.length>0) {
        [self searchData];
    }else{
        [self requestData];
    }
}
//取消按钮点击的回调
- (void)searchBarCancelButtonClicked:(UISearchBar *)searchBar {
    searchBar.showsCancelButton = NO;
    searchBar.text = nil;
    [self.view endEditing:YES];
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (UITableView *)topicTableView{
    if (!_topicTableView) {
        _topicTableView = [[UITableView alloc]initWithFrame:CGRectMake(0, statusbarHeight+120, _window_width, _window_height-120-statusbarHeight)];
        _topicTableView.backgroundColor = Normal_Color;
        _topicTableView.delegate = self;
        _topicTableView.dataSource = self;
        _topicTableView.separatorStyle = 0;
        [self.view addSubview:_topicTableView];
//        _topicTableView.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
//            p = 1;
//            [self requestData];
//        }];
//        _topicTableView.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
//            p++;
//            [self requestData];
//        }];
    }
    return _topicTableView;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.naviView.hidden = YES;
    
    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    self.view.backgroundColor = Normal_Color;
    allArray = [NSMutableArray array];
    p = 1;
    [self addSearchBar];
    [self.view addSubview:self.topicTableView];
    [self requestData];
}
- (void)requestData{
    
    [YBNetworking postWithUrl:@"Label.getList" Dic:nil Suc:^(int code, id info, NSString *msg) {
//        [_topicTableView.mj_header endRefreshing];
//        [_topicTableView.mj_footer endRefreshing];
        if (code == 0) {
            [allArray removeAllObjects];
            NSArray *infoA = [NSArray arrayWithArray:info];
            [allArray addObjectsFromArray:infoA];
            [_topicTableView reloadData];
        }else {
            [MBProgressHUD showPop:msg];
        }
        
    } Fail:^(id fail) {
//        [_topicTableView.mj_header endRefreshing];
//        [_topicTableView.mj_footer endRefreshing];
    }];
}
- (void)searchData{

    [YBNetworking postWithUrl:@"Label.SearchLabel" Dic:@{@"key":searchBars.text} Suc:^(int code, id info, NSString *msg) {
//        [_topicTableView.mj_header endRefreshing];
//        [_topicTableView.mj_footer endRefreshing];
        if (code == 0) {
            [allArray removeAllObjects];
            NSArray *infoA = [NSArray arrayWithArray:info];
            [allArray addObjectsFromArray:infoA];
            [_topicTableView reloadData];
        }else {
            [MBProgressHUD showPop:msg];
        }
        
    } Fail:^(id fail) {
//        [_topicTableView.mj_header endRefreshing];
//        [_topicTableView.mj_footer endRefreshing];
    }];
    
}
-(void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self.view endEditing:YES];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return allArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    videoTopicCell *cell = [tableView dequeueReusableCellWithIdentifier:@"videoTopicCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"videoTopicCell" owner:nil options:nil] lastObject];
    }
    cell.topicTitleL.text = minstr([allArray[indexPath.row] valueForKey:@"name"]);
    cell.backgroundColor = CellRow_Cor;
    cell.contentView.backgroundColor = CellRow_Cor;
    return cell;

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (self.block) {
        self.block(allArray[indexPath.row]);
        [self.view endEditing:YES];
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
