//
//  YBVideoClassVC.m
//  YBVideo
//
//  Created by YB007 on 2019/11/27.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBVideoClassVC.h"
#import "HXSearchBar.h"
#import "videoTopicCell.h"

@interface YBVideoClassVC ()<UISearchBarDelegate,UITableViewDataSource,UITableViewDelegate>{
    HXSearchBar *searchBars;
    NSMutableArray *allArray;
    int p;
}
@property (nonatomic,strong) UITableView *videoClassTableView;

@end

@implementation YBVideoClassVC

//添加搜索条
- (void)addSearchBar {
    UIView *bg = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, statusbarHeight+70)];
    bg.backgroundColor = CellRow_Cor;
    [self.view addSubview:bg];
    //加上 搜索栏
    searchBars = [[HXSearchBar alloc] initWithFrame:CGRectMake(10,10+statusbarHeight, self.view.frame.size.width -20,60)];
    searchBars.backgroundColor = CellRow_Cor;
    searchBars.delegate = self;
    //输入框提示
    searchBars.placeholder = YZMsg(@"搜索分类");
    //光标颜色
    searchBars.cursorColor = [UIColor whiteColor];
    //TextField
    searchBars.searchBarTextField.layer.cornerRadius = 18;
    searchBars.searchBarTextField.layer.masksToBounds = YES;
    //searchBars.searchBarTextField.layer.borderColor = [UIColor grayColor].CGColor;
    //searchBars.searchBarTextField.layer.borderWidth = 1.0;
    searchBars.searchBarTextField.backgroundColor = RGB_COLOR(@"#201F37", 1);
    searchBars.searchBarTextField.textColor = [UIColor whiteColor];
    searchBars.searchBarTextField.font = [UIFont systemFontOfSize:14];
    //清除按钮图标
    //searchBar.clearButtonImage = [UIImage imageNamed:@"demand_delete"];
    UIButton *clearBtn = [searchBars.searchBarTextField valueForKey:@"_clearButton"];
    [clearBtn addTarget:self action:@selector(clickClearBtn) forControlEvents:UIControlEventTouchUpInside];
    //去掉取消按钮灰色背景
    searchBars.hideSearchBarBackgroundImage = YES;
    [searchBars becomeFirstResponder];
    [bg addSubview:searchBars];
    
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(15, bg.bottom, _window_width-15, 50)];
    label.text = YZMsg(@"所有分类");
    label.textColor = RGB_COLOR(@"#959595", 1);
    label.font = [UIFont systemFontOfSize:12];
    [self.view addSubview:label];
}
- (void)clickClearBtn{
    [self requestData];

}
#pragma mark - 搜索代理
- (void)searchBarTextDidBeginEditing:(UISearchBar *)searchBar {
    HXSearchBar *sear = (HXSearchBar *)searchBar;
    sear.cancleButton.backgroundColor = [UIColor clearColor];
    [sear.cancleButton setTitle:YZMsg(@"取消") forState:UIControlStateNormal];
    [sear.cancleButton setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
    sear.cancleButton.titleLabel.font = [UIFont systemFontOfSize:16];
    
}
//文字改变
-(void)searchBar:(UISearchBar *)searchBar textDidChange:(NSString *)searchText{
    if (searchText.length>0) {
        [self searchData];
    }else{
        [self requestData];
    }
}
//取消按钮点击的回调
- (void)searchBarCancelButtonClicked:(UISearchBar *)searchBar {
    searchBar.showsCancelButton = NO;
    searchBar.text = nil;
    [self.view endEditing:YES];
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (UITableView *)videoClassTableView{
    if (!_videoClassTableView) {
        _videoClassTableView = [[UITableView alloc]initWithFrame:CGRectMake(0, statusbarHeight+120, _window_width, _window_height-120-statusbarHeight)];
        _videoClassTableView.backgroundColor = Normal_Color;
        _videoClassTableView.delegate = self;
        _videoClassTableView.dataSource = self;
        _videoClassTableView.separatorStyle = 0;
        [self.view addSubview:_videoClassTableView];
//        _videoClassTableView.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
//            p = 1;
//            [self requestData];
//        }];
//        _videoClassTableView.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
//            p++;
//            [self requestData];
//        }];
    }
    return _videoClassTableView;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.naviView.hidden = YES;
    
    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    self.view.backgroundColor = Normal_Color;
    allArray = [NSMutableArray array];
    p = 1;
    [self addSearchBar];
    [self.view addSubview:self.videoClassTableView];
    [self requestData];
}
- (void)requestData{
    
    [YBNetworking postWithUrl:@"Video.getClassLists" Dic:nil Suc:^(int code, id info, NSString *msg) {
//        [_videoClassTableView.mj_header endRefreshing];
//        [_videoClassTableView.mj_footer endRefreshing];
        if (code == 0) {
            [allArray removeAllObjects];
            NSArray *infoA = [NSArray arrayWithArray:info];
            [allArray addObjectsFromArray:infoA];
            [_videoClassTableView reloadData];
        }else {
            [MBProgressHUD showPop:msg];
        }
        
    } Fail:^(id fail) {
//        [_videoClassTableView.mj_header endRefreshing];
//        [_videoClassTableView.mj_footer endRefreshing];
    }];
}
- (void)searchData{

    [YBNetworking postWithUrl:@"Video.searchClassLists" Dic:@{@"keywords":searchBars.text} Suc:^(int code, id info, NSString *msg) {
//        [_videoClassTableView.mj_header endRefreshing];
//        [_videoClassTableView.mj_footer endRefreshing];
        if (code == 0) {
            [allArray removeAllObjects];
            NSArray *infoA = [NSArray arrayWithArray:info];
            [allArray addObjectsFromArray:infoA];
            [_videoClassTableView reloadData];
        }else {
            [MBProgressHUD showPop:msg];
        }
        
    } Fail:^(id fail) {
//        [_videoClassTableView.mj_header endRefreshing];
//        [_videoClassTableView.mj_footer endRefreshing];
    }];
    
}
-(void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self.view endEditing:YES];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return allArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    videoTopicCell *cell = [tableView dequeueReusableCellWithIdentifier:@"videoTopicCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"videoTopicCell" owner:nil options:nil] lastObject];
    }
    cell.imgView.hidden = YES;
    [cell.topicTitleL mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cell.contentView).offset(15);
        make.centerY.equalTo(cell.contentView);
    }];
    cell.topicTitleL.text = minstr([allArray[indexPath.row] valueForKey:@"title"]);
    cell.backgroundColor = CellRow_Cor;
    cell.contentView.backgroundColor = CellRow_Cor;
    
    return cell;

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (self.videoClassEvent) {
        self.videoClassEvent(allArray[indexPath.row]);
        [self.view endEditing:YES];
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

@end
