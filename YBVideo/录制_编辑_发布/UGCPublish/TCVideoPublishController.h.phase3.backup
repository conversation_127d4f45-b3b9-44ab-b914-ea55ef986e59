
#import <TXLiteAVSDK_Professional/TXVideoEditerTypeDef.h>

#define kRecordType_Camera 0
#define kRecordType_Play 1

@interface TCVideoPublishController : YBBaseViewController<UITextViewDelegate>

@property(nonatomic,strong)NSString *musicID;    //选取音乐的ID
@property(nonatomic,strong)NSString *waterVideoPath;    //选取音乐的ID

@property(nonatomic,assign)BOOL isTakeSame;     //拍摄同款
@property (strong,nonatomic) AVAsset  *videoAsset;

- (instancetype)initWithPath:(NSString *)videoPath videoMsg:(TXVideoInfo *) videoMsg;

@end
