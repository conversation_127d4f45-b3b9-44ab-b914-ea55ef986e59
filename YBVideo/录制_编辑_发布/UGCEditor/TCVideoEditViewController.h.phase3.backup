//
//  TCVideoEditViewController.h
//  TCLVBIMDemo
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2017/4/10.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>
#import "TCVideoRecordViewController.h"
@interface TCVideoEditViewController : YBBaseViewController
@property (strong,nonatomic) NSString *videoPath;

@property (strong,nonatomic) AVAsset  *videoAsset;

@property(nonatomic,strong)NSString *musicPath;
@property(nonatomic,strong)NSString *musicID;     //选取音乐的ID
@property(nonatomic,assign)BOOL haveBGM;          //yes-开拍时候选择了音乐  no-未选择音乐直接开拍
@property(nonatomic,assign)BOOL isAlbum;         //是否是相册视频（相册视频不要添加倒放）

@property(nonatomic,assign)RecordType recordType; //拍摄类型(普通拍摄--合拍  注意:拍摄同款属于普通拍摄)
@property(nonatomic,assign)BOOL isTakeSame;       //拍摄同款


@end
