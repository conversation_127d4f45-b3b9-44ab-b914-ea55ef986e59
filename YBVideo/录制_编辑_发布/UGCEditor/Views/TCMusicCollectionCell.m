//
//  TCMusicCollectionCell.m
//  DeviceManageIOSApp
//
//  Created by rushanting on 2017/5/15.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import "TCMusicCollectionCell.h"
#import "ColorMacro.h"
//#import "UIView+AdditionsX12.h"

@implementation TCMusicCollectionCell


@end

@implementation TCMusicCollectionCell
{

}

- (id)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = UIColorFromRGB(0x181818);
        _iconView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"voice_nor"]];
        [self.contentView addSubview:_iconView];
        
        _songNameLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _songNameLabel.text = YZMsg(@"歌名");
        _songNameLabel.textColor = UIColorFromRGB(0x777777);
        _songNameLabel.textAlignment = NSTextAlignmentCenter;
        _songNameLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:10];
        //_songNameLabel.font = [UIFont systemFontOfSize:10];
        _songNameLabel.numberOfLines = 2;
        _songNameLabel.lineBreakMode = NSLineBreakByWordWrapping;
        [self.contentView addSubview:_songNameLabel];
        
        _authorNameLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _authorNameLabel.textColor = UIColorFromRGB(0x555555);
        _authorNameLabel.text = YZMsg(@"作者");
        _authorNameLabel.textAlignment = NSTextAlignmentCenter;
        _authorNameLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:10];
        //_authorNameLabel.font = [UIFont systemFontOfSize:10];
        [self.contentView addSubview:_authorNameLabel];
        
        _deleteBtn = [UIButton new];
        _deleteBtn.backgroundColor = UIColor.darkGrayColor;
        _deleteBtn.alpha = 0.7;
        [_deleteBtn setImage:[UIImage imageNamed:@"video_record_close"] forState:UIControlStateNormal];
        [self.contentView addSubview:_deleteBtn];
        _deleteBtn.hidden = YES;
    }
    
    return self;
}

- (void)layoutSubviews
{
    [super layoutSubviews];
    
    _iconView.center = CGPointMake(self.width / 2, 15 + _iconView.image.size.height / 2);
    
    [_songNameLabel sizeToFit];
    _songNameLabel.frame = CGRectMake(5, _iconView.bottom + 10, self.width - 10, _songNameLabel.height);
    
    [_authorNameLabel sizeToFit];
    _authorNameLabel.frame = CGRectMake(10, self.height - 10 - 10, self.width - 20, 10);
    
    _deleteBtn.frame = CGRectMake(0, 0, 20, 20);
}

- (void)setSelected:(BOOL)selected
{
    //本地音频的按钮
    if (_authorNameLabel.hidden) {
        return;
    }
    
    
    if (!selected) {
        _iconView.image = [UIImage imageNamed:@"voice_nor"];
        _songNameLabel.textColor = UIColorFromRGB(0x777777);
        _authorNameLabel.textColor = UIColorFromRGB(0x555555);
        self.layer.borderColor = UIColorFromRGB(0x181818).CGColor;
    } else {
        _iconView.image = [UIImage imageNamed:@"voice_pressed"];
        _songNameLabel.textColor = Pink_Cor;//UIColorFromRGB(0x0accac);
        _authorNameLabel.textColor = Pink_Cor;//UIColorFromRGB(0x0accac);
        self.layer.borderWidth = 1;
        self.layer.borderColor = Pink_Cor.CGColor;//UIColorFromRGB(0x0accac).CGColor;
    }
}

- (void)setModel:(TCMusicCollectionCell *)model
{
    _songNameLabel.text = model.soneName;
    if (_songNameLabel.text.length > 0) {
        NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:_songNameLabel.text];
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:11];
        [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [_songNameLabel.text length])];
        _songNameLabel.attributedText = attributedString;
        _songNameLabel.textAlignment = NSTextAlignmentCenter;
    }
    _authorNameLabel.text = model.singerName;
}

#pragma mark - UI event handle

@end
