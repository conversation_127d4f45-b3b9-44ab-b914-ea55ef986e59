//
//  XGGTimeSelectView.h
//  TXLiteAVDemo_Enterprise
//
//  Created by <PERSON><PERSON><PERSON> on 2017/10/27.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol XGGTimeSelectViewDelegate <NSObject>
- (void)onVideoTimeEffectsClear;
- (void)onVideoTimeEffectsSpeed;
- (void)onVideoTimeEffectsBackPlay;
- (void)onVideoTimeEffectsRepeat;
@end

@interface XGGTimeSelectView : UIView
@property(nonatomic,weak) id<XGGTimeSelectViewDelegate> delegate;
@property(nonatomic,assign)BOOL isAlbum;         //是否是相册视频（相册视频不要添加倒放）

-(instancetype)initWithFrame:(CGRect)frame;
@end
