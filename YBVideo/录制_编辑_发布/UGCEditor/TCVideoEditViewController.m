//
//  TCVideoEditViewController.m
//  TCLVBIMDemo
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2017/4/10.
//  Copyright © 2017年 tencent. All rights reserved.
//

#import "TCVideoEditViewController.h"

#import <TXLiteAVSDK_Professional/TXVideoEditer.h>
//#import "TXVideoEditer.h"
#import <MediaPlayer/MPMediaPickerController.h>
#import <AVFoundation/AVFoundation.h>
#import "TCVideoRangeSlider.h"
#import "TCVideoRangeConst.h"
#import "TCVideoPublishController.h"
//#import "UIView+AdditionsX12.h"
#import "UIColor+MLPFlatColors.h"
#import "TCFilterSettingView.h"
#import "TCVideoPreview.h"
#import "TCBottomTabBar.h"
#import "TCVideoCutView.h"
#import "TCMusicMixView.h"
#import "TCTextAddView.h"
#import "YBVideoMusicView.h"
#import "XGGEffectSelectView.h"
#import "XGGTimeSelectView.h"
#import <SDWebImage/SDWebImageDownloader.h>
#import "YBProgressObj.h"
#import "YBTakeSameVideoVC.h"
#import <MediaPlayer/MediaPlayer.h>
typedef  NS_ENUM(NSInteger,ActionType) {
    ActionType_Save,
    ActionType_Publish,
    ActionType_Save_Publish,
};
typedef  NS_ENUM(NSInteger,TimeType) {
    TimeType_Clear,
    TimeType_Back,
    TimeType_Repeat,
    TimeType_Speed,
};

@interface TCVideoEditViewController ()<TXVideoGenerateListener,TCVideoPreviewDelegate, TCVideoPreviewDelegate, TCFilterSettingViewDelegate, TCBottomTabBarDelegate, TCVideoCutViewDelegate, TCMusicMixViewDelegate, MPMediaPickerControllerDelegate, UIActionSheetDelegate,VideoEffectViewDelegate,XGGTimeSelectViewDelegate>



@property(nonatomic,strong)UIButton *backBtn;
@property(nonatomic,strong)UIButton *selMusicBtn;                   //选择音乐
@property(nonatomic,strong)UIButton *selVolumeBtn;                  //选择音量

/** 底部按钮组合：特效、封面、滤镜、下一步 */
@property(nonatomic,strong)UIView *botBtnMix;

@property(nonatomic,strong)UIButton *effectBtn;
@property(nonatomic,strong)UIButton *coverBtn;
@property(nonatomic,strong)UIButton *filterfBtn;
@property(nonatomic,strong)UIButton *nextBtn;


/** 底部功能组合：botBar、videoRangeSlider、剪裁、时间特效、其他特效 */
@property(nonatomic,strong)UIView *botFunctionMix;

@property(nonatomic,strong)TCBottomTabBar *bottomBar;
@property(nonatomic,strong) UILabel  *cutTipsLabel;
@property(nonatomic,strong)TCVideoCutView *videoCutView;


@property(nonatomic,strong)TCFilterSettingView *filterView;
@property(nonatomic,strong)TCMusicMixView *musixMixView;
@property(nonatomic,strong)XGGEffectSelectView *effectSelectView;       //动效选择
@property(nonatomic,strong)XGGTimeSelectView *timeSelectView;           //时间特效栏

@property(nonatomic,strong)NSString *filePath;
@property(nonatomic,assign)ActionType actionType;

@property(nonatomic,strong)NSString *waterVideoPath;

@end

@implementation TCVideoEditViewController {
    TXVideoEditer *_ugcEdit;
    TCVideoPreview *_videoPreview;
    
    unsigned long long  _fileSize;
    NSMutableArray *_cutPathList;
    NSString *_videoOutputPath;
    
    UIProgressView *_playProgressView;
    UILabel *_startTimeLabel;
    UILabel *_endTimeLabel;
    CGFloat _leftTime;
    CGFloat _rightTime;
    int _effectType;
    BOOL  _isReverse;
    CGFloat _playTime;
    TimeType    _timeType;
    
    BOOL _isRecordBg;                   //是否是录制开始设置的背景音乐（改收费版SDK添加）
    UIImage *watermark;
    BOOL isUploadWater;
    CGFloat _originVolume;          //记录系统音量
    MPVolumeView *_volumeView;
    UISlider *_volumeViewSlider;
    BOOL _changeVolume;
}

static int MIXBTN_W = 55;

-(void)creatVoluem {
    if (_volumeViewSlider == nil) {
        _volumeView = [[MPVolumeView alloc] initWithFrame:CGRectMake(-100, -100, 40, 40)];
        [self.view addSubview:_volumeView];
        for (UIView* newView in _volumeView.subviews) {
            if ([newView.class.description isEqualToString:@"MPVolumeSlider"]){
                _volumeViewSlider = (UISlider*)newView;
                break;
            }
        }
    }
    [_volumeView setHidden:NO];
    [_volumeView setShowsRouteButton:YES];
    [_volumeView setShowsVolumeSlider:YES];
}
-(void)recoveryVolume{
    if (_changeVolume) {
        _changeVolume = NO;
        _volumeViewSlider.value = _originVolume ;
    }
}
#pragma mark - get/set
-(UIButton *)backBtn {
    if (!_backBtn) {
        _backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _backBtn.frame = CGRectMake(10, 40, 40, 40);
        [_backBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
        [_backBtn addTarget:self action:@selector(clickBackBtn) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backBtn;
}
- (UIButton *)selVolumeBtn {
    if (!_selVolumeBtn) {
        _selVolumeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _selVolumeBtn.frame = CGRectMake(_window_width-MIXBTN_W*2-8-12, 40, MIXBTN_W, MIXBTN_W);
        [_selVolumeBtn setImage:[UIImage imageNamed:@"音量"] forState:0];
        [_selVolumeBtn setTitle:YZMsg(@"音量") forState:0];
        _selVolumeBtn.titleLabel.font = SYS_Font(13);
        [_selVolumeBtn addTarget:self action:@selector(clickSelVolumeBtn) forControlEvents:UIControlEventTouchUpInside];
        _selVolumeBtn = [PublicObj setUpImgDownText:_selVolumeBtn];
    }
    return _selVolumeBtn;
}
- (UIButton *)selMusicBtn {
    if (!_selMusicBtn) {
        _selMusicBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _selMusicBtn.frame = CGRectMake(_window_width-MIXBTN_W-8-12, 40, MIXBTN_W+12, MIXBTN_W);
        [_selMusicBtn setImage:[UIImage imageNamed:@"音乐"] forState:0];
        [_selMusicBtn setTitle:YZMsg(@"选音乐") forState:0];
        _selMusicBtn.titleLabel.font = SYS_Font(13);
        [_selMusicBtn addTarget:self action:@selector(clickMusicBtn) forControlEvents:UIControlEventTouchUpInside];
        _selMusicBtn = [PublicObj setUpImgDownText:_selMusicBtn];
    }
    return _selMusicBtn;
}
-(UIView *)botBtnMix{
    if (!_botBtnMix) {
        _botBtnMix = [[UIView alloc]initWithFrame:CGRectMake(0,_window_height-80-ShowDiff, _window_width, 80+ShowDiff)];
        _botBtnMix.backgroundColor = [UIColor clearColor];
        
        //特效
        _effectBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _effectBtn.frame = CGRectMake(8, 0, MIXBTN_W, MIXBTN_W);
        [_effectBtn setImage:[UIImage imageNamed:@"特效"] forState:0];
        [_effectBtn setTitle:YZMsg(@"特效") forState:0];
        _effectBtn.titleLabel.font = SYS_Font(13);
        [_effectBtn addTarget:self action:@selector(clickEffectBtn) forControlEvents:UIControlEventTouchUpInside];
        _effectBtn = [PublicObj setUpImgDownText:_effectBtn];
        [_botBtnMix addSubview:_effectBtn];
        
        //封面（隐藏了 CGRectMake(_effectBtn.right+3, 0, MIXBTN_W, MIXBTN_W);）
        _coverBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _coverBtn.frame = CGRectMake(_effectBtn.right+3, 0, 0, 0);
        [_coverBtn setImage:[UIImage imageNamed:@"封面"] forState:0];
        [_coverBtn setTitle:YZMsg(@"封面") forState:0];
        _coverBtn.titleLabel.font = SYS_Font(13);
        [_coverBtn addTarget:self action:@selector(clickCovertBtn) forControlEvents:UIControlEventTouchUpInside];
        _coverBtn = [PublicObj setUpImgDownText:_coverBtn];
        [_botBtnMix addSubview:_coverBtn];
        _coverBtn.hidden = YES;
        
        //滤镜
        _filterfBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _filterfBtn.frame = CGRectMake(_coverBtn.right+3, 0, MIXBTN_W, MIXBTN_W);
        [_filterfBtn setImage:[UIImage imageNamed:@"滤镜"] forState:0];
        [_filterfBtn setTitle:YZMsg(@"滤镜") forState:0];
        _filterfBtn.titleLabel.font = SYS_Font(13);
        [_filterfBtn addTarget:self action:@selector(clickFilterBtn) forControlEvents:UIControlEventTouchUpInside];
        _filterfBtn = [PublicObj setUpImgDownText:_filterfBtn];
        [_botBtnMix addSubview:_filterfBtn];
        
        //下一步
        _nextBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _nextBtn.frame = CGRectMake(_window_width-10-65, 12.5, 65, 30);
        [_nextBtn setTitle:YZMsg(@"下一步") forState:0];
        _nextBtn.titleLabel.font = SYS_Font(14);
        [_nextBtn setTitleColor:[UIColor whiteColor] forState:0];
        _nextBtn.backgroundColor = Pink_Cor;
        _nextBtn.layer.masksToBounds = YES;
        _nextBtn.layer.cornerRadius = 15;
        [_nextBtn addTarget:self action:@selector(clickNextBtn) forControlEvents:UIControlEventTouchUpInside];
        [_botBtnMix addSubview:_nextBtn];
        
    }
    return _botBtnMix;
}

-(UIView *)botFunctionMix {
    if (!_botFunctionMix) {
        CGFloat botFunH = _window_height/2;
        _botFunctionMix = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-botFunH-ShowDiff, _window_width, botFunH+ShowDiff)];
        _botFunctionMix.backgroundColor = [UIColor clearColor];
        
        //底部bar
        _bottomBar = [[TCBottomTabBar alloc] initWithFrame:CGRectMake(0, botFunH - 40 * kScaleY, _window_width, 40 * kScaleY+ShowDiff)];
        _bottomBar.delegate = self;
        [_botFunctionMix addSubview:_bottomBar];
        
        CGFloat selectViewHeight = _window_height >= 667 ? 120 * kScaleY : 120 * kScaleY;
        
        UIView *alphaV = [[UIView alloc]initWithFrame:CGRectMake(0, _bottomBar.top-selectViewHeight, _window_width, selectViewHeight)];
        alphaV.backgroundColor = [UIColor blackColor];
        alphaV.alpha = 0.4;
        [_botFunctionMix addSubview:alphaV];
        
         _cutTipsLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, _bottomBar.top -  selectViewHeight, _window_width, selectViewHeight)];
         _cutTipsLabel.textAlignment = NSTextAlignmentCenter;
         _cutTipsLabel.text = YZMsg(@"请拖拽两侧滑块选择裁剪区域");
         _cutTipsLabel.textColor = [UIColor whiteColor];
         _cutTipsLabel.font = [UIFont systemFontOfSize:16];
         [_cutTipsLabel setAdjustsFontSizeToFitWidth:YES];
         [_botFunctionMix addSubview:_cutTipsLabel];
        
        _videoCutView = [[TCVideoCutView alloc] initWithFrame:CGRectMake(0,40, _window_width, botFunH-_bottomBar.height-selectViewHeight-40) videoPath:_videoPath videoAssert:_videoAsset];
        _videoCutView.delegate = self;
        //_videoCutView.backgroundColor = [UIColor grayColor];
        [_botFunctionMix addSubview:_videoCutView];
        
        _timeSelectView = [[XGGTimeSelectView alloc] initWithFrame:CGRectMake(0, _bottomBar.top -  selectViewHeight, _window_width, selectViewHeight)];
        //_timeSelectView.backgroundColor = [UIColor yellowColor];
        _timeSelectView.delegate = self;
        _timeSelectView.isAlbum = _isAlbum;
        
        _effectSelectView = [[XGGEffectSelectView alloc] initWithFrame:_timeSelectView.frame];
        //_effectSelectView.backgroundColor = [UIColor greenColor];
        _effectSelectView.delegate = self;
    
    }
    return _botFunctionMix;
}

- (TCMusicMixView *)musixMixView {
    if (!_musixMixView) {
        //haveBGM这里初始值一旦为YES“原声”再不可编辑
        _musixMixView = [[TCMusicMixView alloc] initWithFrame:CGRectMake(0, _window_height-ShowDiff-215, _window_width, 215+ShowDiff)haveBgm:_haveBGM];
        _musixMixView.delegate = self;
        _musixMixView.backgroundColor = [UIColor clearColor];
    }
    return _musixMixView;
}

-(TCFilterSettingView *)filterView {
    if (!_filterView) {
        _filterView = [[TCFilterSettingView alloc] initWithFrame:CGRectMake(0, _window_height-ShowDiff-90, _window_width, 90+ShowDiff)];
        _filterView.delegate = self;
    }
    return _filterView;
}

#pragma mark - 按钮点击事件
-(void)showBotBtnMix{
    _musixMixView.hidden = YES;
    _botFunctionMix.hidden = YES;
    _filterView.hidden = YES;
    _botBtnMix.hidden = NO;
}

- (void) touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    if (_botFunctionMix.hidden == NO ||_filterView.hidden == NO ||_musixMixView.hidden == NO) {
        UITouch *touch = [[event allTouches] anyObject];
        CGPoint _touchPoint = [touch locationInView:self.view];
        if (YES == CGRectContainsPoint(CGRectMake(0, 64, _window_width, _window_height*2/3), _touchPoint)) {
            [self showBotBtnMix];
        }
    }else{
        //视频暂停
        [_videoPreview setPlayBtn:_videoPreview.isPlaying];
        [_videoPreview playVideo];
    }
}

-(void)clickBackBtn {
    //[_ugcEdit setBGM:nil result:nil];
    
    YBWeakSelf;
    NSDictionary *contentDic = @{@"title":YZMsg(@"提示"),@"msg":YZMsg(@"是否退出视频编辑"),@"left":YZMsg(@"取消"),@"right":YZMsg(@"确定")};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            [weakSelf sureExit];
        }
    }];
    
}

-(void)sureExit {
    [self recoveryVolume];
    [self pauseBack];
    //[self.navigationController popToRootViewControllerAnimated:YES];
    if (_isTakeSame == YES) {
        //拍同款返回到同款音乐视频列表
        BOOL findVC = NO;
        for (UIViewController *forVC in self.navigationController.childViewControllers) {
            if ([forVC isKindOfClass:[YBTakeSameVideoVC class]]) {
                findVC = YES;
                [self.navigationController popToViewController:forVC animated:YES];
                break;
            }
        }
        if (findVC == NO) {
            [self.navigationController popToRootViewControllerAnimated:YES];
        }
    }else {
        [self.navigationController popToRootViewControllerAnimated:YES];
    }
}

-(void)clickSelVolumeBtn {
    
    [self showBotBtnMix];
    
    //选择音量-剪辑音乐
    [self cutMusic];
    
}
-(void)clickMusicBtn {
    //暂停播放
    [self onVideoPause];
    [self showBotBtnMix];
    
    //更改音乐
    YBVideoMusicView *mVC = [[YBVideoMusicView alloc]init];
    mVC.fromWhere = @"edit";
    __weak TCVideoEditViewController *weakSelf = self;
    mVC.pathEvent = ^(NSString *event, NSString *musicID) {
        weakSelf.musicPath = event;
        weakSelf.musicID = musicID;
        //删除时editView隐藏重新选择后显示
        weakSelf.musixMixView.editView.hidden = NO;
        //重新选择后_filePath 不再是delate状态将其置空
        weakSelf.filePath = nil;
        [self cutMusic];
    };
    YBNavigationController *nav = [[YBNavigationController alloc]initWithRootViewController:mVC];
    nav.modalPresentationStyle = 0;
    [self presentViewController:nav animated:YES completion:nil];
    
}
-(void)clickEffectBtn {
    //特效-展示botbar
    self.botFunctionMix.hidden = NO;
    self.botBtnMix.hidden = YES;
    
}
-(void)clickCovertBtn {
    //封面
    [MBProgressHUD showPop:YZMsg(@"敬请期待")];
}
-(void)clickFilterBtn {
    //滤镜
    self.filterView.hidden = NO;
    self.botBtnMix.hidden = YES;
}
-(void)clickNextBtn {
    //下一步
    [self pause];
//    if (_fileSize > 200 * 1024 * 1024) {
//        [MBProgressHUD showPop:@"视频文件过大,超过200M！"];
//        return;
//    }

    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"仅发布") complete:^{
        weakSelf.actionType = ActionType_Publish;
        [weakSelf clickEvent];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"仅保存") complete:^{
        weakSelf.actionType = ActionType_Save;
        [weakSelf clickEvent];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"保存并发布") complete:^{
        weakSelf.actionType = ActionType_Save_Publish;
        [weakSelf clickEvent];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];
    
    
}

-(void)clickEvent {
    YBWeakSelf;
    [[YBProgressObj progressManeger]setUpViewCancelHidden:NO andComplete:^{
        [weakSelf onGenerateCancelBtnClicked:nil];
    }];
    [YBProgressObj progressManeger].generationHidden = NO;
    
    [_ugcEdit setCutFromTime:_leftTime toTime:_rightTime];
    [self checkVideoOutputPath];
    //VIDEO_COMPRESSED_540P
    [_ugcEdit generateVideo:VIDEO_COMPRESSED_720P videoOutputPath:_videoOutputPath];

    [self onVideoPause];
    [_videoPreview setPlayBtn:NO];
}
- (void)checkVideoOutputPath{
    NSFileManager *manager = [[NSFileManager alloc] init];
    if ([manager fileExistsAtPath:_videoOutputPath]) {
        BOOL success =  [manager removeItemAtPath:_videoOutputPath error:nil];
        if (success) {
            NSLog(@"Already exist. Removed!");
        }
    }
}
-(void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    NSLog(@"rk20200118===编辑====内存内存内存=======");
    
    //[MBProgressHUD showError:@"TC Memory"];
    
}
//返回、音量、音乐保持最前面
-(void)keepViewFront{
    [self.view bringSubviewToFront:self.backBtn];
    [self.view bringSubviewToFront:self.selVolumeBtn];
    [self.view bringSubviewToFront:self.selMusicBtn];
}



-(instancetype)init {
    self = [super init];
    if (self) {
        _cutPathList = [NSMutableArray array];
        _videoOutputPath = [NSTemporaryDirectory() stringByAppendingPathComponent:@"outputCut.mp4"];
        _waterVideoPath = [NSTemporaryDirectory() stringByAppendingPathComponent:@"outputCut_water.mp4"];
        _effectType = -1;
//        _videoTextInfos = [NSMutableArray new];
        [self getWaterImage];
    }
    return self;
}
- (void)getWaterImage{
    NSString *waterUrl = [[NSUserDefaults standardUserDefaults] objectForKey:@"watermarklogo"];
    [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:[NSURL URLWithString:waterUrl] options:0 progress:nil completed:^(UIImage *image, NSData *data, NSError *error, BOOL finished) {
        watermark = image;
    }];
}
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    

    [UIApplication sharedApplication].statusBarHidden = YES;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
   
    [UIApplication sharedApplication].statusBarHidden = NO;
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
}

- (void)dealloc {
    
    [_videoPreview removeNotification];
    _videoPreview = nil;
    
    NSLog(@"rk20200118===编辑===dealloc");
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self creatVoluem];
    self.naviView.hidden = YES;
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    self.navigationController.interactivePopGestureRecognizer.delegate =nil;
    
    self.view.backgroundColor = UIColor.blackColor;
    self.navigationController.navigationBarHidden = YES;
    _isRecordBg = _haveBGM;
    if (_videoAsset == nil && _videoPath != nil) {
        NSURL *avUrl = [NSURL fileURLWithPath:_videoPath];
        _videoAsset = [AVAsset assetWithURL:avUrl];
    }
    
    //返回、音量、音乐
    [self.view addSubview:self.backBtn];
    [self.view addSubview:self.selVolumeBtn];
    [self.view addSubview:self.selMusicBtn];
    if (_recordType == RecordType_Chorus) {
        _selVolumeBtn.hidden = _selMusicBtn.hidden = YES;
    }
   
    _videoPreview = [[TCVideoPreview alloc] initWithFrame:CGRectMake(0, 0, _window_width, _window_height) coverImage:nil];
    _videoPreview.delegate = self;
    [self.view addSubview:_videoPreview];
    
    _playProgressView = [[UIProgressView alloc] initWithFrame:CGRectMake(0, _videoPreview.top+2, _window_width, 6)];
    _playProgressView.trackTintColor = UIColorFromRGB(0xd8d8d8);
    _playProgressView.progressTintColor = Pink_Cor;//UIColorFromRGB(0x0accac);
    [self.view addSubview:_playProgressView];
    
    //保持view-btn不被遮挡
    [self keepViewFront];
    
    //底部按钮区域
    [self.view addSubview:self.botBtnMix];
    //底部功能区域
    [self.view addSubview:self.botFunctionMix];
    self.botFunctionMix.hidden = YES;
    //滤镜种类
    [self.view addSubview:self.filterView];
    self.filterView.hidden = YES;
    //音乐剪辑
    [self.view addSubview:self.musixMixView];
    self.musixMixView.hidden = YES;
    
    TXPreviewParam *param = [[TXPreviewParam alloc] init];
    param.videoView = _videoPreview.renderView;
   
    if (IS_IPHONE_X && _recordType != RecordType_Chorus ){
        if(!_isAlbum){
            param.renderMode =  PREVIEW_RENDER_MODE_FILL_SCREEN;
        }else{
            param.renderMode = PREVIEW_RENDER_MODE_FILL_EDGE;
        }
    }else{
        param.renderMode =  PREVIEW_RENDER_MODE_FILL_EDGE;
    }
    
    _ugcEdit = [[TXVideoEditer alloc] initWithPreview:param];
    _ugcEdit.generateDelegate = self;
    _ugcEdit.previewDelegate = _videoPreview;
    
    [_ugcEdit setVideoAsset:_videoAsset];
    TXVideoInfo *videoMsg = [TXVideoInfoReader getVideoInfoWithAsset:_videoAsset];
    _fileSize   = videoMsg.fileSize;
    CGFloat duration = videoMsg.duration;
    _rightTime = duration;
    _endTimeLabel.text = [NSString stringWithFormat:@"%d:%02d", (int)duration / 60, (int)duration % 60];
    
    if (_isTakeSame) {
        //拍同款选择的 转场、本地视频要加一下音乐
        _musixMixView.editView.hidden = NO;
        _filePath = nil;
        [self cutMusic];
        self.musixMixView.hidden = YES;
        self.botBtnMix.hidden = NO;
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if ([PublicObj getSysOutputVolume] < 0.2) {
        _changeVolume = YES;
        _originVolume = [AVAudioSession sharedInstance].outputVolume;
        _volumeViewSlider.value = 0.3;
    }
    [_videoPreview setPlayBtn:NO];
    [_videoPreview playVideo];
}

//- (void)goBack {
//    [_ugcEdit setBGM:nil result:nil];
//    [self pause];
//    [self.navigationController popViewControllerAnimated:YES];
//
//}
//rk-1-16
- (void)pauseBack{
    [_ugcEdit setBGM:nil result:nil];
    [_ugcEdit pausePlay];
    [_videoPreview setPlayBtn:NO];
    [_ugcEdit stopPlay];
    _ugcEdit.generateDelegate = nil;
    _ugcEdit = nil;
    [_videoPreview removeNotification];
    [_videoPreview removeFromSuperview];
    _videoPreview = nil;
}

- (void)pause {
    [_ugcEdit stopPlay];
    [_videoPreview setPlayBtn:NO];
}


- (void)onGenerateCancelBtnClicked:(UIButton*)sender {
    isUploadWater = NO;
    [_ugcEdit cancelGenerate];
    [self destoryProgressView];
}
-(void)destoryProgressView {
    [YBProgressObj progressManeger].generationHidden = YES;
    [[YBProgressObj progressManeger] progressDestroy];
}
#pragma mark FilterSettingViewDelegate
- (void)onSetFilterWithImage:(UIImage *)image {
    [_ugcEdit setFilter:image];
}

#pragma mark - BottomTabBarDelegate
- (void)onCutBtnClicked {
    //[self pause];
    
    [_timeSelectView removeFromSuperview];
    [_effectSelectView removeFromSuperview];
    [_botFunctionMix addSubview:_videoCutView];
    [_botFunctionMix addSubview:_cutTipsLabel];
    [_videoCutView setEffectDeleteBtnHidden:YES];
    
}

- (void)onFilterBtnClicked {
    //[self pause];
    [_videoCutView removeFromSuperview];
    [_musixMixView removeFromSuperview];
    [_cutTipsLabel removeFromSuperview];
    [_timeSelectView removeFromSuperview];
    [_effectSelectView removeFromSuperview];
    
    [_botFunctionMix addSubview:_filterView];
    _videoCutView.videoRangeSlider.hidden = NO;
    
}

- (void)onMusicBtnClicked {
    //[self pause];
    [_filterView removeFromSuperview];
    [_videoCutView removeFromSuperview];
    [_cutTipsLabel removeFromSuperview];
    [_timeSelectView removeFromSuperview];
    [_effectSelectView removeFromSuperview];
    
    [_botFunctionMix addSubview:_musixMixView];
    _videoCutView.videoRangeSlider.hidden = NO;
    
    
}

- (void)onTextBtnClicked {
    //[self pause];
    [_filterView removeFromSuperview];
    [_videoCutView removeFromSuperview];
    [_musixMixView removeFromSuperview];
    [_cutTipsLabel removeFromSuperview];
    [_timeSelectView removeFromSuperview];
    [_effectSelectView removeFromSuperview];
    
    _videoCutView.videoRangeSlider.hidden = NO;
    
}
- (void)onEffectBtnClicked {
    
    [_cutTipsLabel removeFromSuperview];
    [_timeSelectView removeFromSuperview];
    
    [_botFunctionMix addSubview:_videoCutView];
    [_botFunctionMix addSubview:_effectSelectView];
    [_videoCutView setEffectDeleteBtnHidden:NO];
}
-(void)onTimeBtnClicked {
    
    [_effectSelectView removeFromSuperview];
    [_cutTipsLabel removeFromSuperview];
    
    [_botFunctionMix addSubview:_videoCutView];
    [_botFunctionMix addSubview:_timeSelectView];
    [_videoCutView setEffectDeleteBtnHidden:YES];
}


#pragma mark TXVideoGenerateListener
-(void) onGenerateProgress:(float)progress {
    //[MBProgressHUD HUDForView:self.view].progress = progress;
    if (watermark) {
        if (isUploadWater && _actionType != ActionType_Save) {
            [YBProgressObj progressManeger].generationProgress = progress/2 + 0.5;
        }else{
            [YBProgressObj progressManeger].generationProgress = progress/2;
        }
    }else{
        [YBProgressObj progressManeger].generationProgress = progress;
    }
    
    NSLog(@"==正在合成：%f",progress);
}

-(void) onGenerateComplete:(TXGenerateResult *)result {
    
    if (result.retCode == 0) {
        if (_actionType == ActionType_Save_Publish) {
            if (watermark) {
                if (isUploadWater) {
                    [self destoryProgressView];
                    UISaveVideoAtPathToSavedPhotosAlbum(_videoOutputPath, self, @selector(video:didFinishSavingWithError:contextInfo:), nil);
                }else{
                    isUploadWater = YES;
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [_ugcEdit setWaterMark:watermark normalizationFrame:CGRectMake(0.03, 0.04, 0.25, 0)];
                        [_ugcEdit generateVideo:VIDEO_COMPRESSED_720P videoOutputPath:_waterVideoPath];
                    });
                }
            }else{
                [self destoryProgressView];
                UISaveVideoAtPathToSavedPhotosAlbum(_videoOutputPath, self, @selector(video:didFinishSavingWithError:contextInfo:), nil);
            }
        }else if (_actionType == ActionType_Save){
            [self destoryProgressView];
            UISaveVideoAtPathToSavedPhotosAlbum(_videoOutputPath, self, @selector(video:didFinishSavingWithError:contextInfo:), nil);
        }
        else if (_actionType == ActionType_Publish){
            if (isUploadWater) {
                [self destoryProgressView];
                [self publish:_waterVideoPath];
            }else{
                
                if (watermark) {
                    isUploadWater = YES;
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [_ugcEdit setWaterMark:watermark normalizationFrame:CGRectMake(0.03, 0.04, 0.25, 0)];
                        [_ugcEdit generateVideo:VIDEO_COMPRESSED_720P videoOutputPath:_waterVideoPath];
                    });
                }else{
                    [self destoryProgressView];
                    [self publish:nil];
                }

            }
        }
    }else{
        [self destoryProgressView];
        
        NSDictionary *contentDic = @{@"title":YZMsg(@"视频生成失败"),@"msg":[NSString stringWithFormat:@"%@：%ld %@：%@",YZMsg(@"错误码"),(long)result.retCode,YZMsg(@"错误信息"),result.descMsg],@"left":@"",@"right":YZMsg(@"确定")};
           [YBAlertView showAlertView:contentDic complete:^(int eventType) {
               
           }];
    }
}

- (void)video:(NSString *)videoPath didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo {
    
    if (_actionType == ActionType_Save) {
        //[MBProgressHUD hideAllHUDsForView:self.view animated:YES];
        [MBProgressHUD showPop:YZMsg(@"保存成功")];
        [self pauseBack];
        /*
        for (UIViewController *subVC in [[[[UIApplication sharedApplication].delegate window] rootViewController] childViewControllers]) {
            if ([subVC isKindOfClass:[YBVideoMusicView class]]) {
                [self dismissViewControllerAnimated:NO completion:^{
                    [subVC.navigationController popToRootViewControllerAnimated:NO];
                }];
            }
        }
         */
        [self.navigationController popToRootViewControllerAnimated:YES];
        return;
    }
    if (isUploadWater) {
        [self publish:_waterVideoPath];
    }else{
        [self publish:nil];
        
    }

}


- (void)publish:(NSString *)waterPath {
    [self recoveryVolume];
    isUploadWater = NO;
    //[MBProgressHUD hideAllHUDsForView:self.view animated:YES];
    //TXVideoInfo *videoMsg = [TXUGCVideoInfoReader getVideoInfo:_videoOutputPath];
    [UIApplication sharedApplication].statusBarHidden = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        TCVideoPublishController *vc = [[TCVideoPublishController alloc] initWithPath:_videoOutputPath
                                                                             videoMsg:[TXVideoInfoReader getVideoInfo:_videoOutputPath]];
        vc.musicID = _musicID;
        vc.waterVideoPath = waterPath;
        vc.isTakeSame = _isTakeSame;
        vc.videoAsset = _videoAsset;
        [self.navigationController pushViewController:vc animated:YES];
        [self pauseBack];
    });
}

#pragma mark TCVideoPreviewDelegate

- (void)onVideoPlay {
    CGFloat currentPos = _videoCutView.videoRangeSlider.currentPos;
    if (currentPos < _leftTime || currentPos > _rightTime)
        currentPos = _leftTime;
    
    if(_isReverse && currentPos != 0){
        [_ugcEdit startPlayFromTime:0 toTime:currentPos];
    }
    else if(_videoCutView.videoRangeSlider.rightPos != 0){
        [_ugcEdit startPlayFromTime:currentPos toTime:_videoCutView.videoRangeSlider.rightPos];
    }
    else{
        [_ugcEdit startPlayFromTime:currentPos toTime:_rightTime];
    }
}

- (void)onVideoPause {
    [_ugcEdit pausePlay];
}

- (void)onVideoResume {
    //[_ugcEdit resumePlay];
    [self onVideoPlay];
}

- (void)onVideoPlayProgress:(CGFloat)time {
    _playTime = time;
    
    _playProgressView.progress = (time - _leftTime) / (_rightTime - _leftTime);
    [_videoCutView setPlayTime:time];
    
}

- (void)onVideoPlayFinished {
    if (_effectType != -1) {
        [self onVideoEffectEndClick:_effectType];
    }else{
        [_ugcEdit startPlayFromTime:_leftTime toTime:_rightTime];
    }
}

- (void)onVideoEnterBackground {
    //[MBProgressHUD hideHUDForView:self.view animated:YES];
    [self onVideoPause];
    
    //视频生成中不能进后台，因为使用硬编，会导致失败
    if ([YBProgressObj progressManeger].isExist) {
        [self destoryProgressView];
        [_ugcEdit cancelGenerate];
        
        NSDictionary *contentDic = @{@"title":YZMsg(@"视频生成失败"),@"msg":YZMsg(@"中途切后台或则被电话，闹钟等打断导致,请重新生成"),@"left":@"",@"right":YZMsg(@"确定")};
        [YBAlertView showAlertView:contentDic complete:^(int eventType) {
            
        }];
        
        
    }
}
#pragma mark - MusicMixViewDelegate

-(void)cutMusic{
    //当首次进入这里并且没有选择音乐的时候前两个条件就不能满足要求了，这时要加第三个条件（_musicPath.length > 0）
    if (![_musicPath isEqual:_filePath] && ![_filePath isEqual:@"delate"] && _musicPath.length > 0) {
        NSFileManager *managers=[NSFileManager defaultManager];
        NSArray *subArray = [_musicPath componentsSeparatedByString:@"*"];
        NSString *musicNameStr = [subArray objectAtIndex:1];
        NSString *singerStr = [subArray objectAtIndex:2];
        /*
         NSString *timeStr = [subArray objectAtIndex:3];
         NSString *songID = [subArray objectAtIndex:4];
         NSArray *IDArray = [songID componentsSeparatedByString:@"."];
         songID = [IDArray objectAtIndex:0];
         */
        TCMusicCollectionCell* musicInfo = [TCMusicCollectionCell new];
        NSURL *url = [NSURL fileURLWithPath:_musicPath];
        AVURLAsset *musicAsset = [AVURLAsset URLAssetWithURL:url options:nil];
        musicInfo.duration = musicAsset.duration.value / musicAsset.duration.timescale;
        musicInfo.filePath = _musicPath;
        musicInfo.soneName = musicNameStr;
        musicInfo.singerName = singerStr;
        __weak TCVideoEditViewController *weakSelf = self;
        if ([managers fileExistsAtPath:_musicPath]) {
            dispatch_async(dispatch_get_main_queue(), ^{
                weakSelf.musixMixView.editView.hidden = NO;
                [self saveAssetURLToFile:musicInfo assetURL:url];
            });
        }
    }
    self.musixMixView.hidden = NO;
    self.botBtnMix.hidden = YES;
    
}


- (void)onSetVideoVolume:(CGFloat)videoVolume musicVolume:(CGFloat)musicVolume {
    [_ugcEdit setVideoVolume:videoVolume];
    [_ugcEdit setBGMVolume:musicVolume];
}

- (void)onSetBGMWithFilePath:(NSString *)filePath startTime:(CGFloat)startTime endTime:(CGFloat)endTime {
    if (![_filePath isEqualToString:filePath]) {
        __weak __typeof(self) weakSelf = self;
        [_ugcEdit setBGM:filePath result:^(int result) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (result == -1){
                    NSDictionary *contentDic = @{@"title":YZMsg(@"设置背景音乐失败"),@"msg":YZMsg(@"不支持当前格式的背景音乐!"),@"left":@"",@"right":YZMsg(@"确定")};
                    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
                    }];
                }else{
                    [weakSelf setBGMVolume:filePath startTime:startTime endTime:endTime];
                }
            });
        }];
    }else{
        [self setBGMVolume:filePath startTime:startTime endTime:endTime];
    }
    
}
- (void)delBGM {
    _musicID = @"";
    _musicPath = @"";
    _haveBGM = NO;
    
    YBWeakSelf;
    [_ugcEdit setBGM:nil result:^(int result) {
        NSLog(@"del:%d",result);
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf setBGMVolume:nil startTime:0 endTime:0];
        });
    }];
    
}
-(void)setBGMVolume:(NSString *)filePath startTime:(CGFloat)startTime endTime:(CGFloat)endTime {
    _filePath = filePath;
    [_ugcEdit setBGMStartTime:startTime endTime:endTime];
    if (_filePath == nil && _isRecordBg==NO) {
        CGFloat bgVolume = ([PublicObj getSysOutputVolume] < 0.2 )? 0.3 : [PublicObj getSysOutputVolume];
        [_ugcEdit setVideoVolume:bgVolume];
    }
    
    [_ugcEdit startPlayFromTime:_leftTime toTime:_rightTime];
    [_ugcEdit setBGMLoop:YES];
    [_videoPreview setPlayBtn:YES];
}
#pragma mark - VideoCutViewDelegate
- (void)onVideoLeftCutChanged:(TCVideoRangeSlider *)sender {
    //[_ugcEdit pausePlay];
    [_videoPreview setPlayBtn:NO];
    [_ugcEdit previewAtTime:sender.leftPos];
}

- (void)onVideoRightCutChanged:(TCVideoRangeSlider *)sender {
    [_videoPreview setPlayBtn:NO];
    [_ugcEdit previewAtTime:sender.rightPos];
}

- (void)onVideoCutChangedEnd:(TCVideoRangeSlider *)sender {
    _leftTime = sender.leftPos;
    _rightTime = sender.rightPos;
    _startTimeLabel.text = [NSString stringWithFormat:@"%d:%02d", (int)sender.leftPos / 60, (int)sender.leftPos % 60];
    _endTimeLabel.text = [NSString stringWithFormat:@"%d:%02d", (int)sender.rightPos / 60, (int)sender.rightPos % 60];
    [_ugcEdit startPlayFromTime:sender.leftPos toTime:sender.rightPos];
    [_videoPreview setPlayBtn:YES];
}
- (void)onVideoCenterRepeatChanged:(TCVideoRangeSlider*)sender {
    [_videoPreview setPlayBtn:NO];
    [_ugcEdit previewAtTime:sender.centerPos];
}

- (void)onVideoCenterRepeatEnd:(TCVideoRangeSlider*)sender {
    _leftTime = sender.leftPos;
    _rightTime = sender.rightPos;
    
    if (_timeType == TimeType_Repeat) {
        TXRepeat *repeat = [[TXRepeat alloc] init];
        repeat.startTime = sender.centerPos;
        repeat.endTime = sender.centerPos + 0.5;
        repeat.repeatTimes = 3;
        [_ugcEdit setRepeatPlay:@[repeat]];
        [_ugcEdit setSpeedList:nil];
    }
    else if (_timeType == TimeType_Speed) {
        TXSpeed *speed = [[TXSpeed alloc] init];
        speed.startTime = sender.centerPos;
        speed.endTime = sender.rightPos;
        speed.speedLevel = SPEED_LEVEL_SLOW;
        [_ugcEdit setSpeedList:@[speed]];
        [_ugcEdit setRepeatPlay:nil];
    }
    
    if (_isReverse) {
        [_ugcEdit startPlayFromTime:sender.leftPos toTime:sender.centerPos + 1.5];
    }else{
        [_ugcEdit startPlayFromTime:sender.centerPos toTime:sender.rightPos];
    }
    [_videoPreview setPlayBtn:YES];
}

- (void)onVideoCutChange:(TCVideoRangeSlider *)sender seekToPos:(CGFloat)pos {
    _playTime = pos;
    
    [_ugcEdit previewAtTime:pos];
    [_videoPreview setPlayBtn:NO];
    _playProgressView.progress = (pos - _leftTime) / (_rightTime - _leftTime);
}

//- (void)onSetSpeedUp:(BOOL)isSpeedUp
//{
//    if (isSpeedUp) {
//        [_ugcEdit setSpeedLevel:2.0];
//    } else {
//        [_ugcEdit setSpeedLevel:1.0];
//    }
//}

//- (void)onSetSpeedUpLevel:(CGFloat)level
//{
//    [_ugcEdit setSpeedLevel:level];
//}

- (void)onSetBeautyDepth:(float)beautyDepth WhiteningDepth:(float)whiteningDepth {
    [_ugcEdit setBeautyFilter:beautyDepth setWhiteningLevel:whiteningDepth];
}

- (void)onEffectDelete {
    [_ugcEdit deleteLastEffect];
}

#pragma mark - MPMediaPickerControllerDelegate
- (void)mediaPicker:(MPMediaPickerController *)mediaPicker didPickMediaItems:(MPMediaItemCollection *)mediaItemCollection {
    NSArray *items = mediaItemCollection.items;
    MPMediaItem *songItem = [items objectAtIndex:0];
    
    NSURL *url = [songItem valueForProperty:MPMediaItemPropertyAssetURL];
    NSString* songName = [songItem valueForProperty: MPMediaItemPropertyTitle];
    NSString* authorName = [songItem valueForProperty:MPMediaItemPropertyArtist];
    NSNumber* duration = [songItem valueForKey:MPMediaItemPropertyPlaybackDuration];
    NSLog(@"MPMediaItemPropertyAssetURL = %@", url);
    
    TCMusicCollectionCell* musicInfo = [TCMusicCollectionCell new];
    musicInfo.duration = duration.floatValue;
    musicInfo.soneName = songName;
    musicInfo.singerName = authorName;
    
    if (mediaPicker.editing) {
        mediaPicker.editing = NO;
        dispatch_async(dispatch_get_main_queue(), ^{
            [self saveAssetURLToFile:musicInfo assetURL:url];
        });
        
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

//点击取消时回调
- (void)mediaPickerDidCancel:(MPMediaPickerController *)mediaPicker{
    [self dismissViewControllerAnimated:YES completion:nil];
}

// 将AssetURL(音乐)导出到app的文件夹并播放
- (void)saveAssetURLToFile:(TCMusicCollectionCell*)musicInfo assetURL:(NSURL*)assetURL {
    
    [_musixMixView addMusicInfo:musicInfo];

}
#pragma mark - VideoEffectViewDelegate
- (void)onVideoEffectBeginClick:(TXEffectType)effectType {
    _effectType = effectType;
    switch ((TXEffectType)_effectType) {
        case TXEffectType_ROCK_LIGHT:
            [_videoCutView startColoration:UIColorFromRGB(0xEC5F9B) alpha:0.7];
            break;
        case TXEffectType_DARK_DRAEM:
            [_videoCutView startColoration:UIColorFromRGB(0xEC8435) alpha:0.7];
            break;
        case TXEffectType_SOUL_OUT:
            [_videoCutView startColoration:UIColorFromRGB(0x1FBCB6) alpha:0.7];
            break;
        case TXEffectType_SCREEN_SPLIT:
            [_videoCutView startColoration:UIColorFromRGB(0x449FF3) alpha:0.7];
            break;
        default:
            break;
    }
    [_ugcEdit startEffect:(TXEffectType)_effectType startTime:_playTime];
    if (!_isReverse) {
        [_ugcEdit startPlayFromTime:_videoCutView.videoRangeSlider.currentPos toTime:_videoCutView.videoRangeSlider.rightPos];
    }else{
        [_ugcEdit startPlayFromTime:_videoCutView.videoRangeSlider.leftPos toTime:_videoCutView.videoRangeSlider.currentPos];
    }
    [_videoPreview setPlayBtn:YES];
}

- (void)onVideoEffectEndClick:(TXEffectType)effectType {
    if (_effectType != -1) {
        [_videoPreview setPlayBtn:NO];
        [_videoCutView stopColoration];
        [_ugcEdit stopEffect:effectType endTime:_playTime];
        [_ugcEdit pausePlay];
        _effectType = -1;
    }
}
-(void)onEffectSelDelete{
    //删除特效
    [_videoCutView.videoRangeSlider removeLastColoration];
    [_ugcEdit deleteLastEffect];
    
    if (_videoCutView.videoRangeSlider.colorInfos.count<=0) {
        _effectSelectView.delEffectBtn.hidden = YES;
    }
}
#pragma mark XGGTimeSelectViewDelegate
- (void)onVideoTimeEffectsClear {
    _timeType = TimeType_Clear;
    _isReverse = NO;
    [_ugcEdit setReverse:_isReverse];
    [_ugcEdit setRepeatPlay:nil];
    [_ugcEdit setSpeedList:nil];
    [_ugcEdit startPlayFromTime:_videoCutView.videoRangeSlider.leftPos toTime:_videoCutView.videoRangeSlider.rightPos];
    
    [_videoPreview setPlayBtn:YES];
    [_videoCutView setCenterPanHidden:YES];
}
- (void)onVideoTimeEffectsBackPlay {
    _timeType = TimeType_Back;
    _isReverse = YES;
    [_ugcEdit setReverse:_isReverse];
    [_ugcEdit setRepeatPlay:nil];
    [_ugcEdit setSpeedList:nil];
    [_ugcEdit startPlayFromTime:_videoCutView.videoRangeSlider.leftPos toTime:_videoCutView.videoRangeSlider.rightPos];
    NSLog(@"left:%f\n right:%f",_videoCutView.videoRangeSlider.leftPos,_videoCutView.videoRangeSlider.rightPos);
    [_videoPreview setPlayBtn:YES];
    [_videoCutView setCenterPanHidden:YES];
    _videoCutView.videoRangeSlider.hidden = NO;
}
- (void)onVideoTimeEffectsRepeat {
    _timeType = TimeType_Repeat;
    _isReverse = NO;
    [_ugcEdit setReverse:_isReverse];
    [_ugcEdit setSpeedList:nil];
    TXRepeat *repeat = [[TXRepeat alloc] init];
    repeat.startTime = _leftTime + (_rightTime - _leftTime) / 5;
    repeat.endTime = repeat.startTime + 0.5;
    repeat.repeatTimes = 3;
    [_ugcEdit setRepeatPlay:@[repeat]];
    [_ugcEdit startPlayFromTime:_videoCutView.videoRangeSlider.leftPos toTime:_videoCutView.videoRangeSlider.rightPos];
    
    [_videoPreview setPlayBtn:YES];
    [_videoCutView setCenterPanHidden:NO];
    [_videoCutView setCenterPanFrame:repeat.startTime];
}

- (void)onVideoTimeEffectsSpeed {
    _timeType = TimeType_Speed;
    _isReverse = NO;
    [_ugcEdit setReverse:_isReverse];
    [_ugcEdit setRepeatPlay:nil];
    TXSpeed *speed =[[TXSpeed alloc] init];
    speed.startTime = _leftTime + (_rightTime - _leftTime) * 1.5 / 5;
    speed.endTime = _videoCutView.videoRangeSlider.rightPos;
    speed.speedLevel = SPEED_LEVEL_SLOW;
    [_ugcEdit setSpeedList:@[speed]];
    [_ugcEdit startPlayFromTime:_videoCutView.videoRangeSlider.leftPos toTime:_videoCutView.videoRangeSlider.rightPos];
    
    [_videoPreview setPlayBtn:YES];
    [_videoCutView setCenterPanHidden:NO];
    [_videoCutView setCenterPanFrame:speed.startTime];
}
//- (BOOL)prefersStatusBarHidden{
//    return NO;
//}
@end
/*
 _startTimeLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, _playProgressView.bottom + 10 * kScaleY, 50, 12)];
 _startTimeLabel.text = @"0:00";
 _startTimeLabel.textAlignment = NSTextAlignmentLeft;
 _startTimeLabel.font = [UIFont systemFontOfSize:12];
 _startTimeLabel.textColor = UIColor.lightTextColor;
 [self.view addSubview:_startTimeLabel];
 
 _endTimeLabel = [[UILabel alloc] initWithFrame:CGRectMake(self.view.width - 15 - 50, _playProgressView.bottom + 10, 50, 12)];
 _endTimeLabel.text = @"0:00";
 _endTimeLabel.textAlignment = NSTextAlignmentRight;
 _endTimeLabel.font = [UIFont systemFontOfSize:12];
 _endTimeLabel.textColor = UIColor.lightTextColor;
 [self.view addSubview:_endTimeLabel];
 */
/**
 - (void)saveAssetURLToFile:(TCMusicCollectionCell*)musicInfo assetURL:(NSURL*)assetURL {
     
     [_musixMixView addMusicInfo:musicInfo];
     
     //    AVURLAsset *songAsset = [AVURLAsset URLAssetWithURL:assetURL options:nil];
     //
     //    AVAssetExportSession *exporter = [[AVAssetExportSession alloc] initWithAsset:songAsset presetName:AVAssetExportPresetAppleM4A];
     //    NSLog (@"created exporter. supportedFileTypes: %@", exporter.supportedFileTypes);
     //    exporter.outputFileType = @"com.apple.m4a-audio";
     //
     //    [AVAssetExportSession exportPresetsCompatibleWithAsset:songAsset];
     //    NSString *docDir = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:@"LocalMusics/"];
     //    NSString *exportFilePath = [docDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@_%@.m4a", musicInfo.soneName, musicInfo.singerName]];
     //
     //    exporter.outputURL = [NSURL fileURLWithPath:exportFilePath];
     //    musicInfo.filePath = exportFilePath;
     //
     //    if ([[NSFileManager defaultManager] fileExistsAtPath:exportFilePath]) {
     //        [_musixMixView addMusicInfo:musicInfo];
     //        return;
     //    }
     //
     //    MBProgressHUD* hub = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
     //   hub.labelText = @"音频读取中...";
     
     // do the export
     //__weak typeof(self) weakSelf = self;
     //    [exporter exportAsynchronouslyWithCompletionHandler:^{
     //        dispatch_async(dispatch_get_main_queue(), ^{
     //            [MBProgressHUD hideHUDForView:self.view animated:YES];
     //        });
     //        int exportStatus = exporter.status;
     //        switch (exportStatus) {
     //            case AVAssetExportSessionStatusFailed: {
     //                NSLog (@"AVAssetExportSessionStatusFailed: %@", exporter.error);
     //                break;
     //
     //            }
     //            case AVAssetExportSessionStatusCompleted: {
     //                NSLog(@"AVAssetExportSessionStatusCompleted: %@", exporter.outputURL);
     //
     //                // 播放背景音乐
     //                dispatch_async(dispatch_get_main_queue(), ^{
     //                    [_musixMixView addMusicInfo:musicInfo];
     //                });
     //                break;
     //            }
     //            case AVAssetExportSessionStatusUnknown: { NSLog (@"AVAssetExportSessionStatusUnknown"); break;}
     //            case AVAssetExportSessionStatusExporting: { NSLog (@"AVAssetExportSessionStatusExporting"); break;}
     //            case AVAssetExportSessionStatusCancelled: { NSLog (@"AVAssetExportSessionStatusCancelled"); break;}
     //            case AVAssetExportSessionStatusWaiting: { NSLog (@"AVAssetExportSessionStatusWaiting"); break;}
     //            default: { NSLog (@"didn't get export status"); break;}
     //        }
     //    }];
 }
 
 */
