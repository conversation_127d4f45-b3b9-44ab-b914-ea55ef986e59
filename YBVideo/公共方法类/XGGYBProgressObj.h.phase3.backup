//
//  YBProgressObj.h
//  YBVideo
//
//  Created by YB007 on 2019/11/26.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void (^YBProgressBlock)(void);

@interface YBProgressObj : NSObject


+(instancetype)progressManeger;

@property(nonatomic,assign,readonly)BOOL isExist;
@property(nonatomic,assign)BOOL generationHidden;
@property(nonatomic,assign)CGFloat generationProgress;

-(void)setUpViewCancelHidden:(BOOL)cancelHidden andComplete:(YBProgressBlock)complete;
-(void)progressDestroy;
@end


