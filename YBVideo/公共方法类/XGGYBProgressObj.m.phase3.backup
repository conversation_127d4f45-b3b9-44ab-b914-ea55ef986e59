//
//  YBProgressObj.m
//  YBVideo
//
//  Created by YB007 on 2019/11/26.
//  Copyright © 2019 cat. All rights reserved.
//

#import "YBProgressObj.h"

@interface YBProgressObj()

@property(nonatomic,strong)UIView *generationView;
@property(nonatomic,strong)UILabel *generationTitleLabel;
@property(nonatomic,strong)UIProgressView *generateProgressView;
@property(nonatomic,strong)UIButton *generateCannelBtn;
@property(nonatomic,copy)YBProgressBlock progressEvent;
@property(nonatomic,assign)BOOL isExist;

@end

@implementation YBProgressObj


static YBProgressObj *progressManeger = nil;

+(instancetype)progressManeger {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        progressManeger = [[super allocWithZone:NULL]init];
    });
    return progressManeger;
}

+(instancetype)allocWithZone:(struct _NSZone *)zone {
    return [self progressManeger];
}

-(void)setUpViewCancelHidden:(BOOL)cancelHidden andComplete:(YBProgressBlock)complete {
    self.progressEvent = complete;
    if (!_generationView) {
        self.isExist = YES;
        _generationView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        _generationView.backgroundColor = UIColor.blackColor;
        _generationView.alpha = 0.9f;
        
        _generateProgressView = [UIProgressView new];
        _generateProgressView.center = CGPointMake(_generationView.width / 2, _generationView.height / 2);
        _generateProgressView.bounds = CGRectMake(0, 0, 225, 20);
        _generateProgressView.progressTintColor = Pink_Cor;//UIColorFromRGB(0x0accac);
        [_generateProgressView setTrackImage:[UIImage imageNamed:@"slide_bar_small"]];
        //_generateProgressView.trackTintColor = UIColor.whiteColor;
        //_generateProgressView.transform = CGAffineTransformMakeScale(1.0, 2.0);
        
        _generationTitleLabel = [UILabel new];
        _generationTitleLabel.font = [UIFont systemFontOfSize:14];
        _generationTitleLabel.text = YZMsg(@"请稍后.");
        _generationTitleLabel.textColor = UIColor.whiteColor;
        _generationTitleLabel.textAlignment = NSTextAlignmentCenter;
        _generationTitleLabel.frame = CGRectMake(0, _generateProgressView.y - 34, _generationView.width, 14);
        
        _generateCannelBtn = [UIButton new];
        [_generateCannelBtn setImage:[UIImage imageNamed:@"cancell"] forState:UIControlStateNormal];
        _generateCannelBtn.frame = CGRectMake(_generateProgressView.right + 15, _generationTitleLabel.bottom + 10, 20, 20);
        [_generateCannelBtn addTarget:self action:@selector(onGenerateCancelBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
        _generateCannelBtn.hidden = cancelHidden;
        
        [_generationView addSubview:_generationTitleLabel];
        [_generationView addSubview:_generateProgressView];
        [_generationView addSubview:_generateCannelBtn];
        [[UIApplication sharedApplication].delegate.window addSubview:_generationView];
    }
    _generateProgressView.progress = 0.f;
    [[UIApplication sharedApplication].delegate.window bringSubviewToFront:_generationView];
    
}
- (void)setGenerationProgress:(CGFloat)generationProgress {
    _generateProgressView.progress = generationProgress;
    _generationTitleLabel.text = [NSString stringWithFormat:@"%@(%.f%%)",YZMsg(@"请稍后."),generationProgress*100];
}
- (void)onGenerateCancelBtnClicked:(UIButton*)sender {
    if (self.progressEvent) {
        self.progressEvent();
    }
}
- (void)setGenerationHidden:(BOOL)generationHidden {
    _generationHidden = generationHidden;
    _generationView.hidden = _generationHidden;
    if (_generationHidden == NO) {
        [[UIApplication sharedApplication].delegate.window bringSubviewToFront:_generationView];
    }
}
-(void)progressDestroy {
    if (_generationView) {
        self.isExist = NO;
        [_generationView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
        [_generationView removeFromSuperview];
        _generationView = nil;
    }
}
@end
