//
//  iOSNetworking.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/6.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "iOSNetworking.h"

NSString *const ResponseErrorKey = @"com.alamofire.serialization.response.error.response";
NSInteger const Interval = 20;

@implementation iOSNetworking

#pragma mark - 原生GET网络请求
+(void)getWithURL:(NSString *)url params:(NSDictionary *)params success:(SuccessBlock)success failure:(FailureBlock)failure {
    //完整URL
    NSString *urlString = [NSString string];
    if (params) {
        //参数拼接url
        NSString *paramStr = [self dealWithParam:params];
        urlString = [url stringByAppendingString:paramStr];
    }else{
        urlString = url;
    }
    //对URL中的中文进行转码
    NSString *pathStr = [urlString stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:pathStr]];
    request.timeoutInterval = Interval;
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (data) {
                //利用iOS自带原生JSON解析data数据 保存为Dictionary
                NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:nil];
                success(response,dict);
            }else{
                NSHTTPURLResponse *httpResponse = error.userInfo[ResponseErrorKey];
                if (httpResponse.statusCode != 0) {
                    NSString *ResponseStr = [self showErrorInfoWithStatusCode:httpResponse.statusCode];
                    failure(ResponseStr);
                } else {
                    NSString *ErrorCode = [self showErrorInfoWithStatusCode:error.code];
                    failure(ErrorCode);
                }
            }
        });
    }];
    [task resume];
}

#pragma mark - 原生POST请求
+ (void)postWithURL:(NSString *)url params:(NSDictionary *)params success:(SuccessBlock)success failure:(FailureBlock)failure{
    NSString *baseUrl = [purl stringByAppendingFormat:@"?service=%@",url];
    baseUrl = [baseUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    
    NSMutableDictionary *pullDic = [[NSMutableDictionary alloc]initWithDictionary:params];
    if (![PublicObj checkNull:[Config getOwnID]]) {
        [pullDic addEntriesFromDictionary:@{@"uid":[Config getOwnID],@"token":[Config getOwnToken]}];
    }
    NSString *serLang = [YBLanguageTools serviceLang];
    
    [pullDic addEntriesFromDictionary:@{@"version_ios":[PublicObj getAppBuild],
                                        @"mobileid":[PublicObj getDeviceUUID],
                                        @"device":[PublicObj iphoneType],
                                        @"lang":serLang,
                                      }];
    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:baseUrl]];
    [request setHTTPMethod:@"POST"];
    //把字典中的参数进行拼接
    NSString *body = [self dealWithParam:pullDic];
    NSData *bodyData = [body dataUsingEncoding:NSUTF8StringEncoding];
    //设置请求体
    [request setHTTPBody:bodyData];
    //设置本次请求的数据请求格式
    [request setValue:@"application/x-www-form-urlencoded" forHTTPHeaderField:@"Content-Type"];
    // 设置本次请求请求体的长度(因为服务器会根据你这个设定的长度去解析你的请求体中的参数内容)
    [request setValue:[NSString stringWithFormat:@"%lu", (unsigned long)bodyData.length] forHTTPHeaderField:@"Content-Length"];
    //设置请求最长时间
    request.timeoutInterval = Interval;
    
    BOOL showLog = YES;
    NSURLSessionTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (data) {
            //利用iOS自带原生JSON解析data数据 保存为Dictionary
            NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:nil];
            
            if (!dict) {
                NSString* br_jsonStr = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                if (showLog) {
                    NSLog(@"出现非标准Json:%@",br_jsonStr);
                }
                if ([br_jsonStr containsString:@"{\"ret\""]) {
                    NSRange rangeRes = [br_jsonStr rangeOfString:@"{"];
                    //NSLog(@"xxxxx:%lu",(unsigned long)rangeRes.location);
                    NSString *newStr = [br_jsonStr substringFromIndex:rangeRes.location];
                    NSData *newData = [newStr dataUsingEncoding:NSUTF8StringEncoding];
                    dict = [NSJSONSerialization JSONObjectWithData:newData options:NSJSONReadingMutableContainers error:nil];
                    if (showLog) {
                        NSLog(@"发生截取:%@\n%@\n",url,newStr);
                    }
                }
            }
            success(response,dict);
            if (showLog) {
                NSLog(@"接口地址:\n%@%@\n\n%@",baseUrl,[self getStrWithDic:pullDic],dict);
            }
            /*
            NSDictionary *data = [dict valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if ([code intValue] == 700) {
                [MBProgressHUD hideHUD];
                [YBTools tokenExpired:strFormat([data valueForKey:@"msg"])];
                NSLog(@"====%@",baseUrl);
            }
            */
        }else{
            NSHTTPURLResponse *httpResponse = error.userInfo[ResponseErrorKey];
            if (httpResponse.statusCode != 0) {
                NSString *ResponseStr = [self showErrorInfoWithStatusCode:httpResponse.statusCode];
                NSLog(@"接口地址(failure):\n%@%@\n\n%@",baseUrl,[self getStrWithDic:pullDic],ResponseStr);
                if (failure) {
                    failure(ResponseStr);
                }
            } else {
                NSString *ErrorCode = [self showErrorInfoWithStatusCode:error.code];
                NSLog(@"接口地址(failure):\n%@%@\n\n%@",baseUrl,[self getStrWithDic:pullDic],ErrorCode);
                if (failure) {
                    failure(ErrorCode);
                }
            }
        }
    }];
    [task resume];
}
+(NSString *)getStrWithDic:(NSDictionary*)dic; {
    if ([dic allKeys].count<=0) {
        return @"";
    }
    NSString *urlStr = @"";
    for (NSString *key in [dic allKeys]) {
        NSString *value = minstr([dic valueForKey:key]);
        value = [value stringByReplacingOccurrencesOfString:@" " withString:@""];
        urlStr = [urlStr stringByAppendingFormat:@"&%@=%@",key,value];
    }
    return urlStr;
}
#pragma mark -- 拼接参数
+ (NSString *)dealWithParam:(NSDictionary *)param {
    NSArray *allkeys = [param allKeys];
    NSMutableString *result = [NSMutableString string];
    
    for (NSString *key in allkeys) {
        NSString *string = [NSString stringWithFormat:@"%@=%@&", key, param[key]];
        [result appendString:string];
    }
    NSString *newStr = [result substringToIndex:(result.length-1)];
    return newStr;
}

#pragma mark
+ (NSString *)showErrorInfoWithStatusCode:(NSInteger)statusCode{
    
    NSString *message = nil;
    switch (statusCode) {
        case 401: {
            
        }
            break;
            
        case 500: {
            message = @"服务器异常！";
        }
            break;
            
        case -1001: {
            message = @"网络请求超时，请稍后重试！";
        }
            break;
            
        case -1002: {
            message = @"不支持的URL！";
        }
            break;
            
        case -1003: {
            message = @"未能找到指定的服务器！";
        }
            break;
            
        case -1004: {
            message = @"服务器连接失败！";
        }
            break;
            
        case -1005: {
            message = @"连接丢失，请稍后重试！";
        }
            break;
            
        case -1009: {
            message = @"互联网连接似乎是离线！";
        }
            break;
            
        case -1012: {
            message = @"操作无法完成！";
        }
            break;
            
        default: {
            message = @"网络请求发生未知错误，请稍后再试！";
        }
            break;
    }
    return message;
    
}


@end
