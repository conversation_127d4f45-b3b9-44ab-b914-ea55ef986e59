//
//  RKSysAccess.m
//  YBVideo
//
//  Created by YB007 on 2020/8/21.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RKSysAccess.h"



@interface RKSysAccess()

@property(nonatomic,assign)BOOL alertIsShowing;

@end

@implementation RKSysAccess

static RKSysAccess *_access = nil;

+(instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _access = [[super allocWithZone:NULL]init];
    });
    return _access;
}
+ (instancetype)allocWithZone:(struct _NSZone *)zone{
    return [self shareInstance];
}


-(BOOL)checkMediaAccess {
    AVAuthorizationStatus videoAuthStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    AVAuthorizationStatus audioAuthStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeAudio];
    if (videoAuthStatus == AVAuthorizationStatusAuthorized && audioAuthStatus == AVAuthorizationStatusAuthorized) {
        return YES;
    }
    return NO;
}

-(void)requestMediaAccess:(RKAccessBlock)rkAccess{
    AVAuthorizationStatus videoAuthStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    AVAuthorizationStatus audioAuthStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeAudio];
    if (videoAuthStatus == AVAuthorizationStatusNotDetermined && audioAuthStatus == AVAuthorizationStatusNotDetermined) {
        //相机、麦克风首次授权
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            if (granted == YES) {
                [AVCaptureDevice requestAccessForMediaType:AVMediaTypeAudio completionHandler:^(BOOL granted) {
                    if (rkAccess) {
                        rkAccess(granted);
                    }
                }];
            }else {
                if (rkAccess) {
                    rkAccess(NO);
                }
            }
        }];
    }else if (videoAuthStatus != AVAuthorizationStatusNotDetermined && audioAuthStatus == AVAuthorizationStatusNotDetermined){
        //麦克风首次授权
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeAudio completionHandler:^(BOOL granted) {
            if (rkAccess) {
                rkAccess(granted);
            }
        }];
    }else if (videoAuthStatus == AVAuthorizationStatusNotDetermined && audioAuthStatus != AVAuthorizationStatusNotDetermined){
        //相机首次授权
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            if (rkAccess) {
                rkAccess(granted);
            }
        }];
    }else{
        if (rkAccess) {
            rkAccess(NO);
        }
        [self checkAccess:AVMediaTypeVideo];
        [self checkAccess:AVMediaTypeAudio];
    }
    
    
    
//    if (audioAuthStatus == AVAuthorizationStatusNotDetermined) {
//        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeAudio completionHandler:^(BOOL granted) {
//            if (rkAccess) {
//                rkAccess(granted);
//            }
//            if (granted == NO) {
//                dispatch_async(dispatch_get_main_queue(), ^{
//                    [[XGGAppDelegate sharedAppDelegate].topViewController.navigationController popViewControllerAnimated:YES];
//                });
//            }
//        }];
//    }
//    if (videoAuthStatus == AVAuthorizationStatusAuthorized && audioAuthStatus == AVAuthorizationStatusAuthorized ) {
//        //都同意
//    }else{
//        [self checkAccess:AVMediaTypeVideo];
//        [self checkAccess:AVMediaTypeAudio];
//    }
}

-(void)checkAccess:(AVMediaType)mediaType{
    if (_alertIsShowing) {
        return;
    }
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    switch (authStatus) {
        case AVAuthorizationStatusRestricted: case AVAuthorizationStatusDenied:{
            //被拒或者受限
            _alertIsShowing = YES;
            NSDictionary *alerDic = @{@"title":YZMsg(@"提示"),@"msg":YZMsg(@"麦克风或相机权限未开启，请前往设置"),@"left":YZMsg(@"取消"),@"right":YZMsg(@"设置"),@"richImg":@""};
            [YBAlertView showAlertView:alerDic complete:^(int eventType) {
                _alertIsShowing = NO;
                if (eventType == 1) {
                    [self goSet];
                }else{
                    [[XGGAppDelegate sharedAppDelegate].topViewController.navigationController popViewControllerAnimated:YES];
                }
            }];
        }break;
            
        default:
            break;
    }
}

-(void)goSet{
    CGFloat systemVersion = [PublicObj getSysVersion];
    if (systemVersion >= 8.0 && systemVersion < 10.0) {
        NSURL * url = [NSURL URLWithString:@"prefs:root=General&path=Reset"];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url];
        }
    }else if (systemVersion >= 10.0) {
        NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            if (@available(iOS 10.0, *)) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
                }];
            }
        }
    }
}


@end
