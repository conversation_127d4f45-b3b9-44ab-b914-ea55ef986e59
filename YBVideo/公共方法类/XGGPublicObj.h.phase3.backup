//
//  PublicObj.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/6.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <Photos/Photos.h>
#import "YBTabBarController.h"
#import "YBButton.h"

typedef void (^ThumbSizeBlock)(CGFloat width,CGFloat height);
typedef void (^RoomIMNumBlock)(int pUnread);
typedef void (^TokenAccessBlock)(BOOL isAccess);
typedef void(^GoodsExistenceEvent)(int code, NSString *msg);

@interface PublicObj : NSObject
+(instancetype)publicInstance;
- (void)addSystemVolumeView;
- (void)removeSystemVolumeView;
- (void)addCusDelayedRemove;
/**
 计算字符串宽度

 @param str 字符串
 @param font 字体
 @param height 高度
 @return 宽度
 */
- (CGFloat)widthOfString:(NSString *)str andFont:(UIFont *)font andHeight:(CGFloat)height;
+ (CGFloat)widthOfString:(NSString *)str andFont:(UIFont *)font andHeight:(CGFloat)height;
/**
 计算字符串的高度

 @param str 字符串
 @param font 字体
 @param width 宽度
 @return 高度
 */
+ (CGFloat)heightOfString:(NSString *)str andFont:(UIFont *)font andWidth:(CGFloat)width;

/** 单行文本长度计算 */
+(CGSize)sizeWithString:(NSString *)string andFont:(UIFont *)font;

/** 多行计算frame */
+(CGRect)rectWithString:(NSString *)string andFont:(UIFont *)font maxWidth:(CGFloat)width;

/** token过期 */
+(void)tokenExpired:(NSString *)msg;

/** 返回游客信息 */
+(NSDictionary *)visitorDic;

/** 提醒游客请登录 */
+(void)warnLogin;

/** 根据色值获取图片 */
+(UIImage*)getImgWithColor:(UIColor *)color;
+(UIImage*)getImgWithColor:(UIColor *)color withSize:(CGSize)size;

/** 设置上图下文字 */
+(UIButton*)setUpImgDownText:(UIButton *)btn;

/** 自定义间距上图下文字 */
+(UIButton*)setUpImgDownText:(UIButton *)btn space:(CGFloat)space;

/** 重设root */
+(void)resetVC:(UIViewController*)vc;

/** 根据图片名拼接文件路径 */
+(NSString *)getFilePathWithImageName:(NSString *)imageName;

/** 以当前时间合成视频名称 */
+(NSString *)getNameBaseCurrentTime:(NSString *)suf;

/** 判断是不是本地视频 */
+(BOOL)judgeAssetisInLocalAblum:(PHAsset *)asset;

/** 权限相关 */
+(BOOL)havePhotoLibraryAuthority;

/** 原图-小-恢复 */
+(CAAnimation*)bigToSmallRecovery;

/** 原图-大-小*/
+(CAAnimation*)smallToBigToSmall;

/** 原图-小-保持 */
+(CAAnimation*)originToSmall;

/** 原图-大-小-恢复 */
+(CAAnimation*)originToBigToSmallRecovery;

/* 观看页面上下切换，并且未关注的情况下的过渡动画 */
+(CAAnimation*)followShowTransition;

/** 按钮按下执行动画(录音) */
+(CAAnimation*)touchDownAnimation;

/** 旋转动画 */
+(CABasicAnimation*)rotationAnimation;

/** 动画组 */
+(CAAnimationGroup*)caGroup;

/** MD5 */
+(NSString *)stringToMD5:(NSString *)str;

/** 检查为空 */
+(BOOL)checkNull:(NSString *)str;

/** 双击点赞 */
+(void)doubleClickShowZan:(UIView *)sueprView withCenter:(CGPoint )center;

/** 创建emoji正则表达式 */
+(NSArray <NSTextCheckingResult *> *)machesWithPattern:(NSString *)pattern  andStr:(NSString *)str;

/** 画一条线 */
+(UIView *)lineViewWithFrame:(CGRect)frame andColor:(UIColor *)color andView:(UIView *)view;

/** 获取系统音量滑块 */
+(UISlider*)setSystemVolumSliderWithSuperViwe:(UIViewController *)superView;
+(CGFloat)getSysOutputVolume;

/** 获取缩略图 */
+(UIImage*)getVideoPreViewImageWithPath:(NSURL *)videoPath;

/** 获取启动图 */
 + (UIImage *)getLaunchImage;

/** 获取版本号 */
+(NSString *)getAppBuild;
+(BOOL)isUp;
+(NSString *)getAppName;
+(CGFloat)getSysVersion;
+(UIImage *)getAppIcon;

/** 字典字符串加(解)密排序 */
+ (NSString *)sortString:(NSDictionary *)dic;
+ (NSString *)decrypt:(NSString *)code;

/** 小程序 */
+ (void)openWXMiniProgram:(NSString *)path;

/** git 转 data */
+(NSData *)gifConvertDataWithName:(NSString *)gifName;

/** 封面width、height */
+(void)getThumbSizeWithUrl:(NSString *)url complete:(ThumbSizeBlock)complete;

/** 获取UUID */
+(NSString *)getDeviceUUID;

/** 设置视图左上圆角 */
+(CAShapeLayer *)setViewLeftTop:(CGFloat)leftC andRightTop:(CGFloat)rightC andView:(UIView *)view;

/** 图片压缩相关 */
+(UIImage*)scaleImage:(UIImage *)image scaleToSize:(CGSize)size;
+(CGSize)getObjSize:(CGSize)sourceSize;

/** 渐变色
 *  dire:0-横向 1-纵向 2-斜向
 */
+(void)addGradientWithFromColor:(UIColor *)formColor andEndColor:(UIColor *)endColor andView:(UIView *)view direction:(int)dire;

/** 比较两个时间的大小(date01 老的时间、date02 新的时间 返回 1 -1 0)*/
+(int)compareDate:(NSString*)date01 withDate:(NSString*)date02;

/** 网络类型、手机类型*/
+ (NSString *)getNetworkType;

/// 此方法谨慎使用【使用到了私有属性,随着系统的升级有可能面临苹果的禁用】
+ (int)getSignalStrength;

+ (NSString *)iphoneType;
+(void)saveNetReachability:(NSString *)save;
+ (NSInteger)getDeviceType;

/** 更新pushid */
+(void)updatePushId:(NSString *)pushId;

/** 重置为游客 */
+(void)resetUserToVisitor;
+(YBTabBarController *)currentTabbar;

/** 背景模糊图 */
+(UIImageView *)getAvatarEffectWithUrl:(NSString *)avatarUrl;
+(UIImageView *)getAvatarEffectWithUrl:(NSString *)avatarUrl andFrame:(CGRect)effectFrame;

/** 直播中头像动画 */
+(CAKeyframeAnimation *)avatarScaleAnimation;
+(CAKeyframeAnimation *)bottomScaleAnimation;

/** 获取私信未读数量【去除:粉丝、赞、AT、评论、官方】 */
//+(void)getRoomImUnread:(RoomIMNumBlock)complete;

/** 是否开启了粗体文本 */
+(BOOL)isBlodText;

/** 更新同城tab名称 */
+(NSString *)updataNewCity:(NSString *)cityName;

/** token 验证*/
+(void)tokenAccess:(TokenAccessBlock)complete;

+(void)checkGoodsExistenceWithID:(NSString *)goosID Existence:(GoodsExistenceEvent)existence;

+(YBButton *)youngAlertBtn;

#pragma mark - 各类window弹窗层级布局
+(void)layoutWindowPopLayer;
#pragma mark - 拒绝协议杀掉app
+(void)appDestroy;
#pragma mark - 美颜
+(BOOL)isBaseBeauty;

+(YBBaseViewController *)currentViewContorller;
+(NSString *)getDateDisplayString:(NSDate *)date;
+(NSString *)getUserDateString:(NSDate *)date;
+ (NSString *)getFriendlyDateString:(NSTimeInterval)timeInterval
                    forConversation:(BOOL)isShort;

#pragma mark - data转字符串
+(NSString *)transformToStringWithData:(id)originData;

+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString;

@end
