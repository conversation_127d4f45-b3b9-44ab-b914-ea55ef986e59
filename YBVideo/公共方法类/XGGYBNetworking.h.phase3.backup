//
//  YBNetworking.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/6.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "AFNetworking.h"

typedef void (^PullSuccessBlock)(int code, id info, NSString *msg);
typedef void (^PullFailBlock)(id fail);
typedef void (^PullFinishiBlock)(int code, NSDictionary *resDic);

@interface YBNetworking : NSObject

+(AFHTTPSessionManager *)ybnetManager;

/**
 * 腾讯云上传
 */
+(void)getQCloudWithUrl:(NSString *)url Suc:(PullSuccessBlock)sucBack Fail:(PullFailBlock)failBack;
/**
 网络封装
 @param url 接口名称
 @param dic 接口参数dic
 @param sucBack 成功回调
 @param failBack 失败回调
 */
+(void)postWithUrl:(NSString *)url Dic:(NSDictionary *)dic Suc:(PullSuccessBlock)sucBack Fail:(PullFailBlock)failBack;


/**
 * 获取视频分类
 */
+(void)getVideoClass:(BOOL)postNot;

+(NSString *)getStrWithDic:(NSDictionary*)dic;


/// 获取用户关系
+(void)getRelationWithTouid:(NSString *)touid finish:(PullFinishiBlock)finish;

@end
