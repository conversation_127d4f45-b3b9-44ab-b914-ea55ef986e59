//
//  BGSetting.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/6.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "BGSetting.h"
#import "iOSNetworking.h"

@implementation BGSetting

+(void)getBgSettingUpdate:(BOOL)update maintain:(BOOL)maintain eventBack:(callBack)event {
    
    /** 原生方法 */
    [iOSNetworking postWithURL:@"Home.getConfig" params:nil success:^(NSURLResponse *response,id responseObject) {
        
        NSString *number = [NSString stringWithFormat:@"%@",[responseObject valueForKey:@"ret"]];
        if ([number isEqual:@"200"]) {
            NSDictionary *data = [responseObject valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if ([code isEqual:@"0"]) {
                NSDictionary *subdic = [[data valueForKey:@"info"]firstObject];
                if (![subdic isEqual:[NSNull null]]) {
                    NSString *watermark = minstr([subdic valueForKey:@"watermark"]);
                    [[NSUserDefaults standardUserDefaults] setObject:watermark forKey:@"watermarklogo"];
                    //如果不相等说明未上架，检测是否是新版本
                    if (![[PublicObj getAppBuild] isEqual:minstr([subdic valueForKey:@"ios_shelves"])]) {
                        
                        //后台最新版本
                        NSString *ser_newbuild = minstr([subdic valueForKey:@"ipa_ver"]);
                        NSString *ser_ipa_url = minstr([subdic valueForKey:@"ipa_url"]);
                        NSString *ser_ipa_des = minstr([subdic valueForKey:@"ipa_des"]);
                        if (![ser_newbuild isEqual:[PublicObj getAppBuild]] && update == YES) {
                            //更新提示
                            NSDictionary *contentDic = @{@"title":YZMsg(@"版本更新"),
                                                         @"msg":ser_ipa_des,
                                                         @"left":YZMsg(@"暂不更新"),
                                                         @"right":YZMsg(@"立即使用")};
                            dispatch_async(dispatch_get_main_queue(), ^{
                                YBAlertView *updateAlert = [YBAlertView showAlertView:contentDic complete:^(int eventType) {
                                    if (eventType == 1) {
                                        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:ser_ipa_url]];
                                    }
                                }];
                                [updateAlert.cancleBtn setTitleColor:RGB_COLOR(@"#323232", 1) forState:0];
                                [updateAlert.sureBtn setTitleColor:Pink_Cor forState:0];
                                updateAlert.alertFrom = AlertFrom_AppUpdate;
                            });
                        }
                        
                        //维护
                        NSString *maintain_switch = minstr([subdic valueForKey:@"maintain_switch"]);
                        NSString *maintain_tips = minstr([subdic valueForKey:@"maintain_tips"]);
                        //maintain == yes 才展示弹窗
                        if ([maintain_switch isEqual:@"1"] && maintain == YES) {
                            NSDictionary *contentDic = @{@"title":YZMsg(@"维护信息"),
                                                         @"msg":maintain_tips,
                                                         @"left":@"",
                                                         @"right":YZMsg(@"确认")};
                            dispatch_async(dispatch_get_main_queue(), ^{
                                YBAlertView *maintainA = [YBAlertView showAlertView:contentDic complete:^(int eventType) {
                                    
                                }];
                                [maintainA.sureBtn setTitleColor:RGB_COLOR(@"#3232", 1) forState:0];
                                maintainA.alertFrom = AlertFrom_Maintain;
                            });
                        }
                    }
                }
                //回调
                if (event) {
                    event(subdic);
                }
                [common saveComUnified:subdic];
                NSLog(@"-----------%@",[PublicObj decrypt:[common getTISDKKey]]);
            }
        }
    } failure:nil];
}


@end
