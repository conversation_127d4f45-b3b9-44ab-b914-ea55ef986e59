//
//  iOSNetworking.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/6.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void (^SuccessBlock)(NSURLResponse *response, id responseObject);
typedef void (^FailureBlock)(NSString *error);

@interface iOSNetworking : NSObject

/**
 * 原生 get
 */
+ (void)getWithURL:(NSString *)url params:(NSDictionary *)params success:(SuccessBlock)success failure:(FailureBlock)failure;
/**
 * 原生 post
 */
+ (void)postWithURL:(NSString *)url params:(NSDictionary *)params success:(SuccessBlock)success failure:(FailureBlock)failure;


@end
