//
//  YBCenterVC.h
//  YBVideo
//
//  Created by YB007 on 2022/5/30.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBBaseViewController.h"

typedef void (^CenterBack)(void);
typedef void (^CenterCallBack)(NSString *isAttent);

@interface YBCenterVC : YBBaseViewController

@property(nonatomic,assign)BOOL isChatPage;             //从聊天点击头像而来
@property(nonatomic,copy)CenterBack backEvent;
@property(nonatomic,copy)CenterCallBack followEvent;
@property(nonatomic,assign)BOOL isPush;                 //从其他页面push

/** 他人uid，跳转页面一定要写！默认是自己的uid */
@property(nonatomic,strong)NSString *otherUid;

//tabbar
@property(nonatomic,assign)BOOL isTabbar;

@end


