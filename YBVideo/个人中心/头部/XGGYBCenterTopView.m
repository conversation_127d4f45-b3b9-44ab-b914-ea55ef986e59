//
//  YBCenterTopView.m
//  YBVideo
//
//  Created by YB007 on 2022/5/30.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBCenterTopView.h"
#import "UIImage+RKImgCategory.h"
#import "ShopHomeVC.h"
#import "shopDetailVC.h"
#import "YBGoodsLikeVC.h"
#import "EditVC.h"
#import "fansViewController.h"
#import "attrViewController.h"
#import "Loginbonus.h"
#import "JCHATConversationViewController.h"
#import "JCHATAlertViewWait.h"
#import "YBVipVC.h"
#import "BusinessCardVC.h"
#import "HeaderBackImgView.h"
#import "TZImagePickerController.h"
#import "YBStorageObj.h"
#import "YBCheckLiveObj.h"
#import "YBOtherCenterMore.h"
#import "YBCenterMoreView.h"

@interface YBCenterTopView()<TZImagePickerControllerDelegate,UINavigationControllerDelegate,UIImagePickerControllerDelegate,FirstLogDelegate>{
    CGFloat _btnHeight;
    
    Loginbonus *firstLV;
    NSString *bonus_switch;
    NSString *bonus_day;
    NSArray  *bonus_list;
    NSString *dayCount;
    NSString *is_bonus;//是否签到
    
    MessageListModel *_userModel;

    UIImage *headBgimg;
    NSString *headBgStr;
}
// 背景
@property(nonatomic,strong)HeaderBackImgView *headImgView;
@property(nonatomic,strong)UIImageView *bigBgIV;

@property(nonatomic,strong)YBButton *returnBtn;     // 返回
@property(nonatomic,strong)YBButton *youngBtn;      // 青少年
@property(nonatomic,strong)YBButton *moreBtn;       // 更多

@property(nonatomic,strong)YBButton *iconBtn;
@property(nonatomic,strong)UIView *liveingV;
@property(nonatomic,strong)UILabel *liveingL;

@property(nonatomic,strong)UILabel *nameL;
@property(nonatomic,strong)UILabel *idL;
@property(nonatomic,strong)UIButton *qrcodeBtn;

// 赞、关注、粉丝等父视图
@property(nonatomic,strong)UIView *itemView1;
@property(nonatomic,strong)UIView *item1Content;
@property(nonatomic,strong)UILabel *zanNumL;
@property(nonatomic,strong)UILabel *fansNumL;
@property(nonatomic,strong)UILabel *followNumL;

@property(nonatomic,strong)YBButton *editBtn;       // 自己-编辑
@property(nonatomic,strong)YBButton *signDayBtn;    // 自己-签到
@property(nonatomic,strong)YBButton *followBtn;     // 他人-关注
@property(nonatomic,strong)YBButton *chatBtn;       // 他人-聊天

// 签名、个人资料等父视图
@property(nonatomic,strong)UIView *itemView2;
@property(nonatomic,strong)UIView *item2Content;
@property(nonatomic,strong)UILabel *signatureL;
@property(nonatomic,strong)UIButton *sexBtn;
@property(nonatomic,strong)UIButton *ageBtn;
@property(nonatomic,strong)UIButton *cityBtn;
@property(nonatomic,strong)UIView *vipBgView;
@property(nonatomic,strong)UIImageView *vipRightBgIV;
@property(nonatomic,strong)UILabel *vipTitleL;
@property(nonatomic,strong)UILabel *vipTimeL;

// 直播小店、我的收藏、直播动态等父视图
@property(nonatomic,strong)UIView *itemView3;

@property(nonatomic,strong)MASConstraint *item2ContentMas;
@property(nonatomic,strong)MASConstraint *vipBgMas;

@end

@implementation YBCenterTopView

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self) {
        [self createUI];
    }
    return self;
}

-(void)createUI {
    _userModel = [[MessageListModel alloc]init];
    
    CGFloat bigIVHeight = _window_width * 200.0/375+20+statusbarHeight;
    
    _bigBgIV = [[UIImageView alloc]init];
    _bigBgIV.contentMode = UIViewContentModeScaleAspectFill;
    _bigBgIV.userInteractionEnabled = YES;
    _bigBgIV.clipsToBounds = YES;
    [self addSubview:_bigBgIV];
    [_bigBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.top.equalTo(self);
        make.height.mas_equalTo(bigIVHeight);
    }];
    UITapGestureRecognizer *headTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(headerImgTap)];
    [_bigBgIV addGestureRecognizer:headTap];
    
    UIImageView *iconShadow = [[UIImageView alloc]init];
    iconShadow.image = [UIImage imageNamed:@"个中头部-遮罩"];
    iconShadow.contentMode = UIViewContentModeScaleAspectFill;
    [_bigBgIV addSubview:iconShadow];
    [iconShadow mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.bottom.centerX.equalTo(_bigBgIV);
        make.top.equalTo(_bigBgIV.mas_centerY).multipliedBy(0.2);
    }];
    
    _returnBtn = [self createBtnStatus:BtnFun_CenterReturn corner:0 havebg:NO];
    [_returnBtn setImage:[UIImage imageNamed:@"个中头部-返回"] forState:0];
    [self addSubview:_returnBtn];
    [_returnBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(10);
        make.top.equalTo(self.mas_top).offset(statusbarHeight+20);
        make.width.height.mas_equalTo(50);
    }];
    
    _youngBtn = [self createBtnStatus:BtnFun_CenterYoung corner:14 havebg:NO];
    [_youngBtn setImage:[UIImage imageNamed:@"个中头部-青年"] forState:0];
    [_youngBtn setTitle:YZMsg(@"青少年模式") forState:0];
    _youngBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 6);
    _youngBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -5, 0, 0);
    _youngBtn.backgroundColor = RGB_COLOR(@"#272727", 0.5);
    [self addSubview:_youngBtn];
    [_youngBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_returnBtn.mas_right);
        make.centerY.equalTo(_returnBtn);
        make.height.mas_equalTo(28);
    }];
    
    _moreBtn = [self createBtnStatus:BtnFun_CenterMore corner:0 havebg:NO];
    [_moreBtn setImage:[UIImage imageNamed:@"个中头部-更多"] forState:0];
    [self addSubview:_moreBtn];
    [_moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-15);
        make.width.height.centerY.equalTo(_returnBtn);
    }];
    _returnBtn.hidden = _youngBtn.hidden = YES;
    
    //
    [self addSubview:self.iconBtn];
    [_iconBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(90);
        make.left.equalTo(self.mas_left).offset(15);
        make.bottom.equalTo(_bigBgIV.mas_bottom).offset(-30);
    }];
    [self addSubview:self.liveingV];
    [_liveingV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerX.centerY.equalTo(_iconBtn);
    }];
    _liveingL.hidden = _liveingV.hidden = YES;;
    
    // 昵称
    [self addSubview:self.nameL];
    [_nameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_iconBtn.mas_right).offset(10);
        make.bottom.equalTo(_iconBtn.mas_centerY).offset(-2);
        make.right.lessThanOrEqualTo(self.mas_right).offset(-15);
    }];
    // id
    [self addSubview:self.idL];
    [_idL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_nameL.mas_left);
        make.top.equalTo(_iconBtn.mas_centerY).offset(2);
    }];
    
    _qrcodeBtn = [self createBtnStatus:BtnFun_CenterQrcode corner:0 havebg:NO];
    [_qrcodeBtn setImage:[UIImage imageNamed:@"center_card"] forState:0];
    [self addSubview:_qrcodeBtn];
    _qrcodeBtn.hidden = YES;
    _qrcodeBtn.jk_touchAreaInsets = UIEdgeInsetsMake(10, 10, 10, 10);
    [_qrcodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(16);
        make.left.equalTo(_idL.mas_right).offset(5);
        make.centerY.equalTo(_idL);
    }];
    
    [self addSubview:self.itemView1];
    [_itemView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self);
        make.top.equalTo(_bigBgIV.mas_bottom).offset(-15);
        make.height.mas_equalTo(70);
    }];
    
    [self addSubview:self.itemView2];
    [_itemView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self);
        make.top.equalTo(_itemView1.mas_bottom).offset(0);
    }];
    
    [self addSubview:self.itemView3];
    [_itemView3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self);
        make.top.equalTo(_itemView2.mas_bottom).offset(0);
        make.height.mas_equalTo(70);
    }];
}
- (void)setIsTabbar:(BOOL)isTabbar {
    _isTabbar = isTabbar;
    _returnBtn.hidden = isTabbar;
}
- (void)setOtherUid:(NSString *)otherUid{
    _otherUid = otherUid;
    if ([_otherUid isEqual:@"0"]) {
        _moreBtn.hidden = YES;
        _followBtn.hidden = _chatBtn.hidden = YES;
    }else if ([_otherUid isEqual:[Config getOwnID]]) {
        _editBtn.hidden = _signDayBtn.hidden = NO;
         //_youngBtn.hidden = NO;
        _qrcodeBtn.hidden = NO;
    }else{
        _followBtn.hidden = _chatBtn.hidden = NO;
         //_youngBtn.hidden = YES;
        _qrcodeBtn.hidden = YES;
    }
}
- (void)setDataDic:(NSDictionary *)dataDic{
    _dataDic = dataDic;
    
    [self addLivingAnimation];
    
    NSURL *bg_url = [NSURL URLWithString:[NSString stringWithFormat:@"%@",[_dataDic valueForKey:@"bg_img"]]];
    [_bigBgIV sd_setImageWithURL:bg_url];
    
    NSString *userId = minstr([_dataDic valueForKey:@"id"]);
    NSString *userName = minstr([_dataDic valueForKey:@"user_nickname"]);
    NSString *userIcon = [_dataDic valueForKey:@"avatar"];
    NSString *isAtt = minstr([_dataDic valueForKey:@"isattention"]);
    NSURL *iconUrl = [NSURL URLWithString:[NSString stringWithFormat:@"%@",userIcon]];
    [_iconBtn sd_setImageWithURL:iconUrl forState:0];
    _nameL.text = userName;
    _idL.text = [NSString stringWithFormat:@"ID:%@",userId];
    _userModel.uidStr = userId;
    _userModel.unameStr = userName;
    _userModel.iconStr = userIcon;
    _userModel.isAtt = isAtt;
    
    int bonus_switch = [minstr([dataDic valueForKey:@"bonus_switch"]) intValue];
    int young_switch = [minstr([dataDic valueForKey:@"teenagers_switch"]) intValue];//[YBYoungManager shareInstance].youngSwitch;
    BOOL showSign = NO;
    if (bonus_switch == 1 && young_switch == 0 && ![PublicObj isUp]) {
        showSign = YES;
    }
    [self resetSignDayBtn:showSign];
    int isattention = [minstr([dataDic valueForKey:@"isattention"]) intValue];
    _followBtn.selected = isattention;
    _zanNumL.text = minstr([dataDic valueForKey:@"praise"]);
    _fansNumL.text = minstr([dataDic valueForKey:@"fans"]);
    _followNumL.text = minstr([dataDic valueForKey:@"follows"]);
    
    // 签名
    _signatureL.text = minstr([_dataDic valueForKey:@"signature"]);
    int sex = [minstr([_dataDic valueForKey:@"sex"])intValue];
    if (sex == 1) {
        [_sexBtn setImage:[UIImage imageNamed:@"个中头部-男"] forState:0];
        [_sexBtn setTitle:YZMsg(@"男") forState:0];
    }else{
        [_sexBtn setImage:[UIImage imageNamed:@"个中头部-女"] forState:0];
        [_sexBtn setTitle:YZMsg(@"女") forState:0];
    }
    [_ageBtn setTitle:minstr([_dataDic valueForKey:@"age"]) forState:0];
    [_cityBtn setTitle:minstr([_dataDic valueForKey:@"city"]) forState:0];
    
    //会员
    if ([[_dataDic valueForKey:@"vipinfo"] isKindOfClass:[NSDictionary class]]) {
        NSDictionary *vipInfo = [_dataDic valueForKey:@"vipinfo"];
        NSString *isVip = minstr([vipInfo valueForKey:@"isvip"]);
        NSString *vipSitch = minstr([vipInfo valueForKey:@"vip_switch"]);
        _vipBgView.hidden = YES;
        if ([vipSitch isEqual:@"1"] &&
            [[Config getOwnID] intValue] > 0 &&
            [_otherUid isEqual:[Config getOwnID]] &&
            ![PublicObj isUp] &&
            young_switch == 0) {
            _vipBgView.hidden = NO;
        }
        [self layoutVipView];
        NSString *vipEndTime = minstr([vipInfo valueForKey:@"vip_endtime"]);
        if ([isVip isEqual:@"1"]) {
            _vipTitleL.text = YZMsg(@"尊贵会员");
            _vipTimeL.text = vipEndTime;
        }else {
            _vipTitleL.text = YZMsg(@"成为会员");
            _vipTimeL.text = YZMsg(@"尊享会员特权");
        }
        [_vipBgView layoutIfNeeded];
        [_vipRightBgIV setBackgroundImage:[UIImage rk_gradientColorImageFromColors:@[RGB_COLOR(@"#f1d7b2", 1),RGB_COLOR(@"#e0b77a", 1)] gradientType:RKGradientTypeLeftToRight imgSize:_vipRightBgIV.size]];
    }
    
    // 直播小店、直播历史、收藏
    [self botSubsItemSet:young_switch];
    
    // 回调整体高度
    [self layoutIfNeeded];
    [_itemView1 jk_setRoundedCorners:UIRectCornerTopLeft|UIRectCornerTopRight radius:10];
    CGFloat getmaxh = CGRectGetMaxY(_itemView3.frame);
    if (self.topEvent) {
        self.topEvent(TopCtrType_Layout, @{@"top_height":@(getmaxh)});
    }
}
-(void)layoutVipView {
    [_item2ContentMas uninstall];
    [_vipBgMas uninstall];
    if (_vipBgView.hidden == YES) {
        [_item2ContentMas install];
    }else{
        [_vipBgMas install];
    }
}
-(void)addLivingAnimation {
    //直播中动画
    NSDictionary *liveInfo = [_dataDic valueForKey:@"liveinfo"];
    if ([minstr([liveInfo valueForKey:@"islive"]) isEqual:@"1"] && ![minstr([liveInfo valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
        _liveingV.hidden = _liveingL.hidden = NO;
        [_iconBtn.imageView.layer addAnimation:[PublicObj avatarScaleAnimation] forKey:@"avatar_scale"];
        [_liveingV.layer addAnimation:[PublicObj bottomScaleAnimation] forKey:@"bottom_scale"];
        _iconBtn.layer.borderColor = Pink_Cor.CGColor;
    }else {
        _liveingV.hidden = _liveingL.hidden = YES;
        _iconBtn.layer.borderColor = UIColor.clearColor.CGColor;
    }
}
-(void)removeLivingAnimation {
    [_iconBtn.imageView.layer removeAllAnimations];
    [_liveingV.layer removeAllAnimations];
    _liveingV.hidden = _liveingL.hidden = YES;
    _iconBtn.layer.borderColor = UIColor.clearColor.CGColor;
}
- (YBButton *)iconBtn{
    if (!_iconBtn) {
        _iconBtn = [YBButton buttonWithType:UIButtonTypeCustom];
        _iconBtn.layer.borderWidth = 1;
        _iconBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
        _iconBtn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
        _iconBtn.imageView.contentMode = UIViewContentModeScaleAspectFill;
        _iconBtn.imageView.clipsToBounds = YES;
        _iconBtn.imageView.layer.masksToBounds = YES;
        _iconBtn.imageView.layer.cornerRadius = 45;
        _iconBtn.layer.masksToBounds = YES;
        _iconBtn.layer.cornerRadius = 45;
        [_iconBtn addTarget:self action:@selector(clickIconBtn) forControlEvents:UIControlEventTouchUpInside];
    }
    return _iconBtn;
}
-(void)clickIconBtn {
    NSDictionary *liveInfo = [_dataDic valueForKey:@"liveinfo"];
    if ([minstr([liveInfo valueForKey:@"islive"]) isEqual:@"1"] && ![minstr([liveInfo valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
        [YBCheckLiveObj checkLiveManeger].liveUid = minstr([liveInfo valueForKey:@"uid"]);
        [YBCheckLiveObj checkLiveManeger].liveStream = minstr([liveInfo valueForKey:@"stream"]);
        [YBCheckLiveObj checkLiveManeger].currentIndex = 0;
        [YBCheckLiveObj checkLiveManeger].listArray = nil;
        [[YBCheckLiveObj checkLiveManeger] checkLiving];
    }
}
- (UIView *)liveingV{
    if (!_liveingV) {
        //直播中动画外边缘
        _liveingV = [[UIView alloc]init];
        _liveingV.layer.borderColor = RGB_COLOR(@"#EA377F", 0.5).CGColor;
        _liveingV.layer.borderWidth = 0.8;
        _liveingV.cornerRadius = 45;
        _liveingV.userInteractionEnabled = NO;
        
        _liveingL = [[UILabel alloc]init];
        _liveingL.text = YZMsg(@"直播");
        _liveingL.textAlignment = NSTextAlignmentCenter;
        _liveingL.textColor = UIColor.whiteColor;
        _liveingL.backgroundColor = Pink_Cor;
        _liveingL.font = SYS_Font(10);
        _liveingL.layer.cornerRadius = 35;
        _liveingL.layer.masksToBounds = YES;
        _liveingL.userInteractionEnabled = YES;
        [_iconBtn.imageView addSubview:_liveingL];
        [_liveingL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.top.equalTo(_iconBtn.imageView);
            make.width.equalTo(_iconBtn.imageView.mas_width).multipliedBy(0.85);
            make.height.mas_equalTo(20);
        }];
    }
    return _liveingV;
}
- (UILabel *)nameL{
    if (!_nameL) {
        _nameL = [[UILabel alloc]init];
        _nameL.textColor = UIColor.whiteColor;
        _nameL.font = [UIFont boldSystemFontOfSize:18];
    }
    return _nameL;
}

- (UILabel *)idL{
    if (!_idL) {
        _idL = [[UILabel alloc]init];
        _idL.textColor = UIColor.whiteColor;
        _idL.font = SYS_Font(16);
    }
    return _idL;
}

- (UIView *)itemView1{
    if (!_itemView1) {
        _itemView1 = [[UIView alloc]init];
        _itemView1.backgroundColor = Normal_Color;
        
        _item1Content = [[UIView alloc]init];
        [_itemView1 addSubview:_item1Content];
        [_item1Content mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(_itemView1.mas_width).offset(-10);
            make.centerX.height.centerY.equalTo(_itemView1);
        }];
        
        NSArray *titleA = @[@"获赞",@"粉丝",@"关注"];
        NSArray *funA = @[@(BtnFun_CenterZanList),@(Btnfun_CenterFanList),@(Btnfun_CenterFollowList)];
        MASViewAttribute *mas_left = _item1Content.mas_left;
        for (int i = 0; i<titleA.count; i++) {
            UIView *itemV = [[UIView alloc]init];
            itemV.backgroundColor = UIColor.clearColor;
            [_item1Content addSubview:itemV];
            [itemV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(mas_left).offset(2);
                make.height.centerY.equalTo(_itemView1);
            }];
            mas_left = itemV.mas_right;
            
            UILabel *numsL = [[UILabel alloc]init];
            numsL.textColor = UIColor.whiteColor;
            numsL.font = [UIFont boldSystemFontOfSize:16];
            numsL.textAlignment = NSTextAlignmentCenter;
            numsL.text = @"0";
            [itemV addSubview:numsL];
            [numsL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(itemV.mas_centerY).offset(-2);
                make.centerX.equalTo(itemV);
            }];
            UILabel *titleL = [[UILabel alloc]init];
            titleL.textColor = RGB_COLOR(@"#c9c9c9", 1);
            titleL.font = SYS_Font(13);
            titleL.text = YZMsg(titleA[i]);
            titleL.textAlignment = NSTextAlignmentCenter;
            [itemV addSubview:titleL];
            [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.centerX.equalTo(numsL);
                make.top.equalTo(itemV.mas_centerY).offset(2);
                make.left.equalTo(itemV.mas_left).offset(15);
                make.right.equalTo(itemV.mas_right).offset(-15);
            }];
            YBButton *shadowBtn = [self createBtnStatus:[funA[i] integerValue] corner:0 havebg:NO];
            [itemV addSubview:shadowBtn];
            [shadowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.height.centerX.centerY.equalTo(itemV);
            }];
            if (shadowBtn.btnFunStatus == BtnFun_CenterZanList) {
                _zanNumL = numsL;
            }else if (shadowBtn.btnFunStatus == Btnfun_CenterFanList){
                _fansNumL = numsL;
            }else if (shadowBtn.btnFunStatus == Btnfun_CenterFollowList){
                _followNumL = numsL;
            }
        }
        
        _btnHeight = 30;
        /// 自己
        // 签到
        _signDayBtn = [self createBtnStatus:BtnFun_CenterDaySign corner:5 havebg:YES];
        [_signDayBtn setImage:[UIImage imageNamed:@"个中头部-签到"] forState:0];
        [_item1Content addSubview:_signDayBtn];
        [_signDayBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(_item1Content.mas_right).offset(-10);
            make.width.height.mas_equalTo(_btnHeight);
            make.centerY.equalTo(_itemView1);
        }];
        // 编辑
        _editBtn = [self createBtnStatus:BtnFun_CenterEdit corner:5 havebg:YES];
        [_editBtn setTitle:YZMsg(@"编辑资料") forState:0];
        _editBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 10);
        [_item1Content addSubview:_editBtn];
        [_editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(_signDayBtn);
            make.right.equalTo(_signDayBtn.mas_left).offset(-10);
            make.height.mas_equalTo(_btnHeight);
        }];
        /// 他人
        // 私信
        _chatBtn = [self createBtnStatus:BtnFun_CenterImMsg corner:5 havebg:YES];
        [_chatBtn setImage:[UIImage imageNamed:@"个中头部-私信"] forState:0];
        [_item1Content addSubview:_chatBtn];
        [_chatBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(_item1Content.mas_right).offset(-10);
            make.width.height.mas_equalTo(_btnHeight);
            make.centerY.equalTo(_itemView1);
        }];
        // 关注
        _followBtn = [self createBtnStatus:BtnFun_CenterFollow corner:5 havebg:NO];
        _followBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 18, 0, 18);
        [_followBtn setTitle:[NSString stringWithFormat:@"+ %@",YZMsg(@"关注")] forState:0];
        [_followBtn setTitle:YZMsg(@"已关注") forState:UIControlStateSelected];
        [_followBtn jk_setBackgroundColor:Pink_Cor forState:0];
        [_followBtn jk_setBackgroundColor:RGB_COLOR(@"#2c2840", 1) forState:UIControlStateSelected];
        [_item1Content addSubview:_followBtn];
        [_followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(_chatBtn);
            make.right.equalTo(_chatBtn.mas_left).offset(-10);
            make.height.mas_equalTo(_btnHeight);
        }];
        _signDayBtn.hidden = _editBtn.hidden = _chatBtn.hidden = _followBtn.hidden = YES;
    }
    return _itemView1;
}
-(void)resetSignDayBtn:(BOOL)show {
    if (![_otherUid isEqual:[Config getOwnID]]) {
        return;
    }
    _signDayBtn.hidden = !show;
    [_signDayBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (show) {
            make.right.equalTo(_item1Content.mas_right).offset(-10);
            make.width.mas_equalTo(_btnHeight);
        }else{
            make.right.equalTo(_item1Content.mas_right).offset(0);
            make.width.mas_equalTo(0);
        }
        make.height.mas_equalTo(_btnHeight);
        make.centerY.equalTo(_itemView1);
    }];
}
/// 创建简单按钮【多处公用，加个别专用属性请在外部单独处理】
-(YBButton *)createBtnStatus:(BtnFunStatus)status corner:(CGFloat)corner havebg:(BOOL)havebg{
    YBButton *ybBtn = [YBButton buttonWithType:UIButtonTypeCustom];
    ybBtn.titleLabel.font = SYS_Font(13);
    [ybBtn setTitleColor:UIColor.whiteColor forState:0];
    ybBtn.btnFunStatus = status;
    ybBtn.layer.masksToBounds = YES;
    ybBtn.layer.cornerRadius = corner;
    if (havebg) {
        ybBtn.backgroundColor = RGB_COLOR(@"#2c2840", 1);
    }
    [ybBtn addTarget:self action:@selector(clickShadowBtn:) forControlEvents:UIControlEventTouchUpInside];
    return ybBtn;
}
-(YBButton *)createSexAgeCity{
    YBButton *ybBtn = [YBButton buttonWithType:UIButtonTypeCustom];
    ybBtn.titleLabel.font = SYS_Font(12);
    [ybBtn setTitleColor:RGB_COLOR(@"#d2d2d2", 1) forState:0];
    ybBtn.layer.masksToBounds = YES;
    ybBtn.layer.cornerRadius = 5;
    ybBtn.backgroundColor = RGB_COLOR(@"#2c2840", 1);
    return ybBtn;
}
#pragma mark - 点击事件
-(void)clickShadowBtn:(YBButton *)sender {
    NSLog(@"====click:%ld",(long)sender.btnFunStatus);
    
    if ([[Config getOwnID] intValue]<=0 && sender.btnFunStatus != BtnFun_CenterReturn) {
        [PublicObj warnLogin];
        return;
    }
    
    if (sender.btnFunStatus == Btnfun_CenterFanList){
        /// 粉丝列表
        fansViewController *fans = [[fansViewController alloc]init];
        fans.fensiUid = _otherUid;
        [[XGGAppDelegate sharedAppDelegate] pushViewController:fans animated:YES];
    }else if (sender.btnFunStatus == Btnfun_CenterFollowList){
        /// 关注列表
        attrViewController *attention = [[attrViewController alloc]init];
        attention.guanzhuUID = _otherUid;
        [[XGGAppDelegate sharedAppDelegate]  pushViewController:attention animated:YES];
    }else if (sender.btnFunStatus == BtnFun_CenterDaySign){
        /// 签到
        [self getSignDayReq];
    }else if (sender.btnFunStatus == BtnFun_CenterShop) {
        /// 小店
        if ([_otherUid isEqual:[Config getOwnID]]) {
            ShopHomeVC *shop = [[ShopHomeVC alloc]init];
            shop.shop_name = YZMsg(@"我的小店");
            shop.shop_switch = minstr([_dataDic valueForKey:@"isshop"]);
            [[XGGAppDelegate sharedAppDelegate] pushViewController:shop animated:YES];
        }else{
            shopDetailVC *vc = [[shopDetailVC alloc]init];
            vc.toUserID  = _otherUid;
            [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];
        }
    }else if (sender.btnFunStatus == BtnFun_CenterLiveRecord){
        /// 直播动态
        PubH5 *h5vc = [[PubH5 alloc]init];
        h5vc.url =[h5url stringByAppendingString:[NSString stringWithFormat:@"/appapi/lrecording/record?uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_otherUid]];
        [[XGGAppDelegate sharedAppDelegate] pushViewController:h5vc animated:YES];
        
    }else if (sender.btnFunStatus == BtnFun_CenterCollection){
        /// 我的收藏
        YBGoodsLikeVC *liveVC = [[YBGoodsLikeVC alloc]init];
        [[XGGAppDelegate sharedAppDelegate]pushViewController:liveVC animated:YES];
    }else if (sender.btnFunStatus == BtnFun_CenterEdit){
        /// 编辑
        EditVC *eVC = [[EditVC alloc]init];
        [[XGGAppDelegate sharedAppDelegate]pushViewController:eVC animated:YES];
    }else if (sender.btnFunStatus == BtnFun_CenterFollow){
        /// 关注-取关
        [self doAttentUser];
    }else if (sender.btnFunStatus == BtnFun_CenterImMsg){
        /// 私信
        [self goToChat];
    }else if (sender.btnFunStatus == BtnFun_CenterVip){
        /// vip
        YBVipVC *vipVC = [[YBVipVC alloc]init];
        [[XGGAppDelegate sharedAppDelegate]pushViewController:vipVC animated:YES];
    }else if (sender.btnFunStatus == BtnFun_CenterQrcode){
        /// 名片
        BusinessCardVC *cardVC = [[BusinessCardVC alloc]init];
        [[XGGAppDelegate sharedAppDelegate]pushViewController:cardVC animated:YES];
    }else if (sender.btnFunStatus == BtnFun_CenterMore){
        /// 更多
        if ([_otherUid isEqual:[Config getOwnID]]) {
            [self showSelfMore];
        }else{
            [self showOtherMore];
        }
    }else if (sender.btnFunStatus == BtnFun_CenterYoung){
        /// 青少年
        [[YBYoungManager shareInstance] checkYoungStatus:YoungFrom_Center];
    }else if (sender.btnFunStatus == BtnFun_CenterReturn){
        [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
    }
}
- (UIView *)itemView2{
    if (!_itemView2) {
        _itemView2 = [[UIView alloc]init];
        _itemView2.backgroundColor = Normal_Color;
        
        _item2Content = [[UIView alloc]init];
        _item2Content.backgroundColor = UIColor.clearColor;
        [_itemView2 addSubview:_item2Content];
        [_item2Content mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_itemView2.mas_left).offset(15);
            make.top.equalTo(_itemView2.mas_top).offset(10);
            make.bottom.equalTo(_itemView2.mas_bottom).offset(-10);
            _item2ContentMas = make.right.equalTo(_itemView2.mas_right).offset(-15);
        }];
        
        // vip
        _vipBgView = [[UIView alloc]init];
        _vipBgView.hidden = YES;
        _vipBgView.backgroundColor = UIColor.clearColor;
        [_itemView2 addSubview:_vipBgView];
        [_vipBgView mas_remakeConstraints:^(MASConstraintMaker *make) {
            _vipBgMas = make.right.equalTo(_itemView2);
            make.left.equalTo(_item2Content.mas_right);
            make.centerY.equalTo(_itemView2);
            make.height.mas_equalTo(36);
        }];
        [self layoutVipView];
        
        UIImageView *vipLeftIV = [[UIImageView alloc]init];
        vipLeftIV.image = [UIImage imageNamed:@"会员背景-圆"];
        [_vipBgView addSubview:vipLeftIV];
        [vipLeftIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.height.centerY.equalTo(_vipBgView);
            make.width.equalTo(vipLeftIV.mas_height);
        }];
        UIImageView *vipRightIV = [[UIImageView alloc]init];
        [_vipBgView addSubview:vipRightIV];
        [_vipBgView bringSubviewToFront:vipLeftIV];
        _vipRightBgIV = vipRightIV;
        [vipRightIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(vipLeftIV.mas_centerX);
            make.height.centerY.right.equalTo(_vipBgView);
        }];
        UILabel *vipTitleL = [[UILabel alloc]init];
        vipTitleL.font = [UIFont boldSystemFontOfSize:11];
        vipTitleL.textColor = RGB_COLOR(@"#8C6936", 1);
        [_vipBgView addSubview:vipTitleL];
        _vipTitleL = vipTitleL;
        [vipTitleL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(vipLeftIV.mas_right).offset(5);
            make.right.equalTo(_vipBgView.mas_right).offset(-5);
            make.bottom.equalTo(_vipBgView.mas_centerY).offset(-1);
        }];
        UILabel *vipTimeL = [[UILabel alloc]init];
        vipTimeL.font = SYS_Font(10);
        vipTimeL.textColor = RGB_COLOR(@"#8C6936", 1);
        [_vipBgView addSubview:vipTimeL];
        _vipTimeL = vipTimeL;
        [vipTimeL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(vipTitleL);
            make.top.equalTo(_vipBgView.mas_centerY).offset(1);
        }];
        YBButton *vipShadowBtn = [self createBtnStatus:BtnFun_CenterVip corner:0 havebg:NO];
        [_vipBgView addSubview:vipShadowBtn];
        [vipShadowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.height.centerY.equalTo(_vipBgView);
        }];
        
        _signatureL = [[UILabel alloc]init];
        _signatureL.font = SYS_Font(13);
        _signatureL.textColor = RGB_COLOR(@"#d2d2d2", 1);
        _signatureL.numberOfLines = 3;
        [_item2Content addSubview:_signatureL];
        [_signatureL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.equalTo(_item2Content);
            make.right.equalTo(_item2Content.mas_right);
        }];
        
        CGFloat item2BtnHeight = 20;
        _sexBtn = [self createSexAgeCity];
        _sexBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 8);
        _sexBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -6, 0, 0);
        [_item2Content addSubview:_sexBtn];
        [_sexBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(item2BtnHeight);
            make.left.equalTo(_item2Content.mas_left);
            make.top.equalTo(_signatureL.mas_bottom).offset(10);
            make.bottom.equalTo(_item2Content.mas_bottom);
        }];
        
        _ageBtn = [self createSexAgeCity];
        _ageBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 8, 0, 8);
        [_item2Content addSubview:_ageBtn];
        [_ageBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.centerY.equalTo(_sexBtn);
            make.left.equalTo(_sexBtn.mas_right).offset(8);
        }];
        
        _cityBtn = [self createSexAgeCity];
        _cityBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 8, 0, 8);
        [_item2Content addSubview:_cityBtn];
        [_cityBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.centerY.equalTo(_sexBtn);
            make.left.equalTo(_ageBtn.mas_right).offset(8);
            make.right.lessThanOrEqualTo(_item2Content.mas_right);
        }];
        [_signatureL setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        [_sexBtn setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        [_ageBtn setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        [_cityBtn setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        [_vipTitleL setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        [_vipTimeL setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _itemView2;
}
- (UIView *)itemView3{
    if (!_itemView3) {
        _itemView3 = [[UIView alloc]init];
        _itemView3.backgroundColor = Normal_Color;
    }
    return _itemView3;
}
#pragma mark - 底部样式
-(void)botSubsItemSet:(int)youngSwitch {
    _itemView3.hidden = youngSwitch;
    [_itemView3 mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self);
        make.top.equalTo(_itemView2.mas_bottom).offset(0);
        if (youngSwitch == 1) {
            make.height.mas_equalTo(0);
        }else{
            make.height.mas_equalTo(70);
        }
    }];
    /**
     * 己方展示:
     * 直播小店  我的收藏
     *
     * 他人展示:
     * 直播小店  直播动态
     */
    [_itemView3 removeAllSubViews];
    NSArray *itemsContent = @[];
    if ([_otherUid isEqual:[Config getOwnID]]) {
        itemsContent = @[
            @{@"name":@"直播小店",
              @"des":@"这里什么都有",
              @"img":@"个中头部-小店",
              @"action":@(BtnFun_CenterShop),
            },
            @{@"name":@"我的收藏",
              @"des":@"收藏好物",
              @"img":@"个中头部-收藏",
              @"action":@(BtnFun_CenterCollection),
            }
        ];
    }else{
        int isshop = [minstr([_dataDic valueForKey:@"isshop"]) intValue];
        itemsContent = @[
            @{@"name":@"直播动态",
              @"des":@"查看历史记录",
              @"img":@"个中头部-直记",
              @"action":@(BtnFun_CenterLiveRecord),
            }
        ];
        if (isshop == 1) {
            itemsContent = @[
                @{@"name":@"直播小店",
                  @"des":@"这里什么都有",
                  @"img":@"个中头部-小店",
                  @"action":@(BtnFun_CenterShop),
                },
                @{@"name":@"直播动态",
                  @"des":@"查看历史记录",
                  @"img":@"个中头部-直记",
                  @"action":@(BtnFun_CenterLiveRecord),
                }
            ];
        }
    }
    
    [self layoutItem3ViewWithArray:itemsContent];
}
-(void)layoutItem3ViewWithArray:(NSArray *)itemsContent {
    CGFloat itemSpace = 5;
    CGFloat leftSpace = 15;
    CGFloat itemW = (_window_width-leftSpace*2-itemSpace)/2;
    MASViewAttribute *mas_first_left = _itemView3.mas_left;
    for (int i = 0; i<itemsContent.count; i++) {
        UIView *itemV = [[UIView alloc]init];
        itemV.backgroundColor = UIColor.clearColor;
        [_itemView3 addSubview:itemV];
        [itemV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(mas_first_left).offset(leftSpace);
            make.width.mas_equalTo(itemW);
            make.height.mas_equalTo(40);
            make.centerY.equalTo(_itemView3);
        }];
        leftSpace = itemSpace;
        mas_first_left = itemV.mas_right;
        
        NSDictionary *subDic = itemsContent[i];
        UIView *leftV = [[UIView alloc]init];
        leftV.layer.cornerRadius = 5;
        leftV.layer.masksToBounds = YES;
        leftV.backgroundColor = RGB_COLOR(@"#2c2840", 1);
        [itemV addSubview:leftV];
        [leftV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.height.centerY.equalTo(itemV);
            make.width.equalTo(leftV.mas_height);
        }];
        UIImageView *lftIV = [[UIImageView alloc]init];
        lftIV.image = [UIImage imageNamed:[subDic valueForKey:@"img"]];
        [leftV addSubview:lftIV];
        [lftIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(22);
            make.centerX.centerY.equalTo(leftV);
        }];
        UILabel *rightTopL = [[UILabel alloc]init];
        rightTopL.text = YZMsg([subDic valueForKey:@"name"]);
        rightTopL.textColor = UIColor.whiteColor;
        rightTopL.font = SYS_Font(15);
        [itemV addSubview:rightTopL];
        [rightTopL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(leftV.mas_right).offset(10);
            make.bottom.equalTo(leftV.mas_centerY).offset(-1);
            make.right.lessThanOrEqualTo(itemV.mas_right);
        }];
        UILabel *rightBotL = [[UILabel alloc]init];
        rightBotL.text = YZMsg([subDic valueForKey:@"des"]);
        rightBotL.textColor = RGB_COLOR(@"#969696", 1);
        rightBotL.font = SYS_Font(13);
        [itemV addSubview:rightBotL];
        [rightBotL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(rightTopL);
            make.top.equalTo(leftV.mas_centerY).offset(1);
            make.right.lessThanOrEqualTo(itemV.mas_right);
        }];
        
        YBButton *shadowBtn = [YBButton buttonWithType:UIButtonTypeCustom];
        shadowBtn.btnFunStatus = [[subDic valueForKey:@"action"] integerValue];
        [shadowBtn addTarget:self action:@selector(clickShadowBtn:) forControlEvents:UIControlEventTouchUpInside];
        [itemV addSubview:shadowBtn];
        [shadowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(itemV);
        }];
    }
}
#pragma mark - 顶部背景
-(void)headerImgTap {
    YBWeakSelf;
    if (!_headImgView) {
        _headImgView = [[HeaderBackImgView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andHeadUrl:minstr([_dataDic valueForKey:@"bg_img"]) andUerid:_otherUid];
        _headImgView.tapEvent = ^(NSString *types) {
            if ([types isEqual:@"hide"]) {
            }else if ([types isEqual:@"拍照"]){
                [weakSelf clickTake];
            }else if ([types isEqual:@"相册"]){
                [weakSelf clickSel];
            }
            [weakSelf.headImgView removeFromSuperview];
            weakSelf.headImgView = nil;
        };
        [[UIApplication sharedApplication].keyWindow addSubview:_headImgView];
    }
}
-(void)clickTake{
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.allowsEditing = YES;
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
    imagePickerController.allowsEditing = YES;
    imagePickerController.showsCameraControls = YES;
    imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    //imagePickerController.mediaTypes = @[(NSString *)kUTTypeImage];
    imagePickerController.modalPresentationStyle = 0;
    [[[XGGAppDelegate sharedAppDelegate]topViewController] presentViewController:imagePickerController animated:YES completion:nil];
}
-(void)clickSel {
    TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.modalPresentationStyle = 0;
    imagePC.showSelectBtn = YES;
    imagePC.allowCrop = NO;
    imagePC.allowPickingOriginalPhoto = NO;
    imagePC.oKButtonTitleColorNormal = Pink_Cor;
    imagePC.allowTakePicture = YES;
    imagePC.allowTakeVideo = NO;
    imagePC.allowPickingVideo = NO;
    imagePC.allowPickingMultipleVideo = NO;
    [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    NSLog(@"------多选择图片--：%@",photos);
    UIImage *img = photos[0];
    headBgimg = img;
    [self updateBgImg];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    if (@available(iOS 11, *)) {
        UIScrollView.appearance.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"]) {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        headBgimg = image;
        [self updateBgImg];
        [picker dismissViewControllerAnimated:YES completion:^{
            [UIApplication sharedApplication].statusBarHidden=NO;
        }];
    }
}
-(void)updateBgImg {
    [MBProgressHUD showMessage:@""];
    YBWeakSelf;
    [[YBStorageObj storageManeger]getCOSType:^(int code) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (code == 0) {
                [weakSelf uploadHeadBgImg];
            }
        });
    }];
}
-(void)uploadHeadBgImg{
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    if (headBgimg) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_centerHeaderBg.png"];
            [[YBStorageObj storageManeger]yb_storageImg:headBgimg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                //图片成功
                headBgStr = key;
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self setBgService];
        });
        NSLog(@"任务完成执行");
    });
}
-(void)setBgService{
    //YBWeakSelf;
    NSDictionary *parDic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"img":headBgStr};
    [YBNetworking postWithUrl:@"User.updateBgImg" Dic:parDic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary*infos = [info firstObject];
            NSString *newUrl = minstr([infos valueForKey:@"bg_img"]);
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:_dataDic];
            [m_dic setObject:newUrl forKey:@"bg_img"];
            _dataDic = [NSDictionary dictionaryWithDictionary:m_dic];
            [_bigBgIV sd_setImageWithURL:[NSURL URLWithString:newUrl]];
//            [weakSelf pullData];
        }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];
        }];
}
#pragma mark - 签到
-(void)getSignDayReq {
    [YBNetworking postWithUrl:@"User.Bonus" Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSArray *infos = info;
            bonus_switch = [NSString stringWithFormat:@"%@",[[infos lastObject] valueForKey:@"bonus_switch"]];
            bonus_day = [[infos lastObject] valueForKey:@"bonus_day"];
            bonus_list = [[infos lastObject] valueForKey:@"bonus_list"];
            //int day = [bonus_day intValue];
            dayCount = minstr([[infos lastObject] valueForKey:@"count_day"]);
            is_bonus = minstr([[infos lastObject] valueForKey:@"is_bonus"]);
            if ([bonus_switch isEqual:@"1"]) {
                [self firstLog];
            }
        }
    } Fail:^(id fail) {
    }];
}
-(void)firstLog{
    firstLV = [[Loginbonus alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)AndNSArray:bonus_list AndDay:bonus_day andDayCount:dayCount andIsBonus:is_bonus];
    firstLV.delegate = self;
    [[UIApplication sharedApplication].keyWindow addSubview:firstLV];
}
-(void)removeView:(NSDictionary*)dic{
    [firstLV removeFromSuperview];
    firstLV = nil;
}
#pragma mark - 私信
-(void)goToChat{
    if (_isChatPage) {
        [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        return;
    }
    NSDictionary *userDic = @{
        @"id":_userModel.uidStr,
        @"user_nickname":_userModel.unameStr,
        @"avatar":_userModel.iconStr,
    };
    [[YBMessageManager shareManager] chatWithUser:userDic];
}
#pragma mark - 关注-取关
-(void)doAttentUser{
    YBWeakSelf;
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"touid":[NSString stringWithFormat:@"%@",[_dataDic valueForKey:@"id"]],
                             @"token":[Config getOwnToken],
                             };
    [YBNetworking postWithUrl:@"User.setAttent" Dic:subdic Suc:^(int code, id info, NSString *msg) {
        if(code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *isattent = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isattent"]];
            if (weakSelf.topEvent) {
                weakSelf.topEvent(TopCtrType_Attent, @{@"isattent":isattent});
            }
            _userModel.isAtt = isattent;
            if ([isattent isEqual:@"1"]) {
                [MBProgressHUD showError:YZMsg(@"已关注")];
            }else{
                [MBProgressHUD showError:YZMsg(@"已取消关注")];
            }
            [weakSelf changeFollowState:[isattent intValue]];
        }else if (code == 700) {
            [PublicObj tokenExpired:msg];
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
-(void)changeFollowState:(int)isAttent {
    _followBtn.selected = isAttent;
}
#pragma mark - 他人主页更多
-(void)showOtherMore{
    if ([[Config getOwnID] intValue]<=0) {
        [PublicObj warnLogin];
        return;
    }
    NSString *url = [NSString stringWithFormat:@"User.checkBlack&uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_otherUid];
    YBWeakSelf;
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSString *u2t = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"u2t"]];
            //u2t  0-未拉黑  1-已拉黑
            NSString *blackTitle = YZMsg(@"拉黑");
            if ([u2t isEqual:@"1"]) {
                blackTitle = YZMsg(@"解除拉黑");
            }
            [weakSelf showSheetWithAttTitle:@"" andBlackTitle:blackTitle];
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
-(void)showSheetWithAttTitle:(NSString *)attStr andBlackTitle:(NSString *)blackStr{
    YBWeakSelf;
    //name_str、id_str、black_str
    NSDictionary *infoDic = @{
        @"name_str":minstr([_dataDic valueForKey:@"user_nickname"]),
        @"id_str":[NSString stringWithFormat:@"ID:%@",[_dataDic valueForKey:@"id"]],
        @"black_str":blackStr,
    };
    [YBOtherCenterMore showOtherMoreWithBtns:infoDic complete:^(BtnFunStatus clickType) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (clickType == BtnFun_CenterImMsg) {
                [weakSelf goToChat];
            }else if(clickType == BtnFun_CenterReport){
                [weakSelf doReportUser];
            }else if (clickType == BtnFun_CenterBlack){
                [weakSelf setBlack];
            }
        });
    }];
}
#pragma mark - 举报
-(void)doReportUser{
    PubH5 *h5vc = [[PubH5 alloc]init];
    h5vc.url =[h5url stringByAppendingString:[NSString stringWithFormat:@"/appapi/userreport/index?uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_otherUid]];
    [[XGGAppDelegate sharedAppDelegate] pushViewController:h5vc animated:YES];
}
#pragma mark - 拉黑
-(void)setBlack {
     NSString *url = [NSString stringWithFormat:@"User.setBlack&uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],_otherUid];
    YBWeakSelf;
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSString *infoDic = [info firstObject];
            NSString *isBlakc = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"isblack"]];
            if ([isBlakc isEqual:@"1"]) {
                [MBProgressHUD showPop:YZMsg(@"拉黑成功")];
                if ([_userModel.isAtt isEqual:@"1"]) {
                    _userModel.isAtt = @"0";
                    [weakSelf changeFollowState:0];
                    if (weakSelf.topEvent) {
                        weakSelf.topEvent(TopCtrType_Attent, @{@"isattent":@"0"});
                    }
                }
            }else {
                [MBProgressHUD showPop:YZMsg(@"解除拉黑成功")];
            }
        }else {
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}
#pragma mark - 自己个人主页更多
-(void)showSelfMore {
    [YBCenterMoreView showMoreViewWithSeller:minstr([_dataDic valueForKey:@"isshop"])];
}

@end
