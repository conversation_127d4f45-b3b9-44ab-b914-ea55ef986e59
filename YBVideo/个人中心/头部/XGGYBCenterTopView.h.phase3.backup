//
//  YBCenterTopView.h
//  YBVideo
//
//  Created by YB007 on 2022/5/30.
//  Copyright © 2022 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,TopCtrType) {
    TopCtrType_Default,
    TopCtrType_Layout,      // 布局完成
    TopCtrType_Attent,
};

typedef void (^CenterTopBlock)(TopCtrType ctrType,NSDictionary *resDic);

@interface YBCenterTopView : UIView

@property(nonatomic,strong)NSString *otherUid;
@property(nonatomic,copy)CenterTopBlock topEvent;
@property(nonatomic,strong)NSDictionary *dataDic;
@property(nonatomic,assign)BOOL isChatPage;             //从聊天点击头像而来
//tabbar
@property(nonatomic,assign)BOOL isTabbar;
-(void)removeLivingAnimation;

@end


