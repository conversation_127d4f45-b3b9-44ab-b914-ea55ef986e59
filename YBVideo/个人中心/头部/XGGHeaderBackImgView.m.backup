//
//  HeaderBackImgView.m
//  YBVideo
//
//  Created by ybRRR on 2021/12/22.
//  Copyright © 2021 cat. All rights reserved.
//

#import "HeaderBackImgView.h"

@implementation HeaderBackImgView
-(void)hideTapClick{
    if (self.tapEvent) {
        self.tapEvent(@"hide");
    }
}
-(instancetype)initWithFrame:(CGRect)frame andHeadUrl:(NSString *)urls andUerid:(NSString *)userID
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = RGB(17,13,36);
        UITapGestureRecognizer *hideTaps = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideTapClick)];
        [self addGestureRecognizer:hideTaps];
        _headImgView = [[UIImageView alloc]init];
        _headImgView.frame = CGRectMake(0, 0, _window_width, _window_height);
        _headImgView.contentMode = UIViewContentModeScaleAspectFit;
        _headImgView.userInteractionEnabled = YES;
        [_headImgView sd_setImageWithURL:[NSURL URLWithString:urls]];
        [self addSubview:_headImgView];
        
        UIButton *changeBtn = [UIButton buttonWithType:0];
        changeBtn.frame = CGRectMake(0, _window_height-120, 213, 34);
        changeBtn.centerX = _headImgView.centerX;
        changeBtn.layer.cornerRadius = 5;
        changeBtn.layer.masksToBounds = YES;
        [changeBtn setBackgroundColor:RGB(29,26,46)];
        [changeBtn setTitle:YZMsg(@"更换") forState:0];
        [changeBtn setTitleColor:UIColor.whiteColor forState:0];
        changeBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [changeBtn addTarget:self action:@selector(changeBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [_headImgView addSubview:changeBtn];
        
        UIButton *downLoadBtn = [UIButton buttonWithType:0];
        downLoadBtn.frame = CGRectMake(_window_width-64, _window_height-64, 34, 34);
        [downLoadBtn setImage:[UIImage imageNamed:@"header_下载"] forState:0];
        [downLoadBtn addTarget:self action:@selector(downLoadBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [_headImgView addSubview:downLoadBtn];
        
        if (![userID isEqual:[Config getOwnID]]) {
            changeBtn.hidden = YES;
            downLoadBtn.hidden = YES;
        }

    }
    return self;
}
-(void)changeBtnClick{
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相机") complete:^{
        [weakSelf clickTake];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相册") complete:^{
        [weakSelf clickSel];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];

}
-(void)downLoadBtnClick{
    UIImage *dowloadimg = _headImgView.image;
    [[PHPhotoLibrary sharedPhotoLibrary]performChanges:^{
        [PHAssetChangeRequest creationRequestForAssetFromImage:dowloadimg];
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        if (error) {
            NSLog(@"%@",@"保存失败");
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD showError:YZMsg(@"保存失败")];
            });
        } else {
            NSLog(@"%@",@"保存成功");
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD showError:YZMsg(@"保存成功")];
            });
        }
    }];

}
#pragma mark -   UIImagePickerControllerDelegate
//拍照
-(void)clickTake {
    if (self.tapEvent) {
        self.tapEvent(@"拍照");
    }
}

-(void)clickSel {
    if (self.tapEvent) {
        self.tapEvent(@"相册");
    }
}

@end
