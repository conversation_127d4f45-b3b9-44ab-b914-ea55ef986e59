//
//  WatchRecordListCell.m
//  YBVideo
//
//  Created by YB007 on 2020/12/8.
//  Copyright © 2020 cat. All rights reserved.
//

#import "WatchRecordListCell.h"

@implementation WatchRecordListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}


- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    [_thumbIV sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"thumb"])]];
    NSString *titleStr = minstr([dataDic valueForKey:@"title"]);
    _titleL.text = titleStr.length > 0 ? titleStr : YZMsg(@"当前视频未填写视频标题");
    if ([[dataDic valueForKey:@"userinfo"] isKindOfClass:[NSDictionary class]]) {
        NSDictionary *userInfo = [dataDic valueForKey:@"userinfo"];
        _userNameL.text = minstr([userInfo valueForKey:@"user_nickname"]);
    }else {
        _userNameL.text = @"";
    }
    
    
}


@end
