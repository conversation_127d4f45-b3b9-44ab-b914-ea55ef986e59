//
//  YBGoodsInfoVC.m
//  YBVideo
//
//  Created by YB007 on 2020/8/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBGoodsInfoVC.h"
#import "YBGoodsListVC.h"
#import "YBCenterVC.h"
#import "YBVideoAddGoodsVC.h"
@interface YBGoodsInfoVC ()
{
    NSDictionary *_infoDic;
}
@property(nonatomic,strong)UIImageView *goodsThumbIV;
@property(nonatomic,strong)UIButton *goodsStateBtn;
@property(nonatomic,strong)UILabel *goodsNameL;
@property(nonatomic,strong)UILabel *goodsDesL;
@property(nonatomic,strong)UILabel *goodsPriceL;
@property(nonatomic,strong)UILabel *goodsOldPL;
@property(nonatomic,strong)UILabel *goodsOldLine;
@property(nonatomic,strong)UIButton *botGoodsLikeBtn;

@property(nonatomic,strong)UIImageView *userAvatarIV;
@property(nonatomic,strong)UILabel *shopNameL;

@end

@implementation YBGoodsInfoVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"商品详情");
     [self.rightBtn setImage:[UIImage imageNamed:@"pub_more"] forState:0];
    
    self.view.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
    
    //
    _goodsThumbIV = [[UIImageView alloc]init];
    _goodsThumbIV.contentMode = UIViewContentModeScaleAspectFill;
    _goodsThumbIV.clipsToBounds = YES;
    [self.view addSubview:_goodsThumbIV];
    [_goodsThumbIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.naviView.mas_bottom);
        make.width.centerX.equalTo(self.view);
    }];
    
    _goodsStateBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _goodsStateBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 12, 0, 12);
    _goodsStateBtn.layer.cornerRadius = 12;
    _goodsStateBtn.layer.masksToBounds = YES;
    _goodsStateBtn.backgroundColor = RGB_COLOR(@"#000000", 0.3);
    [_goodsStateBtn setTitleColor:UIColor.whiteColor forState:0];
    _goodsStateBtn.titleLabel.font = SYS_Font(12);
    [_goodsThumbIV addSubview:_goodsStateBtn];
    [_goodsStateBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_goodsThumbIV.mas_right).offset(-10);
        make.top.equalTo(_goodsThumbIV.mas_top).offset(8);
        make.height.mas_equalTo(24);
    }];
    
    //
    UIView *goodsView = [[UIView alloc]init];
    goodsView.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:goodsView];
    [goodsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.top.equalTo(_goodsThumbIV.mas_bottom);
        make.height.mas_equalTo(120);
    }];
    _goodsNameL = [[UILabel alloc]init];
    _goodsNameL.font = [UIFont boldSystemFontOfSize:15];
    _goodsNameL.textColor = RGB_COLOR(@"#323232", 1);
    [goodsView addSubview:_goodsNameL];
    [_goodsNameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(goodsView.mas_left).offset(15);
        make.right.lessThanOrEqualTo(goodsView.mas_right).offset(-15);
        make.top.equalTo(goodsView.mas_top).offset(15);
    }];
    _goodsDesL = [[UILabel alloc]init];
    _goodsDesL.font = SYS_Font(13);
    _goodsDesL.textColor = RGB_COLOR(@"#323232", 1);
    _goodsDesL.numberOfLines = 2;
    [goodsView addSubview:_goodsDesL];
    [_goodsDesL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(goodsView.mas_left).offset(15);
        make.right.lessThanOrEqualTo(goodsView.mas_right).offset(-15);
        make.top.equalTo(_goodsNameL.mas_bottom).offset(10);
    }];
    
    _goodsPriceL = [[UILabel alloc]init];
    _goodsPriceL.font = [UIFont boldSystemFontOfSize:15];
    _goodsPriceL.textColor = Pink_Cor;
    [goodsView addSubview:_goodsPriceL];
    [_goodsPriceL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_goodsNameL);
        make.bottom.equalTo(goodsView.mas_bottom).offset(-10);
    }];
    
    _goodsOldPL = [[UILabel alloc]init];
    _goodsOldPL.font = SYS_Font(13);
    _goodsOldPL.textColor = RGB_COLOR(@"#c9c9c9", 1);
    [goodsView addSubview:_goodsOldPL];
    [_goodsOldPL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_goodsPriceL.mas_right).offset(13);
        make.bottom.equalTo(_goodsPriceL.mas_bottom);
    }];
    _goodsOldLine = [[UILabel alloc]init];
    _goodsOldLine.backgroundColor = RGB_COLOR(@"#c9c9c9", 1);
    [goodsView addSubview:_goodsOldLine];
    [_goodsOldLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_goodsOldPL.mas_width).offset(5);
        make.center.equalTo(_goodsOldPL);
        make.height.mas_equalTo(1);
    }];
    
    //
    UIView *userView = [[UIView alloc]init];
    userView.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:userView];
    [userView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.top.equalTo(goodsView.mas_bottom).offset(10);
        make.height.mas_equalTo(80);
    }];
    
    _userAvatarIV = [[UIImageView alloc]init];
    _userAvatarIV.layer.cornerRadius = 28;
    _userAvatarIV.layer.masksToBounds = YES;
    _userAvatarIV.backgroundColor = UIColor.redColor;
    [userView addSubview:_userAvatarIV];
    [_userAvatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(userView);
        make.left.equalTo(userView.mas_left).offset(15);
        make.width.height.mas_equalTo(56);
    }];
    
    UIButton *enterShopBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    enterShopBtn.titleLabel.font = SYS_Font(13);
    [enterShopBtn setTitleColor:Pink_Cor forState:0];
    NSString *tStr = YZMsg(@"进店逛逛");
    [enterShopBtn setTitle:[NSString stringWithFormat:@"%@ >",tStr] forState:0];
    [enterShopBtn addTarget:self action:@selector(clickEnterShop) forControlEvents:UIControlEventTouchUpInside];
    [userView addSubview:enterShopBtn];
    [enterShopBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(userView);
        make.right.equalTo(userView.mas_right).offset(-15);
    }];
    
    _shopNameL = [[UILabel alloc]init];
    _shopNameL.font = _goodsNameL.font;
    _shopNameL.textColor = _goodsNameL.textColor;
    [userView addSubview:_shopNameL];
    [_shopNameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(_userAvatarIV);
        make.left.equalTo(_userAvatarIV.mas_right).offset(10);
        make.right.lessThanOrEqualTo(enterShopBtn.mas_left).offset(-5);
    }];
    
    UIView *bottomView = [[UIView alloc]init];
    bottomView.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:bottomView];
    [bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.top.equalTo(userView.mas_bottom).offset(10);
        make.height.mas_equalTo(80+ShowDiff);
        make.bottom.equalTo(self.view.mas_bottom);
    }];
    
    UIButton *botShopBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    botShopBtn.titleLabel.font = SYS_Font(13);
    [botShopBtn setTitle:YZMsg(@"店铺") forState:0];
    botShopBtn.backgroundColor = UIColor.clearColor;
    [botShopBtn setImage:[UIImage imageNamed:@"详情-店铺"] forState:0];
    [botShopBtn setTitleColor:RGB_COLOR(@"#6e6d6d", 1) forState:0];
    [botShopBtn addTarget:self action:@selector(clickEnterShop) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:botShopBtn];
    [botShopBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(bottomView.mas_width).multipliedBy(0.2);
        make.height.mas_equalTo(80);
        make.top.left.equalTo(bottomView);
    }];
    
    UIButton *botCenterBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    botCenterBtn.titleLabel.font = SYS_Font(13);
    botCenterBtn.backgroundColor = UIColor.clearColor;
    [botCenterBtn setTitle:YZMsg(@"主页") forState:0];
    [botCenterBtn setImage:[UIImage imageNamed:@"详情-个中"] forState:0];
    [botCenterBtn setTitleColor:RGB_COLOR(@"#6e6d6d", 1) forState:0];
    [botCenterBtn addTarget:self action:@selector(clickBotCenterBtn) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:botCenterBtn];
    [botCenterBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerY.equalTo(botShopBtn);
        make.left.equalTo(botShopBtn.mas_right);
    }];
    
    _botGoodsLikeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _botGoodsLikeBtn.titleLabel.font = SYS_Font(13);
    _botGoodsLikeBtn.backgroundColor = UIColor.clearColor;
    [_botGoodsLikeBtn setTitle:YZMsg(@"收藏") forState:0];
    [_botGoodsLikeBtn setImage:[UIImage imageNamed:@"详情-未收藏"] forState:0];
    [_botGoodsLikeBtn setImage:[UIImage imageNamed:@"详情-已收藏"] forState:UIControlStateSelected];
    [_botGoodsLikeBtn setTitleColor:RGB_COLOR(@"#6e6d6d", 1) forState:0];
    [_botGoodsLikeBtn addTarget:self action:@selector(clickBotLikeBtn) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:_botGoodsLikeBtn];
    [_botGoodsLikeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerY.equalTo(botShopBtn);
        make.left.equalTo(botCenterBtn.mas_right);
    }];
    
    UIButton *botBuyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    botBuyBtn.titleLabel.font = SYS_Font(13);
    [botBuyBtn setTitle:YZMsg(@"立即购买") forState:0];
    [botBuyBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
    botBuyBtn.backgroundColor = Pink_Cor;
    [botBuyBtn addTarget:self action:@selector(clickBotBuyBtn) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:botBuyBtn];
    [botBuyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.centerY.equalTo(botShopBtn);
        make.left.equalTo(_botGoodsLikeBtn.mas_right).offset(20);
        make.right.equalTo(bottomView.mas_right);
    }];
    
    [bottomView layoutIfNeeded];
    botShopBtn = [PublicObj setUpImgDownText:botShopBtn];
    botCenterBtn = [PublicObj setUpImgDownText:botCenterBtn];
    _botGoodsLikeBtn = [PublicObj setUpImgDownText:_botGoodsLikeBtn];
    
    [self pullData];
    
}

-(void)pullData {
    if ([PublicObj checkNull:_goodsID]) {
        return;
    }
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Shop.getGoodsInfo" Dic:@{@"goodsid":_goodsID} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            [weakSelf setData:infoDic];
        }else {
            [MBProgressHUD showPop:msg];
        }
        
    } Fail:^(id fail) {
        
    }];
}

-(void)setData:(NSDictionary *)infoDic {
    _infoDic = infoDic;
    
    [_goodsThumbIV sd_setImageWithURL:[NSURL URLWithString:[NSString stringWithFormat:@"%@",[infoDic valueForKey:@"thumb"]]]];
    //status代表 -2管理员下架, -1商家下架, 0审核中,  1通过,  2拒绝
    int state = [minstr([_infoDic valueForKey:@"status"]) intValue];
    _goodsStateBtn.hidden = NO;
    if (state == -2 || state == -1) {
        [_goodsStateBtn setTitle:YZMsg(@"已下架") forState:0];
    }else if (state == 0){
        self.rightBtn.hidden = YES;
        [_goodsStateBtn setTitle:YZMsg(@"审核中") forState:0];
    }else if (state == 1){
        _goodsStateBtn.hidden = YES;
    }else {
        [_goodsStateBtn setTitle:YZMsg(@"已拒绝") forState:0];
    }
    
    if ([_touserID isEqual:[Config getOwnID]] && state != 0) {
        self.rightBtn.hidden = NO;
    }else {
        self.rightBtn.hidden = YES;
    }
    if (_infoType == InfoEnterType_Video || _infoType == InfoEnterType_Record) {
        self.rightBtn.hidden = YES;
    }
    
    _goodsNameL.text = minstr([_infoDic valueForKey:@"name"]);
    _goodsDesL.text = minstr([_infoDic valueForKey:@"des"]);
    _goodsPriceL.text = [NSString stringWithFormat:@"￥ %@",[_infoDic valueForKey:@"price"]];
    _goodsOldPL.hidden = _goodsOldLine.hidden = YES;
    if (![PublicObj checkNull:minstr([_infoDic valueForKey:@"old_price"])]) {
        _goodsOldPL.hidden = _goodsOldLine.hidden = NO;
        _goodsOldPL.text = [NSString stringWithFormat:@"￥%@",[_infoDic valueForKey:@"old_price"]];
    }
    
    [_userAvatarIV sd_setImageWithURL:[NSURL URLWithString:[NSString stringWithFormat:@"%@",[_infoDic valueForKey:@"shop_avatar"]]]];
    _shopNameL.text = minstr([_infoDic valueForKey:@"shop_name"]);
    
    int isCollect = [minstr([_infoDic valueForKey:@"iscollect"]) intValue];
    _botGoodsLikeBtn.selected = isCollect;
}
-(void)clickEnterShop {
    if ([_touserID isEqual:[Config getOwnID]]) {
        [MBProgressHUD showPop:YZMsg(@"预览不支持该功能")];
        return;
    }
    if (_infoType == InfoEnterType_GList) {
        [self.navigationController popViewControllerAnimated:YES];
    }else{
        YBGoodsListVC *gVC = [[YBGoodsListVC alloc]init];
        gVC.enterType = GoodsEnterType_GoodInfo;
        gVC.touserID = _touserID;
        YBWeakSelf;
        gVC.infoBakcEvent = ^(NSDictionary *newDic) {
            _goodsID = minstr([newDic valueForKey:@"id"]);
            [weakSelf pullData];
        };
        [[XGGAppDelegate sharedAppDelegate]pushViewController:gVC animated:YES];
    }
}
-(void)clickBotCenterBtn {
    if ([_touserID isEqual:[Config getOwnID]]) {
        [MBProgressHUD showPop:YZMsg(@"预览不支持该功能")];
        return;
    }
    YBCenterVC *center = [[YBCenterVC alloc]init];
    center.otherUid = _touserID;
    center.isPush = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:center animated:YES];
}
-(void)clickBotLikeBtn {
    if ([_touserID isEqual:[Config getOwnID]]) {
        [MBProgressHUD showPop:YZMsg(@"预览不支持该功能")];
        return;
    }
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Shop.setCollect" Dic:@{@"goodsid":_goodsID} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showPop:msg];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            int isCollect = [minstr([infoDic valueForKey:@"iscollect"]) intValue];
            _botGoodsLikeBtn.selected = isCollect;
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
-(void)clickBotBuyBtn {
    if ([_touserID isEqual:[Config getOwnID]]) {
        [MBProgressHUD showPop:YZMsg(@"预览不支持该功能")];
        return;
    }
    
    int state = [minstr([_infoDic valueForKey:@"status"]) intValue];
    if (state == -2 || state == -1) {
        [MBProgressHUD showPop:YZMsg(@"商品已下架")];
        return;
    }
    
    if ([minstr([_infoDic valueForKey:@"type"]) isEqual:@"1"]) {
        [PublicObj openWXMiniProgram:minstr([_infoDic valueForKey:@"href"])];
    }else{
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:minstr([_infoDic valueForKey:@"href"])]];
    }

}

- (void)clickNaviRightBtn {
    
    //status代表 -2管理员下架, -1商家下架, 0审核中,  1通过,  2拒绝
    int state = [minstr([_infoDic valueForKey:@"status"]) intValue];
    NSString *upDownStr = YZMsg(@"下架");
    if (state == -2 || state == -1) {
        upDownStr = YZMsg(@"上架");
    }
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_FunPink andTitle:YZMsg(@"编辑") complete:^{
        [weakSelf goodsEdite];
    }];
    if (state != 2) { //拒绝不显示 上、下架
        [sheet addActionWithType:RKSheet_FunPink andTitle:upDownStr complete:^{
            [weakSelf goodsUpDown];
        }];
    }
    [sheet addActionWithType:RKSheet_FunPink andTitle:YZMsg(@"删除") complete:^{
        [weakSelf goodsDelete];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];
}

-(void)goodsEdite {
    //编辑
    YBVideoAddGoodsVC *gVC = [[YBVideoAddGoodsVC alloc]init];
    gVC.isUpdateGoods = YES;
    gVC.goodsInfo = _infoDic;
    YBWeakSelf;
    gVC.addRefreshEvnet = ^{
        [weakSelf pullData];
    };
    [[XGGAppDelegate sharedAppDelegate]pushViewController:gVC animated:YES];
    
}
-(void)goodsUpDown {
    //上、下架
    //status代表 -2管理员下架, -1商家下架, 0审核中[审核中不显示上、下架],  1通过,  2拒绝[被拒绝不显示上、下架]
    int state = [minstr([_infoDic valueForKey:@"status"]) intValue];
    NSString *upDownStr = (state == -2 || state == -1)?@"1":@"-1";
    if ([upDownStr isEqual:@"-1"]) {
        NSDictionary *contentDic = @{@"title":@"",@"msg":YZMsg(@"商品下架后,其它用户将看不到您发布的商品,是否将其下架?"),@"left":YZMsg(@"确定"),@"right":YZMsg(@"取消"),@"richImg":@""};
        YBWeakSelf;
        YBAlertView *alertV = [YBAlertView showAlertView:contentDic complete:^(int eventType) {
            if (eventType == 0) {
                [weakSelf goodsUpDownRes:upDownStr];
            }
        }];
        [alertV.sureBtn setTitleColor:RGB_COLOR(@"#323232", 1) forState:0];
        [alertV.cancleBtn setTitleColor:RGB_COLOR(@"#323232", 1) forState:0];
    }else {
        [self goodsUpDownRes:upDownStr];
    }
    
}

-(void)goodsUpDownRes:(NSString *)upDownStr {
    YBWeakSelf;
    [MBProgressHUD showMessage:@""];
    //status(状态 1上架   -1下架)
    [YBNetworking postWithUrl:@"Shop.setGoodsStatus" Dic:@{@"goodsid":_goodsID,@"status":upDownStr} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showPop:msg];
        if (code == 0) {
            [weakSelf pullData];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}

-(void)goodsDelete {
    //删除
    NSDictionary *contentDic = @{@"title":@"",@"msg":YZMsg(@"商品删除后,其它用户将看不到您发布的商品,是否将其删除?"),@"left":YZMsg(@"确定"),@"right":YZMsg(@"取消"),@"richImg":@""};
    YBWeakSelf;
    YBAlertView *alertV = [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 0) {
            [weakSelf goDelGoodsRes];
        }
    }];
    [alertV.sureBtn setTitleColor:RGB_COLOR(@"#323232", 1) forState:0];
    [alertV.cancleBtn setTitleColor:RGB_COLOR(@"#323232", 1) forState:0];
    
}
-(void)goDelGoodsRes {
    YBWeakSelf;
    [MBProgressHUD showMessage:@""];
    [YBNetworking postWithUrl:@"Shop.delGoods" Dic:@{@"goodsid":_goodsID} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showPop:msg];
        if (code == 0) {
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
}
@end
