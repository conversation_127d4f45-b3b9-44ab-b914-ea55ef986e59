//
//  YBGoodsListVC.h
//  YBVideo
//
//  Created by YB007 on 2020/8/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBBaseViewController.h"
#import "YBGoodsListCell.h"

typedef void (^PublishVideoBlock)(BOOL isSel, NSDictionary *goodsDic);  //发布视频关联商品
typedef void (^GoodsRefreshBlock)(void);                    //直播添加商品返回刷新
typedef void (^InfoBakcBlock)(NSDictionary *newDic);        //从观看商品视频、直播间在售-->详情页---->进店铺: 返回详情页

@interface YBGoodsListVC : YBBaseViewController

@property(nonatomic,copy)PublishVideoBlock publishEvent;    //发布视频关联商品
@property(nonatomic,copy)GoodsRefreshBlock refreshEvent;    //直播添加商品返回刷新
@property(nonatomic,copy)InfoBakcBlock infoBakcEvent;       //从观看商品视频、直播间在售-->详情页---->进店铺: 返回详情页
@property(nonatomic,assign)GoodsEnterType enterType;
@property(nonatomic,strong)NSString *touserID;

@property(nonatomic,strong)NSArray *selGoodsList;           //选中的商品【目的:发布视频选择(选中->返回发布->再去重新选择商品,保留上次选中状态)】

@end


