//
//  YBGoodsListCell.h
//  YBVideo
//
//  Created by YB007 on 2020/8/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,GoodsEnterType) {
    GoodsEnterType_Default,     //默认
    GoodsEnterType_Live,        //直播添加商品进入
    GoodsEnterType_Publish,     //发布视频关联商品
    GoodsEnterType_Shop,        //个中店铺进入
    GoodsEnterType_GoodInfo,    //观看商品视频--商品详情进入
};

@interface YBGoodsListCell : UICollectionViewCell

@property (weak, nonatomic) IBOutlet UIView *bgView;

@property (weak, nonatomic) IBOutlet UIImageView *thumbIV;
@property (weak, nonatomic) IBOutlet UILabel *goodsName;
@property (weak, nonatomic) IBOutlet UILabel *priceL;
@property (weak, nonatomic) IBOutlet UILabel *oldPriceL;
@property (weak, nonatomic) IBOutlet UILabel *oldPL;
@property (weak, nonatomic) IBOutlet UIImageView *selFlagIV;//角标
@property (weak, nonatomic) IBOutlet UIView *selFlagView;   //选中背景
@property (weak, nonatomic) IBOutlet UIButton *goodsStateBtn;

@property(nonatomic,strong)NSString *touserID;
@property(nonatomic,assign)GoodsEnterType enterType;
@property(nonatomic,strong)NSDictionary *dataDic;

@end


