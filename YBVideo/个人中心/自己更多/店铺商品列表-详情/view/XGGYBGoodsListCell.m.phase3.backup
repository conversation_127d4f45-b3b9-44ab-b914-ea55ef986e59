//
//  YBGoodsListCell.m
//  YBVideo
//
//  Created by YB007 on 2020/8/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBGoodsListCell.h"

@implementation YBGoodsListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}


- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
  
    [_thumbIV sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])]];
    _goodsName.text = minstr([_dataDic valueForKey:@"name"]);
    _priceL.text = [NSString stringWithFormat:@"￥ %@",[_dataDic valueForKey:@"price"]];
    _oldPriceL.hidden = _oldPL.hidden = YES;
    if (![PublicObj checkNull:minstr([_dataDic valueForKey:@"old_price"])]) {
        _oldPriceL.hidden = _oldPL.hidden = NO;
        _oldPriceL.text = [NSString stringWithFormat:@"￥%@",[_dataDic valueForKey:@"old_price"]];
    }
    
    //status 代表 0审核中, -1商家下架, 1通过, -2管理员下架, 2拒绝
    int state = [minstr([_dataDic valueForKey:@"status"]) intValue];
    if (state == -1 || state == -2) {
        [_goodsStateBtn setTitle:YZMsg(@"已下架") forState:0];
    }else if (state == 0){
        [_goodsStateBtn setTitle:YZMsg(@"审核中") forState:0];
    }else if (state == 2){
        [_goodsStateBtn setTitle:YZMsg(@"已拒绝") forState:0];
    }
    
    if ([_touserID isEqual:[Config getOwnID]]) {
        if (_enterType == GoodsEnterType_Shop || _enterType == GoodsEnterType_GoodInfo) {
            /*
            _selFlagIV.hidden = YES;
            _bgView.layer.borderColor = UIColor.clearColor.CGColor;
            */
            _selFlagView.hidden = YES;
            _goodsStateBtn.hidden = NO;
            if (state == 1) {
                _goodsStateBtn.hidden = YES;
            }
        }else{
            //直播间商品 :1-选中 0-未选中
            NSString *issale = minstr([_dataDic valueForKey:@"issale"]);
            if ([issale isEqual:@"1"]) {
                /*
                _selFlagIV.hidden = NO;
                _bgView.layer.borderColor = Pink_Cor.CGColor;
                */
                _selFlagView.hidden = NO;
            }else{
                /*
                _selFlagIV.hidden = YES;
                _bgView.layer.borderColor = UIColor.clearColor.CGColor;
                */
                _selFlagView.hidden = YES;
            }
            _goodsStateBtn.hidden = YES;
        }
    }else{
        //_selFlagIV.hidden = YES;
        _selFlagView.hidden = YES;
        _goodsStateBtn.hidden = YES;
    }
    
    
}




@end
