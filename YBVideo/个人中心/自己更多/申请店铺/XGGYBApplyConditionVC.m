//
//  YBApplyConditionVC.m
//  YBVideo
//
//  Created by YB007 on 2020/8/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBApplyConditionVC.h"
#import "YBApplyConditionCell.h"
#import "YBApplyStoreVC.h"
#import "YBUserAuthVC.h"
@interface YBApplyConditionVC ()<UITableViewDelegate,UITableViewDataSource>
{
    BOOL _canApply;
}
@property(nonatomic,strong)UITableView *tableView;

@property(nonatomic,strong)UIView *headerView;
@property(nonatomic,strong)UILabel *headerTitleL;
@property(nonatomic,strong)UILabel *headerDesL;

@property(nonatomic,strong)UIView *footerView;
@property(nonatomic,strong)UILabel *footerTitelL;
@property(nonatomic,strong)UILabel *footerDesL;
@property(nonatomic,strong)UILabel *footerContentL;
@property(nonatomic,strong)UIButton *applyBtn;

@end

@implementation YBApplyConditionVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.titleL.text = YZMsg(@"店铺申请");
    
    [self createHeaderView];
    [self createFooterView];
    [self.view addSubview:self.tableView];
    [self.tableView reloadData];
    
    [self setShowData];
    
}

-(void)setShowData {
    
    _canApply = [minstr([_dataDic valueForKey:@"isapply"]) boolValue];
    
    NSArray *explainA = [NSArray arrayWithArray:[_dataDic valueForKey:@"explain"]];
    if (explainA.count >= 2) {
        NSDictionary *headerDic = explainA[0];
        _headerTitleL.text = minstr([headerDic valueForKey:@"title"]);
        _headerDesL.text = minstr([headerDic valueForKey:@"msg"]);
        
        NSDictionary *footerDic = explainA[1];
        _footerTitelL.text = minstr([footerDic valueForKey:@"title"]);
        _footerDesL.text = minstr([footerDic valueForKey:@"msg"]);
        NSArray *footerA = [footerDic valueForKey:@"list"];
        NSString *temstr = @"";
        for (NSString *ssss in footerA) {
            temstr = [temstr stringByAppendingFormat:@"%@\n",ssss];
        }
        _footerContentL.attributedText = [self asString:temstr];;
    }
    
    [self changeApplyBtnEnable:_canApply];
    
}

#pragma mark - UITableViewDelegate、UITableViewDataSource
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    
    return 50;
}
-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
   
    return _headerView;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return _footerView;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 80;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    NSArray *listA = [NSArray arrayWithArray:[_dataDic valueForKey:@"list"]];
    return listA.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSArray *listA = [NSArray arrayWithArray:[_dataDic valueForKey:@"list"]];
    NSDictionary *subDic = listA[indexPath.row];
    YBApplyConditionCell *cell = [YBApplyConditionCell cellWithTab:tableView index:indexPath];
    cell.dataDic = subDic;
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    NSArray *listA = [NSArray arrayWithArray:[_dataDic valueForKey:@"list"]];
    NSDictionary *subDic = listA[indexPath.row];
    //status  0未达标   1已达标
    int state = [minstr([subDic valueForKey:@"status"]) intValue];
    if ([minstr([subDic valueForKey:@"id"]) isEqual:@"3"] && state == 0) {
        //认证
        YBUserAuthVC *authVC = [[YBUserAuthVC alloc]init];
        [[XGGAppDelegate sharedAppDelegate]pushViewController:authVC animated:YES];
    }
    
}

#pragma mark - set/get

- (void)createHeaderView {
    if (!_headerView) {
        _headerView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 50)];
        _headerTitleL = [[UILabel alloc]init];
        _headerTitleL.font = SYS_Font(15);
        _headerTitleL.textColor = UIColor.whiteColor;
        [_headerView addSubview:_headerTitleL];
        [_headerTitleL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_headerView.mas_left).offset(15);
            make.height.equalTo(_headerView.mas_height).multipliedBy(0.5);
            make.top.equalTo(_headerView);
        }];
        _headerDesL = [[UILabel alloc]init];
        _headerDesL.font = SYS_Font(13);
        _headerDesL.textColor = RGB_COLOR(@"#969696", 1);
        [_headerView addSubview:_headerDesL];
        [_headerDesL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_headerView.mas_left).offset(15);
            make.height.equalTo(_headerView.mas_height).multipliedBy(0.5);
            make.top.equalTo(_headerTitleL.mas_bottom);
        }];
    }
}

- (void)createFooterView {
    if (!_footerView) {
        _footerView = [[UIView alloc]init];
        _footerTitelL = [[UILabel alloc]init];
        _footerTitelL.font = SYS_Font(15);
        _footerTitelL.textColor = UIColor.whiteColor;
        [_footerView addSubview:_footerTitelL];
        [_footerTitelL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_footerView.mas_left).offset(15);
            make.height.mas_equalTo(20);
            make.top.equalTo(_footerView.mas_top).offset(10);
        }];
        _footerDesL = [[UILabel alloc]init];
        _footerDesL.font = SYS_Font(13);
        _footerDesL.textColor = RGB_COLOR(@"#969696", 1);
        [_footerView addSubview:_footerDesL];
        [_footerDesL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.height.equalTo(_footerTitelL);
            make.top.equalTo(_footerTitelL.mas_bottom).offset(5);
            make.height.mas_equalTo(20);
        }];
        
        _footerContentL = [[UILabel alloc]init];
        _footerContentL.font = SYS_Font(13);
        _footerContentL.numberOfLines = 0;
        _footerContentL.text = @" ";
        _footerContentL.textColor = UIColor.whiteColor;
        [_footerView addSubview:_footerContentL];
        [_footerContentL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_footerTitelL);
            make.top.equalTo(_footerDesL.mas_bottom).offset(5);
            make.right.equalTo(_footerView.mas_right).offset(-15);
        }];
        
        _applyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _applyBtn.titleLabel.font = SYS_Font(15);
        [_applyBtn setTitle:YZMsg(@"立即申请") forState:0];
        _applyBtn.layer.cornerRadius = 2;
        _applyBtn.layer.masksToBounds = YES;
        [_footerView addSubview:_applyBtn];
        [_applyBtn addTarget:self action:@selector(clickAppleBtn) forControlEvents:UIControlEventTouchUpInside];
        [_applyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_footerContentL.mas_bottom).offset(90);
            make.centerX.equalTo(_footerView.mas_centerX);
            make.width.equalTo(_footerView.mas_width).offset(-30);
            make.height.mas_equalTo(40);
            make.bottom.equalTo(_footerView.mas_bottom).offset(-ShowDiff-50);
        }];
        
    }
}
-(void)changeApplyBtnEnable:(BOOL)enable {
    _applyBtn.enabled = enable;
    if (enable) {
        [_applyBtn setTitleColor:UIColor.whiteColor forState:0];
        _applyBtn.backgroundColor = Pink_Cor;
    }else{
        [_applyBtn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
        _applyBtn.backgroundColor = CellRow_Cor;
    }
}

-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height - 64-statusbarHeight)style:UITableViewStyleGrouped];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = Normal_Color;
        _tableView.bounces = NO;
        _tableView.estimatedSectionFooterHeight = 200;
    }
    return _tableView;
}
-(void)clickAppleBtn {
    YBApplyStoreVC *shopVC = [[YBApplyStoreVC alloc]init];
    [[XGGAppDelegate sharedAppDelegate]pushViewController:shopVC animated:YES];
}

-(NSMutableAttributedString *)asString:(NSString *)string{
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 3;
    style.alignment = NSTextAlignmentLeft;
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc] initWithString:string];
    [attStr addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, [string length])];
    return attStr;
    
}
@end
