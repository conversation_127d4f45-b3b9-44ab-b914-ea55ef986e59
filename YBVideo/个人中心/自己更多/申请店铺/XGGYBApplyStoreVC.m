//
//  YBApplyStoreVC.m
//  YBVideo
//
//  Created by YB007 on 2020/8/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBApplyStoreVC.h"
#import "YBStorageObj.h"

typedef NS_ENUM(NSInteger,ShopSelType) {
    ShopSelTypeDefault,
    ShopSelTypeCover,
    ShopSelTypeBusiness,
    ShopSelTypeLicence,
    ShopSelTypeOtherCer,
};

@interface YBApplyStoreVC ()<TZImagePickerControllerDelegate,UIImagePickerControllerDelegate,UINavigationControllerDelegate,UITextViewDelegate>
{
    NSString *_shopCoverPath;
    NSString *_businessPath;
    NSString *_licencePath;
    NSString *_otherCerPath;
    BOOL _selectedImg;
}
@property(nonatomic,strong)UIScrollView *bgScrollView;
@property(nonatomic,strong)UIButton *shopCoverBtn;
@property(nonatomic,strong)UIImage *shopCoverImg;
@property(nonatomic,strong)MyTextField *shopNameTF;
@property(nonatomic,strong)MyTextView *shopDesTV;
@property(nonatomic,strong)MyTextField *shopPhoneTF;
@property(nonatomic,strong)UIButton *shopBusinessBtn;   //营业执照
@property(nonatomic,strong)UIImage *shopBusinessImg;
@property(nonatomic,strong)UIButton *shopLicenceBtn;    //许可证
@property(nonatomic,strong)UIImage *shopLicenceImg;
@property(nonatomic,strong)UIButton *shopOtherBtn;      //其他
@property(nonatomic,strong)UIImage *shopOtherImg;
@property(nonatomic,strong)UIButton *shopApplyBtn;
@property(nonatomic,assign)ShopSelType selType;

@end

@implementation YBApplyStoreVC


- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if (_selType == ShopSelTypeDefault) {
        [self pullData];
    }
}
-(void)pullData {
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Shop.getShopAuth" Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [weakSelf updateData:[info firstObject]];
        }else{
            [MBProgressHUD showPop:msg];
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }
    } Fail:^(id fail) {
        
    }];
    
}

-(void)updateData:(NSDictionary *)infoDic {
    int state = [minstr([infoDic valueForKey:@"status"]) intValue];
    //-1未申请 0审核中  1审核通过  2审核失败
    switch (state) {
        case 0:{
            [self changeApplyBtnEnable:NO];
            [_shopApplyBtn setTitle:YZMsg(@"信息审核中") forState:0];
        }break;
        case 1:{
            [self changeApplyBtnEnable:YES];
            [_shopApplyBtn setTitle:YZMsg(@"审核已通过") forState:0];
        }break;
        case 2:{
            [self changeApplyBtnEnable:YES];
            [_shopApplyBtn setTitle:YZMsg(@"审核被拒,提交重审") forState:0];
        }break;
        default:{
            [self changeApplyBtnEnable:YES];
            [_shopApplyBtn setTitle:YZMsg(@"立即申请") forState:0];
        }break;
    }
    _shopCoverPath = minstr([infoDic valueForKey:@"thumb"]);
    if (![PublicObj checkNull:_shopCoverPath]) {
        [_shopCoverBtn sd_setImageWithURL:[NSURL URLWithString:_shopCoverPath] forState:0];
    }
    _shopNameTF.text = [PublicObj checkNull:minstr([infoDic valueForKey:@"name"])] ? @"":minstr([infoDic valueForKey:@"name"]);
    _shopDesTV.text = [PublicObj checkNull:minstr([infoDic valueForKey:@"des"])] ? @"":minstr([infoDic valueForKey:@"des"]);
    _shopPhoneTF.text = [PublicObj checkNull:minstr([infoDic valueForKey:@"tel"])] ? @"":minstr([infoDic valueForKey:@"tel"]);
    
    _businessPath = minstr([infoDic valueForKey:@"certificate"]);
    if (![PublicObj checkNull:_businessPath]) {
        [_shopBusinessBtn sd_setImageWithURL:[NSURL URLWithString:_businessPath] forState:0];
    }
    _licencePath = minstr([infoDic valueForKey:@"license"]);
    if (![PublicObj checkNull:_licencePath]) {
        [_shopLicenceBtn sd_setImageWithURL:[NSURL URLWithString:_licencePath] forState:0];
    }
    _otherCerPath = minstr([infoDic valueForKey:@"other"]);
    if (![PublicObj checkNull:_otherCerPath]) {
        [_shopOtherBtn sd_setImageWithURL:[NSURL URLWithString:_otherCerPath] forState:0];
    }
    
    
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"创建店铺");
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(textFiledEditChanged:) name:UITextFieldTextDidChangeNotification object:nil];

    
    [self.view addSubview:self.bgScrollView];
    
    [_bgScrollView layoutIfNeeded];
    CGFloat maxY = CGRectGetMaxY(_shopApplyBtn.frame)+50;
    _bgScrollView.contentSize = CGSizeMake(0, maxY);
    
}
- (void)dealloc {
    [[NSNotificationCenter defaultCenter]removeObserver:self];
}
- (UIScrollView *)bgScrollView{
    if (!_bgScrollView) {
        _bgScrollView = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight)];
        _bgScrollView.backgroundColor = Normal_Color;
        _bgScrollView.bounces = NO;
        //
        UIColor *titleCor = RGB_COLOR(@"#969696", 1);
        UILabel *shopCoverL = [[UILabel alloc]init];
        shopCoverL.text = YZMsg(@"*店铺图片(用于店铺主页展示)");
        shopCoverL.font = SYS_Font(15);
        shopCoverL.textColor = titleCor;
        NSMutableAttributedString *coverAtt = [[NSMutableAttributedString alloc]initWithString:shopCoverL.text];
        [coverAtt addAttribute:NSForegroundColorAttributeName value:Pink_Cor range:NSMakeRange(0, 1)];
        shopCoverL.attributedText = coverAtt;
        [_bgScrollView addSubview:shopCoverL];
        [shopCoverL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_bgScrollView.mas_left).offset(15);
            make.top.equalTo(_bgScrollView.mas_top).offset(10);
            make.right.lessThanOrEqualTo(_bgScrollView.mas_right).offset(-15);
        }];
        
        //
        _shopCoverBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_shopCoverBtn setImage:[UIImage imageNamed:@"认证-上传"] forState:0];
        _shopCoverBtn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
        _shopCoverBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
        _shopCoverBtn.imageView.contentMode = UIViewContentModeScaleAspectFill;
        _shopCoverBtn.layer.cornerRadius = 3;
        _shopCoverBtn.layer.masksToBounds = YES;
        [_shopCoverBtn addTarget:self action:@selector(clikcUploadEvent:) forControlEvents:UIControlEventTouchUpInside];
        [_bgScrollView addSubview:_shopCoverBtn];
        [_shopCoverBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(80);
            make.top.equalTo(shopCoverL.mas_bottom).offset(10);
            make.left.equalTo(shopCoverL);
        }];
        
        //
        UILabel *shopNameL = [[UILabel alloc]init];
        shopNameL.text = YZMsg(@"*店铺名称(最多可输入10个字)");
        shopNameL.font = shopCoverL.font;
        shopNameL.textColor = titleCor;
        NSMutableAttributedString *nameAtt = [[NSMutableAttributedString alloc]initWithString:shopNameL.text];
        [nameAtt addAttribute:NSForegroundColorAttributeName value:Pink_Cor range:NSMakeRange(0, 1)];
        shopNameL.attributedText = nameAtt;
        [_bgScrollView addSubview:shopNameL];
        [shopNameL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_shopCoverBtn.mas_bottom).offset(10);
            make.left.equalTo(shopCoverL);
            make.right.lessThanOrEqualTo(_bgScrollView.mas_right).offset(-15);
        }];
        
        UIView *nameBgView = [[UIView alloc]init];
        nameBgView.backgroundColor = CellRow_Cor;
        [_bgScrollView addSubview:nameBgView];
        [nameBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.equalTo(_bgScrollView);
            make.height.mas_equalTo(40);
            make.top.equalTo(shopNameL.mas_bottom).offset(10);
        }];
        _shopNameTF = [[MyTextField alloc]init];
        _shopNameTF.font = shopCoverL.font;
        _shopNameTF.textColor = UIColor.whiteColor;
        _shopNameTF.tintColor = UIColor.whiteColor;
        _shopNameTF.backgroundColor = UIColor.clearColor;
        [nameBgView addSubview:_shopNameTF];
        [_shopNameTF mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(nameBgView.mas_width).offset(-30);
            make.centerX.height.centerY.equalTo(nameBgView);
        }];
        
        //
        UILabel *shopDesL = [[UILabel alloc]init];
        shopDesL.text = YZMsg(@"*店铺简介(最多可输入50个字)");
        shopDesL.font = shopCoverL.font;
        shopDesL.textColor = titleCor;
        NSMutableAttributedString *desAtt = [[NSMutableAttributedString alloc]initWithString:shopDesL.text];
        [desAtt addAttribute:NSForegroundColorAttributeName value:Pink_Cor range:NSMakeRange(0, 1)];
        shopDesL.attributedText = desAtt;
        [_bgScrollView addSubview:shopDesL];
        [shopDesL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(nameBgView.mas_bottom).offset(10);
            make.left.equalTo(shopCoverL);
            make.right.lessThanOrEqualTo(_bgScrollView.mas_right).offset(-15);
        }];
        
        UIView *desBgView = [[UIView alloc]init];
        desBgView.backgroundColor = CellRow_Cor;
        [_bgScrollView addSubview:desBgView];
        [desBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.equalTo(_bgScrollView);
            make.height.mas_equalTo(100);
            make.top.equalTo(shopDesL.mas_bottom).offset(10);
        }];
        _shopDesTV = [[MyTextView alloc]init];
        _shopDesTV.delegate = self;
        _shopDesTV.font = shopNameL.font;
        _shopDesTV.textColor = _shopNameTF.textColor;
        _shopDesTV.tintColor = _shopNameTF.tintColor;
        _shopDesTV.backgroundColor = UIColor.clearColor;
        [desBgView addSubview:_shopDesTV];
        [_shopDesTV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(desBgView.mas_width).offset(-30);
            make.centerX.height.centerY.equalTo(desBgView);
        }];
        
        //
        UILabel *shopPhoneL = [[UILabel alloc]init];
        shopPhoneL.text = YZMsg(@"店铺联系方式(店铺联系电话)");
        shopPhoneL.font = shopCoverL.font;
        shopPhoneL.textColor = titleCor;
        [_bgScrollView addSubview:shopPhoneL];
        [shopPhoneL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(desBgView.mas_bottom).offset(10);
            make.left.equalTo(shopCoverL);
            make.right.lessThanOrEqualTo(_bgScrollView.mas_right).offset(-15);
        }];
        
        UIView *phoneBgView = [[UIView alloc]init];
        phoneBgView.backgroundColor = CellRow_Cor;
        [_bgScrollView addSubview:phoneBgView];
        [phoneBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.centerX.equalTo(_bgScrollView);
            make.height.mas_equalTo(40);
            make.top.equalTo(shopPhoneL.mas_bottom).offset(10);
        }];
        _shopPhoneTF = [[MyTextField alloc]init];
        _shopPhoneTF.font = shopCoverL.font;
        _shopPhoneTF.textColor = _shopNameTF.textColor;
        _shopPhoneTF.tintColor = _shopNameTF.tintColor;
        _shopPhoneTF.keyboardType = UIKeyboardTypeNumberPad;
        [phoneBgView addSubview:_shopPhoneTF];
        [_shopPhoneTF mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(phoneBgView.mas_width).offset(-30);
            make.centerX.height.centerY.equalTo(phoneBgView);
        }];
        
        MASViewAttribute *masTop = phoneBgView.mas_bottom;
        //证件
        NSArray *certificatesA = @[YZMsg(@"营业执照"),YZMsg(@"许可证"),YZMsg(@"其他证件")];
        CGFloat space = 20;
        CGFloat cerW = (_window_width - 20*4)/3;
        CGFloat cerH = cerW *75/100;
        MASViewAttribute *cerLeft = _bgScrollView.mas_left;
        MASViewAttribute *cerTop = phoneBgView.mas_bottom;
        for (int i =0; i<certificatesA.count; i++) {
            UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
            btn.layer.cornerRadius = 3;
            btn.layer.masksToBounds = YES;
            [btn setImage:[UIImage imageNamed:@"认证-上传"] forState:0];
            btn.imageView.contentMode = UIViewContentModeScaleAspectFill;
            btn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
            btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
            [btn addTarget:self action:@selector(clikcUploadEvent:) forControlEvents:UIControlEventTouchUpInside];
            [_bgScrollView addSubview:btn];
            switch (i) {
                case 0:{
                    _shopBusinessBtn = btn;
                }break;
                case 1:{
                    _shopLicenceBtn = btn;
                }break;
                case 2:{
                    _shopOtherBtn = btn;
                }break;
                default:
                    break;
            }
            [btn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(cerLeft).offset(space);
                make.top.equalTo(cerTop).offset(18);
                make.width.mas_equalTo(cerW);
                make.height.mas_equalTo(cerH);
            }];
            UILabel *desL = [[UILabel alloc]init];
            desL.font = SYS_Font(12);
            desL.textColor = RGB_COLOR(@"@646464", 1);
            desL.text = certificatesA[i];
            [_bgScrollView addSubview:desL];
            [desL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(btn);
                make.width.lessThanOrEqualTo(btn.mas_width).offset(20);
                make.top.equalTo(btn.mas_bottom).offset(10);
            }];
            cerLeft = btn.mas_right;
            masTop = desL.mas_bottom;
        }
        
        _shopApplyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _shopApplyBtn.titleLabel.font = SYS_Font(15);
        _shopApplyBtn.layer.cornerRadius = 2;
        _shopApplyBtn.layer.masksToBounds = YES;
        [_shopApplyBtn setTitle:YZMsg(@"立即申请") forState:0];
        [_shopApplyBtn addTarget:self action:@selector(clikcApplyBtn) forControlEvents:UIControlEventTouchUpInside];
        [_bgScrollView addSubview:_shopApplyBtn];
        [_shopApplyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_bgScrollView.mas_centerX);
            make.width.equalTo(_bgScrollView.mas_width).offset(-30);
            make.height.mas_equalTo(40);
            make.top.equalTo(masTop).offset(15);
        }];
        [self changeApplyBtnEnable:YES];
    }
    return _bgScrollView;
}
#pragma mark - textfield 监听字数变化
-(void)textFiledEditChanged:(NSNotification *)noti {
    MyTextField *textField = (MyTextField *)noti.object;
    NSString *toBeString = textField.text;
    NSString *lang = [[[UITextInputMode activeInputModes]firstObject] primaryLanguage]; // 键盘输入模式
    if ([lang isEqualToString:@"zh-Hans"]) { // 简体中文输入，包括简体拼音，健体五笔，简体手写
        UITextRange *selectedRange = [textField markedTextRange];//获取高亮部分
        UITextPosition *position = [textField positionFromPosition:selectedRange.start offset:0];
        //没有高亮选择的字，则对已输入的文字进行字数统计和限制
        if (!position) {
            if (textField == _shopNameTF) {
                if (toBeString.length > 10) {
                    textField.text = [toBeString substringToIndex:10];
                    [MBProgressHUD showPop:YZMsg(@"最多可输入10个字")];
                }
            }
        }else{
            //有高亮选择的字符串，则暂不对文字进行统计和限制
        }
    }else{
        // 中文输入法以外的直接对其统计限制即可，不考虑其他语种情况
        if (textField == _shopNameTF) {
            if (toBeString.length > 10) {
                textField.text = [toBeString substringToIndex:10];
                [MBProgressHUD showPop:YZMsg(@"最多可输入10个字")];
            }
        }
    }
}
- (void)textViewDidChange:(UITextView *)textView; {
    if (textView == _shopDesTV) {
        if (textView.text.length > 50) {
            textView.text = [textView.text substringToIndex:50];
            [MBProgressHUD showPop:YZMsg(@"最多可输入50个字")];
        }
    }
}
-(void)changeApplyBtnEnable:(BOOL)enable {
    
    _shopCoverBtn.userInteractionEnabled = enable;
    _shopNameTF.userInteractionEnabled = enable;
    _shopDesTV.userInteractionEnabled = enable;
    _shopPhoneTF.userInteractionEnabled = enable;
    _shopBusinessBtn.userInteractionEnabled = enable;
    _shopLicenceBtn.userInteractionEnabled = enable;
    _shopOtherBtn.userInteractionEnabled = enable;
    
    _shopApplyBtn.enabled = enable;
    if (enable) {
        [_shopApplyBtn setTitleColor:UIColor.whiteColor forState:0];
        _shopApplyBtn.backgroundColor = Pink_Cor;
    }else{
        [_shopApplyBtn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
        _shopApplyBtn.backgroundColor = CellRow_Cor;
    }
}
-(void)clikcUploadEvent:(UIButton *)sender {
    [self.view endEditing:YES];
    
    if (sender == _shopCoverBtn) {
        _selType = ShopSelTypeCover;
    }else if (sender == _shopBusinessBtn){
        _selType = ShopSelTypeBusiness;
    }else if (sender == _shopLicenceBtn){
        _selType = ShopSelTypeLicence;
    }else{
        _selType = ShopSelTypeOtherCer;
    }
    
    YBWeakSelf;
    RKActionSheet *sheet = [[RKActionSheet alloc]initWithTitle:@""];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相机") complete:^{
        [weakSelf takePhoto];
    }];
    [sheet addActionWithType:RKSheet_Default andTitle:YZMsg(@"相册") complete:^{
        [weakSelf selectPhotos];
    }];
    [sheet addActionWithType:RKSheet_Cancle andTitle:YZMsg(@"取消") complete:^{
    }];
    [sheet showSheet];
}

-(void)clikcApplyBtn {
    [self.view endEditing:YES];
    
    if (!_shopCoverImg && [PublicObj checkNull:_shopCoverPath]) {
        [MBProgressHUD showPop:YZMsg(@"请选择店铺图片")];
        return;
    }
     if (_shopNameTF.text.length <=0 ) {
         [MBProgressHUD showPop:YZMsg(@"请输入店铺名称")];
         return;
     }
     if (_shopDesTV.text.length <=0 ) {
         [MBProgressHUD showPop:YZMsg(@"请输入店铺简介")];
         return;
     }
    /* 非必传
     if (_shopPhoneTF.text.length <=0 ) {
         [MBProgressHUD showPop:YZMsg(@"请输入店铺联系方式")];
         return;
     }
    */
    // 非必传 (!_shopOtherImg && [PublicObj checkNull:_otherCerPath])
    
     if ((!_shopBusinessImg && [PublicObj checkNull:_businessPath]) ||
         (!_shopLicenceImg && [PublicObj checkNull:_licencePath]) ) {
         [MBProgressHUD showPop:YZMsg(@"请完善证件信息")];
         return;
     }
     
     if (_selectedImg) {
         //进行上传操作
         YBWeakSelf;
         [[YBStorageObj storageManeger]getCOSType:^(int code) {
             dispatch_async(dispatch_get_main_queue(), ^{
                 if (code == 0) {
                     [weakSelf startUploadCer];
                 }
             });
         }];
     }else{
         //未更改图片直接请求
         [MBProgressHUD showMessage:@""];
         [self goResServer];
     }
    
}
-(void)startUploadCer {
    [MBProgressHUD showMessage:@""];
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    YBWeakSelf;
    //店铺图片
    if (_shopCoverImg) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_shopCover.png"];
            [[YBStorageObj storageManeger]yb_storageImg:_shopCoverImg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                _shopCoverPath = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    
    //营业执照
    if (_shopBusinessImg) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_businessCer.png"];
            [[YBStorageObj storageManeger]yb_storageImg:_shopBusinessImg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                _businessPath = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    
    //许可证
    if (_shopLicenceImg) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_licenceCer.png"];
            [[YBStorageObj storageManeger]yb_storageImg:_shopLicenceImg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                _licencePath = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    //其他证件
    if (_shopOtherImg) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_otherCer.png"];
            [[YBStorageObj storageManeger]yb_storageImg:_shopOtherImg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                _otherCerPath = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf goResServer];
        });
        NSLog(@"任务完成执行");
    });
}
-(void)goResServer {
    
    NSDictionary *postDic = @{@"thumb":[PublicObj checkNull:_shopCoverPath]?@"":_shopCoverPath,
                              @"name":_shopNameTF.text,
                              @"des":_shopDesTV.text,
                              @"tel":_shopPhoneTF.text,
                              @"certificate":[PublicObj checkNull:_businessPath]?@"":_businessPath,
                              @"license":[PublicObj checkNull:_licencePath]?@"":_licencePath,
                              @"other":[PublicObj checkNull:_otherCerPath]?@"":_otherCerPath,
    };
    
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Shop.setShopAuth" Dic:postDic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showPop:msg];
        if (code == 0) {
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }
    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];
    }];
    
}

- (void)takePhoto{
    // 区分拍照-相册
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
    imagePickerController.allowsEditing = NO;
    imagePickerController.showsCameraControls = YES;
    imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
-(void)selectPhotos {
    TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.showSelectBtn = YES;
    imagePC.allowCrop = NO;
    imagePC.allowPickingOriginalPhoto = NO;
    imagePC.oKButtonTitleColorNormal = Pink_Cor;
    imagePC.allowTakePicture = YES;
    imagePC.allowTakeVideo = NO;
    imagePC.allowPickingVideo = NO;
    imagePC.allowPickingMultipleVideo = NO;
    imagePC.modalPresentationStyle = 0;
    [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    _selectedImg = YES;
    //先把图片转成NSData
    UIImage* image = photos[0];
    [self selImgChangeUIWithImg:image];
}
-(void)selImgChangeUIWithImg:(UIImage *)image {
    switch (_selType) {
        case ShopSelTypeCover:{
            _shopCoverImg = image;
            [_shopCoverBtn setImage:image forState:0];
        }break;
        case ShopSelTypeBusiness:{
            _shopBusinessImg = image;
            [_shopBusinessBtn setImage:image forState:0];
        }break;
        case ShopSelTypeLicence:{
            _shopLicenceImg = image;
            [_shopLicenceBtn setImage:image forState:0];
        }break;
        case ShopSelTypeOtherCer:{
            _shopOtherImg = image;
            [_shopOtherBtn setImage:image forState:0];
        }break;
        default:
            break;
    }
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"]){
        _selectedImg = YES;
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerOriginalImage"];
        [self selImgChangeUIWithImg:image];
        
    }
    [picker dismissViewControllerAnimated:YES completion:nil];
}
-(void)imagePickerControllerDidCancel:(UIImagePickerController *)picker{
    [picker dismissViewControllerAnimated:YES completion:nil];
}
- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if ([UIDevice currentDevice].systemVersion.floatValue < 11) {
        return;
    }
    if ([viewController isKindOfClass:NSClassFromString(@"PUPhotoPickerHostViewController")]) {
        [viewController.view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.frame.size.width < 42) {
                [viewController.view sendSubviewToBack:obj];
                *stop = YES;
            }
        }];
    }
}
@end
