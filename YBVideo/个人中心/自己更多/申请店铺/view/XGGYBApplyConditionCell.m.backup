//
//  YBApplyConditionCell.m
//  YBVideo
//
//  Created by YB007 on 2020/8/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBApplyConditionCell.h"

@implementation YBApplyConditionCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    
}

+(YBApplyConditionCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index {
    YBApplyConditionCell *cell = [table dequeueReusableCellWithIdentifier:@"YBApplyConditionCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"YBApplyConditionCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.backgroundColor = UIColor.clearColor;
    cell.contentView.backgroundColor = UIColor.clearColor;
    cell.bgView.backgroundColor = CellRow_Cor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    [_iconIV sd_setImageWithURL:[NSURL URLWithString:[NSString stringWithFormat:@"%@",[_dataDic valueForKey:@"thumb"]]]];
    _nameL.text = minstr([_dataDic valueForKey:@"title"]);
    _desL.text = minstr([_dataDic valueForKey:@"condition"]);
    //status  0未达标   1已达标
    int state = [minstr([_dataDic valueForKey:@"status"]) intValue];
    if (state == 1) {
        [_stateBtn setTitle:YZMsg(@"已达成") forState:0];
        [_stateBtn setTitleColor:RGB_COLOR(@"#41e099", 1) forState:0];
    }else{
        [_stateBtn setTitle:YZMsg(@"未达成") forState:0];
        [_stateBtn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
    }
}

@end
