//
//  YBApplyConditionCell.h
//  YBVideo
//
//  Created by YB007 on 2020/8/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface YBApplyConditionCell : UITableViewCell

@property (weak, nonatomic) IBOutlet UIView *bgView;
@property (weak, nonatomic) IBOutlet UIImageView *iconIV;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UILabel *desL;
@property (weak, nonatomic) IBOutlet UIButton *stateBtn;

@property(nonatomic,strong)NSDictionary *dataDic;

+(YBApplyConditionCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index;


@end

NS_ASSUME_NONNULL_END
