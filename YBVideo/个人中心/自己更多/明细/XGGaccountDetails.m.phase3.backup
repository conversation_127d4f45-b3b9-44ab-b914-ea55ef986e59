#import "accountDetails.h"
#import "Config.h"
#import <WebKit/WebKit.h>
@interface accountDetails ()<WKNavigationDelegate>
{
    WKWebView *wevView;
    NSURL *url;
    //四个下划线
    UILabel *label1;
    UILabel *label2;
    UILabel *label3;
    UILabel *label4;
    UIView *shadowView;
}
@end
@implementation accountDetails
-(void)navtion{
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64 + statusbarHeight)];
     navtion.backgroundColor = Normal_Color;
    
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:returnBtn];
    [self.view addSubview:navtion];
    
    UISegmentedControl *segment = [[UISegmentedControl alloc]initWithItems:@[YZMsg(@"收益明细"),YZMsg(@"提现明细")]];
    segment.frame = CGRectMake(_window_width*0.15,25 + statusbarHeight, _window_width*0.7,30);
    if (@available(iOS 13.0, *)) {
        segment.selectedSegmentTintColor = [UIColor clearColor];
    } else {
        segment.tintColor = [UIColor clearColor];
    }
    NSDictionary *attributes = [NSDictionary dictionaryWithObjectsAndKeys:[UIFont systemFontOfSize:17],NSFontAttributeName,RGB_COLOR(@"#777777", 1), NSForegroundColorAttributeName, nil];
    [segment setTitleTextAttributes:attributes forState:UIControlStateNormal];
    NSDictionary *highlightedAttributes = [NSDictionary dictionaryWithObjectsAndKeys:[UIFont systemFontOfSize:17],NSFontAttributeName,[UIColor whiteColor], NSForegroundColorAttributeName, nil];
    [segment setTitleTextAttributes:highlightedAttributes forState:UIControlStateSelected];
    segment.selectedSegmentIndex = 0;
    [segment addTarget:self action:@selector(change:) forControlEvents:UIControlEventValueChanged];

    [navtion addSubview:segment];
    
}
//segment事件
-(void)change:(UISegmentedControl *)segment{
    
    if (segment.selectedSegmentIndex == 0) {
        [wevView stopLoading];
        //商城
        NSString *paths;
        if([self.fromType isEqual:@"red"]){
            paths = [h5url stringByAppendingFormat:@"/appapi/cash/redrecord?uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
        }else{
            paths = [h5url stringByAppendingFormat:@"/appapi/cash/record?uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
        }
        
        paths = [paths stringByAppendingFormat:@"&lang=%@",[YBLanguageTools serviceLang]];
        url = [NSURL URLWithString:paths];
        NSURLRequest *request = [NSURLRequest requestWithURL:url];
        [wevView removeObserver:self forKeyPath:@"title"];
        [wevView removeFromSuperview];
        wevView = nil;
        wevView = [[WKWebView alloc] init];
        [wevView loadRequest:request];
        wevView.backgroundColor = Normal_Color;
        wevView.frame = CGRectMake(0,64 + statusbarHeight, _window_width, _window_height-64 - statusbarHeight);
        wevView.navigationDelegate = self;
        [self.view addSubview:wevView];
    }
    else if (segment.selectedSegmentIndex == 1){
        [wevView stopLoading];
        NSString *paths;
        if([self.fromType isEqual:@"red"]){
            paths = [h5url stringByAppendingFormat:@"/appapi/cash/redcash?uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
        }else{
            paths = [h5url stringByAppendingFormat:@"/appapi/cash/cash?uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
        }
        
        
        paths = [paths stringByAppendingFormat:@"&lang=%@",[YBLanguageTools serviceLang]];
        url = [NSURL URLWithString:paths];
        NSURLRequest *request = [NSURLRequest requestWithURL:url];
        [wevView removeObserver:self forKeyPath:@"title"];
        [wevView removeFromSuperview];
        wevView = nil;
        wevView = [[WKWebView alloc] init];
        [wevView loadRequest:request];
        wevView.backgroundColor = Normal_Color;
        wevView.frame = CGRectMake(0,64 + statusbarHeight, _window_width, _window_height-64 - statusbarHeight);
        wevView.navigationDelegate = self;
        [self.view addSubview:wevView];
    }
    [wevView addObserver:self forKeyPath:@"title" options:NSKeyValueObservingOptionNew context:NULL];
    shadowView.hidden = NO;
    [self.view bringSubviewToFront:shadowView];
  }
-(void)doReturn{
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.naviView.hidden = YES;
    [self navtion];
//    NSString *paths = [h5url stringByAppendingFormat:@"/appapi/cash/record?uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
    NSString *paths;
    if([self.fromType isEqual:@"red"]){
        paths = [h5url stringByAppendingFormat:@"/appapi/cash/redrecord?uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
    }else{
        paths = [h5url stringByAppendingFormat:@"/appapi/cash/record?uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
    }

    paths = [paths stringByAppendingFormat:@"&lang=%@",[YBLanguageTools serviceLang]];
    url = [NSURL URLWithString:paths];
    NSURLRequest *request = [NSURLRequest requestWithURL:url];
    wevView = [[WKWebView alloc] init];
    [wevView loadRequest:request];
    wevView.backgroundColor = Normal_Color;
    wevView.frame = CGRectMake(0,64 + statusbarHeight, _window_width, _window_height-64 - statusbarHeight);
    wevView.navigationDelegate = self;
    [self.view addSubview:wevView];
    self.view.backgroundColor = Normal_Color;
    
    shadowView = [[UIView alloc]initWithFrame:CGRectMake(0,64 + statusbarHeight, _window_width, _window_height-64 - statusbarHeight)];
    shadowView.backgroundColor = Normal_Color;
    [self.view addSubview:shadowView];
    
    [wevView addObserver:self forKeyPath:@"title" options:NSKeyValueObservingOptionNew context:NULL];

}
- (void)dealloc
{
    [wevView removeObserver:self forKeyPath:@"title"];
}
-(void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
  if ([keyPath isEqualToString:@"title"]){
        if (object == wevView){
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                shadowView.hidden = YES;
            });
        }else{
            [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
        }
    }else{
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
    
}
@end
