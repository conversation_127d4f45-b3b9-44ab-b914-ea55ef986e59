//
//  YBGoodsLikeCell.h
//  YBVideo
//
//  Created by YB007 on 2020/10/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface YBGoodsLikeCell : UITableViewCell

@property (weak, nonatomic) IBOutlet UIImageView *thumbIV;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UILabel *priceL;
@property (weak, nonatomic) IBOutlet UILabel *oldPriceL;
@property (weak, nonatomic) IBOutlet UILabel *oldPriceLineL;

@property (weak, nonatomic) IBOutlet UIImageView *flagIV;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *flagWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *thumbLeft;


@property(nonatomic,strong)NSDictionary *dataDic;
+(YBGoodsLikeCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index;

@end

NS_ASSUME_NONNULL_END
