//
//  YBGoodsLikeCell.m
//  YBVideo
//
//  Created by YB007 on 2020/10/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBGoodsLikeCell.h"

@implementation YBGoodsLikeCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+(YBGoodsLikeCell *)cellWithTab:(UITableView *)table index:(NSIndexPath *)index {
    YBGoodsLikeCell *cell = [table dequeueReusableCellWithIdentifier:@"YBGoodsLikeCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"YBGoodsLikeCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.backgroundColor = CellRow_Cor;
    cell.contentView.backgroundColor = CellRow_Cor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}


- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    [_thumbIV sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])]];
    _nameL.text = minstr([_dataDic valueForKey:@"name"]);
    _priceL.text = [NSString stringWithFormat:@"￥%@",[_dataDic valueForKey:@"price"]];
    _oldPriceL.hidden = _oldPriceLineL.hidden = YES;
    if (![PublicObj checkNull:minstr([_dataDic valueForKey:@"old_price"])]) {
        _oldPriceL.hidden = _oldPriceLineL.hidden = NO;
        _oldPriceL.text = [NSString stringWithFormat:@"￥%@",[_dataDic valueForKey:@"old_price"]];
    }
    
}

@end
