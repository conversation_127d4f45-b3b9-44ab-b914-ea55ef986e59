//
//  YBGetProVC.m
//  yunbaolive
//
//  Created by Boom on 2018/9/26.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "YBGetProVC.h"
#import "YBGetTypeListVC.h"
#import "PubH5.h"
#import "accountDetails.h"

@interface YBGetProVC ()<UITextFieldDelegate>{
    UILabel *allVotesL;
    UILabel *nowVotesL;
    MyTextField *votesT;
    UILabel *_getL;
    UILabel *moneyLabel;
    UILabel *typeLabel;
    int cash_rate;
    UIButton *inputBtn;
    UILabel *tipsLabel;
    NSDictionary *typeDic;
    UIImageView *seletTypeImgView;
    CGFloat _ratio;
    UILabel *_alertTipsL;
}

@end

@implementation YBGetProVC
-(void)navtion{
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64 + statusbarHeight)];
    navtion.backgroundColor = Normal_Color;
    UILabel *label = [[UILabel alloc]init];
    label.text = YZMsg(@"我的收益");
    [label setFont:NaviTitle_Font];
    label.textColor = [UIColor whiteColor];
    label.frame = CGRectMake(0, statusbarHeight,_window_width,84);
    label.textAlignment = NSTextAlignmentCenter;
    [navtion addSubview:label];
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    UIButton *bigBTN = [[UIButton alloc]initWithFrame:CGRectMake(0, statusbarHeight, _window_width/2, 64)];
    [bigBTN addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:bigBTN];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:returnBtn];
    UIButton *btnttttt = [UIButton buttonWithType:UIButtonTypeCustom];
    btnttttt.backgroundColor = [UIColor clearColor];
    [btnttttt addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    btnttttt.frame = CGRectMake(0,0,100,64);
    [navtion addSubview:btnttttt];
    
    
    UIButton *historyBtn = [UIButton buttonWithType:0];
    historyBtn.frame = CGRectMake(_window_width-75, 24+statusbarHeight, 65, 40);
    [historyBtn setTitle:YZMsg(@"明细") forState:0];
    [historyBtn setTitleColor:RGB_COLOR(@"#EA377F", 1) forState:0];
    historyBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [historyBtn addTarget:self action:@selector(addBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:historyBtn];

//    [PublicObj lineViewWithFrame:CGRectMake(0, navtion.height-1, _window_width, 1) andColor:RGB(244, 245, 246) andView:navtion];
    [self.view addSubview:navtion];
}
- (void)addBtnClick:(UIButton *)sender{
    accountDetails *web = [[accountDetails alloc]init];
    [self.navigationController pushViewController:web animated:YES];
}
-(void)doReturn{
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = Normal_Color;
    self.naviView.hidden = YES;
    
    [BGSetting getBgSettingUpdate:NO maintain:NO eventBack:nil];
    
    typeDic = [Config getCashAccount];
    _ratio = 1;
    
    [self navtion];
    [self creatUI];
    [self requestData];
}
- (void)requestData{

    [YBNetworking postWithUrl:@"Cash.getProfit" Dic:@{@"uid":[Config getOwnID],@"token":[Config getOwnToken]} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            //获取收益
            nowVotesL.text = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"total"]];
            allVotesL.text = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"votes"]];//收益 魅力值
            _ratio = [minstr([[info firstObject] valueForKey:@"cash_prop"]) floatValue];
            _alertTipsL.text = minstr([[info firstObject] valueForKey:@"tips"]);
            NSLog(@"收益数据........%@",info);
        }
    } Fail:^(id fail) {
        
    }];
}
- (void)tapClick{
    [votesT resignFirstResponder];
}
- (void)creatUI{
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tapClick)];
    [self.view addGestureRecognizer:tap];
    
    //黄色背景图
    UIImageView *backImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width*0.04, 64+statusbarHeight+10, _window_width*0.92, _window_width*0.92*24/69)];
    backImgView.image = [UIImage imageNamed:@"profitBg"];
    [self.view addSubview:backImgView];
    
    for (int i = 0; i < 4; i++) {
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(backImgView.width/2*(i%2), backImgView.height/4*(i/2+1), backImgView.width/2, backImgView.height/4)];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = [UIColor whiteColor];
        if (i<2) {
            label.font = [UIFont systemFontOfSize:15];
            if (i == 0) {
                label.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"我的"),[common name_votes]];
            }else{
                label.text = YZMsg(@"可提取金额");
                [PublicObj lineViewWithFrame:CGRectMake(backImgView.width/2-0.5, backImgView.height/4, 1, backImgView.height/2) andColor:[UIColor whiteColor] andView:backImgView];
            }
        }else{
            label.font = [UIFont boldSystemFontOfSize:22];
            label.text = @"0";
            if (i == 2) {
                allVotesL = label;
            }else{
                nowVotesL = label;
            }
        }
        [backImgView addSubview:label];
    }
    //输入提现金额的视图
    UIView *textView = [[UIView alloc]initWithFrame:CGRectMake(backImgView.left, backImgView.bottom+10, backImgView.width, 50)];
    textView.backgroundColor = RGB_COLOR(@"#15102C", 1);
    [self.view addSubview:textView];
    votesT = [[MyTextField alloc]initWithFrame:CGRectMake(15, 0, textView.width-30, 50)];
    votesT.textColor = Pink_Cor;
    votesT.font = [UIFont systemFontOfSize:15];
    votesT.placeholder = @"0";
    votesT.placeCol = RGB_COLOR(@"#646464", 1);
    votesT.tintColor = Pink_Cor;
    votesT.keyboardType = UIKeyboardTypeNumberPad;
    votesT.delegate = self;
    [textView addSubview:votesT];
    UIButton *leftL = [UIButton buttonWithType:UIButtonTypeCustom];
    leftL.frame = CGRectMake(0, 0, 150, 50);
    leftL.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [leftL setTitle:YZMsg(@"请输入要提取的金额") forState:0];
    leftL.titleLabel.font = SYS_Font(15);
    [leftL setTitleColor:RGB_COLOR(@"#646464", 1) forState:0];
    //leftL.contentEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 8);
    votesT.leftView = leftL;
    votesT.leftViewMode = UITextFieldViewModeAlways;
    [votesT addTarget:self action:@selector(votesTextFieldChange:) forControlEvents:UIControlEventEditingChanged];
    
    //可到账金额
    UIView *getView = [[UIView alloc]initWithFrame:CGRectMake(textView.left, textView.bottom+10, textView.width, 50)];
    getView.backgroundColor = textView.backgroundColor;
    [self.view addSubview:getView];
    UILabel *getDesL = [[UILabel alloc]init];
    getDesL.textColor = leftL.titleLabel.textColor;
    getDesL.font = leftL.titleLabel.font;
    getDesL.text = YZMsg(@"可到账金额");
    [getView addSubview:getDesL];
    [getDesL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(getView.mas_left).offset(15);
        make.centerY.equalTo(getView);
    }];
    _getL = [[UILabel alloc]init];
    _getL.textColor = Pink_Cor;
    _getL.font = getDesL.font;
    _getL.text = @"￥0";
    [getView addSubview:_getL];
    [_getL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(getDesL.mas_right).offset(15);
        make.centerY.equalTo(getDesL);
        make.right.lessThanOrEqualTo(getView.mas_right).offset(-10);
    }];
    
    //选择提现账户
    
    UIView *typeView = [[UIView alloc]initWithFrame:CGRectMake(backImgView.left, getView.bottom+10, backImgView.width, 50)];
    typeView.backgroundColor = RGB_COLOR(@"#15102C", 1);
    [self.view addSubview:typeView];
    typeLabel = [[UILabel alloc]initWithFrame:CGRectMake(15, 0, typeView.width - 15-40, 50)];
    typeLabel.textColor = RGB_COLOR(@"#646464", 1);
    typeLabel.font = [UIFont systemFontOfSize:15];
    typeLabel.text = YZMsg(@"请选择提现账户");
    [typeView addSubview:typeLabel];
    seletTypeImgView = [[UIImageView alloc]initWithFrame:CGRectMake(typeLabel.left, 15, 20, 20)];
    seletTypeImgView.hidden = YES;
    [typeView addSubview:seletTypeImgView];
    
    UIImageView *rightImgView = [[UIImageView alloc]initWithFrame:CGRectMake(typeView.width-30, (50-12*39/24)/2, 12, 12*39/24)];//  24/39
    rightImgView.image = [UIImage imageNamed:@"更多-箭头"];
    rightImgView.userInteractionEnabled = YES;
    [typeView addSubview:rightImgView];

    UIButton *btn = [UIButton buttonWithType:0];
    btn.frame = CGRectMake(0, 0, typeView.width, typeView.height);
    [btn addTarget:self action:@selector(selectPayType) forControlEvents:UIControlEventTouchUpInside];
    [typeView addSubview:btn];
    
    inputBtn = [UIButton buttonWithType:0];
    inputBtn.frame = CGRectMake(_window_width*0.15, typeView.bottom + 30, _window_width*0.7, 40);
    [inputBtn setBackgroundColor:RGB_COLOR(@"#27223B", 1)];
    [inputBtn setTitle:YZMsg(@"立即提现") forState:0];
    [inputBtn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
    [inputBtn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:UIControlStateSelected];
    [inputBtn addTarget:self action:@selector(inputBtnClick) forControlEvents:UIControlEventTouchUpInside];
    inputBtn.titleLabel.font = [UIFont boldSystemFontOfSize:14];
    inputBtn.layer.cornerRadius = 5;
    inputBtn.layer.masksToBounds = YES;
    inputBtn.userInteractionEnabled = NO;
    [self.view addSubview:inputBtn];
    
    _alertTipsL = [[UILabel alloc]init];
    _alertTipsL.textColor = RGB_COLOR(@"#ffffff", 0.28);
    _alertTipsL.font = SYS_Font(11);
    _alertTipsL.numberOfLines = 0;
    _alertTipsL.textAlignment = NSTextAlignmentLeft;
    [self.view addSubview:_alertTipsL];
    [_alertTipsL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(inputBtn);
        make.top.equalTo(inputBtn.mas_bottom).offset(15);
    }];
    
//    tipsLabel = [[UILabel alloc]initWithFrame:CGRectMake(inputBtn.left+15, inputBtn.bottom + 15, inputBtn.width-30, 100)];
//    tipsLabel.font = [UIFont systemFontOfSize:11];
//    tipsLabel.textColor = gray66;
//    tipsLabel.numberOfLines = 0;
//    [self.view addSubview:tipsLabel];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangeMoenyLabelValue) name:UITextFieldTextDidChangeNotification object:nil];

    if ([typeDic allKeys].count>0) {
        [self updateShowAccount:typeDic];
    }
    
}
-(void)votesTextFieldChange:(UITextField *)text {
    CGFloat realFloat = [votesT.text floatValue]*_ratio;
    if (realFloat <= 0) {
        _getL.text = @"￥0";
    }else{
        _getL.text = [NSString stringWithFormat:@"￥%.2f",realFloat];
    }
}
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    if (textField == votesT) {
        //新输入的
        if (string.length == 0) {
            return YES;
        }
        NSString *checkStr = [textField.text stringByReplacingCharactersInRange:range withString:string];
        //非零的正整数
        NSString *regex = @"^[1-9]\\d*$";
        return [self isValid:checkStr withRegex:regex];
    }
    return YES;
}
- (BOOL) isValid:(NSString*)checkStr withRegex:(NSString*)regex {
    NSPredicate *predicte = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [predicte evaluateWithObject:checkStr];
}
//选择z提现方式
- (void)selectPayType{
    YBGetTypeListVC *vc = [[YBGetTypeListVC alloc]init];
    if ([typeDic allKeys].count>0) {
        vc.selectID = minstr([typeDic valueForKey:@"id"]);
    }else{
        vc.selectID = YZMsg(@"未选择提现方式");
    }
    YBWeakSelf;
    vc.block = ^(NSDictionary * _Nonnull dic) {
        typeDic = dic;
        if ([dic allKeys].count>0) {
            [Config saveCashAccount:typeDic];
        }
        [weakSelf updateShowAccount:dic];

    };
    [self.navigationController pushViewController:vc animated:YES];
}

-(void)updateShowAccount:(NSDictionary *)dic {
    
    seletTypeImgView.hidden = NO;
    typeLabel.x = seletTypeImgView.right + 5;
    typeLabel.textColor = [UIColor whiteColor];
    int type = [minstr([dic valueForKey:@"type"]) intValue];
    switch (type) {
        case 1:
            seletTypeImgView.image = [UIImage imageNamed:@"profit_zff"];
            typeLabel.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
            break;
        case 2:
            seletTypeImgView.image = [UIImage imageNamed:@"profit_wx"];
            typeLabel.text = [NSString stringWithFormat:@"%@",minstr([dic valueForKey:@"account"])];

            break;
        case 3:
            seletTypeImgView.image = [UIImage imageNamed:@"profit_card"];
            typeLabel.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
            break;
            
        default:{
            seletTypeImgView.hidden = YES;
            typeLabel.x = seletTypeImgView.left;
            typeLabel.text = YZMsg(@"请选择提现账户");
            typeLabel.textColor = RGB_COLOR(@"#646464", 1);
        }break;
    }
    
}

//提交申请
- (void)inputBtnClick{
    if(!typeDic || [typeDic allKeys].count<=0){
        [MBProgressHUD showError:YZMsg(@"请选择提现账户")];
        return;
    }
    NSDictionary *dic = @{@"accountid":minstr([typeDic valueForKey:@"id"]),@"money":votesT.text,@"uid":[Config getOwnID],@"token":[Config getOwnToken]};
    
    [YBNetworking postWithUrl:@"Cash.setCash" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            votesT.text = @"";
            _getL.text = @"￥0";
            [MBProgressHUD showError:msg];
            [self requestData];
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];

}
- (void)ChangeMoenyLabelValue{
//    moneyLabel.text = [NSString stringWithFormat:@"¥%lld",[votesT.text longLongValue]/cash_rate];
    //[votesT.text integerValue]
    if (votesT.text.length > 0) {
        inputBtn.userInteractionEnabled = YES;
        [inputBtn setBackgroundColor:RGB_COLOR(@"#EA377F", 1)];
        inputBtn.selected = YES;
    }else{
        inputBtn.userInteractionEnabled = NO;
        inputBtn.selected = NO;
        [inputBtn setBackgroundColor:RGB_COLOR(@"#27223B", 1)];
    }
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
