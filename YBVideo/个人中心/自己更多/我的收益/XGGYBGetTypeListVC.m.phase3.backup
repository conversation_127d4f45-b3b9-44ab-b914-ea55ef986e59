//
//  YBGetTypeListVC.m
//  yunbaolive
//
//  Created by Boom on 2018/10/11.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "YBGetTypeListVC.h"
#import "YBGetTypeListCell.h"
#import "YBAddTypeView.h"
@interface YBGetTypeListVC ()<UITableViewDelegate,UITableViewDataSource,cellDelegate>{
    UITableView *typeTable;
    NSArray *typeArray;
    UILabel *nothingLabel;
    YBAddTypeView *addView;
}

@end

@implementation YBGetTypeListVC
-(void)navtion{
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64 + statusbarHeight)];
    navtion.backgroundColor = Normal_Color;
    UILabel *label = [[UILabel alloc]init];
    label.text = YZMsg(@"提现账户");
    [label setFont:NaviTitle_Font];
    label.textColor = [UIColor whiteColor];
    label.frame = CGRectMake(0, statusbarHeight+24,_window_width,40);
    label.textAlignment = NSTextAlignmentCenter;
    [navtion addSubview:label];
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    UIButton *bigBTN = [[UIButton alloc]initWithFrame:CGRectMake(0, statusbarHeight, _window_width/2, 64)];
    [bigBTN addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:bigBTN];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:returnBtn];
    UIButton *btnttttt = [UIButton buttonWithType:UIButtonTypeCustom];
    btnttttt.backgroundColor = [UIColor clearColor];
    [btnttttt addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    btnttttt.frame = CGRectMake(0,0,100,64);
    [navtion addSubview:btnttttt];
    
    
    UIButton *addBtn = [UIButton buttonWithType:0];
    addBtn.frame = CGRectMake(_window_width-45, 24+statusbarHeight, 45, 45);
    [addBtn setTitle:YZMsg(@"添加") forState:0];
    [addBtn setTitleColor:RGB_COLOR(@"#EA377F", 1) forState:0];
    addBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [addBtn addTarget:self action:@selector(addBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:addBtn];
//    [PublicObj lineViewWithFrame:CGRectMake(0, navtion.height-1, _window_width, 1) andColor:RGB(244, 245, 246) andView:navtion];
    [self.view addSubview:navtion];
}
-(void)doReturn{
    if (self.block && [_selectID isEqual:YZMsg(@"未选择提现方式")]) {
        self.block(@{});
    }
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)addBtnClick:(UIButton *)sender{
    if (!addView) {
        addView = [[YBAddTypeView alloc]init];
        [self.view addSubview:addView];
    }else{
        addView.hidden = NO;
    }
    __weak YBGetTypeListVC *weakSelf = self;
    addView.block = ^{
        [weakSelf requestData];
        [addView removeFromSuperview];
        addView = nil;
    };
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.naviView.hidden = YES;
    
    self.automaticallyAdjustsScrollViewInsets = NO;
    
    [self navtion];
    typeTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
    typeTable.delegate = self;
    typeTable.dataSource = self;
    typeTable.separatorStyle = 0;
    [self.view addSubview:typeTable];
    
    nothingLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, 150, _window_width, 20)];
    nothingLabel.text = YZMsg(@"您当前还没有设置提现账户");
    nothingLabel.textAlignment = NSTextAlignmentCenter;
    nothingLabel.font = [UIFont systemFontOfSize:14];
    nothingLabel.textColor = RGB_COLOR(@"#969696", 1);
    nothingLabel.hidden = YES;
    [self.view addSubview:nothingLabel];
    self.view.backgroundColor = Normal_Color;
    typeTable.backgroundColor = Normal_Color;
    [self requestData];
}
- (void)requestData{
    
    [YBNetworking postWithUrl:@"Cash.getAccountList" Dic:@{@"uid":[Config getOwnID],@"token":[Config getOwnToken]} Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            typeArray = [NSArray arrayWithArray:info];
            [typeTable reloadData];
            if (typeArray.count > 0) {
                nothingLabel.hidden = YES;
                typeTable.hidden = NO;
                [typeTable reloadData];
            }else{
                nothingLabel.hidden = NO;
                typeTable.hidden = YES;
            }
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        nothingLabel.hidden = NO;
        typeTable.hidden = YES;
    }];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return typeArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBGetTypeListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"profitTypeCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"YBGetTypeListCell" owner:nil options:nil] lastObject];
    }
    cell.delegate = self;
    cell.indexRow = indexPath.row;
    NSDictionary *dic = typeArray[indexPath.row];
    if ([minstr([dic valueForKey:@"id"])isEqual:_selectID]) {
        cell.stateImgView.image = [UIImage imageNamed:@"profit_sel"];
    }else{
        cell.stateImgView.image = [UIImage imageNamed:@"profit_nor"];
    }
    int type = [minstr([dic valueForKey:@"type"]) intValue];
    switch (type) {
        case 1:
            cell.typeImgView.image = [UIImage imageNamed:@"profit_zff"];
            cell.nameL.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
            break;
        case 2:
            cell.typeImgView.image = [UIImage imageNamed:@"profit_wx"];
            cell.nameL.text = [NSString stringWithFormat:@"%@",minstr([dic valueForKey:@"account"])];
            break;
        case 3:
            cell.typeImgView.image = [UIImage imageNamed:@"profit_card"];
            cell.nameL.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
            break;

        default:
            break;
    }
    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 60;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *dic = typeArray[indexPath.row];
    if (![minstr([dic valueForKey:@"id"])isEqual:_selectID]) {
        self.block(dic);
        [self.navigationController popViewControllerAnimated:YES];
    }
    
}
- (void)delateIndex:(NSInteger)index{
    
    
    NSDictionary *contentDic = @{@"title":YZMsg(@"提示"),
                                @"msg":YZMsg(@"是否删除该提现账户?"),
                                @"left":YZMsg(@"取消"),
                                 @"right":YZMsg(@"删除")};
    [YBAlertView showAlertView:contentDic complete:^(int eventType) {
        if (eventType == 1) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSDictionary *dic = typeArray[index];
                [YBNetworking postWithUrl:@"Cash.delAccount" Dic:@{@"id":minstr([dic valueForKey:@"id"]),@"uid":[Config getOwnID],@"token":[Config getOwnToken]} Suc:^(int code, id info, NSString *msg) {
                    if (code == 0) {
                        NSDictionary *historyDic = [Config getCashAccount];
                        if ([minstr([historyDic valueForKey:@"id"]) isEqual:minstr([dic valueForKey:@"id"])]) {
                            [Config removeCashAccount];
                        }
                        if ([_selectID isEqual:minstr([dic valueForKey:@"id"])]) {
                            _selectID = YZMsg(@"未选择提现方式");
                        }
                        [MBProgressHUD showError:msg];
                        [self requestData];
                    }else{
                        [MBProgressHUD showError:msg];
                    }
                } Fail:^(id fail) {
                    
                }];
            });
        }
    }];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
