//
//  commodityRecordsVC.m
//  YBVideo
//
//  Created by IOS1 on 2019/7/5.
//  Copyright © 2019 cat. All rights reserved.
//

#import "commodityRecordsVC.h"
#import "commodityRecordsCell.h"
#import "YBGetVideoObj.h"
#import "YBGoodsInfoVC.h"
@interface commodityRecordsVC ()<UITableViewDelegate,UITableViewDataSource>{
    UITableView *listTable;
    NSMutableArray *listArray;
    int page;
}


@end

@implementation commodityRecordsVC



- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = RGB_COLOR(@"#110D24", 1);
    
    self.titleL.text = YZMsg(@"商品记录");
    
    page = 1;
    listArray = [NSMutableArray array];
   
    [self creatUI];
    [self requestData];
    
}
- (void)creatUI{
    listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:0];
    listTable.delegate = self;
    listTable.dataSource = self;
    listTable.backgroundColor = RGB_COLOR(@"#110D24", 1);
    listTable.separatorStyle = 0;
    [self.view addSubview:listTable];
    
    listTable.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
        page = 1;
        [self requestData];
    }];
    listTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
        page ++;
        [self requestData];
    }];
    
}
- (void)requestData{
    NSString *url = [purl stringByAppendingFormat:@"?service=Shop.getGoodsList"];
    
    [YBNetworking postWithUrl:@"Shop.getGoodsList" Dic:@{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"p":@(page)} Suc:^(int code, id info, NSString *msg) {
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];
        if (code == 0 ) {
            NSArray *list = [NSArray arrayWithArray:info];
            if (page == 1) {
                [listArray removeAllObjects];
            }
            [listArray addObjectsFromArray:list];
        }
        if (listArray.count<=0) {
            [PublicView showTextNoData:listTable text1:@"" text2:YZMsg(@"暂无数据") centerY:0.8];
        }else{
            [PublicView hiddenTextNoData:listTable];
        }
        [listTable reloadData];
    } Fail:^(id fail) {
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];
    }];

}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return listArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    commodityRecordsCell *cell = [tableView dequeueReusableCellWithIdentifier:@"commodityRecordsCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"commodityRecordsCell" owner:nil options:nil] lastObject];
    }
    NSDictionary *subDic = listArray[indexPath.row];
    [cell.thumbImgV sd_setImageWithURL:[NSURL URLWithString:minstr([subDic valueForKey:@"thumb"])]];
    cell.nameL.text = minstr([subDic valueForKey:@"name"]);
    cell.contentL.text = minstr([subDic valueForKey:@"des"]);
    cell.priceL.text = minstr([subDic valueForKey:@"price"]);
    cell.priceL2.text = minstr([subDic valueForKey:@"old_price"]);
    cell.numL.text = [NSString stringWithFormat:@"%@：%@%@",YZMsg(@"查看次数"),minstr([subDic valueForKey:@"hits"]),YZMsg(@"次")];
    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 150;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *subDic = listArray[indexPath.row];
    //[self coverClickVideoid:minstr([subDic valueForKey:@"videoid"])];
    [self goGoodsInfo:minstr([subDic valueForKey:@"id"])];
}
-(void)goGoodsInfo:(NSString *)goodsid {
    YBGoodsInfoVC *infoVC = [[YBGoodsInfoVC alloc]init];
    infoVC.infoType = InfoEnterType_Record;
    infoVC.touserID = [Config getOwnID];
    infoVC.goodsID = goodsid;
    [[XGGAppDelegate sharedAppDelegate]pushViewController: infoVC animated:YES];
}
- (void)coverClickVideoid:(NSString *)videoid {
    NSLog(@"播放视频");
    
    [YBGetVideoObj lookManeger].fromWhere = @"commodityRecordsVC";
    [YBGetVideoObj lookManeger].videoID = videoid;
    [YBGetVideoObj lookManeger].playIndex = 0;
    [YBGetVideoObj lookManeger].videoList = @[].mutableCopy;
    [YBGetVideoObj lookManeger].paging = 1;
    [YBGetVideoObj lookManeger].baseUrl = @"";
    [[YBGetVideoObj lookManeger]goLookVC];
    
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
