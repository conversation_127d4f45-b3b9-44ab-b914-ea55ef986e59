//
//  depositAccountVC.m
//  YBVideo
//
//  Created by IOS1 on 2019/7/5.
//  Copyright © 2019 cat. All rights reserved.
//

#import "depositAccountVC.h"
#import "orderVideoCell.h"
#import "YBGetVideoObj.h"
@interface depositAccountVC ()<UITableViewDelegate,UITableViewDataSource>{
    UILabel *coinL;
    UITableView *listTable;
    NSMutableArray *listArray;
    int page;
}

@end

@implementation depositAccountVC


- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = RGB_COLOR(@"#110D24", 1);
    
    self.titleL.text = YZMsg(@"投放账户");
    
    page = 1;
    listArray = [NSMutableArray array];

  
    [self creatUI];
}
- (void)creatUI{
    listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:1];
    listTable.delegate = self;
    listTable.dataSource = self;
    listTable.backgroundColor = RGB_COLOR(@"#110D24", 1);
    listTable.separatorStyle = 0;
    [self.view addSubview:listTable];
    
    listTable.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
        page = 1;
        [self requestData];
    }];
    listTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
        page ++;
        [self requestData];
    }];

    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, 145)];
    view.backgroundColor = RGB_COLOR(@"#110D24", 1);
    listTable.tableHeaderView = view;
    
    UILabel *lelfL = [[UILabel alloc]init];
    lelfL.textColor = RGB_COLOR(@"#969696", 1);
    lelfL.text = YZMsg(@"账户余额");
    lelfL.font = [UIFont systemFontOfSize:14];
    [view addSubview:lelfL];
    [lelfL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(view).offset(15);
        make.top.equalTo(view);
        make.height.mas_equalTo(50);
    }];
    
    coinL = [[UILabel alloc]init];
    coinL.textColor = RGB_COLOR(@"#FB483A", 1);
    coinL.text = @"¥200";
    coinL.font = [UIFont systemFontOfSize:14];
    [view addSubview:coinL];
    [coinL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(view).offset(-15);
        make.top.equalTo(view);
        make.height.mas_equalTo(50);
    }];

    UILabel *contentL = [[UILabel alloc]init];
    contentL.textColor = RGB_COLOR(@"#979797", 1);
    contentL.text = YZMsg(@"*账户余额可用于下次视频投放，也可联系客服申请提现。");
    contentL.font = [UIFont systemFontOfSize:10];
    [view addSubview:contentL];
    [contentL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(view).offset(15);
        make.top.equalTo(lelfL.mas_bottom).offset(8);
    }];

    UILabel *orderL = [[UILabel alloc]init];
    orderL.textColor = RGB_COLOR(@"#ffffff", 1);
    orderL.text = YZMsg(@"我的订单");
    orderL.font = [UIFont systemFontOfSize:15];
    [view addSubview:orderL];
    [orderL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(view).offset(15);
        make.bottom.equalTo(view);
        make.height.mas_equalTo(40);
    }];

    [self requestData];


}
- (void)requestData{
    
    [YBNetworking postWithUrl:@"Popular.GetPutin" Dic:@{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"p":@(page)} Suc:^(int code, id info, NSString *msg) {
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSArray *list = [infoDic valueForKey:@"list"];
            if (page == 1) {
                [listArray removeAllObjects];
                coinL.text = [NSString stringWithFormat:@"¥%@",minstr([infoDic valueForKey:@"coin"])];
            }
            [listArray addObjectsFromArray:list];
        }
        [listTable reloadData];
    } Fail:^(id fail) {
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];
    }];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return 1;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return listArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    orderVideoCell *cell = [tableView dequeueReusableCellWithIdentifier:@"orderVideoCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"orderVideoCell" owner:nil options:nil] lastObject];
    }
    NSDictionary *subDic = listArray[indexPath.row];
    NearbyVideoModel *model = [[NearbyVideoModel alloc]initWithDic:subDic];
    [cell.thumbImgV sd_setImageWithURL:[NSURL URLWithString:model.videoImage]];
    cell.nameL.text = model.userName;
    cell.titleL.text = model.videoTitle;
    cell.timeL.text = [NSString stringWithFormat:@"%@：%@  %@：%@",YZMsg(@"下单时间"),minstr([subDic valueForKey:@"paytime"]),YZMsg(@"下单金额"),minstr([subDic valueForKey:@"money"])];

    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 100;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *subDic = listArray[indexPath.row];
    [self coverClickVideoid:minstr([subDic valueForKey:@"id"])];
}

- (void)coverClickVideoid:(NSString *)videoid {
    NSLog(@"播放视频");

    [YBGetVideoObj lookManeger].fromWhere = @"depositAccountVC";
    [YBGetVideoObj lookManeger].videoID = videoid;
    [YBGetVideoObj lookManeger].playIndex = 0;
    [YBGetVideoObj lookManeger].videoList = @[].mutableCopy;
    [YBGetVideoObj lookManeger].paging = 1;
    [YBGetVideoObj lookManeger].baseUrl = @"";
    [[YBGetVideoObj lookManeger]goLookVC];
}
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 5;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *vv = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 5)];
    vv.backgroundColor = RGB_COLOR(@"#110D24", 1);
    return vv;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
