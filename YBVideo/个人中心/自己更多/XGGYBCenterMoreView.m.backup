//
//  YBCenterMoreView.m
//  YBVideo
//
//  Created by YB007 on 2022/6/1.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBCenterMoreView.h"

#import "YBCenterMoreCell.h"
#import "depositAccountVC.h"
#import "YBGetProVC.h"
#import "SetViewControllor.h"
#import "watchingRecordsVC.h"
#import "commodityRecordsVC.h"
#import "PubH5.h"
#import "YBRechargeVC.h"
#import "RoomManagementVC.h"
#import "YBApplyConditionVC.h"
#import "YBApplyStoreVC.h"
#import "YBDayTaskVC.h"
#import "YBGoodsLikeVC.h"
#import "ShopHomeVC.h"
#import "HotVideoDetailVC.h"
#import "LookHistoryVC.h"
#import "AdvertManagerVC.h"
#import "YBRedProfitVC.h"
@interface YBCenterMoreView()<UIGestureRecognizerDelegate,UITableViewDelegate,UITableViewDataSource>{
    UITableView *listTable;
    NSMutableArray *listArray;
    NSString *build;
}

@property(nonatomic,strong)UIView *bgView;

@property (nonatomic, strong)NSString *isSeller;
@end

@implementation YBCenterMoreView

+(instancetype)showMoreViewWithSeller:(NSString *)isSeller{
    YBCenterMoreView *view = [[YBCenterMoreView alloc]init];
    view.frame = CGRectMake(0, 0, _window_width, _window_height);
    view.isSeller = isSeller;
    [[UIApplication sharedApplication].delegate.window addSubview:view];
    [view createUI];
    return view;
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch; {
    if ([touch.view isDescendantOfView:self.bgView]) {
        return NO;
    }
    return YES;
}
-(void)dissmissView {
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}
-(void)createUI {
   
     UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(dissmissView)];
     tap.delegate = self;
     [self addGestureRecognizer:tap];
    
    self.backgroundColor = RGB_COLOR(@"#000000", 0.3);
    _bgView = [[UIView alloc]init];
    _bgView.backgroundColor = CellRow_Cor;
    _bgView.layer.cornerRadius = 10;
    _bgView.layer.masksToBounds = YES;
    [self addSubview:_bgView];
    [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mas_width).multipliedBy(0.56);
        make.right.equalTo(self.mas_right);
        make.height.centerY.equalTo(self);
    }];
    
    listTable = [[UITableView alloc]initWithFrame:CGRectZero style:0];
    listTable.delegate = self;
    listTable.dataSource = self;
    listTable.backgroundColor = CellRow_Cor;
    listTable.separatorStyle = 0;
    listTable.bounces = NO;
    listTable.showsVerticalScrollIndicator = NO;
    listTable.contentInset = UIEdgeInsetsMake(statusbarHeight+10, 0, ShowDiff+10, 0);
    [_bgView addSubview:listTable];
    [listTable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(_bgView);
        make.width.equalTo(_bgView.mas_width).offset(-2);
        make.top.equalTo(_bgView.mas_top).offset(0);
        make.bottom.equalTo(_bgView.mas_bottom).offset(0);
    }];
  
//    listArray = @[@{@"id":@"0",@"name":YZMsg(@"我的钱包"),@"img":@"我的钱包"},
//                  @{@"id":@"10",@"name":YZMsg(@"我的收藏"),@"img":@"我的收藏"},
//                  @{@"id":@"1",@"name":YZMsg(@"我的收益"),@"img":@"我的收益"},
//                  @{@"id":@"2",@"name":YZMsg(@"我的小店"),@"img":@"店铺申请"},
//                  @{@"id":@"11",@"name":YZMsg(@"上热门明细"),@"img":@"上热门明细"},
//                  @{@"id":@"8",@"name":YZMsg(@"房间管理"),@"img":@"房间管理"},
//                  @{@"id":@"15",@"name":YZMsg(@"红包收益"),@"img":@"红包收益"},
//                  @{@"id":@"14",@"name":YZMsg(@"直播动态"),@"img":@"直播动态"},
//                  @{@"id":@"3",@"name":YZMsg(@"直播权限说明"),@"img":@"权限说明"},
//                  @{@"id":@"4",@"name":YZMsg(@"邀请好友赚钱"),@"img":@"邀请好友赚钱"},
//                  @{@"id":@"5",@"name":YZMsg(@"观看记录"),@"img":@"观看记录"},
////                  @{@"id":@"6",@"name":YZMsg(@"商品记录"),@"img":@"商品记录"},
//                  @{@"id":@"12",@"name":YZMsg(@"广告管理"),@"img":@"广告管理"},
//                  @{@"id":@"9",@"name":YZMsg(@"每日任务"),@"img":@"每日任务"},
//                  @{@"id":@"13",@"name":YZMsg(@"在线客服"),@"img":@"我的客服"},
//                  @{@"id":@"7",@"name":YZMsg(@"个性设置"),@"img":@"个性设置"},
//                 ].mutableCopy;
    
    listArray = @[@{@"id":@"0",@"name":YZMsg(@"我的钱包"),@"img":@"我的钱包"},
                  @{@"id":@"10",@"name":YZMsg(@"我的收藏"),@"img":@"我的收藏"},
                  @{@"id":@"1",@"name":YZMsg(@"我的收益"),@"img":@"我的收益"},
                  @{@"id":@"11",@"name":YZMsg(@"上热门明细"),@"img":@"上热门明细"},
                  @{@"id":@"8",@"name":YZMsg(@"房间管理"),@"img":@"房间管理"},
                  @{@"id":@"15",@"name":YZMsg(@"红包收益"),@"img":@"红包收益"},
                  @{@"id":@"14",@"name":YZMsg(@"直播动态"),@"img":@"直播动态"},
                  @{@"id":@"3",@"name":YZMsg(@"直播权限说明"),@"img":@"权限说明"},
                  @{@"id":@"4",@"name":YZMsg(@"邀请好友赚钱"),@"img":@"邀请好友赚钱"},
                  @{@"id":@"5",@"name":YZMsg(@"观看记录"),@"img":@"观看记录"},
//                  @{@"id":@"6",@"name":YZMsg(@"商品记录"),@"img":@"商品记录"},
                  @{@"id":@"12",@"name":YZMsg(@"广告管理"),@"img":@"广告管理"},
                  @{@"id":@"9",@"name":YZMsg(@"每日任务"),@"img":@"每日任务"},
                  @{@"id":@"13",@"name":YZMsg(@"在线客服"),@"img":@"我的客服"},
                  @{@"id":@"7",@"name":YZMsg(@"个性设置"),@"img":@"个性设置"},
                 ].mutableCopy;
    
    if ([PublicObj isUp]) {
        [listArray removeObject:@{@"id":@"1",@"name":YZMsg(@"我的收益"),@"img":@"我的收益"}];
        [listArray removeObject: @{@"id":@"11",@"name":YZMsg(@"上热门明细"),@"img":@"上热门明细"}];
        [listArray removeObject:@{@"id":@"4",@"name":YZMsg(@"邀请好友赚钱"),@"img":@"邀请好友赚钱"}];
//        [listArray removeObject:@{@"id":@"6",@"name":YZMsg(@"商品记录"),@"img":@"商品记录"}];
        [listArray removeObject:@{@"id":@"12",@"name":YZMsg(@"广告管理"),@"img":@"广告管理"}];
        [listArray removeObject:@{@"id":@"9",@"name":YZMsg(@"每日任务"),@"img":@"每日任务"}];
    }
    if ([minstr([YBInviteCode getAgentSwitch]) isEqual:@"0"]) {
        [listArray removeObject:@{@"id":@"4",@"name":YZMsg(@"邀请好友赚钱"),@"img":@"邀请好友赚钱"}];
    }
    if([[common getVideoAwardSwitch] isEqual:@"0"]){
        [listArray removeObject:@{@"id":@"15",@"name":YZMsg(@"红包收益"),@"img":@"红包收益"}];
    }
    /// 青少年
    int young_switch = [YBYoungManager shareInstance].youngSwitch;
    if (young_switch == 1) {
        listArray = @[@{@"id":@"5",@"name":YZMsg(@"观看记录"),@"img":@"观看记录"},
                      @{@"id":@"13",@"name":YZMsg(@"在线客服"),@"img":@"我的客服"},
                      @{@"id":@"7",@"name":YZMsg(@"个性设置"),@"img":@"个性设置"},
        ].mutableCopy;
    }
    
    /// 客服
    if ([common getServiceSwitch] != 1) {
        [listArray removeObject:@{@"id":@"13",@"name":YZMsg(@"在线客服"),@"img":@"我的客服"}];
    }
    
    [listTable reloadData];
    
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return listArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBCenterMoreCell *cell = [tableView dequeueReusableCellWithIdentifier:@"centerMoreCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"YBCenterMoreCell" owner:nil options:nil] lastObject];
        cell.backgroundColor = CellRow_Cor;
        cell.contentView.backgroundColor = CellRow_Cor;
    }
    NSDictionary *subDic = listArray[indexPath.row];
    cell.nameL.text = minstr([subDic valueForKey:@"name"]);
    cell.thumbImgV.image = [UIImage imageNamed:minstr([subDic valueForKey:@"img"])];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    [self dissmissView];
    
    NSDictionary *subDic = listArray[indexPath.row];
    int idInt = [minstr([subDic valueForKey:@"id"]) intValue];
    switch (idInt) {
        case 0:{
            //钱包
            YBRechargeVC *chargeVC = [[YBRechargeVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:chargeVC animated:YES];
        }break;
        case 1:{
            //我的收益
            [self doProfit];
        }break;
        case 2:{
            ShopHomeVC *shop = [[ShopHomeVC alloc]init];
            shop.shop_name = minstr([subDic valueForKey:@"name"]);
            shop.shop_switch = _isSeller;
            [[XGGAppDelegate sharedAppDelegate] pushViewController:shop animated:YES];
        }break;
        case 3:{
            //直播权限说明
            [self doWebView:[NSString stringWithFormat:@"%@/appapi/liveauthority/index?uid=%@&token=%@",h5url,[Config getOwnID],[Config getOwnToken]]];
        }break;
        case 4:{
            //邀请好友赚钱
            [self doWebView:[NSString stringWithFormat:@"%@/appapi/agent/index?uid=%@&token=%@",h5url,[Config getOwnID],[Config getOwnToken]]];
        }break;
        case 5:{
            //观看记录
            [self doWatchingRecords];
        }break;
        case 6:{
            //商品记录
            [self doCommodityRecordsVC];
        }break;
        case 7:
            [self doSet];
            break;
        case 8:{
            RoomManagementVC *roomVC = [[RoomManagementVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:roomVC animated:YES];
        }break;
        case 9:{
            YBDayTaskVC *roomVC = [[YBDayTaskVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:roomVC animated:YES];
        }break;
        case 10:{
            YBGoodsLikeVC *liveVC = [[YBGoodsLikeVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:liveVC animated:YES];
        }
            break;
        case 11:{
            HotVideoDetailVC *detailVc = [[HotVideoDetailVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:detailVc animated:YES];

        }break;
        case 12:{
            AdvertManagerVC *adVC = [[AdvertManagerVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:adVC animated:YES];
        }break;
        case 13:{
            NSString *url = minstr([common getServiceUrl]);
            if ([PublicObj checkNull:url]) {
                return;
            }
            PubH5*serviceVC = [[PubH5 alloc]init];
            serviceVC.url = url;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:serviceVC animated:YES];
        }break;
        case 14:{
            /// 直播动态
            PubH5 *h5vc = [[PubH5 alloc]init];
            h5vc.url =[h5url stringByAppendingString:[NSString stringWithFormat:@"/appapi/lrecording/record?uid=%@&token=%@&touid=%@",[Config getOwnID],[Config getOwnToken],[Config getOwnID]]];
            [[XGGAppDelegate sharedAppDelegate] pushViewController:h5vc animated:YES];
        }break;
        case 15:{
            // 红包收益
            [self doRedProfit];
        }break;

        default:
            break;
    }
}
- (void)doDepositAccount{
    depositAccountVC *vc = [[depositAccountVC alloc]init];
    vc.hidesBottomBarWhenPushed = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];
}
-(void)doRedProfit{
    YBRedProfitVC *vc = [[YBRedProfitVC alloc]init];
    vc.hidesBottomBarWhenPushed = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

}
- (void)doProfit{
    YBGetProVC *vc = [[YBGetProVC alloc]init];
    vc.hidesBottomBarWhenPushed = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

}
- (void)doWatchingRecords{
    watchingRecordsVC *vc = [[watchingRecordsVC alloc]init];
    vc.hidesBottomBarWhenPushed = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

}
- (void)doCommodityRecordsVC{
//    commodityRecordsVC *vc = [[commodityRecordsVC alloc]init];
//    vc.hidesBottomBarWhenPushed = YES;
//    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];
    
//    LookHistoryVC *history = [[LookHistoryVC alloc]init];
//    [[XGGAppDelegate sharedAppDelegate]pushViewController:history animated:YES];

}
- (void)doSet{
    SetViewControllor *vc = [[SetViewControllor alloc]init];
    vc.hidesBottomBarWhenPushed = YES;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];
}
- (void)doWebView:(NSString *)url{
    PubH5 *vc = [[PubH5 alloc]init];
    vc.hidesBottomBarWhenPushed = YES;
    vc.url = url;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

    
}
@end
