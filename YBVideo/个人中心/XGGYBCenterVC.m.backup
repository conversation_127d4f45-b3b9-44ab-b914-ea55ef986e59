//
//  YBCenterVC.m
//  YBVideo
//
//  Created by YB007 on 2022/5/30.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBCenterVC.h"

#import "YBCenterTopView.h"
#import <TYTabPagerBar.h>
#import <TYPagerController.h>
#import "MessageListModel.h"
#import "CenterListVC.h"

@interface YBCenterVC ()<UIScrollViewDelegate,TYTabPagerBarDelegate,TYTabPagerBarDataSource,TYPagerControllerDelegate,TYPagerControllerDataSource,ListScrollDelegate>
{
    CGFloat scrContentH;
    CGFloat segStartY;
}
@property(nonatomic,strong)YBCenterTopView *topView;

@property(nonatomic,strong)TYTabPagerBar *tabBar;
@property(nonatomic,strong)TYPagerController *pagerController;
@property(nonatomic,strong)NSArray *dataArray;

@property(nonatomic,assign)BOOL followed;
@property(nonatomic,strong)UIScrollView *scrollview;

@property(nonatomic,strong)CenterListVC *listVC1;
@property(nonatomic,strong)CenterListVC *listVC2;
@property(nonatomic,strong)CenterListVC *listVC3;

@end

@implementation YBCenterVC

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self pullData];
}
- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [_topView removeLivingAnimation];
}
- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    _otherUid ? _otherUid:(_otherUid=[Config getOwnID]);
    
    scrContentH = 0;
    _followed = NO;
    
    [self.view addSubview:self.scrollview];
    self.view.backgroundColor = Normal_Color;
    if (@available(iOS 11.0, *)){
        _scrollview.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    [self.rightBtn setImage:[UIImage imageNamed:@"center_more"] forState:0];
    if (![_otherUid isEqual:[Config getOwnID]]) {
        //他人
        self.leftBtn.hidden = NO;
        self.rightBtn.hidden = NO;
    }else{
        //自己
        self.leftBtn.hidden = YES;
        self.rightBtn.hidden = YES;
    }
    //从其他页面push过来
    if (_isPush) {
        self.leftBtn.hidden = NO;
        }
    // 0 - 系统管理员
    if ([_otherUid isEqual:@"0"]) {
        self.rightBtn.hidden = YES;
    }
    //动态改变
    self.subNavi.alpha = 0;
    self.rightBtn.hidden = YES;
    
    [_tabBar reloadData];
    [_pagerController reloadData];
    
}

-(void)pullData {
    NSString *url = [NSString stringWithFormat:@"User.getUserHome&uid=%@&touid=%@",[Config getOwnID],_otherUid];
    [YBNetworking postWithUrl:url Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            if ([_otherUid isEqual:[Config getOwnID]]) {
                //自己个人中心
                [Config saveOwnNicename:minstr([infoDic valueForKey:@"user_nickname"])];
                [Config saveUserSex:minstr([infoDic valueForKey:@"sex"])];
                [Config saveUserAvatar:minstr([infoDic valueForKey:@"avatar"])];
                [Config saveUserCity:minstr([infoDic valueForKey:@"city"])];
                [Config saveUserHomeTown:minstr([infoDic valueForKey:@"hometown"])];
            }
            self.titleL.text = minstr([infoDic valueForKey:@"user_nickname"]);
            _topView.dataDic = infoDic;
            [self updateTop:infoDic];
        }else if (code == 700){
            [PublicObj tokenExpired:msg];
        }else{
            [MBProgressHUD showPop:msg];
        }
    } Fail:^(id fail) {
        
    }];
}

-(void)updateTop:(NSDictionary *)infoDic {
    NSString *itemNums1 = [NSString stringWithFormat:@"%@ %@",YZMsg(@"作品"),[infoDic valueForKey:@"workVideos"]];
    NSString *itemNums2 = [NSString stringWithFormat:@"%@ %@",YZMsg(@"喜欢"),[infoDic valueForKey:@"likeVideos"]];
    NSString *itemNums3 = [NSString stringWithFormat:@"%@ %@",YZMsg(@"收藏"),[infoDic valueForKey:@"collectionVideos"]];
    if ([_otherUid isEqual:[Config getOwnID]]) {
        _dataArray = @[itemNums1,itemNums2,itemNums3];
    }else{
        _dataArray = @[itemNums1,itemNums2];
    }
    [_tabBar reloadData];
}

-(void)topViewEvent:(TopCtrType)ctrType andRes:(NSDictionary *)resDic {
    if (ctrType == TopCtrType_Layout) {
        CGFloat top_height = [minstr([resDic valueForKey:@"top_height"]) floatValue];
        _topView.height = top_height;
        segStartY = _topView.height;
        if (!_isTabbar) {
            scrContentH = _window_height + segStartY-64-statusbarHeight;
        }else{
            scrContentH = _window_height + segStartY-49-64-statusbarHeight-ShowDiff;
        }
        _scrollview.contentSize = CGSizeMake(0, scrContentH);
        _tabBar.frame = CGRectMake(0, _topView.bottom, _window_width, 44);
        _pagerController.view.frame = CGRectMake(0, _tabBar.bottom, _window_width, scrContentH-segStartY-_tabBar.height);
    }else if (ctrType == TopCtrType_Attent){
        NSString *isattent = minstr([resDic valueForKey:@"isattent"]);
        if (self.followEvent) {
            self.followEvent(isattent);
        }
    }
}
#pragma mark - set/get
-(UIScrollView *)scrollview {
    if (!_scrollview) {
        CGFloat scrH = _window_height-49-ShowDiff;
        if (!_isTabbar) {
            scrH = _window_height;
        }
        _scrollview = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 0,_window_width, scrH)];
        _scrollview.backgroundColor = Normal_Color;
        _scrollview.delegate = self;
        _scrollview.bounces = NO;
        _scrollview.pagingEnabled = NO;
        _scrollview.showsVerticalScrollIndicator = NO;
        YBWeakSelf;
        _topView = [[YBCenterTopView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 0)];
        _topView.isChatPage = _isChatPage;
        _topView.isTabbar = _isTabbar;
        _topView.otherUid = _otherUid ? _otherUid : [Config getOwnID];;
        _topView.topEvent = ^(TopCtrType ctrType, NSDictionary *resDic) {
            [weakSelf topViewEvent:ctrType andRes:resDic];
        };
        [_scrollview addSubview:_topView];
        
        _dataArray = @[YZMsg(@"作品"),YZMsg(@"喜欢"),YZMsg(@"收藏")];
        CGFloat cellSpace = 40;
        if (![_otherUid isEqual:[Config getOwnID]]) {
            _dataArray = @[YZMsg(@"作品"),YZMsg(@"喜欢")];
            cellSpace = 80;
        }
        
        TYTabPagerBar *tabBar = [[TYTabPagerBar alloc]init];
        tabBar.dataSource = self;
        tabBar.delegate = self;
        tabBar.layout.barStyle = TYPagerBarStyleProgressView;
        tabBar.layout.selectedTextColor = UIColor.whiteColor;
        tabBar.layout.normalTextColor = UIColor.whiteColor;
        tabBar.layout.selectedTextFont = [UIFont boldSystemFontOfSize:17];
        tabBar.layout.normalTextFont = [UIFont boldSystemFontOfSize:17];
        tabBar.layout.progressColor = UIColor.whiteColor;
        tabBar.layout.progressHeight = 4;
        tabBar.layout.progressRadius = 2;
        tabBar.layout.progressHorEdging = 10;
        tabBar.layout.progressVerEdging = 5;
        tabBar.layout.cellWidth = 0;
        tabBar.layout.cellSpacing = cellSpace;
        tabBar.layout.adjustContentCellsCenter = YES;
        tabBar.backgroundColor = UIColor.clearColor;
        
        [tabBar registerClass:[TYTabPagerBarCell class] forCellWithReuseIdentifier:[TYTabPagerBarCell cellIdentifier]];
        [_scrollview addSubview:tabBar];
        _tabBar = tabBar;
        TYPagerController *pagerController = [[TYPagerController alloc] init];
        pagerController.dataSource = self;
        pagerController.delegate = self;
        pagerController.layout.adjustScrollViewInset = NO;
        pagerController.layout.scrollView.backgroundColor = Normal_Color;
        [self addChildViewController:pagerController];
        [_scrollview addSubview:pagerController.view];
        _pagerController = pagerController;
        
    }
    return _scrollview;
}
#pragma mark - TYTabPagerBarDataSource
- (NSInteger)numberOfItemsInPagerTabBar {
    return _dataArray.count;
}
- (UICollectionViewCell<TYTabPagerBarCellProtocol> *)pagerTabBar:(TYTabPagerBar *)pagerTabBar cellForItemAtIndex:(NSInteger)index {
    UICollectionViewCell<TYTabPagerBarCellProtocol> *cell = [pagerTabBar dequeueReusableCellWithReuseIdentifier:[TYTabPagerBarCell cellIdentifier] forIndex:index];
    cell.titleLabel.text = _dataArray[index];
    return cell;
}

#pragma mark - TYTabPagerBarDelegate
- (CGFloat)pagerTabBar:(TYTabPagerBar *)pagerTabBar widthForItemAtIndex:(NSInteger)index {
    NSString *title = _dataArray[index];
    return [pagerTabBar cellWidthForTitle:title];
}

- (void)pagerTabBar:(TYTabPagerBar *)pagerTabBar didSelectItemAtIndex:(NSInteger)index {
    [_pagerController scrollToControllerAtIndex:index animate:YES];
}

#pragma mark - TYPagerControllerDataSource
- (NSInteger)numberOfControllersInPagerController {
    return _dataArray.count;
}
- (UIViewController *)pagerController:(TYPagerController *)pagerController controllerForIndex:(NSInteger)index prefetching:(BOOL)prefetching {
    if (index == 0) {
        // 作品
        if (!_listVC1) {
            _listVC1 = [self createListVCType:ListType_Normal];
        }
        return _listVC1;
    }else if (index == 1){
        // 喜欢
        if (!_listVC2) {
            _listVC2 = [self createListVCType:ListType_Likes];
        }
        return _listVC2;
    }else{
        // 收藏
        if (!_listVC3) {
            _listVC3 = [self createListVCType:ListType_Collection];
        }
        return _listVC3;
    }
}
-(CenterListVC *)createListVCType:(ListType)listType {
    CenterListVC *listVC = [[CenterListVC alloc]init];
    listVC.delegate = self;
    listVC.listType = listType;
    listVC.otherUid = _otherUid ? _otherUid : [Config getOwnID];
    listVC.isTabbar = _isTabbar;
    return listVC;
}
#pragma mark - TYPagerControllerDelegate
- (void)pagerController:(TYPagerController *)pagerController transitionFromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex animated:(BOOL)animated {
    [_tabBar scrollToItemFromIndex:fromIndex toIndex:toIndex animate:animated];
}
-(void)pagerController:(TYPagerController *)pagerController transitionFromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex progress:(CGFloat)progress {
    [_tabBar scrollToItemFromIndex:fromIndex toIndex:toIndex progress:progress];
}
- (void)pagerController:(TYPagerController *)pagerController viewDidAppear:(UIViewController *)viewController forIndex:(NSInteger)index;{
    // 使用空数据代理会引起滑动问题,这里主动调用一下didScroll重置子视图的滚动属性
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self scrollViewDidScroll:_scrollview];
    });
}
#pragma mark - 设置滑动事件
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset{
    //NSLog(@"centerVC======%f",velocity.y);
}
-(void)scrollViewDidScroll:(UIScrollView *)scrollView{
    NSLog(@"centerVC==scrContentH:%f===did-scr===%f",segStartY-64-statusbarHeight,scrollView.contentOffset.y);
//    if(scrollView.contentOffset.y >= segStartY-64-statusbarHeight){
//        _listVC1.collectionView.scrollEnabled = YES;
//        _listVC2.collectionView.scrollEnabled = YES;
//        _listVC3.collectionView.scrollEnabled = YES;
//        [self.view bringSubviewToFront:self.naviView];
//    }else{
//        _listVC1.collectionView.scrollEnabled = NO;
//        _listVC2.collectionView.scrollEnabled = NO;
//        _listVC3.collectionView.scrollEnabled = NO;
//        [self.view sendSubviewToBack:self.naviView];
//    }
    
    self.titleL.alpha = scrollView.contentOffset.y/(segStartY-64-statusbarHeight);
    self.subNavi.alpha = scrollView.contentOffset.y/(segStartY-64-statusbarHeight);
}

-(void)listScrollToTop:(CGFloat)Y{
    if (Y <0) {
        [_scrollview setContentOffset:CGPointMake(0,0) animated:YES];
    }
}
@end
