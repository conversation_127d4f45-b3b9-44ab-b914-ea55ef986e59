//
//  YBPrivateVC.m
//  YBVideo
//
//  Created by YB007 on 2020/6/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBPrivateVC.h"
#import "GuideViewController.h"
#import <YYText/YYLabel.h>
#import <YYText/NSAttributedString+YYText.h>

@interface YBPrivateVC ()

@property(nonatomic,strong)UIButton *agreeBtn;
@property(nonatomic,strong)UIButton *unagreeBtn;

@property(nonatomic,strong)YYLabel *contentL;

@end

@implementation YBPrivateVC


- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self pullData];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    AFNetworkReachabilityManager *netManager = [AFNetworkReachabilityManager sharedManager];
    [netManager startMonitoring];
    [netManager setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status){
        if (status == AFNetworkReachabilityStatusNotReachable){
            [self pullData];
        }else if (status == AFNetworkReachabilityStatusUnknown || status == AFNetworkReachabilityStatusNotReachable){
            NSLog(@"nonetwork-------");
            [self pullData];
        }else if ((status == AFNetworkReachabilityStatusReachableViaWWAN)||(status == AFNetworkReachabilityStatusReachableViaWiFi)){
            [self pullData];
            NSLog(@"wifi-------");
        }
    }];
    
    
    self.leftBtn.hidden = YES;
    
    self.agreeBtn = [self createBtn:YZMsg(@"同意") bgColor:Pink_Cor];
    self.unagreeBtn = [self createBtn:YZMsg(@"暂不使用") bgColor:RGB_COLOR(@"#ffffff", 0.1)];
    [_agreeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_bottom).offset(-36-ShowDiff);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.4);
        make.height.mas_equalTo(35);
        make.centerX.equalTo(self.view.mas_centerX).multipliedBy(1.5);
    }];
    [_unagreeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.centerY.equalTo(_agreeBtn);
        make.centerX.equalTo(self.view.mas_centerX).multipliedBy(0.5);
    }];
    
    _contentL = [[YYLabel alloc]init];
    _contentL.textColor = RGB_COLOR(@"#ffffff", 1);
    _contentL.font = SYS_Font(15);
    _contentL.numberOfLines = 0;
    _contentL.preferredMaxLayoutWidth = _window_width*0.9;
    [self.view addSubview:_contentL];
    [_contentL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.view.mas_width).multipliedBy(0.9);
        make.centerX.equalTo(self.view.mas_centerX);
        make.top.equalTo(self.naviView.mas_bottom).offset(30);
        make.bottom.lessThanOrEqualTo(_agreeBtn.mas_top).offset(-10);
    }];
    
    if (_hideBottom) {
        self.leftBtn.hidden = NO;
        _agreeBtn.hidden = _unagreeBtn.hidden = YES;
    }
    
}

-(UIButton *)createBtn:(NSString *)title bgColor:(UIColor *)bgColor{
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    btn.titleLabel.font = SYS_Font(15);
    [btn setTitleColor:RGB_COLOR(@"#ffffff", 1) forState:0];
    [btn setTitle:title forState:0];
    btn.backgroundColor = bgColor;
    btn.layer.cornerRadius = 5;
    btn.layer.masksToBounds = YES;
    [btn addTarget:self action:@selector(clickBtn:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:btn];
    return btn;
}
-(void)clickBtn:(UIButton *)sender {
    if (sender == _unagreeBtn) {
        dispatch_async(dispatch_get_main_queue(), ^{
            //不同意条款主动结束程序
            //运行时不必关心这个函数；
            abort();
        });
    }
    if (sender == _agreeBtn) {
        [PublicObj resetVC:[[GuideViewController alloc] init]];
    }
}

-(void)pullData {
    YBWeakSelf;
    [YBNetworking postWithUrl:@"Home.getLogin" Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            NSDictionary *alertDic = [infoDic valueForKey:@"login_alert"];
            [weakSelf setupDate:alertDic];
        }
    } Fail:^(id fail) {
       
    }];
}
-(void)setupDate:(NSDictionary *)dataDic {
    
    self.titleL.text = minstr([dataDic valueForKey:@"title"]);
    if (_hideBottom) {
        self.titleL.text = YZMsg(@"隐私政策");
    }
    _contentL.text = [dataDic valueForKey:@"content"];
    NSArray *ppA = [NSArray arrayWithArray:[dataDic valueForKey:@"message"]];
    
    NSMutableAttributedString *textAtt = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@",_contentL.text]];
    [textAtt addAttribute:NSForegroundColorAttributeName value:RGB_COLOR(@"#ffffff", 1) range:textAtt.yy_rangeOfAll];
    [textAtt addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:15] range:textAtt.yy_rangeOfAll];
    for (int i=0; i<ppA.count; i++) {
        NSDictionary *subDic = ppA[i];
        NSRange clickRange = [[textAtt string]rangeOfString:minstr([subDic valueForKey:@"title"])];
        [textAtt yy_setTextHighlightRange:clickRange color:Pink_Cor backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
            NSLog(@"协议");
            if ([PublicObj checkNull:minstr([subDic valueForKey:@"url"])]) {
                [MBProgressHUD showError:YZMsg(@"链接不存在")];
                return;
            }
            PubH5 *h5vc = [[PubH5 alloc]init];
            h5vc.url = minstr([subDic valueForKey:@"url"]);;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:h5vc animated:YES];
        }];
    }
    textAtt.yy_lineSpacing = 5;
    _contentL.attributedText = textAtt;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
