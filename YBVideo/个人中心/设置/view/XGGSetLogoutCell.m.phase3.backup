//
//  userItemCell14.m
//  YBVideo
//
//  Created by cat on 16/3/10.
//  Copyright © 2016年 cat. All rights reserved.
//

#import "SetLogoutCell.h"

@implementation SetLogoutCell

- (void)awakeFromNib{
    [super awakeFromNib];
    _signOutL.text = YZMsg(@"退出登录");
}

-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        
        
    }
    return self;
}

+(SetLogoutCell *)cellWithTableView:(UITableView *)tableView{
    SetLogoutCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SetLogoutCell"];
    
    if (!cell) {
        cell = [[NSBundle mainBundle]loadNibNamed:@"SetLogoutCell" owner:self options:nil].lastObject;
    }
    
    return cell;
    
    
}

@end
