//
//  SetCell.m
//  YBVideo
//
//  Created by cat on 16/3/13.
//  Copyright © 2016年 cat. All rights reserved.
//

#import "SetCell.h"

@implementation SetCell



-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        
        
    }
    return self;
}

+(SetCell *)cellWithTableView:(UITableView *)tableView{
    SetCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SetCell"];
    
    if (!cell) {
        cell = [[NSBundle mainBundle]loadNibNamed:@"SetCell" owner:self options:nil].lastObject;
    }
    
    return cell;
    
    
}
- (IBAction)clickSwitchBtn:(UIButton *)sender {
    
    sender.selected = !sender.selected;
    [common saveMsgVoiceSwitch:sender.selected];
}

@end
