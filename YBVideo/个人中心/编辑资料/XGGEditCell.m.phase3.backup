//
//  EditCell.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/14.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "EditCell.h"

@implementation EditCell

- (void)awakeFromNib {
    [super awakeFromNib];
    if ([lagType isEqual:ZH_CN]) {
        _leftNameWidth.constant = 60;
    }else{
        _leftNameWidth.constant = 80;
    }
    _nameL.text = YZMsg(@"昵称");
    _nameTF.placeholder = YZMsg(@"请输入昵称");
    _birthL.text = YZMsg(@"生日");
    _birthTF.placeholder = YZMsg(@"请选择生日");
    _sexL.text = YZMsg(@"性别");
    _sexTF.placeholder = YZMsg(@"请选择性别");
    _localL.text = YZMsg(@"地区");
    _locationTF.placeholder = YZMsg(@"选择您所在的地区");
    _signaL.text = YZMsg(@"签名");
    _signatoryTF.placeholder = YZMsg(@"留下点什么吧~");
    
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    
}

+(EditCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath{
    EditCell *cell;
    switch (indexPath.row) {
        case 0:{
            cell = [tableView dequeueReusableCellWithIdentifier:@"nickname"];
            if (!cell) {
                cell = [[[NSBundle mainBundle]loadNibNamed:@"EditCell" owner:nil options:nil]objectAtIndex:0];
            }
            cell.nameTF.placeCol = RGB_COLOR(@"#6F7073", 1);
            
        }break;
        case 1:{
            cell = [tableView dequeueReusableCellWithIdentifier:@"birth"];
            if (!cell) {
                cell = [[[NSBundle mainBundle]loadNibNamed:@"EditCell" owner:nil options:nil]objectAtIndex:1];
            }
            cell.birthTF.placeCol = RGB_COLOR(@"#6F7073", 1);
        }break;
        case 2:{
            cell = [tableView dequeueReusableCellWithIdentifier:@"sex"];
            if (!cell) {
                cell = [[[NSBundle mainBundle]loadNibNamed:@"EditCell" owner:nil options:nil]objectAtIndex:2];
            }
            cell.sexTF.placeCol = RGB_COLOR(@"#6F7073", 1);
           
        }break;
        case 3:{
            cell = [tableView dequeueReusableCellWithIdentifier:@"location"];
            if (!cell) {
                cell = [[[NSBundle mainBundle]loadNibNamed:@"EditCell" owner:nil options:nil]objectAtIndex:3];
            }
            cell.locationTF.placeCol = RGB_COLOR(@"#6F7073", 1);
        }break;
        case 4:{
            cell = [tableView dequeueReusableCellWithIdentifier:@"signatory"];
            if (!cell) {
                cell = [[[NSBundle mainBundle]loadNibNamed:@"EditCell" owner:self options:nil]objectAtIndex:4];
            }
            cell.signatoryTF.placeCol = RGB_COLOR(@"#6F7073", 1);
            
        }break;
            
        default:
            break;
    }
    return cell;
}

@end
