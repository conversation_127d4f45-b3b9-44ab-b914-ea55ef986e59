//
//  EditCell.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/14.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface EditCell : UITableViewCell


@property (weak, nonatomic) IBOutlet NSLayoutConstraint *leftNameWidth;


#pragma 昵称
@property (weak, nonatomic) IBOutlet MyTextField *nameTF;
@property (weak, nonatomic) IBOutlet UILabel *nameL;


#pragma 生日

@property (weak, nonatomic) IBOutlet MyTextField *birthTF;
@property (weak, nonatomic) IBOutlet UILabel *birthL;


#pragma 性别

@property (weak, nonatomic) IBOutlet MyTextField *sexTF;
@property (weak, nonatomic) IBOutlet UILabel *sexL;


#pragma 地区

@property (weak, nonatomic) IBOutlet MyTextField *locationTF;
@property (weak, nonatomic) IBOutlet UILabel *localL;

#pragma 签名

@property (weak, nonatomic) IBOutlet MyTextField *signatoryTF;
@property (weak, nonatomic) IBOutlet UILabel *signaL;

@property (weak, nonatomic) IBOutlet UILabel *numL;

+(EditCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath;

@end
