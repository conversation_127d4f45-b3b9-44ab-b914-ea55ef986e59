//
//  YBOtherCenterMore.m
//  YBVideo
//
//  Created by YB007 on 2022/4/18.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBOtherCenterMore.h"

@interface YBOtherCenterMore()<UIGestureRecognizerDelegate>

@property(nonatomic,strong)UIView *bgView;
@property(nonatomic,copy)OtherMoreBlock moreEvent;
@property(nonatomic,strong)NSDictionary *dataDic;

@end

@implementation YBOtherCenterMore
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch; {
    if ([touch.view isDescendantOfView:self.bgView]) {
        return NO;
    }
    return YES;
}
-(void)dissmissView {
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self removeFromSuperview];
}
+(instancetype)showOtherMoreWithBtns:(NSDictionary *)dataDic complete:(OtherMoreBlock)complete{
    YBOtherCenterMore *view = [[YBOtherCenterMore alloc]init];
    view.dataDic = dataDic;
    view.moreEvent = complete;
    view.frame = CGRectMake(0, 0, _window_width, _window_height);
    [[UIApplication sharedApplication].delegate.window addSubview:view];
    [view createUI];
    return view;
}

-(void)createUI {
     UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(dissmissView)];
     tap.delegate = self;
     [self addGestureRecognizer:tap];
    
    self.backgroundColor = RGB_COLOR(@"#000000", 0.5);;
    _bgView = [[UIView alloc]init];
    _bgView.backgroundColor = UIColor.whiteColor;
    _bgView.layer.cornerRadius = 10;
    _bgView.layer.masksToBounds = YES;
    [self addSubview:_bgView];
    [_bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.width.centerX.equalTo(self);
    }];
    
    //
    UILabel *titleL = [[UILabel alloc]init];
    titleL.font = SYS_Font(15);
    titleL.text = minstr([_dataDic valueForKey:@"name_str"]);
    titleL.textColor = RGB_COLOR(@"#323232", 1);
    [_bgView addSubview:titleL];
    [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_bgView.mas_left).offset(20);
        make.right.lessThanOrEqualTo(_bgView.mas_right).offset(-20);
        make.top.equalTo(_bgView.mas_top).offset(25);
    }];
    
    //
    UILabel *idL = [[UILabel alloc]init];
    idL.font = SYS_Font(13);
    idL.text = minstr([_dataDic valueForKey:@"id_str"]);
    idL.textColor = RGB_COLOR(@"#7d7d7d", 1);
    [_bgView addSubview:idL];
    [idL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleL.mas_left);
        make.right.lessThanOrEqualTo(_bgView.mas_right).offset(-20);
        make.top.equalTo(titleL.mas_bottom).offset(5);
    }];
    
    //
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:[UIImage imageNamed:@"个中-他人关闭"] forState:0];
    [closeBtn addTarget:self action:@selector(dissmissView) forControlEvents:UIControlEventTouchUpInside];
    [_bgView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(40);
        make.top.equalTo(_bgView.mas_top).offset(0);
        make.right.equalTo(_bgView.mas_right).offset(0);
    }];
    
    //funview
    NSArray *listA = @[
        @{@"title":@"私信",
          @"img":@"个中头更-私信",
          @"action":@(BtnFun_CenterImMsg),
        },
        @{@"title":@"举报",
          @"img":@"个中头更-举报",
          @"action":@(BtnFun_CenterReport),
        },
        @{@"title":minstr([_dataDic valueForKey:@"black_str"]),
          @"img":@"个中头更-拉黑",
          @"action":@(BtnFun_CenterBlack),
        },
    ];
    
    CGFloat itemSpace = 8;
    CGFloat itemW = (_window_width-40-itemSpace*(listA.count-1))/listA.count;
    UIView *boxView = [[UIView alloc]init];
    boxView.backgroundColor = UIColor.clearColor;
    [_bgView addSubview:boxView];
    [boxView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(_bgView.mas_width).offset(-40);
        make.centerX.equalTo(_bgView);
        make.top.equalTo(idL.mas_bottom).offset(20);
        make.height.mas_equalTo(70);
        make.bottom.equalTo(_bgView.mas_bottom).offset(-ShowDiff-30);
    }];
    MASViewAttribute *mas_left = boxView.mas_left;
    CGFloat realSpace = 0;
    for (int i = 0; i<listA.count; i++) {
        UIView *item = [[UIView alloc]init];
        item.backgroundColor = RGB_COLOR(@"#f4f4f4", 1);
        item.layer.cornerRadius = 5;
        item.layer.masksToBounds = YES;
        [boxView addSubview:item];
        [item mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(mas_left).offset(realSpace);
            make.height.centerY.equalTo(boxView);
            make.width.mas_equalTo(itemW);
        }];
        realSpace = itemSpace;
        mas_left = item.mas_right;
        
        NSDictionary *subDic = listA[i];
        UIImageView *img = [[UIImageView alloc]init];
        img.image = [UIImage imageNamed:[subDic valueForKey:@"img"]];
        [item addSubview:img];
        [img mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(20);
            make.centerX.equalTo(item);
            make.bottom.equalTo(item.mas_centerY).offset(-2);
        }];
        UILabel *nameL = [[UILabel alloc]init];
        nameL.textColor = RGB_COLOR(@"#4F5259", 1);
        nameL.font = SYS_Font(12);
        nameL.text = [subDic valueForKey:@"title"];
        [item addSubview:nameL];
        [nameL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(item.mas_centerY).offset(2);
            make.centerX.equalTo(item);
            make.width.lessThanOrEqualTo(item);
        }];
        
        YBButton *shadowBtn = [YBButton buttonWithType:UIButtonTypeCustom];
        shadowBtn.btnFunStatus = [[subDic valueForKey:@"action"] integerValue];
        [shadowBtn addTarget:self action:@selector(clickShadownBtn:) forControlEvents:UIControlEventTouchUpInside];
        [item addSubview:shadowBtn];
        [shadowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(item);
        }];
    }
}
-(void)clickShadownBtn:(YBButton *)sender {
    if (self.moreEvent) {
        self.moreEvent(sender.btnFunStatus);
    }
    [self dissmissView];
}

@end
