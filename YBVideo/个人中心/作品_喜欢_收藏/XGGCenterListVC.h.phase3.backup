//
//  CenterListVC.h
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/14.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>


@protocol ListScrollDelegate <NSObject>
-(void)listScrollToTop:(CGFloat)Y;
@end

typedef NS_ENUM(NSInteger,ListType) {
    ListType_Normal,            // 作品
    ListType_Likes,             // 喜欢
    ListType_Collection,        // 收藏
};

@interface CenterListVC : YBBaseViewController

@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,assign)id<ListScrollDelegate> delegate;

@property(nonatomic,assign)ListType listType;
@property(nonatomic,strong)NSString *otherUid;
@property(nonatomic,assign)BOOL canShowGif;
//tabbar
@property(nonatomic,assign)BOOL isTabbar;



@end
