//
//  CenterListVC.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/14.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "CenterListVC.h"

#import "NearbyVideoModel.h"
#import "CenterListCell.h"
#import "YBGetVideoObj.h"

@interface CenterListVC ()<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout,DZNEmptyDataSetSource,DZNEmptyDataSetDelegate>
{
    BOOL canBackTop;
    NSString *pullurl;
}

@property(nonatomic,assign)int paging;
@property(nonatomic,strong)UIImageView *backimage;
@property(nonatomic,strong)NSMutableArray *videoArray;
@property(nonatomic,strong)NSArray *models;

@end

@implementation CenterListVC

-(void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (_listType == ListType_Normal) {
        [PublicView hideCenterGif:nil];
    }
}
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self pullData];
}

-(NSArray *)models{
    NSMutableArray *m_array = [NSMutableArray array];
    for (NSDictionary *dic in self.videoArray) {
        NearbyVideoModel *model = [NearbyVideoModel modelWithDic:dic];
        [m_array addObject:model];
    }
    _models = m_array;
    return _models;
}

-(void)pullData {
    
    if (_listType == ListType_Normal) {
       pullurl = [NSString stringWithFormat:@"Video.getHomeVideo&touid=%@&p=%d",_otherUid,_paging];
    }else if(_listType == ListType_Likes){
       pullurl = [NSString stringWithFormat:@"User.getLikeVideos&touid=%@&p=%d",_otherUid,_paging];
    }else{
        pullurl = [NSString stringWithFormat:@"User.getCollectionVideos&touid=%@&p=%d",_otherUid,_paging];
    }
    [YBNetworking postWithUrl:pullurl Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_collectionView.mj_footer endRefreshing];
        [_collectionView.mj_header endRefreshing];
        if (code == 0) {
            NSArray *infoA = [NSArray arrayWithArray:info];
            if (_paging==1) {
                [_videoArray removeAllObjects];
            }
            if (infoA.count>0) {
                [_videoArray addObjectsFromArray:infoA];
            }else{
                [_collectionView.mj_footer endRefreshingWithNoMoreData];
            }
            [self pubNoData];
            [_collectionView reloadData];
            
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [_collectionView.mj_footer endRefreshing];
        [_collectionView.mj_header endRefreshing];
        [self pubNoData];
    }];
}
-(void)pubNoData {
    if (_listType == ListType_Normal && [_otherUid isEqual:[Config getOwnID]]) {
        if (_videoArray.count > 0) {
            [PublicView hideCenterGif:nil];
        }else{
            [PublicView showCenterGif:nil];
        }
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.naviView.hidden = YES;
    
    _paging = 1;
    self.videoArray = [NSMutableArray array];
    [self.view addSubview:self.collectionView];
    
    canBackTop = NO;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}
#pragma mark - UICollectionViewDataSource
- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath{
    if (kind == UICollectionElementKindSectionFooter){
        UICollectionReusableView *view = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:@"space_footer" forIndexPath:indexPath];
        view.backgroundColor = Normal_Color;
        return view;
    }
    return nil;
}
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section;{
    return CGSizeMake(_window_width,ShowDiff);
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.models.count;
}
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    CenterListCell *cell = (CenterListCell*)[collectionView dequeueReusableCellWithReuseIdentifier:@"CenterListCell" forIndexPath:indexPath];
    cell.model = _models[indexPath.row];
    return cell;
}
#pragma mark - UICollectionViewDelegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *videoDic = _videoArray[indexPath.row];
    NSString *vidoeID = minstr([videoDic valueForKey:@"id"]);
    [YBGetVideoObj lookManeger].fromWhere = @"CenterVC";
    [YBGetVideoObj lookManeger].videoID = vidoeID;
    [YBGetVideoObj lookManeger].playIndex = (int)indexPath.row;
    [YBGetVideoObj lookManeger].videoList = [_videoArray mutableCopy];
    [YBGetVideoObj lookManeger].paging = _paging;
    [YBGetVideoObj lookManeger].baseUrl = pullurl;
    [[YBGetVideoObj lookManeger]goLookVC];
}
-(void)scrollViewDidScroll:(UIScrollView *)scrollView{
    
    NSLog(@"lsitvc=====%f===%d",scrollView.contentOffset.y,_collectionView.scrollEnabled);
    if (scrollView.contentOffset.y<=-50) {
        canBackTop = YES;
    }else{
       canBackTop = NO;
    }
}
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset{
    if (canBackTop == YES) {
        [self.delegate listScrollToTop:velocity.y];
    }
}
#pragma mark - set/get
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionVertical;
        flow.itemSize = CGSizeMake(_window_width/3-1, _window_width/3*4/3);
        flow.minimumLineSpacing = 1;
        flow.minimumInteritemSpacing = 0.5;
        CGFloat HH = _window_height-44-64-49-statusbarHeight;
        if (!_isTabbar) {
            HH = _window_height-44-64-statusbarHeight;
        }
        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0, _window_width, HH) collectionViewLayout:flow];
        [self.collectionView registerNib:[UINib nibWithNibName:@"CenterListCell" bundle:nil] forCellWithReuseIdentifier:@"CenterListCell"];
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.delegate =self;
        _collectionView.dataSource = self;
        _collectionView.backgroundColor = Normal_Color;
        _collectionView.scrollEnabled = NO;
        [_collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:@"space_footer"];
        _collectionView.emptyDataSetSource = self;
        _collectionView.emptyDataSetDelegate = self;
        _collectionView.mj_insetB = -ShowDiff;
        YBWeakSelf;
        _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            weakSelf.paging = 1;
            [self pullData];
        }];
        self.collectionView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            weakSelf.paging +=1;
            [self pullData];
        }];
    }
    return _collectionView;
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    
    NSString *text = @"";
    if (_listType == ListType_Normal && ![_otherUid isEqual:[Config getOwnID]]) {
        text = YZMsg(@"TA还没有作品");
    }else if (_listType == ListType_Likes && ![_otherUid isEqual:[Config getOwnID]]){
        text = YZMsg(@"TA还没有喜欢的视频");
    }else if (_listType == ListType_Likes && [_otherUid isEqual:[Config getOwnID]]){
        text = YZMsg(@"你赞过的作品都会放到这里");
    }else if (_listType == ListType_Collection){
        text = YZMsg(@"你收藏的作品都会放到这里");
    }
    NSDictionary *attributes = @{NSFontAttributeName: ybNodataFont,
                                 NSForegroundColorAttributeName: ybNodataCol};
    return [[NSAttributedString alloc] initWithString:text attributes:attributes];
}

- (CGFloat)verticalOffsetForEmptyDataSet:(UIScrollView *)scrollView{
    CGFloat moveY = -(statusbarHeight+64+44);
    return moveY;
}
- (BOOL)emptyDataSetShouldAllowScroll:(UIScrollView *)scrollView{
    return NO;
}



@end
