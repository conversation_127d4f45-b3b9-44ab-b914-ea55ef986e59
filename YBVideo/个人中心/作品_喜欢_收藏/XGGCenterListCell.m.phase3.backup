//
//  CenterListCell.m
//  YBVideo
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/14.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "CenterListCell.h"
#import "NearbyVideoModel.h"

@implementation CenterListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    _auditL.layer.borderColor = RGB_COLOR(@"#ffffff", 0.4).CGColor;
    _auditL.layer.borderWidth = 1;
    _auditL.layer.masksToBounds = YES;
    _auditL.layer.cornerRadius = 10;
    
}


- (void)setModel:(NearbyVideoModel *)model{
    _model = model;
    [self.videoBgIV sd_setImageWithURL:[NSURL URLWithString:_model.videoImage]];
    self.zanNumL.text = _model.zanNum;
    if ([_model.status isEqual:@"0"]) {
        _auditL.hidden = NO;
    }else{
        _auditL.hidden = YES;
    }
}

@end
