//
//  YBAlertActionSheet.m
//  yunbaolive
//
//  Created by ybRRR on 2020/5/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBAlertActionSheet.h"

@implementation YBAlertActionSheet

-(instancetype)initWithFrame:(CGRect)frame cancelTitle:(NSString *)cancelText cancelColor:(UIColor *)cancelcolors andRowHeight:(int)rowH andOtherTitle:(NSArray *)otherArr
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor =RGBA(10, 10, 10, 0.4);
        
        cancelBtn = [UIButton buttonWithType:0];
        cancelBtn.frame = CGRectMake(10, _window_height-rowH-15, _window_width-20, rowH);
        [cancelBtn setTitle:cancelText forState:(UIControlState)0];
        [cancelBtn setTitleColor:cancelcolors forState:0];
        cancelBtn.titleLabel.font = [UIFont systemFontOfSize:15];
        cancelBtn.layer.cornerRadius = 10;
        cancelBtn.layer.masksToBounds = YES;
        [cancelBtn setBackgroundColor:[UIColor whiteColor]];
        [cancelBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [self addSubview:cancelBtn];
        
        titleBack = [[UIView alloc]initWithFrame:CGRectMake(10, _window_height-rowH-25-rowH*otherArr.count, _window_width-20, rowH*otherArr.count)];
        titleBack.layer.cornerRadius = 10;
        titleBack.layer.masksToBounds = YES;
        titleBack.backgroundColor = [UIColor whiteColor];
        [self addSubview:titleBack];
        
        for (int i = 0; i < otherArr.count ; i ++) {
            UIButton *btn = [UIButton buttonWithType:0];
            btn.frame = CGRectMake(0, i * rowH, titleBack.width, rowH);
            [btn setTitle:otherArr[i] forState:0];
            [btn setTitleColor:[UIColor blackColor] forState:0];
            btn.titleLabel.font = [UIFont systemFontOfSize:15];
            [btn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
            [titleBack addSubview:btn];
            if (i != 0) {
                UILabel *lineLb = [[UILabel alloc]initWithFrame:CGRectMake(0, i * rowH, titleBack.width, 1)];
                lineLb.backgroundColor = RGB(240, 240, 240);
                [titleBack addSubview:lineLb];
            }
        }
    }
    return self;
}
-(void)btnClick:(UIButton *)sender{
    if (self.btnEvent) {
        self.btnEvent(sender.titleLabel.text);
    }
}
@end
