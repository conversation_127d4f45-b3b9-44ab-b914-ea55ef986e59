//
//  YBButton.h
//  YBVideo
//
//  Created by YB007 on 2022/4/18.
//  Copyright © 2022 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,BtnFunStatus) {
    BtnFun_Default,
    
    /// 个人中心
    BtnFun_CenterShop,              /// 直播小店
    BtnFun_CenterCollection,        /// 收藏
    BtnFun_CenterLiveRecord,        /// 直播记录
    
    BtnFun_CenterZanList,           /// 赞列表
    Btnfun_CenterFanList,           /// 粉丝列表
    Btnfun_CenterFollowList,        /// 关注列表
    
    BtnFun_CenterEdit,              /// 编辑资料
    BtnFun_CenterDaySign,           /// 签到
    BtnFun_CenterImMsg,             /// 私信
    BtnFun_CenterFollow,            /// 关注
    BtnFun_CenterBlack,             /// 拉黑
    BtnFun_CenterReport,            /// 举报
    BtnFun_CenterReturn,            /// 返回
    BtnFun_CenterMore,              /// 更多
    BtnFun_CenterYoung,             /// 青少年
    BtnFun_CenterQrcode,            /// 二维码
    BtnFun_CenterAge,               /// 年龄
    BtnFun_CenterSex,               /// 性别
    BtnFun_CenterCity,              /// 城市
    BtnFun_CenterVip,               /// Vip
};

@interface YBButton : UIButton

@property(nonatomic,assign)BtnFunStatus btnFunStatus;

@end


