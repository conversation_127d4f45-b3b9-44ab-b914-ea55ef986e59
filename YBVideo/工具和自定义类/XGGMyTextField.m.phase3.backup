//
//  MyTextField.m
//  YBVideo
//
//  Created by Rookie on 2017/8/19.
//  Copyright © 2017年 cat. All rights reserved.
//

#import "MyTextField.h"

@implementation MyTextField

-(void)drawPlaceholderInRect:(CGRect)rect {
    //设置富文本属性
    NSMutableDictionary *dictM = [NSMutableDictionary dictionary];
    dictM[NSFontAttributeName] = self.font;
    dictM[NSForegroundColorAttributeName] = _placeCol ? _placeCol:[UIColor whiteColor];
    CGPoint point = CGPointMake(0, (rect.size.height - self.font.lineHeight) * 0.5);
    [self.placeholder drawAtPoint:point withAttributes:dictM];
}

@end
