//
//  RKHorPickerView.m
//  YBVideo
//
//  Created by YB007 on 2019/12/9.
//  Copyright © 2019 cat. All rights reserved.
//

#import "RKHorPickerView.h"
#import "UIImage+RKCircleImg.h"

@interface RKHorPickerView()<UIPickerViewDelegate,UIPickerViewDataSource>

@end

@implementation RKHorPickerView {
    UIPickerView *picker;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self performSelector:@selector(initPickerView)];
    }
    return self;
}

-(void)initPickerView{
    CGAffineTransform rotate = CGAffineTransformMakeRotation(-M_PI/2);
    rotate = CGAffineTransformScale(rotate, 0.1, 1);
    //旋转 -π/2角度
    picker = [[UIPickerView alloc]initWithFrame:CGRectMake(0, 0, self.frame.size.height*10, self.frame.size.width-6)];
    
    [picker setTag: 10086];
    picker.delegate = self;
    picker.dataSource = self;
    picker.showsSelectionIndicator = false;
    [picker setBackgroundColor:[UIColor clearColor]];
    
    UIImageView *imageV = [[UIImageView alloc]initWithFrame:CGRectMake(self.frame.size.width/2-2.5, (self.frame.size.height- 6), 5, 5)];
    imageV.image = [[self getImgWithColor:[UIColor clearColor] withSize:CGSizeMake(5, 5)] rk_circleImage];
    UIView *bgV = [[UIView alloc]initWithFrame:CGRectMake(0, 0, self.frame.size.width,self.frame.size.height)];
    [bgV addSubview:picker];
    [bgV addSubview:imageV];
    [self addSubview:bgV];
    [picker setTransform:rotate];
    picker.center = CGPointMake(self.frame.size.width / 2, self.frame.size.height / 2);
    
}

-(NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component{
    return _dataArray.count;
}

-(NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView{
    return 1;
}

-(UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view{
    
    if ([self.delegate performSelector:@selector(pickerViewBeginScroll)]) {
        [self.delegate pickerViewBeginScroll];
    }
    
    CGAffineTransform rotateItem = CGAffineTransformMakeRotation(M_PI/2);
    rotateItem = CGAffineTransformScale(rotateItem, 1, 10);
    
    CGFloat width = self.frame.size.height;
    
    UIView *itemView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, width*2, width-6)];
    itemView.backgroundColor = UIColor.clearColor;
    
    UILabel *title = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, CGRectGetWidth(itemView.frame), CGRectGetHeight(itemView.frame))];
    title.font = [UIFont systemFontOfSize:15];
    title.textColor = UIColor.whiteColor;
    title.text = _dataArray[row];
    title.textAlignment = NSTextAlignmentCenter;
    [itemView addSubview:title];
    
    itemView.transform = rotateItem;
     
    [[pickerView.subviews objectAtIndex:1] setHidden:TRUE];
    [[pickerView.subviews objectAtIndex:2] setHidden:TRUE];
    return itemView;
}


- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component __TVOS_PROHIBITED{
    return self.frame.size.height;
}

- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component{
    return self.frame.size.height*2;
}


-(void)scrollToIndex:(NSInteger)scrollToIndex{
    [picker selectRow:scrollToIndex inComponent:0 animated:true];
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component{
    [self.delegate pickerView:pickerView didSelectRow:row];
}

-(UIImage*)getImgWithColor:(UIColor *)color withSize:(CGSize)size{
    
    CGRect rect = CGRectMake(0.0f,0.0f, size.width,size.height);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context =UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *image =UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

@end
