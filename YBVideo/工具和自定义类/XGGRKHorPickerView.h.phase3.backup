//
//  RKHorPickerView.h
//  YBVideo
//
//  Created by YB007 on 2019/12/9.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol RKPickerViewDelegate <NSObject>

@optional

/**
 pickerView选中item代理
 @param row 选中的row
 */
- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row;

/** PickerView 开始滚动 */
- (void)pickerViewBeginScroll;
@end


@interface RKHorPickerView : UIView

/** PickerView 数据源 */
@property (nonatomic,strong) NSArray *dataArray;

/** 滑动到指定位置 */
@property (nonatomic,assign,setter=scrollToIndex:) NSInteger scrollToIndex;
/** pickerView代理 */
@property (nonatomic,weak) id<RKPickerViewDelegate> delegate;

@end


