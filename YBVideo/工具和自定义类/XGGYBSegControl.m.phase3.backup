//
//  YBSegControl.m
//  YBmyth
//
//  Created by <PERSON><PERSON><PERSON> on 2018/1/16.
//  Copyright © 2018年 Rookie. All rights reserved.
//

#import "YBSegControl.h"

@implementation YBSegControl


/**
 * 重写 以下两个方法 做到重复点击也能响应事件
 */
-(void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self setSelectedSegmentIndex:self.selectedSegmentIndex];
    [super touchesEnded:touches withEvent:event];
}
-(void)setSelectedSegmentIndex:(NSInteger)selectedSegmentIndex {
    if (self.selectedSegmentIndex == selectedSegmentIndex) {
        [super setSelectedSegmentIndex:UISegmentedControlNoSegment];
    }else {
        [super setSelectedSegmentIndex:selectedSegmentIndex];
    }
}

-(void)drawRect:(CGRect)rect{
    
//    for (int i=0; i<self.subviews.count; i++) {
//        if (self.selectedSegmentIndex == i) {
//            UIView *v = self.subviews[i];
//            UIColor *circleColor = [UIColor redColor];
//            UIBezierPath* linePath = [UIBezierPath bezierPath];
//            CGFloat y = CGRectGetHeight(rect);
//            [linePath moveToPoint:CGPointMake(CGRectGetMinX(v.frame), y)];
//            [linePath addLineToPoint:CGPointMake(CGRectGetMaxX(v.frame), y)];
//            linePath.lineWidth = 2;
//            [circleColor setStroke];
//            [linePath stroke];
//        }
//    }
    
}

@end
