//
//  RKCircularProgress.m
//  TestApp
//
//  Created by YB007 on 2020/7/7.
//  Copyright © 2020 Rookie. All rights reserved.
//

#import "RKCircularProgress.h"


@interface RKCircularProgress()


@end

@implementation RKCircularProgress


- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = UIColor.clearColor;
        self.layer.cornerRadius = 5;
        self.clipsToBounds = YES;
    }
    return self;
}


- (void)setProgress:(CGFloat)progress {
    _progress = progress;
    
    dispatch_async(dispatch_get_main_queue(), ^{
//        if (progress >= 1.0) {
//            [self removeFromSuperview];
//        } else {
            [self setNeedsDisplay];
//        }
    });
    
}

- (void)setCenterProgressText:(NSString *)text withAttributes:(NSDictionary *)attributes {
    CGFloat xCenter = self.frame.size.width * 0.5;
    CGFloat yCenter = self.frame.size.height * 0.5;
    
    CGSize strSize = [text sizeWithAttributes:attributes];
    CGFloat strX = xCenter - strSize.width * 0.5;
    CGFloat strY = yCenter - strSize.height * 0.5;
    [text drawAtPoint:CGPointMake(strX, strY) withAttributes:attributes];
    
}

// 清除指示器
- (void)dismiss {
    self.progress = 1.0;
}

- (void)drawRect:(CGRect)rect {
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    
    CGFloat xCenter = rect.size.width * 0.5;
    CGFloat yCenter = rect.size.height * 0.5;
    [[UIColor whiteColor] set];
    
    CGContextSetLineWidth(ctx, 2);
    CGContextSetLineCap(ctx, kCGLineCapRound);
    CGFloat to = -M_PI * 0.5 + self.progress * M_PI * 2 + 0.00; // 初始值0.01
    CGFloat radius = MIN(rect.size.width, rect.size.height) * 0.2;
    CGContextAddArc(ctx, xCenter, yCenter, radius, -M_PI * 0.5, to, 0);
    CGContextStrokePath(ctx);
    
    //进度数字
    NSString *progressStr = [NSString stringWithFormat:@"%.0f%%", self.progress * 100];
    NSMutableDictionary *attributes = [NSMutableDictionary dictionary];
    attributes[NSFontAttributeName] = [UIFont systemFontOfSize:MIN(rect.size.width, rect.size.height)*0.1];
    attributes[NSForegroundColorAttributeName] = [UIColor whiteColor];
    [self setCenterProgressText:progressStr withAttributes:attributes];
}

@end
