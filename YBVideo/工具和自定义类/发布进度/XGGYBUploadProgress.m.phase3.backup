//
//  YBUploadProgress.m
//  TestApp
//
//  Created by YB007 on 2020/7/7.
//  Copyright © 2020 Rookie. All rights reserved.
//

#import "YBUploadProgress.h"

#import "RKCircularProgress.h"
@interface YBUploadProgress()

@property(nonatomic,strong)UIImageView *bgIV;
@property(nonatomic,strong)RKCircularProgress *cirularPro;

@end

@implementation YBUploadProgress

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = [UIColor whiteColor];
        self.layer.cornerRadius = 5;
        self.clipsToBounds = YES;
        [self addSubview:self.bgIV];
        [self addSubview:self.cirularPro];
    }
    return self;
}

- (UIImageView *)bgIV {
    if (!_bgIV) {
        _bgIV = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
        _bgIV.contentMode = UIViewContentModeScaleAspectFill;
        _bgIV.clipsToBounds = YES;
    }
    return _bgIV;
}

- (RKCircularProgress *)cirularPro {
    if (!_cirularPro) {
        _cirularPro = [[RKCircularProgress alloc]initWithFrame:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
    }
    return _cirularPro;;
}

//
-(void)setProImg:(UIImage *)proImg{
    _proImg = proImg;
    if (_proImg) {
        _bgIV.image = _proImg;
    }
}
- (void)setProgress:(CGFloat)progress {
    _progress = progress;
    
    _cirularPro.progress = _progress;
    
}
@end
