//
//  RefundHeadView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/20.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RefundHeadView.h"

@implementation RefundHeadView

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame: frame];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    UIView *backView = [[UIView alloc]init];
    backView.frame = CGRectMake(0, 0, _window_width, 70);
    backView.backgroundColor = Pink_Cor;
    [self addSubview:backView];
    
    titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(12, 10, _window_width, 20);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor whiteColor];
    [backView addSubview:titleLb];
    
    timeLb = [[UILabel alloc]init];
    timeLb.frame = CGRectMake(12, titleLb.bottom+5, _window_width, 20);
    timeLb.font = [UIFont systemFontOfSize:14];
    timeLb.textColor = [UIColor whiteColor];
    [backView addSubview:timeLb];
    
    
    contentLb = [[UILabel alloc]init];
    contentLb.frame = CGRectMake(12, backView.bottom+15, _window_width-24, 40);
    contentLb.textColor = Normal_TextColor;
    contentLb.font = [UIFont systemFontOfSize:14];
    contentLb.lineBreakMode = NSLineBreakByWordWrapping;
    contentLb.numberOfLines = 0;
    contentLb.hidden = YES;
    [self addSubview:contentLb];

    agreeBtn = [UIButton buttonWithType:0];
    agreeBtn.frame = CGRectMake(_window_width-70, backView.bottom+10, 60, 24);
    agreeBtn.layer.cornerRadius = 12;
    agreeBtn.layer.borderColor = Pink_Cor.CGColor;
    agreeBtn.layer.borderWidth = 1;
    agreeBtn.layer.masksToBounds = YES;
    [agreeBtn setTitleColor:Pink_Cor forState:0];
    [agreeBtn setTitle:YZMsg(@"同意") forState:0];
    agreeBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [agreeBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:agreeBtn];
    
    refuseBtn = [UIButton buttonWithType:0];
    refuseBtn.frame = CGRectMake(agreeBtn.left-70, backView.bottom+10, 60, 24);
    refuseBtn.layer.cornerRadius = 12;
    refuseBtn.layer.borderColor = Normal_TextColor.CGColor;
    refuseBtn.layer.borderWidth = 1;
    refuseBtn.layer.masksToBounds = YES;
    [refuseBtn setTitleColor:Normal_TextColor forState:0];
    [refuseBtn setTitle:YZMsg(@"拒绝") forState:0];
    refuseBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [refuseBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

    [self addSubview:refuseBtn];
    
}
-(void)btnClick:(UIButton *)sender{
    if (self.clickEvent) {
        self.clickEvent(sender.titleLabel.text);
    }
}
//卖家端

-(void)setRefundData:(RefundDetailModel *)models{
    if ([models.can_handle isEqual:@"1"]) {
        agreeBtn.hidden = NO;
        refuseBtn.hidden = NO;
        contentLb.hidden = YES;
    }else{
        agreeBtn.hidden = YES;
        refuseBtn.hidden = YES;
        contentLb.hidden = NO;
    }
    titleLb.text = models.refund_status_name;
    timeLb.text = models.refund_status_time;
    contentLb.text = models.refund_status_desc;

}
@end
