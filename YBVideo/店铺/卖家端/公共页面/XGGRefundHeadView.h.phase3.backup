//
//  RefundHeadView.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/20.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "RefundDetailModel.h"
NS_ASSUME_NONNULL_BEGIN

typedef void(^btnClickEvent)(NSString *btnTitle);
@interface RefundHeadView : UIView
{
    UILabel *titleLb;
    UILabel *timeLb;
    UILabel *contentLb;
    
    UIButton *agreeBtn;
    UIButton *refuseBtn;
}
@property(nonatomic, copy)btnClickEvent clickEvent;

-(void)setRefundData:(RefundDetailModel *)models;

@end

NS_ASSUME_NONNULL_END
