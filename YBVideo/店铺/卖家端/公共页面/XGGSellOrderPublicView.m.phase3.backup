//
//  SellOrderPublicView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SellOrderPublicView.h"

@implementation SellOrderPublicView

-(instancetype)init{
    self = [super init];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    
    _orderImg = [[UIImageView alloc]init];
    _orderImg.backgroundColor = [UIColor lightGrayColor];
    _orderImg.contentMode = UIViewContentModeScaleAspectFill;
    _orderImg.layer.cornerRadius = 5;
    _orderImg.layer.masksToBounds = YES;
    [self addSubview:_orderImg];
    [_orderImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(12);
        make.top.equalTo(self).offset(10);
        make.width.height.mas_equalTo(90);
    }];
    
    _orderTitleLb = [[UILabel alloc]init];
    _orderTitleLb.textColor = [UIColor whiteColor];
    _orderTitleLb.font = [UIFont systemFontOfSize:14];
    _orderTitleLb.text = @"大猪蹄子";
    [self addSubview:_orderTitleLb];
    [_orderTitleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_orderImg.mas_right).offset(5);
        make.top.equalTo(_orderImg).offset(8);
        make.height.mas_equalTo(18);
    }];
    
    _orderPriceLb = [[UILabel alloc]init];
    _orderPriceLb.textColor = [UIColor whiteColor];
    _orderPriceLb.font = [UIFont systemFontOfSize:14];
    _orderPriceLb.text = @"¥188.00";
    _orderPriceLb.textAlignment = NSTextAlignmentRight;
    [self addSubview:_orderPriceLb];
    [_orderPriceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-10);
        make.centerY.height.equalTo(_orderTitleLb);
    }];
    _orderContentLb = [[UILabel alloc]init];
    _orderContentLb.textColor = Normal_TextColor;
    _orderContentLb.font = [UIFont systemFontOfSize:14];
    _orderContentLb.text = @"麻辣鲜香味";
    [self addSubview:_orderContentLb];
    [_orderContentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_orderTitleLb);
        make.top.equalTo(_orderTitleLb.mas_bottom).offset(5);
        make.height.equalTo(_orderTitleLb);
    }];
    
    _orderCountLb = [[UILabel alloc]init];
    _orderCountLb.textColor = Normal_TextColor;
    _orderCountLb.font = [UIFont systemFontOfSize:14];
    _orderCountLb.text = @"x1";
    _orderCountLb.textAlignment = NSTextAlignmentRight;
    [self addSubview:_orderCountLb];
    [_orderCountLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-10);
        make.centerY.height.equalTo(_orderContentLb);
    }];

    
    UILabel *yunfeiLb = [[UILabel alloc]init];
    yunfeiLb.textColor = Normal_TextColor;
    yunfeiLb.font = [UIFont systemFontOfSize:14];
    yunfeiLb.text = YZMsg(@"运费");
    [self addSubview:yunfeiLb];
    [yunfeiLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_orderImg);
        make.top.equalTo(_orderImg.mas_bottom).offset(10);
        make.height.mas_equalTo(18);
    }];

    UILabel *priceTitle = [[UILabel alloc]init];
    priceTitle.textColor = [UIColor whiteColor];
    priceTitle.font = [UIFont systemFontOfSize:14];
    priceTitle.text = YZMsg(@"需付款/实付款");
    [self addSubview:priceTitle];
    [priceTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(yunfeiLb);
        make.top.equalTo(yunfeiLb.mas_bottom).offset(10);
        make.height.mas_equalTo(18);
    }];

    _freightLb = [[UILabel alloc]init];
    _freightLb.textColor = Normal_TextColor;
    _freightLb.font = [UIFont systemFontOfSize:14];
    _freightLb.text = @"¥0.00";
    [self addSubview:_freightLb];
    [_freightLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-12);
        make.centerY.equalTo(yunfeiLb);
        make.height.mas_equalTo(18);

    }];
    
    _priceLb= [[UILabel alloc]init];
    _priceLb.textColor = Pink_Cor;
    _priceLb.font = [UIFont systemFontOfSize:14];
    _priceLb.text = @"¥188.00";
    [self addSubview:_priceLb];
    [_priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_freightLb);
        make.centerY.equalTo(priceTitle);
        make.height.mas_equalTo(18);

    }];

}
-(void)setOrderModel:(SellOrderDetailModel *)model
{
    [_orderImg sd_setImageWithURL:[NSURL URLWithString:model.spec_thumb_format]];
    _orderTitleLb.text = model.goods_name;
    _orderPriceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),model.price];
    _orderContentLb.text = model.spec_name;
    _orderCountLb.text = [NSString stringWithFormat:@"x%@",model.nums];
    _freightLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), model.postage];
    _priceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), model.total];
}
@end
