//
//  EditStockVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import "EditStockVC.h"
#import "StockView.h"
@interface EditStockVC ()
{
    UIScrollView *_backScroll;
}
@property (nonatomic, strong) NSMutableArray *specsArr;

@end

@implementation EditStockVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"价格与库存");
    
    _backScroll = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60)];
    _backScroll.backgroundColor = Normal_BackColor;
    [self.view addSubview:_backScroll];
    
    
    self.specsArr = [NSMutableArray array];
    for (int i = 0; i < self.model.specs.count; i ++) {
        StockView *stock = [[NSBundle mainBundle]loadNibNamed:@"StockView" owner:nil options:nil].lastObject;
        stock.frame = CGRectMake(0, i*160, _window_width, 160);
        stock.standardTitle.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"规格"),minstr([self.model.specs[i] valueForKey:@"spec_id"])];
        stock.nameLb.text = minstr([self.model.specs[i]valueForKey:@"spec_name"]);
        stock.countFeild.text = minstr([self.model.specs[i]valueForKey:@"spec_num"]);
        stock.priceFeild.text = minstr([self.model.specs[i]valueForKey:@"price"]);
        [_backScroll addSubview:stock];
        
        [self.specsArr addObject:stock];
    }
    
    
    [self addBottomView];
}
-(void)addBottomView{
    UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-60, _window_width, 60)];
    backView.backgroundColor = Normal_SubColor;
    [self.view addSubview:backView];
    
    UIButton *saveBtn = [UIButton buttonWithType:0];
    saveBtn.frame =CGRectMake(15, 10, _window_width-30, 40);
    [saveBtn setBackgroundColor:Pink_Cor];
    [saveBtn setTitle:YZMsg(@"保存") forState:0];
    [saveBtn setTitleColor:[UIColor whiteColor] forState:0];
    saveBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    saveBtn.layer.cornerRadius = 5;
    saveBtn.layer.masksToBounds = YES;
    [saveBtn addTarget:self action:@selector(saveBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:saveBtn];
    
}
-(void)saveBtnClick{
    NSMutableArray *currentSpecs = [NSMutableArray array];
    
    for (int i = 0; i < self.specsArr.count; i ++) {
        StockView *stock = self.specsArr[i];
        
        NSString *spec_id = [self.model.specs[i] valueForKey:@"spec_id"];
        NSString *spec_name = [self.model.specs[i] valueForKey:@"spec_name"];
        NSString *spec_num = stock.countFeild.text;
        NSString *price = stock.priceFeild.text;
        NSString *thumb = [self.model.specs[i] valueForKey:@"thumb"];
        
        NSMutableDictionary *specDic = [NSMutableDictionary dictionary];
        [specDic setValue:spec_id forKey:@"spec_id"];
        [specDic setValue:spec_name forKey:@"spec_name"];
        [specDic setValue:spec_num forKey:@"spec_num"];
        [specDic setValue:price forKey:@"price"];
        [specDic setValue:thumb forKey:@"thumb"];
        
        [currentSpecs addObject:specDic];
    }
    NSString *specsStr =  [self gs_jsonStringCompactFormatForNSArray:currentSpecs];
    
    
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":self.model.idStr,
                          @"specs":specsStr,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                            };
    [YBNetworking postWithUrl:@"Seller.upGoodsSpecs" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            [MBProgressHUD showError:msg];
            if (self.reloadEvent) {
                self.reloadEvent();
            }
            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}
- (NSString *)gs_jsonStringCompactFormatForNSArray:(NSArray *)arrJson {
    if (![arrJson isKindOfClass:[NSArray class]] || ![NSJSONSerialization isValidJSONObject:arrJson]) {
        return nil;
    }
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:arrJson options:0 error:nil];
    NSString *strJson = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    return strJson;
}

-(void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}
@end
