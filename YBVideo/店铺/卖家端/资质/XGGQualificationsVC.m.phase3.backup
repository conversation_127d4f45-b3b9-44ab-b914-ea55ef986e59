//
//  QualificationsVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/11.
//  Copyright © 2020 cat. All rights reserved.
//

#import "QualificationsVC.h"

@interface QualificationsVC ()

@end

@implementation QualificationsVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"资质证明");
    [self createUI];
}
-(void)createUI{
    UILabel *contentLb = [[UILabel alloc]init];
    contentLb.font = [UIFont systemFontOfSize:14];
    contentLb.textColor = [UIColor whiteColor];
    contentLb.numberOfLines = 0;
    contentLb.lineBreakMode = NSLineBreakByWordWrapping;
    contentLb.text = minstr([self.infos valueForKey:@"certificate_desc"]);
    [self.view addSubview:contentLb];
    [contentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(15);
        make.right.equalTo(self.view).offset(-15);
        make.top.equalTo(self.view).offset(64+statusbarHeight+20);
    }];
    
    UIImageView *img = [[UIImageView alloc]init];
    img.contentMode = UIViewContentModeScaleAspectFit;
    NSString *urls = minstr([self.infos valueForKey:@"certificate"]);
    [img sd_setImageWithURL:[NSURL URLWithString:urls]];
    [self.view addSubview:img];
    [img mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(300);
        make.height.mas_equalTo(425);
        make.top.equalTo(contentLb.mas_bottom).offset(10);
        make.centerX.equalTo(self.view.mas_centerX);
    }];
    
}

@end
