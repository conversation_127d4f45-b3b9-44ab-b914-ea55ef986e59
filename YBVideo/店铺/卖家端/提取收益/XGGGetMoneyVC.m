//
//  GetMoneyVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import "GetMoneyVC.h"
//#import "profitTypeVC.h"
#import "YBGetTypeListVC.h"
#define kMaxLength 10

@interface GetMoneyVC ()
{
    UILabel *allVotesL;
    UILabel *nowVotesL;
    UITextField *votesT;

    UILabel *typeLabel;
    int cash_rate;
    UIButton *inputBtn;
    UILabel *tipsLabel;
    NSDictionary *typeDic;
    UIImageView *seletTypeImgView;

}
@end

@implementation GetMoneyVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = Normal_Color;
    [self navtion];
    [self creatUI];

    
}
-(void)navtion{
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64 + statusbarHeight)];
    navtion.backgroundColor =NaviBg_Color;
    UILabel *label = [[UILabel alloc]init];
    label.text = YZMsg(@"提取收益");
    [label setFont:NaviTitle_Font];
    label.textColor = NaviTitle_Color;
    label.frame = CGRectMake(0, statusbarHeight,_window_width,84);
    label.textAlignment = NSTextAlignmentCenter;
    [navtion addSubview:label];
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    UIButton *bigBTN = [[UIButton alloc]initWithFrame:CGRectMake(0, statusbarHeight, _window_width/2, 64)];
    [bigBTN addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:bigBTN];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:returnBtn];
    UIButton *btnttttt = [UIButton buttonWithType:UIButtonTypeCustom];
    btnttttt.backgroundColor = [UIColor clearColor];
    [btnttttt addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    btnttttt.frame = CGRectMake(0,0,100,64);
    [navtion addSubview:btnttttt];
    [PublicObj  lineViewWithFrame:CGRectMake(0, navtion.height-1, _window_width, 1) andColor:Normal_SubColor andView:navtion];
    [self.view addSubview:navtion];
}
-(void)doReturn{
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)tapClick{
    [votesT resignFirstResponder];
}

- (void)creatUI{
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tapClick)];
    [self.view addGestureRecognizer:tap];
    
    //黄色背景图
    UIImageView *backImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width*0.04, 64+statusbarHeight+10, _window_width*0.92, _window_width*0.92*24/69)];
    backImgView.image = [UIImage imageNamed:@"profitBg"];
    [self.view addSubview:backImgView];
    
    for (int i = 0; i < 4; i++) {
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(backImgView.width/2*(i%2), backImgView.height/4*(i/2+1), backImgView.width/2, backImgView.height/4)];
        label.textAlignment = NSTextAlignmentCenter;
        label.textColor = [UIColor whiteColor];
        if (i<2) {
            label.font = [UIFont systemFontOfSize:15];
            if (i == 0) {
                label.text = YZMsg(@"累计收入");
            }else{
                label.text = YZMsg(@"可提取金额");
                [PublicObj  lineViewWithFrame:CGRectMake(backImgView.width/2-0.5, backImgView.height/4, 1, backImgView.height/2) andColor:[UIColor whiteColor] andView:backImgView];
            }
        }else{
            label.font = [UIFont boldSystemFontOfSize:22];
            if (i == 2) {
                label.text = minstr([self.moneyDic valueForKey:@"balance_total"]);
                allVotesL = label;
            }else{
                label.text = minstr([self.moneyDic valueForKey:@"balance"]);
                nowVotesL = label;
            }
        }
        [backImgView addSubview:label];
    }
    //输入提现金额的视图
    UIView *textView = [[UIView alloc]initWithFrame:CGRectMake(backImgView.left, backImgView.bottom+10, backImgView.width, backImgView.height/2)];
    textView.backgroundColor = Normal_SubColor;
    textView.layer.cornerRadius = 5.0;
    textView.layer.masksToBounds = YES;
    [self.view addSubview:textView];
    NSArray *arr = @[YZMsg(@"输入提取金额")];
    for (int i = 0; i<1; i++) {
        CGFloat labelW = [PublicObj  widthOfString:arr[i] andFont:[UIFont systemFontOfSize:15] andHeight:textView.height];
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(textView.width*0.05, textView.height/2*i, labelW+20, textView.height)];
        label.textColor = [UIColor grayColor];
        label.font = [UIFont systemFontOfSize:15];
        label.text = arr[i];
        [textView addSubview:label];
        
        votesT = [[UITextField alloc]initWithFrame:CGRectMake(label.right, 0, textView.width*0.95-label.right, textView.height)];
        votesT.textColor = Pink_Cor;
        votesT.font = [UIFont boldSystemFontOfSize:17];
        votesT.placeholder = @"0";
        votesT.keyboardType = UIKeyboardTypeNumberPad;
        [textView addSubview:votesT];
        [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(textFieldEditChanged:)
        name:@"UITextFieldTextDidChangeNotification" object:votesT];

    }
    
    //选择提现账户
    
    UIView *typeView = [[UIView alloc]initWithFrame:CGRectMake(backImgView.left, textView.bottom+10, backImgView.width, 50)];
    typeView.backgroundColor = Normal_SubColor;
    typeView.layer.cornerRadius = 5.0;
    typeView.layer.masksToBounds = YES;
    [self.view addSubview:typeView];
    typeLabel = [[UILabel alloc]initWithFrame:CGRectMake(textView.width*0.05, 0, typeView.width*0.95-40, 50)];
    typeLabel.textColor = [UIColor grayColor];
    typeLabel.font = [UIFont systemFontOfSize:15];
    typeLabel.text = YZMsg(@"请选择提现账户");
    [typeView addSubview:typeLabel];
    seletTypeImgView = [[UIImageView alloc]initWithFrame:CGRectMake(typeLabel.left, 15, 20, 20)];
    seletTypeImgView.hidden = YES;
    [typeView addSubview:seletTypeImgView];
    
    UIImageView *rightImgView = [[UIImageView alloc]initWithFrame:CGRectMake(typeView.width-30, 18, 14, 14)];
    rightImgView.image = [UIImage imageNamed:@"person_right"];
    rightImgView.userInteractionEnabled = YES;
    [typeView addSubview:rightImgView];

    UIButton *btn = [UIButton buttonWithType:0];
    btn.frame = CGRectMake(0, 0, typeView.width, typeView.height);
    [btn addTarget:self action:@selector(selectPayType) forControlEvents:UIControlEventTouchUpInside];
    [typeView addSubview:btn];
    
    inputBtn = [UIButton buttonWithType:0];
    inputBtn.frame = CGRectMake(15, typeView.bottom + 50, _window_width - 30, 40);
    [inputBtn setBackgroundColor: Pink_Cor];
    [inputBtn setTitle:YZMsg(@"立即提现") forState:0];
    [inputBtn addTarget:self action:@selector(inputBtnClick) forControlEvents:UIControlEventTouchUpInside];
    inputBtn.titleLabel.font = [UIFont boldSystemFontOfSize:14];
    inputBtn.layer.cornerRadius = 20;
    inputBtn.layer.masksToBounds = YES;
    [self.view addSubview:inputBtn];
    
    tipsLabel = [[UILabel alloc]initWithFrame:CGRectMake(inputBtn.left+15, inputBtn.bottom + 15, inputBtn.width-30, 100)];
    tipsLabel.font = [UIFont systemFontOfSize:11];
    tipsLabel.textColor = RGB_COLOR(@"#666666", 1);
    tipsLabel.numberOfLines = 0;
    [self.view addSubview:tipsLabel];
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangeMoenyLabelValue) name:UITextFieldTextDidChangeNotification object:nil];

}
//选择z提现方式
- (void)selectPayType{
    YBGetTypeListVC *vc = [[YBGetTypeListVC alloc]init];
    if ([typeDic allKeys].count>0) {
        vc.selectID = minstr([typeDic valueForKey:@"id"]);
    }else{
        vc.selectID = YZMsg(@"未选择提现方式");
    }
    YBWeakSelf;
    vc.block = ^(NSDictionary * _Nonnull dic) {
        typeDic = dic;
        if ([dic allKeys].count>0) {
            [Config saveCashAccount:typeDic];
        }
        [weakSelf updateShowAccount:dic];

    };
    [self.navigationController pushViewController:vc animated:YES];


    //zl----待修改
//    profitTypeVC *vc = [[profitTypeVC alloc]init];
//    if (typeDic) {
//        vc.selectID = minstr([typeDic valueForKey:@"id"]);
//    }else{
//        vc.selectID = YZMsg(@"未选择提现方式");
//    }
//    vc.block = ^(NSDictionary * _Nonnull dic) {
//        typeDic = dic;
//        seletTypeImgView.hidden = NO;
//        typeLabel.x = seletTypeImgView.right + 5;
//        int type = [minstr([dic valueForKey:@"type"]) intValue];
//        switch (type) {
//            case 1:
//                seletTypeImgView.image = [UIImage imageNamed:@"profit_alipay"];
//                typeLabel.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
//                break;
//            case 2:
//                seletTypeImgView.image = [UIImage imageNamed:@"profit_wx"];
//                typeLabel.text = [NSString stringWithFormat:@"%@",minstr([dic valueForKey:@"account"])];
//
//                break;
//            case 3:
//                seletTypeImgView.image = [UIImage imageNamed:@"profit_card"];
//                typeLabel.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
//                break;
//
//            default:
//                break;
//        }
//
//    };
//    [self.navigationController pushViewController:vc animated:YES];
}
-(void)updateShowAccount:(NSDictionary *)dic {
    
    seletTypeImgView.hidden = NO;
    typeLabel.x = seletTypeImgView.right + 5;
    typeLabel.textColor = [UIColor whiteColor];
    int type = [minstr([dic valueForKey:@"type"]) intValue];
    switch (type) {
        case 1:
            seletTypeImgView.image = [UIImage imageNamed:@"profit_zff"];
            typeLabel.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
            break;
        case 2:
            seletTypeImgView.image = [UIImage imageNamed:@"profit_wx"];
            typeLabel.text = [NSString stringWithFormat:@"%@",minstr([dic valueForKey:@"account"])];

            break;
        case 3:
            seletTypeImgView.image = [UIImage imageNamed:@"profit_card"];
            typeLabel.text = [NSString stringWithFormat:@"%@(%@)",minstr([dic valueForKey:@"account"]),minstr([dic valueForKey:@"name"])];
            break;
            
        default:{
            seletTypeImgView.hidden = YES;
            typeLabel.x = seletTypeImgView.left;
            typeLabel.text = YZMsg(@"请选择提现账户");
            typeLabel.textColor = RGB_COLOR(@"#646464", 1);
        }break;
    }
    
}

//提交申请
- (void)inputBtnClick{
    if(!typeDic){
        [MBProgressHUD showError:YZMsg(@"请填写提现账户")];
        return;
    }
    
    NSString *url = [purl stringByAppendingFormat:@"?service=User.setShopCash"];

    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"accountid":minstr([typeDic valueForKey:@"id"]),@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"accountid":minstr([typeDic valueForKey:@"id"]),
                          @"money":votesT.text,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign,
                          };

    [YBNetworking postWithUrl:@"User.setShopCash" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            [MBProgressHUD showError:msg];
            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];
}
- (void)ChangeMoenyLabelValue{
    if ([[NSString stringWithFormat:@"%lld",[votesT.text longLongValue]/cash_rate] integerValue] > 0) {
        inputBtn.userInteractionEnabled = YES;
        [inputBtn setBackgroundColor:Pink_Cor];
    }else{
        inputBtn.userInteractionEnabled = NO;
        [inputBtn setBackgroundColor:RGB_COLOR(@"#dcdcdc", 1)];
    }
}
-(void)textFieldEditChanged:(NSNotification *)obj{
UITextField *textField = (UITextField *)obj.object;
NSString *toBeString = textField.text;
NSString *lang = [[UITextInputMode currentInputMode] primaryLanguage]; // 键盘输入模式
if ([lang isEqualToString:@"zh-Hans"]) { // 简体中文输入，包括简体拼音，健体五笔，简体手写
   UITextRange *selectedRange = [textField markedTextRange];       //获取高亮部分
   UITextPosition *position = [textField positionFromPosition:selectedRange.start offset:0];
   // 没有高亮选择的字，则对已输入的文字进行字数统计和限制
   if (!position) {
        if (toBeString.length > kMaxLength) {
            textField.text = [toBeString substringToIndex:kMaxLength];
        }
    }       // 有高亮选择的字符串，则暂不对文字进行统计和限制
    else{
     }
   }   // 中文输入法以外的直接对其统计限制即可，不考虑其他语种情况   else{
    if (toBeString.length > kMaxLength) {
        textField.text = [toBeString substringToIndex:kMaxLength];
    }
}

@end
