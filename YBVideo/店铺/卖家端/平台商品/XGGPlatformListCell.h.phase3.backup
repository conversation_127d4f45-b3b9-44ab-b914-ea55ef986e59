//
//  PlatformListCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/12/1.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^reloadDataDic)(NSDictionary *dic, NSString *statuStr);

@interface PlatformListCell : UICollectionViewCell
@property (weak, nonatomic) IBOutlet UIImageView *thumbImg;
@property (weak, nonatomic) IBOutlet UILabel *titleLb;
@property (weak, nonatomic) IBOutlet UIButton *commissionBtn;
@property (weak, nonatomic) IBOutlet UIButton *addBtn;
@property (weak, nonatomic) IBOutlet UILabel *priceLb;
@property (weak, nonatomic) IBOutlet UILabel *saleLb;
@property (nonatomic, strong)NSDictionary *dataDic;

@property (nonatomic, copy)reloadDataDic reloadEvent;
@end

