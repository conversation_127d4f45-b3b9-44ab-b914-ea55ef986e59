//
//  PlatformGoodsVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/12/1.
//  Copyright © 2020 cat. All rights reserved.
//

#import "PlatformGoodsVC.h"
#import "PlatformListCell.h"
#import "OutsideGoodsDetailVC.h"
#import "CommodityDetailVC.h"
@interface PlatformGoodsVC ()<UICollectionViewDelegate,UICollectionViewDataSource>
{
    int page;
}
@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,strong)NSMutableArray *goodsArray;
@end

@implementation PlatformGoodsVC

-(void)initData{
    self.goodsArray = [NSMutableArray array];
    page = 1;
}
-(void)pullInternet{
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign,
                          @"p":@(page)
                          };
    [YBNetworking postWithUrl:@"Seller.getPlatformGoodsLists" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
        if (code == 0) {
            NSArray *infos = info;
            if (page == 1) {
                [self.goodsArray removeAllObjects];

            }
            [self.goodsArray addObjectsFromArray:infos];
            if (self.goodsArray.count < 1) {
                [PublicView showImgNoData:_collectionView name:@"shop_无数据" text:YZMsg(@"你还没有相关商品")];
                [self.collectionView reloadData];
                return ;
            }else{
                [PublicView hiddenImgNoData:_collectionView];
            }

            [self.collectionView reloadData];
        }

          } Fail:^(id fail) {
              [_collectionView.mj_header endRefreshing];
              [_collectionView.mj_footer endRefreshing];

          }];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"平台商品");
    [self initData];
    [self createCollectionView];
}
-(void)createCollectionView{
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionVertical;
        flow.itemSize = CGSizeMake((_window_width-21)/2, (_window_width-21)/2 + 90);
        flow.minimumLineSpacing = 7;
        flow.minimumInteritemSpacing = 7;
        flow.sectionInset = UIEdgeInsetsMake(7, 7, 7, 7);

        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) collectionViewLayout:flow];
        _collectionView.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
        _collectionView.delegate   = self;
        _collectionView.dataSource = self;
        [_collectionView registerNib:[UINib nibWithNibName:@"PlatformListCell" bundle:nil] forCellWithReuseIdentifier:@"PlatformListCeLL"];
        [self.view addSubview:_collectionView];

        if (@available(iOS 11.0, *)) {
            _collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        } else {
            self.automaticallyAdjustsScrollViewInsets = NO;
        }
        
        _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            page = 1;
            [self pullInternet];
        }];
        _collectionView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            page ++;
            [self pullInternet];
        }];
        [self pullInternet];
}

-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.goodsArray.count;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];

    NSDictionary *subDic = self.goodsArray[indexPath.row];
    
    [PublicObj checkGoodsExistenceWithID:minstr([subDic valueForKey:@"id"]) Existence:^(int code, NSString *msg) {
        if (code == 0 ) {
                CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                detail.goodsID = minstr([subDic valueForKey:@"id"]);
                detail.liveUid = @"0";
                [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];
        }else{
            [MBProgressHUD showError:msg];

        }
    }];


}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    PlatformListCell *cell = (PlatformListCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"PlatformListCeLL" forIndexPath:indexPath];
    cell.dataDic = self.goodsArray[indexPath.row];
    cell.reloadEvent = ^(NSDictionary *dic, NSString *statuStr) {
        for (int i = 0; i < self.goodsArray.count; i ++) {
            NSMutableDictionary *onedic = [NSMutableDictionary dictionaryWithDictionary:self.goodsArray[i]];
            if ([[onedic valueForKey:@"id"] isEqual:[dic valueForKey:@"id"]]) {
                NSDictionary *replayDic = @{@"isadd":statuStr};
                [onedic addEntriesFromDictionary:replayDic];
                [self.goodsArray replaceObjectAtIndex:i withObject:onedic];

            }

        }

    };
    return cell;
}

@end
