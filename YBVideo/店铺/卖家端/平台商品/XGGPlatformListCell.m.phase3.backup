//
//  PlatformListCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/12/1.
//  Copyright © 2020 cat. All rights reserved.
//

#import "PlatformListCell.h"

@implementation PlatformListCell

- (void)awakeFromNib {
    [super awakeFromNib];
   UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.commissionBtn.bounds byRoundingCorners:UIRectCornerBottomLeft | UIRectCornerTopLeft cornerRadii:CGSizeMake(10, 10)];
   CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
   maskLayer.frame = self.commissionBtn.bounds;
   maskLayer.path = maskPath.CGPath;
   self.commissionBtn.layer.mask = maskLayer;

   UIBezierPath *addBtnmaskPath = [UIBezierPath bezierPathWithRoundedRect:self.addBtn.bounds byRoundingCorners:UIRectCornerBottomRight | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
   CAShapeLayer *addMaskLayer = [[CAShapeLayer alloc] init];
   addMaskLayer.frame = self.addBtn.bounds;
   addMaskLayer.path = addBtnmaskPath.CGPath;
   self.addBtn.layer.mask = addMaskLayer;

}
-(void)setDataDic:(NSDictionary *)dataDic
{
    _dataDic = dataDic;
    self.titleLb.text = minstr([dataDic valueForKey:@"name"]);
    self.priceLb.text = [NSString stringWithFormat:@"¥%@",minstr([dataDic valueForKey:@"price"])];
    self.saleLb.text =[NSString stringWithFormat:YZMsg(@"已售%@件"),minstr([dataDic valueForKey:@"sale_nums"])];
    [self.commissionBtn setTitle:[NSString stringWithFormat:YZMsg(@"佣 ¥%@"),minstr([dataDic valueForKey:@"commission"])] forState:0];
    [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"thumb"])]];

    NSString *isAdd = minstr([dataDic valueForKey:@"isadd"]);
    if ([isAdd isEqual:@"1"]) {
        [self.addBtn setTitle:YZMsg(@"已添加") forState:0];
        [self.addBtn setTitleColor:RGB(150,150,150) forState:0];
        [self.addBtn setBackgroundColor:RGB(243, 243, 243)];
    }else{
        [self.addBtn setTitle:YZMsg(@"添加") forState:0];
        [self.addBtn setTitleColor:[UIColor whiteColor] forState:0];
        [self.addBtn setBackgroundColor:Pink_Cor];

    }
}
- (IBAction)addBtnClick:(UIButton *)sender {
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign,
                          @"goodsid":minstr([_dataDic valueForKey:@"id"])
                          };

    [YBNetworking postWithUrl:@"Seller.setPlatformGoods" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSDictionary *infos = [info firstObject];
            if ([minstr([infos valueForKey:@"status"]) isEqual:@"1"]) {
                [self.addBtn setTitle:YZMsg(@"已添加") forState:0];
                [self.addBtn setTitleColor:RGB(150,150,150) forState:0];
                [self.addBtn setBackgroundColor:RGB(243, 243, 243)];

            }else{
                [self.addBtn setTitle:YZMsg(@"添加") forState:0];
                [self.addBtn setTitleColor:[UIColor whiteColor] forState:0];
                [self.addBtn setBackgroundColor:Pink_Cor];

            }
            if (self.reloadEvent) {
                self.reloadEvent(_dataDic,minstr([infos valueForKey:@"status"]));
            }

        }
        [MBProgressHUD showError:msg];

        } Fail:^(id fail) {
            
        }];


}
@end
