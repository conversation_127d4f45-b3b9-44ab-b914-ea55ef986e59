//
//  ShopInfoVC.m
//  YBVideo
//
//  Created by ybRRR on 2021/3/8.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ShopInfoVC.h"
#import "QualificationsVC.h"

@interface ShopInfoVC ()<UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong)UITableView *shopTable;
@end

@implementation ShopInfoVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"小店详情");
    [self.view addSubview:self.shopTable];
}

-(UITableView *)shopTable{
    if (!_shopTable) {
        _shopTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
        _shopTable.delegate = self;
        _shopTable.dataSource = self;
        _shopTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _shopTable.backgroundColor = Normal_SubColor;
        _shopTable.tableFooterView = [UIView new];
    }
    return _shopTable;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 4;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 50;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"shopcell"];
    cell.backgroundColor = Normal_SubColor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;

    if (indexPath.row == 0) {
        cell.textLabel.text = YZMsg(@"基础信息");
        cell.textLabel.font = [UIFont systemFontOfSize:16];
        cell.textLabel.textColor = Normal_TextColor;
    }else{
        cell.textLabel.font = [UIFont systemFontOfSize:14];
        cell.textLabel.textColor = Normal_TextColor;
        
        UILabel *statuLb = [[UILabel alloc]init];
        statuLb.font = [UIFont systemFontOfSize:14];
        statuLb.textColor = [UIColor whiteColor];
        [cell addSubview:statuLb];
        [statuLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(cell).offset(-20);
            make.centerY.equalTo(cell);
            make.height.mas_equalTo(18);
        }];
        UIImageView *rightImg = [[UIImageView alloc]init];
        rightImg.image = [UIImage imageNamed:@"shop_right"];
        [cell addSubview:rightImg];
        [rightImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(statuLb.mas_right).offset(2);
            make.centerY.equalTo(statuLb);
            make.width.height.mas_equalTo(15);
        }];
        if (indexPath.row == 1) {
            cell.textLabel.text =YZMsg(@"资质证明") ;
            statuLb.text = YZMsg(@"查看资质证明");
            rightImg.hidden = NO;
        }else if (indexPath.row == 2){
            cell.textLabel.text = YZMsg(@"实名认证");
            statuLb.text = YZMsg(@"已认证");
            rightImg.hidden = YES;
        }else{
            cell.textLabel.text = YZMsg(@"店铺保证金");
            statuLb.text = YZMsg(@"已缴纳");
            rightImg.hidden = YES;
        }
    }
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    if (indexPath.row == 1) {
        QualificationsVC *quaVC = [[QualificationsVC alloc]init];
        quaVC.infos = self.shopInfoDic;
        [[XGGAppDelegate sharedAppDelegate]pushViewController:quaVC animated:YES];
    }
}

@end
