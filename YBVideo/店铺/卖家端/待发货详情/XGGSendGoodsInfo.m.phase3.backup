//
//  SendGoodsInfo.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SendGoodsInfo.h"

@implementation SendGoodsInfo

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        [self createUI];
    }
    return self;
}
-(void)createUI{
    orderLb = [[UILabel alloc]initWithFrame:CGRectMake(12, 10, _window_width-24, 30)];
    orderLb.font = [UIFont systemFontOfSize:14];
    orderLb.textColor = Normal_TextColor;
    [self addSubview:orderLb];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, orderLb.bottom+10, _window_width, 1) andColor:Line_Cor andView:self];
    
    UIView *fieldBack = [[UIView alloc]init];
    fieldBack.frame = CGRectMake(12, orderLb.bottom+20, _window_width-24, 90);
    fieldBack.layer.cornerRadius = 5;
    fieldBack.layer.borderColor = Line_Cor.CGColor;
    fieldBack.layer.borderWidth = 1;
    fieldBack.layer.masksToBounds = YES;
    [self addSubview:fieldBack];
    
    numField = [[UITextField alloc]initWithFrame:CGRectMake(10, fieldBack.height/4-15, fieldBack.width-20, 30)];
    numField.borderStyle = UITextBorderStyleNone;
//    numField.placeholder = YZMsg(@"填写物流单号");
    NSAttributedString *attrString = [[NSAttributedString alloc] initWithString: YZMsg(@"填写物流单号") attributes:
    @{NSForegroundColorAttributeName:Normal_TextColor,
                 NSFontAttributeName:numField.font
         }];
    numField.attributedPlaceholder = attrString;

    numField.font = [UIFont systemFontOfSize:14];
    numField.textColor = [UIColor whiteColor];
    numField.tag = 30000;
    [fieldBack addSubview:numField];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, fieldBack.height/2, fieldBack.width, 1) andColor:Line_Cor andView:fieldBack];
    
    companyField = [[UITextField alloc]initWithFrame:CGRectMake(10, (fieldBack.height/2+fieldBack.height/4)-15, fieldBack.width-20, 30)];
    companyField.borderStyle = UITextBorderStyleNone;
//    companyField.placeholder = YZMsg(@"选择物流公司");
    NSAttributedString *attrStrings = [[NSAttributedString alloc] initWithString:YZMsg(@"选择物流公司") attributes:
    @{NSForegroundColorAttributeName:Normal_TextColor,
                 NSFontAttributeName:companyField.font
         }];
    companyField.attributedPlaceholder = attrStrings;

    companyField.font = [UIFont systemFontOfSize:14];
    companyField.tag = 30001;
    companyField.delegate = self;
    companyField.textColor = [UIColor whiteColor];
    [fieldBack addSubview:companyField];
    
    _accessBtn = [UIButton buttonWithType:0];
    _accessBtn.frame = CGRectMake(companyField.width-20, 5, 20, 20);
    [_accessBtn setImage:[UIImage imageNamed:@"order_下拉"] forState:0];
    [_accessBtn setImage:[UIImage imageNamed:@"order_上拉"] forState:UIControlStateSelected];
    [companyField addSubview:_accessBtn];
    
    UIButton *companyBtn = [UIButton buttonWithType:0];
    companyBtn.frame = CGRectMake(0, 0, companyField.width, companyField.height);
    [companyBtn addTarget:self action:@selector(companyBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [companyField addSubview:companyBtn];
    
    
    UIButton *sureBtn = [UIButton buttonWithType:0];
    sureBtn.frame = CGRectMake(12, fieldBack.bottom+10, _window_width-24, 40);
    [sureBtn setBackgroundColor:Pink_Cor];
    [sureBtn setTitle:YZMsg(@"确认发货") forState:0];
    sureBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [sureBtn setTitleColor:[UIColor whiteColor] forState:0];
    sureBtn.layer.cornerRadius = 5;
    sureBtn.layer.masksToBounds  = YES;
    [sureBtn addTarget:self action:@selector(sendExpressClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:sureBtn];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, sureBtn.bottom+10, self.width, 6) andColor:Normal_BackColor andView:self];

}
-(void)setExpress:(NSDictionary *)dic{
    expressDic = dic;
    companyField.text = minstr([dic valueForKey:@"express_name"]);
    
}
-(void)setData:(NSDictionary *)dic{
    orderLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"订单编号:"), minstr([dic valueForKey:@"num"])];
}
-(void)companyBtnClick{
    if (self.selEvent) {
        self.selEvent();
    }
}
-(BOOL)textFieldShouldBeginEditing:(UITextField *)textField{
    if (textField.tag == 30001) {
        return NO;
    }
    return YES;

}
-(void)sendExpressClick{
    BOOL expressBool = [PublicObj checkNull:minstr([expressDic valueForKey:@"id"])];
    NSString *expressID = expressBool ? @"" :minstr([expressDic valueForKey:@"id"]);
    NSLog(@"sssssss0000:%@",expressID);
    [self.delegate sendGoodsExpressid:expressID Number:numField.text];
}
@end
