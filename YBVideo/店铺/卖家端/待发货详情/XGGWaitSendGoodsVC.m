//
//  WaitSendGoodsVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import "WaitSendGoodsVC.h"
#import "SendGoodsInfo.h"
#import "OrderPublicView.h"
#import "SellOrderPublicView.h"
#import "SellOrderDetailModel.h"
#import "LogisticsCell.h"
@interface WaitSendGoodsVC ()<UITableViewDelegate, UITableViewDataSource,SendGoodsInfoDelegate,UIGestureRecognizerDelegate>
{
    SendGoodsInfo *logisticsView;
    
}
@property (nonatomic, strong)SellOrderDetailModel *model;
@property (nonatomic, strong)UITableView *logisticsTable;
@property (nonatomic, strong)NSArray *dataArr;
@property (nonatomic, assign)BOOL isShowLogistics;
@end

@implementation WaitSendGoodsVC


#pragma mark-----卖家获取商品订单详情-------
-(void)getGoodsOrderInfo{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Seller.getGoodsOrderInfo"];
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.orderModel.idStr,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                            };
    
    [YBNetworking postWithUrl:@"Seller.getGoodsOrderInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            self.model  = [SellOrderDetailModel modelWithDic:infos];
            [self createUI];
        }else{
            [MBProgressHUD showError:msg];
        }
        } Fail:^(id fail) {
            
     }];
}
#pragma mark-----获取物流公司列表-------
-(void)getExpressList{
    NSString *url = [purl stringByAppendingFormat:@"?service=Seller.getExpressList"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken]
    };
    
    [YBNetworking postWithUrl:@"Seller.getExpressList" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSArray *infos = info;
            self.dataArr = infos;
            self.logisticsTable.hidden = NO;
            [self.logisticsTable reloadData];
            
            logisticsView.accessBtn.selected = YES;
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"订单详情");
    self.dataArr = [NSArray array];
    [self getGoodsOrderInfo];
}
-(void)createUI{
    
    backScroll = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight)];
    backScroll.backgroundColor = Normal_SubColor;
    [self.view addSubview:backScroll];
    
    UITapGestureRecognizer *taps = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideKeyBoard)];
    taps.delegate = self;
    [backScroll addGestureRecognizer:taps];
    
    UIView *headBack = [[UIView alloc]init];
    headBack.frame = CGRectMake(0, 0, _window_width, 90);
//    headBack.backgroundColor = Normal_Color;
    CAGradientLayer *gl = [CAGradientLayer layer];
    gl.frame = CGRectMake(0, 0, _window_width, 90);
    gl.colors = @[
    (__bridge id)[UIColor colorWithRed:252/255.0 green:88/255.0 blue:192/255.0 alpha:1.00].CGColor,
    (__bridge id)[UIColor colorWithRed:252/255.0 green:64/255.0 blue:140/255.0 alpha:1.00].CGColor,
    ];
    gl.locations = @[@(0),@(1)];
    [headBack.layer addSublayer:gl];
    headBack.backgroundColor = [UIColor colorWithRed:234/255.0 green:55/255.0 blue:127/255.0 alpha:1.00];
    [backScroll addSubview:headBack];
    
    UILabel *titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(12, 12, _window_width-24, 20);
    titleLb.font = [UIFont systemFontOfSize:15];
    titleLb.text = self.model.status_name;
    titleLb.textColor = [UIColor whiteColor];
    [headBack addSubview:titleLb];
    
    UILabel *infoLb = [[UILabel alloc]init];
    infoLb.font = [UIFont systemFontOfSize:13];
    infoLb.textColor = [UIColor whiteColor];
    infoLb.frame = CGRectMake(12, titleLb.bottom, _window_width-24, 40);
    infoLb.numberOfLines = 0;
    infoLb.lineBreakMode  =NSLineBreakByWordWrapping;
    infoLb.text = self.model.status_desc;;
    [headBack addSubview:infoLb];
    
    YBWeakSelf;
    logisticsView = [[SendGoodsInfo alloc]initWithFrame:CGRectMake(0, headBack.bottom, _window_width, 220)];
    NSDictionary *dic = @{@"num":self.model.orderno};
    logisticsView.delegate = self;
    [logisticsView setData:dic];
    logisticsView.selEvent = ^{
        weakSelf.isShowLogistics = !weakSelf.isShowLogistics;
        if (weakSelf.isShowLogistics) {
            [weakSelf getExpressList];
            
        }else{
            weakSelf.logisticsTable.hidden= YES;
            logisticsView.accessBtn.selected = NO;
        }
    };
    [backScroll addSubview:logisticsView];
    
    OrderPublicView *addressView = [[OrderPublicView alloc]init];
    addressView.frame = CGRectMake(0, logisticsView.bottom, _window_width, 60);
    [addressView setSellOrderModelData:self.model AndIndex:1];
    [backScroll addSubview:addressView];
    
    UIButton *copyBtn = [UIButton buttonWithType:0];
    copyBtn.frame =CGRectMake(_window_width-100, addressView.bottom, 83, 26);
    copyBtn.layer.cornerRadius = 5;
    copyBtn.layer.borderColor = [UIColor grayColor].CGColor;
    copyBtn.layer.borderWidth = 1;
    copyBtn.layer.masksToBounds = YES;
    [copyBtn setTitle:YZMsg(@"复制地址") forState:0];
    copyBtn.titleLabel.font = [UIFont systemFontOfSize:10];
    [copyBtn setTitleColor:[UIColor grayColor] forState:0];
    [copyBtn addTarget:self action:@selector(copyAddress) forControlEvents:UIControlEventTouchUpInside];
    [backScroll addSubview:copyBtn];
    
    OrderPublicView *msgView = [[OrderPublicView alloc]init];
    msgView.frame = CGRectMake(0, copyBtn.bottom, _window_width, 60);
    [msgView setSellOrderModelData:self.model AndIndex:2];
    msgView.lineLb.hidden = NO;
    [backScroll addSubview:msgView];

    CGFloat textHeight = [PublicObj heightOfString:self.model.message andFont:[UIFont systemFontOfSize:12] andWidth:_window_width-50];
    msgView.size = CGSizeMake(_window_width, 50+textHeight);
    
    
    SellOrderPublicView *infos = [[SellOrderPublicView alloc]init];
    infos.frame = CGRectMake(0, msgView.bottom, _window_width, 200);
    [infos setOrderModel:self.model];
    [backScroll addSubview:infos];
    
    backScroll.contentSize = CGSizeMake(_window_width, headBack.height+logisticsView.height+addressView.height+copyBtn.height+msgView.height+infos.height+20);
    
    [backScroll addSubview:self.logisticsTable];
}
-(UITableView *)logisticsTable{
    if (!_logisticsTable) {
        _logisticsTable = [[UITableView alloc]initWithFrame:CGRectMake(15, logisticsView.bottom-60, _window_width-30, 150) style:UITableViewStylePlain];
        _logisticsTable.delegate = self;
        _logisticsTable.dataSource = self;
        _logisticsTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _logisticsTable.hidden = YES;
        
    }
    return _logisticsTable;
}

- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}

-(void)copyAddress{
    UIPasteboard *paste = [UIPasteboard generalPasteboard];
    paste.string = self.model.address_format;
    [MBProgressHUD showError:YZMsg(@"复制成功")];
}

#pragma mark---------------
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.dataArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 60;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSDictionary *dic = self.dataArr[indexPath.row];
    LogisticsCell *cell = [LogisticsCell cellWithTab:tableView andIndexPath:indexPath];
    cell.dataDic = dic;
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *dic = self.dataArr[indexPath.row];
    [logisticsView setExpress:dic];
    self.logisticsTable.hidden = YES;
    
}

#pragma mark------确认发货------------
-(void)sendGoodsExpressid:(NSString *)expressid Number:(NSString *)number
{
    if (number.length < 1) {
        [MBProgressHUD showError:YZMsg(@"请填写物流单号")];
        return;
    }
    if (expressid.length < 1) {
        [MBProgressHUD showError:YZMsg(@"请选择物流公司")];
        return;
    }
    NSString *url = [purl stringByAppendingFormat:@"?service=Seller.setExpressInfo"];
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"orderid":self.orderModel.idStr,@"expressid":expressid, @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.orderModel.idStr,
                          @"expressid":expressid,
                          @"express_number":number,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                            };
    
    [YBNetworking postWithUrl:@"Seller.setExpressInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            [MBProgressHUD showError:msg];
            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    NSLog(@"s0000000000:%@",NSStringFromClass([touch.view class]));
    if ([NSStringFromClass([touch.view class]) isEqual:@"UITableViewCellContentView"]) {
        return NO;
    }
    return YES;
}

-(void)hideKeyBoard{
    [self.view endEditing:YES];
}
@end
