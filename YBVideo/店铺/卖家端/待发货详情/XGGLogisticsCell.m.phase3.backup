//
//  LogisticsCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/6/12.
//  Copyright © 2020 cat. All rights reserved.
//

#import "LogisticsCell.h"

@implementation LogisticsCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(LogisticsCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath {
    LogisticsCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LogisticsCell"];
     if (!cell) {
             cell = [[[NSBundle mainBundle]loadNibNamed:@"LogisticsCell" owner:nil options:nil]objectAtIndex:0];
     }
     return cell;
}
-(void)setDataDic:(NSDictionary *)dataDic
{
    [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"express_thumb"])]];
    self.titleLb.text = minstr([dataDic valueForKey:@"express_name"]);

}
@end
