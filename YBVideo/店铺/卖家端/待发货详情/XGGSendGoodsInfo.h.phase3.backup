//
//  SendGoodsInfo.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol SendGoodsInfoDelegate<NSObject>
-(void)sendGoodsExpressid:(NSString*)expressid Number:(NSString *)number;
@end

typedef void(^selectLogisticsEvent)();
@interface SendGoodsInfo : UIView<UITextFieldDelegate>
{
    UILabel *orderLb;
    
    UITextField *numField;
    UITextField *companyField;
    NSDictionary *expressDic;
}
@property (nonatomic, strong)UIButton *accessBtn;
@property (nonatomic, copy)selectLogisticsEvent selEvent;
@property (nonatomic, assign)id<SendGoodsInfoDelegate>delegate;
-(void)setData:(NSDictionary *)dic;
-(void)setExpress:(NSDictionary *)dic;
@end

NS_ASSUME_NONNULL_END
