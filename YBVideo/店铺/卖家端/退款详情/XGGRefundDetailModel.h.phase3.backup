//
//  RefundDetailModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/20.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface RefundDetailModel : NSObject

@property (nonatomic, strong)NSDictionary *order_info;
@property (nonatomic, strong)NSDictionary *refund_info;
@property (nonatomic, strong)NSDictionary *shop_info;

@property (nonatomic, strong)NSString *status_name;
@property (nonatomic, strong)NSString *status_desc;
@property (nonatomic, strong)NSString *status_time;
@property (nonatomic, strong)NSString *reason;//退款原因
@property (nonatomic, strong)NSString *spec_thumb_format;
@property (nonatomic, strong)NSString *goods_name;
@property (nonatomic, strong)NSString *spec_name;
@property (nonatomic, strong)NSString *price;
@property (nonatomic, strong)NSString *total;//退款金额
@property (nonatomic, strong)NSString *nums;
@property (nonatomic, strong)NSString *order_phone;

@property (nonatomic, strong)NSString *user_nickname;
@property (nonatomic, strong)NSString *type;//退款方式
@property (nonatomic, strong)NSString *refund_status_name;//退款方式
@property (nonatomic, strong)NSString *refund_status_time;
@property (nonatomic, strong)NSString *refund_status_desc;
@property (nonatomic, strong)NSString *refund_status_content;
@property (nonatomic, strong)NSString *addtime;//申请时间
@property (nonatomic, strong)NSString *orderno;//退款单号
@property (nonatomic, strong)NSString *refund_uid;//买家
@property (nonatomic, strong)NSString *shop_result;//退款单号
@property (nonatomic, strong)NSString *can_handle;//1可处理。0不可处理
@property (nonatomic, strong)NSString *shop_avatar; //问题描述

+(instancetype)modelWithDic:(NSDictionary *)subdic;

@end

NS_ASSUME_NONNULL_END
