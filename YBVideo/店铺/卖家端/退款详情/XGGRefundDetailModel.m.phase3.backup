//
//  RefundDetailModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/20.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RefundDetailModel.h"

@implementation RefundDetailModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        
        self.order_info = [dic valueForKey:@"order_info"];
        self.refund_info = [dic valueForKey:@"refund_info"];
        self.shop_info = [dic valueForKey:@"shop_info"];
        self.shop_avatar =minstr([self.shop_info valueForKey:@"avatar"]);

        self.status_name = minstr([self.order_info valueForKey:@"status_name"]);
        self.status_desc = minstr([self.order_info valueForKey:@"status_desc"]);
        self.status_time = minstr([self.order_info valueForKey:@"status_time"]);
        self.spec_thumb_format = minstr([self.order_info valueForKey:@"spec_thumb_format"]);
        self.goods_name= minstr([self.order_info valueForKey:@"goods_name"]);
        self.spec_name = minstr([self.order_info valueForKey:@"spec_name"]);
        self.price = minstr([self.order_info valueForKey:@"price"]);
        self.total = minstr([self.order_info valueForKey:@"total"]);//退款金额
        self.nums = minstr([self.order_info valueForKey:@"nums"]);
        self.order_phone =minstr([self.order_info valueForKey:@"phone"]);
        
        self.user_nickname = minstr([self.refund_info valueForKey:@"user_nickname"]);
        self.type = minstr([self.refund_info valueForKey:@"type"]);//退款方式
        self.addtime = minstr([self.refund_info valueForKey:@"addtime"]);//申请时间
        self.orderno= minstr([self.order_info valueForKey:@"orderno"]);//退款单号
        self.shop_result= minstr([self.refund_info valueForKey:@"shop_result"]);//退款单号
        self.can_handle = minstr([self.refund_info valueForKey:@"can_handle"]);
        self.reason = minstr([self.refund_info valueForKey:@"reason"]);//退款原因
        self.refund_uid =minstr([self.refund_info valueForKey:@"uid"]);
        
        self.refund_status_name =minstr([self.refund_info valueForKey:@"status_name"]);
        self.refund_status_time =minstr([self.refund_info valueForKey:@"status_time"]);
        self.refund_status_desc = minstr([self.refund_info valueForKey:@"status_desc"]);
        self.refund_status_content = minstr([self.refund_info valueForKey:@"content"]);

    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)subdic{
    RefundDetailModel *model = [[RefundDetailModel alloc]initWithDic:subdic];
    return model;
}

@end
