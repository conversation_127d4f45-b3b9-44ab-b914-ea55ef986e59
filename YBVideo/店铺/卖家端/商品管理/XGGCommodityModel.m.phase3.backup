//
//  CommodityModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/24.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityModel.h"

@implementation CommodityModel
-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
//        self.typeStr = minstr([dic valueForKey:@"type"]);
        self.specs = [dic valueForKey:@"specs"];
        self.idStr = minstr([dic valueForKey:@"id"]);
        self.name = minstr([dic valueForKey:@"name"]);
        self.price = minstr([dic valueForKey:@"price"]);
        self.sale_nums = minstr([dic valueForKey:@"sale_nums"]);
        self.thumb = minstr([dic valueForKey:@"thumb"]);
        self.type = minstr([dic valueForKey:@"type"]);
        self.original_price = minstr([dic valueForKey:@"original_price"]);
        self.status = minstr([dic valueForKey:@"status"]);
        self.commission = minstr([dic valueForKey:@"commission"]);
    }
    return self;
}

@end
