//
//  CommodityCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/24.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityCell.h"

@implementation CommodityCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    _typeImg.image = [UIImage imageNamed:getImagename(@"outsidegoodsTag")];
    [_btn4 setTitle:YZMsg(@"价格与库存") forState:0];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(CommodityCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath {
    CommodityCell *cell = [tableView dequeueReusableCellWithIdentifier:@"CommodityCell"];
     if (!cell) {
             cell = [[[NSBundle mainBundle]loadNibNamed:@"CommodityCell" owner:nil options:nil]objectAtIndex:0];
     }
    cell.contentView.backgroundColor = CellRow_Cor;

     return cell;

}
-(void)setModel:(CommodityModel *)model
{
    _model = model;
    if ([model.typeStr isEqual:@"saleother"]) {
        _statusLb.hidden = YES;
        _btn3.hidden = YES;
        _btn2.hidden = YES;
        _btn4.hidden = YES;
        [_btn1 setTitle:YZMsg(@"取消代卖") forState:0];
        [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:model.thumb]];
        self.nameLb.text = model.name;
        self.countLb.hidden = YES;
        self.priceLb.text =[NSString stringWithFormat:@"¥%@",model.price];
        self.original_priceLb.hidden = NO;
        self.original_priceLb.text = [NSString stringWithFormat:YZMsg(@"佣 ¥%@"),model.commission];
        self.original_priceLb.textColor = [UIColor whiteColor];
    }else{
        if ([model.typeStr isEqual:@"onsale"]) {
            _statusLb.hidden = YES;
            _btn3.hidden = YES;
            _btn2.hidden = YES;
            if ([model.type isEqual:@"1"]) {
                _btn4.hidden = YES;
            }else{
                _btn4.hidden = NO;
            }
            [_btn1 setTitle:YZMsg(@"下架") forState:0];
        }else if ([model.typeStr isEqual:@"onexamine"]){
            _statusLb.hidden = NO;
            if ([_model.status isEqual:@"0"]) {
                _statusLb.text =YZMsg(@"审核中");
            }else if([_model.status isEqual:@"2"]){
                _statusLb.text =YZMsg(@"已拒绝");
            }
            _btn3.hidden = YES;
            _btn4.hidden = YES;
            [_btn1 setTitle:YZMsg(@"删除") forState:0];
            [_btn2 setTitle:YZMsg(@"编辑") forState:0];
        }else{
            _statusLb.hidden = YES;
            _btn4.hidden = YES;
//            [_btn1 setTitleColor:[Normal_TextColor] forState:0];
//            _btn1.layer.borderColor = Normal_TextColor.CGColor;
//            _btn3.layer.borderColor =Normal_TextColor.CGColor;
            [_btn1 setTitle:YZMsg(@"上架") forState:0];
            [_btn2 setTitle:YZMsg(@"删除") forState:0];
            [_btn3 setTitle:YZMsg(@"编辑") forState:0];

        }
        [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:model.thumb]];
        self.nameLb.text = model.name;
        self.priceLb.text =[NSString stringWithFormat:@"¥%@",model.price];
        //['type'] 商品类型   0 站内商品  1 外链商品
        if ([model.type isEqual:@"1"]) {
            self.countLb.hidden = YES;
            self.typeImg.hidden = NO;
            self.original_priceLb.hidden = NO;
            self.lineLb.hidden = NO;
            self.original_priceLb.text = [NSString stringWithFormat:@"¥%@",model.original_price];
        }else{
            self.countLb.hidden = NO;
            self.countLb.text = [NSString stringWithFormat:YZMsg(@"已售%@件"),model.sale_nums];
            self.typeImg.hidden = YES;
            self.original_priceLb.hidden = YES;
            self.lineLb.hidden = YES;
        }

    }
}
- (IBAction)sellerCommBtnClick:(UIButton *)sender {
    NSString *tttt = sender.titleLabel.text;
    if (self.btnEvent) {
        self.btnEvent(tttt, _model);
    }
}
@end
