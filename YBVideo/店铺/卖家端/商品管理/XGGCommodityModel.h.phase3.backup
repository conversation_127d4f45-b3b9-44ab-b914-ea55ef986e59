//
//  CommodityModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/24.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CommodityModel : NSObject
@property (nonatomic, strong)NSString *typeStr;

@property (nonatomic, strong)NSString *idStr;//商品id
@property (nonatomic, strong)NSString *name;
@property (nonatomic, strong)NSString *price;
@property (nonatomic, strong)NSString *sale_nums;
@property (nonatomic, strong)NSString *thumb;
@property (nonatomic, strong)NSArray *specs;
@property (nonatomic, strong)NSString *type;
@property (nonatomic, strong)NSString *original_price;
@property (nonatomic, strong)NSString *status;
@property (nonatomic, strong)NSString *commission;

-(instancetype)initWithDic:(NSDictionary *)dic;

@end

NS_ASSUME_NONNULL_END
