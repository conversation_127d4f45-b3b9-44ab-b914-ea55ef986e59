//
//  CommodityCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/24.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "CommodityModel.h"
NS_ASSUME_NONNULL_BEGIN

typedef void(^commodityCellBtnEvent)(NSString *titles, CommodityModel *models);

@interface CommodityCell : UITableViewCell
@property (weak, nonatomic) IBOutlet UIImageView *thumbImg;
@property (weak, nonatomic) IBOutlet UILabel *priceLb;
@property (weak, nonatomic) IBOutlet UILabel *countLb;
@property (weak, nonatomic) IBOutlet UILabel *nameLb;
@property (weak, nonatomic) IBOutlet UILabel *statusLb;
@property (weak, nonatomic) IBOutlet UIButton *btn1;
@property (weak, nonatomic) IBOutlet UIButton *btn2;
@property (weak, nonatomic) IBOutlet UIButton *btn3;
@property (weak, nonatomic) IBOutlet UIButton *btn4;
@property (strong, nonatomic) IBOutlet UIImageView *typeImg;
@property (strong, nonatomic) IBOutlet UILabel *original_priceLb;
@property (strong, nonatomic) IBOutlet UILabel *lineLb;

@property (nonatomic, strong)CommodityModel *model;

@property (nonatomic, copy)commodityCellBtnEvent btnEvent;

+(CommodityCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end

NS_ASSUME_NONNULL_END
