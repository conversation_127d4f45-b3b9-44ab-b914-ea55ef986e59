//
//  AddOtherSaleGoodsVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/12/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "AddOtherSaleGoodsVC.h"
#import "RelationGoodsModel.h"
#import "goodsShowCell.h"
@interface AddOtherSaleGoodsVC ()<UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate>
{
    int page;
    NSMutableArray *goodsList;
    RelationGoodsModel *selectModel;

}
@property (nonatomic,strong)UITextField *searchT;
@property (nonatomic,strong) UITableView *goodsTableV;

@end

@implementation AddOtherSaleGoodsVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"添加商品");
    page = 1;
    goodsList = [NSMutableArray array];
    [self creatSearch];
    [self.view addSubview:self.goodsTableV];
    [self searchAnchorWithText:@""];

}
- (void)creatSearch{
    _searchT = [[UITextField alloc]initWithFrame:CGRectMake(13, statusbarHeight + 64 + 8, _window_width-26, 30)];
    _searchT.backgroundColor = RGB_COLOR(@"#fafafa", 1);
    _searchT.font = SYS_Font(15);
    _searchT.placeholder = YZMsg(@"请输入商品名称");
    _searchT.layer.cornerRadius = 15;
    _searchT.layer.masksToBounds = YES;
    _searchT.delegate = self;
    _searchT.leftViewMode = UITextFieldViewModeAlways;
    _searchT.keyboardType = UIKeyboardTypeWebSearch;
    UIImageView *leftImgView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, 30, 30)];
    leftImgView.image = [UIImage imageNamed:@"left_search"];
    _searchT.leftView = leftImgView;
    [self.view addSubview:_searchT];
}
- (UITableView *)goodsTableV{
    if (!_goodsTableV) {
        _goodsTableV = [[UITableView alloc]initWithFrame:CGRectMake(0, _searchT.bottom+8, _window_width, _window_height-ShowDiff-(_searchT.bottom+8)) style:0];
        _goodsTableV.delegate = self;
        _goodsTableV.dataSource = self;
        _goodsTableV.separatorStyle = 0;
        _goodsTableV.backgroundColor = [UIColor whiteColor];
        _goodsTableV.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
            page = 1;
            [self searchAnchorWithText:_searchT.text];
        }];
        _goodsTableV.mj_footer = [MJRefreshFooter footerWithRefreshingBlock:^{
            page ++;
            [self searchAnchorWithText:_searchT.text];
        }];

    }
    return _goodsTableV;
}
#pragma mark ================ searchBar代理 ===============
- (BOOL)textFieldShouldReturn:(UITextField *)textField{
    [_searchT resignFirstResponder];
    page = 1;
    [self searchAnchorWithText:_searchT.text];
    return YES;
}
- (void)searchAnchorWithText:(NSString *)searchStr{
    
    [YBNetworking postWithUrl:[NSString stringWithFormat:@"Shop.searchShopGoods&keywords=%@&p=%@",searchStr,@(page)] Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_goodsTableV.mj_header endRefreshing];
        [_goodsTableV.mj_footer endRefreshing];
        if (code == 0) {
            if (page == 1) {
                [goodsList removeAllObjects];
            }
            for (NSDictionary *dic in info) {
                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
                model.goosdType = 3;
                [goodsList addObject:model];
            }
            [_goodsTableV reloadData];
        }

        } Fail:^(id fail) {
            [_goodsTableV.mj_header endRefreshing];
            [_goodsTableV.mj_footer endRefreshing];

     }];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return goodsList.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:@"goodsShowCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];
    }
    RelationGoodsModel *model = goodsList[indexPath.row];
    cell.setBtn.hidden = NO;
    cell.stateImgV.hidden = YES;
    cell.model = model;
    return cell;

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 101;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
- (void)doReturn{
//    [super doReturn];
    [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
}

@end
