//
//  RelationVideoGoodsVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/31.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RelationVideoGoodsVC.h"
#import "goodsShowCell.h"
#import "RelationGoodsModel.h"
@interface RelationVideoGoodsVC ()<UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate>
{
    int page;
    int platfromPage;
    int contentPage;
    NSMutableArray *goodsList;
    NSMutableArray *contentList;
    NSMutableArray *platfromList;

    RelationGoodsModel *selectModel;
    
    UIView *_naviView;
    UIButton *_returnBtn;
    UIButton *goodsBtn;
    UIButton *contentBtn;
    UIButton *platfromBtn;

    NSString * goodstype;
}
@property (nonatomic,strong)UITextField *searchT;
@property (nonatomic,strong) UITableView *goodsTableV;
@property (nonatomic,strong) UITableView *contentTableV;
@property (nonatomic,strong) UITableView *platformTableV;

@end

@implementation RelationVideoGoodsVC

-(void)createNav{
    _naviView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
    _naviView.backgroundColor = Normal_BackColor;
    [self.view addSubview:_naviView];
    
    _returnBtn = [UIButton buttonWithType:0];
    _returnBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
    [_returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
    [_returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [_naviView addSubview:_returnBtn];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, _naviView.height-1, _window_width, 1) andColor:Line_Cor andView:_naviView];

    
    goodsBtn = [UIButton buttonWithType:0];
    goodsBtn.frame = CGRectMake(_window_width/2-80, 34+statusbarHeight, 70, 20);
    goodsBtn.titleLabel.font = SYS_Font(15);
    [goodsBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
    [goodsBtn setTitleColor:Normal_TextColor forState:UIControlStateNormal];
    goodsBtn.titleLabel.textAlignment = NSTextAlignmentLeft;
    [goodsBtn setTitle:YZMsg(@"小店商品") forState:0];
    goodsBtn.tag = 10000;
    goodsBtn.selected = YES;
    [goodsBtn addTarget:self action:@selector(titleBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [_naviView addSubview:goodsBtn];

    platfromBtn= [UIButton buttonWithType:0];
    platfromBtn.frame = CGRectMake(_window_width/2+10, 34+statusbarHeight, 70, 20);
    platfromBtn.titleLabel.font = SYS_Font(15);
    [platfromBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
    [platfromBtn setTitleColor:Normal_TextColor forState:UIControlStateNormal];
    platfromBtn.titleLabel.textAlignment = NSTextAlignmentLeft;
    [platfromBtn setTitle:YZMsg(@"平台自营") forState:0];
    platfromBtn.tag = 10002;
    [platfromBtn addTarget:self action:@selector(titleBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [_naviView addSubview:platfromBtn];
}
-(void)titleBtnClick:(UIButton *)sender{
    _selectID = @"0";
    selectModel = nil;
    [self.goodsTableV reloadData];
    [self.contentTableV reloadData];
    [self.platformTableV reloadData];
    if (sender.tag == 10000) {
        goodsBtn.selected = YES;
        contentBtn.selected = NO;
        platfromBtn.selected = NO;
        goodstype = @"1";
        [self.view bringSubviewToFront:self.goodsTableV];
        _searchT.placeholder =YZMsg(@"请输入商品名称");

    }else{
        platfromBtn.selected = YES;
        goodsBtn.selected = NO;
        contentBtn.selected = NO;
        goodstype = @"3";
        [self.view bringSubviewToFront:self.platformTableV];
        _searchT.placeholder =YZMsg(@"请输入商品名称");

    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = Normal_BackColor;
    page = 1;
    contentPage = 1;
    platfromPage = 1;
    goodstype = @"0";
    goodsList = [NSMutableArray array];
    contentList = [NSMutableArray array];
    platfromList = [NSMutableArray array];
    [self createNav];
    [self creatSearch];
    [self.view addSubview:self.contentTableV];
    [self.view addSubview:self.platformTableV];
    [self.view addSubview:self.goodsTableV];

    [self searchAnchorWithText:@""];
    [self searchContentAnchorWithText:@""];
    [self searchPlatfromWithText:@""];
}
- (void)creatSearch{
    _searchT = [[UITextField alloc]initWithFrame:CGRectMake(13, statusbarHeight + 64 + 8, _window_width-26, 30)];
    _searchT.backgroundColor =Normal_SubColor;// RGB_COLOR(@"#fafafa", 1);
    _searchT.font = SYS_Font(15);
//    _searchT.placeholder = YZMsg(@"请输入商品名称");
    _searchT.layer.cornerRadius = 15;
    _searchT.layer.masksToBounds = YES;
    _searchT.delegate = self;
    _searchT.leftViewMode = UITextFieldViewModeAlways;
    _searchT.keyboardType = UIKeyboardTypeWebSearch;
    _searchT.textColor = [UIColor whiteColor];
    NSMutableAttributedString*holderString = [[NSMutableAttributedString alloc] initWithString:YZMsg(@"请输入商品名称") attributes:@{NSForegroundColorAttributeName:[UIColor lightGrayColor] }];
    _searchT.attributedPlaceholder = holderString;

    [_searchT addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    
    UIImageView *leftImgView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, 30, 30)];
    leftImgView.image = [UIImage imageNamed:@"left_search"];
    _searchT.leftView = leftImgView;
    [self.view addSubview:_searchT];
}

- (UITableView *)goodsTableV{
    if (!_goodsTableV) {
        _goodsTableV = [[UITableView alloc]initWithFrame:CGRectMake(0, _searchT.bottom+8, _window_width, _window_height-ShowDiff-(_searchT.bottom+8)) style:0];
        _goodsTableV.delegate = self;
        _goodsTableV.dataSource = self;
        _goodsTableV.separatorStyle = 0;
        _goodsTableV.backgroundColor = Normal_BackColor;
        _goodsTableV.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
            page = 1;
            [self searchAnchorWithText:_searchT.text];
        }];
        _goodsTableV.mj_footer = [MJRefreshFooter footerWithRefreshingBlock:^{
            page ++;
            [self searchAnchorWithText:_searchT.text];
        }];

    }
    return _goodsTableV;
}
-(UITableView *)platformTableV{
    if (!_platformTableV) {
        _platformTableV = [[UITableView alloc]initWithFrame:CGRectMake(0, _searchT.bottom+8, _window_width, _window_height-ShowDiff-(_searchT.bottom+8)) style:0];
        _platformTableV.delegate = self;
        _platformTableV.dataSource = self;
        _platformTableV.separatorStyle = 0;
        _platformTableV.backgroundColor = Normal_BackColor;
        _platformTableV.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
            platfromPage = 1;
            [self searchPlatfromWithText:_searchT.text];
        }];
        _platformTableV.mj_footer = [MJRefreshFooter footerWithRefreshingBlock:^{
            platfromPage ++;
            [self searchPlatfromWithText:_searchT.text];
        }];

    }
    return _platformTableV;

}
-(UITableView *)contentTableV{
    if (!_contentTableV) {
        _contentTableV = [[UITableView alloc]initWithFrame:CGRectMake(0, _searchT.bottom+8, _window_width, _window_height-ShowDiff-(_searchT.bottom+8)) style:0];
        _contentTableV.delegate = self;
        _contentTableV.dataSource = self;
        _contentTableV.separatorStyle = 0;
        _contentTableV.backgroundColor = [UIColor whiteColor];
        _contentTableV.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
            contentPage = 1;
            [self searchContentAnchorWithText:_searchT.text];
        }];
        _contentTableV.mj_footer = [MJRefreshFooter footerWithRefreshingBlock:^{
            contentPage ++;
            [self searchContentAnchorWithText:_searchT.text];
        }];

    }
    return _contentTableV;

}
#pragma mark ================ searchBar代理 ===============
- (BOOL)textFieldShouldReturn:(UITextField *)textField{
    [_searchT resignFirstResponder];
    if ([goodstype isEqual:@"1"]) {
        page = 1;
        [self searchAnchorWithText:_searchT.text];
    }else if ([goodstype isEqual:@"3"]){
        platfromPage = 1;
        [self searchPlatfromWithText:_searchT.text];

    } else{
        contentPage = 1;
        [self searchContentAnchorWithText:_searchT.text];

    }
    return YES;
}
-(void)textFieldDidChange:(UITextField *)textField{
    if ( goodsBtn.selected == YES) {
        [goodsList removeAllObjects];
        [_goodsTableV reloadData];

        page = 1;
        [self searchAnchorWithText:textField.text];
    }else if (platfromBtn.selected == YES){
        [platfromList removeAllObjects];
        [_platformTableV reloadData];

        platfromPage = 1;
        [self searchPlatfromWithText:textField.text];
    } else{
        [contentList removeAllObjects];
        [_contentTableV reloadData];

        contentPage = 1;
        [self searchContentAnchorWithText:textField.text];
    }
}

//搜索平台商品
-(void)searchPlatfromWithText:(NSString *)searchStr{
    
    [YBNetworking postWithUrl:[NSString stringWithFormat:@"Shop.searchOnsalePlatformGoods&keywords=%@&p=%@",searchStr,@(platfromPage)] Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_platformTableV.mj_header endRefreshing];
        [_platformTableV.mj_footer endRefreshing];
        if (code == 0) {
            if (platfromPage == 1) {
                [platfromList removeAllObjects];
            }
            for (NSDictionary *dic in info) {
                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
                model.goosdType = 3;
                [platfromList addObject:model];
            }
            if (platfromList.count == 0) {
                [PublicView showImgNoData:_platformTableV name:@"shop_无数据" text:YZMsg(@"你还没有相关商品")];
            }else{
                [PublicView hiddenImgNoData:_platformTableV];
            }
            [_platformTableV reloadData];
        }

        } Fail:^(id fail) {
            [_platformTableV.mj_header endRefreshing];
            [_platformTableV.mj_footer endRefreshing];

     }];
}
- (void)searchAnchorWithText:(NSString *)searchStr{
    
    [YBNetworking postWithUrl:[NSString stringWithFormat:@"Shop.searchShopGoods&keywords=%@&p=%@",searchStr,@(page)] Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_goodsTableV.mj_header endRefreshing];
        [_goodsTableV.mj_footer endRefreshing];
        if (code == 0) {
            if (page == 1) {
                [goodsList removeAllObjects];
            }
            for (NSDictionary *dic in info) {
                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
                model.goosdType = 3;
                [goodsList addObject:model];
            }
            if (goodsList.count == 0) {
                [PublicView showImgNoData:_goodsTableV name:@"shop_无数据" text:YZMsg(@"你还没有相关商品")];
            }else{
                [PublicView hiddenImgNoData:_goodsTableV];
            }
            [_goodsTableV reloadData];
        }

        } Fail:^(id fail) {
            [_goodsTableV.mj_header endRefreshing];
            [_goodsTableV.mj_footer endRefreshing];

     }];
}
//搜索付费内容
- (void)searchContentAnchorWithText:(NSString *)searchStr{
    
    [YBNetworking postWithUrl:[NSString stringWithFormat:@"Paidprogram.searchPaidProgram&keywords=%@&p=%@",searchStr,@(contentPage)] Dic:nil Suc:^(int code, id info, NSString *msg) {
        [_contentTableV.mj_header endRefreshing];
        [_contentTableV.mj_footer endRefreshing];
        if (code == 0) {
            if (contentPage == 1) {
                [contentList removeAllObjects];
            }
            for (NSDictionary *dic in info) {
                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
                model.goosdType = 3;
                [contentList addObject:model];
            }
            
            if (contentList.count == 0) {
                [PublicView showImgNoData:_contentTableV name:@"shop_无数据" text:YZMsg(@"你还没有发布过付费内容")];
            }else{
                [PublicView hiddenImgNoData:_contentTableV];
            }

            [_contentTableV reloadData];
        }

        } Fail:^(id fail) {
            [_contentTableV.mj_header endRefreshing];
            [_contentTableV.mj_footer endRefreshing];

     }];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if (tableView == self.goodsTableV) {
        return goodsList.count;
    }else if (tableView == self.platformTableV){
        return platfromList.count;
    } else{
        return contentList.count;
    }
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    if (tableView == self.goodsTableV) {
        goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:@"goodsShowCELL"];
        if (!cell) {
            cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];
        }
        RelationGoodsModel *model = goodsList[indexPath.row];
        cell.setBtn.hidden = YES;
        cell.stateImgV.hidden = NO;
        if ([model.goodsid isEqual:_selectID]) {
            cell.stateImgV.image = [UIImage imageNamed:@"shop_sel"];
            if (!selectModel) {
                selectModel = model;
                goodstype = @"1";

            }
        }else{
            cell.stateImgV.image = [UIImage imageNamed:@"shop_nor"];
        }
        cell.model = model;
        return cell;

    }else if (tableView == self.platformTableV){
        goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:@"goodsShowCELL"];
        if (!cell) {
            cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];
        }
        RelationGoodsModel *model = platfromList[indexPath.row];
        cell.setBtn.hidden = YES;
        cell.stateImgV.hidden = NO;
        if ([model.goodsid isEqual:_selectID]) {
            cell.stateImgV.image = [UIImage imageNamed:@"shop_sel"];
            if (!selectModel) {
                selectModel = model;
                goodstype = @"1";

            }
        }else{
            cell.stateImgV.image = [UIImage imageNamed:@"shop_nor"];
        }
        cell.model = model;
        cell.priceOldL.textColor = Normal_Color;
        cell.priceOldL.text = [NSString stringWithFormat:YZMsg(@"佣 ¥%@"),model.commission];
        cell.priceOldL.hidden =NO;
        return cell;


    } else{
        goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:@"goodsShowCELL"];
        if (!cell) {
            cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];
        }
        RelationGoodsModel *model = contentList[indexPath.row];
        cell.setBtn.hidden = YES;
        cell.stateImgV.hidden = NO;
        cell.countLb.hidden = NO;
        
        if ([model.goodsid isEqual:_selectID]) {
            cell.stateImgV.image = [UIImage imageNamed:@"shop_sel"];
            if (!selectModel) {
                selectModel = model;
                goodstype = @"2";
            }
        }else{
            cell.stateImgV.image = [UIImage imageNamed:@"shop_nor"];
        }
        cell.model = model;
        cell.countLb.text = model.video_num;
        return cell;

    }

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 101;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (tableView == self.goodsTableV) {
        RelationGoodsModel *model = goodsList[indexPath.row];
        if (model.goodsid == _selectID) {
            _selectID = @"0";
            selectModel = nil;
            goodstype = @"0";
        }else{
            selectModel = model;
            _selectID = selectModel.goodsid;
            goodstype = @"1";
        }
        [tableView reloadData];

    }else if (tableView == self.platformTableV){
        RelationGoodsModel *model = platfromList[indexPath.row];
        if (model.goodsid == _selectID) {
            _selectID = @"0";
            selectModel = nil;
            goodstype = @"0";
        }else{
            selectModel = model;
            _selectID = selectModel.goodsid;
            goodstype = @"3";
        }
        [tableView reloadData];

    } else{
        RelationGoodsModel *model = contentList[indexPath.row];
        if (model.goodsid == _selectID) {
            _selectID = @"0";
            selectModel = nil;
            goodstype = @"0";

        }else{
            selectModel = model;
            _selectID = selectModel.goodsid;
            goodstype = @"2";

        }
        [tableView reloadData];

    }
}
- (void)doReturn{
        
    if (selectModel) {
        self.block(selectModel.name, selectModel.goodsid,goodstype);
    }else{
        self.block(YZMsg(@"关联商品"), @"0",goodstype);
    }
    [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
}

@end
