//
//  PlatformCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/12/4.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface PlatformCell : UICollectionViewCell
@property (weak, nonatomic) IBOutlet UIImageView *thumbImgV;
@property (weak, nonatomic) IBOutlet UILabel *titleL;
@property (weak, nonatomic) IBOutlet UILabel *priceL;
@property (weak, nonatomic) IBOutlet UILabel *oldPriceL;
@property (weak, nonatomic) IBOutlet UIView *statusView;
@property (weak, nonatomic) IBOutlet UILabel *statusLabel;
@property (weak, nonatomic) IBOutlet UILabel *sellCountLb;
@property (strong, nonatomic) IBOutlet UILabel *lineLb;
@property (weak, nonatomic) IBOutlet UILabel *commissionLb;

@end

NS_ASSUME_NONNULL_END
