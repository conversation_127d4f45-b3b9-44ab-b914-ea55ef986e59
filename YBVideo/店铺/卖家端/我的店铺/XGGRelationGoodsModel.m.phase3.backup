//
//  RelationGoodsModel.m
//  yunbaolive
//
//  Created by IOS1 on 2019/10/12.
//  Copyright © 2019 cat. All rights reserved.
//

#import "RelationGoodsModel.h"

@implementation RelationGoodsModel
-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        _name = minstr([dic valueForKey:@"name"]);
        _goodsid = minstr([dic valueForKey:@"id"]);
        _thumb = minstr([dic valueForKey:@"thumb"]);
        _hits = minstr([dic valueForKey:@"hits"]);
        _old_price = minstr([dic valueForKey:@"old_price"]);
        _original_price = minstr([dic valueForKey:@"original_price"]);
        _price = minstr([dic valueForKey:@"price"]);
        _des = minstr([dic valueForKey:@"des"]);
        _issale = minstr([dic valueForKey:@"issale"]);
        _href = minstr([dic valueForKey:@"href"]);
        _type = minstr([dic valueForKey:@"type"]);
        _goodsUserid = minstr([dic valueForKey:@"uid"]);
        _status = minstr([dic valueForKey:@"status"]);
        _video_num = minstr([dic valueForKey:@"video_num"]);
        _live_isshow = minstr([dic valueForKey:@"live_isshow"]);
        _commission = minstr([dic valueForKey:@"commission"]);
    }
    return self;
}

@end
