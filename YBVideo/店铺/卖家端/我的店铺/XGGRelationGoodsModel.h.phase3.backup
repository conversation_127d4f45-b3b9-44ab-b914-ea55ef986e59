//
//  RelationGoodsModel.h
//  yunbaolive
//
//  Created by IOS1 on 2019/10/12.
//  Copyright © 2019 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface RelationGoodsModel : NSObject
@property (nonatomic,strong) NSString *name;
@property (nonatomic,strong) NSString *goodsid;
@property (nonatomic,strong) NSString *hits;
@property (nonatomic,strong) NSString *thumb;
@property (nonatomic,strong) NSString *old_price;
@property (nonatomic,strong) NSString *original_price;
@property (nonatomic,strong) NSString *price;
@property (nonatomic,strong) NSString *des;
@property (nonatomic,strong) NSString *issale;
@property (nonatomic,strong) NSString *href;
@property (nonatomic,strong) NSString *type;
@property (nonatomic,strong) NSString *goodsUserid;
@property (nonatomic,strong) NSString *status;
@property (nonatomic,strong) NSString *video_num;
@property (nonatomic,strong) NSString *live_isshow;
@property (nonatomic,strong) NSString *commission;
@property (nonatomic,strong) NSString *isOtherSale;

@property (nonatomic,assign) int goosdType;//1-主播直播间商品 2-用户直播间商品 3-添加在售商品

-(instancetype)initWithDic:(NSDictionary *)dic;
@end

NS_ASSUME_NONNULL_END
