//
//  goodsShowCell.m
//  yunbaolive
//
//  Created by IOS1 on 2019/8/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import "goodsShowCell.h"
#import "GoodsDetailViewController.h"
#import "CommodityDetailVC.h"
#import "OutsideGoodsDetailVC.h"
@implementation goodsShowCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.contentView.backgroundColor = CellRow_Cor;
    [_showBtn setTitle:YZMsg(@"展示") forState:0];
    [_removeBtn setTitle:YZMsg(@"移除") forState:0];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
- (void)setModel:(RelationGoodsModel *)model{
    //goosdType 1-主播直播间商品 2-用户直播间商品 3-添加在售商品
    _model = model;
    [_thumbImgV sd_setImageWithURL:[NSURL URLWithString:_model.thumb]];
    _nameL.text = _model.name;
    _priceL.text = [NSString stringWithFormat:@"¥%@",_model.price];
    if ([model.type isEqual:@"1"]) {
        _priceOldL.hidden = NO;
        _lineView.hidden = NO;
        _priceOldL.text = [NSString stringWithFormat:@"¥%@",_model.original_price];
    }
    if ([_model.issale isEqual:@"1"]) {
        [_setBtn setTitle:YZMsg(@"已添加") forState:0];
        [_setBtn setTitleColor:RGB_COLOR(@"#C8C8C8", 1) forState:0];
        _setBtn.layer.borderColor = RGB_COLOR(@"#C8C8C8", 1).CGColor;
    }else{
        [_setBtn setTitle:YZMsg(@"添加") forState:0];
        [_setBtn setTitleColor:Pink_Cor forState:0];
        _setBtn.layer.borderColor = Pink_Cor.CGColor;
    }

}
- (IBAction)setBtnClick:(id)sender {
    //添加平台商品
    if ([_model.isOtherSale isEqual:@"1"]) {
        //增删在售商品
        NSString *saleStr;
        if ([_model.issale isEqual:@"1"]) {
            saleStr = @"0";
        }else{
            saleStr = @"1";
        }
        NSLog(@"sdsjdsjkkk----:%@", self.haveList);
        for (RelationGoodsModel *model in self.haveList) {
            if ([model.goodsid isEqual:_model.goodsid]) {
                return;
            }
        }
        [YBNetworking postWithUrl:@"Shop.setPlatformGoodsSale" Dic:@{@"goodsid":_model.goodsid,@"issale":saleStr} Suc:^(int code, id info, NSString *msg) {
            [MBProgressHUD showError:msg];
            if (code == 0) {
                _model.issale = saleStr;
                if ([_model.issale isEqual:@"1"]) {
                    [_setBtn setTitle:YZMsg(@"已添加") forState:0];
                    [_setBtn setTitleColor:RGB_COLOR(@"#C8C8C8", 1) forState:0];
                    _setBtn.layer.borderColor = RGB_COLOR(@"#C8C8C8", 1).CGColor;
                }else{
                    [_setBtn setTitle:YZMsg(@"添加") forState:0];
                    [_setBtn setTitleColor:Pink_Cor forState:0];
                    _setBtn.layer.borderColor = Pink_Cor.CGColor;
                }
            }

            } Fail:^(id fail) {
                
         }];


    }else{
        //1-主播直播间商品 2-用户直播间商品 3-添加在售商品
        if (_model.goosdType == 3 || _model.goosdType == 1) {
            //增删在售商品
            NSString *saleStr;
            if (_model.goosdType == 1) {
                saleStr = @"0";
            }else{
                if ([_model.issale isEqual:@"1"]) {
                    saleStr = @"0";
                }else{
                    saleStr = @"1";
                }
            }
            NSLog(@"sdsjdsjkkk----:%@", self.haveList);
            for (RelationGoodsModel *model in self.haveList) {
                if ([model.goodsid isEqual:_model.goodsid]) {
                    return;
                }
            }
            
            [YBNetworking postWithUrl:@"Shop.SetSale" Dic:@{@"goodsid":_model.goodsid,@"issale":saleStr} Suc:^(int code, id info, NSString *msg) {
                [MBProgressHUD showError:msg];
                if (code == 0) {
                    _model.issale = saleStr;
                    if (_model.goosdType == 1) {
                        if (self.delegate) {
                            [self.delegate removeThisGoods:_model];
                        }
                    }else{
                        if ([_model.issale isEqual:@"1"]) {
                            [_setBtn setTitle:YZMsg(@"已添加") forState:0];
                            [_setBtn setTitleColor:RGB_COLOR(@"#C8C8C8", 1) forState:0];
                            _setBtn.layer.borderColor = RGB_COLOR(@"#C8C8C8", 1).CGColor;
                        }else{
                            [_setBtn setTitle:YZMsg(@"添加") forState:0];
                            [_setBtn setTitleColor:Pink_Cor forState:0];
                            _setBtn.layer.borderColor = Pink_Cor.CGColor;
                        }
                    }
                }

                } Fail:^(id fail) {
                    
             }];

        }else{
            if (_model.goosdType == 2){
                [PublicObj checkGoodsExistenceWithID:_model.goodsid Existence:^(int code, NSString *msg) {
                    if (code == 0) {
                        if ([_model.type isEqual:@"1"]) {
                            OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
                            detail.goodsID = _model.goodsid;
                            [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];

                        }else{
                            CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                            detail.goodsID = _model.goodsid;
                            detail.liveUid = self.liveUid;
                            [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];

                        }

                    }else{
                        [MBProgressHUD showError:msg];

                    }
                }];

            }
        }

    }
    
}
- (IBAction)showBtnClick:(UIButton *)sender {
//    NSString *url = [purl stringByAppendingFormat:@"?service=Live.setLiveGoodsIsShow"];
    
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":_model.goodsid
                          };

    [YBNetworking postWithUrl:@"Live.setLiveGoodsIsShow" Dic:dic Suc:^(int code, id info, NSString *msg) {
        NSDictionary *infos = [info firstObject];
        NSLog(@"infos------set goods：%@",infos);
        [MBProgressHUD showError:YZMsg(@"设置成功")];
        [self.delegate reloadShowGoods:infos andmodel:_model];

        } Fail:^(id fail) {
            
     }];

}
- (IBAction)removeBtnClick:(UIButton *)sender {
    NSString *saleStr;
    if (_model.goosdType == 1) {
        saleStr = @"0";
    }
    NSString *urlStr = @"";
    if ([_model.type isEqual:@"2"]) {
        urlStr = @"Shop.setPlatformGoodsSale";
    }else{
        urlStr = @"Shop.SetSale";
    }
    [YBNetworking postWithUrl:urlStr Dic:@{@"goodsid":_model.goodsid,@"issale":saleStr} Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD showError:msg];
        if (code == 0) {
            _model.issale = saleStr;
            if (_model.goosdType == 1) {
                if (self.delegate) {
                    [self.delegate removeThisGoods:_model];
                }
            }
        }

        } Fail:^(id fail) {
            
     }];

}

@end
