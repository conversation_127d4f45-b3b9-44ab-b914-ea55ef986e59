//
//  SellOrderDetailModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/20.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SellOrderDetailModel : NSObject

@property (nonatomic, strong)NSDictionary *order_info;
@property (nonatomic, strong)NSDictionary *shop_info;
@property (nonatomic, strong)NSArray *express_info;

@property (nonatomic, strong)NSString *status;
@property (nonatomic, strong)NSString *status_name;//订单状态
@property (nonatomic, strong)NSString *status_desc;//订单状态详情
@property (nonatomic, strong)NSString *orderno;//订单编号
@property (nonatomic, strong)NSString *orderid;//订单id
@property (nonatomic, strong)NSString *message;//买家留言
@property (nonatomic, strong)NSString *username;//买家姓名
@property (nonatomic, strong)NSString *phone;//买家电话
@property (nonatomic, strong)NSString *province;//省
@property (nonatomic, strong)NSString *city;//市
@property (nonatomic, strong)NSString *area;//区
@property (nonatomic, strong)NSString *address;//详细地址
@property (nonatomic, strong)NSString *address_format;//拼接好详细地址
@property (nonatomic, strong)NSString *buyerUid;

@property (nonatomic, strong)NSString *spec_thumb_format;//商品规格图片
@property (nonatomic, strong)NSString *goods_name;//商品名称
@property (nonatomic, strong)NSString *spec_name;//商品规格
@property (nonatomic, strong)NSString *price;//商品价格
@property (nonatomic, strong)NSString *nums;//商品个数
@property (nonatomic, strong)NSString *postage;//商品运费
@property (nonatomic, strong)NSString *total;//实付款
@property (nonatomic, strong)NSString *paytype;
@property (nonatomic, strong)NSString *paytime;//下单时间
@property (nonatomic, strong)NSString *addtime;//下单时间

@property (nonatomic, strong)NSString *shop_uid;//店铺id
@property (nonatomic, strong)NSString *shop_avatar;//头像
@property (nonatomic, strong)NSString *shop_phone;//电话

@property (nonatomic, strong)NSString *express_desc;//包裹详细数据
@property (nonatomic, strong)NSString *express_state_name;//包裹标题

+(instancetype)modelWithDic:(NSDictionary *)subdic;

@end

NS_ASSUME_NONNULL_END
