//
//  SellOrderDetailModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/20.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SellOrderDetailModel.h"

@implementation SellOrderDetailModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.order_info = [dic valueForKey:@"order_info"];
        self.shop_info = [dic valueForKey:@"shop_info"];
        self.express_info = [dic valueForKey:@"express_info"];

        self.status = minstr([self.order_info valueForKey:@"status"]);
        self.status_name = minstr([self.order_info valueForKey:@"status_name"]);//订单状态
        self.status_desc = minstr([self.order_info valueForKey:@"status_desc"]);//订单状态详情
        
        self.orderno= minstr([self.order_info valueForKey:@"orderno"]);//订单编号
        self.message= minstr([self.order_info valueForKey:@"message"]);//买家留言
        self.username= minstr([self.order_info valueForKey:@"username"]);//买家姓名
        self.phone= minstr([self.order_info valueForKey:@"phone"]);//买家电话
        self.province= minstr([self.order_info valueForKey:@"province"]);//省
        self.city= minstr([self.order_info valueForKey:@"city"]);//市
        self.area= minstr([self.order_info valueForKey:@"area"]);//区
        self.address= minstr([self.order_info valueForKey:@"address"]);//详细地址
        self.address_format= minstr([self.order_info valueForKey:@"address_format"]);//拼接好详细地址
        self.buyerUid = minstr([self.order_info valueForKey:@"uid"]);
        
        self.spec_thumb_format= minstr([self.order_info valueForKey:@"spec_thumb_format"]);//商品规格图片
        self.goods_name= minstr([self.order_info valueForKey:@"goods_name"]);//商品名称
        self.spec_name= minstr([self.order_info valueForKey:@"spec_name"]);//商品规格
        self.price= minstr([self.order_info valueForKey:@"price"]);//商品价格
        self.nums= minstr([self.order_info valueForKey:@"nums"]);//商品个数
        self.postage= minstr([self.order_info valueForKey:@"postage"]);//商品运费
        self.total= minstr([self.order_info valueForKey:@"total"]);//实付款
        self.paytype = minstr([self.order_info valueForKey:@"type"]);
        self.paytime = minstr([self.order_info valueForKey:@"paytime"]);
        self.addtime = minstr([self.order_info valueForKey:@"addtime"]);
        self.orderid = minstr([self.order_info valueForKey:@"id"]);
        self.express_desc = minstr([self.express_info valueForKey:@"desc"]);
        self.express_state_name = minstr([self.express_info valueForKey:@"state_name"]);

        self.shop_uid = minstr([self.shop_info valueForKey:@"uid"]);
        self.shop_avatar =minstr([self.shop_info valueForKey:@"avatar"]);
        self.shop_phone = minstr([self.shop_info valueForKey:@"service_phone"]);

    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)subdic{
    SellOrderDetailModel *model = [[SellOrderDetailModel alloc]initWithDic:subdic];
    return model;
}

@end
