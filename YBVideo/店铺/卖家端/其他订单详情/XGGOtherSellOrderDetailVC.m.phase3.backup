//
//  OtherSellOrderDetailVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/19.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OtherSellOrderDetailVC.h"
#import "OrderHeaderView.h"
#import "SellOrderDetailModel.h"
#import "OrderPublicView.h"
#import "OrderInfoView.h"
#import "OrderPriceView.h"
@interface OtherSellOrderDetailVC (){
    OrderHeaderView *headView;
    OrderInfoView   *infosView;
    OrderPriceView *price;
    
    OrderPublicView *messagePublicView;//买家留言
}
@property(nonatomic, strong)UIScrollView *backScroll;
@property (nonatomic, strong)SellOrderDetailModel *model;

@end

@implementation OtherSellOrderDetailVC
-(void)doReturn{
    [[YBBaseAppDelegate sharedAppDelegate]popViewController:YES];
}
-(void)getGoodsOrderInfo{
    
    NSString *url = [purl stringByAppendingFormat:@"?service="];
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.orderModel.idStr,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                            };
    [YBNetworking postWithUrl:@"Seller.getGoodsOrderInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            self.model  = [SellOrderDetailModel modelWithDic:infos];
            [self creatUI];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBarHidden = YES;

    self.view.backgroundColor = Normal_BackColor;
    _backScroll = [[UIScrollView alloc]init];
    _backScroll.backgroundColor = Normal_BackColor;
    [self.view addSubview:_backScroll];
    [_backScroll mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(self.view);
    }];
    if (@available(iOS 11.0, *)) {
        _backScroll.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    UIButton *returnBtn = [UIButton buttonWithType:0];
    returnBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
    [returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:returnBtn];
    
    [self getGoodsOrderInfo];
}
-(void)creatUI{
    [self creatHeadView];
    //    -1 已关闭 0 待买家付款  1 待发货  2  待确认收货   3 待评价  4 已评价  5 退款
    if ([self.model.status isEqual:@"0"] ||[self.model.status isEqual:@"-1"]) {
        OrderPublicView *addressView = [[OrderPublicView alloc]init];
        addressView.frame = CGRectMake(0, headView.bottom, _window_width, 75);
        [addressView setSellOrderModelData:self.model AndIndex:1];
        [_backScroll addSubview:addressView];
        addressView.lineLb.hidden = NO;

        infosView = [[OrderInfoView alloc]init];
        infosView.sellerModel = self.model;
        [_backScroll addSubview:infosView];
        if (self.model.message.length > 0) {
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(addressView.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(120);
            }];
        }else{
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(addressView.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(120);
            }];

        }
        
        price = [[OrderPriceView alloc]init];
        [_backScroll addSubview:price];
        [price mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(infosView.mas_bottom);
            make.left.right.equalTo(self.view);
            make.height.mas_equalTo(108);
        }];
        [price.kefuBtn setTitle:YZMsg(@"联系买家") forState:0];
//        [price setSellerModelData:self.model];
        price.models = self.model;
    }else{
        OrderPublicView* publicView = [[OrderPublicView alloc]init];
        [_backScroll addSubview:publicView];
        [publicView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(headView.mas_bottom);
            make.height.mas_equalTo(60);
        }];
        [publicView setSellOrderModelData:self.model AndIndex:1];
        
        OrderPublicView* publicView1 = [[OrderPublicView alloc]init];
        [_backScroll addSubview:publicView1];
        [publicView1 mas_makeConstraints:^(MASConstraintMaker *make) {
             make.left.right.equalTo(self.view);
             make.top.equalTo(publicView.mas_bottom);
             make.height.mas_equalTo(75);
         }];
        [publicView1 setSellOrderModelData:self.model AndIndex:2];
        
        //如果有买家留言
        if (self.model.message.length > 0) {
            messagePublicView = [[OrderPublicView alloc]init];
            [_backScroll addSubview:messagePublicView];
             [messagePublicView mas_makeConstraints:^(MASConstraintMaker *make) {
                 make.left.right.equalTo(self.view);
                 make.top.equalTo(publicView1.mas_bottom);
                 make.height.mas_equalTo(60);
             }];
            [messagePublicView setSellOrderModelData:self.model AndIndex:3];
            messagePublicView.lineLb.hidden = NO;
        }else{
            publicView1.lineLb.hidden = NO;
        }

        infosView = [[OrderInfoView alloc]init];
        infosView.sellerModel = self.model;
        [_backScroll addSubview:infosView];
        if (self.model.message.length > 0) {
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(messagePublicView.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(120);
            }];
        }else{
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(publicView1.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(120);
            }];

        }
        
        price = [[OrderPriceView alloc]init];
        [_backScroll addSubview:price];
        [price mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(infosView.mas_bottom);
            make.left.right.equalTo(self.view);
            make.height.mas_equalTo(108);
        }];
        [price.kefuBtn setTitle:YZMsg(@"联系买家") forState:0];

//        [price setSellerModelData:self.model];
        price.models = self.model;

    }
    
    [_backScroll layoutSubviews];

    NSArray *orderArr;
    if ([self.model.status isEqual:@"0"]||[self.model.status isEqual:@"-1"]) {
        orderArr = @[YZMsg(@"订单编号"),YZMsg(@"下单时间")];
    }else{
        orderArr = @[YZMsg(@"订单编号"),YZMsg(@"下单时间"),YZMsg(@"支付方式"),YZMsg(@"支付时间")];
    }
    UIView *backView = [[UIView alloc]init];
    backView.backgroundColor = Normal_SubColor;
    backView.frame = CGRectMake(0, price.bottom, _window_width, 40*orderArr.count);
    [_backScroll addSubview:backView];

    for (int i = 0; i < orderArr.count; i ++) {
        UILabel *lb = [[UILabel alloc]init];
        lb.frame = CGRectMake(12, 40*i, _window_width*0.8, 40);
        lb.font = [UIFont systemFontOfSize:14];
        lb.textColor = [UIColor grayColor];
        lb.text = orderArr[i];
        [backView addSubview:lb];
        if (i == 0) {
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],self.model.orderno];
            
            UIButton *copyBtn = [UIButton buttonWithType:0];
            copyBtn.frame = CGRectMake(lb.right+10, 0, 50, 40);
            [copyBtn setTitle:YZMsg(@"复制") forState:0];
            [copyBtn setTitleColor:RGB(68, 121, 255) forState:0];
            copyBtn.titleLabel.font = [UIFont systemFontOfSize:14];
            [copyBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

            [backView addSubview:copyBtn];
        }else if (i == 1){
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],self.model.addtime];

        }else if (i == 2){
            NSString *pay;
            if ([self.model.paytype isEqual:@"1"]) {
                pay =YZMsg(@"支付宝");
            }else if ([self.model.paytype isEqual:@"2"]){
                pay =YZMsg(@"微信");
            }else{
                pay =YZMsg(@"余额");
            }
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],pay];

        }else{
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],self.model.paytime];
        }
    }
    _backScroll.contentSize = CGSizeMake(_window_width, CGRectGetMaxY(backView.frame)+60);

    if ([self.model.status isEqual:@"-1"]) {
        [self creatBottomBtn];
    }
}
-(void)creatHeadView{
    headView = [[OrderHeaderView alloc]init];
    headView.frame = CGRectMake(0, 0, _window_width, 135);
    [_backScroll  addSubview:headView];
    [headView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_backScroll);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(135);
    }];
    [headView setSellerData:self.model];
}
-(void)creatBottomBtn{
    UIView *bottomView = [[UIView alloc]init];
    bottomView.frame= CGRectMake(0, _window_height-50, _window_width, 50);
    bottomView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bottomView];

    
    UIButton *payBtn = [UIButton buttonWithType:0];
    payBtn.frame = CGRectMake(_window_width-98, 12, 83, 26);
    [payBtn setTitle:YZMsg(@"删除订单") forState:0];
    [payBtn setTitleColor:[UIColor blackColor] forState:0];
    payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    payBtn.layer.borderColor = [UIColor lightGrayColor].CGColor;
    payBtn.layer.cornerRadius = 2;
    payBtn.layer.borderWidth = 1;
    payBtn.layer.masksToBounds = YES;
    [payBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

    [bottomView addSubview:payBtn];

}
-(void)btnClick:(UIButton *)sender{
    NSString *titles =sender.titleLabel.text;
    if ([titles isEqual:YZMsg(@"复制")]){
        [self copyOrderNum];
    }else if ([titles isEqual:YZMsg(@"删除订单")]){
        [self delGoodsOrder];
    }
}
#pragma mark-----复制订单号-------
-(void)copyOrderNum{
    UIPasteboard *paste = [UIPasteboard generalPasteboard];
    paste.string = self.model.orderno;
    [MBProgressHUD showError:YZMsg(@"复制成功")];

}
#pragma mark------删除订单----------
-(void)delGoodsOrder{
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.model.orderid,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                            };
    [YBNetworking postWithUrl:@"Seller.delGoodsOrder" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            [MBProgressHUD showError:msg];
            [[YBBaseAppDelegate sharedAppDelegate]popViewController:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];


}
@end
