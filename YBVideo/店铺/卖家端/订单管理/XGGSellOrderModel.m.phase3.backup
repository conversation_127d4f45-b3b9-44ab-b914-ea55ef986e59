//
//  SellOrderModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/18.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SellOrderModel.h"

@implementation SellOrderModel
-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.orderNum = minstr([dic valueForKey:@"num"]);
        self.status = minstr([dic valueForKey:@"status"]);
        self.status_name = minstr([dic valueForKey:@"status_name"]);
        self.nums = minstr([dic valueForKey:@"nums"]);
        self.refund_status = minstr([dic valueForKey:@"refund_status"]);
        self.refund_shop_status = minstr([dic valueForKey:@"refund_shop_status"]);
        self.phone = minstr([dic valueForKey:@"phone"]);
        self.username = minstr([dic valueForKey:@"username"]);
        self.orderno = minstr([dic valueForKey:@"orderno"]);
        self.spec_name = minstr([dic valueForKey:@"spec_name"]);
        self.goodsid = minstr([dic valueForKey:@"goodsid"]);
        self.goods_name = minstr([dic valueForKey:@"goods_name"]);
        self.spec_thumb = minstr([dic valueForKey:@"spec_thumb"]);
        self.price = minstr([dic valueForKey:@"price"]);
        self.idStr = minstr([dic valueForKey:@"id"]);
        self.isattention = minstr([dic valueForKey:@"isattention"]);
        self.user_nickname =minstr([dic valueForKey:@"user_nickname"]);
        self.avatar = minstr([dic valueForKey:@"avatar"]);
        self.uidStr =minstr([dic valueForKey:@"uid"]);
        self.refund_type = minstr([dic valueForKey:@"refund_type"]);
        self.total = minstr([dic valueForKey:@"total"]);
    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)subdic{
    SellOrderModel *model = [[SellOrderModel alloc]initWithDic:subdic];
    return model;
}

@end
