//
//  SellOrderModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/18.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SellOrderModel : NSObject

@property(strong, nonatomic)NSString *status;
@property(strong, nonatomic)NSString *status_name;
@property(strong, nonatomic)NSString *nums;
@property(strong, nonatomic)NSString *refund_status;
@property(strong, nonatomic)NSString *refund_shop_status;
@property(strong, nonatomic)NSString *phone;
@property(strong, nonatomic)NSString *username;
@property(strong, nonatomic)NSString *orderno;
@property(strong, nonatomic)NSString *spec_name;
@property(strong, nonatomic)NSString *goodsid;
@property(strong, nonatomic)NSString *goods_name;
@property(strong, nonatomic)NSString *spec_thumb;
@property(strong, nonatomic)NSString *orderNum;
@property(strong, nonatomic)NSString *price;
@property(strong, nonatomic)NSString *idStr;
@property(strong, nonatomic)NSString *isattention;
@property(strong, nonatomic)NSString *avatar;
@property(strong, nonatomic)NSString *user_nickname;
@property(strong, nonatomic)NSString *uidStr;
@property(strong, nonatomic)NSString *refund_type;
@property(strong, nonatomic)NSString *total;
-(instancetype)initWithDic:(NSDictionary *)dic;
+(instancetype)modelWithDic:(NSDictionary *)subdic;

@end

NS_ASSUME_NONNULL_END
