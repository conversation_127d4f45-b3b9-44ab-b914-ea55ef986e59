//
//  SellOrderCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/18.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SellOrderCell.h"
#import "WaitSendGoodsVC.h"
#import "OtherSellOrderDetailVC.h"
#import "RefundDetailVC.h"
#import "JCHATConversationViewController.h"

@implementation SellOrderCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    [_sendGoodsBtn setTitle:YZMsg(@"去发货") forState:0];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(SellOrderCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath {
    SellOrderCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SellOrderCell"];
     if (!cell) {
             cell = [[[NSBundle mainBundle]loadNibNamed:@"SellOrderCell" owner:nil options:nil]objectAtIndex:0];
     }
     return cell;

}
-(void)setModel:(SellOrderModel *)model
{
    /*
     info[0]['list'][].status状态：
     -1 已关闭 0 待买家付款  1 待发货  2  待确认收货   3 待评价  4 已评价  5 退款
     info[0]['list'][].refund_status 退款处理结果 -1 失败 0 处理中 1 成功
     */
    _model = model;
    
    if ([model.status isEqual:@"0"]) {
        self.sendGoodsBtn.hidden = YES;
        self.btn2.hidden = YES;
        [self.btn1 setTitle:YZMsg(@"联系买家") forState:0];
        [self.btn1 setTitleColor:[UIColor grayColor] forState:0];
        self.btn1.layer.borderColor = [UIColor grayColor].CGColor;
        self.countLb.hidden = YES;
    }else if ([model.status isEqual:@"1"]) {
        self.btn1.hidden = YES;
        self.btn2.hidden = YES;
        self.sendGoodsBtn.hidden = NO;
        self.statusLb.text =YZMsg(@"待发货");
    }else if ([model.status isEqual:@"5"]&&[model.refund_status isEqual:@"0"]){
        self.sendGoodsBtn.hidden = YES;
        self.statusLb.text = YZMsg(@"待处理，2天23小时");
        [self.btn1 setTitle:YZMsg(@"退款详情") forState:0];
        [self.btn2 setTitle:YZMsg(@"联系买家") forState:0];

    }else if ([model.status isEqual:@"0"]){
        self.sendGoodsBtn.hidden = YES;
        self.btn2.hidden = YES;
        self.statusLb.text = YZMsg(@"待处理，2天23小时");
        [self.btn1 setTitle:YZMsg(@"联系买家") forState:0];
        [self.btn1 setTitleColor:[UIColor grayColor] forState:0];

        self.btn1.layer.borderColor = [UIColor grayColor].CGColor;
        
    }else if ([model.status isEqual:@"5"]&&![model.refund_status isEqual:@"0"]){
        self.sendGoodsBtn.hidden = YES;
        self.btn2.hidden = YES;
        self.statusLb.text =YZMsg(@"已退款");
        [self.btn1 setTitle:YZMsg(@"删除订单") forState:0];
        [self.btn1 setTitleColor:[UIColor grayColor] forState:0];
        self.btn1.layer.borderColor = [UIColor grayColor].CGColor;

    }else if ([model.status isEqual:@"2"]){
        self.sendGoodsBtn.hidden = YES;
        self.btn2.hidden = YES;
        self.statusLb.text = YZMsg(@"已发货");
        [self.btn1 setTitle:YZMsg(@"查看物流") forState:0];
        [self.btn1 setTitleColor:[UIColor grayColor] forState:0];
        self.btn1.layer.borderColor = [UIColor grayColor].CGColor;

    }else if ([model.status isEqual:@"3"]){
        self.sendGoodsBtn.hidden = YES;
        self.btn2.hidden = YES;
        self.statusLb.text = YZMsg(@"已签收");
        [self.btn1 setTitle:YZMsg(@"查看物流") forState:0];
        [self.btn1 setTitleColor:[UIColor grayColor] forState:0];
        self.btn1.layer.borderColor = [UIColor grayColor].CGColor;

    }else if ([model.status isEqual:@"4"]){
        self.sendGoodsBtn.hidden = YES;
        self.btn2.hidden = YES;
        self.statusLb.text = YZMsg(@"交易成功");
        [self.btn1 setTitle:YZMsg(@"删除订单") forState:0];
        [self.btn1 setTitleColor:[UIColor grayColor] forState:0];
        self.btn1.layer.borderColor = [UIColor grayColor].CGColor;

    }else if ([model.status isEqual:@"-1"]){
        self.sendGoodsBtn.hidden = YES;
        self.btn2.hidden = YES;
        self.statusLb.text = YZMsg(@"已关闭");
        [self.btn1 setTitle:YZMsg(@"删除订单") forState:0];
        [self.btn1 setTitleColor:[UIColor grayColor] forState:0];
        self.btn1.layer.borderColor = [UIColor grayColor].CGColor;

    }
    self.orderNumLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"订单号:"), model.orderno];
    self.statusLb.text = model.status_name;
    [self.specImg sd_setImageWithURL:[NSURL URLWithString:model.spec_thumb]];
    self.goodsNameLb.text = model.goods_name;
    self.standardsLb.text = model.spec_name;
    self.priceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), model.price];
    self.buyCountLb.text = [NSString stringWithFormat:@"x%@",model.nums];
    if ([model.status isEqual:@"5"]) {
        NSString *refundTypeStr;
        if ([model.refund_type isEqual:@"0"]) {
            refundTypeStr = YZMsg(@"仅退款");
        }else{
            refundTypeStr = YZMsg(@"退货退款");
        }
        self.countLb.text = [NSString stringWithFormat:@"%@,%@%@",refundTypeStr,YZMsg(@"金额¥"),model.total];
    }else{
        self.countLb.text = [NSString stringWithFormat:YZMsg(@"共%@件商品"),model.nums];
    }
    self.buyernameLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"买家昵称:"), model.user_nickname];
}
- (IBAction)btnClick:(UIButton *)sender {
    if ([sender.titleLabel.text isEqual:YZMsg(@"去发货")]) {
        WaitSendGoodsVC *send = [[WaitSendGoodsVC alloc]init];
        send.orderModel = _model;
        [[XGGAppDelegate sharedAppDelegate]pushViewController:send animated:YES];
    }else if ([sender.titleLabel.text isEqual:YZMsg(@"查看物流")]){
        [self lookExpress:_model];
    }else if ([sender.titleLabel.text isEqual:YZMsg(@"退款详情")]){
        RefundDetailVC *refund = [[RefundDetailVC alloc]init];
        refund.orderModel = _model;
        [[XGGAppDelegate sharedAppDelegate]pushViewController:refund animated:YES];
    }else if([sender.titleLabel.text isEqual:YZMsg(@"删除订单")]){
        [self.delegate cellBtnClickWithModel:_model withTitle:sender.titleLabel.text];
    }else if([sender.titleLabel.text isEqual:YZMsg(@"联系买家")]){
        NSDictionary *userDic = @{
            @"id":_model.uidStr,
            @"user_nickname":_model.user_nickname,
            @"avatar":_model.avatar,
        };
        [[YBMessageManager shareManager] chatWithUser:userDic];
    }
}
-(void)lookExpress:(SellOrderModel *)model{
    NSString *url =[NSString stringWithFormat:@"%@/appapi/express/index",h5url];
    PubH5 *h5VC = [[PubH5 alloc]init];
    h5VC.url = [self addurl:url addModel:model];;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:h5VC animated:YES];

}
//所有h5需要拼接uid和token
-(NSString *)addurl:(NSString *)url addModel:(SellOrderModel *)models{
    return [url stringByAppendingFormat:@"&uid=%@&token=%@&orderid=%@&user_type=seller",[Config getOwnID],[Config getOwnToken],models.idStr];
}

@end
