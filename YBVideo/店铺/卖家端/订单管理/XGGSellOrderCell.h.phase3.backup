//
//  SellOrderCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/18.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "SellOrderModel.h"
NS_ASSUME_NONNULL_BEGIN

@protocol sellOrderCellDelegate <NSObject>

-(void)cellBtnClickWithModel:(SellOrderModel *)model withTitle:(NSString *)title;

@end

@interface SellOrderCell : UITableViewCell

@property (nonatomic, strong)SellOrderModel *model;
@property (weak, nonatomic) IBOutlet UILabel *orderNumLb;
@property (weak, nonatomic) IBOutlet UILabel *statusLb;
@property (weak, nonatomic) IBOutlet UIButton *sendGoodsBtn;
@property (weak, nonatomic) IBOutlet UIButton *btn1;
@property (weak, nonatomic) IBOutlet UIButton *btn2;
@property (weak, nonatomic) IBOutlet UIImageView *specImg;
@property (weak, nonatomic) IBOutlet UILabel *goodsNameLb;
@property (weak, nonatomic) IBOutlet UILabel *standardsLb;
@property (weak, nonatomic) IBOutlet UILabel *priceLb;
@property (weak, nonatomic) IBOutlet UILabel *countLb;
@property (weak, nonatomic) IBOutlet UILabel *buyCountLb;
@property (weak, nonatomic) IBOutlet UILabel *buyernameLb;

@property (assign, nonatomic)id<sellOrderCellDelegate>delegate;

+(SellOrderCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath ;
@end

NS_ASSUME_NONNULL_END
