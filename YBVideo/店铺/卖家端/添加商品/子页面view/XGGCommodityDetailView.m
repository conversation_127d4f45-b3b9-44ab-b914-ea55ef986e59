//
//  CommodityDetailView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/25.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityDetailView.h"
#import "YBImageView.h"
@implementation CommodityDetailView

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        imageArray = [NSMutableArray array];
        imgWidth = (_window_width - 75)/4;

        [self createUI];
    }
    return self;
}

-(void)createUI{
    UILabel*title = [[UILabel alloc]init];
    title.frame = CGRectMake(12, 10, 180, 20);
    title.font = [UIFont boldSystemFontOfSize:14];
    title.text = YZMsg(@"商品详情");
    title.textColor = [UIColor whiteColor];
    [self addSubview:title];
    
    self.contentText = [[MyTextView alloc]init];
    self.contentText.frame = CGRectMake(12, title.bottom+10, _window_width-24, 180);
    self.contentText.font = [UIFont systemFontOfSize:14];
    self.contentText.placeholder = YZMsg(@"建议通过细节说明、规格、搭配、购买须知等多方面描述商品的详情，不超过300个字");
    self.contentText.textColor = [UIColor whiteColor];
    self.contentText.backgroundColor = [UIColor clearColor];
    self.contentText.placeholderColor = [UIColor lightGrayColor];
    self.contentText.returnKeyType = UIReturnKeyDone;
    self.contentText.delegate = self;
    [self addSubview:self.contentText];
    
    
    imgeBtn = [UIButton buttonWithType:0];
    imgeBtn.frame = CGRectMake(15, self.contentText.bottom+15, imgWidth, imgWidth);
    imgeBtn.backgroundColor = RGB_COLOR(@"#302D41", 1);
    [imgeBtn addTarget:self action:@selector(imageBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    imgeBtn.tag = 1000;
    [imgeBtn setImage:[UIImage imageNamed:@"addComm"] forState:0];
    [imgeBtn setTitle:YZMsg(@"详情图") forState:0];
    [imgeBtn setTitleColor:[UIColor grayColor] forState:0];
    imgeBtn.titleLabel.font = [UIFont systemFontOfSize:10];
    imgeBtn = [PublicObj setUpImgDownText:imgeBtn];
    [self addSubview:imgeBtn];

}
- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if ([text isEqualToString:@"\n"]) {
        [self endEditing:YES];
        return NO;//这里返回NO，就代表return键值失效，即页面上按下return，不会出现换行，如果为yes，则输入页面会换行
    }
    return YES;
}

- (void)imageBtnClick:(UIButton *)sender{
        TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:20-imageArray.count delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.modalPresentationStyle = 0;
    imagePC.showSelectBtn = YES;
        imagePC.allowCrop = NO;
        imagePC.allowPickingOriginalPhoto = NO;
        imagePC.oKButtonTitleColorNormal = Pink_Cor;
        imagePC.allowTakePicture = YES;
        imagePC.allowTakeVideo = NO;
        imagePC.allowPickingVideo = NO;
        imagePC.allowPickingMultipleVideo = NO;
        [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}
#pragma mark---------TZImagePickerControllerDelegate------------
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    NSLog(@"------多选择图片--：%@",photos);
    [imageArray addObjectsFromArray:photos];
    [self setImageFrame];
    [self.delegate setCommodityDetailImg:imageArray];
}
#pragma mark-------重新赋值图片坐标----------
-(void)setImageFrame{
    for (UIButton *btn in self.subviews) {
        if (btn.tag >=3000) {
            [btn removeFromSuperview];
        }
    }
    //如果有图片
    if (imageArray.count > 0) {
        if (self.heightEvent) {
            self.heightEvent(330+ imageArray.count/4 *(imgWidth+15));
        }
        for (int i = 0; i < imageArray.count ; i ++) {
            UIButton *btn = [UIButton buttonWithType:0];
            btn.backgroundColor = RGB(250, 250, 250);
            btn.imageView.contentMode = UIViewContentModeScaleAspectFill;
            btn.imageView.clipsToBounds = YES;

            btn.frame =CGRectMake(15+(i%4)*(imgWidth + 15), self.contentText.bottom+(i/4)*(imgWidth + 15), imgWidth, imgWidth);
            btn.tag = 3000+i;
            //如果是编辑图片需要赋值判断：判断图片类型：UIImage或网络图片
            if ([self.fromStr isEqual:@"seller"]) {
                if ([imageArray[i] isKindOfClass:[UIImage class]]) {
                    [btn setImage:imageArray[i] forState:0];
                }else{
                    [btn sd_setImageWithURL:[NSURL URLWithString:imageArray[i]] forState:0];
                }
            }else{
                [btn setImage:imageArray[i] forState:0];
            }
            [btn addTarget:self action:@selector(showImage:) forControlEvents:UIControlEventTouchUpInside];
            [self addSubview:btn];

            UIButton *delBtn = [UIButton buttonWithType:0];
            delBtn.frame = CGRectMake(imgWidth-25, 5, 20, 20);
            [delBtn setBackgroundImage:[UIImage imageNamed:@"comm_del"] forState:0];
            delBtn.tag = 4000+i;
            [delBtn addTarget:self action:@selector(delImageClick:) forControlEvents:UIControlEventTouchUpInside];
            [btn addSubview:delBtn];
        }
    }else{
        //没有图片
        self.heightEvent(330);
        imgeBtn.frame =  CGRectMake(15, self.contentText.bottom+15, imgWidth, imgWidth);
        [imgeBtn setTitle:YZMsg(@"详情图") forState:0];
        return;
    }
    if (imageArray.count == 20) {
        imgeBtn.hidden = YES;
    }else{
        imgeBtn.hidden = NO;
        imgeBtn.frame = CGRectMake(15+(imageArray.count%4)*(imgWidth + 15), self.contentText.bottom+(imageArray.count/4)*(imgWidth + 15), imgWidth, imgWidth);
        [imgeBtn setTitle:[NSString stringWithFormat:@"%ld/20",imageArray.count] forState:0];
        imgeBtn = [PublicObj setUpImgDownText:imgeBtn];
    }
}
#pragma mark------预览图片---------------
-(void)showImage:(UIButton *)sender{
    NSInteger index = sender.tag-3000;
    YBImageView *imgView = [[YBImageView alloc] initWithImageArray:imageArray andIndex:index andMine:NO isDtCell:NO andBlock:^(NSArray * _Nonnull array) {
    }];
    [imgView hideDelete];
    [[UIApplication sharedApplication].keyWindow addSubview:imgView];
}
#pragma mark------删除图片---------------
-(void)delImageClick:(UIButton *)sender{
    NSInteger index = sender.tag-4000;
    [imageArray removeObjectAtIndex:index];
    [self setImageFrame];
    [self.delegate setCommodityDetailImg:imageArray];

}
#pragma mark--------编辑商品赋值-----------------
-(void)setDetaiModelData:(CommodityDetailModel *)detailModel fromWhere:(NSString *)from
{
    _goodsModel = detailModel;
    self.fromStr = from;
    //有图片
    if (detailModel.pictures_format.count > 0) {
        [imageArray addObjectsFromArray:detailModel.pictures_format];
        [self setImageFrame];
        [self.delegate setCommodityDetailImg:imageArray];
    }
}
@end
