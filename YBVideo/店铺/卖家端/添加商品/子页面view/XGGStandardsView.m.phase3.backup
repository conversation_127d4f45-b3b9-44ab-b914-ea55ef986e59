//
//  StandardsView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/26.
//  Copyright © 2020 cat. All rights reserved.
//

#import "StandardsView.h"

@implementation StandardsView

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        self.imageArray = [NSMutableArray array];
        imgWidth = (_window_width - 75)/4;
        [self createUI];

    }
    return self;
}
-(void)createUI{
    NSArray *titleArr = @[YZMsg(@"规格"),YZMsg(@"库存(件)"),YZMsg(@"单价(元)")];
    NSArray *pholderArr = @[YZMsg(@"最多15个字符"),YZMsg(@"最多9999999件"),YZMsg(@"最少1.00元")];

    for (int i = 0; i < titleArr.count; i ++) {
        UILabel *tLb = [[UILabel alloc]init];
        tLb.frame = CGRectMake(12, 50*i, 80, 50);
        tLb.font = [UIFont systemFontOfSize:14];
        tLb.text = titleArr[i];
        tLb.textColor = Normal_TextColor;
        [self addSubview:tLb];
        
        UITextField *tf = [[UITextField alloc]init];
        tf.backgroundColor =RGB_COLOR(@"#302D41", 1);
        tf.textColor = [UIColor whiteColor];
        tf.font = [UIFont systemFontOfSize:14];
//        tf.placeholder = [NSString stringWithFormat:@"  %@",pholderArr[i]];
        tf.layer.cornerRadius = 3;
        tf.layer.masksToBounds = YES;
        tf.returnKeyType = UIReturnKeyDone;
        tf.delegate = self;
        NSMutableAttributedString*holderString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"  %@",pholderArr[i]] attributes:@{NSForegroundColorAttributeName:[UIColor lightGrayColor]}];
        tf.attributedPlaceholder = holderString;

        [self addSubview:tf];
        [tf mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(tLb.mas_right).offset(5);
            make.right.equalTo(self).offset(-20);
            make.centerY.equalTo(tLb);
            make.height.mas_equalTo(30);
        }];
        if ( i == 0) {
            _titleLb = tLb;
            _nameField = tf;
            [_nameField addTarget:self action:@selector(titleTextFieldDidChange:) forControlEvents:UIControlEventEditingChanged];

        }else if (i  == 1){
            _countField = tf;
            _countField.keyboardType = UIKeyboardTypeNumberPad;

        }else{
            _priceField = tf;
            _priceField.keyboardType = UIKeyboardTypeDefault;
            _priceField.delegate = self;
        }
        
    }
    [self layoutIfNeeded];
    
    _imgeBtn = [UIButton buttonWithType:0];
    _imgeBtn.frame = CGRectMake(15, self.priceField.bottom+15, imgWidth, imgWidth);
    _imgeBtn.backgroundColor = RGB_COLOR(@"#302D41", 1);
    [_imgeBtn addTarget:self action:@selector(imageBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    _imgeBtn.tag = 1000;
    [_imgeBtn setImage:[UIImage imageNamed:@"addComm"] forState:0];
    [_imgeBtn setTitle:YZMsg(@"上传图片") forState:0];
    [_imgeBtn setTitleColor:[UIColor grayColor] forState:0];
    _imgeBtn.titleLabel.font = [UIFont systemFontOfSize:10];
    _imgeBtn = [PublicObj setUpImgDownText:_imgeBtn];
    [self addSubview:_imgeBtn];

    _delBtn = [UIButton buttonWithType:0];
    _delBtn.frame = CGRectMake(imgWidth-25, 5, 20, 20);
    [_delBtn setBackgroundImage:[UIImage imageNamed:@"comm_del"] forState:0];
    [_delBtn addTarget:self action:@selector(delImageClick:) forControlEvents:UIControlEventTouchUpInside];
    _delBtn.hidden = YES;
    [_imgeBtn addSubview:_delBtn];
    
    
    self.deleteBtn = [UIButton buttonWithType:0];
    self.deleteBtn.frame = CGRectMake(_window_width-90, 0, 72, 26);
    self.deleteBtn.centerY = _imgeBtn.centerY;
    [self.deleteBtn setTitle:YZMsg(@"删除规格") forState:0];
    [self.deleteBtn setTitleColor:[UIColor grayColor] forState:0];
    self.deleteBtn.titleLabel.font = [UIFont systemFontOfSize:12];
    self.deleteBtn.layer.borderColor = [UIColor grayColor].CGColor;
    self.deleteBtn.layer.borderWidth = 1;
    self.deleteBtn.layer.masksToBounds = YES;
    self.deleteBtn.hidden = YES;
    [self.deleteBtn addTarget:self action:@selector(deleteStandardView:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.deleteBtn];
    
    
    UILabel *line = [[UILabel alloc]init];
    line.frame = CGRectMake(12, _imgeBtn.bottom+5, _window_width-24, 1);
    line.backgroundColor = RGB_COLOR(@"#302D41", 1);
    [self addSubview:line];

}
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    if (textField == _priceField) {
        if (string.length == 0) {
            return YES;
        }
        NSString *checkStr = [textField.text stringByReplacingCharactersInRange:range withString:string];
        
        NSString *regex = @"^\\-?([1-9]\\d*|0)(\\.\\d{0,2})?$";
        return [self isValid:checkStr withRegex:regex];
    }
    return YES;
}
- (BOOL) isValid:(NSString*)checkStr withRegex:(NSString*)regex {
    NSPredicate *predicte = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [predicte evaluateWithObject:checkStr];
}
#pragma mark - UITextViewDelegate
-(void)titleTextFieldDidChange:(UITextField *)textField
{
    NSString *nameString;
    if (textField == _nameField) {
        nameString = _nameField.text;
    }
    NSString *lang = [[[UITextInputMode activeInputModes]firstObject] primaryLanguage]; // 键盘输入模式
    if ([lang isEqualToString:@"zh-Hans"]) { // 简体中文输入，包括简体拼音，健体五笔，简体手写
        UITextRange *selectedRange = [self.nameField markedTextRange];//获取高亮部分
        UITextPosition *position = [self.nameField positionFromPosition:selectedRange.start offset:0];
        //没有高亮选择的字，则对已输入的文字进行字数统计和限制
        if (!position) {
            if (nameString.length > 15) {
                self.nameField.text = [nameString substringToIndex:15];
            }
        }else{
            //有高亮选择的字符串，则暂不对文字进行统计和限制
        }
    }else{
        // 中文输入法以外的直接对其统计限制即可，不考虑其他语种情况
        if (nameString.length > 15) {
            self.nameField.text = [nameString substringToIndex:15];
        }
    }

}

-(BOOL)textFieldShouldReturn:(UITextField *)textField
{
    [textField resignFirstResponder];
    return YES;
}
-(void)deleteStandardView:(UIButton *)sender{
    if (self.deleteEvent) {
        self.deleteEvent(self.index);
    }
}
//删除照片
-(void)delImageClick:(UIButton *)sender{
    [self.imageArray removeObjectAtIndex:0];
    [_imgeBtn setImage:[UIImage imageNamed:@"addComm"] forState:0];
    [_imgeBtn setTitle:YZMsg(@"上传图片") forState:0];
    [_imgeBtn setBackgroundImage:nil forState:0];
    _delBtn.hidden = YES;

}

- (void)imageBtnClick:(UIButton *)sender{
        TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1-self.imageArray.count delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.modalPresentationStyle = 0;
    imagePC.showSelectBtn = YES;
        imagePC.allowCrop = NO;
        imagePC.allowPickingOriginalPhoto = NO;
        imagePC.oKButtonTitleColorNormal = Pink_Cor;
        imagePC.allowTakePicture = YES;
        imagePC.allowTakeVideo = NO;
        imagePC.allowPickingVideo = NO;
        imagePC.allowPickingMultipleVideo = NO;
        [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    NSLog(@"------多选择图片--：%@",photos);
    [self.imageArray addObjectsFromArray:photos];
    [_imgeBtn setImage:nil forState:0];
    [_imgeBtn setTitle:nil forState:0];

    [_imgeBtn setBackgroundImage:self.imageArray[0] forState:0];
    _delBtn.hidden = NO;

}

@end
