//
//  StandardsView.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/26.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TZImagePickerController.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^StandardDeleteEvent)(NSInteger tags);
@interface StandardsView : UIView<TZImagePickerControllerDelegate,UITextFieldDelegate>
{
    

    CGFloat imgWidth;
}
@property (nonatomic, strong)UILabel *titleLb;
@property (nonatomic, strong)UITextField *nameField;
@property (nonatomic, strong)UITextField *countField;
@property (nonatomic, strong)UITextField *priceField;
@property (nonatomic, strong)UIButton *imgeBtn;
@property (nonatomic, strong)UIButton *delBtn;

@property (nonatomic, strong)UIButton *deleteBtn;
@property (nonatomic, assign)int index;
@property (nonatomic, copy)StandardDeleteEvent deleteEvent;
@property (nonatomic, strong)NSMutableArray *imageArray;
@end

NS_ASSUME_NONNULL_END
