//
//  CommodityTitleView.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/24.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TZImagePickerController.h"
#import "CommodityDetailModel.h"

NS_ASSUME_NONNULL_BEGIN
@protocol commodityTitleDelegate <NSObject>

-(void)setCommodityImg:(NSMutableArray *)imgArr;

@end

typedef void(^addHeightEvent)(CGFloat height);
@interface CommodityTitleView : UIView<TZImagePickerControllerDelegate,UITextFieldDelegate>
{
    NSMutableArray *imageArray;
    UIButton *imgeBtn;
    UIButton *videoBtn;
    UIButton *subVideoBtn;
    CGFloat imgWidth;
    BOOL image3Tag;
    BOOL image7Tag;

}
@property(nonatomic, strong)UITextField *titleText;
@property(nonatomic, strong)NSString *videoPath;
@property(nonatomic, strong)NSString *newvideoPath;

@property(nonatomic, strong)UIImage *videoCoverImage;
@property(nonatomic, strong)NSString *fromStr;
@property(nonatomic, strong)CommodityDetailModel *goodsModel;
@property(nonatomic, copy) addHeightEvent heightEvent;

@property(nonatomic, assign)id<commodityTitleDelegate>delegate;

-(void)setDetaiModelData:(CommodityDetailModel *)detailModel fromWhere:(NSString *)from;

@end

NS_ASSUME_NONNULL_END
