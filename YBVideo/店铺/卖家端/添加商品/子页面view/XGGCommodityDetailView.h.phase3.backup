//
//  CommodityDetailView.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/25.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TZImagePickerController.h"
#import "CommodityDetailModel.h"

@protocol commodityDetailDelegate <NSObject>

-(void)setCommodityDetailImg:(NSMutableArray *)imgArr;

@end

NS_ASSUME_NONNULL_BEGIN
typedef void(^addHeightEvent)(CGFloat height);

@interface CommodityDetailView : UIView<TZImagePickerControllerDelegate,UITextViewDelegate>
{
    NSMutableArray *imageArray;
    CGFloat imgWidth;
    UIButton *imgeBtn;

}
@property(nonatomic, strong)MyTextView *contentText;
@property(nonatomic, copy) addHeightEvent heightEvent;
@property(nonatomic, strong)CommodityDetailModel *goodsModel;
@property(nonatomic, strong)NSString *fromStr;

@property(nonatomic, assign)id<commodityDetailDelegate>delegate;

-(void)setDetaiModelData:(CommodityDetailModel *)detailModel fromWhere:(NSString *)from;

@end

NS_ASSUME_NONNULL_END
