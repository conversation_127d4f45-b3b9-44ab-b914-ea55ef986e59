//
//  SelCommodityClassVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SelCommodityClassVC.h"

@interface SelCommodityClassVC ()<UITableViewDelegate,UITableViewDataSource>
{
    NSDictionary *first;
}
@property(nonatomic, strong)UITableView *firstTable;
@property(nonatomic, strong)UITableView *secondTable;

@property(nonatomic, strong)NSArray *firstArr;
@property(nonatomic, strong)NSArray *twoArr;
@property(nonatomic, strong)NSArray *threeArr;

@end

@implementation SelCommodityClassVC
#pragma mark-------请求数据------------------
-(void)requestData{
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          };
    [YBNetworking postWithUrl:@"Seller.getGoodsClass" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            self.firstArr = info;
            [self.firstTable reloadData];
            first = self.firstArr[0];
            //默认给第一条赋值
            [self.firstTable selectRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0] animated:YES scrollPosition:UITableViewScrollPositionTop];
            self.twoArr = [self.firstArr[0] valueForKey:@"two_list"];
            self.threeArr = [self.twoArr[0]valueForKey:@"three_list"];

            [self.secondTable reloadData];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = Normal_BackColor;
    self.titleL.text = YZMsg(@"选择商品类别");
    self.firstArr = [NSArray array];
    self.twoArr = [NSArray array];
    self.threeArr = [NSArray array];
    
    [self.view addSubview:self.firstTable];
    [self.view addSubview:self.secondTable];
    
    [self requestData];
}

-(UITableView *)firstTable{
    if (!_firstTable) {
        _firstTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width*0.4, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
        _firstTable.delegate = self;
        _firstTable.dataSource = self;
        _firstTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _firstTable.backgroundColor = Normal_BackColor;
        _firstTable.backgroundView = nil;
    }
    return _firstTable;
}
-(UITableView *)secondTable{
    if (!_secondTable) {
        _secondTable = [[UITableView alloc]initWithFrame:CGRectMake(_window_width*0.4, 64+statusbarHeight, _window_width*0.6, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
        _secondTable.delegate = self;
        _secondTable.dataSource = self;
        _secondTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _secondTable.backgroundColor = Normal_SubColor;
    }
    return _secondTable;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    if (tableView == self.firstTable) {
        return 1;
    }else{
        return self.twoArr.count;
    }
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (tableView == self.firstTable) {
        return self.firstArr.count;
    }else{
        self.threeArr =[self.twoArr[section] valueForKey:@"three_list"];
        return self.threeArr.count;
    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == self.firstTable) {
        return 80;
    }
    else{
        return 44;
    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    if (tableView == self.firstTable) {
        return 0.01;
    }else{
        return 44;

    }
}
-(UIView*)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    if (tableView == self.firstTable) {
        UIView *view = [[UIView alloc]initWithFrame:CGRectZero];
        return view;
    }else{
        UIView *headView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 44)];
        headView.backgroundColor = Normal_SubColor;
        
        UILabel *title = [[UILabel alloc]initWithFrame:CGRectMake(10, 12, _window_width, 20)];
        title.textColor = [UIColor whiteColor];
        title.font = [UIFont boldSystemFontOfSize:14];
        title.text = [self.twoArr[section] valueForKey:@"gc_name"];
        if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([self.twoArr[section] valueForKey:@"gc_name_en"])]) {
            title.text = [self.twoArr[section] valueForKey:@"gc_name_en"];
        }
        [headView addSubview:title];
        
        return headView;

    }
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    
    if (tableView == self.firstTable) {
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"firstCell"];
        cell.textLabel.font = [UIFont boldSystemFontOfSize:14];
        cell.textLabel.text = minstr([self.firstArr[indexPath.row]valueForKey:@"gc_name"]);
        if ([lagType isEqual:EN] && ![PublicObj checkNull:minstr([self.firstArr[indexPath.row]valueForKey:@"gc_name_en"])]) {
            cell.textLabel.text = minstr([self.firstArr[indexPath.row]valueForKey:@"gc_name_en"]);
        }
        cell.textLabel.textColor = Normal_TextColor;
        cell.backgroundColor = Normal_BackColor;
        UIView* tempView1=[[UIView alloc] initWithFrame:cell.frame];
        tempView1.backgroundColor = Normal_SubColor;
        cell.selectedBackgroundView = tempView1;
        return cell;

    }else{
        self.threeArr =[self.twoArr[indexPath.section] valueForKey:@"three_list"];
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"secondCell"];
        cell.textLabel.font = [UIFont systemFontOfSize:14];
        cell.textLabel.text = minstr([self.threeArr[indexPath.row]valueForKey:@"gc_name"]);
        if ([lagType isEqual:EN] && ![PublicObj checkNull: minstr([self.threeArr[indexPath.row]valueForKey:@"gc_name_en"])]) {
            cell.textLabel.text = minstr([self.threeArr[indexPath.row]valueForKey:@"gc_name_en"]);
        }
        cell.textLabel.textColor = Normal_TextColor;
        cell.backgroundColor = Normal_SubColor;

        return cell;
    }
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == self.firstTable) {
        self.twoArr = [self.firstArr[indexPath.row] valueForKey:@"two_list"];
        self.threeArr = [self.twoArr[indexPath.row]valueForKey:@"three_list"];
        first = self.firstArr[indexPath.row];
        [self.secondTable reloadData];
    }else{
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
        NSDictionary *two = self.twoArr[indexPath.section];
        self.threeArr =[self.twoArr[indexPath.section] valueForKey:@"three_list"];

        NSDictionary *three =self.threeArr[indexPath.row];
        if (self.selClassEvent) {
            self.selClassEvent(first, two, three);
            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        }
    }
}
@end
