//
//  BillCell.m
//  yunbaolive
//
//  Created by ybRR<PERSON> on 2020/2/6.
//  Copyright © 2020 cat. All rights reserved.
//

#import "BillCell.h"

@implementation BillCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(BillCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
   BillCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BillCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"BillCell" owner:nil options:nil]objectAtIndex:0];
    }
    cell.backgroundColor = CellRow_Cor;
    return cell;
}
-(void)setInfoDic:(NSDictionary *)infoDic
{
    self.priceLb.text = minstr([infoDic valueForKey:@"balance"]);
    self.timeLb.text = minstr([infoDic valueForKey:@"addtime"]);
    self.statusLb.text = minstr([infoDic valueForKey:@"result"]);
}
@end
