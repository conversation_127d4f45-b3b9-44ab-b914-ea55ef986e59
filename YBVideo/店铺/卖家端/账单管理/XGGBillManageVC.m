//
//  BillManageVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/6.
//  Copyright © 2020 cat. All rights reserved.
//

#import "BillManageVC.h"
#import "BillCell.h"
//#import "YBWebViewController.h"
#import "GetMoneyVC.h"
@interface BillManageVC ()<UITableViewDelegate, UITableViewDataSource>
{
    UILabel *allIncomeLb;//总收入
    UILabel *transactionLb;//交易中
    UILabel *bottomPrice;  //底部账户余额
    UIView *bottomView;
    int  pageIndex;
    NSDictionary *user_balance;
}
@property(nonatomic, strong)UITableView *billTable;
@property(nonatomic, strong)UIView *headerView;
@property(nonatomic, strong)NSMutableArray *infoArr;
@end

@implementation BillManageVC

-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:YES];
    [self requestBillData];

}

-(void)requestBillData{
    NSString *url = [purl stringByAppendingFormat:@"?service=Seller.getSettlementList"];
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign,
                          @"p":@(pageIndex)
                          };
    
    [YBNetworking postWithUrl:@"Seller.getSettlementList" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            NSArray *listArr = [infos valueForKey:@"list"];
            user_balance =[infos valueForKey:@"user_balance"];
            [self reloadCurrentBalance:user_balance];
            if (pageIndex == 1) {
                [self.infoArr removeAllObjects];
                
                if (listArr.count < 1) {
                    [PublicView showImgNoData:self.billTable name:@"shop_无数据" text:YZMsg(@"无相关结算记录")];
                    [self.billTable reloadData];
                    return ;
                }else{
                    [PublicView hiddenImgNoData:self.billTable];
                }
            }
            [self.infoArr addObjectsFromArray:listArr];
            [self.billTable reloadData];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];
}
-(void)reloadCurrentBalance:(NSDictionary *)dic{
    bottomPrice.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), minstr([dic valueForKey:@"balance"])];
    transactionLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),minstr([user_balance valueForKey:@"wait_settlement"])];
    allIncomeLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),minstr([user_balance valueForKey:@"balance_total"])];

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"账单管理");
    pageIndex = 1;
    _infoArr = [NSMutableArray array];
    self.view.backgroundColor = RGB(252, 249, 252);
    [self.view addSubview:self.billTable];
    
    [self creatBottomView];
}
-(UITableView *)billTable{
    if (!_billTable) {
        _billTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60) style:UITableViewStylePlain];
        _billTable.delegate = self;
        _billTable.dataSource = self;
        _billTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _billTable.backgroundColor = Normal_BackColor;
        _billTable.tableFooterView = [UIView new];
    }
    return _billTable;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.infoArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 60;
}
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return 130;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    BillCell *cell = [BillCell cellWithTab:tableView andIndexPath:indexPath];
    cell.infoDic = self.infoArr[indexPath.row];
    return  cell;
}

-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{

    return self.headerView;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
#pragma mark-------底部view-------
-(void)creatBottomView{
    bottomView = [[UIView alloc]init];
    bottomView.frame = CGRectMake(0, _window_height-60, _window_width, 60);
    bottomView.backgroundColor = Normal_SubColor;
    [self.view addSubview:bottomView];
    
    UILabel *titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(12, 10, 100, 16);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor whiteColor];
    titleLb.text = YZMsg(@"小店账户余额");
    [bottomView addSubview:titleLb];
    
    bottomPrice =[[UILabel alloc]init];
    bottomPrice.frame = CGRectMake(12, titleLb.bottom+5, 100, 20);
    bottomPrice.font = [UIFont boldSystemFontOfSize:16];
    bottomPrice.textColor = [UIColor whiteColor];
    bottomPrice.text = @"¥0.00";
    [bottomView addSubview:bottomPrice];
    
    //收益
    UIButton *profitBtn = [UIButton buttonWithType:0];
    profitBtn.frame = CGRectMake(_window_width-85, bottomView.height/2-13, 75, 26);
    [profitBtn setBackgroundColor:Pink_Cor];
    [profitBtn setTitle:YZMsg(@"提取收益") forState:UIControlStateNormal];
    profitBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [profitBtn setTitleColor:[UIColor whiteColor] forState:0];
    profitBtn.layer.cornerRadius = 2;
    profitBtn.layer.masksToBounds = YES;
    [profitBtn addTarget:self action:@selector(profitBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:profitBtn];
    
    //提现
    UIButton *cashBtn = [UIButton buttonWithType:0];
    cashBtn.frame = CGRectMake(profitBtn.left-85, bottomView.height/2-13, 75, 26);
    cashBtn.layer.borderColor = Pink_Cor.CGColor;
    cashBtn.layer.borderWidth = 1;
    cashBtn.layer.cornerRadius = 2;
    cashBtn.layer.masksToBounds = YES;
    [cashBtn setTitle:YZMsg(@"提现记录") forState:UIControlStateNormal];
    cashBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [cashBtn setTitleColor:Pink_Cor forState:0];
    [cashBtn addTarget:self action:@selector(cashBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:cashBtn];

}

-(UIView *)headerView
{
    if (!_headerView) {
        _headerView = [[UIView alloc]init];
        _headerView.frame = CGRectMake(0, 0, _window_width, 130);
        _headerView.backgroundColor =Normal_Color;
        
        UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _headerView.height-40)];
        backView.backgroundColor = Pink_Cor;
        [_headerView addSubview:backView];
        
        UILabel *title1 = [[UILabel alloc]init];
        title1.frame = CGRectMake(12, 15, _window_width/2, 20);
        title1.font = [UIFont systemFontOfSize:14];
        title1.textColor = RGBA(255,255,255,0.5);
        title1.text = YZMsg(@"累计收入 (元)");
        [backView addSubview:title1];
        
        UILabel *title2 = [[UILabel alloc]init];
        title2.frame = CGRectMake(_window_width/2, 15, _window_width/2-10, 20);
        title2.font = [UIFont systemFontOfSize:14];
        title2.textColor = RGBA(255,255,255,0.5);
        title2.text = YZMsg(@"交易中");
        title2.textAlignment = NSTextAlignmentRight;
        [backView addSubview:title2];

        allIncomeLb = [[UILabel alloc]init];
        allIncomeLb.frame = CGRectMake(12, title1.bottom + 10, _window_width/2-10, 20);
        allIncomeLb.font = [UIFont systemFontOfSize:15];
        allIncomeLb.textColor = [UIColor whiteColor];
        allIncomeLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),minstr([user_balance valueForKey:@"balance_total"])];
        [backView addSubview:allIncomeLb];

//        UIImageView *rightImg = [[UIImageView alloc]init];
//        rightImg.frame = CGRectMake(backView.width-30, title1.bottom + 10, 20, 20);
//        rightImg.image = [UIImage imageNamed:@"right_shixin"];
//        [backView addSubview:rightImg];
        
        transactionLb =[[UILabel alloc]init];
        transactionLb.frame = CGRectMake(_window_width/2, title1.bottom + 10, _window_width/2-10, 20);
        transactionLb.font = [UIFont systemFontOfSize:15];
        transactionLb.textColor = [UIColor whiteColor];
        transactionLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),minstr([user_balance valueForKey:@"wait_settlement"])];
        transactionLb.textAlignment = NSTextAlignmentRight;
        [backView addSubview:transactionLb];
        
        UILabel *bottomLb = [[UILabel alloc]init];
        bottomLb.frame = CGRectMake(12, _headerView.height-30, _window_width-20, 20);
        bottomLb.text = YZMsg(@"结算记录");
        bottomLb.font = [UIFont boldSystemFontOfSize:14];
        bottomLb.textColor = [UIColor grayColor];
        [_headerView addSubview:bottomLb];
    }
    return _headerView;
}

-(void)cashBtnClick{    
    NSString *url =[NSString stringWithFormat:@"%@/appapi/shopcash/index",h5url];

    PubH5 *h5VC = [[PubH5 alloc]init];
    h5VC.url = [self addurl:url];;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:h5VC animated:YES];

}
//所有h5需要拼接uid和token
-(NSString *)addurl:(NSString *)url{
    return [url stringByAppendingFormat:@"&uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
}
-(void)profitBtnClick{
    GetMoneyVC *getVC = [[GetMoneyVC alloc]init];
    getVC.moneyDic = user_balance;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:getVC animated:YES];
}
@end
