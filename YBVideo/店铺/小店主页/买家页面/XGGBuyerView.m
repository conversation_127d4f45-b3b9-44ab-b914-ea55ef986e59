//
//  BuyerView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/1/16.
//  Copyright © 2020 cat. All rights reserved.
//

#import "BuyerView.h"
#import "LookHistoryVC.h"
#import "AddressVC.h"
#import "AccountBalanceVC.h"
#import "OrderListVC.h"
#import "ApplyShopVC.h"
#import "ShopApplyStatusVC.h"
@implementation BuyerView

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self creatUI];
        [self creatSectionOne];
        [self creatSectionTwo];
        [self creatSectionThree];
    }
    return self;
}
#pragma mark -----数据请求-------------
-(void)requstData{
    YBWeakSelf;
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          };

    [YBNetworking postWithUrl:@"Buyer.getHome" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            [weakSelf reloadUIWithData:infos];
            shopStatus = minstr([infos valueForKey:@"apply_status"]);
            if ([shopStatus isEqual:@"1"]) {
                backView3.hidden = YES;
            }else{
                backView3.hidden = NO;
            }
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}

-(void)reloadUIWithData:(NSDictionary *)dic{
     //待付款
    if ([minstr([dic valueForKey:@"wait_payment"]) isEqual:@"0"]) {
        wait_payLb.hidden = YES;
    }else{
        wait_payLb.hidden = NO;
        wait_payLb.text =minstr([dic valueForKey:@"wait_payment"]);
    }
    //待发货
    if ([minstr([dic valueForKey:@"wait_shipment"]) isEqual:@"0"]) {
        wait_sendLb.hidden = YES;
    }else{
        wait_sendLb.hidden = NO;
        wait_sendLb.text =minstr([dic valueForKey:@"wait_shipment"]);
    }
     //待收货
    if ([minstr([dic valueForKey:@"wait_receive"]) isEqual:@"0"]) {
        wait_receiveLb.hidden = YES;
    }else{
        wait_receiveLb.hidden = NO;
        wait_receiveLb.text =minstr([dic valueForKey:@"wait_receive"]);
    }
     //待评价
    if ([minstr([dic valueForKey:@"wait_evaluate"]) isEqual:@"0"]) {
        wait_evaluateLb.hidden = YES;
    }else{
        wait_evaluateLb.hidden = NO;
        wait_evaluateLb.text =minstr([dic valueForKey:@"wait_evaluate"]);
    }
    //退款
    if ([minstr([dic valueForKey:@"refund"]) isEqual:@"0"]) {
        wait_refundLb.hidden = YES;
    }else{
        wait_refundLb.hidden = NO;
        wait_refundLb.text =minstr([dic valueForKey:@"refund"]);
    }

    if([minstr([dic valueForKey:@"apply_status"]) isEqual:@"1"]){
        backView3.hidden = YES;
    }
}
-(void)creatUI{
    
    
    headImg = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 190)];
    headImg.userInteractionEnabled = YES;
    headImg.image = [UIImage imageNamed:@"顶部背景"];
    [self addSubview:headImg];

    UIImageView *avatarImg = [[UIImageView alloc]init];
    [avatarImg sd_setImageWithURL:[NSURL URLWithString:[Config getUserAvatar]]];
    avatarImg.layer.cornerRadius = 25;
    avatarImg.layer.masksToBounds = YES;
    [self addSubview:avatarImg];
    [avatarImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(34+statusbarHeight+20+15);
        make.width.height.mas_equalTo(50);
        make.left.equalTo(self).offset(20);
    }];
    
    UILabel *nameLb = [[UILabel alloc]init];
    nameLb.font =[UIFont boldSystemFontOfSize:15];
    nameLb.textColor = [UIColor whiteColor];
    nameLb.text = [Config getOwnNicename];
    [self addSubview:nameLb];
    [nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(avatarImg.mas_right).offset(5);
        make.centerY.equalTo(avatarImg.mas_centerY);
        make.height.mas_equalTo(18);
    }];
    
}
-(void)creatSectionOne{
    backView1 = [[UIView alloc]init];
    backView1.frame = CGRectMake(15, headImg.bottom-35, _window_width-30, 70);
    backView1.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0];
    backView1.layer.cornerRadius = 5;
    backView1.layer.masksToBounds = YES;
    backView1.layer.shadowColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.05].CGColor;
    backView1.layer.shadowOffset = CGSizeMake(0,0);
    backView1.layer.shadowOpacity = 1;
    backView1.layer.shadowRadius = 3;
    [self addSubview:backView1];

    NSArray *titleArr = @[YZMsg(@"我的地址"),YZMsg(@"账户余额")];//YZMsg(@"浏览记录"),
    NSArray *imageArr = @[YZMsg(@"shop_地址"),YZMsg(@"shop_账户")];//YZMsg(@"shop_记录"),
    for (int i = 0; i < titleArr.count; i ++) {
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(i *backView1.width/2, 10, backView1.width/2, 50);
        [btn setTitle:titleArr[i] forState:0];
        [btn setTitleColor:[UIColor blackColor] forState:0];
        btn.titleLabel.font = [UIFont boldSystemFontOfSize:12];
        btn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        [btn setImage:[UIImage imageNamed:imageArr[i]] forState:0];
        btn = [PublicObj setUpImgDownText:btn];
        btn.tag = 10000+i;
        [btn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
        [backView1 addSubview:btn];
    }

}
-(void)creatSectionTwo{
    backView2 = [[UIView alloc]initWithFrame:CGRectMake(15, backView1.bottom+10, _window_width-30, 120)];
    backView2.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0];
    backView2.layer.shadowColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.05].CGColor;
    backView2.layer.shadowOffset = CGSizeMake(0,0);
    backView2.layer.shadowOpacity = 1;
    backView2.layer.shadowRadius = 3;
    backView2.layer.cornerRadius = 5;
    [self addSubview:backView2];
    
    UILabel *orderTitle = [[UILabel alloc]init];
    orderTitle.frame = CGRectMake(15, 15, 80, 18);
    orderTitle.text =YZMsg(@"我的订单");
    orderTitle.font = SYS_Font(14);
    orderTitle.textColor = [UIColor blackColor];
    [backView2 addSubview:orderTitle];
    
    UIButton *allOrderBtn = [UIButton buttonWithType:0];
    allOrderBtn.frame = CGRectMake(backView2.width-110, 15, 110, 18);
    [allOrderBtn setTitleColor:RGB(200, 200, 200) forState:0];
    [allOrderBtn setTitle:YZMsg(@"查看全部订单") forState:0];
    [allOrderBtn setImage:[UIImage imageNamed:@"shop_right"] forState:0];
    allOrderBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    [allOrderBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, - allOrderBtn.imageView.image.size.width, 0, allOrderBtn.imageView.image.size.width)];
    [allOrderBtn setImageEdgeInsets:UIEdgeInsetsMake(0, allOrderBtn.titleLabel.bounds.size.width, 0, -allOrderBtn.titleLabel.bounds.size.width)];
    allOrderBtn.tag = 10009;
    [allOrderBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

    [backView2 addSubview:allOrderBtn];
    
    NSArray *title2Arr = @[YZMsg(@"待付款"),YZMsg(@"待发货"),YZMsg(@"待收货"),YZMsg(@"待评价"),YZMsg(@"退款")];
    for (int i = 0; i < title2Arr.count; i ++) {
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(i *backView2.width/5, orderTitle.bottom +18, backView1.width/5, 50);
        [btn setTitle:title2Arr[i] forState:0];
        [btn setTitleColor:[UIColor blackColor] forState:0];
        btn.titleLabel.font = [UIFont boldSystemFontOfSize:12];
        btn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"shop_%@",title2Arr[i]]] forState:0];
        btn = [PublicObj setUpImgDownText:btn];
        btn.tag = 20000+i;
        [btn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
        [backView2 addSubview:btn];
        
        UILabel *countlb= [[UILabel alloc]init];
        countlb.frame = CGRectMake(btn.width-20, 0, 12, 12);
        countlb.backgroundColor = Pink_Cor;
        countlb.layer.cornerRadius = 6;
        countlb.layer.masksToBounds = YES;
        countlb.font = [UIFont systemFontOfSize:8];
        countlb.textColor = [UIColor whiteColor];
        countlb.textAlignment = NSTextAlignmentCenter;
        [btn addSubview:countlb];
        
        countlb.hidden = YES;
        if (i ==0 ) {
            wait_payLb = countlb;
        }else if (i == 1){
            wait_sendLb  = countlb;
        }else if (i == 2){
            wait_receiveLb = countlb;
        }else if (i == 3){
            wait_evaluateLb = countlb;
        }else if (i == 4){
            wait_refundLb = countlb;
        }
    }

}
-(void)creatSectionThree{
    backView3 = [[UIView alloc]initWithFrame:CGRectMake(15, backView2.bottom+10, _window_width-30, 50)];
    backView3.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0];
    backView3.layer.shadowColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.05].CGColor;
    backView3.layer.shadowOffset = CGSizeMake(0,0);
    backView3.layer.shadowOpacity = 1;
    backView3.layer.shadowRadius = 3;
    backView3.layer.cornerRadius = 5;
    [self addSubview:backView3];

    
    UILabel *openShopTitle = [[UILabel alloc]init];
    openShopTitle.frame = CGRectMake(15, 15, 180, 20);
    openShopTitle.text =YZMsg(@"我要开店");
    openShopTitle.font = SYS_Font(14);
    openShopTitle.textColor = [UIColor blackColor];
    [backView3 addSubview:openShopTitle];

    UIButton *openShopBtn = [UIButton buttonWithType:0];
    openShopBtn.frame = CGRectMake(backView2.width-130, 15, 130, 18);
    [openShopBtn setTitleColor:RGB(200, 200, 200) forState:0];
    [openShopBtn setTitle:YZMsg(@"开小店,来赚钱") forState:0];
    [openShopBtn setImage:[UIImage imageNamed:@"shop_right"] forState:0];
    openShopBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    [openShopBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, - openShopBtn.imageView.image.size.width, 0, openShopBtn.imageView.image.size.width)];
    [openShopBtn setImageEdgeInsets:UIEdgeInsetsMake(0, openShopBtn.titleLabel.bounds.size.width, 0, -openShopBtn.titleLabel.bounds.size.width)];
    openShopBtn.tag = 30000;
    [openShopBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

    [backView3 addSubview:openShopBtn];
    
    UIButton *clickBtn = [UIButton buttonWithType:0];
    clickBtn.frame = CGRectMake(0, 0, backView3.width, backView3.height);
    clickBtn.tag = 30000;
    [clickBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    [backView3 addSubview:clickBtn];

}

-(void)btnClick:(UIButton *)sender{
    switch (sender.tag) {
//            //浏览记录
//        case 10000:{
//            LookHistoryVC *history = [[LookHistoryVC alloc]init];
//            [[XGGAppDelegate sharedAppDelegate]pushViewController:history animated:YES];
//        }
//            break;
            //我的地址
        case 10000:{
            AddressVC *address = [[AddressVC alloc]init];
            address.titleStr =  YZMsg(@"我的地址");
            [[XGGAppDelegate sharedAppDelegate]pushViewController:address animated:YES];
        }
            break;
            //账户余额
        case 10001:{
            AccountBalanceVC *balance = [[AccountBalanceVC alloc]init];
            [[XGGAppDelegate sharedAppDelegate]pushViewController:balance animated:YES];
        }
            break;
            //全部订单
        case 10009:{
            OrderListVC *order = [[OrderListVC alloc]init];
            order.selectIndex = 0;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:order animated:YES];
        }
            break;
            //待付款
        case 20000:{
            OrderListVC *order = [[OrderListVC alloc]init];
            order.selectIndex = 1;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:order animated:YES];
        }
            break;
            //待发货
        case 20001:{
            OrderListVC *order = [[OrderListVC alloc]init];
            order.selectIndex = 2;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:order animated:YES];
        }
            break;
            //待收货
        case 20002:{
            OrderListVC *order = [[OrderListVC alloc]init];
            order.selectIndex = 3;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:order animated:YES];
        }
            break;
            //待评价
        case 20003:{
            OrderListVC *order = [[OrderListVC alloc]init];
            order.selectIndex = 4;
            [[XGGAppDelegate sharedAppDelegate]pushViewController:order animated:YES];
        }
            break;
            //退款
        case 20004: {
                OrderListVC *order = [[OrderListVC alloc]init];
                order.selectIndex = 5;
                [[XGGAppDelegate sharedAppDelegate]pushViewController:order animated:YES];
            }
            break;
            //我要开店
        case 30000:{
            if ([shopStatus isEqual:@"-1"]) {
                [self getAuthInfo];

            }else{
                ShopApplyStatusVC *status = [[ShopApplyStatusVC alloc]init];
                status.statusStr = shopStatus;
                [[XGGAppDelegate sharedAppDelegate]pushViewController:status animated:YES];
            }
        }
            break;
        default:
            break;
    }
}
-(void)getAuthInfo{
    YBWeakSelf;
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          };

    [YBNetworking postWithUrl:@"Auth.getAuth" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            if (![minstr([infos valueForKey:@"status"]) isEqual:@"1"]) {
                [MBProgressHUD showError:YZMsg(@"请先进行实名认证")];
                return;
            }
            ApplyShopVC *apply = [[ApplyShopVC alloc]init];
            apply.presonDic = infos;
            apply.subEvent = ^{
                [weakSelf  requstData];
            };
            [[XGGAppDelegate sharedAppDelegate]pushViewController:apply animated:YES];

        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];
}

@end
