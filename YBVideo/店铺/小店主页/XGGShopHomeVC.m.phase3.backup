//
//  ShopHomeVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/1/16.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ShopHomeVC.h"
#import "BuyerView.h"
#import "SellerView.h"
@interface ShopHomeVC ()
{
    UILabel *_titleL;

    UIButton *changeBtn;    //切换按钮
    UIButton *backBtn;
    
    BOOL isBuger;
}
@property (nonatomic, strong)BuyerView *buyerView;
@property (nonatomic, strong)SellerView *sellerView;
@end

@implementation ShopHomeVC

-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:YES];
    if (isBuger) {
        [self.buyerView requstData];
    }else{
        [self.sellerView requstData];
//        [self.sellerView getUnreadCount];
    }
}
-(void)doReturn{
    [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = RGB(250, 250, 250);
    isBuger = YES;

    [self.view addSubview:self.buyerView];
    [self.view addSubview:self.sellerView];
    [self creatHeader];
}
-(void)creatHeader{
    backBtn = [UIButton buttonWithType:0];
    backBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
    [backBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
    [backBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:backBtn];
    
    _titleL = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2-80, 34+statusbarHeight, 160, 20)];
    _titleL.font =[UIFont boldSystemFontOfSize:15];
    _titleL.textColor = [UIColor whiteColor];
    _titleL.textAlignment = NSTextAlignmentCenter;
    _titleL.text = self.shop_name;
    [self.view addSubview:_titleL];
    
    changeBtn = [UIButton buttonWithType:0];
    changeBtn.frame = CGRectMake(_window_width-50, 24+statusbarHeight, 40, 40);
    [changeBtn addTarget:self action:@selector(changeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [changeBtn setImage:[UIImage imageNamed:@"shop_切换"] forState:0];
    [self.view addSubview:changeBtn];
    
    if ([self.shop_switch isEqual:@"1"]) {
        [self changeBtnClick];
    }else{
        changeBtn.hidden = YES;
        [self.buyerView requstData];
    }
}
-(void)changeBtnClick{
    isBuger = !isBuger;
    if (isBuger) {
        self.buyerView.hidden = NO;
        self.sellerView.hidden = YES;
        [self.buyerView requstData];
        NSString *name =[NSString stringWithFormat:@"%@%@",self.shop_name,YZMsg(@"(买家端)")];
        NSMutableAttributedString *contentStr = [[NSMutableAttributedString alloc] initWithString:name];
        NSRange redRange = NSMakeRange(name.length-5, 5);
        [contentStr addAttributes:@{NSForegroundColorAttributeName:[UIColor whiteColor],NSFontAttributeName:[UIFont boldSystemFontOfSize:12]} range:redRange];
        _titleL.attributedText =contentStr;
    }else{
        self.buyerView.hidden = YES;
        self.sellerView.hidden = NO;
        [self.sellerView requstData];
        NSString *name =[NSString stringWithFormat:@"%@%@",self.shop_name,YZMsg(@"(卖家端)")];
        NSMutableAttributedString *contentStr = [[NSMutableAttributedString alloc] initWithString:name];
        NSRange redRange = NSMakeRange(name.length-5, 5);
        [contentStr addAttributes:@{NSForegroundColorAttributeName:[UIColor whiteColor],NSFontAttributeName:[UIFont boldSystemFontOfSize:12]} range:redRange];
        _titleL.attributedText =contentStr;
    }
}
-(UIView *)buyerView{
    if (!_buyerView) {
        _buyerView = [[BuyerView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        _buyerView.backgroundColor = [UIColor clearColor];
    }
    return _buyerView;
}
-(UIView *)sellerView{
    if (!_sellerView) {
        _sellerView = [[SellerView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        _sellerView.backgroundColor = [UIColor clearColor];
        _sellerView.hidden = YES;
    }
    return _sellerView;

}
@end
