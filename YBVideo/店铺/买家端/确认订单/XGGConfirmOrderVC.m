//
//  ConfirmOrderVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/13.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ConfirmOrderVC.h"
#import "AddressVC.h"
#import "PayOrderView.h"
#import "OrderDetailVC.h"
@interface ConfirmOrderVC ()<UITableViewDelegate, UITableViewDataSource,addressSeletedDelegate>
{
    UIView *addressView;
    NSDictionary *addressDic;
    UITextField * countFeild;
    UILabel *allprice;
    UILabel *payLb;
    MyTextView *msgText;
    NSArray *addArr;
    
    NSString *orderid;
}
@property (nonatomic, strong)UITableView *orderTable;
@property (nonatomic, strong)PayOrderView *payView;
@end

@implementation ConfirmOrderVC


-(void)requestAddressData{
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };

    [YBNetworking postWithUrl:@"Buyer.addressList" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            addArr = info;
            if (addArr.count < 1) {
//                [PublicView showImgNoData:self.addressTable name:@"shop_无数据" text:@"还没有收货地址"];
            }else{
//                [PublicView hiddenImgNoData:self.addressTable];
                for (int i = 0; i < addArr.count ; i++) {
                    NSDictionary *dic = addArr[i];
                    if ([minstr([dic valueForKey:@"is_default"]) isEqual:@"1"]) {
                        addressDic = dic;
                    }
                }
                NSIndexPath *index = [NSIndexPath indexPathForRow:0 inSection:0];
                [self.orderTable reloadRowsAtIndexPaths:[NSArray arrayWithObjects:index,nil] withRowAnimation:UITableViewRowAnimationNone];
            }
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];


}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"确认订单");
    orderid = @"";
    [self.view addSubview:self.orderTable];
    [self requestAddressData];
    
    [self createBottom];
    
    CGFloat postage = [minstr([self.orderModel.goods_info valueForKey:@"postage"]) floatValue];
    CGFloat price =[minstr([self.orderDic valueForKey:@"price"]) floatValue];
    NSLog(@"iiiiiii----:%@",minstr([self.orderDic valueForKey:@"price"]));
//    NSLog(@"333333333----:%f",(float)[self.orderDic valueForKey:@"price"];
    payLb.text = [NSString stringWithFormat:@"¥%.2f",price *self.countNum+postage];
}
-(void)createBottom{
    UILabel *titlelb = [[UILabel alloc]init];
    titlelb.frame = CGRectMake(15, _window_height-55, 50+20, 40);
    titlelb.font = [UIFont systemFontOfSize:14];
    titlelb.text = YZMsg(@"应付:");
    titlelb.textColor = [UIColor whiteColor];
    [self.view addSubview:titlelb];
    
    payLb = [[UILabel alloc]init];
    payLb.frame = CGRectMake(titlelb.right, _window_height-55, 100, 40);
    payLb.textColor = Pink_Cor;
    payLb.font = [UIFont systemFontOfSize:14];
    [self.view addSubview:payLb];
    
    UIButton *subBtn = [UIButton buttonWithType:0];
    [subBtn setBackgroundColor:Pink_Cor];
    subBtn.frame = CGRectMake(_window_width-165, _window_height-55, 150, 40);
    subBtn.layer.cornerRadius = 20;
    [subBtn setTitle:YZMsg(@"提交订单") forState:0];
    subBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [subBtn setTitleColor:[UIColor whiteColor] forState:0];
    [subBtn addTarget:self action:@selector(subBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:subBtn];
}
-(UITableView *)orderTable{
    if (!_orderTable) {
        _orderTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-70) style:UITableViewStylePlain];
        _orderTable.delegate = self;
        _orderTable.dataSource = self;
        _orderTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _orderTable.backgroundColor = CellRow_Cor;
    }
    return _orderTable;
}

-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 6;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    
    if (indexPath.row == 0) {
        return 80;
    }else if (indexPath.row == 1){
        return 160;
    }else if (indexPath.row == 5){
        return 80;
    }else {
        return 60;
    }
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.row == 0) {
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"addresscell"];

        if (addArr.count < 1) {
            cell.textLabel.text =YZMsg(@"请设置收货人信息");
            cell.textLabel.font = [UIFont systemFontOfSize:14];
            cell.textLabel.textColor = Normal_TextColor;
            cell.selectionStyle = UITableViewCellSelectionStyleNone;

        }else{
            if (addressDic == nil) {
                NSLog(@"jinalile ----------");
                cell.textLabel.text =YZMsg(@"请设置收货人信息");
                cell.textLabel.font = [UIFont systemFontOfSize:14];
                cell.textLabel.textColor = Normal_TextColor;
                cell.selectionStyle = UITableViewCellSelectionStyleNone;


            }else{
//                cell.backgroundColor = RGB(250, 250, 250);

                UILabel *nameLb = [[UILabel alloc]init];
                nameLb.textColor = [UIColor whiteColor];
                nameLb.font = [UIFont systemFontOfSize:14];
                nameLb.text = minstr([addressDic valueForKey:@"name"]);
                [cell addSubview:nameLb];
                [nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(cell).offset(15);
                    make.top.equalTo(cell).offset(15);
                }];
                UILabel *phoneLb =  [[UILabel alloc]init];
                phoneLb.textColor = [UIColor whiteColor];
                phoneLb.font = [UIFont systemFontOfSize:14];
                phoneLb.text = minstr([addressDic valueForKey:@"phone"]);
                [cell addSubview:phoneLb];
                [phoneLb mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(nameLb.mas_right).offset(10);
                    make.centerY.equalTo(nameLb.mas_centerY);
                }];
                
                UIImageView *addImg = [[UIImageView alloc]init];
                addImg.image = [UIImage imageNamed:@"shop_收货地址"];
                [cell addSubview:addImg];
                [addImg mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(nameLb.mas_left);
                    make.top.equalTo(nameLb.mas_bottom).offset(10);
                    make.width.height.mas_equalTo(15);
                }];
                UILabel *addressLb =[[UILabel alloc]init];
                addressLb.textColor = Normal_TextColor;
                addressLb.font = [UIFont systemFontOfSize:12];
                addressLb.text = [NSString stringWithFormat:@"%@ %@ %@ %@",minstr([addressDic valueForKey:@"province"]),minstr([addressDic valueForKey:@"city"]),minstr([addressDic valueForKey:@"area"]),minstr([addressDic valueForKey:@"address"])];

                [cell addSubview:addressLb];
                [addressLb mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(addImg.mas_right).offset(10);
                    make.right.equalTo(cell).offset(-50);
                    make.centerY.equalTo(addImg.mas_centerY);
                }];
            }
        }
        UIImageView *lineImg = [[UIImageView alloc]init];
        lineImg.image = [UIImage imageNamed:@"order_线"];
        [cell addSubview:lineImg];
        [lineImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(cell);
            make.bottom.equalTo(cell.mas_bottom).offset(-8);
            make.height.mas_equalTo(3);
        }];
        UIImageView *rightImgV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"profit_right"]];
        [cell addSubview:rightImgV];
        [rightImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(cell.mas_right).offset(-9);
            make.centerY.equalTo(cell.mas_centerY);
            make.width.height.mas_equalTo(20);
        }];
        cell.backgroundColor = CellRow_Cor;
        return cell;
    }else if (indexPath.row == 1){
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"shopCell"];
        
        UIImageView *shopImg = [[UIImageView alloc]init];
        shopImg.image = [UIImage imageNamed:@"店铺"];
        [cell addSubview:shopImg];
        [shopImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(cell).offset(15);
            make.width.height.mas_equalTo(16);
        }];
        
        UILabel *shopLb = [[UILabel alloc]init];
        shopLb.textColor = Normal_TextColor;
        shopLb.font = [UIFont systemFontOfSize:14];
        shopLb.text =minstr([self.orderModel.shop_info valueForKey:@"name"]);
        [cell addSubview:shopLb];
        [shopLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(shopImg.mas_right).offset(10);
            make.centerY.equalTo(shopImg.mas_centerY);
        }];
        
        UIImageView *headImage = [[UIImageView alloc]init];
        headImage.layer.cornerRadius = 5;
        headImage.layer.masksToBounds = YES;
        [headImage sd_setImageWithURL:[NSURL URLWithString:minstr([self.orderDic valueForKey:@"thumb"])]] ;
        headImage.backgroundColor = RGB(250, 250, 250);
        [cell addSubview:headImage];
        [headImage mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(shopImg);
            make.top.equalTo(shopImg.mas_bottom).offset(15);
            make.height.width.mas_equalTo(90);
        }];
        
        UILabel *nameLb = [[UILabel alloc]init];
        nameLb.textColor = [UIColor whiteColor];
        nameLb.font = [UIFont boldSystemFontOfSize:15];
        nameLb.text =minstr([self.orderModel.goods_info valueForKey:@"name"]);
        [cell addSubview:nameLb];
        [nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(headImage.mas_right).offset(10);
            make.top.equalTo(headImage.mas_top).offset(10);
        }];

        UILabel *standardLb = [[UILabel alloc]init];
        standardLb.textColor = Normal_TextColor;
        standardLb.font = [UIFont systemFontOfSize:14];
        standardLb.text =[NSString stringWithFormat:@"%@ %@",YZMsg(@"规格:"), minstr([self.orderDic valueForKey:@"spec_name"])];
        [cell addSubview:standardLb];
        [standardLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(headImage.mas_right).offset(10);
            make.top.equalTo(nameLb.mas_bottom).offset(10);
        }];
        
        UILabel *priceLb = [[UILabel alloc]init];
        priceLb.textColor = Pink_Cor;
        priceLb.font = [UIFont systemFontOfSize:15];
        priceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),minstr([self.orderDic valueForKey:@"price"])];
        [cell addSubview:priceLb];
        [priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(headImage.mas_right).offset(10);
            make.bottom.equalTo(headImage.mas_bottom);
        }];


        [PublicObj lineViewWithFrame:CGRectMake(15, 160-1, _window_width-30, 1) andColor:Line_Cor andView:cell.contentView];
        cell.backgroundColor = CellRow_Cor;
        return cell;
    }else if (indexPath.row == 2){
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"countCell"];
        
        UILabel *titleCount =[[UILabel alloc]initWithFrame:CGRectMake(15, 60/2-15, 40, 30)];
        titleCount.font = [UIFont systemFontOfSize:14];
        titleCount.textColor = Normal_TextColor;
        titleCount.text = YZMsg(@"数量");
        [cell addSubview:titleCount];
        

        UIButton *addBtn = [UIButton buttonWithType:0];
        addBtn.frame = CGRectMake(_window_width-20-30, 60/2-10, 20, 20);
        [addBtn setImage:[UIImage imageNamed:@"standards_加"] forState:0];
        [addBtn addTarget:self action:@selector(addCount) forControlEvents:UIControlEventTouchUpInside];
        [cell addSubview:addBtn];
        
        countFeild = [[UITextField alloc]init];
        countFeild.frame = CGRectMake(addBtn.left-50, 60/2-15, 40, 30);
        countFeild.textAlignment = NSTextAlignmentCenter;
        countFeild.font = [UIFont systemFontOfSize:14];
        countFeild.backgroundColor = Normal_SubColor;
        countFeild.textColor = Pink_Cor;
        countFeild.text =[NSString stringWithFormat:@"%d",self.countNum];
        countFeild.userInteractionEnabled = NO;
        [cell addSubview:countFeild];
        
        UIButton *reduceBtn = [UIButton buttonWithType:0];
        reduceBtn.frame = CGRectMake(countFeild.left-30, 60/2-10, 20, 20);
        [reduceBtn setImage:[UIImage imageNamed:@"standards_减"] forState:0];
        [reduceBtn addTarget:self action:@selector(reduceBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [cell addSubview:reduceBtn];
        
        [PublicObj lineViewWithFrame:CGRectMake(15, 60-1, _window_width-30, 1) andColor:Line_Cor andView:cell.contentView];
        cell.backgroundColor = CellRow_Cor;
        return cell;
    }else if (indexPath.row == 3){
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"postpagCell"];
        UILabel *titleCount =[[UILabel alloc]initWithFrame:CGRectMake(15, 60/2-15, 40+20, 30)];
        titleCount.font = [UIFont systemFontOfSize:14];
        titleCount.textColor = Normal_TextColor;
        titleCount.text = YZMsg(@"邮费");
        [cell addSubview:titleCount];

        UILabel *postage =[[UILabel alloc]initWithFrame:CGRectMake(_window_width-100, 60/2-15, 85, 30)];
        postage.font = [UIFont systemFontOfSize:14];
        postage.textColor = Pink_Cor;
        postage.textAlignment = NSTextAlignmentRight;
        postage.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),[self.orderModel.goods_info valueForKey:@"postage"]];
        [cell addSubview:postage];

        
        [PublicObj lineViewWithFrame:CGRectMake(15, 60-1, _window_width-30, 1) andColor:Line_Cor andView:cell.contentView];
        cell.backgroundColor = CellRow_Cor;
        return cell;
    }else if (indexPath.row == 4){
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"priceCell"];
        UILabel *titleCount =[[UILabel alloc]initWithFrame:CGRectMake(15, 60/2-15, 40, 30)];
        titleCount.font = [UIFont systemFontOfSize:14];
        titleCount.textColor = Normal_TextColor;
        titleCount.text = YZMsg(@"总计");
        [cell addSubview:titleCount];

        
        allprice =[[UILabel alloc]initWithFrame:CGRectMake(_window_width-100, 60/2-15, 85, 30)];
        allprice.font = [UIFont systemFontOfSize:14];
        allprice.textColor = Pink_Cor;
        allprice.textAlignment = NSTextAlignmentRight;
//        allprice.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),minstr([self.orderDic valueForKey:@"price"])];
        CGFloat price =[minstr([self.orderDic valueForKey:@"price"]) floatValue];
        CGFloat postage = [minstr([self.orderModel.goods_info valueForKey:@"postage"]) floatValue];

        allprice.text = [NSString stringWithFormat:@"¥%.2f",price *self.countNum+postage];

        [cell addSubview:allprice];

        [PublicObj lineViewWithFrame:CGRectMake(15, 60-1, _window_width-30, 1) andColor:Line_Cor andView:cell.contentView];
        cell.backgroundColor = CellRow_Cor;
        return cell;
    }else{
        UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"messageCell"];
        UILabel *titleCount =[[UILabel alloc]initWithFrame:CGRectMake(15, 60/2-15, 80+20, 30)];
        titleCount.font = [UIFont systemFontOfSize:14];
        titleCount.textColor = Normal_TextColor;
        titleCount.text = YZMsg(@"买家留言:");
        [cell addSubview:titleCount];

        
        msgText = [[MyTextView alloc]init];
        msgText.font = [UIFont systemFontOfSize:14];
        msgText.placeholderColor = [UIColor lightGrayColor];
        msgText.placeholder = YZMsg(@"建议留言前先与商家沟通确认");
        msgText.textColor = [UIColor whiteColor];
        msgText.backgroundColor = CellRow_Cor;
        [cell addSubview:msgText];
        [msgText mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(titleCount.mas_top);
            make.left.equalTo(titleCount.mas_right).offset(5);
            make.right.equalTo(cell).offset(-15);
            make.height.mas_equalTo(60);
        }];
        cell.backgroundColor = CellRow_Cor;
        return cell;
    }
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (indexPath.row == 0) {
        AddressVC *address = [[AddressVC alloc]init];
        address.titleStr =  YZMsg(@"我的地址");
        address.from = @"order";
        address.delegate =self;
        [[XGGAppDelegate sharedAppDelegate]pushViewController:address animated:YES];
    }
}
-(void)selectAddress:(NSDictionary *)dic
{
    addressDic = dic;
    addArr = [NSArray arrayWithObject:addressDic];
    NSIndexPath *index = [NSIndexPath indexPathForRow:0 inSection:0];
    [self.orderTable reloadRowsAtIndexPaths:[NSArray arrayWithObjects:index,nil] withRowAnimation:UITableViewRowAnimationNone];

}
-(void)addCount{
    self.countNum+=1;
    countFeild.text =[NSString stringWithFormat:@"%d",self.countNum];
    
    CGFloat postage = [minstr([self.orderModel.goods_info valueForKey:@"postage"]) floatValue];
    CGFloat price =[minstr([self.orderDic valueForKey:@"price"]) floatValue];
    allprice.text = [NSString stringWithFormat:@"%@%.2f",YZMsg(@"¥"),price *self.countNum+postage];
    payLb.text = [NSString stringWithFormat:@"%@%.2f",YZMsg(@"¥"),price *self.countNum+postage];
}
-(void)reduceBtnClick{
    if (self.countNum == 1) {
        [MBProgressHUD showError:YZMsg(@"最小数量为1件")];
        return;
    }
    self.countNum-=1;
    countFeild.text =[NSString stringWithFormat:@"%d",self.countNum];
    
    CGFloat postage = [minstr([self.orderModel.goods_info valueForKey:@"postage"]) floatValue];
    CGFloat price =[minstr([self.orderDic valueForKey:@"price"]) floatValue];
    allprice.text = [NSString stringWithFormat:@"%@%.2f",YZMsg(@"¥"),price *self.countNum+postage];
    payLb.text = [NSString stringWithFormat:@"%@%.2f",YZMsg(@"¥"),price *self.countNum+postage];

}
-(void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

#pragma mark------提交-------
-(void)subBtnClick{
    YBWeakSelf;
    if (addArr.count < 1) {
        [MBProgressHUD showError:YZMsg(@"请设置收货人信息")];
        return;
    }else if (addressDic == nil) {
        [MBProgressHUD showError:YZMsg(@"请设置收货人信息")];
        return;

    }
    NSString *goodid = minstr([self.orderModel.goods_info valueForKey:@"id"]);
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"addressid":minstr([addressDic valueForKey:@"id"]),@"goodsid":goodid,@"spec_id":minstr([self.orderDic valueForKey:@"spec_id"]), @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];
    
    if (self.shareUserid.length > 0 && ![PublicObj checkNull:self.shareUserid]) {
        self.liveuid = @"0";
    }

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"addressid":minstr([addressDic valueForKey:@"id"]),
                          @"goodsid":goodid,
                          @"spec_id":minstr([self.orderDic valueForKey:@"spec_id"]),
                          @"nums":countFeild.text,
                          @"message":msgText.text,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign,
                          @"liveuid":self.liveuid,
                          @"shareuid":self.shareUserid

                          };
    
    [YBNetworking postWithUrl:@"Buyer.createGoodsOrder" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            orderid = minstr([[info firstObject] valueForKey:@"orderid"]);
            [weakSelf showPayView:orderid];
            [MBProgressHUD showError:YZMsg(@"订单提交成功")];

        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}

-(void)showPayView:(NSString *)orderID{
    YBWeakSelf;
    if (_payView) {
        [_payView removeFromSuperview];
        _payView = nil;
    }

    _payView = [[PayOrderView alloc]initWithPrice:payLb.text AndOrderId:orderID AndShopName:self.orderModel.shop_name];
    _payView.frame = CGRectMake(0, 0, _window_width, _window_height);
    _payView.hideEvent = ^(BOOL paySuccess) {
//        if (paySuccess) {
        OrderDetailVC *detail = [[OrderDetailVC alloc]init];
        detail.orderId = orderID;
        detail.fromWhere = @"orderDetail";
        [[XGGAppDelegate sharedAppDelegate]pushViewController:detail animated:YES];

//        }
        [weakSelf.payView removeFromSuperview];
        weakSelf.payView = nil;

    };
//    _payView.hideEvent = ^{
//    };
    [self.view addSubview:_payView];
}
@end
