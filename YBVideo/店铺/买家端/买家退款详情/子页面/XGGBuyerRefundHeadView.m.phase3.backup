//
//  BuyerRefundHeadView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/21.
//  Copyright © 2020 cat. All rights reserved.
//

#import "BuyerRefundHeadView.h"

@implementation BuyerRefundHeadView

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame: frame];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        [self creatUI];
    }
    return self;
}

-(void)creatUI{
    backView = [[UIView alloc]init];
    backView.frame = CGRectMake(0, 0, _window_width, 70);
    backView.backgroundColor = Pink_Cor;
    [self addSubview:backView];
    
    titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(12, 10, _window_width, 20);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor whiteColor];
    [backView addSubview:titleLb];
    
    timeLb = [[UILabel alloc]init];
    timeLb.frame = CGRectMake(12, titleLb.bottom+5, _window_width, 20);
    timeLb.font = [UIFont systemFontOfSize:14];
    timeLb.textColor = [UIColor whiteColor];
    [backView addSubview:timeLb];
    
    
    contentLb = [[UILabel alloc]init];
    contentLb.frame = CGRectMake(12, backView.bottom+15, _window_width-24, 40);
    contentLb.textColor = Normal_TextColor;
    contentLb.font = [UIFont systemFontOfSize:14];
    contentLb.lineBreakMode = NSLineBreakByWordWrapping;
    contentLb.numberOfLines = 0;
    contentLb.hidden = YES;
    [self addSubview:contentLb];

    reRefundBtn = [UIButton buttonWithType:0];
    reRefundBtn.frame = CGRectMake(_window_width-80, backView.bottom+10, 70, 24);
    reRefundBtn.layer.cornerRadius = 5;
    reRefundBtn.layer.borderColor = Pink_Cor.CGColor;
    reRefundBtn.layer.borderWidth = 1;
    reRefundBtn.layer.masksToBounds = YES;
    [reRefundBtn setTitleColor:Pink_Cor forState:0];
    [reRefundBtn setTitle:YZMsg(@"重新申请") forState:0];
    reRefundBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [reRefundBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    reRefundBtn.hidden = YES;
    [self addSubview:reRefundBtn];
    
    masterBtn = [UIButton buttonWithType:0];
    masterBtn.frame =CGRectMake(reRefundBtn.left-80, backView.bottom+10, 70, 24) ;
    masterBtn.layer.cornerRadius = 5;
    masterBtn.layer.borderColor = Normal_TextColor.CGColor;
    masterBtn.layer.borderWidth = 1;
    masterBtn.layer.masksToBounds = YES;
    [masterBtn setTitleColor:Normal_TextColor forState:0];
    [masterBtn setTitle:YZMsg(@"平台介入") forState:0];
    masterBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [masterBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    masterBtn.hidden = YES;
    [self addSubview:masterBtn];

    cancleBtn = [UIButton buttonWithType:0];
    cancleBtn.frame = CGRectMake(masterBtn.left-80, backView.bottom+10, 70, 24);
    cancleBtn.layer.cornerRadius = 5;
    cancleBtn.layer.borderColor = Normal_TextColor.CGColor;
    cancleBtn.layer.borderWidth = 1;
    cancleBtn.layer.masksToBounds = YES;
    [cancleBtn setTitleColor:Normal_TextColor forState:0];
    [cancleBtn setTitle:YZMsg(@"取消申请") forState:0];
    cancleBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [cancleBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    cancleBtn.hidden = YES;
    [self addSubview:cancleBtn];
    
}
-(void)btnClick:(UIButton *)sender{
    if (self.clickEvent) {
        self.clickEvent(sender.titleLabel.text);
    }

}
-(void)setRefundData:(BuyerRefundModel *)models{
    titleLb.text = models.refund_status_name;
    timeLb.text = models.refund_status_time;
    if ([models.refund_status isEqual:@"0"] && [models.is_reapply isEqual:@"1"] &&[models.is_platform isEqual:@"1"]) {
        reRefundBtn.hidden = NO;
        cancleBtn.hidden = NO;
        masterBtn.hidden = NO;
        cancleBtn.frame = CGRectMake(reRefundBtn.left-80, backView.bottom+10, 70, 24) ;
        masterBtn.frame =CGRectMake(cancleBtn.left-80, backView.bottom+10, 70, 24);
    }else if ([models.refund_status isEqual:@"0"] && [models.is_reapply isEqual:@"1"]&&[models.is_platform isEqual:@"0"]){
        reRefundBtn.hidden = NO;
        cancleBtn.hidden = NO;
    }else if ([models.refund_status isEqual:@"0"] && [models.is_reapply isEqual:@"0"]&&[models.is_platform isEqual:@"0"]){
        reRefundBtn.hidden = YES;
        cancleBtn.hidden = NO;
        masterBtn.hidden = YES;
        cancleBtn.frame = CGRectMake(_window_width-80, backView.bottom+10, 70, 24);
    }else if ([models.refund_status isEqual:@"0"] && [models.is_reapply isEqual:@"0"]&&[models.is_platform isEqual:@"1"]){
        cancleBtn.hidden = NO;
        masterBtn.hidden = NO;
        masterBtn.frame = CGRectMake(_window_width-80, backView.bottom+10, 70, 24);
        cancleBtn.frame = CGRectMake(masterBtn.left-80, backView.bottom+10, 70, 24);

    }else {
        reRefundBtn.hidden = YES;
        cancleBtn.hidden = YES;
        masterBtn.hidden = YES;
        contentLb.hidden = NO;
        contentLb.text =models.refund_status_desc;
    }
}
@end
