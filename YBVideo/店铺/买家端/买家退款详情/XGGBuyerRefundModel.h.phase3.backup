//
//  BuyerRefundModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/21.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface BuyerRefundModel : NSObject

@property (nonatomic, strong)NSDictionary *order_info;
@property (nonatomic, strong)NSDictionary *refund_info;
@property (nonatomic, strong)NSDictionary *shop_info;

@property (nonatomic, strong)NSString *spec_thumb_format;
@property (nonatomic, strong)NSString *goods_name;
@property (nonatomic, strong)NSString *spec_name;
@property (nonatomic, strong)NSString *price;
@property (nonatomic, strong)NSString *total;

@property (nonatomic, strong)NSString *nums;

@property (nonatomic, strong)NSString *refund_reason;
@property (nonatomic, strong)NSString *refund_content;
@property (nonatomic, strong)NSString *refund_status;
@property (nonatomic, strong)NSString *is_platform;//是否可平台介入 0 否 1 是
@property (nonatomic, strong)NSString *is_platform_interpose;
@property (nonatomic, strong)NSString *is_reapply;//是否可重新申请 0 否 1 是
@property (nonatomic, strong)NSString *refund_status_name;//退款订单状态
@property (nonatomic, strong)NSString *refund_status_desc;//退款订单说明
@property (nonatomic, strong)NSString *refund_shop_result;//店铺处理结果 -1 拒绝 0 处理中 1 同意
@property (nonatomic, strong)NSString *refund_status_time;
@property (nonatomic, strong)NSString *type;//退款方式
@property (nonatomic, strong)NSString *addtime;//申请时间
@property (nonatomic, strong)NSString *shop_refuse_reason;//卖家拒绝的原因
@property (nonatomic, strong)NSString *shop_handle_desc;//卖家拒绝的备注
@property (nonatomic, strong)NSString *service_phone;//客服
@property (nonatomic, strong)NSString *service_uid;//客服uid
@property (nonatomic, strong)NSString *service_avatar;
@property (nonatomic, strong)NSString *service_name;
+(instancetype)modelWithDic:(NSDictionary *)subdic;

@end

NS_ASSUME_NONNULL_END
