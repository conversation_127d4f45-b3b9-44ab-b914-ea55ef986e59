//
//  BuyerRefundModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/21.
//  Copyright © 2020 cat. All rights reserved.
//

#import "BuyerRefundModel.h"

@implementation BuyerRefundModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.order_info = [dic valueForKey:@"order_info"];
        self.refund_info = [dic valueForKey:@"refund_info"];
        self.shop_info = [dic valueForKey:@"shop_info"];

        self.spec_thumb_format = minstr([self.order_info valueForKey:@"spec_thumb_format"]);
        self.goods_name = minstr([self.order_info valueForKey:@"goods_name"]);
        self.price = minstr([self.order_info valueForKey:@"price"]);
        self.total =minstr([self.order_info valueForKey:@"total"]);
        self.nums = minstr([self.order_info valueForKey:@"nums"]);
        self.spec_name = minstr([self.order_info valueForKey:@"spec_name"]);
        self.is_platform_interpose = minstr([self.refund_info valueForKey:@"is_platform_interpose"]);
        self.refund_reason = minstr([self.refund_info valueForKey:@"reason"]);
        self.refund_content = minstr([self.refund_info valueForKey:@"content"]);;
        self.refund_status = minstr([self.refund_info valueForKey:@"status"]);;
        self.is_platform = minstr([self.refund_info valueForKey:@"is_platform"]);;//是否可平台介入 0 否 1 是
        self.is_reapply = minstr([self.refund_info valueForKey:@"is_reapply"]);;//是否可重新申请 0 否 1 是
        self.refund_status_name = minstr([self.refund_info valueForKey:@"status_name"]);;//退款订单状态
        self.refund_status_desc = minstr([self.refund_info valueForKey:@"status_desc"]);;//退款订单说明
        self.refund_shop_result = minstr([self.refund_info valueForKey:@"shop_result"]);;//店铺处理结果
        self.refund_status_time = minstr([self.refund_info valueForKey:@"status_time"]);//时间
        self.type = minstr([self.refund_info valueForKey:@"type"]);//退款方式
        self.addtime = minstr([self.refund_info valueForKey:@"addtime"]);//申请时间

        self.shop_refuse_reason = minstr([self.refund_info valueForKey:@"shop_refuse_reason"]);
        self.shop_handle_desc = minstr([self.refund_info valueForKey:@"shop_handle_desc"]);

        self.service_phone = minstr([self.shop_info valueForKey:@"service_phone"]);
        self.service_uid = minstr([self.shop_info valueForKey:@"uid"]);
        self.service_avatar = minstr([self.shop_info valueForKey:@"avatar"]);
        self.service_name = minstr([self.shop_info valueForKey:@"name"]);


    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)subdic{
    BuyerRefundModel *model = [[BuyerRefundModel alloc]initWithDic:subdic];
    return model;
}

@end
