//
//  AddressModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/12.
//  Copyright © 2020 cat. All rights reserved.
//

#import "AddressModel.h"

@implementation AddressModel
-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.name = minstr([dic valueForKey:@"name"]);//姓名
        self.country = minstr([dic valueForKey:@"country"]);//国家
        self.province = minstr([dic valueForKey:@"province"]);//省
        self.city = minstr([dic valueForKey:@"city"]);//市
        self.area = minstr([dic valueForKey:@"area"]);//区
        self.address = minstr([dic valueForKey:@"address"]);//详细地址
        self.country_code = minstr([dic valueForKey:@"country_code"]);//国家代码
        self.phone = minstr([dic valueForKey:@"phone"]);//电话
        self.is_default = minstr([dic valueForKey:@"is_default"]);//是否默认
        self.idStr = minstr([dic valueForKey:@"id"]);
    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)subdic{
    AddressModel *model = [[AddressModel alloc]initWithDic:subdic];
    return model;
}

@end
