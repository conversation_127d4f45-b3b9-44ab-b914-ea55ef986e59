//
//  AddressModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/12.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AddressModel : NSObject

@property (nonatomic, strong)NSString *name;//姓名
@property (nonatomic, strong)NSString *country;//国家
@property (nonatomic, strong)NSString *province;//省
@property (nonatomic, strong)NSString *city;//市
@property (nonatomic, strong)NSString *area;//区
@property (nonatomic, strong)NSString *address;//详细地址
@property (nonatomic, strong)NSString *country_code;//国家代码
@property (nonatomic, strong)NSString *phone;//电话
@property (nonatomic, strong)NSString *is_default;//是否默认
@property (nonatomic, strong)NSString *idStr;

-(instancetype)initWithDic:(NSDictionary *)dic;
+(instancetype)modelWithDic:(NSDictionary *)subdic;
@end

NS_ASSUME_NONNULL_END
