//
//  AddressCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "AddressCell.h"

@implementation AddressCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    self.nameLb = [[UILabel alloc]init];
    self.nameLb.font = [UIFont systemFontOfSize:14];
    self.nameLb.textColor =[UIColor whiteColor];
    [self.contentView addSubview:self.nameLb];
    [self.nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.top.equalTo(self).offset(10);
        make.height.mas_equalTo(16);
    }];
    
    self.phoneLb = [[UILabel alloc]init];
    self.phoneLb.font = [UIFont systemFontOfSize:14];
    self.phoneLb.textColor =[UIColor whiteColor];
    [self.contentView addSubview:self.phoneLb];
    [self.phoneLb mas_makeConstraints:^(MASConstraintMaker *make) {
           make.left.equalTo(self.nameLb.mas_right).offset(5);
           make.top.equalTo(self).offset(10);
           make.height.mas_equalTo(16);
       }];
    
    self.statusLb = [[UILabel alloc]init];
    self.statusLb.font = [UIFont systemFontOfSize:10];
    self.statusLb.textColor =[UIColor whiteColor];
    self.statusLb.backgroundColor = Pink_Cor;
    self.statusLb.text = YZMsg(@"默认");
    self.statusLb.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:self.statusLb];
    [self.statusLb mas_makeConstraints:^(MASConstraintMaker *make) {
           make.left.equalTo(self.phoneLb.mas_right).offset(5);
           make.top.equalTo(self).offset(10);
           make.height.mas_equalTo(14);
        if ([lagType isEqual:EN]) {
            make.width.mas_equalTo(50);
        }else{
            make.width.mas_equalTo(30);
        }
       }];

    self.addressLb =[[UILabel alloc]init];
    self.addressLb.font = [UIFont systemFontOfSize:12];
    self.addressLb.textColor =[UIColor grayColor];
    self.addressLb.lineBreakMode = NSLineBreakByCharWrapping;
    self.addressLb.numberOfLines = 0;
    [self.contentView addSubview:self.addressLb];
    [self.addressLb mas_makeConstraints:^(MASConstraintMaker *make) {
           make.left.equalTo(self).offset(15);
           make.right.equalTo(self).offset(-40);
           make.top.equalTo(self.nameLb.mas_bottom).offset(8);
           make.height.mas_equalTo(30);
       }];
    
    self.editLb = [[UILabel alloc]init];
    self.editLb.font = [UIFont systemFontOfSize:12];
    self.editLb.textColor = RGB(218, 204, 49);
    self.editLb.text = YZMsg(@"编辑");
    [self.contentView addSubview:self.editLb];
    [self.editLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-15);
        make.centerY.equalTo(self);
        make.height.mas_equalTo(16);

    }];
    
    _editbtn = [UIButton buttonWithType:0];
    [_editbtn addTarget:self action:@selector(editBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:_editbtn];
    [_editbtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.editLb.mas_left);
        make.right.equalTo(self);
        make.height.equalTo(self);
        make.centerY.equalTo(self);
    }];
    
    UILabel *line = [[UILabel alloc]init];
    line.backgroundColor = RGB(49, 47, 66);
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.right.equalTo(self).offset(-15);
        make.height.mas_equalTo(1);
        make.bottom.equalTo(self);

    }];
//    [PublicObj lineViewWithFrame:CGRectMake(15, self.height-1, _window_width-30, 1) andColor:RGB(250, 250, 250) andView:self];
}
-(void)setModel:(AddressModel *)model
{
    _model = model;
    
    self.nameLb.text =model.name;
    self.phoneLb.text = model.phone;
    if ([model.is_default isEqual:@"0"]) {
        self.statusLb.hidden = YES;
    }else{
        self.statusLb.hidden = NO;
    }
    self.addressLb.text = [NSString stringWithFormat:@"%@ %@ %@ %@",model.province,model.city,model.area,model.address];

}
-(void)setRejectModel:(RejectAddressModel *)rejectModel
{
    _rejectModel = rejectModel;
    
    self.nameLb.text =_rejectModel.receiver;
    self.phoneLb.text = _rejectModel.receiver_phone;
    self.statusLb.hidden = YES;
    self.addressLb.text = [NSString stringWithFormat:@"%@ %@ %@ %@",_rejectModel.receiver_province,_rejectModel.receiver_city,_rejectModel.receiver_area,_rejectModel.receiver_address];
}
-(void)editBtnClick{
    [self.delegate editAddressWithModel:_model];
}
@end
