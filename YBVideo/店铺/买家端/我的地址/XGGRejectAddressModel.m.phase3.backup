//
//  RejectAddressModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/11.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RejectAddressModel.h"

@implementation RejectAddressModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.receiver = minstr([dic valueForKey:@"receiver"]);
        self.receiver_phone = minstr([dic valueForKey:@"receiver_phone"]);
        self.receiver_province = minstr([dic valueForKey:@"receiver_province"]);
        self.receiver_city = minstr([dic valueForKey:@"receiver_city"]);
        self.receiver_area = minstr([dic valueForKey:@"receiver_area"]);
        self.receiver_address = minstr([dic valueForKey:@"receiver_address"]);
    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)subdic{
    RejectAddressModel *model = [[RejectAddressModel alloc]initWithDic:subdic];
    return model;
}

@end
