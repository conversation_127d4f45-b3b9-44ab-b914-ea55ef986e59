//
//  AddressCell.h
//  yunbaolive
//
//  Created by ybR<PERSON><PERSON> on 2020/2/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "AddressModel.h"
#import "RejectAddressModel.h"
NS_ASSUME_NONNULL_BEGIN

@protocol addressCellEditDelagate <NSObject>

-(void)editAddressWithModel:(AddressModel *)model;

@end

@interface AddressCell : UITableViewCell

@property(nonatomic, strong)UILabel *nameLb;
@property(nonatomic, strong)UILabel *phoneLb;
@property(nonatomic, strong)UILabel *statusLb;
@property(nonatomic, strong)UILabel *addressLb;
@property(nonatomic, strong)UILabel *editLb;

@property(nonatomic, strong)UIButton *editbtn;
@property (nonatomic, strong)AddressModel *model;
@property (nonatomic, strong)RejectAddressModel *rejectModel;

@property (nonatomic, strong)id<addressCellEditDelagate>delegate;
@end

NS_ASSUME_NONNULL_END
