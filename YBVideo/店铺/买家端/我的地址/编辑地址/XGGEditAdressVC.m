//
//  EditAdressVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "EditAdressVC.h"
@import CoreLocation;

@interface EditAdressVC ()<UIPickerViewDelegate, UIPickerViewDataSource,CLLocationManagerDelegate>
{
    UIView *cityPickBack;
    UIPickerView *cityPicker;
    //省市区-数组
    NSArray *province;
    NSArray *city;
    NSArray *district;
    
    //省市区-字符串
    NSString *provinceStr;
    NSString *cityStr;
    NSString *districtStr;
    NSDictionary *areaDic;
    NSString *selectedProvince;

    NSString *normalProvince;
    NSString *normalCity;
    NSString *normalDistrict;
    CLLocationManager   *_lbsManager;

}
@end

@implementation EditAdressVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    _nameL.text = YZMsg(@"姓名");
    _phoneL.text = YZMsg(@"联系电话");
    _diquL.text = YZMsg(@"所在地区");
    _xiangxiL.text = YZMsg(@"详细地址");
    _setDefaulL.text = YZMsg(@"设为默认地址");
    [_saveBtn setTitle:YZMsg(@"保存并使用") forState:0];
    
    
    
    _viewTop.constant = 64+statusbarHeight;
    self.normalSwitch.onTintColor = Pink_Cor;
    self.normalSwitch.tintColor =[UIColor lightGrayColor];
    self.normalSwitch.backgroundColor = [UIColor clearColor];
    self.titleL.text =YZMsg(@"编辑收货地址");
    if ([self.formWhere isEqual:@"home"]) {
        self.rightBtn.hidden = NO;
        [self.rightBtn setTitleColor:Pink_Cor forState:0];
        [self.rightBtn setTitle:YZMsg(@"删除") forState:0];
        [self setModelData:self.model];
    }
    
    NSBundle *bundle = [NSBundle mainBundle];
    NSString *plistPath = [bundle pathForResource:@"area" ofType:@"plist"];
    areaDic = [[NSDictionary alloc] initWithContentsOfFile:plistPath];

    NSArray *components = [areaDic allKeys];
    NSArray *sortedArray = [components sortedArrayUsingComparator: ^(id obj1, id obj2) {
        
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;
        }
        
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;
        }
        return (NSComparisonResult)NSOrderedSame;
    }];

    NSMutableArray *provinceTmp = [[NSMutableArray alloc] init];
    for (int i=0; i<[sortedArray count]; i++) {
        NSString *index = [sortedArray objectAtIndex:i];
        NSArray *tmp = [[areaDic objectForKey: index] allKeys];
        [provinceTmp addObject: [tmp objectAtIndex:0]];
    }
    //---> //rk_3-7 修复首次加载问题
    province = [[NSArray alloc] initWithArray: provinceTmp];
    NSString *index = [sortedArray objectAtIndex:0];
    //NSString *selected = [province objectAtIndex:0];
    selectedProvince = [province objectAtIndex:0];
    NSDictionary *proviceDic = [NSDictionary dictionaryWithDictionary: [[areaDic objectForKey:index]objectForKey:selectedProvince]];

    NSArray *cityArray = [proviceDic allKeys];
    NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: [cityArray objectAtIndex:0]]];
    //city = [[NSArray alloc] initWithArray: [cityDic allKeys]];

    NSArray *citySortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;//递减
        }
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;//上升
        }
        return (NSComparisonResult)NSOrderedSame;
    }];
    NSMutableArray *m_array = [[NSMutableArray alloc] init];
    for (int i=0; i<[citySortedArray count]; i++) {
        NSString *index = [citySortedArray objectAtIndex:i];
        NSArray *temp = [[proviceDic objectForKey: index] allKeys];
        [m_array addObject: [temp objectAtIndex:0]];
    }
    city = [NSArray arrayWithArray:m_array];
    //<-----------

    NSString *selectedCity = [city objectAtIndex: 0];
    district = [[NSArray alloc] initWithArray: [cityDic objectForKey: selectedCity]];
   
    normalProvince = @"";
    normalCity = @"";
    normalDistrict = @"";

    [self location];

}
#pragma mark------删除----------------
-(void)clickNaviRightBtn
{
    [self delteClick];
}
-(void)delteClick{
    YBWeakSelf;
    UIAlertController *deleteAlert = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"确定删除吗？") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [weakSelf deleteAddress];
    }];
    [sureAction setValue:Pink_Cor forKey:@"_titleTextColor"];
    [cancleAction setValue:[UIColor grayColor] forKey:@"_titleTextColor"];

    [deleteAlert addAction:cancleAction];
    [deleteAlert addAction:sureAction];
    [self presentViewController:deleteAlert animated:YES completion:nil];

}
-(void)deleteAddress{
    NSString *url = [purl stringByAppendingFormat:@"?service="];
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"addressid":self.model.idStr, @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"addressid":self.model.idStr,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    [YBNetworking postWithUrl:@"Buyer.delAddress" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            if (self.addressEvent) {
                self.addressEvent();
            }
            [MBProgressHUD showError:msg];
            [self.navigationController popViewControllerAnimated:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];

}
#pragma mark-------选择地址---------------
- (IBAction)selAddressClick:(id)sender {
    [self.view endEditing:YES];
    if (!cityPickBack) {
        cityPickBack = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        cityPickBack.backgroundColor = RGB_COLOR(@"#000000", 0.3);
        [self.view addSubview:cityPickBack];
        
        UIView *titleView = [[UIView alloc]initWithFrame:CGRectMake(15, _window_height-240, _window_width-30, 40)];
        titleView.backgroundColor = [UIColor whiteColor];
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:titleView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.frame = titleView.bounds;
        maskLayer.path = maskPath.CGPath;
        titleView.layer.mask = maskLayer;
        [cityPickBack addSubview:titleView];
        
        [PublicObj lineViewWithFrame:CGRectMake(0,titleView.height-1, titleView.width, 1) andColor:RGB(240, 240, 240) andView:titleView];

        UIButton *cancleBtn = [UIButton buttonWithType:0];
        cancleBtn.frame = CGRectMake(10, 0, 80, 40);
        cancleBtn.tag = 100;
        [cancleBtn setTitle:YZMsg(@"取消") forState:0];
        cancleBtn.titleLabel.font = [UIFont systemFontOfSize:15];
        [cancleBtn setTitleColor:[UIColor blackColor] forState:0];
        [cancleBtn addTarget:self action:@selector(cityCancleOrSure:) forControlEvents:UIControlEventTouchUpInside];
        [titleView addSubview:cancleBtn];
        UIButton *sureBtn = [UIButton buttonWithType:0];
        sureBtn.frame = CGRectMake(titleView.width-90, 0, 80, 40);
        sureBtn.tag = 101;
        [sureBtn setTitle:YZMsg(@"确定") forState:0];
        sureBtn.titleLabel.font = [UIFont systemFontOfSize:15];
        [sureBtn setTitleColor:Pink_Cor forState:0];
        [sureBtn addTarget:self action:@selector(cityCancleOrSure:) forControlEvents:UIControlEventTouchUpInside];
        [titleView addSubview:sureBtn];
        
        UILabel *titleLb = [[UILabel alloc]initWithFrame:CGRectMake(titleView.width/2-90, 0, 180, 40)];
        titleLb.font = [UIFont systemFontOfSize:15];
        titleLb.textColor = [UIColor blackColor];
        titleLb.textAlignment = NSTextAlignmentCenter;
        titleLb.text = YZMsg(@"选择地区");
        [titleView addSubview:titleLb];

        cityPicker = [[UIPickerView alloc]initWithFrame:CGRectMake(15, _window_height-200, _window_width-30, 190)];
        cityPicker.backgroundColor = [UIColor whiteColor];
        cityPicker.delegate = self;
        cityPicker.dataSource = self;
        cityPicker.showsSelectionIndicator = YES;
        [cityPicker selectRow: 0 inComponent: 0 animated: YES];
        [cityPickBack addSubview:cityPicker];
        
        UIBezierPath *maskPath2 = [UIBezierPath bezierPathWithRoundedRect:cityPicker.bounds byRoundingCorners:UIRectCornerBottomLeft | UIRectCornerBottomRight cornerRadii:CGSizeMake(10, 10)];
        CAShapeLayer *maskLayer2 = [[CAShapeLayer alloc] init];
        maskLayer2.frame = cityPicker.bounds;
        maskLayer2.path = maskPath2.CGPath;
        cityPicker.layer.mask = maskLayer2;
        [self setLocationAddress];

    }else{
        cityPickBack.hidden = NO;
    }

}

- (void)cityCancleOrSure:(UIButton *)button{
    if (button.tag == 100) {
        //return;
    }else{
        NSInteger provinceIndex = [cityPicker selectedRowInComponent: 0];
        NSInteger cityIndex = [cityPicker selectedRowInComponent: 1];
        NSInteger districtIndex = [cityPicker selectedRowInComponent: 2];
        
        provinceStr = [province objectAtIndex: provinceIndex];
        cityStr = [city objectAtIndex: cityIndex];
        districtStr = [district objectAtIndex:districtIndex];
        NSString *dizhi = [NSString stringWithFormat:@"%@ %@ %@",provinceStr,cityStr,districtStr];
        self.addressLb.text = dizhi;
        
    }
    cityPickBack.hidden = YES;
    
}

- (IBAction)saveBtnClick:(id)sender {
    
    if (self.nameField.text.length < 1) {
        [MBProgressHUD showError:YZMsg(@"请输入姓名")];
        return;
    }else if (self.phoneField.text.length < 1){
        [MBProgressHUD showError:YZMsg(@"请输入联系电话")];
        return;
    }else if (self.addressLb.text.length < 1){
        [MBProgressHUD showError:YZMsg(@"请输入所在地区")];
        return;
    }else if (self.addressDetail.text.length < 1){
        [MBProgressHUD showError:YZMsg(@"请输入详细地址")];
        return;
    }
    //1. addNew 新增。 //home 修改
    if ([self.formWhere isEqual:@"addNew"]) {
        [self addNewAddress];
    }else{
        [self editOldAddress];

    }
}
#pragma mark------新增地址---------
-(void)addNewAddress{
    BOOL defaultBool = self.normalSwitch.on;
    NSString *is_default;
    if (defaultBool) {
        is_default = @"1";
    }else{
        is_default = @"0";
    }
    
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"is_default":is_default, @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"username":self.nameField.text,
                          @"phone":self.phoneField.text,
                          @"province":provinceStr,
                          @"city":cityStr,
                          @"area":districtStr,
                          @"address":self.addressDetail.text,
                          @"is_default":is_default,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    [YBNetworking postWithUrl:@"Buyer.addAddress" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            if (self.addressEvent) {
                self.addressEvent();
            }
            [MBProgressHUD showError:msg];
            [self.navigationController popViewControllerAnimated:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];

}
#pragma mark------修改地址---------
-(void)editOldAddress{
    BOOL defaultBool = self.normalSwitch.on;
    NSString *is_default;
    if (defaultBool) {
        is_default = @"1";
    }else{
        is_default = @"0";
    }
    
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"addressid":self.model.idStr, @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"addressid":self.model.idStr,
                          @"username":self.nameField.text,
                          @"phone":self.phoneField.text,
                          @"province":provinceStr,
                          @"city":cityStr,
                          @"area":districtStr,
                          @"address":self.addressDetail.text,
                          @"is_default":is_default,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    
    [YBNetworking postWithUrl:@"Buyer.editAddress" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            if (self.addressEvent) {
                self.addressEvent();
            }
            [MBProgressHUD showError:msg];
            [self.navigationController popViewControllerAnimated:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];

}
-(void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.nameField resignFirstResponder];
    [self.phoneField resignFirstResponder];
    [self.addressDetail resignFirstResponder];
}
//-(void)setModelData:(AddressModel *)model{
-(void)setModelData:(AddressModel *)model
{
    self.nameField.text = model.name;
    self.phoneField.text = model.phone;
    
    provinceStr = model.province;
    cityStr = model.city;
    districtStr = model.area;
    NSString *dizhi = [NSString stringWithFormat:@"%@ %@ %@",provinceStr,cityStr,districtStr];
    self.addressLb.text = dizhi;

    self.addressDetail.text = model.address;
    if ([model.is_default isEqual:@"1"]) {
        self.normalSwitch.on = YES;
    }else{
        self.normalSwitch.on = NO;

    }
}

#pragma mark- Picker Data Source Methods
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    if (pickerView == cityPicker) {
        return 3;
    }
    return 0;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    if (pickerView == cityPicker) {
        if (component == 0) {
            return [province count];
        }
        else if (component == 1) {
            return [city count];
        }
        else {
            return [district count];
        }
    }else{
        return 100;
    }
}


#pragma mark- Picker Delegate Methods

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    if (pickerView == cityPicker) {
        if (component == 0) {
            return [province objectAtIndex: row];
        }
        else if (component == 1) {
            return [city objectAtIndex: row];
        }
        else {
            return [district objectAtIndex: row];
        }
    }else{
        return nil;
    }
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component{
    if (pickerView == cityPicker) {
        if (component == 0) {
            selectedProvince = [province objectAtIndex: row];
            NSDictionary *tmp = [NSDictionary dictionaryWithDictionary: [areaDic objectForKey: [NSString stringWithFormat:@"%ld", row]]];
            NSDictionary *dic = [NSDictionary dictionaryWithDictionary: [tmp objectForKey: selectedProvince]];
            NSArray *cityArray = [dic allKeys];
            NSArray *sortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                
                if ([obj1 integerValue] > [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedDescending;//递减
                }
                if ([obj1 integerValue] < [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedAscending;//上升
                }
                return (NSComparisonResult)NSOrderedSame;
            }];
            
            NSMutableArray *array = [[NSMutableArray alloc] init];
            for (int i=0; i<[sortedArray count]; i++) {
                NSString *index = [sortedArray objectAtIndex:i];
                NSArray *temp = [[dic objectForKey: index] allKeys];
                [array addObject: [temp objectAtIndex:0]];
            }
            
            city = [[NSArray alloc] initWithArray: array];
            
            NSDictionary *cityDic = [dic objectForKey: [sortedArray objectAtIndex: 0]];
            district = [[NSArray alloc] initWithArray: [cityDic objectForKey: [city objectAtIndex: 0]]];
            [cityPicker selectRow: 0 inComponent: 1 animated: YES];
            [cityPicker selectRow: 0 inComponent: 2 animated: YES];
            [cityPicker reloadComponent: 1];
            [cityPicker reloadComponent: 2];
            
        } else if (component == 1) {
            NSString *provinceIndex = [NSString stringWithFormat: @"%ld", [province indexOfObject: selectedProvince]];
            NSDictionary *tmp = [NSDictionary dictionaryWithDictionary: [areaDic objectForKey: provinceIndex]];
            NSDictionary *dic = [NSDictionary dictionaryWithDictionary: [tmp objectForKey: selectedProvince]];
            NSArray *dicKeyArray = [dic allKeys];
            NSArray *sortedArray = [dicKeyArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                
                if ([obj1 integerValue] > [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedDescending;
                }
                
                if ([obj1 integerValue] < [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedAscending;
                }
                return (NSComparisonResult)NSOrderedSame;
            }];
            
            NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [dic objectForKey: [sortedArray objectAtIndex: row]]];
            NSArray *cityKeyArray = [cityDic allKeys];
            
            district = [[NSArray alloc] initWithArray: [cityDic objectForKey: [cityKeyArray objectAtIndex:0]]];
            [cityPicker selectRow: 0 inComponent: 2 animated: YES];
            [cityPicker reloadComponent: 2];
        }
    }else{
        
    }
    
}


- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component {
    if (component == 0) {
        return 80;
    }
    else if (component == 1) {
        return 100;
    }
    else {
        return 115;
    }
}

- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view {
    UILabel *myView = nil;
    if (pickerView == cityPicker) {
        if (component == 0) {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [province objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
        else if (component == 1) {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [city objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
        else {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [district objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
    }
    return myView;
}
-(void)setLocationAddress{
    int provinceIndex = 0;
    int cityIndex = 0;

    NSArray *components = [areaDic allKeys];
    NSArray *sortedArray = [components sortedArrayUsingComparator: ^(id obj1, id obj2) {
        
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;
        }
        
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;
        }
        return (NSComparisonResult)NSOrderedSame;
    }];
    
    NSMutableArray *provinceTmp = [[NSMutableArray alloc] init];
    for (int i=0; i<[sortedArray count]; i++) {
        NSString *index = [sortedArray objectAtIndex:i];
        NSArray *tmp = [[areaDic objectForKey: index] allKeys];
        [provinceTmp addObject: [tmp objectAtIndex:0]];
    }

    if (normalProvince.length > 0) {
        selectedProvince = normalProvince;

        for (int i = 0; i < province.count; i ++) {
            if ([normalProvince isEqual:province[i]]) {
                provinceIndex = i;
                NSString *index = [sortedArray objectAtIndex:i];

                NSDictionary *proviceDic = [NSDictionary dictionaryWithDictionary: [[areaDic objectForKey:index]objectForKey:normalProvince]];
                NSArray *cityArray = [proviceDic allKeys];
                NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: [cityArray objectAtIndex:i]]];

                NSArray *citySortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                    if ([obj1 integerValue] > [obj2 integerValue]) {
                        return (NSComparisonResult)NSOrderedDescending;//递减
                    }
                    if ([obj1 integerValue] < [obj2 integerValue]) {
                        return (NSComparisonResult)NSOrderedAscending;//上升
                    }
                    return (NSComparisonResult)NSOrderedSame;
                }];
                NSMutableArray *m_array = [[NSMutableArray alloc] init];
                for (int i=0; i<[citySortedArray count]; i++) {
                    NSString *index = [citySortedArray objectAtIndex:i];
                    NSArray *temp = [[proviceDic objectForKey: index] allKeys];
                    [m_array addObject: [temp objectAtIndex:0]];
                }
               NSArray *cityArr = [NSArray arrayWithArray:m_array];
                city =[NSArray arrayWithArray:m_array];
                for (int j = 0; j < cityArr.count; j ++) {
                    if ([normalCity isEqual:cityArr[j]]) {
                        cityIndex = j;
                        NSString *keys = [NSString stringWithFormat:@"%d",cityIndex];
                        NSDictionary *dicssss = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: keys]];

                        NSString *selectedCity = [cityArr objectAtIndex: j];
                        district = [[NSArray alloc] initWithArray: [dicssss objectForKey: selectedCity]];

                        NSArray * districtArr = [[NSArray alloc] initWithArray: [dicssss objectForKey: selectedCity]];
                        for (int k = 0; k <districtArr.count; k ++) {
                            if ([normalDistrict isEqual:districtArr[k]]) {
                                [cityPicker selectRow: provinceIndex inComponent: 0 animated: YES];
                                [cityPicker reloadComponent: 1];

                                [cityPicker selectRow: cityIndex inComponent: 1 animated: YES];
                                [cityPicker reloadComponent: 2];

                                [cityPicker selectRow: k inComponent: 2 animated: YES];
                            }
                        }
                    }
                }
            }
        }
    }
    NSLog(@"province===:%@  \n city:%@   \ndistrict:%@",province,city,district);
}
-(void)location{
    if (!_lbsManager) {
        _lbsManager = [[CLLocationManager alloc] init];
        [_lbsManager setDesiredAccuracy:kCLLocationAccuracyBest];
        _lbsManager.delegate = self;
        // 兼容iOS8定位
        CLAuthorizationStatus status = [CLLocationManager authorizationStatus];

        if (kCLAuthorizationStatusDenied == status || kCLAuthorizationStatusRestricted == status) {
            NSLog(@"请打开您的位置服务!");
            NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];

               UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:nil message:[NSString stringWithFormat:YZMsg(@"打开“定位服务”来允许“%@”确定您的位置"),[infoDictionary objectForKey:@"CFBundleDisplayName"]] preferredStyle:UIAlertControllerStyleAlert];
               UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"设置") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                   [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
               }];
               [alertContro addAction:cancleAction];
            [cancleAction setValue:Normal_Color forKey:@"_titleTextColor"];
               UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                   
               }];
               [alertContro addAction:sureAction];
            [sureAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];

            [[[XGGAppDelegate sharedAppDelegate]topViewController]presentViewController:alertContro animated:YES completion:nil];

        }else{
            // 兼容iOS8定位
            SEL requestSelector = NSSelectorFromString(@"requestWhenInUseAuthorization");
            if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined && [_lbsManager respondsToSelector:requestSelector]) {
                [_lbsManager requestWhenInUseAuthorization];  //调用了这句,就会弹出允许框了.
            } else {
                [_lbsManager startUpdatingLocation];
            }
        }
    }
}
- (void)stopLbs {
    [_lbsManager stopUpdatingHeading];
    _lbsManager.delegate = nil;
    _lbsManager = nil;
}
- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    if (status == kCLAuthorizationStatusRestricted || status == kCLAuthorizationStatusDenied) {
        [self stopLbs];
    } else {
        [_lbsManager startUpdatingLocation];
    }
}
- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error
{
    [self stopLbs];
}
- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
{
    CLLocation *newLocatioin = locations[0];
    //zl----待修改
//    liveCity *cityU = [XGGcityDefault myProfile];
//    cityU.lat = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.latitude];
//    cityU.lng = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.longitude];
    CLGeocoder *geocoder = [[CLGeocoder alloc] init];
    [geocoder reverseGeocodeLocation:newLocatioin completionHandler:^(NSArray *placemarks, NSError *error) {
        if (!error)
        {
            CLPlacemark *placeMark = placemarks[0];
            NSString *addr = [NSString stringWithFormat:@"%@%@%@",placeMark.administrativeArea,placeMark.locality,placeMark.subLocality];
            NSLog(@"hhhhhhhh----:%@", addr);
            normalProvince =placeMark.administrativeArea;
            normalCity = placeMark.locality;
            normalDistrict = placeMark.subLocality;
        }
    }];
     [self stopLbs];
}
@end
