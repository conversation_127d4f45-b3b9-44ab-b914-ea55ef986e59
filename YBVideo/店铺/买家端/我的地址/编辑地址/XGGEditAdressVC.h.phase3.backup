//
//  EditAdressVC.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "AddressModel.h"
NS_ASSUME_NONNULL_BEGIN

typedef void(^EditAddressEvent)();

@interface EditAdressVC : YBBaseViewController
@property (weak, nonatomic) IBOutlet UITextField *nameField;
@property (weak, nonatomic) IBOutlet UITextField *phoneField;
@property (weak, nonatomic) IBOutlet UILabel *addressLb;
@property (weak, nonatomic) IBOutlet UITextView *addressDetail;
@property (weak, nonatomic) IBOutlet UISwitch *normalSwitch;
@property (strong, nonatomic) IBOutlet NSLayoutConstraint *viewTop;

@property (nonatomic, strong)NSString *formWhere;
@property (nonatomic, strong)AddressModel *model;

@property (nonatomic, copy)EditAddressEvent addressEvent;



@property (weak, nonatomic) IBOutlet UILabel *nameL;

@property (weak, nonatomic) IBOutlet UILabel *phoneL;
@property (weak, nonatomic) IBOutlet UILabel *diquL;
@property (weak, nonatomic) IBOutlet UILabel *xiangxiL;
@property (weak, nonatomic) IBOutlet UILabel *setDefaulL;
@property (weak, nonatomic) IBOutlet UIButton *saveBtn;

//-(void)setModelData:(AddressModel *)model;
@end

NS_ASSUME_NONNULL_END
