//
//  AddressVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "AddressVC.h"
#import "AddressCell.h"
#import "EditAdressVC.h"
#import "EditSaveAddressVC.h"
#import "AddressModel.h"
#import "RejectAddressModel.h"
@interface AddressVC ()<UITableViewDelegate, UITableViewDataSource,addressCellEditDelagate>
@property (nonatomic, strong)UITableView *addressTable;
@property (nonatomic, strong)NSArray *addArr;
@property(nonatomic,strong)NSArray *models;
@property(nonatomic,strong)NSArray *rejectmodels;

@end


@implementation AddressVC

-(NSArray *)models{
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in self.addArr) {
        AddressModel *model = [AddressModel modelWithDic:dic];
        [array addObject:model];
    }
    _models = array;
    return _models;
}
-(NSArray *)rejectmodels{
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in self.addArr) {
        RejectAddressModel *model = [RejectAddressModel modelWithDic:dic];
        [array addObject:model];
    }
    _rejectmodels = array;
    return _rejectmodels;

}

#pragma mark-----卖家退货地址--------
-(void)requestRejectData{
    NSString *url = [purl stringByAppendingFormat:@"?service=Seller.getReceiverAddress"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          };
    
    [YBNetworking postWithUrl:@"Seller.getReceiverAddress" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            self.addArr = info;
            if (self.addArr.count < 1) {
                [PublicView showImgNoData:self.addressTable name:@"shop_无数据" text:YZMsg(@"还没有退货地址")];
            }else{
                [PublicView hiddenImgNoData:self.addressTable];
                [self.addressTable reloadData];
            }
        }else{
            [MBProgressHUD showError:msg];
        }
        [self.addressTable.mj_header endRefreshing];

        } Fail:^(id fail) {
            [self.addressTable.mj_header endRefreshing];

     }];

}
#pragma mark-------买家数据---------
-(void)requestData{
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };

    [YBNetworking postWithUrl:@"Buyer.addressList" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [self.addressTable.mj_header endRefreshing];

        if (code ==0) {
            self.addArr = info;
            if (self.addArr.count < 1) {
                
                [PublicView showImgNoData:self.addressTable name:@"shop_无数据" text:YZMsg(@"还没有收货地址")];
            }else{
                [PublicView hiddenImgNoData:self.addressTable];
            }
            [self.addressTable reloadData];

        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            [self.addressTable.mj_header endRefreshing];

     }];
}
-(void)viewDidLoad
{
    [super viewDidLoad];
    self.titleL.text  =self.titleStr;
    self.view.backgroundColor =Normal_BackColor;

    [self.view addSubview:self.addressTable];
    _addArr = [NSArray array];
    _models = [NSArray array];
    
    UIButton *addBtn = [UIButton buttonWithType:0];
    addBtn.frame = CGRectMake(15, _window_height-60, _window_width-30, 44);
    [addBtn setBackgroundColor:Pink_Cor];
    [addBtn setTitle:YZMsg(@"新增收货地址") forState:0];
    [addBtn setTitleColor:[UIColor whiteColor] forState:0];
    addBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    addBtn.layer.cornerRadius = 2.5;
    addBtn.layer.masksToBounds = YES;
    [addBtn addTarget:self action:@selector(addAddress) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:addBtn];
    
    if ([self.titleL.text containsString:YZMsg(@"退货")]) {
        addBtn.hidden = YES;
        [self requestRejectData];
    }else{
        [self requestData];

    }
//    if ([self.from isEqual:@"order"]) {
//        addBtn.hidden = YES;
//    }
    
}

#pragma mark ----新增收货地址--------
-(void)addAddress{
    YBWeakSelf;
    EditAdressVC *edit = [[EditAdressVC alloc]initWithNibName:@"EditAdressVC" bundle:nil];
    edit.formWhere = @"addNew";
    edit.addressEvent = ^{
        [weakSelf.addressTable.mj_header beginRefreshing];
    };
    [[XGGAppDelegate sharedAppDelegate]pushViewController:edit animated:YES];
}
-(UITableView *)addressTable{
    if (!_addressTable) {
        _addressTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60) style:UITableViewStylePlain];
        _addressTable.delegate = self;
        _addressTable.dataSource = self;
        _addressTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _addressTable.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            if ([self.titleL.text containsString:YZMsg(@"退货")]) {
                [self requestRejectData];
            }else{
                [self requestData];
            }
        }];
        _addressTable.backgroundColor = Normal_BackColor;


    }
    return _addressTable;
}


#pragma mark----tabelviewDelegate--------
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if ([self.titleL.text containsString:YZMsg(@"退货")]) {
        return self.rejectmodels.count;
    }else{
        return self.models.count;
    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 70;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    AddressCell *cell = [[AddressCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"AddressCell"];
    cell.contentView.backgroundColor = Normal_BackColor;
    if ([self.titleL.text containsString:YZMsg(@"退货")]) {
        cell.editbtn.hidden = YES;
        cell.statusLb.hidden = YES;
        cell.rejectModel = self.rejectmodels[indexPath.row];
    }else{
        cell.model =self.models[indexPath.row];
        cell.delegate = self;
    }
    
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    if ([self.from isEqual:@"order"]) {
        [self.delegate selectAddress:self.addArr[indexPath.row]];
        [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
    }else{
        if ([self.titleL.text containsString:YZMsg(@"退货")]) {
            YBWeakSelf;
            EditSaveAddressVC *address = [[EditSaveAddressVC alloc]init];
            address.addModel =self.rejectmodels[indexPath.row];
            address.sellAddressEvent = ^{
                [weakSelf.addressTable.mj_header beginRefreshing];
            };
            [[XGGAppDelegate sharedAppDelegate]pushViewController:address animated:YES];

        }else{
            YBWeakSelf;
            EditAdressVC *edit = [[EditAdressVC alloc]initWithNibName:@"EditAdressVC" bundle:nil];
            edit.formWhere = @"home";
            edit.model = self.models[indexPath.row];
            edit.addressEvent = ^{
                [weakSelf.addressTable.mj_header beginRefreshing];
            };
            [[XGGAppDelegate sharedAppDelegate]pushViewController:edit animated:YES];

        }

    }
}
-(void)editAddressWithModel:(AddressModel *)model
{
    YBWeakSelf;
    EditAdressVC *edit = [[EditAdressVC alloc]initWithNibName:@"EditAdressVC" bundle:nil];
    edit.formWhere = @"home";
    edit.model = model;
    edit.addressEvent = ^{
        [weakSelf.addressTable.mj_header beginRefreshing];
    };
    [[XGGAppDelegate sharedAppDelegate]pushViewController:edit animated:YES];

}
@end
