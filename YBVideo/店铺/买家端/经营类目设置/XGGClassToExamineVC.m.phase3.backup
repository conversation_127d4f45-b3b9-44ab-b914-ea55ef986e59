//
//  ClassToExamineVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/10/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ClassToExamineVC.h"
#import <YYText/YYLabel.h>
#import <YYText/NSAttributedString+YYText.h>
#import "ClassificationVC.h"

@interface ClassToExamineVC ()

@end

@implementation ClassToExamineVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"经营类目设置");
    self.view.backgroundColor = Normal_BackColor;
    
    UIImageView *statusImg = [[UIImageView alloc]init];
    statusImg.frame = CGRectMake(0, 150, 80, 80);
    statusImg.centerX = self.view.centerX;
    [self.view addSubview:statusImg];
    
    NSString *infosStr;
    if ([self.statusStr isEqual:@"0"]) {
        infosStr = YZMsg(@"管理员正在飞速审核中~");
        statusImg.image = [UIImage imageNamed:@"审核中"];
    }else{
        infosStr = YZMsg(@"您的审核没有通过，请重新提交");
        statusImg.image = [UIImage imageNamed:@"审核失败"];
        infosStr = [infosStr stringByAppendingFormat:@":%@",_reasonStr];
    }
    YYLabel *lb = [[YYLabel alloc]init];
    lb.font = [UIFont systemFontOfSize:14];
    lb.text = infosStr;
    lb.numberOfLines = 0;
    lb.preferredMaxLayoutWidth = _window_width-30;
    lb.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:lb];
    [lb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(statusImg.mas_bottom).offset(10);
        make.centerX.equalTo(statusImg);
        make.width.lessThanOrEqualTo(self.view.mas_width).offset(-30);
    }];
    
    NSMutableAttributedString *textAtt = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@",infosStr]];
    
    [textAtt addAttribute:NSForegroundColorAttributeName value:[UIColor whiteColor] range:textAtt.yy_rangeOfAll];//RGB_COLOR(@"#323232", 1)
    [textAtt addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:14] range:NSMakeRange(0,textAtt.length)];

    if ([self.statusStr isEqual:@"2"]) {
        NSRange clickRange = [[textAtt string]rangeOfString:YZMsg(@"重新提交")];
        [textAtt yy_setTextHighlightRange:clickRange color:RGB_COLOR(@"#168EF4", 1) backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
            ClassificationVC *classVc = [[ClassificationVC alloc]init];
            classVc.fromwhere = @"examine";
            [[XGGAppDelegate sharedAppDelegate]pushViewController:classVc animated:YES];
        }];

    }
    lb.attributedText = textAtt;

}

@end
