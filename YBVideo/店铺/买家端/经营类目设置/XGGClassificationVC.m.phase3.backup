//
//  ClassificationVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/10/26.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ClassificationVC.h"
#import "CommodityClassCell.h"
#import "CommodityClassModel.h"

@interface ClassificationVC ()<UITableViewDelegate, UITableViewDataSource>
{
    NSMutableArray *classArray;
    NSMutableArray *selArr;
    UIButton *sureBtn;
}

@property (nonatomic, strong)UITableView *classTable;
@property (nonatomic, strong)NSArray *modelArr;

@end

@implementation ClassificationVC
-(void)initData{
    
    classArray = [NSMutableArray array];
    selArr = [NSMutableArray array];
//    selArr = self.havaSelArr;

}
-(NSArray *)modelArr{
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in classArray) {
        CommodityClassModel *model = [CommodityClassModel modelWithDic:dic];
        [array addObject:model];
    }
    _modelArr = array;
    return _modelArr;
}


- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"经营类目设置");
    self.view.backgroundColor = RGB(17, 13, 35);
    self.automaticallyAdjustsScrollViewInsets = NO;
    [self initData];
    [self.view addSubview:self.classTable];
    [self addBottomView];
    [self requestData];
}
-(UITableView *)classTable{
    if (!_classTable) {
        _classTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60) style:UITableViewStylePlain];
        _classTable.delegate = self;
        _classTable.dataSource = self;
        _classTable.separatorStyle = 0;
        _classTable.backgroundColor = RGB(17, 13, 35);
    }
    return _classTable;
}
-(void)addBottomView{
    sureBtn = [UIButton buttonWithType:0];
    sureBtn.frame = CGRectMake(15, _window_height-60, _window_width-30, 45);
    [sureBtn setBackgroundColor:Pink_Cor];
    sureBtn.layer.cornerRadius = 5;
    sureBtn.layer.masksToBounds = YES;
    [sureBtn setTitle:YZMsg(@"提交") forState:0];
    sureBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [sureBtn setTitleColor:[UIColor whiteColor] forState:0];
    [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:sureBtn];
    sureBtn.userInteractionEnabled = NO;
}
-(void)sureBtnClick{
    if (selArr.count < 1) {
        [MBProgressHUD showError:YZMsg(@"请选择经营类目")];
        return;
    }
    
    NSString *classIdStr = @"";
    for (int i = 0; i < selArr.count; i ++) {
        CommodityClassModel *mod = selArr[i];
        classIdStr = [classIdStr stringByAppendingFormat:@"%@,",mod.idStr];
    }
    classIdStr = [classIdStr substringToIndex:([classIdStr length]-1)];// 去掉最后一个","
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"classid":classIdStr
                          };

    [YBNetworking postWithUrl:@"Shop.applyBusinessCategory" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [MBProgressHUD showError:msg];
            if ([self.fromwhere isEqual:@"examine"]) {
                [self.navigationController popToViewController:[self.navigationController.viewControllers objectAtIndex:([self.navigationController.viewControllers count] -3)] animated:YES];
            }else{
                [[XGGAppDelegate sharedAppDelegate]popViewController:YES];

            }

        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
    }];

}
#pragma mark ------数据请求----------
-(void)requestData{
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          };

    [YBNetworking postWithUrl:@"Shop.getBusinessCategory" Dic:dic Suc:^(int code, id info, NSString *msg) {
            if (code == 0) {
                classArray = info;
                [self.classTable reloadData];
            }else{
                [MBProgressHUD showError:msg];
            }

        } Fail:^(id fail) {
            
        }];

}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.modelArr.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    CommodityClassCell *cell = [tableView dequeueReusableCellWithIdentifier:@"CommodityClassCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"CommodityClassCell" owner:nil options:nil] lastObject];
    }
    cell.backgroundColor = RGB(24, 21, 43);
    CommodityClassModel *allmodel =self.modelArr[indexPath.row];
    for (CommodityClassModel *model in selArr) {
        if ([model.idStr isEqual:allmodel.idStr]) {
            allmodel.gc_isshow = @"0";
        }
    }
    cell.models = allmodel;

    return cell;
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 90)];
    view.backgroundColor = RGB(24, 21, 43);//RGB(250, 250, 250);
    
    UIView *backView = [[UIView alloc]init];
    backView.frame = CGRectMake(0, 0, _window_width, 50);
    backView.backgroundColor =RGB(17, 13, 35);;
    [view addSubview:backView];
    
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(12, 10, _window_width-20, 30)];
    label.font = [UIFont systemFontOfSize:13];
    label.textColor = RGB(150, 150, 150);//RGB_COLOR(@"#646464", 1);
    label.text = YZMsg(@"请谨慎选择，主营类目设置成功后将不可更改");
    [backView addSubview:label];
    
    UILabel *lb = [[UILabel alloc]init];
    lb.frame = CGRectMake(12, backView.bottom+5, _window_width, 30);
    lb.font = [UIFont systemFontOfSize:16];
    lb.textColor = RGB(150, 150, 150);
    lb.text = YZMsg(@"选择主营类目");
    [view addSubview:lb];
    
    return view;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 90;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.classTable deselectRowAtIndexPath:indexPath animated:YES];

    CommodityClassModel *model = self.modelArr[indexPath.row];
    if ([model.isexists isEqual:@"1"]) {
        return;
    }
     __block  NSInteger indexsss;
     __block BOOL isExist = NO;
     [selArr enumerateObjectsUsingBlock:^(CommodityClassModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj.idStr isEqualToString:model.idStr]) {//数组中已经存在该对象
                    *stop = YES;
                    isExist = YES;
                    indexsss = idx;
              }
    }];
    if (!isExist) {//如果不存在就添加进去
        [selArr addObject:model];
    }else{
        [selArr removeObjectAtIndex:indexsss];
    }
    [self.classTable reloadData];
    NSLog(@"----------selarr:-----1111:%@",selArr);
    if (selArr.count > 0) {
        [sureBtn setBackgroundColor:Pink_Cor];
        sureBtn.userInteractionEnabled = YES;
    }else{
        [sureBtn setBackgroundColor:Pink_Cor];
        sureBtn.userInteractionEnabled = NO;
    }
}

@end
