//
//  EvaluationListModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "EvaluationListModel.h"

@implementation EvaluationListModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        _thumb_format = [dic valueForKey:@"thumb_format"];
        _append_comment = [dic valueForKey:@"append_comment"];

        _avatar = minstr([dic valueForKey:@"avatar"]);
        _content = minstr([dic valueForKey:@"content"]);
        _quality_points = minstr([dic valueForKey:@"quality_points"]);
        _has_append_comment = minstr([dic valueForKey:@"has_append_comment"]);
        _spec_name = minstr([dic valueForKey:@"spec_name"]);
        _user_nickname = minstr([dic valueForKey:@"user_nickname"]);
        _video_thumb = minstr([dic valueForKey:@"video_thumb"]);
        _video_url = minstr([dic valueForKey:@"video_url"]);
        _time_format = minstr([dic valueForKey:@"time_format"]);
        
        if ([_has_append_comment isEqual:@"1"]) {
            _append_thumb_format = [_append_comment valueForKey:@"thumb_format"];
            _append_avatar = minstr([_append_comment valueForKey:@"avatar"]);
            _append_content = minstr([_append_comment valueForKey:@"content"]);
            _append_spec_name = minstr([_append_comment valueForKey:@"spec_name"]);
            _append_user_nickname = minstr([_append_comment valueForKey:@"user_nickname"]);
            _append_video_thumb = minstr([_append_comment valueForKey:@"video_thumb"]);
            _append_video_url = minstr([_append_comment valueForKey:@"video_url"]);
            _append_time_format = minstr([_append_comment valueForKey:@"date_tips"]);
        }
        
        //计算评价content
        CGFloat content_height  = [PublicObj heightOfString:_content andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-50];
        _cellHeight = 70+content_height+10;
        
       CGFloat _imgWidth =( _window_width-100-30)/4;

        NSMutableArray *_allArr = [NSMutableArray array];
        if (_thumb_format.count > 0 && _video_url.length > 0) {
            [_allArr addObject:_video_thumb];
            [_allArr addObjectsFromArray:_thumb_format];
        }else if (_thumb_format.count < 1 && _video_url.length > 0){
            [_allArr addObject:_video_thumb];
            
        }else if (_thumb_format.count > 0 &&  _video_url.length < 2){
            [_allArr addObjectsFromArray:_thumb_format];
        }
        if (_allArr.count > 0 && _allArr.count < 4) {
            _cellHeight += _imgWidth+20;
        }else if (_allArr.count >3)
        {
            _cellHeight +=30+_imgWidth*2;

        }
        _cellHeight += 25;
        if ([_has_append_comment isEqual:@"1"]) {
            CGFloat append_content_height  = [PublicObj heightOfString:_append_content andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-50];
            _cellHeight += 35+append_content_height;
            
            
            NSMutableArray *append_allArr = [NSMutableArray array];
            if (_append_thumb_format.count > 0 && _append_video_url.length > 0) {
                [append_allArr addObject:_append_video_thumb];
                [append_allArr addObjectsFromArray:_append_thumb_format];
            }else if (_append_thumb_format.count < 1 && _append_video_url.length > 0){
                [append_allArr addObject:_append_video_thumb];
                
            }else if (_append_thumb_format.count > 0 &&  _append_video_url.length < 2){
                [append_allArr addObjectsFromArray:_append_thumb_format];
            }
            if (append_allArr.count > 0 && append_allArr.count < 4) {
                _cellHeight += _imgWidth+20;
            }else if (append_allArr.count >3)
            {
                _cellHeight +=30+_imgWidth*2;
            }
        }

    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)subdic{
    EvaluationListModel *model = [[EvaluationListModel alloc]initWithDic:subdic];
    return model;
}

@end
