//
//  EvaluationListCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "EvaluationListCell.h"
#import "ShowDetailVC.h"
#import "YBImageView.h"
@implementation EvaluationListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _imgWidth =( _window_width-100-30)/4;
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    _headImg = [[UIImageView alloc]init];
    _headImg.frame = CGRectMake(15, 15, 24, 24);
    _headImg.layer.cornerRadius = 12;
    _headImg.layer.masksToBounds = YES;
    _headImg.tag = 1000;
    [self.contentView addSubview:_headImg];
    
    _nameLb = [[UILabel alloc]init];
    _nameLb.frame = CGRectMake(_headImg.right+10, 0, _window_width-_headImg.right-10, 20);
    _nameLb.centerY = _headImg.centerY;
    _nameLb.textColor = [UIColor grayColor];
    _nameLb.font = [UIFont systemFontOfSize:14];
    [self.contentView addSubview:_nameLb];
    
    _starView = [[CWStarRateView alloc]initWithFrame:CGRectMake(_nameLb.left, _headImg.bottom+5, 90, 12) numberOfStars:5 andSelImg:Comm_FOREGROUND_STAR_IMAGE_NAME andNormalImg:Comm_BACKGROUND_STAR_IMAGE_NAME];
    _starView.userInteractionEnabled = NO;
    _starView.delegate = self;
    [self.contentView addSubview:_starView];
    
    contentLb = [[UILabel alloc]init];
    contentLb.font = [UIFont systemFontOfSize:14];
    contentLb.numberOfLines = 0;
    contentLb.lineBreakMode = NSLineBreakByWordWrapping;
    contentLb.textColor = [UIColor blackColor];
    [self.contentView addSubview:contentLb];
    
    UILabel *line = [[UILabel alloc]init];
    line.backgroundColor = RGB(240, 240, 240);
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.right.equalTo(self).offset(-15);
        make.bottom.equalTo(self);
        make.height.mas_equalTo(1);
    }];
     dateLb = [[UILabel alloc]init];
     dateLb.font = [UIFont systemFontOfSize:13];
     dateLb.textColor = [UIColor grayColor];
     [self.contentView addSubview:dateLb];
    
    standLb = [[UILabel alloc]init];
    standLb.font = [UIFont systemFontOfSize:13];
    standLb.textColor = [UIColor grayColor];
    [self.contentView addSubview:standLb];

    append_timeLb = [[UILabel alloc]init];
    append_timeLb.font = [UIFont systemFontOfSize:12];
    append_timeLb.numberOfLines = 0;
    append_timeLb.lineBreakMode = NSLineBreakByWordWrapping;
    append_timeLb.textColor = Normal_Color;
    [self.contentView addSubview:append_timeLb];
    
    append_contentLb = [[UILabel alloc]init];
    append_contentLb.font = [UIFont systemFontOfSize:14];
    append_contentLb.numberOfLines = 0;
    append_contentLb.lineBreakMode = NSLineBreakByWordWrapping;
    append_contentLb.textColor = [UIColor blackColor];
    [self.contentView addSubview:append_contentLb];

    
    appenline = [[UILabel alloc]init];
    appenline.backgroundColor = RGB(240, 240, 240);
    [self.contentView addSubview:appenline];

}
-(void)setModel:(EvaluationListModel *)model
{
    NSLog(@"time----:%@  \n ---spec:%@",model.time_format, model.spec_name);
    _model = model;
    [_headImg sd_setImageWithURL:[NSURL URLWithString:model.avatar]];
    _nameLb.text = model.user_nickname;
    _starView.scorePercent = [model.quality_points floatValue]/5;
    CGFloat content_height  = [PublicObj heightOfString:model.content andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-50];
    contentLb.frame  =CGRectMake(_headImg.right+10, _starView.bottom+5, _window_width-_headImg.right-30, content_height+5);
    contentLb.text = model.content;

    for(id tmpView in [self subviews])
    {
        if([tmpView isKindOfClass:[UIImageView class]]){
            UIImageView *selView = (UIImageView *)tmpView;
            if (selView.tag >= 5000) {
                [selView removeFromSuperview];
            }
        }
    }
    if (append_timeLb || append_contentLb) {
        append_timeLb.hidden = YES;
        append_contentLb.hidden = YES;
        appenline.hidden = YES;
    }
    NSMutableArray * _allArr = [NSMutableArray array];
    CGFloat imgY = contentLb.bottom+10;;

    NSLog(@"celllll--------thumb:%@", model.video_thumb);
    if (model.thumb_format.count > 0 && model.video_thumb.length > 10) {
        [_allArr addObject:model.video_thumb];
        [_allArr addObjectsFromArray:model.thumb_format];
        
        for (int i = 0; i < _allArr.count; i ++) {
            if (i < 3) {
                imgY =contentLb.bottom+10;
            }else{
                imgY =contentLb.bottom+20+_imgWidth;
            }
            UIImageView *img = [[UIImageView alloc]init];
            img.frame = CGRectMake(50+(i%3)*(_imgWidth + 10), imgY, _imgWidth, _imgWidth);
            [img sd_setImageWithURL:[NSURL URLWithString:_allArr[i]]];
            img.tag = 5000+i;
            img.userInteractionEnabled = YES;
            [self addSubview:img];
            
            UIButton *tapBtn = [UIButton buttonWithType:0];
            tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
            tapBtn.tag =5000+i;
            [tapBtn addTarget:self action:@selector(detailBtnClick:) forControlEvents:UIControlEventTouchUpInside];
            [img addSubview:tapBtn];
            if (i == 0) {
                [tapBtn setImage:[UIImage imageNamed:@"commodity_播放"] forState:0];
            }
        }
    }else if (model.thumb_format.count < 1 && model.video_thumb.length > 10){
            [_allArr addObject:model.video_thumb];
            imgY =contentLb.bottom+10;
            UIImageView *img = [[UIImageView alloc]init];
            img.frame = CGRectMake(50, imgY, _imgWidth, _imgWidth);
            [img sd_setImageWithURL:[NSURL URLWithString:model.video_thumb]];
            img.tag = 5000;
            img.userInteractionEnabled = YES;
            [self addSubview:img];
        
            UIButton *tapBtn = [UIButton buttonWithType:0];
            tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
            tapBtn.tag =5000;
            [tapBtn addTarget:self action:@selector(detailBtnClick:) forControlEvents:UIControlEventTouchUpInside];

            [img addSubview:tapBtn];
            [tapBtn setImage:[UIImage imageNamed:@"commodity_播放"] forState:0];
            
    }else if (model.thumb_format.count > 0 &&  model.video_thumb.length < 2){
        [_allArr addObjectsFromArray:model.thumb_format];
            for (int i = 0; i < _allArr.count; i ++) {
            if (i < 3) {
                imgY =contentLb.bottom+10;
            }else{
                imgY =contentLb.bottom+20+_imgWidth;
            }
            UIImageView *img = [[UIImageView alloc]init];
            img.frame = CGRectMake(50+(i%3)*(_imgWidth + 10), imgY, _imgWidth, _imgWidth);
            [img sd_setImageWithURL:[NSURL URLWithString:_allArr[i]]];
            img.tag = 5001+i;
            img.userInteractionEnabled = YES;
            [self addSubview:img];
                
            UIButton *tapBtn = [UIButton buttonWithType:0];
            tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
            tapBtn.tag =5001+i;
            [tapBtn addTarget:self action:@selector(detailBtnClick:) forControlEvents:UIControlEventTouchUpInside];
            [img addSubview:tapBtn];

        }
    }
    if (_allArr.count > 0 && _allArr.count < 4) {
        imgY =contentLb.bottom+20+_imgWidth;
    }else if (_allArr.count >3)
    {
        imgY =contentLb.bottom+30+_imgWidth*2;

    }else if(_allArr.count == 0){
        imgY =contentLb.bottom+10;
    }
//    dateLb = [[UILabel alloc]init];
    dateLb.frame = CGRectMake(_headImg.right+10, imgY, 100, 20);
//    dateLb.font = [UIFont systemFontOfSize:13];
//    dateLb.textColor = [UIColor grayColor];
    dateLb.text =model.time_format;
//    [self addSubview:dateLb];
    
//    standLb = [[UILabel alloc]init];
    standLb.frame = CGRectMake(dateLb.right+10, imgY, 100, 20);
//    standLb.font = [UIFont systemFontOfSize:13];
//    standLb.textColor = [UIColor grayColor];
    standLb.text =model.spec_name;
//    [self addSubview:standLb];
    imgY = dateLb.bottom+5;
    //如果有追评论
    if([model.has_append_comment isEqual:@"1"]){
        append_contentLb.hidden = NO;
        append_timeLb.hidden = NO;
        appenline.hidden = NO;
        appenline.frame = CGRectMake(_headImg.right+10, imgY+5, _window_width-_headImg.right-10-15, 1);

        append_timeLb.frame = CGRectMake(_headImg.right+10, imgY+10, _window_width, 18);
//        append_timeLb.text =[NSString stringWithFormat:@"%@%@",model.append_time_format,YZMsg(@"追评")] ;
        append_timeLb.text =[NSString stringWithFormat:@"%@",model.append_time_format] ;
        if ([append_timeLb.text isEqual:YZMsg(@"当日评论")]) {
            append_timeLb.textColor = Pink_Cor;
        }
        CGFloat append_content_height  = [PublicObj heightOfString:model.append_content andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-50];
        append_contentLb.frame  =CGRectMake(_headImg.right+10, append_timeLb.bottom+5, _window_width-_headImg.right-30, append_content_height+5);
        append_contentLb.text = model.append_content;
        
        NSMutableArray * append_allArr = [NSMutableArray array];
        CGFloat append_imgY = append_contentLb.bottom+10;;

        if (model.append_thumb_format.count > 0 && model.append_video_thumb.length > 10) {
            [append_allArr addObject:model.append_video_thumb];
            [append_allArr addObjectsFromArray:model.append_thumb_format];
            
            for (int i = 0; i < append_allArr.count; i ++) {
                if (i < 3) {
                    append_imgY =append_contentLb.bottom+10;
                }else{
                    append_imgY =append_contentLb.bottom+20+_imgWidth;
                }
                UIImageView *img = [[UIImageView alloc]init];
                img.frame = CGRectMake(50+(i%3)*(_imgWidth + 10), append_imgY, _imgWidth, _imgWidth);
                [img sd_setImageWithURL:[NSURL URLWithString:append_allArr[i]]];
                img.tag = 6000+i;
                img.userInteractionEnabled = YES;

                [self addSubview:img];
                
                UIButton *tapBtn = [UIButton buttonWithType:0];
                tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
                tapBtn.tag =6000+i;
                [tapBtn addTarget:self action:@selector(appendDetailBtnClick:) forControlEvents:UIControlEventTouchUpInside];
                [img addSubview:tapBtn];
                if (i == 0) {
                    [tapBtn setImage:[UIImage imageNamed:@"commodity_播放"] forState:0];
                }

            }

        }else if (model.append_thumb_format.count < 1 && model.append_video_thumb.length > 10){
                [append_allArr addObject:model.append_video_thumb];
                append_imgY =append_contentLb.bottom+10;
                UIImageView *img = [[UIImageView alloc]init];
                img.frame = CGRectMake(50, append_imgY, _imgWidth, _imgWidth);
                [img sd_setImageWithURL:[NSURL URLWithString:model.append_video_thumb]];
                img.tag = 6000;
                img.userInteractionEnabled = YES;
            
                [self addSubview:img];
            
                UIButton *tapBtn = [UIButton buttonWithType:0];
                tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
                tapBtn.tag =6000;
                [tapBtn addTarget:self action:@selector(appendDetailBtnClick:) forControlEvents:UIControlEventTouchUpInside];

                [img addSubview:tapBtn];
                [tapBtn setImage:[UIImage imageNamed:@"commodity_播放"] forState:0];

                
        }else if (model.append_thumb_format.count > 0 &&  model.append_video_thumb.length < 2){
            [append_allArr addObjectsFromArray:model.append_thumb_format];
                for (int i = 0; i < append_allArr.count; i ++) {
                if (i < 3) {
                    append_imgY =append_contentLb.bottom+10;
                }else{
                    append_imgY =append_contentLb.bottom+20+_imgWidth;
                }
                UIImageView *img = [[UIImageView alloc]init];
                img.frame = CGRectMake(50+(i%3)*(_imgWidth + 10), append_imgY, _imgWidth, _imgWidth);
                [img sd_setImageWithURL:[NSURL URLWithString:append_allArr[i]]];
                img.tag = 6001+i;
                img.userInteractionEnabled = YES;
                [self addSubview:img];
                    
                UIButton *tapBtn = [UIButton buttonWithType:0];
                tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
                tapBtn.tag =6001+i;
                [tapBtn addTarget:self action:@selector(appendDetailBtnClick:) forControlEvents:UIControlEventTouchUpInside];
                [img addSubview:tapBtn];

            }
        }
    }
}
- (void)starRateView:(CWStarRateView *)starRateView scroePercentDidChange:(CGFloat)newScorePercent{
    NSLog(@"%f",newScorePercent);
}
-(void)detailBtnClick:(UIButton *)sender{
    if(sender.tag == 5000){
        ShowDetailVC *detail = [[ShowDetailVC alloc]init];
        detail.fromStr = @"trendlist";
        NSLog(@"=-=-=-=-=-=-=-:%@",_model.video_url);
        detail.videoPath =_model.video_url;
        detail.deleteEvent = ^(NSString *type) {
        };
        [[XGGAppDelegate sharedAppDelegate]pushViewController:detail animated:NO];
    }else{
        NSInteger index = sender.tag - 5001;
        YBImageView *imgView = [[YBImageView alloc] initWithImageArray:_model.thumb_format andIndex:index andMine:NO isDtCell:NO andBlock:^(NSArray * _Nonnull array) {
        }];
        [[UIApplication sharedApplication].keyWindow addSubview:imgView];

    }
}

-(void)appendDetailBtnClick:(UIButton *)sender{
    if(sender.tag == 6000){
        ShowDetailVC *detail = [[ShowDetailVC alloc]init];
        detail.fromStr = @"trendlist";
        NSLog(@"=-=-=-=-=-=-=-:%@",_model.append_video_url);
        detail.videoPath =_model.append_video_url;
        detail.deleteEvent = ^(NSString *type) {
        };
        [[XGGAppDelegate sharedAppDelegate]pushViewController:detail animated:NO];
    }else{
        NSInteger index = sender.tag - 6001;
        YBImageView *imgView = [[YBImageView alloc] initWithImageArray:_model.append_thumb_format andIndex:index andMine:NO isDtCell:NO andBlock:^(NSArray * _Nonnull array) {
        }];
        [[UIApplication sharedApplication].keyWindow addSubview:imgView];

    }

}
@end
