//
//  EvaluationListModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface EvaluationListModel : NSObject

@property (nonatomic, strong)NSDictionary *append_comment;

@property (nonatomic, strong)NSArray *thumb_format;
@property (nonatomic, strong)NSString *avatar;
@property (nonatomic, strong)NSString *content;
@property (nonatomic, strong)NSString *has_append_comment;
@property (nonatomic, strong)NSString *spec_name;
@property (nonatomic, strong)NSString *user_nickname;
@property (nonatomic, strong)NSString *video_thumb;
@property (nonatomic, strong)NSString *video_url;
@property (nonatomic, strong)NSString *time_format;
@property (nonatomic, strong)NSString *quality_points;
/*****************追评*******/
@property (nonatomic, strong)NSString *append_avatar;
@property (nonatomic, strong)NSString *append_content;
@property (nonatomic, strong)NSString *append_spec_name;
@property (nonatomic, strong)NSString *append_user_nickname;
@property (nonatomic, strong)NSString *append_video_thumb;
@property (nonatomic, strong)NSString *append_video_url;
@property (nonatomic, strong)NSString *append_time_format;
@property (nonatomic, strong)NSArray  *append_thumb_format;

@property (nonatomic, assign)CGFloat cellHeight;
+(instancetype)modelWithDic:(NSDictionary *)subdic;

@end

NS_ASSUME_NONNULL_END
