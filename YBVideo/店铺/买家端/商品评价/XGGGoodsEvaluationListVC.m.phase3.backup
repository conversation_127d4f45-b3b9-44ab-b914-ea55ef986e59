//
//  GoodsEvaluationListVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "GoodsEvaluationListVC.h"
#import "EvaluationListCell.h"
#import "EvaluationListModel.h"
@interface GoodsEvaluationListVC ()<UITableViewDelegate, UITableViewDataSource>
{
    NSMutableArray *evalTypeBtnArray;
    NSString *typeStr;
    NSArray *typeArr;
    int page;
    UIView *headBack;
}
@property (nonatomic, strong)UITableView *listTable;
@property (nonatomic, strong)NSArray *models;
@property (nonatomic, strong)NSMutableArray *datasArr;

@end

@implementation GoodsEvaluationListVC
-(void)requetCommentData{
    NSString *url = [purl stringByAppendingFormat:@"?service=Shop.getGoodsCommentList"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":self.goodsIDStr,
                          @"type":typeStr,
                          @"p":@(page)
                          };
    
    [YBNetworking postWithUrl:@"Shop.getGoodsCommentList" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [self.listTable.mj_header endRefreshing];
        [self.listTable.mj_footer endRefreshing];
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            NSArray *list =[infos valueForKey:@"comment_lists"];
            [self  addHeadView:[infos valueForKey:@"type_nums"]];
            
            if (page == 1) {
                [self.datasArr removeAllObjects];
            }
            [self.datasArr addObjectsFromArray:list];
            [self.listTable reloadData];

            if (self.datasArr.count == 0) {
                [PublicView showImgNoData:self.listTable name:@"shop_无数据" text:YZMsg(@"还没有收到任何评价")];
            }else{
                [PublicView hiddenImgNoData:self.listTable];

            }
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            [self.listTable.mj_header endRefreshing];
            [self.listTable.mj_footer endRefreshing];

     }];

}
-(NSArray *)models
{
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in self.datasArr) {
        EvaluationListModel *model = [EvaluationListModel modelWithDic:dic];
        [array addObject:model];
    }
    _models = array;
    return _models;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"全部评价");
    evalTypeBtnArray = [NSMutableArray array];
    self.datasArr =[NSMutableArray array];
    typeArr = @[@"all",@"img",@"video",@"append"];
    typeStr = typeArr[0];
    page = 1;
    [self requetCommentData];
    
    [self.view addSubview:self.listTable];
}
-(void)addHeadView:(NSDictionary *)dic{
    if (!headBack) {
        headBack = [[UIView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, 85)];
        headBack.backgroundColor = [UIColor whiteColor];
        [self.view addSubview:headBack];
        
        NSArray *arr = @[YZMsg(@"全部"),YZMsg(@"有图"),YZMsg(@"视频"),YZMsg(@"追加")];
        NSArray *keyArr = @[@"all_nums",@"img_nums",@"video_nums",@"append_nums"];
        CGFloat imgY = 0;
        for (int i = 0; i < arr.count; i ++) {
            if (i < 3) {
                imgY = 15;
            }else{
                imgY =15+26+10;
            }
            UIButton *btn = [UIButton buttonWithType:0];
            btn.frame = CGRectMake(15+(i%3)*(90 + 10), imgY, 90, 26);
            [btn setTitle:[NSString stringWithFormat:@"%@(%@)",arr[i],minstr([dic valueForKey:keyArr[i]])] forState:0];
            [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
            [btn setTitleColor:Pink_Cor forState:UIControlStateNormal];
            [btn setBackgroundImage:[UIImage imageNamed:@"evaluation_sel"] forState:UIControlStateSelected];
            [btn setBackgroundImage:[UIImage imageNamed:@"evaluation_normal"] forState:UIControlStateNormal];
            btn.titleLabel.font = [UIFont systemFontOfSize:14];
            [btn addTarget:self action:@selector(evaluateClassClick:) forControlEvents:UIControlEventTouchUpInside];
            btn.tag = 20000+i;
            [headBack addSubview:btn];
            if (i == 0) {
                btn.selected = YES;
            }else{
                btn.selected = NO;
            }
            [evalTypeBtnArray addObject:btn];
        }
        
        [PublicObj lineViewWithFrame:CGRectMake(0, headBack.height-1, _window_width, 1) andColor:RGB(240, 240, 240) andView:headBack];

    }
    
}
- (void)evaluateClassClick:(UIButton *)sender{
    if (sender.selected) {
        return;
    }
    typeStr = [NSString stringWithFormat:@"%@",typeArr[sender.tag -20000]];
    NSLog(@"--=--=--=-=-:%@", typeStr);
    page = 1;
    for (UIButton *btn in evalTypeBtnArray) {
        if (btn == sender) {
            btn.selected = YES;
        }else{
            btn.selected = NO;
        }
    }
    [self requetCommentData];

}

-(UITableView *)listTable{
    if (!_listTable) {
        _listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight+85, _window_width, _window_height-64-statusbarHeight-85) style:UITableViewStylePlain];
        _listTable.delegate = self;
        _listTable.dataSource = self;
        _listTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _listTable.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
            page = 1;
            [self requetCommentData];
        }];
        _listTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
            page ++;
            [self requetCommentData];

        }];
    }
    return _listTable;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.models.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    EvaluationListModel *model = self.models[indexPath.row];
    return model.cellHeight;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSString *CellIdentifier = [NSString stringWithFormat:@"EvaluationListCell%ld%ld",indexPath.section,indexPath.row];
    EvaluationListCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
    if (!cell) {
        cell = [[EvaluationListCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
    }
    EvaluationListModel *modelss = self.models[indexPath.row];
    cell.model = modelss;
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
@end
