//
//  OutsideHeadCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/5/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OutsideHeadCell.h"

@implementation OutsideHeadCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self  = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self createUI];
    }
    return self;
}
-(void)createUI{
    if (!sliderView) {
        sliderView = [[sliderCollectionView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 375)];
        [self addSubview:sliderView];

    }
    statusLb = [[UILabel alloc]init];
    statusLb.frame = CGRectMake(_window_width-70, 15, 60, 26);
    statusLb.backgroundColor = RGBA(0, 0, 0, 0.37);
    statusLb.textColor = [UIColor whiteColor];
    statusLb.font = [UIFont systemFontOfSize:12];
    statusLb.layer.cornerRadius = 13;
    statusLb.layer.masksToBounds = YES;
    statusLb.text = YZMsg(@"已下架");
    statusLb.textAlignment = NSTextAlignmentCenter;
    statusLb.hidden = YES;
    [self addSubview:statusLb];

    priceLb = [[UILabel alloc]init];
//    priceLb.frame = CGRectMake(15, sliderView.bottom+10, self.width-30, 30);
    priceLb.font = [UIFont boldSystemFontOfSize:20];
    priceLb.textColor = Pink_Cor;
    priceLb.text = @"¥199.00";
    [self addSubview:priceLb];
    [priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.top.equalTo(sliderView.mas_bottom).offset(10);
        make.height.mas_equalTo(30);
    }];
    
    original_priceLb =[[UILabel alloc]init];
//    original_priceLb.frame = CGRectMake(priceLb.right+, sliderView.bottom+10, self.width-30, 30);
    original_priceLb.font = [UIFont systemFontOfSize:15];
    original_priceLb.textColor = [UIColor grayColor];
    original_priceLb.text = @"¥199.00";
    [self addSubview:original_priceLb];
    [original_priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(priceLb.mas_right).offset(15);
        make.centerY.equalTo(priceLb.mas_centerY);
        make.height.mas_equalTo(30);
    }];
    
    UILabel *lineLb = [[UILabel alloc]init];
    lineLb.backgroundColor = [UIColor grayColor];
    [self addSubview:lineLb];
    [lineLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(original_priceLb);
        make.height.mas_equalTo(1);
        make.centerY.equalTo(original_priceLb.mas_centerY);
    }];
    
    nameLb = [[UILabel alloc]init];
//    nameLb.frame = CGRectMake(15, sliderView.bottom+10+30+10, self.width-30, 40);
    nameLb.font = [UIFont boldSystemFontOfSize:16];
    nameLb.textColor = [UIColor blackColor];
    nameLb.numberOfLines = 0;
    nameLb.lineBreakMode = NSLineBreakByWordWrapping;
    nameLb.text = @"北京烤鸭";
    [self addSubview:nameLb];
    [nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(priceLb.mas_left);
        make.top.equalTo(priceLb.mas_bottom).offset(10);
        make.right.equalTo(self).offset(-15);
    }];
    
    desLb =[[UILabel alloc]init];
    desLb.font = [UIFont systemFontOfSize:15];
    desLb.textColor = [UIColor darkGrayColor];
    desLb.numberOfLines = 0;
    desLb.lineBreakMode = NSLineBreakByWordWrapping;
    [self addSubview:desLb];
    [desLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(priceLb.mas_left);
        make.top.equalTo(nameLb.mas_bottom).offset(10);
        make.right.equalTo(self).offset(-15);
    }];
    
}
#pragma mark ============轮播图点击=============
-(void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index{
}
-(void)setModel:(CommodityDetailModel *)model
{
    _model = model;
    if ([model.goods_status isEqual:@"-1"]) {
        statusLb.hidden = NO;
    }

    NSMutableArray *muaaa = [NSMutableArray array];
    BOOL isVideo;
    if (_model.video_url_format.length > 6) {
        [muaaa addObject:_model.video_url_format];
        isVideo = YES;
    }else{
        isVideo = NO;
    }
    [muaaa addObjectsFromArray:_model.thumbs_format];
    if(muaaa.count > 0){
        [sliderView reload:muaaa andIsvideo:isVideo];
    }
    priceLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), model.present_price] ;
    original_priceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),model.original_price] ;
    nameLb.text = model.name;
    desLb.text = model.goods_desc;
}

@end
