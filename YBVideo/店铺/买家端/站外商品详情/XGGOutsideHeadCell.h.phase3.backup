//
//  OutsideHeadCell.h
//  yunbaolive
//
//  Created by ybR<PERSON><PERSON> on 2020/5/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "SDCycleScrollView.h"
#import "CommodityDetailModel.h"
#import "sliderCollectionView.h"

NS_ASSUME_NONNULL_BEGIN

@interface OutsideHeadCell : UITableViewCell<SDCycleScrollViewDelegate>
{
    UILabel *statusLb;
    UILabel *priceLb;
    UILabel *original_priceLb;
    UILabel *nameLb;
    UILabel *desLb;
    sliderCollectionView *sliderView;

}
@property (nonatomic,strong) SDCycleScrollView *cycleScroll;
@property (nonatomic, strong)CommodityDetailModel *model;

@end

NS_ASSUME_NONNULL_END
