//
//  PublishEvaluateVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/16.
//  Copyright © 2020 cat. All rights reserved.
//

#import "PublishEvaluateVC.h"
#import "CWStarRateView.h"
#import "TZImagePickerController.h"
#import <Qiniu/QiniuSDK.h>
#import "YBImageView.h"
#import "ShowDetailVC.h"
#import "YBStorageObj.h"
@interface PublishEvaluateVC ()<CWStarRateViewDelegate,TZImagePickerControllerDelegate>
{
    UIScrollView *_backScroll;
    CWStarRateView *rateView;
    NSString *rateStr;
    
    CWStarRateView *deliveryView;
    NSString *deliveryStr;

    CWStarRateView *attitudeView;//态度
    NSString *attitudeStr;
    
    NSInteger starCool;
    MyTextView *evaluateText;
    
    UIView *_photoView;
    UIView *_bottomView;

    BOOL isOpen;
    NSMutableArray *imageArray;
    UIButton *imageBtn;
    UIButton *videoBtn;
    UILabel *phoneline;
    UIButton *subVideoBtn;
    CGFloat imgWidth;
    
    NSString *videoPath;
    UIImage *videoCoverImage;
    NSString *videoCorverStr;
    NSString *videoHerfStr;

    NSString *picThumbsStr;
    NSMutableArray *imageUrlArray;
}
@end

@implementation PublishEvaluateVC
-(void)initData{
    starCool = 0;
    imgWidth = (_window_width - 75)/4;
    imageArray = [NSMutableArray array];
    rateStr = @"0";
    deliveryStr = @"0";
    attitudeStr = @"0";
    videoPath = @"";
    picThumbsStr = @"";
    videoHerfStr = @"";
    videoCorverStr =@"";

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"发表评价");
    self.rightBtn.hidden = NO;
    [self.rightBtn setTitle:YZMsg(@"发布") forState:0];
    [self.rightBtn setTitleColor:Pink_Cor forState:0];
    
    [self initData];
    [self createUI];
}
-(void)clickNaviRightBtn
{
    if (evaluateText.text.length < 1) {
        [MBProgressHUD showError:YZMsg(@"请填写评价内容")];
        return;
    }
    [MBProgressHUD showMessage:@""];
    NSLog(@"-------image:%@  \n===---video:%@",imageArray,videoPath);
    if (videoPath.length > 0 || imageArray.count > 0) {
        YBWeakSelf;
        [[YBStorageObj storageManeger]getCOSType:^(int code) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (code == 0) {
                    [weakSelf startUpload];
                }
            });
        }];
    }else{
        [self doUploadEvaluate];
    }
}
-(void)startUpload{
    YBWeakSelf;
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);

    //如果有视频或图片先上传视频
    if (videoPath.length > 0) {
        dispatch_group_async(group, queue, ^{
            NSData *imageData = UIImagePNGRepresentation(videoCoverImage);
            if (!imageData) {
                [MBProgressHUD hideHUD];
                [MBProgressHUD showError:YZMsg(@"请重新选择")];
                return;
            }
            UIImage *herfImg = [UIImage imageWithData:imageData];
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_action_video_cover.png"];
            [[YBStorageObj storageManeger]yb_storageImg:herfImg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                //图片成功
                videoCorverStr = key;
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });

        dispatch_group_async(group, queue, ^{
            //传视频
            NSString *videoName = [PublicObj getNameBaseCurrentTime:@"_action_video.mp4"];

            [[YBStorageObj storageManeger]yb_storageVideoOrVoice:videoPath andName:videoName progress:^(CGFloat percent) {
                
            } complete:^(int code, NSString *key) {
                videoHerfStr = key;

                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });

    }
    if (imageArray.count > 0) {
        imageUrlArray = [NSMutableArray array];
        picThumbsStr = @"";
        for (int i = 0; i < imageArray.count ; i ++) {
            
            UIImage *image =imageArray[i];
            NSData *imageData = UIImagePNGRepresentation(image);
            if (!imageData) {
                [MBProgressHUD hideHUD];
                [MBProgressHUD showError:YZMsg(@"图片错误")];
                return;
            }
            
            
            dispatch_group_async(group, queue, ^{
                NSString *imageName = [PublicObj getNameBaseCurrentTime:[NSString stringWithFormat:@"_action_image%d_cover.png", i]];
                [[YBStorageObj storageManeger]yb_storageImg:image andName:imageName progress:^(CGFloat percent) {
                    
                }complete:^(int code, NSString *key) {
                    [imageUrlArray addObject:key];
                    dispatch_semaphore_signal(semaphore);
                }];
                dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
            });
        }
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
                for (NSString *str in imageUrlArray) {
                    if (picThumbsStr.length == 0) {
                        picThumbsStr = str;
                    }else{
                        picThumbsStr = [NSString stringWithFormat:@"%@,%@",picThumbsStr,str];
                    }
                }
                [weakSelf doUploadEvaluate];
        });
        NSLog(@"任务完成执行");
    });

}

-(void)createUI{
    _backScroll = [[UIScrollView alloc]init];
    _backScroll.frame = CGRectMake(0, 64+statusbarHeight, _window_width, _window_height);
    _backScroll.backgroundColor = Normal_SubColor;
    [self.view addSubview:_backScroll];
    
    UIImageView *headImg = [[UIImageView alloc]init];
    headImg.frame = CGRectMake(15, 10, 60, 60);
    headImg.layer.cornerRadius = 5;
    headImg.layer.masksToBounds = YES;
    headImg.contentMode = UIViewContentModeScaleAspectFill;
    [headImg sd_setImageWithURL:[NSURL URLWithString:self.model.spec_thumb]];
    [_backScroll addSubview:headImg];
    
    UILabel *goodsname = [[UILabel alloc]init];
    goodsname.frame = CGRectMake(headImg.right+10, headImg.top+10, 200, 20);
    goodsname.font = [UIFont systemFontOfSize:14];
    goodsname.textColor = [UIColor whiteColor];
    goodsname.text = self.model.goods_name;
    [_backScroll addSubview:goodsname];
    
    UILabel *specname = [[UILabel alloc]init];
    specname.frame = CGRectMake(headImg.right+10, goodsname.bottom+5, 200, 20);
    specname.font = [UIFont systemFontOfSize:13];
    specname.textColor = Normal_TextColor;
    specname.text =[NSString stringWithFormat:@"%@ x%@",self.model.spec_name,self.model.nums];
    [_backScroll addSubview:specname];
    
    UILabel *title1 = [[UILabel alloc]init];
    title1.frame = CGRectMake(headImg.left, headImg.bottom+15, 60, 20);
    title1.font = [UIFont systemFontOfSize:14];
    title1.textColor = [UIColor whiteColor];
    title1.text =YZMsg(@"描述相符");
    [_backScroll addSubview:title1];

    rateView = [[CWStarRateView alloc]initWithFrame:CGRectMake(title1.right+10, title1.top, 120, 20) numberOfStars:5 andSelImg:FOREGROUND_STAR_IMAGE_NAME andNormalImg:BACKGROUND_STAR_IMAGE_NAME];
    rateView.delegate = self;
    rateView.scorePercent = 0;

    [_backScroll addSubview:rateView];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, title1.bottom+15, _window_width, 1) andColor:Line_Cor andView:_backScroll];
    
    evaluateText = [[MyTextView alloc]init];
    evaluateText.frame = CGRectMake(10, title1.bottom+30, _window_width-20, 125);
    evaluateText.placeholderColor = Normal_TextColor;
    evaluateText.placeholder =YZMsg(@"宝贝满足你的期望吗？说说它的优点和不足吧");
    evaluateText.font = [UIFont systemFontOfSize:14];
    evaluateText.backgroundColor = Normal_SubColor;
    evaluateText.textColor = [UIColor whiteColor];
    [_backScroll addSubview:evaluateText];
    
    _photoView = [[UIView alloc]init];
    _photoView.frame = CGRectMake(0, evaluateText.bottom, _window_width, imgWidth+10);
    
        
    [_backScroll addSubview:_photoView];
    NSArray *arr = @[@"添加图片",@"添加视频"];
    NSArray *titlearr = @[YZMsg(@"添加图片"),YZMsg(@"添加视频")];

    imageBtn = [UIButton buttonWithType:0];
    imageBtn.frame = CGRectMake(15,0, imgWidth, imgWidth);
    CAShapeLayer *border = [CAShapeLayer layer];
    //虚线的颜色
    border.strokeColor = Normal_TextColor.CGColor;
    //填充的颜色
    border.fillColor = [UIColor clearColor].CGColor;
    //设置路径
    border.path = [UIBezierPath bezierPathWithRect:imageBtn.bounds].CGPath;
    border.frame = imageBtn.bounds;
    //虚线的宽度
    border.lineWidth = 1.f;
    //虚线的间隔
    border.lineDashPattern = @[@4, @2];
    [imageBtn.layer addSublayer:border];
    
    [imageBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"evaluate%@",arr[0]]] forState:0];
    [imageBtn setTitle:titlearr[0] forState:0];
    [imageBtn setTitleColor:Normal_TextColor forState:0];
    imageBtn.titleLabel.font = [UIFont systemFontOfSize:10];
    imageBtn = [PublicObj setUpImgDownText:imageBtn];
    imageBtn.tag = 1000;
    [imageBtn addTarget:self action:@selector(imageBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [_photoView addSubview:imageBtn];

    
    videoBtn = [UIButton buttonWithType:0];
    videoBtn.frame = CGRectMake(15+(1%4)*(imgWidth + 15),0, imgWidth, imgWidth);
    CAShapeLayer *border2 = [CAShapeLayer layer];
   //虚线的颜色
    border2.strokeColor = Normal_TextColor.CGColor;
   //填充的颜色
    border2.fillColor = [UIColor clearColor].CGColor;
   //设置路径
    border2.path = [UIBezierPath bezierPathWithRect:videoBtn.bounds].CGPath;
    border2.frame = videoBtn.bounds;
   //虚线的宽度
    border2.lineWidth = 1.f;
   //虚线的间隔
    border2.lineDashPattern = @[@4, @2];
    [videoBtn.layer addSublayer:border2];
   
    [videoBtn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"evaluate%@",arr[1]]] forState:0];
    [videoBtn setTitle:titlearr[1] forState:0];
    [videoBtn setTitleColor:Normal_TextColor forState:0];
    videoBtn.titleLabel.font = [UIFont systemFontOfSize:10];
    videoBtn = [PublicObj setUpImgDownText:videoBtn];
    videoBtn.tag = 1001;
    [videoBtn addTarget:self action:@selector(imageBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [_photoView addSubview:videoBtn];
    
    phoneline = [[UILabel alloc]initWithFrame:CGRectMake(0, _photoView.height-1, _window_width, 1)];
    phoneline.backgroundColor = Line_Cor;
    [_photoView addSubview:phoneline];
    
    _bottomView = [[UIView alloc]init];
    _bottomView.frame =CGRectMake(0, _photoView.bottom, _window_width, 175);
    [_backScroll addSubview:_bottomView];
    
    UIButton *openBtn = [UIButton buttonWithType:0];
    openBtn.frame =CGRectMake(15, 10, 80, 20);
    openBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [openBtn setTitle:YZMsg(@"公开") forState:UIControlStateNormal];
    [openBtn setTitle:YZMsg(@"匿名") forState:UIControlStateSelected];
    [openBtn setTitleColor:Pink_Cor forState:UIControlStateNormal];
    [openBtn setTitleColor:Normal_TextColor forState:UIControlStateSelected];
    [openBtn setImage:[UIImage imageNamed:@"evaluate公开"] forState:UIControlStateNormal];
    [openBtn setImage:[UIImage imageNamed:@"evaluate匿名"] forState:UIControlStateSelected];
    [openBtn addTarget:self action:@selector(openBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    openBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -10, 0, 0);
    [_bottomView addSubview:openBtn];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, openBtn.bottom+10, _window_width, 5) andColor:Normal_BackColor andView:_bottomView];

    UILabel *shoptitle = [[UILabel alloc]init];
    shoptitle.frame = CGRectMake(headImg.left, openBtn.bottom+25, 60, 20);
    shoptitle.font = [UIFont systemFontOfSize:14];
    shoptitle.textColor = [UIColor whiteColor];
    shoptitle.text =YZMsg(@"店铺评价");
    [_bottomView addSubview:shoptitle];
    
    
    UILabel *sendtitle = [[UILabel alloc]init];
    sendtitle.frame = CGRectMake(headImg.left, shoptitle.bottom+10, 60, 20);
    sendtitle.font = [UIFont systemFontOfSize:14];
    sendtitle.textColor = Normal_TextColor;
    sendtitle.text =YZMsg(@"送货服务");
    [_bottomView addSubview:sendtitle];

    deliveryView = [[CWStarRateView alloc]initWithFrame:CGRectMake(sendtitle.right+10, sendtitle.top, 120, 20) numberOfStars:5 andSelImg:FOREGROUND_STAR_IMAGE_NAME andNormalImg:BACKGROUND_STAR_IMAGE_NAME];
    deliveryView.delegate = self;
    deliveryView.scorePercent = 0;

    [_bottomView addSubview:deliveryView];

    UILabel *attitudetitle = [[UILabel alloc]init];
    attitudetitle.frame = CGRectMake(headImg.left, sendtitle.bottom+10, 60, 20);
    attitudetitle.font = [UIFont systemFontOfSize:14];
    attitudetitle.textColor = Normal_TextColor;
    attitudetitle.text =YZMsg(@"服务态度");
    [_bottomView addSubview:attitudetitle];

    attitudeView = [[CWStarRateView alloc]initWithFrame:CGRectMake(attitudetitle.right+10, attitudetitle.top, 120, 20) numberOfStars:5 andSelImg:FOREGROUND_STAR_IMAGE_NAME andNormalImg:BACKGROUND_STAR_IMAGE_NAME];
    attitudeView.delegate = self;
    attitudeView.scorePercent = 0;

    [_bottomView addSubview:attitudeView];

}
-(void)openBtnClick:(UIButton *)sender{
    sender.selected = !sender.selected;
    isOpen = sender.selected;
}
- (void)starRateView:(CWStarRateView *)starRateView scroePercentDidChange:(CGFloat)newScorePercent{
    NSLog(@"%f",newScorePercent);
    starCool = (NSInteger)newScorePercent;
    if (newScorePercent == 0.2) {
        starCool = 1;
    }else if (newScorePercent == 0.4) {
        starCool = 2;
    }else if (newScorePercent == 0.6) {
        starCool = 3;
    }else if (newScorePercent == 0.8) {
        starCool = 4;
    }else if (newScorePercent == 1) {
        starCool = 5;
    }
    if (starRateView == rateView) {
        rateStr = [NSString stringWithFormat:@"%ld",starCool];
    }else if (starRateView == deliveryView){
        deliveryStr =[NSString stringWithFormat:@"%ld",starCool];
    }else if (starRateView == attitudeView){
        attitudeStr =[NSString stringWithFormat:@"%ld",starCool];
    }
    NSLog(@"%ld",starCool);

//    [self checkContent];
}
-(void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}
-(void)imageBtnClick:(UIButton *)sender
{
    if (sender.tag == 1000) {
        TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:5-imageArray.count delegate:self];
        imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
        imagePC.modalPresentationStyle = 0;
        imagePC.showSelectBtn = YES;
        imagePC.allowCrop = NO;
        imagePC.allowPickingOriginalPhoto = NO;
        imagePC.oKButtonTitleColorNormal = Pink_Cor;
        imagePC.allowTakePicture = YES;
        imagePC.allowTakeVideo = NO;
        imagePC.allowPickingVideo = NO;
        imagePC.allowPickingMultipleVideo = NO;
        [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
    }else if (sender.tag == 1001){
        BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
        if (isShowLive) {
            [MBProgressHUD showError:YZMsg(@"直播间开启期间不可使用该操作")];
            return;

        }

        TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
        imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
        imagePC.modalPresentationStyle = 0;
        imagePC.showSelectBtn = NO;
        imagePC.allowCrop = NO;
        imagePC.allowPickingOriginalPhoto = NO;
        imagePC.oKButtonTitleColorNormal = Pink_Cor;
        imagePC.allowPickingImage = NO;
        imagePC.allowTakePicture = NO;
        imagePC.allowTakeVideo = YES;
        imagePC.allowPickingVideo = YES;
        imagePC.allowPickingMultipleVideo = NO;
        imagePC.videoMaximumDuration = live_MAX_RECORD_TIME;
        [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
    }
}
#pragma mark - TZImagePickerControllerDelegate
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingVideo:(UIImage *)coverImage sourceAssets:(PHAsset *)asset {
    NSLog(@"-dsddddddddd--%@\n===%@",asset,coverImage);
    
    [MBProgressHUD showMessage:@""];
    [[TZImageManager manager] getVideoOutputPathWithAsset:asset presetName:AVAssetExportPresetMediumQuality success:^(NSString *outputPath) {
        NSLog(@"视频导出到本地完成,沙盒路径为:%@",outputPath);
        if (outputPath) {
            [MBProgressHUD hideHUD];
            videoPath = outputPath;
            videoCoverImage = coverImage;
            [self addVideoimage];
        }else{
            [MBProgressHUD hideHUD];
            [MBProgressHUD showError:@"请重新选择(iCloud视频请先在本地相册下载后上传)"];
        }

    } failure:^(NSString *errorMessage, NSError *error) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:errorMessage];
        NSLog(@"视频导出失败:%@,error:%@",errorMessage, error);
    }];
}
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    NSLog(@"------多选择图片--：%@",photos);
    [imageArray addObjectsFromArray:photos];
    [self setImageFrame];
}
-(void)addVideoimage{
    subVideoBtn = [UIButton buttonWithType:0];
    subVideoBtn.frame = CGRectMake(0, 0, videoBtn.width, videoBtn.height);
    [subVideoBtn setBackgroundImage:videoCoverImage forState:0];
    subVideoBtn.contentMode =  UIViewContentModeScaleAspectFill;
    subVideoBtn.clipsToBounds = YES;
    [subVideoBtn setImage:[UIImage imageNamed:@"evaluate_播放"] forState:0];
    [subVideoBtn addTarget:self action:@selector(showVideo) forControlEvents:UIControlEventTouchUpInside];
    [videoBtn addSubview:subVideoBtn];
    
    UIButton *delBtn = [UIButton buttonWithType:0];
    delBtn.frame = CGRectMake(subVideoBtn.width-25, 5, 20, 20);
    [delBtn setBackgroundImage:[UIImage imageNamed:@"comm_del"] forState:0];
    [delBtn addTarget:self action:@selector(delVideoClick) forControlEvents:UIControlEventTouchUpInside];
    [subVideoBtn addSubview:delBtn];

}
-(void)delVideoClick{
    videoPath = @"";
    [subVideoBtn removeFromSuperview];
    subVideoBtn = nil;
}
-(void)showVideo{
    ShowDetailVC *detail = [[ShowDetailVC alloc]init];
    NSLog(@"=-=-=-=-=-=-=-:%@",videoPath);
    detail.videoPath =videoPath;
    detail.deleteEvent = ^(NSString *type) {
    };
    [[XGGAppDelegate sharedAppDelegate]pushViewController:detail animated:NO];

}
-(void)setImageFrame{

    for (UIButton *btn in _photoView.subviews) {
        if (btn.tag >=3000) {
            [btn removeFromSuperview];
        }
    }
    if (imageArray.count > 0) {
        if (imageArray.count < 3) {
            _photoView.size= CGSizeMake(_window_width, imgWidth+10);
            _bottomView.frame =CGRectMake(0, _photoView.bottom, _window_width, 175);

        }else if (imageArray.count>=3){
            _photoView.size= CGSizeMake(_window_width, imgWidth*2+20);
            _bottomView.frame =CGRectMake(0, _photoView.bottom, _window_width, 175);
        }
        for (int i = 0; i < imageArray.count ; i ++) {
            UIButton *btn = [UIButton buttonWithType:0];
            btn.backgroundColor = RGB(250, 250, 250);
            if (i < 4) {
                btn.frame = CGRectMake(15+(i%4)*(imgWidth + 15), 0, imgWidth, imgWidth);

            }else{
                btn.frame = CGRectMake(15+(i%4)*(imgWidth + 15), (imgWidth+10), imgWidth, imgWidth);
            }
            btn.tag = 3000+i;
            [btn setImage:imageArray[i] forState:0];
            btn.imageView.contentMode = UIViewContentModeScaleAspectFill;
            btn.imageView.clipsToBounds = YES;
            [btn addTarget:self action:@selector(showImg:) forControlEvents:UIControlEventTouchUpInside];
            [_photoView addSubview:btn];
            
            UIButton *delBtn = [UIButton buttonWithType:0];
            delBtn.frame = CGRectMake(imgWidth-25, 5, 20, 20);
            [delBtn setBackgroundImage:[UIImage imageNamed:@"comm_del"] forState:0];
            delBtn.tag = 4000+i;
            [delBtn addTarget:self action:@selector(delImageClick:) forControlEvents:UIControlEventTouchUpInside];
            [btn addSubview:delBtn];
        }
        if (imageArray.count < 3) {
            imageBtn.frame = CGRectMake(15+(imageArray.count%4)*(imgWidth + 15), 0, imgWidth, imgWidth);
            videoBtn.frame = CGRectMake(15+((imageArray.count+1)%4)*(imgWidth + 15), 0, imgWidth, imgWidth);

        }else if (imageArray.count == 3){
            imageBtn.frame = CGRectMake(15+(imageArray.count%4)*(imgWidth + 15), 0, imgWidth, imgWidth);
            videoBtn.frame = CGRectMake(15+((imageArray.count+1)%4)*(imgWidth + 15), (imgWidth+10), imgWidth, imgWidth);

        }else if (imageArray.count > 3){
            imageBtn.frame = CGRectMake(15+(imageArray.count%4)*(imgWidth + 15), (imgWidth+10), imgWidth, imgWidth);
            videoBtn.frame = CGRectMake(15+((imageArray.count+1)%4)*(imgWidth + 15), (imgWidth+10), imgWidth, imgWidth);
        }
        [imageBtn setTitle:[NSString stringWithFormat:@"%ld/5",imageArray.count] forState:0];
        imageBtn = [PublicObj setUpImgDownText:imageBtn];

    }else{
        imageBtn.frame = CGRectMake(15,0, imgWidth, imgWidth);
        [imageBtn setTitle:YZMsg(@"添加图片") forState:0];
        videoBtn.frame = CGRectMake(15+(1%4)*(imgWidth + 15),0, imgWidth, imgWidth);
    }
    if (imageArray.count == 5) {
        imageBtn.hidden = YES;
        videoBtn.frame = CGRectMake(15+(imageArray.count%4)*(imgWidth + 15), (imgWidth+10), imgWidth, imgWidth);
    }else{
        imageBtn.hidden = NO;
    }
    phoneline.frame = CGRectMake(0, _photoView.height-1, _window_width, 1);
}
#pragma mark-----删除图片-------
-(void)delImageClick:(UIButton *)sender{
    NSInteger index = sender.tag-4000;
    [imageArray removeObjectAtIndex:index];
    [self setImageFrame];
}
#pragma mark------显示图片--------
-(void)showImg:(UIButton *)sender{
    NSInteger index = sender.tag-3000;
    YBImageView *imgView = [[YBImageView alloc] initWithImageArray:imageArray andIndex:index andMine:NO isDtCell:NO andBlock:^(NSArray * _Nonnull array) {
    }];
    [imgView hideDelete];
    [[UIApplication sharedApplication].keyWindow addSubview:imgView];

}
#pragma mark-----正式发布-----------
-(void)doUploadEvaluate{
    NSString *is_anonym = @"0";
    if (isOpen) {
        is_anonym = @"1";
    }else{
         is_anonym = @"0";
    }
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Buyer.evaluateGoodsOrder"];
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"orderid":self.model.idStr,@"is_anonym":is_anonym,@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.model.idStr,
                          @"content":evaluateText.text,
                          @"thumbs":picThumbsStr,
                          @"video_url":videoHerfStr,
                          @"video_thumb":videoCorverStr,
                          @"is_anonym":is_anonym,
                          @"quality_points":rateStr,
                          @"service_points":attitudeStr,
                          @"express_points":deliveryStr,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    [YBNetworking postWithUrl:@"Buyer.evaluateGoodsOrder" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            [MBProgressHUD showError: msg];
            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];

        }];

}
@end
