//
//  CommodityClassModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/2.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityClassModel.h"

@implementation CommodityClassModel


-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.idStr = minstr([dic valueForKey:@"gc_id"]);
        self.nameStr = minstr([dic valueForKey:@"gc_name"]);
        self.gc_isshow = minstr([dic valueForKey:@"gc_isshow"]);
        self.isexists = minstr([dic valueForKey:@"isexists"]);
    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)subdic{
    CommodityClassModel *model = [[CommodityClassModel alloc]initWithDic:subdic];
    return model;
}

@end
