//
//  SelectClassVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/2.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SelectClassVC.h"
#import "CommodityClassCell.h"
#import "CommodityClassModel.h"
@interface SelectClassVC ()<UITableViewDelegate, UITableViewDataSource>
{
    NSMutableArray *classArray;
    NSMutableArray *selArr;
}
@property (nonatomic, strong)UITableView *classTable;
@property (nonatomic, strong)NSArray *modelArr;
@end

@implementation SelectClassVC
-(void)initData{
    
    classArray = [NSMutableArray array];
    selArr = [NSMutableArray array];
    selArr = self.havaSelArr;

}

-(NSArray *)modelArr{
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in classArray) {
        CommodityClassModel *model = [CommodityClassModel modelWithDic:dic];
        [array addObject:model];
    }
    _modelArr = array;
    return _modelArr;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"经营类目设置");
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = RGB(17, 13, 35);
    [self initData];
    [self.view addSubview:self.classTable];
    [self addBottomView];
    [self requestData];
    
}
-(UITableView *)classTable{
    if (!_classTable) {
        _classTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60) style:UITableViewStylePlain];
        _classTable.delegate = self;
        _classTable.dataSource = self;
        _classTable.separatorStyle = 0;
        _classTable.backgroundColor = RGB(17, 13, 35);

    }
    return _classTable;
}
-(void)addBottomView{
    UIButton *sureBtn = [UIButton buttonWithType:0];
    sureBtn.frame = CGRectMake(15, _window_height-60, _window_width-30, 45);
    [sureBtn setBackgroundColor:Pink_Cor];
    sureBtn.layer.cornerRadius = 5;
    sureBtn.layer.masksToBounds = YES;
    [sureBtn setTitle:YZMsg(@"提交") forState:0];
    sureBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [sureBtn setTitleColor:[UIColor whiteColor] forState:0];
    [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:sureBtn];
}
-(void)sureBtnClick{
    if (selArr.count < 1) {
        [MBProgressHUD showError:YZMsg(@"请选择经营类目")];
        return;
    }
    if (self.classEvent) {
        self.classEvent(selArr);
    }
    [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
}
#pragma mark ------数据请求----------
-(void)requestData{
    [YBNetworking postWithUrl:@"Shop.getOneGoodsClass" Dic:nil Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            classArray = info;
            [self.classTable reloadData];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.modelArr.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    CommodityClassCell *cell = [tableView dequeueReusableCellWithIdentifier:@"CommodityClassCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"CommodityClassCell" owner:nil options:nil] lastObject];
    }
    CommodityClassModel *allmodel =self.modelArr[indexPath.row];
    for (CommodityClassModel *model in selArr) {
        if ([model.idStr isEqual:allmodel.idStr]) {
            allmodel.gc_isshow = @"0";
        }
    }
    cell.models = allmodel;
    cell.contentView.backgroundColor = RGB(24, 21, 43);
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 90)];
    view.backgroundColor = RGB(24, 21, 43);

    UIView *backView = [[UIView alloc]init];
    backView.frame = CGRectMake(0, 0, _window_width, 50);
    backView.backgroundColor = RGB(17, 13, 35);
    [view addSubview:backView];
    
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(12, 10, _window_width-20, 30)];
    label.font = [UIFont systemFontOfSize:13];
    label.textColor = RGB(150, 150, 150);
    label.text = YZMsg(@"请谨慎选择，主营类目设置成功后将不可更改");
    [backView addSubview:label];
    
    UILabel *lb = [[UILabel alloc]init];
    lb.frame = CGRectMake(12, backView.bottom+5, _window_width, 30);
    lb.font = [UIFont systemFontOfSize:16];
    lb.textColor = RGB(150, 150, 150);
    lb.text = YZMsg(@"选择主营类目");
    [view addSubview:lb];
    
    return view;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 90;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.classTable deselectRowAtIndexPath:indexPath animated:YES];

    CommodityClassModel *model = self.modelArr[indexPath.row];
     __block  NSInteger indexsss;
     __block BOOL isExist = NO;
     [selArr enumerateObjectsUsingBlock:^(CommodityClassModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj.idStr isEqualToString:model.idStr]) {//数组中已经存在该对象
                    *stop = YES;
                    isExist = YES;
                    indexsss = idx;
              }
    }];
    if (!isExist) {
        //如果不存在就添加进去
        [selArr addObject:model];
    }else{
        [selArr removeObjectAtIndex:indexsss];
    }
    [self.classTable reloadData];
    NSLog(@"----------selarr:-----1111:%@",selArr);
}
@end
