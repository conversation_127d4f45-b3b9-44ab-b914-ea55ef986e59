//
//  CommodityClassCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/2.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityClassCell.h"

@implementation CommodityClassCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
//    self.tintColor = Normal_Color;
//    self.selectedBackgroundView.backgroundColor = [UIColor whiteColor];

}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    
}
-(void)setModels:(CommodityClassModel *)models
{
    _models = models;
    if ([models.isexists isEqual:@"1"]) {
        self.selectImg.hidden = YES;
        self.titleLb.textColor = RGB(150, 150, 150);
    }
    if ([_models.gc_isshow isEqual:@"1"]) {
        self.selectImg.image = [UIImage imageNamed:@"commodity_记录未选"];
    }else{
        self.selectImg.image = [UIImage imageNamed:@"commodity_记录选中"];
    }
    self.titleLb.text = _models.nameStr;

}

@end
