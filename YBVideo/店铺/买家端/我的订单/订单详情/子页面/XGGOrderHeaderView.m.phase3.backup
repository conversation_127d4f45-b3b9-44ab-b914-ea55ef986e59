//
//  OrderHeaderView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/7.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderHeaderView.h"

@implementation OrderHeaderView

-(instancetype)init
{
    self = [super init];
    if (self) {
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = CGRectMake(0, 0, _window_width, 135);
        gl.colors = @[
        (__bridge id)[UIColor colorWithRed:252/255.0 green:88/255.0 blue:192/255.0 alpha:1.00].CGColor,
        (__bridge id)[UIColor colorWithRed:252/255.0 green:64/255.0 blue:140/255.0 alpha:1.00].CGColor,
        ];
        gl.locations = @[@(0),@(1)];
        [self.layer addSublayer:gl];
        self.backgroundColor = [UIColor colorWithRed:234/255.0 green:55/255.0 blue:127/255.0 alpha:1.00];
//        self.backgroundColor = Pink_Cor;
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    statusLb = [[UILabel alloc]init];
    statusLb.textColor = [UIColor whiteColor];
    statusLb.font = [UIFont systemFontOfSize:16];
    statusLb.textAlignment = NSTextAlignmentCenter;
    statusLb.numberOfLines  =0;
    [self addSubview:statusLb];
    [statusLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.centerY.equalTo(self);
        make.height.mas_greaterThanOrEqualTo(20);
    }];
    
    timeLb = [[UILabel alloc]init];
    timeLb.textColor = [UIColor whiteColor];
    timeLb.font = [UIFont systemFontOfSize:13];
    timeLb.textAlignment = NSTextAlignmentCenter;
    timeLb.numberOfLines = 0;
    [self addSubview:timeLb];
    [timeLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(statusLb);
        make.top.equalTo(statusLb.mas_bottom);
    }];
    //['status']  订单状态  -1 已关闭  0 待付款 1 待发货 2 待收货 3 待评价 4 已评价 5 退款
}

-(void)setBuyerData:(OrderDetailModel *)model{
    statusLb.text = model.order_status_name;
    timeLb.text = model.order_status_desc;

}
-(void)setSellerData:(SellOrderDetailModel *)model{
    statusLb.text = model.status_name;
    timeLb.text = model.status_desc;

}
@end
