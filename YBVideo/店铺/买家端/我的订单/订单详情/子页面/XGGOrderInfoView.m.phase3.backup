//
//  OrderInfoView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/10.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderInfoView.h"
#import "ApplyRefundVC.h"
#import "BuyerRefundDetailVC.h"
#import "shopDetailVC.h"
@implementation OrderInfoView

-(instancetype)init{
    self = [super init];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    _shopIconImg = [[UIImageView alloc]init];
    _shopIconImg.image = [UIImage imageNamed:@"店铺"];
    [self addSubview:_shopIconImg];
    [_shopIconImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self).offset(12);
        make.width.height.mas_equalTo(15);
    }];
    _shopTitleLb = [[UILabel alloc]init];
    _shopTitleLb.textColor = Normal_TextColor;
    _shopTitleLb.font = [UIFont systemFontOfSize:14];
    [self addSubview:_shopTitleLb];
    [_shopTitleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_shopIconImg.mas_right).offset(5);
        make.centerY.equalTo(_shopIconImg);
        make.height.mas_equalTo(18);
    }];
    UIButton *shopBtn = [UIButton buttonWithType:0];
    [shopBtn addTarget:self action:@selector(shopTitleClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:shopBtn];
    [shopBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(_shopTitleLb);
    }];
    
    rightImg = [[UIImageView alloc]init];
    rightImg.image = [UIImage imageNamed:@"shop_right"];
    [self addSubview:rightImg];
    [rightImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_shopTitleLb.mas_right).offset(5);
        make.centerY.equalTo(_shopTitleLb);
        make.width.height.mas_equalTo(15);
    }];
    
    _orderImg = [[UIImageView alloc]init];
    _orderImg.backgroundColor = [UIColor lightGrayColor];
    _orderImg.layer.cornerRadius = 5;
    _orderImg.layer.masksToBounds = YES;
    _orderImg.contentMode = UIViewContentModeScaleAspectFill;
    [self addSubview:_orderImg];
    [_orderImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_shopIconImg.mas_left);
        make.top.equalTo(_shopTitleLb.mas_bottom).offset(10);
        make.width.height.mas_equalTo(90);
    }];
    
    _orderTitleLb = [[UILabel alloc]init];
    _orderTitleLb.textColor = [UIColor whiteColor];
    _orderTitleLb.font = [UIFont systemFontOfSize:14];
    [self addSubview:_orderTitleLb];
    [_orderTitleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_orderImg.mas_right).offset(5);
        make.top.equalTo(_orderImg).offset(8);
        make.height.mas_equalTo(18);
    }];
    
    _orderPriceLb = [[UILabel alloc]init];
    _orderPriceLb.textColor = [UIColor whiteColor];
    _orderPriceLb.font = [UIFont systemFontOfSize:14];
    _orderPriceLb.textAlignment = NSTextAlignmentRight;
    [self addSubview:_orderPriceLb];
    [_orderPriceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-10);
        make.centerY.height.equalTo(_orderTitleLb);
    }];
    _orderContentLb = [[UILabel alloc]init];
    _orderContentLb.textColor = Normal_TextColor;
    _orderContentLb.font = [UIFont systemFontOfSize:14];
    [self addSubview:_orderContentLb];
    [_orderContentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_orderTitleLb);
        make.top.equalTo(_orderTitleLb.mas_bottom).offset(5);
        make.height.equalTo(_orderTitleLb);
    }];
    
    _orderCountLb = [[UILabel alloc]init];
    _orderCountLb.textColor = Normal_TextColor;
    _orderCountLb.font = [UIFont systemFontOfSize:14];
    _orderCountLb.textAlignment = NSTextAlignmentRight;
    [self addSubview:_orderCountLb];
    [_orderCountLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-10);
        make.centerY.height.equalTo(_orderContentLb);
    }];

    _statusBtn  = [UIButton buttonWithType:0];
    [_statusBtn setTitle:YZMsg(@"退款") forState:UIControlStateNormal];
    [_statusBtn setTitleColor:Normal_TextColor forState:0];
    _statusBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    _statusBtn.layer.cornerRadius = 11;
    _statusBtn.layer.borderColor = Normal_TextColor.CGColor;
    _statusBtn.layer.borderWidth = 1;
    _statusBtn.hidden = YES;
    [_statusBtn addTarget:self action:@selector(refundBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:_statusBtn];
    [_statusBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_orderCountLb.mas_right);
        make.bottom.equalTo(_orderImg.mas_bottom);
        make.width.mas_equalTo(50);
        make.height.mas_equalTo(22);
    }];
    
    _lineLb = [[UILabel alloc]init];
    _lineLb.backgroundColor = Line_Cor;
    [self addSubview:_lineLb];
    [_lineLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_shopIconImg);
        make.right.equalTo(self).offset(-10);
        make.top.equalTo(_orderImg.mas_bottom).offset(10);
        make.height.mas_equalTo(1);
    }];
}
-(void)setModel:(OrderDetailModel *)model
{
    //['status']  订单状态  -1 已关闭  0 待付款 1 待发货 2 待收货 3 待评价 4 已评价 5 退款
    _model = model;
    [_orderImg sd_setImageWithURL:[NSURL URLWithString:model.orderspec_thumb_format]];
    _shopTitleLb.text = model.shop_name;
    _orderPriceLb.text =[NSString stringWithFormat:@"¥%@",model.order_price];
    _orderTitleLb.text = model.order_goods_name;
    _orderContentLb.text = model.order_spec_name;
    _orderCountLb.text =[NSString stringWithFormat:@"x%@",model.order_nums];

    if (([model.order_status isEqual:@"1"] || [model.order_status isEqual:@"2"]) && [model.is_refund isEqual:@"1"]) {
        _statusBtn.hidden = NO;
    }else if (([model.order_status isEqual:@"1"] || [model.order_status isEqual:@"2"]) && [model.is_refund isEqual:@"0"]){
        _statusBtn.hidden = NO;
        [_statusBtn setTitle:YZMsg(@"退款详情") forState:0];
        [_statusBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(80);
        }];
    }
    if ([model.order_status isEqual:@"3"] && [model.is_refund isEqual:@"1"]) {
        _statusBtn.hidden = NO;
    }else if ([model.order_status isEqual:@"3"] && [model.is_refund isEqual:@"0"] && ![model.refund_status isEqual:@"0"]){
        _statusBtn.hidden = NO;
        [_statusBtn setTitle:YZMsg(@"退款详情") forState:0];
        [_statusBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(80);
        }];
    }
}

-(void)refundBtnClick:(UIButton *)sender{
    if ([sender.titleLabel.text isEqual:YZMsg(@"退款")]) {
        ApplyRefundVC *refund = [[ApplyRefundVC alloc]init];
        refund.models = self.model;
        [[XGGAppDelegate sharedAppDelegate]pushViewController:refund animated:YES];
    }else{
        BuyerRefundDetailVC *refund = [[BuyerRefundDetailVC alloc]init];
        refund.orderId = _model.order_id;
        [[XGGAppDelegate sharedAppDelegate]pushViewController:refund animated:YES];
    }
}

-(void)shopTitleClick
{
    shopDetailVC *vc = [[shopDetailVC alloc]init];
    vc.toUserID = _model.shop_uid;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

}




/////////////////////////////卖家端
-(void)setSellerModel:(SellOrderDetailModel *)sellerModel
{
    //['status']  订单状态  -1 已关闭  0 待付款 1 待发货 2 待收货 3 待评价 4 已评价 5 退款
    _shopIconImg.hidden = YES;
    _shopTitleLb.hidden = YES;
    rightImg.hidden = YES;
    _sellerModel = sellerModel;
    [_orderImg sd_setImageWithURL:[NSURL URLWithString:sellerModel.spec_thumb_format]];
    _shopTitleLb.text = sellerModel.goods_name;
    _orderPriceLb.text =[NSString stringWithFormat:@"¥%@",sellerModel.price];
    _orderTitleLb.text = sellerModel.goods_name;
    _orderContentLb.text = sellerModel.spec_name;
    _orderCountLb.text =[NSString stringWithFormat:@"x%@",sellerModel.nums];

    [_orderImg mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self).offset(12);
    }];
    [_lineLb mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(5);
        make.left.right.equalTo(self);
    }];
}
@end
