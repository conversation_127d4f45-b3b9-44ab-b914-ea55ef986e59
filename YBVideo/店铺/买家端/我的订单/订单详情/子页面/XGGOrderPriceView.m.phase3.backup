//
//  OrderPriceView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/11.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderPriceView.h"
#import "JCHATConversationViewController.h"

@implementation OrderPriceView

-(instancetype)init{
    self = [super init];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    UILabel *yunfeiLb = [[UILabel alloc]init];
    yunfeiLb.textColor = Normal_TextColor;
    yunfeiLb.font = [UIFont systemFontOfSize:14];
    yunfeiLb.text = YZMsg(@"运费");
    [self addSubview:yunfeiLb];
    [yunfeiLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self).offset(12);
        make.height.mas_equalTo(18);
    }];

    UILabel *priceTitle = [[UILabel alloc]init];
    priceTitle.textColor = [UIColor whiteColor];
    priceTitle.font = [UIFont systemFontOfSize:14];
    priceTitle.text = YZMsg(@"需付款/实付款");
    [self addSubview:priceTitle];
    [priceTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(yunfeiLb);
        make.top.equalTo(yunfeiLb.mas_bottom).offset(10);
        make.height.mas_equalTo(18);
    }];

    _freightLb = [[UILabel alloc]init];
    _freightLb.textColor = Normal_TextColor;
    _freightLb.font = [UIFont systemFontOfSize:14];
    [self addSubview:_freightLb];
    [_freightLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-12);
        make.centerY.equalTo(yunfeiLb);
        make.height.mas_equalTo(18);

    }];
    
    _priceLb= [[UILabel alloc]init];
    _priceLb.textColor = Pink_Cor;
    _priceLb.font = [UIFont systemFontOfSize:14];
    [self addSubview:_priceLb];
    [_priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_freightLb);
        make.centerY.equalTo(priceTitle);
        make.height.mas_equalTo(18);

    }];
    
    UILabel *lineLb = [[UILabel alloc]init];
    lineLb.backgroundColor = Line_Cor;
    [self addSubview:lineLb];
    [lineLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(priceTitle.mas_bottom).offset(5);
        make.left.right.equalTo(self);
        make.height.mas_equalTo(1);
    }];
    
    
    _kefuBtn = [UIButton buttonWithType:0];
    [_kefuBtn setBackgroundColor: Normal_SubColor];
    [_kefuBtn setImage:[UIImage imageNamed:@"联系客服"] forState:0];
    [_kefuBtn setTitle:YZMsg(@"联系客服") forState:0];
    [_kefuBtn setTitleColor:[UIColor whiteColor] forState:0];
    _kefuBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [_kefuBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, 10, 0, 0 )];
    [_kefuBtn addTarget:self action:@selector(kefuBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:_kefuBtn];
    [_kefuBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self);
        make.top.equalTo(lineLb.mas_bottom);
        make.width.equalTo(self).multipliedBy(0.5);
        make.height.mas_equalTo(40);
    }];
    
    UIButton *callBtn = [UIButton buttonWithType:0];
    [callBtn setBackgroundColor: Normal_SubColor];
    [callBtn setImage:[UIImage imageNamed:@"拨打电话"] forState:0];
    [callBtn setTitle:YZMsg(@"拨打电话") forState:0];
    [callBtn setTitleColor:[UIColor whiteColor] forState:0];
    callBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [callBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, 10, 0, 0 )];
    [callBtn addTarget:self action:@selector(callBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:callBtn];
    
    [callBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_kefuBtn.mas_right);
        make.top.equalTo(lineLb.mas_bottom);
        make.width.equalTo(self).multipliedBy(0.5);
        make.height.mas_equalTo(40);
    }];
    UILabel *sline = [[UILabel alloc]init];
    sline.backgroundColor = Line_Cor;
    [self addSubview:sline];
    [sline mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(lineLb.mas_bottom);
        make.left.equalTo(_kefuBtn.mas_right);
        make.width.mas_equalTo(1);
        make.height.mas_equalTo(40);
    }];
    UILabel *bottom = [[UILabel alloc]init];
    bottom.backgroundColor = Normal_BackColor;
    [self addSubview:bottom];
    [bottom mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(sline.mas_bottom);
        make.left.right.equalTo(self);
        make.height.mas_equalTo(3);
    }];
}
-(void)setModelData:(OrderDetailModel *)models{
    _ModelData = models;
    _freightLb.text =[NSString stringWithFormat:@"¥%@",models.order_postage];
    _priceLb.text = [NSString stringWithFormat:@"¥%@",models.order_total];
    shopuid = models.shop_uid;
    shopPhone = models.shop_phone;
    shopAvatar = models.shop_avatar;
    shopName = models.shop_name;
}
-(void)setModels:(SellOrderDetailModel *)models{
    _models = models;
    _freightLb.text =[NSString stringWithFormat:@"¥%@",models.postage];
    _priceLb.text = [NSString stringWithFormat:@"¥%@",models.total];

    shopuid = models.buyerUid;
    shopPhone = models.phone;
    shopAvatar = models.shop_avatar;
    shopName = models.username;

}
//-(void)setSellerModelData:(SellOrderDetailModel *)models{
//
//}

-(void)kefuBtnClick{
    if ([shopuid isEqual:@"1"]) {
        UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"请到个性设置-关于我们中联系客服") preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [sureAction setValue:Normal_Color forKey:@"_titleTextColor"];
        [alertControl addAction:sureAction];
        [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:alertControl animated:YES completion:nil];

    }else{
        NSDictionary *userDic = @{
            @"id":shopuid,
            @"user_nickname":shopName,
            @"avatar":shopAvatar,
        };
        [[YBMessageManager shareManager] chatWithUser:userDic];
    }
}
-(void)callBtnClick{
        NSString *phone =[NSString stringWithFormat:@"tel://%@",shopPhone] ;
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:phone]];
}
@end
