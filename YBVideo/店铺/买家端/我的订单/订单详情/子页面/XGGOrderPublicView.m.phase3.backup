//
//  OrderPublicView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/8.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderPublicView.h"

@implementation OrderPublicView

-(instancetype)init
{
    self = [super init];
    if (self) {
        self.backgroundColor = Normal_SubColor;
        [self creatUI];
        
    }
    return self;
}
-(void)creatUI{
    _markImg = [[UIImageView alloc]init];
    _markImg.contentMode = UIViewContentModeScaleAspectFit;
    [self addSubview:_markImg];
    [_markImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self).offset(16);
        make.width.mas_equalTo(18);
        make.height.mas_equalTo(14);

    }];
    
    _titleLb = [[UILabel alloc]init];
    _titleLb.textColor = [UIColor whiteColor];
    _titleLb.font = [UIFont systemFontOfSize:14];
    [self addSubview:_titleLb];
    [_titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_markImg.mas_right).offset(5);
        make.centerY.equalTo(_markImg);
        make.height.mas_equalTo(18);
    }];
    _contentLb = [[UILabel alloc]init];
    _contentLb.textColor = Normal_TextColor;
    _contentLb.font = [UIFont systemFontOfSize:12];
    [self addSubview:_contentLb];
    [_contentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_titleLb);
        make.top.equalTo(_titleLb.mas_bottom).offset(5);
        make.height.mas_equalTo(18);
        make.right.equalTo(self).offset(-15);
    }];
    
    _lineLb = [[UILabel alloc]init];
    _lineLb.backgroundColor = Normal_BackColor;
    [self addSubview:_lineLb];
    [_lineLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self);
        make.left.right.equalTo(self);
        make.height.mas_equalTo(3);
    }];
    _lineLb.hidden = YES;
}

-(void)setData:(NSString *)type{
    
    if ([type isEqual:@"0"]) {
        self.markImg.image = [UIImage imageNamed:@"mark_物流"];
        _titleLb.textColor = [UIColor whiteColor];
        self.titleLb.text = @"包裹正在揽收";
        self.contentLb.text = @"顺丰快递。777999999";
        self.lineLb.hidden =NO;
    }else if ([type isEqual:@"0"] ||[type isEqual:@"-1"]) {
        self.markImg.image = [UIImage imageNamed:@"shop_收货地址"];
        self.titleLb.text = @"李二狗 15311112222";
        self.contentLb.text = @"山东省 泰安市 泰山区 万达广场2号楼1702";
    }else{
        self.markImg.image = [UIImage imageNamed:@"shop_留言"];
        self.titleLb.text = YZMsg(@"买家留言");
        self.contentLb.text = YZMsg(@"请务必检查好货品");

    }
}

-(void)setModelData:(OrderDetailModel *)model AndIndex:(int)index{
    if ([model.order_status isEqual:@"0"] ||[model.order_status isEqual:@"-1"]) {
        self.markImg.image = [UIImage imageNamed:@"shop_收货地址"];
        self.titleLb.text =[NSString stringWithFormat:@"%@ %@",model.order_username,model.order_phone];
        self.contentLb.text =[NSString stringWithFormat:@"%@ %@ %@ %@",model.order_province,model.order_city, model.order_area,model.order_address];
        self.contentLb.numberOfLines = 0;
        self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;
        [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(30);
        }];
    }else if ([model.order_status isEqual:@"2"] ||[model.order_status isEqual:@"3"]||[model.order_status isEqual:@"4"]){
        if (index == 1) {
            self.markImg.image = [UIImage imageNamed:@"mark_物流"];
            _titleLb.textColor = [UIColor whiteColor];
            self.titleLb.text = model.express_state_name;
            self.contentLb.text =model.express_desc;
            self.lineLb.hidden =NO;

        }else if (index == 2){
            self.markImg.image = [UIImage imageNamed:@"shop_收货地址"];
            self.titleLb.text =[NSString stringWithFormat:@"%@ %@",model.order_username,model.order_phone];
            self.contentLb.text =[NSString stringWithFormat:@"%@ %@ %@ %@",model.order_province,model.order_city, model.order_area,model.order_address];
            self.contentLb.numberOfLines = 0;
            self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;
            [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(30);
            }];

        }else{
            self.markImg.image = [UIImage imageNamed:@"shop_留言"];
            self.titleLb.text = YZMsg(@"买家留言");
            self.contentLb.text = model.order_message;

        }
    }else if ([model.order_status isEqual:@"1"]){
        if (index == 1){
            self.markImg.image = [UIImage imageNamed:@"shop_收货地址"];
            self.titleLb.text =[NSString stringWithFormat:@"%@ %@",model.order_username,model.order_phone];
            self.contentLb.text = [NSString stringWithFormat:@"%@ %@ %@ %@",model.order_province,model.order_city, model.order_area,model.order_address];
            self.contentLb.numberOfLines = 0;
            self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;
            [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(30);
            }];

        }else{
            self.markImg.image = [UIImage imageNamed:@"shop_留言"];
            self.titleLb.text = YZMsg(@"买家留言");
            self.contentLb.numberOfLines = 0;
            self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;
            self.contentLb.text =  model.order_message;
            CGFloat textHeight = [PublicObj heightOfString:model.order_message andFont:[UIFont systemFontOfSize:12] andWidth:_window_width-50];
            NSLog(@"gaodugaodugaodu-----:%f", textHeight);
            [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(textHeight+10);
            }];


        }
    }
}


/*****************卖家数据***************/
-(void)setSellOrderModelData:(SellOrderDetailModel *)model AndIndex:(int)index{
    //    -1 已关闭 0 待买家付款  1 待发货  2  待确认收货   3 待评价  4 已评价  5 退款
    if ([model.status isEqual:@"1"]) {
        if (index == 1){
            self.markImg.image = [UIImage imageNamed:@"shop_收货地址"];
            self.titleLb.text =[NSString stringWithFormat:@"%@ %@",model.username,model.phone];// @"李二狗 15311112222";
            self.contentLb.text = model.address_format;//@"山东省 泰安市 泰山区 万达广场2号楼1702";
            self.contentLb.numberOfLines = 0;
            self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;
            [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(30);
            }];

        }else{
            self.markImg.image = [UIImage imageNamed:@"shop_留言"];
            self.contentLb.numberOfLines = 0;
            self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;

            self.titleLb.text = YZMsg(@"买家留言");
            CGFloat textHeight = [PublicObj heightOfString:model.message andFont:[UIFont systemFontOfSize:12] andWidth:_window_width-50];
            [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(textHeight+10);
            }];
            self.contentLb.text =  model.message;

        }
    }else if ([model.status isEqual:@"0"] ||[model.status isEqual:@"-1"] ){
        self.markImg.image = [UIImage imageNamed:@"shop_收货地址"];
        self.titleLb.text =[NSString stringWithFormat:@"%@ %@",model.username,model.phone];//@"李二狗 15311112222"
        self.contentLb.text = model.address_format;
        self.contentLb.numberOfLines = 0;
        self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;
        [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(30);
        }];

    }else{
        if (index == 1) {
            self.markImg.image = [UIImage imageNamed:@"mark_物流"];
            _titleLb.textColor = [UIColor whiteColor];
            self.titleLb.text = model.express_state_name;
            self.contentLb.text =model.express_desc;
            self.lineLb.hidden =NO;

        }else if (index == 2){
            self.markImg.image = [UIImage imageNamed:@"shop_收货地址"];
            self.titleLb.text =[NSString stringWithFormat:@"%@ %@",model.username,model.phone];//@"李二狗 15311112222"
            self.contentLb.text = model.address_format;
            self.contentLb.numberOfLines = 0;
            self.contentLb.lineBreakMode = NSLineBreakByWordWrapping;
            [self.contentLb mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(30);
            }];

        }else{
            self.markImg.image = [UIImage imageNamed:@"shop_留言"];
            self.titleLb.text = YZMsg(@"买家留言");
            self.contentLb.text = model.message;
        }

    }
}
@end
