//
//  OrderDetailModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/17.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderDetailModel.h"

@implementation OrderDetailModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.express_info = [dic valueForKey:@"express_info"];
        self.express_desc = minstr([self.express_info valueForKey:@"desc"]);
        self.express_state_name = minstr([self.express_info valueForKey:@"state_name"]);
        
        self.order_info = [dic valueForKey:@"order_info"];
        self.order_status = minstr([self.order_info valueForKey:@"status"]);
        self.is_append_evaluate =minstr([self.order_info valueForKey:@"is_append_evaluate"]);
        self.is_refund = minstr([self.order_info valueForKey:@"is_refund"]);
        self.refund_status = minstr([self.order_info valueForKey:@"refund_status"]);
        self.order_status_name = minstr([self.order_info valueForKey:@"status_name"]);
        self.order_status_desc = minstr([self.order_info valueForKey:@"status_desc"]);
        self.order_username  =minstr([self.order_info valueForKey:@"username"]);
        self.order_phone  =minstr([self.order_info valueForKey:@"phone"]);
        self.order_province = minstr([self.order_info valueForKey:@"province"]);
        self.order_city = minstr([self.order_info valueForKey:@"city"]);
        self.order_area = minstr([self.order_info valueForKey:@"area"]);
        self.order_address  =minstr([self.order_info valueForKey:@"address"]);
        self.order_message =minstr([self.order_info valueForKey:@"message"]);
        
        self.order_goods_name = minstr([self.order_info valueForKey:@"goods_name"]);//商品名称
        self.order_price= minstr([self.order_info valueForKey:@"price"]);//单价
        self.order_nums= minstr([self.order_info valueForKey:@"nums"]);//个数
        self.orderspec_thumb_format= minstr([self.order_info valueForKey:@"spec_thumb_format"]);//商品图片
        self.order_spec_name= minstr([self.order_info valueForKey:@"spec_name"]);//商品规格
        self.order_postage= minstr([self.order_info valueForKey:@"postage"]);//运费
        self.order_total= minstr([self.order_info valueForKey:@"total"]);//实付款
        self.order_no = minstr([self.order_info valueForKey:@"orderno"]);
        self.order_payType = minstr([self.order_info valueForKey:@"type"]);
        self.order_addtime = minstr([self.order_info valueForKey:@"addtime"]);
        self.order_paytime = minstr([self.order_info valueForKey:@"paytime"]);
        self.order_id = minstr([self.order_info valueForKey:@"id"]);

        
        self.shop_info = [dic valueForKey:@"shop_info"];
        self.shop_name= minstr([self.shop_info valueForKey:@"name"]);//店铺名称
        self.shop_uid = minstr([self.shop_info valueForKey:@"uid"]);
        self.shop_avatar =minstr([self.shop_info valueForKey:@"avatar"]);
        self.shop_phone = minstr([self.shop_info valueForKey:@"service_phone"]);
        self.isattention = minstr([self.shop_info valueForKey:@"isattention"]);

    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)subdic{
    OrderDetailModel *model = [[OrderDetailModel alloc]initWithDic:subdic];
    return model;
}

@end
