//
//  OrderDetailModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/17.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface OrderDetailModel : NSObject

@property (nonatomic, strong)NSDictionary *express_info;
@property (nonatomic, strong)NSDictionary *order_info;
@property (nonatomic, strong)NSDictionary *shop_info;

@property (nonatomic, strong)NSString *order_status_desc;//头部内容
@property (nonatomic, strong)NSString *order_status_name;//头部状态
@property (nonatomic, strong)NSString *order_status;//订单类型
@property (nonatomic, strong)NSString *is_append_evaluate;
@property (nonatomic, strong)NSString *refund_status;
@property (nonatomic, strong)NSString *is_refund;//是否可退款 0 否 1 是
@property (nonatomic, strong)NSString *order_username;//收货人名字
@property (nonatomic, strong)NSString *order_phone;//电话
@property (nonatomic, strong)NSString *order_address;//详细地址
@property (nonatomic, strong)NSString *order_city;//市
@property (nonatomic, strong)NSString *order_area;//区
@property (nonatomic, strong)NSString *order_province;//省
@property (nonatomic, strong)NSString *order_message;//留言
@property (nonatomic, strong)NSString *order_goods_name;//商品名称
@property (nonatomic, strong)NSString *order_price;//单价
@property (nonatomic, strong)NSString *order_nums;//个数
@property (nonatomic, strong)NSString *orderspec_thumb_format;//商品图片
@property (nonatomic, strong)NSString *order_spec_name;//商品规格
@property (nonatomic, strong)NSString *order_postage;//运费
@property (nonatomic, strong)NSString *order_total;//实付款
@property (nonatomic, strong)NSString *order_no;//订单编号
@property (nonatomic, strong)NSString *order_addtime;//下单时间
@property (nonatomic, strong)NSString *order_paytime;//支付时间
@property (nonatomic, strong)NSString *order_payType;//支付方式
@property (nonatomic, strong)NSString *order_id;//订单id
@property (nonatomic, strong)NSString *isattention;

@property (nonatomic, strong)NSString *shop_name;//店铺名称
@property (nonatomic, strong)NSString *shop_uid;//店铺名称
@property (nonatomic, strong)NSString *shop_avatar;//头像
@property (nonatomic, strong)NSString *shop_phone;//电话
@property (nonatomic, strong)NSString *express_desc;//包裹详细数据
@property (nonatomic, strong)NSString *express_state_name;//包裹标题


-(instancetype)initWithDic:(NSDictionary *)dic;

+(instancetype)modelWithDic:(NSDictionary *)subdic;

@end

NS_ASSUME_NONNULL_END
