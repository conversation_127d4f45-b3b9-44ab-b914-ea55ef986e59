//
//  OrderDetailVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/7.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderDetailVC.h"
#import "OrderHeaderView.h"
#import "OrderPublicView.h"
#import "OrderInfoView.h"
#import "OrderPriceView.h"
#import "OrderDetailModel.h"
#import "PayOrderView.h"
#import "PublishEvaluateVC.h"
#import "OrderModel.h"
#import "AppendEvaluateVC.h"
@interface OrderDetailVC (){
    OrderHeaderView*headView;
    OrderInfoView *infosView;
    OrderPublicView *messagePublicView;//买家留言
}
@property(nonatomic, strong)UIScrollView *backScroll;
@property(nonatomic, strong)OrderDetailModel *orderModel;
@property (nonatomic, strong)PayOrderView *payView;

@end

@implementation OrderDetailVC
-(void)doReturn{
    if ([self.fromWhere isEqual:@"orderDetail"]) {
        NSInteger index = (NSInteger)[[self.navigationController viewControllers] indexOfObject:self];
        if (index > 2) {
               [self.navigationController popToViewController:[self.navigationController.viewControllers objectAtIndex:(index-2)] animated:YES];
        }
    }else{
        [[XGGAppDelegate sharedAppDelegate]popViewController:YES];

    }
}
#pragma mark-----获取订单详情--------
-(void)requestOrderDetail{
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.orderId,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    
    [YBNetworking postWithUrl:@"Buyer.getGoodsOrderInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {

            NSDictionary *infos = [info firstObject];
            self.orderModel = [OrderDetailModel modelWithDic:infos];
            [self creatUI];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];

}
-(void)creatUI{
//['status']  订单状态  -1 已关闭  0 待付款 1 待发货 2 待收货 3 待评价 4 已评价 5 退款
//['is_append_evaluate']  订单是否可追加评价 1 可以  0  不可以

    [self creatHeadView];

    if ([self.orderModel.order_status isEqual:@"0"] ||[self.orderModel.order_status isEqual:@"-1"]) {
        OrderPublicView* publicView = [[OrderPublicView alloc]init];
        [_backScroll addSubview:publicView];
        [publicView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(headView.mas_bottom);
            make.height.mas_equalTo(75);
        }];
        [publicView setModelData:self.orderModel AndIndex:1];
        publicView.lineLb.hidden = NO;

        infosView = [[OrderInfoView alloc]init];
        infosView.model = self.orderModel;
        [_backScroll addSubview:infosView];
        [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(publicView.mas_bottom);
            make.left.right.equalTo(self.view);
            make.height.mas_equalTo(140);
        }];
    }else if ([self.orderModel.order_status isEqual:@"2"] ||[self.orderModel.order_status isEqual:@"3"]||[self.orderModel.order_status isEqual:@"4"]){
        OrderPublicView* publicView = [[OrderPublicView alloc]init];
        [_backScroll addSubview:publicView];
        [publicView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(headView.mas_bottom);
            make.height.mas_equalTo(60);
        }];
        [publicView setModelData:self.orderModel AndIndex:1];
        
        OrderPublicView* publicView1 = [[OrderPublicView alloc]init];
        [_backScroll addSubview:publicView1];
        [publicView1 mas_makeConstraints:^(MASConstraintMaker *make) {
             make.left.right.equalTo(self.view);
             make.top.equalTo(publicView.mas_bottom);
             make.height.mas_equalTo(75);
         }];
        [publicView1 setModelData:self.orderModel AndIndex:2];
        
        //如果有买家留言
        if (self.orderModel.order_message.length > 1) {
            messagePublicView = [[OrderPublicView alloc]init];
            [_backScroll addSubview:messagePublicView];
             [messagePublicView mas_makeConstraints:^(MASConstraintMaker *make) {
                 make.left.right.equalTo(self.view);
                 make.top.equalTo(publicView1.mas_bottom);
                 make.height.mas_equalTo(60);
             }];
            [messagePublicView setModelData:self.orderModel AndIndex:3];
            messagePublicView.lineLb.hidden = NO;
        }else{
            publicView1.lineLb.hidden = NO;
        }

        infosView = [[OrderInfoView alloc]init];
        infosView.model = self.orderModel;
        [_backScroll addSubview:infosView];
        if (self.orderModel.order_message.length > 0) {
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(messagePublicView.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(140);
            }];
        }else{
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(publicView1.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(140);
            }];
        }

    }else if ([self.orderModel.order_status isEqual:@"1"]){

        OrderPublicView* publicView = [[OrderPublicView alloc]init];
        [_backScroll addSubview:publicView];
        [publicView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(headView.mas_bottom);
            make.height.mas_equalTo(75);
        }];
        [publicView setModelData:self.orderModel AndIndex:1];
        
        if (self.orderModel.order_message.length > 1) {
            CGFloat textHeight = [PublicObj heightOfString:self.orderModel.order_message andFont:[UIFont systemFontOfSize:12] andWidth:_window_width-50];
            
            messagePublicView = [[OrderPublicView alloc]init];
            [_backScroll addSubview:messagePublicView];
             [messagePublicView mas_makeConstraints:^(MASConstraintMaker *make) {
                 make.left.right.equalTo(self.view);
                 make.top.equalTo(publicView.mas_bottom);
                 make.height.mas_equalTo(50+textHeight);
             }];
            [messagePublicView setModelData:self.orderModel AndIndex:3];
            messagePublicView.lineLb.hidden = NO;
        }else{
            publicView.lineLb.hidden = NO;
        }

        infosView = [[OrderInfoView alloc]init];
        infosView.model = self.orderModel;

        [_backScroll addSubview:infosView];
        if (self.orderModel.order_message.length > 0) {
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(messagePublicView.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(140);
            }];
        }else{
            [infosView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(publicView.mas_bottom);
                make.left.right.equalTo(self.view);
                make.height.mas_equalTo(140);
            }];
        }
    }
    OrderPriceView *price = [[OrderPriceView alloc]init];
    [_backScroll addSubview:price];
    [price mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(infosView.mas_bottom);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(108);
    }];
    [price setModelData:self.orderModel];
    
    [_backScroll layoutSubviews];
    NSArray *orderArr;
    if ([self.orderModel.order_status isEqual:@"0"]||[self.orderModel.order_status isEqual:@"-1"]) {
        orderArr = @[YZMsg(@"订单编号"),YZMsg(@"下单时间")];
    }else{
        orderArr = @[YZMsg(@"订单编号"),YZMsg(@"下单时间"),YZMsg(@"支付方式"),YZMsg(@"支付时间")];
    }
    UIView *backView = [[UIView alloc]init];
    backView.backgroundColor = Normal_SubColor;
    backView.frame = CGRectMake(0, price.bottom, _window_width, 40*orderArr.count);
    [_backScroll addSubview:backView];
    
    for (int i = 0; i < orderArr.count; i ++) {
        UILabel *lb = [[UILabel alloc]init];
        lb.frame = CGRectMake(12, 40*i, _window_width*0.8, 40);
        lb.font = [UIFont systemFontOfSize:14];
        lb.textColor = [UIColor grayColor];
        lb.text = orderArr[i];
        [backView addSubview:lb];
        if (i == 0) {
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],self.orderModel.order_no];
            
            UIButton *copyBtn = [UIButton buttonWithType:0];
            copyBtn.frame = CGRectMake(lb.right+10, 0, 50, 40);
            [copyBtn setTitle:YZMsg(@"复制") forState:0];
            [copyBtn setTitleColor:Pink_Cor forState:0];
            copyBtn.titleLabel.font = [UIFont systemFontOfSize:14];
            [copyBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

            [backView addSubview:copyBtn];
        }else if (i == 1){
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],self.orderModel.order_addtime];

        }else if (i == 2){
            NSString *pay;
            if ([self.orderModel.order_payType isEqual:@"1"]) {
                pay = YZMsg(@"支付宝");
            }else if ([self.orderModel.order_payType isEqual:@"2"]){
                pay = YZMsg(@"微信");
            }else{
                pay = YZMsg(@"余额");
            }
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],pay];

        }else{
            lb.text = [NSString stringWithFormat:@"%@: %@",orderArr[i],self.orderModel.order_paytime];
        }
    }
    _backScroll.contentSize = CGSizeMake(_window_width, CGRectGetMaxY(backView.frame)+60);

    [self creatBottomUI];
}
-(void)creatBottomUI{
    //['status']  订单状态  -1 已关闭  0 待付款 1 待发货 2 待收货 3 待评价 4 已评价 5 退款
    //['is_append_evaluate']  订单是否可追加评价 1 可以  0  不可以

    UIView *bottomView = [[UIView alloc]init];
    bottomView.frame= CGRectMake(0, _window_height-50, _window_width, 50);
    bottomView.backgroundColor = Normal_SubColor;
    [self.view addSubview:bottomView];
    
    if ([self.orderModel.order_status isEqual:@"0"]) {
        UIButton *payBtn = [UIButton buttonWithType:0];
        payBtn.frame = CGRectMake(_window_width-70, 12, 55, 26);
        [payBtn setTitle:YZMsg(@"付款") forState:0];
        [payBtn setTitleColor:Pink_Cor forState:0];
        payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        payBtn.layer.borderColor = Pink_Cor.CGColor;
        payBtn.layer.cornerRadius = 13;
        payBtn.layer.borderWidth = 1;
        payBtn.layer.masksToBounds = YES;
        [payBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
        [bottomView addSubview:payBtn];
        
        UIButton *cancelBtn = [UIButton buttonWithType:0];
        cancelBtn.frame = CGRectMake(payBtn.left-90, 12, 83, 26);
        [cancelBtn setTitle:YZMsg(@"取消订单") forState:0];
        [cancelBtn setTitleColor:Normal_TextColor forState:0];
        cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        cancelBtn.layer.borderColor = Normal_TextColor.CGColor;
        cancelBtn.layer.cornerRadius = 13;
        cancelBtn.layer.borderWidth = 1;
        cancelBtn.layer.masksToBounds = YES;
        [cancelBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:cancelBtn];
    }else if ([self.orderModel.order_status isEqual:@"2"]) {
        UIButton *payBtn = [UIButton buttonWithType:0];
        payBtn.frame = CGRectMake(_window_width-98, 12, 83, 26);
        if ([lagType isEqual:EN]) {
            payBtn.frame = CGRectMake(_window_width-98, 12, 83+15, 26);
        }
        [payBtn setTitle:YZMsg(@"确认收货") forState:0];
        [payBtn setTitleColor:Pink_Cor forState:0];
        payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        payBtn.layer.borderColor = Pink_Cor.CGColor;
        payBtn.layer.cornerRadius = 13;
        payBtn.layer.borderWidth = 1;
        payBtn.layer.masksToBounds = YES;
        [payBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:payBtn];
        
        UIButton *cancelBtn = [UIButton buttonWithType:0];
        cancelBtn.frame = CGRectMake(payBtn.left-90, 12, 83, 26);
        if ([lagType isEqual:EN]) {
            cancelBtn.frame = CGRectMake(payBtn.left-90-15, 12, 83+15, 26);
        }
        [cancelBtn setTitle:YZMsg(@"查看物流") forState:0];
        [cancelBtn setTitleColor:Normal_TextColor forState:0];
        cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        cancelBtn.layer.borderColor = Normal_TextColor.CGColor;
        cancelBtn.layer.cornerRadius = 13;
        cancelBtn.layer.borderWidth = 1;
        cancelBtn.layer.masksToBounds = YES;
        [cancelBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:cancelBtn];

    }else if ([self.orderModel.order_status isEqual:@"3"]){
        UIButton *payBtn = [UIButton buttonWithType:0];
        payBtn.frame = CGRectMake(_window_width-70, 12, 55, 26);
        [payBtn setTitle:YZMsg(@"评价") forState:0];
        [payBtn setTitleColor:Pink_Cor forState:0];
        payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        payBtn.layer.borderColor = Pink_Cor.CGColor;
        payBtn.layer.cornerRadius = 13;
        payBtn.layer.borderWidth = 1;
        payBtn.layer.masksToBounds = YES;
        [payBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:payBtn];
        
        UIButton *cancelBtn = [UIButton buttonWithType:0];
        cancelBtn.frame = CGRectMake(payBtn.left-90, 12, 83, 26);
        [cancelBtn setTitle:YZMsg(@"查看物流") forState:0];
        [cancelBtn setTitleColor:Normal_TextColor forState:0];
        cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        cancelBtn.layer.borderColor = Normal_TextColor.CGColor;
        cancelBtn.layer.cornerRadius = 13;
        cancelBtn.layer.borderWidth = 1;
        cancelBtn.layer.masksToBounds = YES;
        [cancelBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:cancelBtn];

    }else if ([self.orderModel.order_status isEqual:@"-1"]){
        UIButton *payBtn = [UIButton buttonWithType:0];
        payBtn.frame = CGRectMake(_window_width-98, 12, 83, 26);
        [payBtn setTitle:YZMsg(@"删除订单") forState:0];
        [payBtn setTitleColor:Normal_TextColor forState:0];
        payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        payBtn.layer.borderColor = Normal_TextColor.CGColor;
        payBtn.layer.cornerRadius = 13;
        payBtn.layer.borderWidth = 1;
        payBtn.layer.masksToBounds = YES;
        [payBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:payBtn];

    }else if ([self.orderModel.order_status isEqual:@"4"] && [self.orderModel.is_append_evaluate isEqual:@"1"] ) {
        UIButton *payBtn = [UIButton buttonWithType:0];
        payBtn.frame = CGRectMake(_window_width-98, 12, 83, 26);
        [payBtn setTitle:YZMsg(@"追加评价") forState:0];
        [payBtn setTitleColor:Pink_Cor forState:0];
        payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        payBtn.layer.borderColor = Pink_Cor.CGColor;
        payBtn.layer.cornerRadius = 13;
        payBtn.layer.borderWidth = 1;
        payBtn.layer.masksToBounds = YES;
        [payBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:payBtn];
        
        UIButton *cancelBtn = [UIButton buttonWithType:0];
        cancelBtn.frame = CGRectMake(payBtn.left-90, 12, 83, 26);
        [cancelBtn setTitle:YZMsg(@"查看物流") forState:0];
        [cancelBtn setTitleColor:Normal_TextColor forState:0];
        cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        cancelBtn.layer.borderColor = Normal_TextColor.CGColor;
        cancelBtn.layer.cornerRadius = 13;
        cancelBtn.layer.borderWidth = 1;
        cancelBtn.layer.masksToBounds = YES;
        [cancelBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:cancelBtn];

    }else if ([self.orderModel.order_status isEqual:@"4"] && [self.orderModel.is_append_evaluate isEqual:@"0"] ){
        UIButton *cancelBtn = [UIButton buttonWithType:0];
        cancelBtn.frame = CGRectMake(_window_width-98, 12, 83, 26);
        [cancelBtn setTitle:YZMsg(@"删除订单") forState:0];
        [cancelBtn setTitleColor:Normal_TextColor forState:0];
        cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        cancelBtn.layer.borderColor = Normal_TextColor.CGColor;
        cancelBtn.layer.cornerRadius = 13;
        cancelBtn.layer.borderWidth = 1;
        cancelBtn.layer.masksToBounds = YES;
        [cancelBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];

        [bottomView addSubview:cancelBtn];

    }
}
-(void)btnClick:(UIButton *)sender{
    NSString *titles =sender.titleLabel.text;
    if ([titles isEqual:YZMsg(@"付款")]) {
        [self showPayView:self.orderModel.order_id];
    }else if ([titles isEqual:YZMsg(@"取消订单")]){
        [self cancelGoodsOrder];
    }else if ([titles isEqual:YZMsg(@"确认收货")]){
        UIAlertController *cancelAlert = [UIAlertController alertControllerWithTitle:YZMsg(@"确定已收到商品？") message:nil preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancle = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
        }];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self receiveGoodsOrder];
        }];
        [cancelAlert addAction:cancle];
        [cancelAlert addAction:sureAction];
        [cancle setValue:[UIColor lightGrayColor] forKey:@"titleTextColor"];
        [sureAction setValue:Pink_Cor forKey:@"titleTextColor"];

        [self presentViewController:cancelAlert animated:YES completion:nil];

    }else if ([titles isEqual:YZMsg(@"查看物流")]){
        [self logistics];
    }else if ([titles isEqual:YZMsg(@"评价")]){
        [self toEvaluate];
    }else if ([titles isEqual:YZMsg(@"追加评价")]){
        [self toAppendEvaluate];

    }else if ([titles isEqual:YZMsg(@"删除订单")]){
        [self delGoodsOrder];
    }else if ([titles isEqual:YZMsg(@"复制")]){
        [self copyOrderNum];
    }
}
-(NSString *)addurl:(NSString *)url addModel:(OrderDetailModel *)models{
    return [url stringByAppendingFormat:@"&uid=%@&token=%@&orderid=%@&user_type=buyer",[Config getOwnID],[Config getOwnToken],models.order_id];
}
#pragma mark-----复制订单号-------
-(void)copyOrderNum{
    UIPasteboard *paste = [UIPasteboard generalPasteboard];
    paste.string = self.orderModel.order_no;
    [MBProgressHUD showError:YZMsg(@"复制成功")];

}
#pragma mark-----查看物流------
-(void)logistics{
    PubH5 *h5VC = [[PubH5 alloc]init];
    NSString *url =[NSString stringWithFormat:@"%@/appapi/express/index",h5url];
    h5VC.url =  [self addurl:url addModel:self.orderModel];;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:h5VC animated:YES];

}
#pragma mark---付款------
-(void)showPayView:(NSString *)orderID{
    YBWeakSelf;
    if (_payView) {
        [_payView removeFromSuperview];
        _payView = nil;
    }

    _payView = [[PayOrderView alloc]initWithPrice:self.orderModel.order_total AndOrderId:orderID AndShopName:self.orderModel.shop_name];
    _payView.frame = CGRectMake(0, 0, _window_width, _window_height);
    _payView.hideEvent = ^(BOOL paySuccess) {
        [weakSelf.payView removeFromSuperview];
        weakSelf.payView = nil;
        [weakSelf requestOrderDetail];
    };
//    _payView.hideEvent = ^{
//        [weakSelf.payView removeFromSuperview];
//        weakSelf.payView = nil;
//    };
    [self.view addSubview:_payView];
}
#pragma mark----评价---------
-(void)toEvaluate{
    OrderModel *modells = [[OrderModel alloc]init];
    modells.spec_thumb = self.orderModel.orderspec_thumb_format;
    modells.goods_name =self.orderModel.order_goods_name;
    modells.spec_name = self.orderModel.order_spec_name;
    modells.nums = self.orderModel.order_nums;
    modells.idStr = self.orderModel.order_id;
    PublishEvaluateVC *evaluate = [[PublishEvaluateVC alloc]init];
    evaluate.model = modells;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:evaluate animated:YES];
}
-(void)toAppendEvaluate{
    OrderModel *modells = [[OrderModel alloc]init];
    modells.spec_thumb = self.orderModel.orderspec_thumb_format;
    modells.goods_name =self.orderModel.order_goods_name;
    modells.spec_name = self.orderModel.order_spec_name;
    modells.nums = self.orderModel.order_nums;
    modells.idStr = self.orderModel.order_id;

    AppendEvaluateVC *append = [[AppendEvaluateVC alloc]init];
    append.model = modells;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:append animated:YES];

}
#pragma mark-----取消订单-------
-(void)cancelGoodsOrder{
    
    UIAlertController *alertControl =[UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"确定取消订单?") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSString *url = [purl stringByAppendingFormat:@"?service=Buyer.cancelGoodsOrder"];
        
        NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
        NSString *sign = [PublicObj sortString:signdic];

        NSDictionary *dic = @{
                              @"uid":[Config getOwnID],
                              @"token":[Config getOwnToken],
                              @"orderid":self.orderId,
                              @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                              @"sign":sign
                              };
        
        [YBNetworking postWithUrl:@"Buyer.cancelGoodsOrder" Dic:dic Suc:^(int code, id info, NSString *msg) {
            if (code ==0) {
                [MBProgressHUD showError: msg];
                [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
            }else{
                [MBProgressHUD showError:msg];
            }

            } Fail:^(id fail) {
                
         }];

    }];
    [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    [sureAction setValue:Normal_Color forKey:@"_titleTextColor"];
    [alertControl addAction:cancelAction];
    [alertControl addAction:sureAction];

    [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:alertControl animated:YES completion:nil];
}
#pragma mark-----确认收货-------
-(void)receiveGoodsOrder{
    NSString *url = [purl stringByAppendingFormat:@"?service=Buyer.receiveGoodsOrder"];
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.orderId,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    
    [YBNetworking postWithUrl:@"Buyer.receiveGoodsOrder" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            [MBProgressHUD showError: msg];
            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];
}
#pragma mark-----删除订单-------
-(void)delGoodsOrder{
    NSString *url = [purl stringByAppendingFormat:@"?service=Buyer.delGoodsOrder"];
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"orderid":self.orderId,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    
    [YBNetworking postWithUrl:@"Buyer.delGoodsOrder" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            [MBProgressHUD showError: msg];
            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBarHidden = YES;

    self.view.backgroundColor = [UIColor whiteColor];
    _backScroll = [[UIScrollView alloc]init];
    _backScroll.backgroundColor = Normal_BackColor;
    [self.view addSubview:self.backScroll];
    [_backScroll mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(self.view);
    }];
    if (@available(iOS 11.0, *)) {
        _backScroll.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
        self.automaticallyAdjustsScrollViewInsets = NO;
    }

    UIButton *returnBtn = [UIButton buttonWithType:0];
    returnBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
    [returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:returnBtn];
    
    [self requestOrderDetail];
}
-(void)creatHeadView{
    headView = [[OrderHeaderView alloc]init];
    headView.frame = CGRectMake(0, 0, _window_width, 135);
    [headView setBuyerData:self.orderModel];
    [_backScroll  addSubview:headView];
    [headView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_backScroll);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(135);
    }];
}
@end
