//
//  OrderListCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/7.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderListCell.h"
#import "shopDetailVC.h"

@implementation OrderListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    
    /// 待付款
    [_dfk_fukuanBtn setTitle:YZMsg(@"付款") forState:0];
    [_dfk_qxddBtn setTitle:YZMsg(@"取消订单") forState:0];

    /// 待收货
    [_dsh_sureBtn setTitle:YZMsg(@"确认收货") forState:0];
    [_dsh_ckwlBtn setTitle:YZMsg(@"查看物流") forState:0];

    /// 待评价
    [_dpj_pjBtn setTitle:YZMsg(@"评价") forState:0];

    /// 退款
    [_tk_tkxqBtn setTitle:YZMsg(@"退款详情") forState:0];

    /// 已关闭
    [_ygb_scddBtn setTitle:YZMsg(@"删除订单") forState:0];

    /// 退款中
    [_tkz_tkxqBtn setTitle:YZMsg(@"退款详情") forState:0];

    /// 已评价可追加
    [_ypjkzj_zjpjBtn setTitle:YZMsg(@"追加评价") forState:0];
    [_ypjkzj_ckwlBtn setTitle:YZMsg(@"查看物流") forState:0];
    [_ypjkzj_scddBtn setTitle:YZMsg(@"删除订单") forState:0];

    /// 已评价不可追加
    [_ypjbkzj_scBtn setTitle:YZMsg(@"删除订单") forState:0];

    
    
    
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}
-(void)setModel:(OrderModel *)model
{
    _model = model;
    [self.shopNameBtn setTitle:[model.shop_info valueForKey:@"name"] forState:0];
    self.statusLb.text = model.status_name;
    [self.commodityImg sd_setImageWithURL:[NSURL URLWithString:model.spec_thumb]];
    self.commNameLb.text = model.goods_name;
    self.standardLb.text =model.spec_name;
    self.priceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), model.price];
    self.countLb.text = [NSString stringWithFormat:@"x%@",model.nums];
    self.allPriceLb.text = [NSString stringWithFormat:YZMsg(@"共%@件商品,合计¥%@"),model.nums, model.total];
}
- (IBAction)btnClick:(UIButton *)sender {
    NSLog(@"点击了标题------:%@",sender.titleLabel.text);
    [self.delegate btnClickWithTitle:sender.titleLabel.text AndModel:_model];
}
- (IBAction)shopTitleClick:(UIButton *)sender {
    
    shopDetailVC *vc = [[shopDetailVC alloc]init];
    vc.toUserID = _model.shop_uid;
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

}


@end
