//
//  OrderModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/7.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderModel.h"

@implementation OrderModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        
        self.goods_name = minstr([dic valueForKey:@"goods_name"]);
        self.goodsid = minstr([dic valueForKey:@"goodsid"]);
        self.idStr= minstr([dic valueForKey:@"id"]);
        self.is_append_evaluate= minstr([dic valueForKey:@"is_append_evaluate"]);
        self.nums= minstr([dic valueForKey:@"nums"]);
        self.price= minstr([dic valueForKey:@"price"]);
        self.shop_info= [dic valueForKey:@"shop_info"];
        self.shop_uid= minstr([dic valueForKey:@"shop_uid"]);
        self.spec_name= minstr([dic valueForKey:@"spec_name"]);
        self.spec_thumb= minstr([dic valueForKey:@"spec_thumb"]);
        self.status= minstr([dic valueForKey:@"status"]);
        self.status_name= minstr([dic valueForKey:@"status_name"]);
        self.total= minstr([dic valueForKey:@"total"]);
        self.uid= minstr([dic valueForKey:@"uid"]);
        self.refund_status= minstr([dic valueForKey:@"refund_status"]);

        self.type = minstr([dic valueForKey:@"type"]);
    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)subdic{
    OrderModel *model = [[OrderModel alloc]initWithDic:subdic];
    return model;
}

@end
