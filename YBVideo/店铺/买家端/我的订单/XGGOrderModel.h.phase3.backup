//
//  OrderModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/7.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface OrderModel : NSObject

@property (nonatomic, strong)NSString *goods_name;
@property (nonatomic, strong)NSString *goodsid;
@property (nonatomic, strong)NSString *idStr;
@property (nonatomic, strong)NSString *is_append_evaluate;
@property (nonatomic, strong)NSString *nums;
@property (nonatomic, strong)NSString *price;
@property (nonatomic, strong)NSDictionary *shop_info;
@property (nonatomic, strong)NSString *shop_uid;
@property (nonatomic, strong)NSString *spec_name;
@property (nonatomic, strong)NSString *spec_thumb;
@property (nonatomic, strong)NSString *status;
@property (nonatomic, strong)NSString *status_name;
@property (nonatomic, strong)NSString *total;
@property (nonatomic, strong)NSString *uid;
@property (nonatomic, strong)NSString *refund_status;


@property (nonatomic, strong)NSString *type;
@property (nonatomic, assign)CGFloat height;

-(instancetype)initWithDic:(NSDictionary *)dic;
+(instancetype)modelWithDic:(NSDictionary *)subdic;
@end

NS_ASSUME_NONNULL_END
