//
//  OrderListCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/7.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "OrderModel.h"
NS_ASSUME_NONNULL_BEGIN
@protocol orderCellBtnDelegate <NSObject>

-(void)btnClickWithTitle:(NSString *)titls AndModel:(OrderModel *)models;

@end

@interface OrderListCell : UITableViewCell
@property (weak, nonatomic) IBOutlet UIButton *shopNameBtn;
@property (weak, nonatomic) IBOutlet UILabel *statusLb;
@property (weak, nonatomic) IBOutlet UIImageView *commodityImg;
@property (weak, nonatomic) IBOutlet UILabel *commNameLb;
@property (weak, nonatomic) IBOutlet UILabel *standardLb;
@property (weak, nonatomic) IBOutlet UILabel *priceLb;
@property (weak, nonatomic) IBOutlet UILabel *countLb;
@property (weak, nonatomic) IBOutlet UILabel *allPriceLb;
@property (nonatomic, assign)id<orderCellBtnDelegate>delegate;

@property (nonatomic, strong)OrderModel *model;

/// 待付款
@property (weak, nonatomic) IBOutlet UIButton *dfk_fukuanBtn;
@property (weak, nonatomic) IBOutlet UIButton *dfk_qxddBtn;

/// 待收货
@property (weak, nonatomic) IBOutlet UIButton *dsh_sureBtn;
@property (weak, nonatomic) IBOutlet UIButton *dsh_ckwlBtn;

/// 待评价
@property (weak, nonatomic) IBOutlet UIButton *dpj_pjBtn;

/// 退款
@property (weak, nonatomic) IBOutlet UIButton *tk_tkxqBtn;

/// 已关闭
@property (weak, nonatomic) IBOutlet UIButton *ygb_scddBtn;

/// 退款中
@property (weak, nonatomic) IBOutlet UIButton *tkz_tkxqBtn;

/// 已评价可追加
@property (weak, nonatomic) IBOutlet UIButton *ypjkzj_zjpjBtn;
@property (weak, nonatomic) IBOutlet UIButton *ypjkzj_ckwlBtn;
@property (weak, nonatomic) IBOutlet UIButton *ypjkzj_scddBtn;

/// 已评价不可追加
@property (weak, nonatomic) IBOutlet UIButton *ypjbkzj_scBtn;



@end

NS_ASSUME_NONNULL_END
