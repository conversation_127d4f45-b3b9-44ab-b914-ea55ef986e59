<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16086"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="70" id="KGk-i7-Jjw" customClass="LookHistoryCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="70"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="70"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zHe-OP-G96">
                        <rect key="frame" x="10" y="20" width="30" height="30"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="30" id="CHW-7a-97N"/>
                            <constraint firstAttribute="height" constant="30" id="ZnI-F8-9IB"/>
                        </constraints>
                        <state key="normal" image="记录未选.png"/>
                    </button>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="u6G-Nw-AQJ">
                        <rect key="frame" x="45" y="10" width="50" height="50"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="50" id="JZw-rh-egh"/>
                            <constraint firstAttribute="width" constant="50" id="fIE-BE-fEC"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="名称" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Ds-Dn-AZo">
                        <rect key="frame" x="103" y="10" width="147" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="ekL-3Z-ePd"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="¥0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ITS-ci-gK3">
                        <rect key="frame" x="103" y="40" width="18" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="HpI-QO-adX"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" systemColor="systemRedColor" red="1" green="0.23137254900000001" blue="0.18823529410000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="01P-L9-sJQ">
                        <rect key="frame" x="103" y="69" width="205" height="1"/>
                        <color key="backgroundColor" cocoaTouchSystemColor="groupTableViewBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="i1u-Kh-ERa"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Thy-PK-jk6">
                        <rect key="frame" x="129" y="40" width="35.5" height="20"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" systemColor="systemGrayColor" red="0.5568627451" green="0.5568627451" blue="0.57647058819999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HM7-h9-WGe">
                        <rect key="frame" x="129" y="49.5" width="35.5" height="1"/>
                        <color key="backgroundColor" systemColor="systemGrayColor" red="0.5568627451" green="0.5568627451" blue="0.57647058819999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="RsL-Z5-fLO"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="ITS-ci-gK3" firstAttribute="leading" secondItem="8Ds-Dn-AZo" secondAttribute="leading" id="1t8-il-8HZ"/>
                    <constraint firstItem="HM7-h9-WGe" firstAttribute="trailing" secondItem="Thy-PK-jk6" secondAttribute="trailing" id="43K-LO-5nY"/>
                    <constraint firstItem="zHe-OP-G96" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="4xL-wm-6ko"/>
                    <constraint firstItem="8Ds-Dn-AZo" firstAttribute="top" secondItem="u6G-Nw-AQJ" secondAttribute="top" id="5JS-kz-Mp0"/>
                    <constraint firstItem="HM7-h9-WGe" firstAttribute="centerY" secondItem="Thy-PK-jk6" secondAttribute="centerY" id="6oh-mL-SDa"/>
                    <constraint firstItem="Thy-PK-jk6" firstAttribute="leading" secondItem="ITS-ci-gK3" secondAttribute="trailing" constant="8" id="9aP-5n-kfR"/>
                    <constraint firstAttribute="bottom" secondItem="01P-L9-sJQ" secondAttribute="bottom" id="CZ8-er-jFj"/>
                    <constraint firstAttribute="trailing" secondItem="01P-L9-sJQ" secondAttribute="trailing" constant="12" id="HpX-Ol-AEE"/>
                    <constraint firstItem="HM7-h9-WGe" firstAttribute="leading" secondItem="Thy-PK-jk6" secondAttribute="leading" id="LEy-F2-5bq"/>
                    <constraint firstItem="u6G-Nw-AQJ" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Spc-VW-GpI"/>
                    <constraint firstItem="u6G-Nw-AQJ" firstAttribute="leading" secondItem="zHe-OP-G96" secondAttribute="trailing" constant="5" id="Urw-YD-0ze"/>
                    <constraint firstAttribute="trailing" secondItem="8Ds-Dn-AZo" secondAttribute="trailing" constant="70" id="WHL-YM-PZa"/>
                    <constraint firstItem="8Ds-Dn-AZo" firstAttribute="leading" secondItem="u6G-Nw-AQJ" secondAttribute="trailing" constant="8" id="iNq-e3-jqF"/>
                    <constraint firstItem="01P-L9-sJQ" firstAttribute="leading" secondItem="8Ds-Dn-AZo" secondAttribute="leading" id="pba-jx-bIu"/>
                    <constraint firstItem="ITS-ci-gK3" firstAttribute="bottom" secondItem="u6G-Nw-AQJ" secondAttribute="bottom" id="s20-8T-pIx"/>
                    <constraint firstItem="zHe-OP-G96" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="s8r-a5-faG"/>
                    <constraint firstItem="Thy-PK-jk6" firstAttribute="top" secondItem="ITS-ci-gK3" secondAttribute="top" id="xFl-up-Jee"/>
                    <constraint firstItem="Thy-PK-jk6" firstAttribute="bottom" secondItem="ITS-ci-gK3" secondAttribute="bottom" id="y78-uG-o6f"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="headImg" destination="u6G-Nw-AQJ" id="uRm-fc-3wi"/>
                <outlet property="lineLb" destination="HM7-h9-WGe" id="8Cg-1w-Qtf"/>
                <outlet property="nameLb" destination="8Ds-Dn-AZo" id="zBg-hd-AHK"/>
                <outlet property="original_priceLb" destination="Thy-PK-jk6" id="w5c-rb-f1b"/>
                <outlet property="priceLb" destination="ITS-ci-gK3" id="3Of-Av-Yge"/>
                <outlet property="selBtn" destination="zHe-OP-G96" id="pWI-Sa-J1x"/>
            </connections>
            <point key="canvasLocation" x="137.68115942028987" y="98.4375"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="记录未选.png" width="22" height="22"/>
    </resources>
</document>
