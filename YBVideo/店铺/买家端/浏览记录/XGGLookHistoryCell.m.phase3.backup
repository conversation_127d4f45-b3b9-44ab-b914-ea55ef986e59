//
//  LookHistoryCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/5.
//  Copyright © 2020 cat. All rights reserved.
//

#import "LookHistoryCell.h"

@implementation LookHistoryCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self.selBtn setImage:[UIImage imageNamed:@"记录选中"] forState:UIControlStateSelected];
    [self.selBtn setImage:[UIImage imageNamed:@"记录未选"] forState:UIControlStateNormal];

    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
//    for (UIView *subview in self.subviews) {
//        if ([subview isKindOfClass:NSClassFromString(@"UITableViewCellEditControl")]) {
//            UIControl *control = (UIControl *)subview;
//            [control setSelected:YES];
//        }
//    }
}
+(LookHistoryCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
   LookHistoryCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LookHistoryCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"LookHistoryCell" owner:nil options:nil]objectAtIndex:0];
    }
    return cell;
}
-(void)setModel:(LookHistoryModel *)model
{
    _model = model;
    self.selBtn.selected = _model.isSelected;

    [self.headImg sd_setImageWithURL:[NSURL URLWithString:model.goods_thumb]];
    self.nameLb.text = model.goods_name;
    if ([model.type isEqual:@"1"]) {
        self.original_priceLb.hidden = NO;
        self.lineLb.hidden = NO;
        self.priceLb.text =model.goods_price;
        self.original_priceLb.text = model.original_price;
    }else{
        self.priceLb.text= model.goods_price;
    }
}
@end
