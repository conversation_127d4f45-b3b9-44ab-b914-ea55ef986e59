//
//  LookHistoryCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/5.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "LookHistoryModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface LookHistoryCell : UITableViewCell
@property (weak, nonatomic) IBOutlet UILabel *nameLb;
@property (weak, nonatomic) IBOutlet UILabel *priceLb;
@property (weak, nonatomic) IBOutlet UIButton *selBtn;
@property (weak, nonatomic) IBOutlet UIImageView *headImg;
@property (strong, nonatomic) IBOutlet UILabel *original_priceLb;
@property (strong, nonatomic) IBOutlet UILabel *lineLb;

@property (nonatomic, assign) BOOL isSel;
@property (nonatomic, strong)LookHistoryModel *model;

+(LookHistoryCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end

NS_ASSUME_NONNULL_END
