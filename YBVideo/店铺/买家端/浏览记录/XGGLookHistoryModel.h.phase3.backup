//
//  LookHistoryModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/2/5.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface LookHistoryModel : NSObject

@property (nonatomic, assign)BOOL isSelected;

@property (nonatomic, strong)NSString *addtime;
@property (nonatomic, strong)NSString *goods_name;
@property (nonatomic, strong)NSString *goods_price;
@property (nonatomic, strong)NSString *goods_status;
@property (nonatomic, strong)NSString *goods_thumb;
@property (nonatomic, strong)NSString *goodsid;
@property (nonatomic, strong)NSString *time_format;
@property (nonatomic, strong)NSString *idStr;
@property (nonatomic, strong)NSString *uid;
@property (nonatomic, strong)NSString *type;
@property (nonatomic, strong)NSString *href;
@property (nonatomic, strong)NSString *original_price;

//-(instancetype)initWithDic:(NSDictionary *)dic;
//+(instancetype)modelWithDic:(NSDictionary *)subdic;
@end

NS_ASSUME_NONNULL_END
