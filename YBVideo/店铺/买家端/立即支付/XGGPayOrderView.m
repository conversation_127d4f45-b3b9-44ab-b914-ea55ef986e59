//
//  PayOrderView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/14.
//  Copyright © 2020 cat. All rights reserved.
//

#import "PayOrderView.h"
#import <WXApi.h>
#import "Order.h"
#import <AlipaySDK/AlipaySDK.h>
#import "DataSigner.h"
#import "DataVerifier.h"
#import "YBRechargeType.h"

@implementation PayOrderView

-(instancetype)initWithPrice:(NSString *)price AndOrderId:(NSString *)orderid AndShopName:(NSString *)nameStr{
    self = [super init];
    if (self) {
        self.backgroundColor = RGBA(29, 29, 29, 0.4);
        self.payArr = [NSArray array];
        infoDic = [NSDictionary dictionary];
        index = 0;
        _priceStr = [price stringByReplacingOccurrencesOfString:@"¥" withString:@""];
        _orderIdStr = orderid;
        _shopNameStr = nameStr;
        [self createUI];
        [self requestBalance];
    }
    return self;
}

-(void)createUI{
    UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height*0.4, _window_width, _window_height*0.6)];
    backView.backgroundColor = [UIColor whiteColor];
    [self addSubview:backView];
    
    UIButton *closeBtn = [UIButton buttonWithType:0];
    closeBtn.frame = CGRectMake(_window_width-30, 15, 20, 20);
    [closeBtn setImage:[UIImage imageNamed:@"standardClose"] forState:0];
    [closeBtn addTarget:self action:@selector(closeClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:closeBtn];
    
    UILabel *titlelb = [[UILabel alloc]init];
    titlelb.frame = CGRectMake(0, 10, _window_width, 30);
    titlelb.font = [UIFont systemFontOfSize:14];
    titlelb.textAlignment = NSTextAlignmentCenter;
    titlelb.textColor = [UIColor blackColor];
    titlelb.text = YZMsg(@"收银台");
    [backView addSubview:titlelb];
    [PublicObj lineViewWithFrame:CGRectMake(0, titlelb.bottom+10, _window_width, 1) andColor:RGB(240, 240, 240) andView:backView];
    
    UILabel *priceLb = [[UILabel alloc]init];
    priceLb.font = [UIFont systemFontOfSize:20];
    priceLb.text = _priceStr;
    priceLb.textColor = [UIColor blackColor];
    [backView addSubview:priceLb];
    [priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(backView);
        make.top.equalTo(titlelb.mas_bottom).offset(30);
    }];
    UILabel *fuhao =[[UILabel alloc]init];
    fuhao.font = [UIFont systemFontOfSize:14];
    fuhao.text = @"¥";
    fuhao.textColor = [UIColor blackColor];
    [backView addSubview:fuhao];
    [fuhao mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(priceLb.mas_left);
        make.bottom.equalTo(priceLb.mas_bottom);
    }];
    
    UILabel *shopNameLb = [[UILabel alloc]init];
    shopNameLb.font = [UIFont systemFontOfSize:14];
    shopNameLb.text = [NSString stringWithFormat:@"%@%@",[common shop_system_name],YZMsg(@"支付")];//_shopNameStr;
    shopNameLb.textColor = [UIColor grayColor];
    [backView addSubview:shopNameLb];
    [shopNameLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(priceLb.mas_bottom).offset(5);
        make.centerX.equalTo(backView);
    }];
    [backView layoutIfNeeded];
    payTable = [[UITableView alloc]initWithFrame:CGRectMake(15, shopNameLb.bottom, _window_width-30, backView.height-shopNameLb.bottom-80) style:UITableViewStylePlain];
    payTable.delegate = self;
    payTable.dataSource = self;
    payTable.separatorStyle = UITableViewCellSeparatorStyleNone;
    [backView addSubview:payTable];
    
    UIButton *payBtn = [UIButton buttonWithType:0];
    payBtn.frame = CGRectMake(15, backView.height-60, _window_width-30, 40);
    [payBtn setBackgroundColor:Pink_Cor];
    [payBtn setTitle:YZMsg(@"立即支付") forState:0];
    [payBtn setTitleColor:[UIColor whiteColor] forState:0];
    payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    payBtn.layer.cornerRadius = 20;
    payBtn.layer.masksToBounds = YES;
    [payBtn addTarget:self action:@selector(payBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:payBtn];
    
}
-(void)closeClick{
    if (self.hideEvent) {
        self.hideEvent(NO);
    }
}
-(void)payBtnClick{
    if ([typeStr isEqual:@"1"]) {
        [self doAlipayPay];
    }else if ([typeStr isEqual:@"2"]){
        [self WeiXinPay];
    }else if ([typeStr isEqual:@"3"]){
        [self doBalance];
    }else if ([typeStr isEqual:@"6"]){
        //paypal支付
        [self dopayPal];
    }
}
-(void)requestBalance{
        
        NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
        NSString *sign = [PublicObj sortString:signdic];
        NSDictionary *dic = @{
                              @"uid":[Config getOwnID],
                              @"token":[Config getOwnToken],
                              @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                              @"sign":sign
                              };
        
    [YBNetworking postWithUrl:@"Buyer.getBalance" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            infoDic = [info firstObject];
            NSMutableArray *originPaylist = [NSMutableArray arrayWithArray:[infoDic valueForKey:@"paylist"]];
            NSMutableArray *delArray = [NSMutableArray array];
            if ([PublicObj isUp]) {
                for (NSDictionary *subDic in originPaylist) {
                    if ([[subDic valueForKey:@"id"] isEqual:@"balance"]) {
                        [delArray addObject:subDic];
                    }
                }
                [originPaylist removeObjectsInArray:delArray];
            }

            self.payArr = [NSArray arrayWithArray:originPaylist];
            NSDictionary *payDic = self.payArr[0];
            if ([minstr([payDic valueForKey:@"id"]) isEqual:@"ali"]) {
                typeStr = @"1";
            }else if([minstr([payDic valueForKey:@"id"]) isEqual:@"wx"]){
                typeStr = @"2";

            }else if([minstr([payDic valueForKey:@"id"]) isEqual:@"balance"]){
                typeStr = @"3";
            }
            _aliapp_key_ios = [infoDic valueForKey:@"aliapp_key_ios"];
            _aliapp_partner = [infoDic valueForKey:@"aliapp_partner"];
            _aliapp_seller_id = [infoDic valueForKey:@"aliapp_seller_id"];
            //微信的信息
            _wx_appid = [infoDic valueForKey:@"wx_appid"];
            [WXApi registerApp:_wx_appid universalLink:WechatUniversalLink];

            [payTable reloadData];
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
     }];

}

#pragma mark-----tabelviewDelegate------
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _payArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 50;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"paycell"];
    UIImageView *iconImg = [[UIImageView alloc]init];
    [iconImg sd_setImageWithURL:[NSURL URLWithString:[self.payArr[indexPath.row]valueForKey:@"thumb"]]];
    [cell addSubview:iconImg];
    [iconImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cell).offset(5);
        make.centerY.equalTo(cell.mas_centerY);
        make.height.width.mas_equalTo(20);
    }];
    
    UILabel *nameLb = [[UILabel alloc]init];
    nameLb.font = [UIFont systemFontOfSize:14];
    nameLb.textColor = [UIColor blackColor];
    nameLb.text = minstr([self.payArr[indexPath.row]valueForKey:@"name"]);
    [cell addSubview:nameLb];
    [nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconImg.mas_right).offset(5);
        make.centerY.equalTo(iconImg.mas_centerY);
    }];
    UILabel *balanceLb = [[UILabel alloc]init];
    balanceLb.font = [UIFont systemFontOfSize:14];
    balanceLb.textColor = [UIColor grayColor];
    balanceLb.text =[NSString stringWithFormat:@"(¥%@)",minstr([infoDic valueForKey:@"balance"])];
    if ([nameLb.text containsString:YZMsg(@"余额")]) {
        balanceLb.hidden = NO;
    }else{
         balanceLb.hidden = YES;
    }
    [cell addSubview:balanceLb];
    [balanceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLb.mas_right).offset(5);
        make.centerY.equalTo(nameLb.mas_centerY);
    }];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, 49, _window_width-30, 1) andColor:RGB(240, 240, 240) andView:cell];
    
    UIImageView *duihaoImg = [[UIImageView alloc]init];
    duihaoImg.frame = CGRectMake(cell.width-30, 50/2-10, 18, 13);
    duihaoImg.image = [UIImage imageNamed:@"pay_选中"];
    duihaoImg.hidden = YES;
    [cell addSubview:duihaoImg];
    if (index == indexPath.row) {
        duihaoImg.hidden = NO;
    }else{
        duihaoImg.hidden = YES;

    }
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    index = indexPath.row;
    NSDictionary *payDic = self.payArr[indexPath.row];
    if ([minstr([payDic valueForKey:@"id"]) isEqual:@"ali"]) {
        typeStr = @"1";
    }else if([minstr([payDic valueForKey:@"id"]) isEqual:@"wx"]){
        typeStr = @"2";
    }else if([minstr([payDic valueForKey:@"id"]) isEqual:@"balance"]){
        typeStr = @"3";
    }else if([minstr([payDic valueForKey:@"id"]) isEqual:@"paypal"]){
        typeStr = minstr([payDic valueForKey:@"type"]);
    }
    [payTable reloadData];
}

//paypal***************
/*payPal支付*/
-(void)dopayPal{
    
        NSLog(@"paypal支付");
        [MBProgressHUD showMessage:@""];
    
        NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"orderid":_orderIdStr,@"type":typeStr,@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
        NSString *sign = [PublicObj sortString:signdic];
    
        NSDictionary *subdic = @{
                                 @"uid":[Config getOwnID],
                                 @"token":[Config getOwnToken],
                                 @"orderid":_orderIdStr,
                                 @"type":typeStr,
                                 @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                                 @"sign":sign
                                 };
    [YBNetworking postWithUrl:@"Buyer.goodsOrderPay" Dic:subdic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *dict = [info firstObject];
            NSDictionary *payDic = @{@"money":_priceStr,
                                 @"orderid":minstr([dict valueForKey:@"orderid"]),
            };
            
            [[YBRechargeType chargeManeger] selPayPalAndPatameter:payDic rechargeType:rechargeType_Goods complete:^(int stateCode, int payType, NSString *msg) {
                            dispatch_async(dispatch_get_main_queue(), ^{
                                if (stateCode == 0) {
                                    if (self.hideEvent) {
                                        self.hideEvent(YES);
                                    }

                                }
                            });
            }];
        }
        else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];

        }];
    
    

}

#pragma mark - PayPal

//- (PayPalConfiguration *)paypalConfiguration {
//    if (!_paypalConfiguration) {
//        _paypalConfiguration = [[PayPalConfiguration alloc] init];
//        _paypalConfiguration.merchantName = [PublicObj getAppName];
//        _paypalConfiguration.acceptCreditCards = NO;//不支持信用卡
//        _paypalConfiguration.payPalShippingAddressOption = PayPalShippingAddressOptionPayPal;
//        _paypalConfiguration.languageOrLocale = lagType;
//    }
//    return _paypalConfiguration;
//}

//-(void)dopayPal{
//    NSLog(@"paypal支付");
//    [MBProgressHUD showMessage:@""];
//
//    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"orderid":_orderIdStr,@"type":@"5",@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
//    NSString *sign = [PublicObj sortString:signdic];
//
//    NSDictionary *subdic = @{
//                             @"uid":[Config getOwnID],
//                             @"token":[Config getOwnToken],
//                             @"orderid":_orderIdStr,
//                             @"type":@"5",
//                             @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
//                             @"sign":sign
//                             };
//    [YBToolClass postNetworkWithUrl:@"Buyer.goodsOrderPay" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
//        [MBProgressHUD hideHUD];
//        if (code == 0) {
//            NSDictionary *dict = [info firstObject];
//            //调起paypal支付
//            [self selPaypalParameter:dict];
//        }
//        else{
//            [MBProgressHUD showError:msg];
//        }
//
//    } fail:^{
//        [MBProgressHUD hideHUD];
//
//    }];
//}
//-(void)selPaypalParameter:(NSDictionary *)parameter{
//    NSString *sandBox = minstr([parameter valueForKey:@"paypal_sandbox"]);//0-沙盒  1正式
//    NSString *client_id = minstr([parameter valueForKey:@"product_clientid"]);
//    NSString *paypal_sandbox_id = minstr([parameter valueForKey:@"sandbox_clientid"]);
//
//    if ([sandBox isEqual:@"1"]) {
//        if ([PublicObj checkNull:client_id] ) {
//            [MBProgressHUD showError:YZMsg(@"贝宝未配置")];
//
//                 return;
//             }
//        [PayPalMobile initializeWithClientIdsForEnvironments:@{PayPalEnvironmentProduction:client_id,PayPalEnvironmentSandbox:paypal_sandbox_id}];
//
//        [PayPalMobile preconnectWithEnvironment:PayPalEnvironmentProduction];
//    }else{
//        if ([PublicObj checkNull:paypal_sandbox_id] ) {
//            [MBProgressHUD showError:YZMsg(@"贝宝未配置")];
//
//                  return;
//              }
//        [PayPalMobile initializeWithClientIdsForEnvironments:@{PayPalEnvironmentProduction:client_id,PayPalEnvironmentSandbox:paypal_sandbox_id}];
//
//        [PayPalMobile preconnectWithEnvironment:PayPalEnvironmentSandbox];
//    }
//
//    NSString *orderStr = minstr([parameter valueForKey:@"orderid"]);
//    PayPalPayment *payment = [[PayPalPayment alloc] init];
//    payment.amount = [[NSDecimalNumber alloc] initWithString:_priceStr];
//    payment.currencyCode = @"USD";
//    payment.invoiceNumber = orderStr;
//    payment.shortDescription = minstr([parameter valueForKey:@"goods_name"]); //YZMsg(@"直播小店支付");//
//    payment.custom = @"order_pay";//
//    payment.items = nil;  // if not including multiple items, then leave payment.items as nil
//    payment.paymentDetails = nil; // if not including payment details, then leave payment.paymentDetails as nil
//    payment.intent = PayPalPaymentIntentSale;
//    if (!payment.processable) {
//        NSLog(@"-------------");
//    }
//    PayPalPaymentViewController *paymentViewController = [[PayPalPaymentViewController alloc] initWithPayment:payment configuration:self.paypalConfiguration delegate:self];
//    [[XGGAppDelegate sharedAppDelegate].topViewController presentViewController:paymentViewController animated:YES completion:nil];
//
//}
//#pragma mark - PayPalPaymentDelegate methods
//- (void)payPalPaymentViewController:(PayPalPaymentViewController *)paymentViewController didCompletePayment:(PayPalPayment *)completedPayment {
//    [self verifyCompletedPayment:completedPayment];
//    [[XGGAppDelegate sharedAppDelegate].topViewController dismissViewControllerAnimated:YES completion:nil];
//}
//
//- (void)payPalPaymentDidCancel:(PayPalPaymentViewController *)paymentViewController {
//    NSLog(@"支付有错误 稍后重试");
//    [[XGGAppDelegate sharedAppDelegate].topViewController dismissViewControllerAnimated:YES completion:nil];
//}
//
//- (void)verifyCompletedPayment:(PayPalPayment *)completedPayment {
//    // Send the entire confirmation dictionary
//    NSData *confirmation = [NSJSONSerialization dataWithJSONObject:completedPayment.confirmation options:0 error:nil];
//    NSLog(@"=================%@",completedPayment.confirmation);
//    NSLog(@"---------------------------------");
//    NSLog(@"==================%@",confirmation);
//    if (self.hideEvent) {
//        self.hideEvent(YES);
//    }
//
//
//
//}



//微信支付*****************************************************************************************************************
-(void)WeiXinPay{
    NSLog(@"微信支付");
    [MBProgressHUD showMessage:@""];
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"orderid":_orderIdStr,@"type":@"2",@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"orderid":_orderIdStr,
                             @"type":@"2",
                             @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                             @"sign":sign
                             };
    
    [YBNetworking postWithUrl:@"Buyer.goodsOrderPay" Dic:subdic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            [MBProgressHUD hideHUD];
            NSDictionary *dict = [info firstObject];
            //调起微信支付
            NSString *times = [dict objectForKey:@"timestamp"];
            PayReq* req             = [[PayReq alloc] init];
            req.partnerId           = [dict objectForKey:@"partnerid"];
            NSString *pid = [NSString stringWithFormat:@"%@",[dict objectForKey:@"prepayid"]];
            if ([pid isEqual:[NSNull null]] || pid == NULL || [pid isEqual:@"null"]) {
                pid = @"123";
            }
            req.prepayId            = pid;
            req.nonceStr            = [dict objectForKey:@"noncestr"];
            req.timeStamp           = times.intValue;
            req.package             = [dict objectForKey:@"package"];
            req.sign                = [dict objectForKey:@"sign"];
            [WXApi sendReq:req completion:^(BOOL success) {
                NSLog(@"wxapi调用 %d",success);
            }];
        }
        else{
            [MBProgressHUD hideHUD];
        }

        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];

     }];
}
-(void)onResp:(BaseResp *)resp{
    //支付返回结果，实际支付结果需要去微信服务器端查询
    NSString *strMsg = [NSString stringWithFormat:@"支付结果"];
    switch (resp.errCode) {
        case WXSuccess:
            strMsg = @"支付结果：成功！";
            NSLog(@"支付成功－PaySuccess，retcode = %d", resp.errCode);
//            [self requestData];
            [MBProgressHUD hideHUD];
            [MBProgressHUD showError:YZMsg(@"支付成功")];
//            [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
            if (self.hideEvent) {
                self.hideEvent(YES);
            }


            break;
        default:
            strMsg = [NSString stringWithFormat:@"支付结果：失败！retcode = %d, retstr = %@", resp.errCode,resp.errStr];
            NSLog(@"错误，retcode = %d, retstr = %@", resp.errCode,resp.errStr);
            [MBProgressHUD hideHUD];
            [self closeClick];
            break;
    }
}
//微信支付*****************************************************************************************************************

//支付宝支付*****************************************************************************************************************
- (void)doAlipayPay
{
    NSString *partner = _aliapp_partner;
    NSString *seller =  _aliapp_seller_id;
    NSString *privateKey = _aliapp_key_ios;
    
    
    
    //partner和seller获取失败,提示
    if ([partner length] == 0 ||
        [seller length] == 0 ||
        [privateKey length] == 0){
        [MBProgressHUD showError:YZMsg(@"缺少partner或者seller或者私钥")];
        return;
    }
    /*
     *生成订单信息及签名
     */
    //将商品信息赋予AlixPayOrder的成员变量
    Order *order = [[Order alloc] init];
    order.partner = partner;
    order.seller = seller;
    //获取订单id
    //将商品信息拼接成字符串
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"orderid":_orderIdStr,@"type":@"1",@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"orderid":_orderIdStr,
                             @"type":@"1",
                             @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                             @"sign":sign
                             };
    
    [YBNetworking postWithUrl:@"Buyer.goodsOrderPay" Dic:subdic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSString *infos = [[info firstObject] valueForKey:@"orderid"];
            order.tradeNO = infos;
            order.notifyURL = [h5url stringByAppendingString:@"/appapi/shoppay/notify_ali"];
            order.amount = _priceStr;
            order.productName = [NSString stringWithFormat:@"%@%@",_priceStr,_shopNameStr];
            order.productDescription = @"productDescription";
            //以下配置信息是默认信息,不需要更改.
            order.service = @"mobile.securitypay.pay";
            order.paymentType = @"1";
            order.inputCharset = @"utf-8";
            order.itBPay = @"30m";
            order.showUrl = @"m.alipay.com";
            //应用注册scheme,在AlixPayDemo-Info.plist定义URL types,用于快捷支付成功后重新唤起商户应用
            NSString *appScheme = [[NSBundle mainBundle] bundleIdentifier];
            //将商品信息拼接成字符串
            NSString *orderSpec = [order description];
            NSLog(@"orderSpec = %@",orderSpec);
            //获取私钥并将商户信息签名,外部商户可以根据情况存放私钥和签名,只需要遵循RSA签名规范,并将签名字符串base64编码和UrlEncode
            id<DataSigner> signer = CreateRSADataSigner(privateKey);
            NSString *signedString = [signer signString:orderSpec];
            //将签名成功字符串格式化为订单字符串,请严格按照该格式
            NSString *orderString = nil;
            if (signedString != nil) {
                orderString = [NSString stringWithFormat:@"%@&sign=\"%@\"&sign_type=\"%@\"",
                               orderSpec, signedString, @"RSA"];
                
                [[AlipaySDK defaultService] payOrder:orderString fromScheme:appScheme callback:^(NSDictionary *resultDic) {
                    NSLog(@"reslut = %@",resultDic);
                    NSInteger resultStatus = [resultDic[@"resultStatus"] integerValue];
                    NSLog(@"#######%ld",(long)resultStatus);
                    // NSString *publicKey = alipaypublicKey;
                    NSLog(@"支付状态信息---%ld---%@",resultStatus,[resultDic valueForKey:@"memo"]);
                    // 是否支付成功
                    if (9000 == resultStatus) {
                        /*
                         *用公钥验证签名
                         */
                        [MBProgressHUD showError:YZMsg(@"支付成功")];
                        if (self.hideEvent) {
                            self.hideEvent(YES);
                        }
                    }else{
                        [self closeClick];

                    }
                }];
            }
        }

        } Fail:^(id fail) {
            
     }];
}
/*余额支付*/
-(void)doBalance{
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"orderid":_orderIdStr,@"type":@"3",@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"orderid":_orderIdStr,
                             @"type":@"3",
                             @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                             @"sign":sign
                             };
        [YBNetworking postWithUrl:@"Buyer.goodsOrderPay" Dic:subdic Suc:^(int code, id info, NSString *msg) {
            if (code == 0) {
                [MBProgressHUD showError:msg];
                if (self.hideEvent) {
                    self.hideEvent(YES);
                }
            }else{
                 [MBProgressHUD showError:msg];
            }

            } Fail:^(id fail) {
                
         }];
}
@end
