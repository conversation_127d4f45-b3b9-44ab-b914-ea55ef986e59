//
//  PayOrderView.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/14.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
////Paypal
//#import <PayPalConfiguration.h>
//#import <PayPalPayment.h>
//#import <PayPalPaymentViewController.h>
//#import <PayPalMobile.h>PayPalPaymentDelegate

NS_ASSUME_NONNULL_BEGIN
typedef void(^PayOrderEvent)(BOOL paySuccess);
@interface PayOrderView : UIView<UITableViewDelegate, UITableViewDataSource>
{
    NSString *_priceStr;
    NSString *_orderIdStr;
    NSString *_shopNameStr;
    
    UITableView *payTable;
    NSDictionary *infoDic;
    
    NSInteger index;
    
    NSString *typeStr;
}
@property (nonatomic, strong)NSArray *payArr;
@property (nonatomic, copy)PayOrderEvent hideEvent;
//支付宝
@property(nonatomic,copy)NSString *aliapp_key_ios;
@property(nonatomic,copy)NSString *aliapp_partner;
@property(nonatomic,copy)NSString *aliapp_seller_id;
//微信
@property(nonatomic,copy)NSString *wx_appid;
////paypal
//@property (nonatomic, strong, readwrite) PayPalConfiguration *paypalConfiguration;

-(instancetype)initWithPrice:(NSString *)price AndOrderId:(NSString *)orderid AndShopName:(NSString *)nameStr;
@end

NS_ASSUME_NONNULL_END
