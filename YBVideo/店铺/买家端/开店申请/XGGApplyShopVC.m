//
//  ApplyShopVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/14.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ApplyShopVC.h"
#import "BondViewController.h"
#import "SelectClassVC.h"
#import "CommodityClassModel.h"
#import "TZImagePickerController.h"
#import <Qiniu/QiniuSDK.h>
#import "YBStorageObj.h"
@import CoreLocation;

@interface ApplyShopVC ()<UIPickerViewDelegate, UIPickerViewDataSource,TZImagePickerControllerDelegate,UITextFieldDelegate,UITextViewDelegate,CLLocationManagerDelegate>
{
    UIScrollView *_backScroll;
    
    UITextField *idField;
    UITextField *nameField;
    UITextField *cardField;
    
    
    UITextField *sellerNameField;//经营者名字
    UITextField *sellerPhoneField;//经营者手机号
    UITextField *sellerAddressField;//经营者所在地区
    MyTextView *sellerAddDesField;   //经营者详细地址

    UITextField *receiverField;//经营者名字
    UITextField *receiverPhoneField;//经营者手机号
    UITextField *receiverAddressField;//经营者所在地区
    MyTextView *receiverAddDesField;   //经营者详细地址

    NSString *selType;
    NSString *recever_provinceStr;
    NSString *recever_cityStr;
    NSString *recever_districtStr;

    UIButton *classBtn;
    
    UITapGestureRecognizer *customerTap;
    UITapGestureRecognizer *rejectedTap;
    UITapGestureRecognizer *bondTap;

    BOOL phoneBool;
    BOOL rejectedBool;
    
    CGFloat customerHeight;
    CGFloat rejectedHeight;
    
    UIView *cityPickBack;
    UIPickerView *cityPicker;
    //省市区-数组
    NSArray *province;
    NSArray *city;
    NSArray *district;
    
    //省市区-字符串
    NSString *provinceStr;
    NSString *cityStr;
    NSString *districtStr;
    NSDictionary *areaDic;
    NSString *selectedProvince;

    UIButton *busImgBtn;
    UIButton *otherImgBtn;
    
    UIImage *_dsp_busImg;
    UIImage *_dsp_othetImg;
    NSString *businessImgUrl;
    NSString *otherImgUrl;
    NSString *imgType;

    NSString *normalProvince;
    NSString *normalCity;
    NSString *normalDistrict;
    CLLocationManager   *_lbsManager;
    NSString *classId;
}
@property(nonatomic, strong)UIView *infoView;//信息确认
@property(nonatomic, strong)UIView *classView;//经营类目
@property(nonatomic, strong)UIView *contactView;//联系方式
@property(nonatomic, strong)UIView *customerView;//客服信息
@property(nonatomic, strong)UIView *rejectedView;//退货信息
@property(nonatomic, strong)UIView *zizhiView;//资质
@property(nonatomic, strong)UIView *bondView;//保证金
@property(nonatomic, strong)UIView *rejectDetail;
@property(nonatomic, strong)UITextField *phoneField;

@property(nonatomic, strong)NSMutableArray *classArr;

@end

@implementation ApplyShopVC

-(void)initData{
    rejectedHeight = 56 ;
    customerHeight = 56;
    self.classArr =[NSMutableArray array];
    selType = @"0";
    imgType = @"0";
    businessImgUrl = @"";
    otherImgUrl = @"";
    normalProvince = @"";
    normalCity = @"";
    normalDistrict = @"";

    NSBundle *bundle = [NSBundle mainBundle];
    NSString *plistPath = [bundle pathForResource:@"area" ofType:@"plist"];
    areaDic = [[NSDictionary alloc] initWithContentsOfFile:plistPath];

    NSArray *components = [areaDic allKeys];
    NSArray *sortedArray = [components sortedArrayUsingComparator: ^(id obj1, id obj2) {
        
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;
        }
        
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;
        }
        return (NSComparisonResult)NSOrderedSame;
    }];

    NSMutableArray *provinceTmp = [[NSMutableArray alloc] init];
    for (int i=0; i<[sortedArray count]; i++) {
        NSString *index = [sortedArray objectAtIndex:i];
        NSArray *tmp = [[areaDic objectForKey: index] allKeys];
        [provinceTmp addObject: [tmp objectAtIndex:0]];
    }
    //---> //rk_3-7 修复首次加载问题
    province = [[NSArray alloc] initWithArray: provinceTmp];
    NSString *index = [sortedArray objectAtIndex:0];
    //NSString *selected = [province objectAtIndex:0];
    selectedProvince = [province objectAtIndex:0];
    NSDictionary *proviceDic = [NSDictionary dictionaryWithDictionary: [[areaDic objectForKey:index]objectForKey:selectedProvince]];

    NSArray *cityArray = [proviceDic allKeys];
    NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: [cityArray objectAtIndex:0]]];
    //city = [[NSArray alloc] initWithArray: [cityDic allKeys]];

    NSArray *citySortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;//递减
        }
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;//上升
        }
        return (NSComparisonResult)NSOrderedSame;
    }];
    NSMutableArray *m_array = [[NSMutableArray alloc] init];
    for (int i=0; i<[citySortedArray count]; i++) {
        NSString *index = [citySortedArray objectAtIndex:i];
        NSArray *temp = [[proviceDic objectForKey: index] allKeys];
        [m_array addObject: [temp objectAtIndex:0]];
    }
    city = [NSArray arrayWithArray:m_array];
    //<-----------

    NSString *selectedCity = [city objectAtIndex: 0];
    district = [[NSArray alloc] initWithArray: [cityDic objectForKey: selectedCity]];
    [self location];

}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"开通小店");
    [self initData];
    _backScroll = [[UIScrollView alloc]init];
    _backScroll.frame = CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60);
    _backScroll.backgroundColor = RGB(17, 13, 35);
    [self.view addSubview:_backScroll];
    
    UITapGestureRecognizer *hideTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideTapClick)];
    [_backScroll addGestureRecognizer:hideTap];
    
    [_backScroll addSubview:self.infoView];
    [_backScroll addSubview:self.classView];
    [_backScroll addSubview:self.contactView];
    [_backScroll addSubview:self.customerView];
    [_backScroll addSubview:self.rejectedView];
    [_backScroll addSubview:self.zizhiView];
    [_backScroll addSubview:self.bondView];
    [self addBottomView];
    
    _backScroll.contentSize = CGSizeMake(_window_width, self.infoView.height+self.classView.height+self.contactView.height+self.customerView.height+self.rejectedView.height+self.zizhiView.height+self.bondView.height+60);
    
    //如果是拒绝进来的 就赋值
    if ([self.fromWhere isEqual:@"reject"]) {
        [self setApplyData:self.applyDic];
    }
}
-(void)hideTapClick{
    [self.view endEditing:YES];
}
#pragma mark ------信息界面--------
-(UIView *)infoView{
    if (!_infoView) {
        _infoView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 290+15)];
        _infoView.backgroundColor = RGB(17, 13, 35);
        
        UILabel *taps = [[UILabel alloc]init];
        taps.frame = CGRectMake(12, 10, 150, 30);
        taps.font = [UIFont boldSystemFontOfSize:15];
        taps.text = YZMsg(@"信息确认");
        taps.textColor = RGB(150, 150, 150);
        [_infoView addSubview:taps];
        
        UIView *backView = [[UIView alloc]init];
        backView.frame = CGRectMake(20, taps.bottom+10, _window_width-40, 100);
        backView.backgroundColor = RGB(24, 21, 43);
        [_infoView addSubview:backView];
        NSArray *arr = @[YZMsg(@"账号ID:"),YZMsg(@"姓名:"),YZMsg(@"身份证号:")];
        for (int i = 0; i < arr.count; i++) {
            UILabel *titleLb = [[UILabel alloc]init];
            titleLb.frame = CGRectMake(10, i * 33, 70, 33);
            titleLb.textColor = RGB(150, 150, 150);
            titleLb.font = [UIFont systemFontOfSize:13];
            titleLb.text = arr[i];
            [backView addSubview:titleLb];
            
            UITextField *tf = [[UITextField alloc]init];
            tf.frame = CGRectMake(titleLb.right+10, i* 33, backView.width-titleLb.width-30, 33);
            tf.textColor = [UIColor whiteColor];
            tf.font = [UIFont systemFontOfSize:12];
            tf.textAlignment = NSTextAlignmentRight;
            
            [backView addSubview:tf];
            
            if (i == 0) {
                idField = tf;
                idField.userInteractionEnabled = NO;
                idField.text = [Config getOwnID];
            }else if (i == 1) {
                nameField = tf;
                nameField.text = minstr([self.presonDic valueForKey:@"real_name"]);
                nameField.userInteractionEnabled = NO;
            }else{
                cardField = tf;
                cardField.text = minstr([self.presonDic valueForKey:@"cer_no"]);
                cardField.userInteractionEnabled = NO;
            }
        }
        
        UILabel *explainLb = [[UILabel alloc]init];
        explainLb.frame = CGRectMake(12, backView.bottom+10, _window_width-24, _infoView.height-backView.height-60);
        explainLb.numberOfLines = 0;
        explainLb.lineBreakMode = NSLineBreakByWordWrapping;
        explainLb.font = [UIFont systemFontOfSize:11];
        explainLb.textColor = RGB(150, 150, 150);
        explainLb.text = [NSString stringWithFormat:@"%@ \n\n %@ \n\n %@",YZMsg(@"1.请确认上述账号ID、姓名、身份证号为本人信息。若非本人信息请停止操作。"),YZMsg(@"2.您同意授权本平台公司获取并保存账号ID、姓名、身份证号个人信息用于身份认证备案。"),YZMsg(@"3.您承诺通过本平台小店开展的经营活动为根据法律法规规定不需要办理市场主体登记的经营行为。")];
        [_infoView addSubview:explainLb];
    }
    return _infoView;
}
#pragma mark ------经营类目界面--------
-(UIView *)classView{
    if (!_classView) {
        _classView = [[UIView alloc]initWithFrame:CGRectMake(0, self.infoView.bottom+5, _window_width, 56)];
        _classView.backgroundColor =RGB(24, 21, 43);
        
        UILabel *taps = [[UILabel alloc]init];
        taps.frame = CGRectMake(12, _classView.height/2-15, 150, 30);
        taps.font = [UIFont boldSystemFontOfSize:15];
        taps.text = YZMsg(@"经营类目");
        taps.textColor = RGB(150, 150, 150);
        [_classView addSubview:taps];
        
        classBtn = [UIButton buttonWithType:0];
        classBtn.frame = CGRectMake(_classView.width-110, _classView.height/2-15, 110, 30);
        [classBtn setTitleColor:RGB(150, 150, 150) forState:0];
        [classBtn setTitle:YZMsg(@"请选择") forState:0];
        [classBtn setImage:[UIImage imageNamed:@"shop_right"] forState:0];
        classBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [classBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, - classBtn.imageView.image.size.width, 0, classBtn.imageView.image.size.width)];
        [classBtn setImageEdgeInsets:UIEdgeInsetsMake(0, classBtn.titleLabel.bounds.size.width, 0, -classBtn.titleLabel.bounds.size.width)];
        classBtn.tag = 30000;
        [classBtn addTarget:self action:@selector(selClass:) forControlEvents:UIControlEventTouchUpInside];
        [_classView addSubview:classBtn];
    }
    return _classView;
}
#pragma mark ------经营者联系方式界面--------

-(UIView *)contactView{
    if (!_contactView) {
        _contactView =  [[UIView alloc]initWithFrame:CGRectMake(0, self.classView.bottom+5, _window_width, 260)];
        _contactView.backgroundColor = RGB(24, 21, 43);
        
        UILabel *taps = [[UILabel alloc]init];
        taps.frame = CGRectMake(12, 10, 250, 30);
        taps.font = [UIFont boldSystemFontOfSize:15];
        taps.text = YZMsg(@"经营者联系方式");
        taps.textColor = RGB(150, 150, 150);
        [_contactView addSubview:taps];

        NSArray *arr = @[YZMsg(@"联系人"),YZMsg(@"手机号"),YZMsg(@"所在地区"),YZMsg(@"详细地址")];
        for (int i = 0; i < arr.count; i ++) {
            if (i < 3) {
                UITextField *tf = [[UITextField alloc]init];
                tf.frame = CGRectMake(12, taps.bottom + i* 50, _contactView.width-24, 50);
                tf.textColor = [UIColor whiteColor];
                tf.font = [UIFont systemFontOfSize:14];
                tf.placeholder = arr[i];
                tf.delegate = self;
                tf.returnKeyType = UIReturnKeyDone;
                NSMutableAttributedString*holderString = [[NSMutableAttributedString alloc] initWithString:arr[i] attributes:@{NSForegroundColorAttributeName:RGB(150, 150, 150)}];
                tf.attributedPlaceholder = holderString;

//                [tf setValue:RGB(150, 150, 150) forKeyPath:@"_placeholderLabel.textColor"];
                [_contactView addSubview:tf];
            
                UILabel *line = [[UILabel alloc]init];
                line.backgroundColor =  RGB(49, 47, 66);
                line.frame = CGRectMake(12, tf.bottom+1, _contactView.width-24, 1);
                [_contactView addSubview:line];
                if (i == 0) {
                    sellerNameField = tf;
                }else if (i == 1){
                    sellerPhoneField = tf;
                }else if (i == 2){
                    sellerAddressField = tf;
                    sellerAddressField.userInteractionEnabled = NO;
                    
                    UIButton *selAddressBtn = [UIButton buttonWithType:0];
                    selAddressBtn.frame = tf.frame;
                    selAddressBtn.tag = 9999;
                    [selAddressBtn addTarget:self action:@selector(addressClick:) forControlEvents:UIControlEventTouchUpInside];
                    [_contactView addSubview:selAddressBtn];
                }
            }else{
                MyTextView *tv = [[MyTextView alloc]init];
                tv.backgroundColor = RGB(24, 21, 43);
                tv.frame =CGRectMake(5, taps.bottom+ 5+ i* 50, _contactView.width-24, 60);
                tv.textColor = [UIColor whiteColor];
                tv.font = [UIFont systemFontOfSize:14];
                tv.placeholder = arr[i];
                tv.placeholderColor = RGB(150, 150, 150);
                tv.delegate = self;
                tv.returnKeyType = UIReturnKeyDone;
                [_contactView addSubview:tv];
                sellerAddDesField = tv;
            }
        }
    }
    return _contactView;
}

#pragma mark ------客服信息界面--------

-(UIView *)customerView{
    if (!_customerView) {
        _customerView = [[UIView alloc]initWithFrame:CGRectMake(0, self.contactView.bottom+5, _window_width, 56)];
        _customerView.backgroundColor = RGB(24, 21, 43);
        
        UILabel *taps = [[UILabel alloc]init];
        taps.frame = CGRectMake(12, _classView.height/2-15, 65, 30);
        if ([lagType isEqual:EN]) {
            taps.frame = CGRectMake(12, _classView.height/2-15, 85, 30);
        }
        taps.font = [UIFont boldSystemFontOfSize:15];
        taps.text = YZMsg(@"客服信息");
        taps.textColor = RGB(150, 150, 150);
        [_customerView addSubview:taps];
        
        UILabel *taps2 = [[UILabel alloc]init];
        taps2.frame = CGRectMake(taps.right, _classView.height/2-15, 200, 30);
        taps2.font = [UIFont systemFontOfSize:12];
        taps2.text = YZMsg(@"(不填默认设置为联系人手机号)");
        taps2.textColor = RGB(150, 150, 150);
        [_customerView addSubview:taps2];
        
        UIImageView *rightImg = [[UIImageView alloc]init];
        rightImg.frame = CGRectMake(_customerView.width-40, _customerView.height/2-8, 16, 16);
        rightImg.image = [UIImage imageNamed:@"jiantou_down"];
        [_customerView addSubview:rightImg];

        customerTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(btnClick:)];
        [_customerView addGestureRecognizer:customerTap];
        if ([self.fromWhere isEqual:@"reject"]) {
            [self btnClick:customerTap];
        }
    }
    return _customerView;
}
#pragma mark ------退货界面--------

-(UIView *)rejectedView{
    if (!_rejectedView) {
        _rejectedView = [[UIView alloc]initWithFrame:CGRectMake(0, self.customerView.bottom+5, _window_width, 56)];
        _rejectedView.backgroundColor = RGB(24, 21, 43);
        
        UILabel *taps = [[UILabel alloc]init];
        taps.frame = CGRectMake(12, _classView.height/2-15, 65, 30);
        if ([lagType isEqual:EN]) {
            taps.frame = CGRectMake(12, _classView.height/2-15, 85, 30);
        }
        taps.font = [UIFont boldSystemFontOfSize:15];
        taps.text = YZMsg(@"退货信息");
        taps.textColor = RGB(150, 150, 150);
        [_rejectedView addSubview:taps];
        
        UILabel *taps2 = [[UILabel alloc]init];
        taps2.frame = CGRectMake(taps.right, _classView.height/2-15, 250, 30);
        taps2.font = [UIFont systemFontOfSize:12];
        taps2.text = YZMsg(@"(不填默认设置为经营地址、电话信息)");
        taps2.textColor = RGB(150, 150, 150);
        [_rejectedView addSubview:taps2];

        UIImageView *rightImg = [[UIImageView alloc]init];
        rightImg.frame = CGRectMake(_rejectedView.width-40, _rejectedView.height/2-8, 16, 16);
        rightImg.image = [UIImage imageNamed:@"jiantou_down"];
        [_rejectedView addSubview:rightImg];

        rejectedTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(btnClick:)];
        [_rejectedView addGestureRecognizer:rejectedTap];
        if ([self.fromWhere isEqual:@"reject"]) {
            [self btnClick:rejectedTap];
        }

    }
    return _rejectedView;

}
#pragma mark ------资质证明界面--------
-(UIView *)zizhiView{
    if (!_zizhiView) {
        _zizhiView =[[UIView alloc]initWithFrame:CGRectMake(0, self.rejectedView.bottom+5, _window_width, 180)];
        _zizhiView.backgroundColor = RGB(24, 21, 43);
        
        UILabel *taps = [[UILabel alloc]init];
        taps.frame = CGRectMake(12, 10, 165, 30);
        taps.font = [UIFont boldSystemFontOfSize:15];
        taps.text = YZMsg(@"资质证明");
        taps.textColor = RGB(150, 150, 150);
        [_zizhiView addSubview:taps];
        
        NSArray *arr = @[YZMsg(@"营业执照"),YZMsg(@"其他证件")];
        for (int i = 0; i < arr.count; i ++) {
            UIButton *btn = [UIButton buttonWithType:0];
            btn.frame = CGRectMake(15+ i * 115, taps.bottom+10, 100, 75);
            [btn setBackgroundColor:RGB(31, 29, 50)];
            [btn setImage:[UIImage imageNamed:@"addComm"] forState:0];
            btn.imageView.contentMode = UIViewContentModeScaleAspectFill;
            btn.imageView.clipsToBounds = YES;
            btn.titleLabel.font = [UIFont systemFontOfSize:14];
            btn.tag = 50000+i;
            [btn addTarget:self action:@selector(selectImageClick:) forControlEvents:UIControlEventTouchUpInside];
            [_zizhiView addSubview:btn];
            
            UILabel *lb = [[UILabel alloc]init];
            lb.frame = CGRectMake(btn.left, btn.bottom+10, btn.width, 20);
            lb.font = [UIFont systemFontOfSize:14];
            lb.textColor = RGB(150, 150, 150);
            lb.text = arr[i];
            lb.textAlignment = NSTextAlignmentCenter;
            [_zizhiView addSubview:lb];
            
            if (i == 0) {
                busImgBtn = btn;
            }else{
                otherImgBtn = btn;
            }
        }
    }
    return _zizhiView;
}
#pragma mark ------缴纳保证金--------
-(UIView *)bondView{
    if (!_bondView) {
        _bondView = [[UIView alloc]initWithFrame:CGRectMake(0, self.zizhiView.bottom+5, _window_width, 56)];
        _bondView.backgroundColor = RGB(24, 21, 43);
        
        UILabel *taps = [[UILabel alloc]init];
        taps.frame = CGRectMake(12, _bondView.height/2-15, 100, 30);
        taps.font = [UIFont boldSystemFontOfSize:15];
        taps.text = YZMsg(@"缴纳保证金");
        taps.textColor = RGB(150, 150, 150);
        [_bondView addSubview:taps];
        
        
        UIImageView *rightImg = [[UIImageView alloc]init];
        rightImg.frame = CGRectMake(_bondView.width-30, _bondView.height/2-8, 16, 16);
        rightImg.image = [UIImage imageNamed:@"shop_right"];
        [_bondView addSubview:rightImg];

        bondTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(btnClick:)];
        [_bondView addGestureRecognizer:bondTap];

    }
    return _bondView;
}
-(void)addBottomView{
    UIView *bottom = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-60, _window_width, 60)];
    bottom.backgroundColor = RGB(17, 13, 35);
    [self.view addSubview:bottom];
    
    UIButton *subBtn = [UIButton buttonWithType:0];
    subBtn.frame = CGRectMake(15, 10, _window_width-30, 40);
    [subBtn setBackgroundColor:Pink_Cor];
    [subBtn setTitle:YZMsg(@"提交") forState:0];
    [subBtn setTitleColor:[UIColor whiteColor] forState:0];
    subBtn.layer.cornerRadius = 5;
    subBtn.layer.masksToBounds = YES;
    [subBtn addTarget:self action:@selector(uploadMessage) forControlEvents:UIControlEventTouchUpInside];
    [bottom addSubview:subBtn];
}
#pragma mark------提交------------
-(void)uploadMessage{
    classId = @"";

    if (self.classArr.count < 1) {
        [MBProgressHUD showError:YZMsg(@"请选择经营类目")];
        return;
    }else{
        for (CommodityClassModel *model in self.classArr) {
            classId = [classId stringByAppendingFormat:@"%@,",model.idStr];
        }
        NSRange range = NSMakeRange(classId.length-1, 1);
        classId = [classId stringByReplacingCharactersInRange:range withString:@""];
    }
    if ((!_dsp_busImg && [PublicObj checkNull:businessImgUrl]) ||
        (!_dsp_othetImg && [PublicObj checkNull:otherImgUrl])) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:YZMsg(@"请上传资质证明图")];
        return;
    }
    if (_dsp_busImg || _dsp_othetImg) {
        [self  uploadImg];
    }else{
        [self subBtnClick];
    }
    

}

-(void)subBtnClick{
    
        NSMutableDictionary *dic = @{
                              @"uid":[Config getOwnID],
                              @"token":[Config getOwnToken],
                              @"username":nameField.text,
                              @"cardno":cardField.text,
                              @"classid":classId,
                              @"contact":sellerNameField.text,
                              @"country_code":@"",
                              @"phone":sellerPhoneField.text,
                              @"province":provinceStr,
                              @"city":cityStr,
                              @"area":districtStr,
                              @"address":sellerAddDesField.text,
                              @"service_phone":_phoneField.text,
                              @"receiver":receiverField.text,
                              @"receiver_phone":receiverPhoneField.text,
                              @"receiver_province":recever_provinceStr,
                              @"receiver_city":recever_cityStr,
                              @"receiver_area":recever_districtStr,
                              @"receiver_address":receiverAddDesField.text,
                              }.mutableCopy;
        [dic setObject:businessImgUrl forKey:@"certificate"];
        [dic setObject:otherImgUrl forKey:@"other"];

    [YBNetworking postWithUrl:@"Shop.shopApply" Dic:dic Suc:^(int code, id info, NSString *msg) {
        [MBProgressHUD hideHUD];
        if (code ==0) {
            [MBProgressHUD showError:msg];
            if (self.subEvent) {
                self.subEvent();
            }
            if ([self.fromWhere isEqual:@"reject"]) {
                [self.navigationController popToViewController:[self.navigationController.viewControllers objectAtIndex:([self.navigationController.viewControllers count] -3)] animated:YES];

            }else{
                [[XGGAppDelegate sharedAppDelegate]popViewController:YES];

            }
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}
#pragma mark------选择类目-----------
-(void)selClass:(UIButton *)sender{
    YBWeakSelf;
    SelectClassVC *class = [[SelectClassVC alloc]init];
    class.havaSelArr = [self.classArr mutableCopy];
    class.classEvent = ^(NSMutableArray * _Nonnull arr) {
        weakSelf.classArr = [arr mutableCopy];
        [classBtn setTitle:YZMsg(@"已选择") forState:0];
    };
    [[XGGAppDelegate sharedAppDelegate] pushViewController:class animated:YES];
}

-(void)btnClick:(UITapGestureRecognizer *)taps{
    //客服信息
    if (taps == customerTap) {
        phoneBool = !phoneBool;
        if (phoneBool) {
            _customerView.size = CGSizeMake(_window_width, 56 +50);
            [_customerView addSubview:self.phoneField];
            self.phoneField.hidden = NO;
            customerHeight = 56+50;
        }else{
            _customerView.size = CGSizeMake(_window_width, 56);
            self.phoneField.hidden = YES;
            customerHeight = 56;
        }
        [self reloadUIHeight];

    }else if (taps == rejectedTap){
    //退货信息
        rejectedBool = !rejectedBool;
        if (rejectedBool) {
            [_rejectedView addSubview:self.rejectDetail];
            _rejectedView.size =CGSizeMake(_window_width, 56 +self.rejectDetail.height);
            self.rejectDetail.hidden = NO;
            rejectedHeight = 56 +self.rejectDetail.height;

        }else{
            _rejectedView.size = CGSizeMake(_window_width, 56);
            rejectedHeight = 56 ;
            self.rejectDetail.hidden = YES;

        }
        [self reloadUIHeight];

    }else if (taps == bondTap){
        [self requstBondStatus];
    }

}
-(void)requstBondStatus{
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          };
    [YBNetworking postWithUrl:@"Shop.getBond" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            if ([minstr([infos valueForKey:@"bond_status"]) isEqual:@"1"]) {
                [MBProgressHUD showError:YZMsg(@"已缴纳保证金")];
            }else{
                BondViewController *bond = [[BondViewController alloc]init];
                bond.infoDic = infos;
                [[XGGAppDelegate sharedAppDelegate]pushViewController:bond animated:YES];


            }
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}

-(UITextField *)phoneField{
    if (!_phoneField) {
        _phoneField = [[UITextField alloc]init];
        _phoneField.frame = CGRectMake(12, 50, _contactView.width-24, 50);
        _phoneField.textColor = [UIColor whiteColor];
        _phoneField.font = [UIFont systemFontOfSize:14];
//        _phoneField.placeholder =YZMsg(@"客服电话");
        NSMutableAttributedString*holderString = [[NSMutableAttributedString alloc] initWithString:YZMsg(@"客服电话") attributes:@{NSForegroundColorAttributeName:RGB(150, 150, 150)}];
        _phoneField.attributedPlaceholder = holderString;

        _phoneField.delegate =self;
        _phoneField.returnKeyType = UIReturnKeyDone;
        [_customerView addSubview:_phoneField];
    }
    return _phoneField;
}
#pragma mark ------退货信息界面--------
-(UIView*)rejectDetail{
    if (!_rejectDetail) {
        _rejectDetail = [[UIView alloc]init];
        _rejectDetail.frame = CGRectMake(0, 56, _window_width, 200);
        
        NSArray *arr = @[YZMsg(@"收货人"),YZMsg(@"收货人手机号"),YZMsg(@"所在地区"),YZMsg(@"详细地址")];
        for (int i = 0; i < arr.count; i ++) {
            if (i < 3) {
                UITextField *tf = [[UITextField alloc]init];
                tf.frame = CGRectMake(12, i* 50, _contactView.width-24, 50);
                tf.textColor = [UIColor whiteColor];
                tf.font = [UIFont systemFontOfSize:14];
                tf.placeholder = arr[i];
                tf.delegate = self;
                tf.returnKeyType = UIReturnKeyDone;
                NSMutableAttributedString*holderString = [[NSMutableAttributedString alloc] initWithString:arr[i] attributes:@{NSForegroundColorAttributeName:RGB(150, 150, 150)}];
                tf.attributedPlaceholder = holderString;

                [_rejectDetail addSubview:tf];
            
                UILabel *line = [[UILabel alloc]init];
                line.backgroundColor = RGB(49, 47, 66);
                line.frame = CGRectMake(12, tf.bottom+1, _contactView.width-24, 1);
                [_rejectDetail addSubview:line];
                
                if (i == 0) {
                    receiverField = tf;
                }else if (i == 1){
                    receiverPhoneField = tf;
                }else if (i == 2){
                    receiverAddressField = tf;
                    receiverAddressField.userInteractionEnabled = NO;
                    
                    UIButton *selAddressBtn = [UIButton buttonWithType:0];
                    selAddressBtn.frame = tf.frame;
                    selAddressBtn.tag = 8888;
                    [selAddressBtn addTarget:self action:@selector(addressClick:) forControlEvents:UIControlEventTouchUpInside];
                    [_rejectDetail addSubview:selAddressBtn];
                }

            }else{
                MyTextView *tv = [[MyTextView alloc]init];
                tv.frame =CGRectMake(5, 5+ i* 50, _contactView.width-24, 60);
                tv.textColor = [UIColor whiteColor];
                tv.font = [UIFont systemFontOfSize:14];
                tv.placeholder = arr[i];
                tv.delegate = self;
                tv.returnKeyType = UIReturnKeyDone;
                tv.placeholderColor = RGB(150, 150, 150);
                tv.backgroundColor =  RGB(24, 21, 43);
                receiverAddDesField = tv;
                [_rejectDetail addSubview:tv];
            }
        }

    }
    return _rejectDetail;
}

-(void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}
#pragma mark -------更新UI界面-------
-(void)reloadUIHeight{
    self.customerView.frame =  CGRectMake(0, self.contactView.bottom+5, _window_width, customerHeight);
    self.rejectedView.frame = CGRectMake(0, self.customerView.bottom+5, _window_width, rejectedHeight);
    self.zizhiView.frame = CGRectMake(0, self.rejectedView.bottom+5, _window_width, 180);
    self.bondView.frame =CGRectMake(0, self.zizhiView.bottom+5, _window_width, 56);
    
    _backScroll.contentSize = CGSizeMake(_window_width, self.infoView.height+self.classView.height+self.contactView.height+self.customerView.height+self.rejectedView.height+self.zizhiView.height+self.bondView.height+60);

}
#pragma mark ------选择地区------------
-(void)addressClick:(UIButton *)sender{
    if (sender.tag == 9999) {
        selType = @"0";
    }else if(sender.tag == 8888){
        selType = @"1";
    }
    [self.view endEditing:YES];
    if (!cityPickBack) {
        cityPickBack = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        cityPickBack.backgroundColor = RGB_COLOR(@"#000000", 0.3);
        [self.view addSubview:cityPickBack];
        
        UIView *titleView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-240, _window_width, 40)];
        titleView.backgroundColor = RGB_COLOR(@"#ececec", 1);
        [cityPickBack addSubview:titleView];
        
        UIButton *cancleBtn = [UIButton buttonWithType:0];
        cancleBtn.frame = CGRectMake(20, 0, 80, 40);
        cancleBtn.tag = 100;
        [cancleBtn setTitle:YZMsg(@"取消") forState:0];
        [cancleBtn setTitleColor:[UIColor grayColor] forState:0];
        [cancleBtn addTarget:self action:@selector(cityCancleOrSure:) forControlEvents:UIControlEventTouchUpInside];
        [titleView addSubview:cancleBtn];
        UIButton *sureBtn = [UIButton buttonWithType:0];
        sureBtn.frame = CGRectMake(_window_width-100, 0, 80, 40);
        sureBtn.tag = 101;
        [sureBtn setTitle:YZMsg(@"确定") forState:0];
        [sureBtn setTitleColor:Normal_Color forState:0];
        [sureBtn addTarget:self action:@selector(cityCancleOrSure:) forControlEvents:UIControlEventTouchUpInside];
        [titleView addSubview:sureBtn];
        
        cityPicker = [[UIPickerView alloc]initWithFrame:CGRectMake(0, _window_height-200, _window_width, 200)];
        cityPicker.backgroundColor = [UIColor whiteColor];
        cityPicker.delegate = self;
        cityPicker.dataSource = self;
        cityPicker.showsSelectionIndicator = YES;
        [cityPicker selectRow: 0 inComponent: 0 animated: YES];
        [cityPickBack addSubview:cityPicker];
        
        [self setLocationAddress];

    }else{
        cityPickBack.hidden = NO;
    }

}
- (void)cityCancleOrSure:(UIButton *)button{
    if (button.tag == 100) {
        //return;
    }else{
        NSInteger provinceIndex = [cityPicker selectedRowInComponent: 0];
        NSInteger cityIndex = [cityPicker selectedRowInComponent: 1];
        NSInteger districtIndex = [cityPicker selectedRowInComponent: 2];
        
        if([selType isEqual:@"0"]){
            provinceStr = [province objectAtIndex: provinceIndex];
            cityStr = [city objectAtIndex: cityIndex];
            districtStr = [district objectAtIndex:districtIndex];
            NSString *dizhi = [NSString stringWithFormat:@"%@ %@ %@",provinceStr,cityStr,districtStr];
            sellerAddressField.text = dizhi;

        }else if ([selType isEqual:@"1"]){
            recever_provinceStr = [province objectAtIndex: provinceIndex];
            recever_cityStr = [city objectAtIndex: cityIndex];
            recever_districtStr = [district objectAtIndex:districtIndex];
            NSString *dizhi = [NSString stringWithFormat:@"%@ %@ %@",recever_provinceStr,recever_cityStr,recever_districtStr];
            receiverAddressField.text = dizhi;
        }
    }
    cityPickBack.hidden = YES;
}
#pragma mark- Picker Data Source Methods
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    if (pickerView == cityPicker) {
        return 3;
    }
    return 0;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    if (pickerView == cityPicker) {
        if (component == 0) {
            return [province count];
        }
        else if (component == 1) {
            return [city count];
        }
        else {
            return [district count];
        }
    }else{
        return 100;
    }
}


#pragma mark- Picker Delegate Methods

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    if (pickerView == cityPicker) {
        if (component == 0) {
            return [province objectAtIndex: row];
        }
        else if (component == 1) {
            return [city objectAtIndex: row];
        }
        else {
            return [district objectAtIndex: row];
        }
    }else{
        return nil;
    }
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component{
    if (pickerView == cityPicker) {
        if (component == 0) {
            selectedProvince = [province objectAtIndex: row];
            NSDictionary *tmp = [NSDictionary dictionaryWithDictionary: [areaDic objectForKey: [NSString stringWithFormat:@"%ld", row]]];
            NSDictionary *dic = [NSDictionary dictionaryWithDictionary: [tmp objectForKey: selectedProvince]];
            NSArray *cityArray = [dic allKeys];
            NSArray *sortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                
                if ([obj1 integerValue] > [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedDescending;//递减
                }
                if ([obj1 integerValue] < [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedAscending;//上升
                }
                return (NSComparisonResult)NSOrderedSame;
            }];
            
            NSMutableArray *array = [[NSMutableArray alloc] init];
            for (int i=0; i<[sortedArray count]; i++) {
                NSString *index = [sortedArray objectAtIndex:i];
                NSArray *temp = [[dic objectForKey: index] allKeys];
                [array addObject: [temp objectAtIndex:0]];
            }
            
            city = [[NSArray alloc] initWithArray: array];
            
            NSDictionary *cityDic = [dic objectForKey: [sortedArray objectAtIndex: 0]];
            district = [[NSArray alloc] initWithArray: [cityDic objectForKey: [city objectAtIndex: 0]]];
            [cityPicker selectRow: 0 inComponent: 1 animated: YES];
            [cityPicker selectRow: 0 inComponent: 2 animated: YES];
            [cityPicker reloadComponent: 1];
            [cityPicker reloadComponent: 2];
            
        } else if (component == 1) {
            NSString *provinceIndex = [NSString stringWithFormat: @"%ld", [province indexOfObject: selectedProvince]];
            NSDictionary *tmp = [NSDictionary dictionaryWithDictionary: [areaDic objectForKey: provinceIndex]];
            NSDictionary *dic = [NSDictionary dictionaryWithDictionary: [tmp objectForKey: selectedProvince]];
            NSArray *dicKeyArray = [dic allKeys];
            NSArray *sortedArray = [dicKeyArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                
                if ([obj1 integerValue] > [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedDescending;
                }
                
                if ([obj1 integerValue] < [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedAscending;
                }
                return (NSComparisonResult)NSOrderedSame;
            }];
            
            NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [dic objectForKey: [sortedArray objectAtIndex: row]]];
            NSArray *cityKeyArray = [cityDic allKeys];
            
            district = [[NSArray alloc] initWithArray: [cityDic objectForKey: [cityKeyArray objectAtIndex:0]]];
            [cityPicker selectRow: 0 inComponent: 2 animated: YES];
            [cityPicker reloadComponent: 2];
        }
    }else{
        
    }
    
}


- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component {
    if (component == 0) {
        return 80;
    }
    else if (component == 1) {
        return 100;
    }
    else {
        return 115;
    }
}

- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view {
    UILabel *myView = nil;
    if (pickerView == cityPicker) {
        if (component == 0) {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [province objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
        else if (component == 1) {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [city objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
        else {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [district objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
    }
    return myView;
}


-(void)selectImageClick:(UIButton *)sender{
    if (sender.tag == 50000) {
        imgType = @"0";
        TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
        imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
        imagePC.modalPresentationStyle = 0;
        imagePC.showSelectBtn = YES;
        imagePC.allowCrop = NO;
        imagePC.allowPickingOriginalPhoto = NO;
        imagePC.oKButtonTitleColorNormal = Pink_Cor;
        imagePC.allowTakePicture = YES;
        imagePC.allowTakeVideo = NO;
        imagePC.allowPickingVideo = NO;
        imagePC.allowPickingMultipleVideo = NO;
        [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];

    }else{
        imgType = @"1";
        TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
        imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
        imagePC.modalPresentationStyle = 0;
        imagePC.showSelectBtn = YES;
        imagePC.allowCrop = NO;
        imagePC.allowPickingOriginalPhoto = NO;
        imagePC.oKButtonTitleColorNormal = Pink_Cor;
        imagePC.allowTakePicture = YES;
        imagePC.allowTakeVideo = NO;
        imagePC.allowPickingVideo = NO;
        imagePC.allowPickingMultipleVideo = NO;
        [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];

    }
}
- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    NSLog(@"------多选择图片--：%@",photos);
    if ([imgType isEqual:@"0"]) {
        _dsp_busImg = photos[0];
        [busImgBtn setImage:photos[0] forState:0];
//        [self uploadImg];
    }else{
        _dsp_othetImg = photos[0];
        [otherImgBtn setImage:photos[0] forState:0];
//        [self uploadImg];

    }
}
-(void)uploadImg{
    [MBProgressHUD showMessage:@""];
    
    YBWeakSelf;
    [[YBStorageObj storageManeger]getCOSType:^(int code) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (code == 0) {
                [weakSelf startUpload];
            }
        });
    }];
}
-(void)startUpload{
    YBWeakSelf;
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    UIImage *busimage =  busImgBtn.imageView.image;
    UIImage *otherimage =otherImgBtn.imageView.image;
    dispatch_group_async(group, queue, ^{
        NSData *imageData = UIImagePNGRepresentation(busimage);
        if (!imageData) {
            [MBProgressHUD hideHUD];
            [MBProgressHUD showError:YZMsg(@"图片错误")];
            return;
        }
        UIImage *herfImg = [UIImage imageWithData:imageData];
        NSString *imageName = [PublicObj getNameBaseCurrentTime:[NSString stringWithFormat:@"_shop_image0%@_cover.png", imgType]];
        [[YBStorageObj storageManeger]yb_storageImg:herfImg andName:imageName progress:^(CGFloat percent) {
            
        }complete:^(int code, NSString *key) {
            //图片成功
            businessImgUrl = key;
            dispatch_semaphore_signal(semaphore);
        }];
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    });

    dispatch_group_async(group, queue, ^{
        NSData *imageData = UIImagePNGRepresentation(otherimage);
        if (!imageData) {
            [MBProgressHUD hideHUD];
            [MBProgressHUD showError:YZMsg(@"图片错误")];
            return;
        }
        UIImage *herfImg = [UIImage imageWithData:imageData];
        NSString *imageName = [PublicObj getNameBaseCurrentTime:[NSString stringWithFormat:@"_shop_image1%@_cover.png", imgType]];
        [[YBStorageObj storageManeger]yb_storageImg:herfImg andName:imageName progress:^(CGFloat percent) {
            
        }complete:^(int code, NSString *key) {
            //图片成功
            otherImgUrl = key;
            dispatch_semaphore_signal(semaphore);
        }];
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    });
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self subBtnClick];
        });
        NSLog(@"任务完成执行");
    });


}

#pragma mark-------审核拒绝赋值-----------
-(void)setApplyData:(NSDictionary *)dic{
    idField.text = minstr([dic valueForKey:@"uid"]);
    nameField.text = minstr([dic valueForKey:@"username"]);
    cardField.text = minstr([dic valueForKey:@"cardno"]);
    sellerNameField.text = minstr([dic valueForKey:@"contact"]);
    sellerPhoneField.text = minstr([dic valueForKey:@"phone"]);
    sellerAddressField.text =[NSString stringWithFormat:@"%@%@%@", minstr([dic valueForKey:@"province"]),minstr([dic valueForKey:@"city"]),minstr([dic valueForKey:@"area"])];
    provinceStr =minstr([dic valueForKey:@"province"]);
    cityStr = minstr([dic valueForKey:@"city"]);
    districtStr = minstr([dic valueForKey:@"area"]);
    
    sellerAddDesField.text =minstr([dic valueForKey:@"address"]);
    _phoneField.text = minstr([dic valueForKey:@"service_phone"]);
    receiverField.text = minstr([dic valueForKey:@"receiver"]);
    receiverPhoneField.text = minstr([dic valueForKey:@"receiver_phone"]);
    receiverAddressField.text =[NSString stringWithFormat:@"%@%@%@",minstr([dic valueForKey:@"receiver_province"]),minstr([dic valueForKey:@"receiver_city"]),minstr([dic valueForKey:@"receiver_area"])] ;
    recever_provinceStr = minstr([dic valueForKey:@"receiver_province"]);
    recever_cityStr = minstr([dic valueForKey:@"receiver_city"]);
    recever_districtStr = minstr([dic valueForKey:@"receiver_area"]);

    receiverAddDesField.text = minstr([dic valueForKey:@"receiver_address"]);
    
    [busImgBtn sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"certificate_format"])] forState:0 completed:nil];
    [otherImgBtn sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"other_format"])] forState:0 completed:nil];
    businessImgUrl =minstr([dic valueForKey:@"certificate"]);
    otherImgUrl = minstr([dic valueForKey:@"other"]);
    
    NSMutableArray *array = [NSMutableArray array];
    NSArray *class = [dic valueForKey:@"goods_classid"];
    for (NSDictionary *dic in class) {
        CommodityClassModel *model = [CommodityClassModel modelWithDic:dic];
        [array addObject:model];
    }
    self.classArr = array;
}
-(BOOL)textFieldShouldReturn:(UITextField *)textField
{
    [textField resignFirstResponder];
    return YES;
}
- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if ([text isEqualToString:@"\n"]) {
        [self.view endEditing:YES];
        return NO;//这里返回NO，就代表return键值失效，即页面上按下return，不会出现换行，如果为yes，则输入页面会换行
    }
    return YES;
}
-(void)setLocationAddress{
    int provinceIndex = 0;
    int cityIndex = 0;

    NSArray *components = [areaDic allKeys];
    NSArray *sortedArray = [components sortedArrayUsingComparator: ^(id obj1, id obj2) {
        
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;
        }
        
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;
        }
        return (NSComparisonResult)NSOrderedSame;
    }];
    
    NSMutableArray *provinceTmp = [[NSMutableArray alloc] init];
    for (int i=0; i<[sortedArray count]; i++) {
        NSString *index = [sortedArray objectAtIndex:i];
        NSArray *tmp = [[areaDic objectForKey: index] allKeys];
        [provinceTmp addObject: [tmp objectAtIndex:0]];
    }

    if (normalProvince.length > 0) {
        selectedProvince = normalProvince;

        for (int i = 0; i < province.count; i ++) {
            if ([normalProvince isEqual:province[i]]) {
                provinceIndex = i;
                NSString *index = [sortedArray objectAtIndex:i];

                NSDictionary *proviceDic = [NSDictionary dictionaryWithDictionary: [[areaDic objectForKey:index]objectForKey:normalProvince]];
                NSArray *cityArray = [proviceDic allKeys];
                NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: [cityArray objectAtIndex:i]]];

                NSArray *citySortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                    if ([obj1 integerValue] > [obj2 integerValue]) {
                        return (NSComparisonResult)NSOrderedDescending;//递减
                    }
                    if ([obj1 integerValue] < [obj2 integerValue]) {
                        return (NSComparisonResult)NSOrderedAscending;//上升
                    }
                    return (NSComparisonResult)NSOrderedSame;
                }];
                NSMutableArray *m_array = [[NSMutableArray alloc] init];
                for (int i=0; i<[citySortedArray count]; i++) {
                    NSString *index = [citySortedArray objectAtIndex:i];
                    NSArray *temp = [[proviceDic objectForKey: index] allKeys];
                    [m_array addObject: [temp objectAtIndex:0]];
                }
               NSArray *cityArr = [NSArray arrayWithArray:m_array];
                city =[NSArray arrayWithArray:m_array];
                for (int j = 0; j < cityArr.count; j ++) {
                    if ([normalCity isEqual:cityArr[j]]) {
                        cityIndex = j;
                        NSString *keys = [NSString stringWithFormat:@"%d",cityIndex];
                        NSDictionary *dicssss = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: keys]];

                        NSString *selectedCity = [cityArr objectAtIndex: j];
                        district = [[NSArray alloc] initWithArray: [dicssss objectForKey: selectedCity]];

                        NSArray * districtArr = [[NSArray alloc] initWithArray: [dicssss objectForKey: selectedCity]];
                        for (int k = 0; k <districtArr.count; k ++) {
                            if ([normalDistrict isEqual:districtArr[k]]) {
                                [cityPicker selectRow: provinceIndex inComponent: 0 animated: YES];
                                [cityPicker reloadComponent: 1];

                                [cityPicker selectRow: cityIndex inComponent: 1 animated: YES];
                                [cityPicker reloadComponent: 2];

                                [cityPicker selectRow: k inComponent: 2 animated: YES];
                            }
                        }
                    }
                }
            }
        }
    }
    NSLog(@"province===:%@  \n city:%@   \ndistrict:%@",province,city,district);
}
-(void)location{
    if (!_lbsManager) {
        _lbsManager = [[CLLocationManager alloc] init];
        [_lbsManager setDesiredAccuracy:kCLLocationAccuracyBest];
        _lbsManager.delegate = self;
        // 兼容iOS8定位
        CLAuthorizationStatus status = [CLLocationManager authorizationStatus];

        if (kCLAuthorizationStatusDenied == status || kCLAuthorizationStatusRestricted == status) {
            NSLog(@"请打开您的位置服务!");
            NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];

               UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:nil message:[NSString stringWithFormat:YZMsg(@"打开“定位服务”来允许“%@”确定您的位置"),[infoDictionary objectForKey:@"CFBundleDisplayName"]] preferredStyle:UIAlertControllerStyleAlert];
               UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"设置") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                   [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
               }];
               [alertContro addAction:cancleAction];
            [cancleAction setValue:Normal_Color forKey:@"_titleTextColor"];
               UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                   
               }];
               [alertContro addAction:sureAction];
            [sureAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];

            [[[XGGAppDelegate sharedAppDelegate]topViewController]presentViewController:alertContro animated:YES completion:nil];

        }else{


            // 兼容iOS8定位
            SEL requestSelector = NSSelectorFromString(@"requestWhenInUseAuthorization");
            if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined && [_lbsManager respondsToSelector:requestSelector]) {
                [_lbsManager requestWhenInUseAuthorization];  //调用了这句,就会弹出允许框了.
            } else {
                [_lbsManager startUpdatingLocation];
            }
        }
    }
}
- (void)stopLbs {
    [_lbsManager stopUpdatingHeading];
    _lbsManager.delegate = nil;
    _lbsManager = nil;
}
- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    if (status == kCLAuthorizationStatusRestricted || status == kCLAuthorizationStatusDenied) {
        [self stopLbs];
    } else {
        [_lbsManager startUpdatingLocation];
    }
}
- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error
{
    [self stopLbs];
}
- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
{
    //zl-----待修改
    CLLocation *newLocatioin = locations[0];
//    liveCity *cityU = [XGGcityDefault myProfile];
//    cityU.lat = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.latitude];
//    cityU.lng = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.longitude];
    CLGeocoder *geocoder = [[CLGeocoder alloc] init];
    [geocoder reverseGeocodeLocation:newLocatioin completionHandler:^(NSArray *placemarks, NSError *error) {
        if (!error)
        {
            CLPlacemark *placeMark = placemarks[0];
            NSString *addr = [NSString stringWithFormat:@"%@%@%@",placeMark.administrativeArea,placeMark.locality,placeMark.subLocality];
            NSLog(@"hhhhhhhh----:%@", addr);
            normalProvince =placeMark.administrativeArea;
            normalCity = placeMark.locality;
            normalDistrict = placeMark.subLocality;
        }
    }];
     [self stopLbs];
}

@end
