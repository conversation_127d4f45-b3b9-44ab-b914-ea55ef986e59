//
//  ShopApplyStatusVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/9.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ShopApplyStatusVC.h"
#import "ApplyShopVC.h"
@interface ShopApplyStatusVC ()
{
    NSDictionary *infoDic;
    UILabel *infoLb;
}
@end

@implementation ShopApplyStatusVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"开通小店");
    infoDic = [NSDictionary dictionary];
    UIImageView *statuImg = [[UIImageView alloc]init];
    statuImg.frame = CGRectMake(0, 64+statusbarHeight+30, 134, 114);
    statuImg.centerX = self.view.centerX;
    statuImg.image = [UIImage imageNamed:@"shupapply"];
    [self.view addSubview:statuImg];
    
    UILabel *statusLb = [[UILabel alloc]init];
    statusLb.font = [UIFont systemFontOfSize:16];
    statusLb.textAlignment = NSTextAlignmentCenter;
    statusLb.numberOfLines = 0;
    if ([self.statusStr isEqual:@"0"]) {
        statusLb.textColor = RGB(100, 100, 100);
        statusLb.text = YZMsg(@"信息审核中...");
    }else if ([self.statusStr isEqual:@"2"]){
        statusLb.textColor = Pink_Cor;
        statusLb.text = YZMsg(@"身份信息审核未通过");
    }
    [self.view addSubview:statusLb];
    [statusLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(statuImg.mas_bottom).offset(20);
        make.height.mas_equalTo(20);
    }];
    
    infoLb = [[UILabel alloc]init];
    infoLb.font = [UIFont systemFontOfSize:14];
    infoLb.textAlignment = NSTextAlignmentCenter;
    infoLb.textColor = [UIColor grayColor];
    infoLb.numberOfLines = 0;
    if ([self.statusStr isEqual:@"0"]) {
        infoLb.text = YZMsg(@"3个工作日内会有审核结果,请耐心等待");
    }else if ([self.statusStr isEqual:@"2"]){
        infoLb.text = YZMsg(@"证件照片信息模糊，请上传清晰照片");
    }
    [self.view addSubview:infoLb];
    [infoLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(statusLb.mas_bottom).offset(10);
       // make.height.mas_equalTo(20);

    }];
    
    UIButton *reApply = [UIButton buttonWithType:0];
    [reApply setBackgroundColor:Pink_Cor];
    [reApply setTitle:YZMsg(@"重新认证") forState:0];
    [reApply setTitleColor:[UIColor whiteColor] forState:0];
    reApply.titleLabel.font = [UIFont systemFontOfSize:14];
    reApply.layer.cornerRadius = 20;
    reApply.layer.masksToBounds = YES;
    [reApply addTarget:self action:@selector(goApplyShopVC) forControlEvents:UIControlEventTouchUpInside];
    if ([self.statusStr isEqual:@"0"]) {
        reApply.hidden = YES;
    }else if ([self.statusStr isEqual:@"2"]){
        reApply.hidden = NO;
    }
    [self.view addSubview:reApply];
    
    [reApply mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(infoLb.mas_bottom).offset(30);
        make.height.mas_equalTo(40);
        make.width.mas_equalTo(200);
    }];

    [self getApplyInfo];
}
-(void)goApplyShopVC{
    ApplyShopVC *shop = [[ApplyShopVC alloc]init];
    shop.fromWhere = @"reject";
    shop.applyDic = infoDic;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:shop animated:YES];

}
-(void)getApplyInfo{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Shop.getShopApplyInfo"];
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };

    [YBNetworking postWithUrl:@"Shop.getShopApplyInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            infoDic = [info firstObject];
            infoLb.text = minstr([infoDic valueForKey:@"reason"]);
        }
        } Fail:^(id fail) {
            
     }];

}

@end
