//
//  MJPropertyKey.h
//  MJExtensionExample
//
//  Created by <PERSON><PERSON> on 15/8/11.
//  Copyright (c) 2015年 小码哥. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum {
    MJPropertyKeyTypeDictionary = 0, // 字典的key
    MJPropertyKeyTypeArray // 数组的key
} MJPropertyKeyType;

/**
 *  属性的key
 */
@interface MJPropertyKey : NSObject
/** key的名字 */
@property (copy,   nonatomic) NSString *name;
/** key的种类，可能是@"10"，可能是@"age" */
@property (assign, nonatomic) MJPropertyKeyType type;

/**
 *  根据当前的key，也就是name，从object（字典或者数组）中取值
 */
- (id)valueInObject:(id)object;

@end
