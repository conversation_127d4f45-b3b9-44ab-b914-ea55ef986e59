//
//  SelectStandardsView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/13.
//  Copyright © 2020 cat. All rights reserved.
//

#import "SelectStandardsView.h"
#import "StandardsCell.h"
#import "ConfirmOrderVC.h"
@implementation SelectStandardsView

-(instancetype)init{
    self = [super init];
    if (self) {
        self.backgroundColor = RGBA(29, 29, 29, 0.3);
        countNum = 1;
        [self creatUI];
    }
    return self;
}

-(void)creatUI{
    
    UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height *0.35, _window_width, _window_height *0.65)];
    backView.backgroundColor = [UIColor whiteColor];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:backView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = backView.bounds;
    maskLayer.path = maskPath.CGPath;
    backView.layer.mask = maskLayer;
    [self addSubview:backView];
    
    UIButton *closeBtn = [UIButton buttonWithType:0];
    closeBtn.frame = CGRectMake(_window_width-30, 15, 20, 20);
    [closeBtn setImage:[UIImage imageNamed:@"standardClose"] forState:0];
    [closeBtn addTarget:self action:@selector(closeClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:closeBtn];
    
    
    self.headImg = [[UIImageView alloc]init];
    self.headImg.frame = CGRectMake(15, 15, 110, 110);
    self.headImg.backgroundColor = [UIColor lightGrayColor];
    self.headImg.layer.cornerRadius = 5;
    self.headImg.layer.masksToBounds = YES;
    self.headImg.contentMode = UIViewContentModeScaleAspectFill;
    [backView addSubview:self.headImg];
    
    self.priceLb = [[UILabel alloc]init];
    self.priceLb.frame = CGRectMake(self.headImg.right+10, self.headImg.centerY-15, 200, 20);
    self.priceLb.font = [UIFont systemFontOfSize:16];
    self.priceLb.textColor = Pink_Cor;
    [backView addSubview:self.priceLb];
    
    self.countLb = [[UILabel alloc]init];
    self.countLb.frame = CGRectMake(self.headImg.right+10, self.priceLb.bottom+5, 200, 20);
    self.countLb.font = [UIFont systemFontOfSize:14];
    self.countLb.textColor = [UIColor grayColor];
    [backView addSubview:self.countLb];

    self.haveSelLb = [[UILabel alloc]init];
    self.haveSelLb.frame = CGRectMake(self.headImg.right+10, self.countLb.bottom+5, 200, 20);
    self.haveSelLb.font = [UIFont systemFontOfSize:14];
    self.haveSelLb.textColor = [UIColor blackColor];
    [backView addSubview:self.haveSelLb];

    UILabel *line = [[UILabel alloc]initWithFrame:CGRectMake(15, self.headImg.bottom+15, _window_width-30, 1)];
    line.backgroundColor = RGB(240, 240, 240);
    [backView addSubview:line];
    
    UILabel *title = [[UILabel alloc]initWithFrame:CGRectMake(15, line.bottom+10, _window_width-30, 20)];
    title.font = [UIFont systemFontOfSize:14];
    title.textColor = [UIColor blackColor];
    title.text = YZMsg(@"规格");
    [backView addSubview:title];

    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    
    flow.itemSize = CGSizeMake((_window_width-20-50)/3, 30);
    flow.minimumLineSpacing = 5;
    flow.minimumInteritemSpacing = 5;

    self.standardsCollection = [[UICollectionView alloc]initWithFrame:CGRectMake(10, title.bottom+10, _window_width-20, 90) collectionViewLayout:flow];
    self.standardsCollection.delegate = self;
    self.standardsCollection.dataSource = self;
    [self.standardsCollection registerNib:[UINib nibWithNibName:@"StandardsCell" bundle:nil] forCellWithReuseIdentifier:@"StandardsCell"];

    self.standardsCollection.alwaysBounceHorizontal = YES;
    self.standardsCollection.backgroundColor = [UIColor whiteColor];
    [backView addSubview:self.standardsCollection];

//    UILabel *line2 = [[UILabel alloc]initWithFrame:CGRectMake(15, self.standardsCollection.bottom+10, _window_width-30, 1)];
    UILabel *line2 = [[UILabel alloc]init];
    line2.backgroundColor = RGB(240, 240, 240);
    [backView addSubview:line2];
    [line2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.right.equalTo(self).offset(-15);
        make.top.equalTo(self.standardsCollection.mas_bottom).offset(10);
        make.height.mas_equalTo(1);
    }];
    
//    UILabel *titleCount =[[UILabel alloc]initWithFrame:CGRectMake(15, line2.bottom+20, 40, 30)];
    UILabel *titleCount =[[UILabel alloc]init];
    titleCount.font = [UIFont systemFontOfSize:14];
    titleCount.textColor = [UIColor blackColor];
    titleCount.text = YZMsg(@"数量");
    [backView addSubview:titleCount];
    [titleCount mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(line2.mas_bottom).offset(20);
        make.left.equalTo(self).offset(15);
        make.height.mas_equalTo(30);
    }];

    UIButton *addBtn = [UIButton buttonWithType:0];
//    addBtn.frame = CGRectMake(_window_width-20-30, line2.bottom+25, 20, 20);
    [addBtn setImage:[UIImage imageNamed:@"standards_加"] forState:0];
    [addBtn addTarget:self action:@selector(addCount) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:addBtn];
    [addBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-30);
        make.centerY.equalTo(titleCount.mas_centerY);
        make.width.height.mas_equalTo(20);
    }];
    
    countFeild = [[UITextField alloc]init];
//    countFeild.frame = CGRectMake(addBtn.left-50, line2.bottom+20, 40, 30);
    countFeild.textAlignment = NSTextAlignmentCenter;
    countFeild.font = [UIFont systemFontOfSize:14];
    countFeild.backgroundColor = RGB(250, 250, 250);
    countFeild.textColor = [UIColor blackColor];
    countFeild.text =[NSString stringWithFormat:@"%d",countNum];
    countFeild.userInteractionEnabled = NO;
    [backView addSubview:countFeild];
    [countFeild mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(addBtn.mas_left).offset(-10);
        make.centerY.equalTo(addBtn.mas_centerY);
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(30);
    }];
    
    UIButton *reduceBtn = [UIButton buttonWithType:0];
//    reduceBtn.frame = CGRectMake(countFeild.left-30, line2.bottom+25, 20, 20);
    [reduceBtn setImage:[UIImage imageNamed:@"standards_减"] forState:0];
    [reduceBtn addTarget:self action:@selector(reduceBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:reduceBtn];
    [reduceBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(countFeild.mas_left).offset(-10);
        make.centerY.equalTo(titleCount.mas_centerY);
        make.width.height.mas_equalTo(20);
    }];
    
    UIButton *payBtn = [UIButton buttonWithType:0];
    payBtn.frame = CGRectMake(15, backView.height-60, _window_width-30, 40);
    [payBtn setBackgroundColor:Pink_Cor];
    payBtn.layer.cornerRadius = 20;
    payBtn.layer.masksToBounds = YES;
    [payBtn setTitle:YZMsg(@"立即购买") forState:0];
    payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [payBtn setTitleColor:[UIColor whiteColor] forState:0];
    [payBtn addTarget:self action:@selector(payBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:payBtn];
    
}
-(void)addCount{
    countNum+=1;
    countFeild.text =[NSString stringWithFormat:@"%d",countNum];
}
-(void)reduceBtnClick{
    if (countNum == 1) {
        [MBProgressHUD showError:YZMsg(@"最小数量为1件")];
        return;
    }
    countNum-=1;
    countFeild.text =[NSString stringWithFormat:@"%d",countNum];
}

-(void)closeClick{
    if (self.standarEvent) {
        self.standarEvent(@"", @"", @"0");
    }
}

-(void)setModel:(CommodityDetailModel *)model{
    _model = model;
    NSDictionary *first = _model.specs_format[0];
    selectDic = first;
    [self.headImg sd_setImageWithURL:[NSURL URLWithString:minstr([first valueForKey:@"thumb"])]];
    self.priceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), minstr([first valueForKey:@"price"])];
    self.countLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"库存:"),minstr([first valueForKey:@"spec_num"])];
    self.haveSelLb.text=[NSString stringWithFormat:@"%@%@",YZMsg(@"已选:"),minstr([first valueForKey:@"spec_name"])];
    
    NSIndexPath *index = [NSIndexPath indexPathForItem:0 inSection:0];
    currentIndex = index;
    if (model.specs_format.count < 3) {
        self.standardsCollection.size = CGSizeMake(_window_width-20, 45);
    }else{
        self.standardsCollection.size = CGSizeMake(_window_width-20, 90);

    }
    [self.standardsCollection reloadData];
    [self layoutIfNeeded];
}

-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return _model.specs_format.count;
}
-(UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section
{
    
    return UIEdgeInsetsMake(0, 0, 0, 0);
}
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    
    StandardsCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"StandardsCell" forIndexPath:indexPath];
    
    cell.titleLb.text =[_model.specs_format[indexPath.item] valueForKey:@"spec_name"];
    cell.titleLb.adjustsFontSizeToFitWidth=YES;
    cell.titleLb.minimumScaleFactor=0.5;
    if (currentIndex == indexPath) {
        cell.titleLb.backgroundColor = Pink_Cor;
        cell.titleLb.textColor = [UIColor whiteColor];

    }else{
        cell.titleLb.backgroundColor = RGB(250, 250, 250);
        cell.titleLb.textColor = [UIColor grayColor];

    }
    return cell;
    
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    NSDictionary *infos = _model.specs_format[indexPath.item];
    selectDic = infos;
    
    [self.headImg sd_setImageWithURL:[NSURL URLWithString:minstr([infos valueForKey:@"thumb"])]];
    self.priceLb.text =[NSString stringWithFormat:@"%@%@",YZMsg(@"¥"),minstr([infos valueForKey:@"price"])];
    self.countLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"库存:"),minstr([infos valueForKey:@"spec_num"])];
    self.haveSelLb.text=[NSString stringWithFormat:@"%@%@",YZMsg(@"已选:"),minstr([infos valueForKey:@"spec_name"])];
    
    currentIndex = indexPath;
    [self.standardsCollection reloadData];
}
-(void)payBtnClick{
    ConfirmOrderVC *orderVc = [[ConfirmOrderVC alloc]init];
    orderVc.orderDic = selectDic;
    orderVc.orderModel = _model;
    orderVc.liveuid = self.liveId;
    orderVc.countNum = [countFeild.text intValue];
    orderVc.shareUserid = self.shareUID;

    [[YBBaseAppDelegate sharedAppDelegate]pushViewController:orderVc animated:YES];
    
    [self closeClick];
}
@end
