//
//  SelectStandardsView.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/13.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "CommodityDetailModel.h"
NS_ASSUME_NONNULL_BEGIN
typedef void(^StandardsEvent)(NSString *standards, NSString *countStr, NSString *type);
@interface SelectStandardsView : UIView<UICollectionViewDelegate, UICollectionViewDataSource>
{
    NSIndexPath *currentIndex;
    int countNum;
    UITextField *countFeild;
    
    NSDictionary *selectDic;
}
@property (nonatomic, strong)NSString *liveId;

@property(nonatomic, strong)UIImageView *headImg;
@property(nonatomic, strong)UILabel *priceLb;
@property(nonatomic, strong)UILabel *countLb;
@property(nonatomic, strong)UILabel *haveSelLb;
@property(nonatomic, strong)UICollectionView *standardsCollection;
@property(nonatomic, strong)NSString *shareUID;

@property (nonatomic, copy)StandardsEvent standarEvent;

@property (nonatomic, strong)CommodityDetailModel *model;

@end

NS_ASSUME_NONNULL_END
