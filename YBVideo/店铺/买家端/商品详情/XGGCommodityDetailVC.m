//
//  CommodityDetailVC.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/2.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityDetailVC.h"
#import "StoreInfoView.h"
#import "CommodityCell1.h"
#import "CommodityCell2Row1.h"
#import "CommodityCell2Row2.h"
#import "CommodityCell3.h"
#import "CommodityDetailModel.h"
#import "shopDetailVC.h"
#import "GuaranteeView.h"
#import "SelectStandardsView.h"
#import "CommodityEvaluationCell.h"
#import "GoodsEvaluationListVC.h"
#import "GoodsExplainCell.h"
#import "JCHATConversationViewController.h"
#import "ShareGoodsAlert.h"
@interface CommodityDetailVC ()<UITableViewDelegate, UITableViewDataSource,goshopDelegate>{
    UILabel *sel_line;
    UIButton *allBtn;
    UIButton *detailBtn;
    
    UIButton *isCollectBtn;
    BOOL isEvaluate;
}
@property (nonatomic, strong)UITableView *commodityTable;

@property (nonatomic, strong)CommodityDetailModel *commodityModel;
@property (nonatomic, strong)GuaranteeView *guarView;
@property (nonatomic, strong)SelectStandardsView *standardsView;

@end

@implementation CommodityDetailVC

#pragma mark ----添加商品访问记录------
-(void)addGoodsVisit{
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"goodsid":self.goodsID, @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":self.goodsID,
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    [YBNetworking postWithUrl:@"Buyer.addGoodsVisitRecord" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {

        }else{
            if (code !=1001) {
                [MBProgressHUD showError:msg];
            }
        }

        } Fail:^(id fail) {
            
        }];

}
#pragma mark----获取商品详情------
-(void)getGoodInfo{
    NSString *url = [purl stringByAppendingFormat:@"?service=Shop.getGoodsInfo"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":self.goodsID,
                          };
    
    [YBNetworking postWithUrl:@"Shop.getGoodsInfo" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            self.commodityModel = [CommodityDetailModel modelWithDic:[info firstObject]];
            [self.commodityTable reloadData];
            if ([self.commodityModel.iscollect isEqual:@"1"]) {
                [isCollectBtn setImage:[UIImage imageNamed:getImagename(@"commodity_收藏亮")] forState:0];
            }else{
                [isCollectBtn setImage:[UIImage imageNamed:getImagename(@"commodity_收藏")] forState:0];
            }
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}
-(void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:YES];
    [[NSNotificationCenter defaultCenter]postNotificationName:@"stopGoodsVideo" object:nil];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    if ([self.fromWhere isEqual:@"seller"]) {
        self.titleL.text = YZMsg(@"预览商品");
    }else{
        self.titleL.text = YZMsg(@"商品详情");
        [self addGoodsVisit];

    }
    self.rightBtn.hidden = NO;
    [self.rightBtn setImage:[UIImage imageNamed:@"goods_share"] forState:0];
    isEvaluate = NO;
    [self.view addSubview:self.commodityTable];
    [self createBottom];
    [self getGoodInfo];
}

-(void)clickNaviRightBtn
{
    ShareGoodsAlert *alert = [[ShareGoodsAlert alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andGoodsModel:self.commodityModel];
    [self.view addSubview:alert];

}

-(UITableView *)commodityTable{
    if (!_commodityTable) {
        _commodityTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60-ShowDiff) style:UITableViewStylePlain];
        _commodityTable.delegate = self;
        _commodityTable.dataSource = self;
        _commodityTable.separatorStyle = UITableViewCellSeparatorStyleNone;
//        _commodityTable.estimatedRowHeight = 0;
//        _commodityTable.estimatedSectionHeaderHeight = 0;
//        _commodityTable.estimatedSectionFooterHeight = 0;
        _commodityTable.showsVerticalScrollIndicator = NO;
    }
    return _commodityTable;
}
#pragma mark-------UITableviewDelegate--------------
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 4;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (section == 0) {
        return 1;

    }else if (section ==  1){
        return 2;

    }else if (section ==  2){
        return 1;

    }else {
        if (isEvaluate) {
            return self.commodityModel.comment_lists.count+1;
        }else{
            return 1;
        }
    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {
        return 505;
    }else if(indexPath.section == 1){
        return 45;
    }else if (indexPath.section == 2){
        return 170;
    }else{
        NSLog(@"ddddddddddd:%f", self.commodityModel.commentCellHeight);
        if (isEvaluate) {
            if (indexPath.row == self.commodityModel.comment_lists.count) {
                        return 44;
            }else{
                return self.commodityModel.commentCellHeight;
            }
        }else{
            return _window_height-64-statusbarHeight-50-80;
        }
    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section
{
    if (section == 0) {
        return 5;
    }else if(section == 1){
        return 5;
    }else if (section == 2){
        return 5;
    }else{
        return 0;
    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    if (section == 3) {
        return 50;
    }else{
        return 0.01;
    }
}
-(UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section
{
    UIView *footer = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 5)];
    footer.backgroundColor = RGB(250, 250, 250);
    return footer;
}

-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    if (section == 3) {
        UIView *footer = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 50)];
        footer.backgroundColor = [UIColor whiteColor];
        allBtn = [UIButton buttonWithType:0];
        allBtn.frame =CGRectMake(_window_width/2+10, 10, 110, 30);// CGRectMake(_window_width/2-120, 10, 110, 30);
        [allBtn setTitle:[NSString stringWithFormat:@"%@ (%@)",YZMsg(@"宝贝评价"),self.commodityModel.comment_nums] forState:0];
        allBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [allBtn setTitleColor:[UIColor blackColor] forState:UIControlStateSelected];
        [allBtn setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
        allBtn.titleLabel.textAlignment = NSTextAlignmentRight;
        [allBtn addTarget:self action:@selector(evaluateClick:) forControlEvents:UIControlEventTouchUpInside];
        allBtn.tag = 30000;
        [footer addSubview: allBtn];
        
        detailBtn = [UIButton buttonWithType:0];
        detailBtn.frame = CGRectMake(_window_width/2-120, 10, 110, 30);//CGRectMake(_window_width/2+10, 10, 110, 30);
        [detailBtn setTitle:[NSString stringWithFormat:@"%@",YZMsg(@"宝贝详情")] forState:0];
        detailBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [detailBtn setTitleColor:[UIColor blackColor] forState:UIControlStateSelected];
        [detailBtn setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
        [detailBtn addTarget:self action:@selector(evaluateClick:) forControlEvents:UIControlEventTouchUpInside];
        detailBtn.tag = 30001;

        [footer addSubview: detailBtn];

        
        sel_line = [[UILabel alloc]init];
        sel_line.frame = CGRectMake(0, allBtn.bottom, 20, 2);
        sel_line.backgroundColor = Pink_Cor ;
        [footer addSubview:sel_line];
        if (isEvaluate) {
            allBtn.selected = YES;
            detailBtn.selected = NO;

            sel_line.centerX = allBtn.centerX;
        }else{
            detailBtn.selected = YES;
            allBtn.selected = NO;
            sel_line.centerX = detailBtn.centerX;
        }
        return footer;
    }else{
        UIView *view = [[UIView alloc]initWithFrame:CGRectZero];
        return view;
    }
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {
        CommodityCell1 *cell1 = [[CommodityCell1 alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell1"];
        cell1.model = self.commodityModel;
        cell1.selectionStyle = UITableViewCellSelectionStyleNone;
        cell1.contentView.backgroundColor = UIColor.clearColor;
        cell1.backgroundColor = UIColor.clearColor;
        return cell1;
    }else if(indexPath.section == 1){
        if (indexPath.row == 0) {
            CommodityCell2Row1 *row1 = [[NSBundle mainBundle]loadNibNamed:@"CommodityCell2Row1" owner:self options:nil].firstObject;
            return row1;
        }else{
            CommodityCell2Row2 *row2 = [[NSBundle mainBundle]loadNibNamed:@"CommodityCell2Row2" owner:self options:nil].firstObject;
            return row2;
        }

    }else if(indexPath.section == 2){
        CommodityCell3 *cell3 = [[CommodityCell3 alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell3"];
        cell3.model = self.commodityModel;
        cell3.delegate = self;
        cell3.selectionStyle = UITableViewCellSelectionStyleNone;

        return cell3;

    }else{
        if (isEvaluate) {
            if (indexPath.row == self.commodityModel.comment_lists.count) {
                UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cells"];
                
                UILabel *lookMore = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, _window_width, 40)];
                lookMore.font =[UIFont systemFontOfSize:14];
                lookMore.textColor = [UIColor grayColor];
                if (self.commodityModel.comment_lists.count == 0) {
                    lookMore.text = YZMsg(@"还没有收到任何评价");
                }else{
                    lookMore.text = YZMsg(@"查看更多评价");

                }
                lookMore.textAlignment = NSTextAlignmentCenter;
                [cell addSubview:lookMore];
                return cell;
        }else{
            NSString *CellIdentifier = [NSString stringWithFormat:@"CommodityEvaluationCell%ld%ld",indexPath.section,indexPath.row];
            CommodityEvaluationCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
            if (!cell) {
                cell = [[CommodityEvaluationCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
            }
            //最外层用户评价
            [cell setCommodityModel:self.commodityModel andListModel:self.commodityModel.comment_lists[indexPath.row]];
            
            self.commodityModel.comment_dic =self.commodityModel.comment_lists[indexPath.row];
            CGFloat content_height = [PublicObj heightOfString:[self.commodityModel.comment_dic valueForKey:@"content"] andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-50];
            
            self.commodityModel.commentCellHeight = content_height + 80;
            NSLog(@"66666666666:%f", self.commodityModel.commentCellHeight);
            
            if (cell.allArr.count > 0) {
                if (cell.allArr.count > 3) {
                    self.commodityModel.commentCellHeight += cell.imgWidth*2+30+30;
                }else{
                    self.commodityModel.commentCellHeight += cell.imgWidth+20+30;
                }
            }else{
                self.commodityModel.commentCellHeight+=40;
            }
            NSLog(@"7777777777:%f", self.commodityModel.commentCellHeight);

            return cell;

        }
        }else{
            GoodsExplainCell *cell = [[GoodsExplainCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"GoodsExplainCell"];
            cell.model = self.commodityModel;
            return cell;
        }
    }
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if ([self.fromWhere isEqual:@"seller"]) {
        [MBProgressHUD showError:YZMsg(@"预览不支持该功能")];
        return;
    }
    if ([self.commodityModel.goods_status isEqual:@"-1"]){
        [MBProgressHUD showError:YZMsg(@"商品已下架")];
        return;
    }

    YBWeakSelf;
    if(indexPath.section == 1){
        if (indexPath.row == 0) {
            if (_standardsView) {
                [_standardsView removeFromSuperview];
                _standardsView = nil;
            }
            _standardsView = [[SelectStandardsView alloc]init];
            _standardsView.frame = CGRectMake(0, 0, _window_width, _window_height);
            _standardsView.model = self.commodityModel;
            _standardsView.shareUID = self.shareuid;

            _standardsView.standarEvent = ^(NSString * _Nonnull standards, NSString * _Nonnull countStr, NSString * _Nonnull type) {
                if ([type isEqual:@"0"]) {
                    [weakSelf.standardsView removeFromSuperview];
                    weakSelf.standardsView = nil;
                }
            };
            [self.view addSubview:_standardsView];
        }else{
            if (_guarView) {
                [_guarView removeFromSuperview];
                _guarView = nil;
            }
            _guarView = [[NSBundle mainBundle]loadNibNamed:@"GuaranteeView" owner:self options:nil].firstObject;
            _guarView.frame = CGRectMake(0, 0, _window_width, _window_height);
            _guarView.hideEvent = ^{
                [weakSelf.guarView removeFromSuperview];
                weakSelf.guarView = nil;
            };
            [self.view addSubview:_guarView];
        }
    }else if (indexPath.section == 3){
        if (isEvaluate){
            if (self.commodityModel.comment_lists.count == 0) {
                return;
            }
            if (indexPath.row == self.commodityModel.comment_lists.count) {
                GoodsEvaluationListVC *listVC = [[GoodsEvaluationListVC alloc]init];
                listVC.goodsIDStr = self.goodsID;
                [[XGGAppDelegate sharedAppDelegate]pushViewController:listVC animated:YES];
            }

        }
    }else
    {
        [tableView deselectRowAtIndexPath:indexPath animated:NO];
    }
}
-(void)evaluateClick:(UIButton *)sender{
    if (sender.tag == 30000) {
        sel_line.centerX = allBtn.centerX;
        isEvaluate = YES;
    }else{
        sel_line.centerX = detailBtn.centerX;
        isEvaluate = NO;
    }
    [UIView performWithoutAnimation:^{
        NSIndexSet *indexSet=[[NSIndexSet alloc]initWithIndex:3];
        [self.commodityTable reloadSections:indexSet withRowAnimation:UITableViewRowAnimationNone];
    }];
}

#pragma mark------添加底部按钮---------
-(void)createBottom{
    UIView *bottomBack = [[UIView alloc]init];
    bottomBack.frame = CGRectMake(0, _window_height-60-ShowDiff, _window_width, 60+ShowDiff);
    bottomBack.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bottomBack];
    
    [PublicObj lineViewWithFrame:CGRectMake(0, 0, _window_width, 1) andColor:RGB(240, 240, 240) andView:bottomBack];
    NSArray *arr = @[@"店铺",@"客服",@"收藏",@"立即购买"];
    NSArray *titlearr = @[YZMsg(@"店铺"),YZMsg(@"客服"),YZMsg(@"收藏"),YZMsg(@"立即购买")];

    for (int i = 0; i < arr.count; i ++) {
        if ( i < 3) {
            UIButton *btn = [UIButton buttonWithType:0];
            btn.frame = CGRectMake(i * _window_width *0.2, 0, _window_width *0.2, 60);
            NSString *langStr = @"";
            if ([lagType isEqual:EN]) {
                langStr = [common getLanguage];
            }
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"commodity_%@%@",arr[i],langStr]] forState:0];
            btn.tag = 100000+i;
            [btn addTarget:self action:@selector(bottomBtnClick:) forControlEvents:UIControlEventTouchUpInside];
            [bottomBack addSubview:btn];
            if (i == 2) {
                isCollectBtn = btn;
            }
        }else{
            UIButton *btn = [UIButton buttonWithType:0];
            btn.frame = CGRectMake( _window_width *0.6, 0, _window_width *0.4, 60);
            [btn setTitle:titlearr[i] forState:0];
            [btn setTitleColor:[UIColor whiteColor] forState:0];
            btn.titleLabel.font = [UIFont systemFontOfSize:14];
            btn.tag = 100000+i;
            [btn addTarget:self action:@selector(bottomBtnClick:) forControlEvents:UIControlEventTouchUpInside];

            [btn setBackgroundColor:Pink_Cor];
            [bottomBack addSubview:btn];
        }
    }
}
-(void)bottomBtnClick:(UIButton *)sender{
    NSLog(@"yonghuUID-----:%@",self.commodityModel.shop_uid);
    if ([self.fromWhere isEqual:@"seller"] || [self.commodityModel.shop_uid isEqual:[Config getOwnID]]) {
        [MBProgressHUD showError:YZMsg(@"预览不支持该功能")];
        return;
    }

    switch (sender.tag) {
        case 100000:
            if ([self.commodityModel.goods_status isEqual:@"-1"]){
                [MBProgressHUD showError:YZMsg(@"商品已下架")];
                return;
            }

            [self goshopClick];
            break;
        case 100001:{
            if ([self.commodityModel.goods_status isEqual:@"-1"]){
                [MBProgressHUD showError:YZMsg(@"商品已下架")];
                return;
            }
            if ([self.commodityModel.type isEqual:@"2"]) {
                UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"请到个性设置-关于我们中联系客服") preferredStyle:UIAlertControllerStyleAlert];
                UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                }];
                [sureAction setValue:Normal_Color forKey:@"_titleTextColor"];
                [alertControl addAction:sureAction];
                [[[XGGAppDelegate sharedAppDelegate] topViewController]presentViewController:alertControl animated:YES completion:nil];

            }else{
                NSDictionary *userDic = @{
                    @"id":self.commodityModel.shop_uid,
                    @"user_nickname":self.commodityModel.shop_usernicename,
                    @"avatar":self.commodityModel.shop_avatar,
                };
                [[YBMessageManager shareManager] chatWithUser:userDic];
            }

        }
            break;
        case 100002:{
            [self collectionGoods];
        }
            break;
        case 100003:{
            if ([self.commodityModel.goods_status isEqual:@"-1"]){
                [MBProgressHUD showError:YZMsg(@"商品已下架")];
                return;
            }

            YBWeakSelf;
            if (_standardsView) {
                [_standardsView removeFromSuperview];
                _standardsView = nil;
            }
            _standardsView = [[SelectStandardsView alloc]init];
            _standardsView.frame = CGRectMake(0, 0, _window_width, _window_height);
            _standardsView.model = self.commodityModel;
            _standardsView.liveId = self.liveUid;
            _standardsView.shareUID = self.shareuid;

            _standardsView.standarEvent = ^(NSString * _Nonnull standards, NSString * _Nonnull countStr, NSString * _Nonnull type) {
                if ([type isEqual:@"0"]) {
                    [weakSelf.standardsView removeFromSuperview];
                    weakSelf.standardsView = nil;
                }
            };
            [self.view addSubview:_standardsView];
        }
            break;
        default:
            break;
    }
}
-(void)collectionGoods{
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":self.goodsID
                          };
    
    [YBNetworking postWithUrl:@"Shop.setCollect" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            NSDictionary *infos = [info firstObject];
            NSString *iscollect = minstr([infos valueForKey:@"iscollect"]);
            if ([iscollect isEqual:@"1"]) {
                [isCollectBtn setImage:[UIImage imageNamed:getImagename(@"commodity_收藏亮")] forState:0];
                [MBProgressHUD showError:YZMsg(@"已收藏")];
            }else{
                [isCollectBtn setImage:[UIImage imageNamed:getImagename(@"commodity_收藏")] forState:0];
                [MBProgressHUD showError:YZMsg(@"已取消收藏")];
            }
        }else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];

}
#pragma mark--------店铺主页-----------
-(void)goshopClick
{
    if ([self.fromWhere isEqual:@"seller"]) {
        [MBProgressHUD showError:YZMsg(@"预览不支持该功能")];
        return;
    }
    if ([self.backTags isEqual:@"shop"]) {
        [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
        return;
    }
    shopDetailVC *vc = [[shopDetailVC alloc]init];
    vc.toUserID = minstr([self.commodityModel.shop_info valueForKey:@"uid"]);
    [[XGGAppDelegate sharedAppDelegate] pushViewController:vc animated:YES];

}
@end
