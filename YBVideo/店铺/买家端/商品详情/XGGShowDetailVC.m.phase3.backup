//
//  ShowDetailVC.m
//  live1v1
//
//  Created by ybRRR on 2019/8/2.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "ShowDetailVC.h"

#import <ZFPlayer/ZFPlayer.h>
#import <ZFPlayer/ZFAVPlayerManager.h>
#import <ZFPlayer/ZFIJKPlayerManager.h>
#import <ZFPlayer/ZFPlayerControlView.h>
#import "YBGoodPlayerCtrView.h"

@interface ShowDetailVC ()
{
    UIImageView *playVideoImg;
    UIImageView *_pauseIV;
}
@property (nonatomic, strong) ZFPlayerController *player;
@property(nonatomic,strong)YBGoodPlayerCtrView *controlView;

@end

@implementation ShowDetailVC

-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:YES];

    if (self.player) {
        self.player.viewControllerDisappear = YES;
    }
    
}
- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    if (self.player) {
        self.player.viewControllerDisappear = NO;
    }
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    self.navigationController.navigationBar.hidden = YES;
    UIButton *returnBtn = [UIButton buttonWithType:0];
    returnBtn.frame = CGRectMake(12, 22+statusbarHeight, 40, 40);
    [returnBtn setImage:[UIImage imageNamed:@"pub_back"] forState:0];
    [returnBtn addTarget:self action:@selector(returnBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    playVideoImg = [[UIImageView alloc]init];
    playVideoImg.frame = CGRectMake(0, 0, _window_width, _window_height);
    if (_thumbImg) {
        playVideoImg.image = _thumbImg;
    }

    [self.view addSubview:playVideoImg];
    [self.view sendSubviewToBack:playVideoImg];
    [self.view addSubview:returnBtn];

    NSURL *pathUrl = [NSURL URLWithString:self.videoPath];
    if (![self.videoPath containsString:@"http"]) {
        pathUrl = [NSURL fileURLWithPath:self.videoPath isDirectory:NO];
    }
    [self initPlayer];

}
-(void)returnBtnClick{
    [[XGGAppDelegate sharedAppDelegate]popViewController:YES];

}
-(void)deleteClick{
    UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"要删除此视频吗？") preferredStyle:UIAlertControllerStyleActionSheet];
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
    }];
    [alertContro addAction:cancleAction];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"删除") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        if (self.deleteEvent) {
            self.deleteEvent(@"视频");

        }
        [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
    }];
    [sureAction setValue:[UIColor redColor] forKey:@"_titleTextColor"];
    [alertContro addAction:sureAction];
    [self presentViewController:alertContro animated:YES completion:nil];

}


-(void)initPlayer {
    if (self.player) {
        return;
    }
    ZFAVPlayerManager *playerManager = [[ZFAVPlayerManager alloc] init];
//    playerManager.requestHeader = @{@"Referer":h5url};
    NSDictionary *header = @{@"Referer":h5url};
    NSDictionary *optiosDic = @{@"AVURLAssetHTTPHeaderFieldsKey" : header};
    [playerManager setRequestHeader:optiosDic];
    /// player的tag值必须在cell里设置
    self.player = [ZFPlayerController playerWithPlayerManager:playerManager containerView:playVideoImg];
    self.player.controlView = self.controlView;
    //[self.player setDisableGestureTypes:ZFPlayerDisableGestureTypesAll];
    self.player.currentPlayerManager.scalingMode = ZFPlayerScalingModeAspectFill;
    /// 竖屏的全屏
    self.player.orientationObserver.fullScreenMode = ZFFullScreenModePortrait;
    /*
    /// 隐藏全屏的状态栏
    self.player.orientationObserver.fullScreenStatusBarHidden = YES;
    self.player.orientationObserver.fullScreenStatusBarAnimation = UIStatusBarAnimationNone;
    /// 全屏的填充模式（全屏填充、按视频大小填充）
    self.player.orientationObserver.portraitFullScreenMode = ZFPortraitFullScreenModeScaleAspectFit;
    /// 禁用竖屏全屏的手势（点击、拖动手势）
    self.player.orientationObserver.disablePortraitGestureTypes = ZFDisablePortraitGestureTypesNone;
    */
    self.player.playerDisapperaPercent = 1.0;
    
    NSURL *pathUrl = [NSURL URLWithString:self.videoPath];
    if (![self.videoPath containsString:@"http"]) {
        pathUrl = [NSURL fileURLWithPath:self.videoPath isDirectory:NO];
    }
    self.player.assetURL = pathUrl;
    //功能
    self.player.playerPrepareToPlay = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSURL * _Nonnull assetURL) {
        NSLog(@"准备");
    };
    YBWeakSelf;
    self.player.playerDidToEnd = ^(id  _Nonnull asset) {
        NSLog(@"结束");
        [weakSelf.player.currentPlayerManager replay];
    };
    
    /*
    /// 封面信息
    [self.controlView showCoverViewWithUrl:_itemLayout.data.video_thumb];
    CGSize videoSize = CGSizeMake(_itemLayout.data.width, _itemLayout.data.height);
    self.player.currentPlayerManager.presentationSize = videoSize;
    */
}

- (YBGoodPlayerCtrView *)controlView {
    if (!_controlView) {
        _controlView = [YBGoodPlayerCtrView new];
    }
    return _controlView;
}




@end
