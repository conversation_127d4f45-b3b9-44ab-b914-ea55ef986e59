//
//  CommodityEvaluationCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityEvaluationCell.h"
#import "ShowDetailVC.h"
#import "YBImageView.h"
@implementation CommodityEvaluationCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _imgWidth =( _window_width-100-30)/4;
        [self creatUI];
    }
    return self;
}
-(void)creatUI{
    _headImg = [[UIImageView alloc]init];
    _headImg.frame = CGRectMake(15, 15, 24, 24);
    _headImg.layer.cornerRadius = 12;
    _headImg.layer.masksToBounds = YES;
    [self addSubview:_headImg];
    
    _nameLb = [[UILabel alloc]init];
    _nameLb.frame = CGRectMake(_headImg.right+10, 0, _window_width-_headImg.right-10, 20);
    _nameLb.centerY = _headImg.centerY;
    _nameLb.textColor = [UIColor grayColor];
    _nameLb.font = [UIFont systemFontOfSize:14];
    [self addSubview:_nameLb];
    
    _starView = [[CWStarRateView alloc]initWithFrame:CGRectMake(_nameLb.left, _headImg.bottom+5, 90, 15) numberOfStars:5 andSelImg:Comm_FOREGROUND_STAR_IMAGE_NAME andNormalImg:Comm_BACKGROUND_STAR_IMAGE_NAME];
    _starView.userInteractionEnabled = NO;
    _starView.delegate = self;
    [self addSubview:_starView];
    
    
    contentLb = [[UILabel alloc]init];
    contentLb.font = [UIFont systemFontOfSize:14];
    contentLb.numberOfLines = 0;
    contentLb.lineBreakMode = NSLineBreakByWordWrapping;
    contentLb.textColor = [UIColor blackColor];
    [self addSubview:contentLb];

}
-(void)setCommodityModel:(CommodityDetailModel *)model andListModel:(NSDictionary *)listDic{
    
    _model = model;
    
    NSString *quality_points = minstr([listDic valueForKey:@"quality_points"]);
    _starView.scorePercent = [quality_points floatValue]/5;
    NSLog(@"ssssss======:%f",_starView.scorePercent);
    [_headImg sd_setImageWithURL:[NSURL URLWithString:minstr([listDic valueForKey:@"avatar"])]];
    _nameLb.text = minstr([listDic valueForKey:@"user_nickname"]);
    
    NSString *content = minstr([listDic valueForKey:@"content"]);
    CGFloat content_height  = [PublicObj heightOfString:content andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-50];
    
    contentLb.frame  =CGRectMake(_headImg.right+10, _starView.bottom+5, _window_width-_headImg.right-30, content_height+5);
    contentLb.text = content;
    
    NSArray *imgeArr =[listDic valueForKey:@"thumb_format"];
    NSString *video_thumb =minstr([listDic valueForKey:@"video_thumb"]);
    NSString *video_url =minstr([listDic valueForKey:@"video_url"]);
    
    _allArr = [NSMutableArray array];
    CGFloat imgY = contentLb.bottom+10;;
    if (imgeArr.count > 0  && video_url.length > 0) {
        [_allArr addObject:video_thumb];
        [_allArr addObjectsFromArray:imgeArr];
        
        for (int i = 0; i < _allArr.count; i ++) {
            if (i < 3) {
                imgY =contentLb.bottom+10;
            }else{
                imgY =contentLb.bottom+20+_imgWidth;

            }
            UIImageView *img = [[UIImageView alloc]init];
            img.frame = CGRectMake(50+(i%3)*(_imgWidth + 10), imgY, _imgWidth, _imgWidth);
            [img sd_setImageWithURL:[NSURL URLWithString:_allArr[i]]];
            img.userInteractionEnabled = YES;
            [self addSubview:img];
            
            UIButton *tapBtn = [UIButton buttonWithType:0];
            tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
            tapBtn.tag =5000+i;
            [tapBtn addTarget:self action:@selector(detailBtnClick:) forControlEvents:UIControlEventTouchUpInside];

            NSDictionary *dataDic = @{@"video_url":video_url,@"imgeArr":imgeArr};
            objc_setAssociatedObject(tapBtn, @"tapBtn", dataDic, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

            [img addSubview:tapBtn];
            if (i == 0) {
                [tapBtn setImage:[UIImage imageNamed:@"commodity_播放"] forState:0];
            }
        }
    }else if (imgeArr.count < 1 && video_url.length > 0){
        [_allArr addObject:video_thumb];
        imgY =contentLb.bottom+10;
        
        UIImageView *img = [[UIImageView alloc]init];
        img.frame = CGRectMake(50, imgY, _imgWidth, _imgWidth);
        [img sd_setImageWithURL:[NSURL URLWithString:video_thumb]];
        img.userInteractionEnabled = YES;

        [self addSubview:img];
        
        UIButton *tapBtn = [UIButton buttonWithType:0];
        tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
        tapBtn.tag =5000;
        [tapBtn addTarget:self action:@selector(detailBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [img addSubview:tapBtn];
        [tapBtn setImage:[UIImage imageNamed:@"commodity_播放"] forState:0];

        NSDictionary *dataDic = @{@"video_url":video_url,@"imgeArr":@[]};
        objc_setAssociatedObject(tapBtn, @"tapBtn", dataDic, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

    }else if (imgeArr.count > 0 &&  video_url.length < 2){
        [_allArr addObjectsFromArray:imgeArr];
        for (int i = 0; i < _allArr.count; i ++) {
            if (i < 3) {
                imgY =contentLb.bottom+10;
            }else{
                imgY =contentLb.bottom+20+_imgWidth;

            }
            UIImageView *img = [[UIImageView alloc]init];
            img.frame = CGRectMake(50+(i%3)*(_imgWidth + 10), imgY, _imgWidth, _imgWidth);
            [img sd_setImageWithURL:[NSURL URLWithString:_allArr[i]]];
            img.userInteractionEnabled = YES;
            [self addSubview:img];
            
            UIButton *tapBtn = [UIButton buttonWithType:0];
            tapBtn.frame = CGRectMake(0, 0, img.width, img.height);
            tapBtn.tag =5001+i;
            [tapBtn addTarget:self action:@selector(detailBtnClick:) forControlEvents:UIControlEventTouchUpInside];
            [img addSubview:tapBtn];

            NSDictionary *dataDic = @{@"video_url":@"",@"imgeArr":imgeArr};
            objc_setAssociatedObject(tapBtn, @"tapBtn", dataDic, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

        }
    }
    
    if (_allArr.count > 0 && _allArr.count < 4) {
        imgY =contentLb.bottom+20+_imgWidth;
    }else if (_allArr.count >3)
    {
        imgY =contentLb.bottom+30+_imgWidth*2;

    }else if(_allArr.count == 0){
        imgY =contentLb.bottom+10;
    }
    UILabel *dateLb = [[UILabel alloc]init];
    dateLb.frame = CGRectMake(_headImg.right+10, imgY, 100, 20);
    dateLb.font = [UIFont systemFontOfSize:13];
    dateLb.textColor = [UIColor grayColor];
    dateLb.text =minstr([listDic valueForKey:@"time_format"]);
    [self addSubview:dateLb];
    
    UILabel *standLb = [[UILabel alloc]init];
    standLb.frame = CGRectMake(dateLb.right+10, imgY, 200, 20);
    standLb.font = [UIFont systemFontOfSize:13];
    standLb.textColor = [UIColor grayColor];
    standLb.adjustsFontSizeToFitWidth = YES;
    standLb.text =minstr([listDic valueForKey:@"spec_name"]);
    [self addSubview:standLb];
    
    UILabel *line = [[UILabel alloc]init];
    line.backgroundColor = RGB(240, 240, 240);
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(15);
        make.right.equalTo(self).offset(-15);
        make.bottom.equalTo(self);
        make.height.mas_equalTo(1);
    }];

}
- (void)starRateView:(CWStarRateView *)starRateView scroePercentDidChange:(CGFloat)newScorePercent{
    NSLog(@"%f",newScorePercent);
}

-(void)detailBtnClick:(UIButton *)sender{
    NSDictionary *dic = objc_getAssociatedObject(sender, @"tapBtn");

    if(sender.tag == 5000){
        ShowDetailVC *detail = [[ShowDetailVC alloc]init];
        detail.fromStr = @"trendlist";
        NSLog(@"=-=-=-=-=-=-=-:%@",minstr([dic valueForKey:@"video_url"]));
        detail.videoPath =minstr([dic valueForKey:@"video_url"]);
        detail.deleteEvent = ^(NSString *type) {
        };
        [[XGGAppDelegate sharedAppDelegate]pushViewController:detail animated:NO];
    }else{
        NSArray *imageArr = [dic valueForKey:@"imgeArr"];
        
        NSInteger index = sender.tag - 5001;
        YBImageView *imgView = [[YBImageView alloc] initWithImageArray:imageArr andIndex:index andMine:NO isDtCell:NO andBlock:^(NSArray * _Nonnull array) {
        }];
        [[UIApplication sharedApplication].keyWindow addSubview:imgView];

    }
}

@end
