//
//  CommodityEvaluationCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/27.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "CWStarRateView.h"
#import "CommodityDetailModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface CommodityEvaluationCell : UITableViewCell<CWStarRateViewDelegate>
{
    UILabel *contentLb;
}
@property (nonatomic, strong)UIImageView *headImg;
@property (nonatomic, strong)UILabel *nameLb;
@property (nonatomic, strong)CWStarRateView *starView;
@property (nonatomic, strong)NSMutableArray *allArr;
@property (nonatomic, assign)CGFloat imgWidth;

@property (nonatomic, strong)CommodityDetailModel *model;

-(void)setCommodityModel:(CommodityDetailModel *)model andListModel:(NSDictionary *)listDic;
@end

NS_ASSUME_NONNULL_END
