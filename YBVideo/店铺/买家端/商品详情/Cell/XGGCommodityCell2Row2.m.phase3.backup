//
//  CommodityCell2Row2.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityCell2Row2.h"

@implementation CommodityCell2Row2

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.serviceLb.text = YZMsg(@"服务");
    self.bondLb.text = YZMsg(@"已交保证金");
    self.guaranteeLb.text = YZMsg(@"资质保障");
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
