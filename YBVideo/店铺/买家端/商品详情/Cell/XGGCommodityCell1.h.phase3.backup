//
//  CommodityCell1.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "SDCycleScrollView.h"
#import "CommodityDetailModel.h"
#import "sliderCollectionView.h"

NS_ASSUME_NONNULL_BEGIN

@interface CommodityCell1 : UITableViewCell<SDCycleScrollViewDelegate>

{
    UILabel *statusLb;
    UILabel *priceLb;
    UILabel *commissionLb;

    UILabel *nameLb;
    UILabel *freightLb;
    UILabel *countLb;
    UILabel *addressLb;

    NSArray *infoarr;
    sliderCollectionView *sliderView;

}
@property (nonatomic,strong) SDCycleScrollView *cycleScroll;
@property (nonatomic, strong)CommodityDetailModel *model;
@end

NS_ASSUME_NONNULL_END
