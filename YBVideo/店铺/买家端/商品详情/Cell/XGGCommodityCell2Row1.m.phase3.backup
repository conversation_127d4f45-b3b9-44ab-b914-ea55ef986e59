//
//  CommodityCell2Row1.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityCell2Row1.h"

@implementation CommodityCell2Row1

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.seltitle.text = YZMsg(@"选择");
    self.selContent.text = YZMsg(@"请选择规格");
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
