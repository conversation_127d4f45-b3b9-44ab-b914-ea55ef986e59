//
//  CommodityCell1.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityCell1.h"

@implementation CommodityCell1

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self  = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        infoarr = [NSArray array];
        [self createUI];
    }
    return self;
}
-(void)createUI{
    if (!sliderView) {
        sliderView = [[sliderCollectionView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 375)];
        [self.contentView addSubview:sliderView];

    }
    statusLb = [[UILabel alloc]init];
    statusLb.frame = CGRectMake(_window_width-70, 15, 60, 26);
    statusLb.backgroundColor = RGBA(0, 0, 0, 0.37);
    statusLb.textColor = [UIColor whiteColor];
    statusLb.font = [UIFont systemFontOfSize:12];
    statusLb.layer.cornerRadius = 13;
    statusLb.layer.masksToBounds = YES;
    statusLb.text = YZMsg(@"已下架");
    statusLb.textAlignment = NSTextAlignmentCenter;
    statusLb.hidden = YES;
    [self.contentView addSubview:statusLb];
    
    priceLb = [[UILabel alloc]init];
    priceLb.frame = CGRectMake(15, sliderView.bottom+10, 80, 30);
    priceLb.font = [UIFont boldSystemFontOfSize:20];
    priceLb.textColor = Pink_Cor;
    priceLb.text = @"¥199.00";
    [self.contentView addSubview:priceLb];
    
    
    commissionLb = [[UILabel alloc]init];
    commissionLb.frame = CGRectMake(priceLb.right+5, sliderView.bottom+10, self.width-30, 30);
    commissionLb.font = [UIFont systemFontOfSize:16];
    commissionLb.textColor = Pink_Cor;
    commissionLb.hidden = YES;
    [self.contentView addSubview:commissionLb];

    
    nameLb = [[UILabel alloc]init];
    nameLb.frame = CGRectMake(15, priceLb.bottom+10, self.width-30, 20);
    nameLb.font = [UIFont systemFontOfSize:16];
    nameLb.textColor = [UIColor blackColor];
    nameLb.text = @"北京烤鸭";
    [self.contentView addSubview:nameLb];
    infoarr = @[YZMsg(@"运费:"),YZMsg(@"已售"),YZMsg(@"地点")];
    for (int i = 0; i < infoarr.count; i ++) {
        UILabel *lb = [[UILabel alloc]init];
        lb.frame = CGRectMake(15+i *(_window_width-26)/3, nameLb.bottom+10, (_window_width-26)/3,  20);
        lb.text = infoarr[i];
        lb.font = [UIFont systemFontOfSize:14];
        lb.textColor = [UIColor grayColor];
        lb.adjustsFontSizeToFitWidth = YES;
        [self addSubview:lb];
        if (i == 0) {
            freightLb = lb;
        }else if (i == 1) {
            lb.textAlignment = NSTextAlignmentCenter;
            countLb = lb;
        }else if (i == 2){
            lb.textAlignment = NSTextAlignmentRight;
            addressLb = lb;
        }
    }
}
#pragma mark ============轮播图点击=============
-(void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index{
}
-(void)setModel:(CommodityDetailModel *)model
{
    _model = model;
    if ([model.goods_status isEqual:@"-1"]) {
        statusLb.hidden = NO;
    }
    NSMutableArray *muaaa = [NSMutableArray array];
    BOOL isVideo;
    if (_model.video_url_format.length > 6) {
        [muaaa addObject:_model.video_url_format];
        isVideo = YES;
    }else{
        isVideo = NO;
    }
    [muaaa addObjectsFromArray:_model.thumbs_format];
    if(muaaa.count > 0){
        [sliderView reload:muaaa andIsvideo:isVideo];
    }
    sliderView.backScroll.showsHorizontalScrollIndicator = NO;
    [sliderView resumeGoodsVideo];
//    _cycleScroll.imageURLStringsGroup = model.pictures_format;
    priceLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), model.price] ;
    if ([model.is_sale_platform isEqual:@"1"]) {
        commissionLb.hidden = NO;
        commissionLb.text = [NSString stringWithFormat:YZMsg(@"佣 ¥%@"), model.commission];
    }
    nameLb.text = model.name;
    freightLb.text = [NSString stringWithFormat:YZMsg(@"运费:%@元"), model.postage];
    countLb.text = [NSString stringWithFormat:YZMsg(@"已售%@件"),model.sale_nums];
    addressLb.text = model.address_format;
}
@end
