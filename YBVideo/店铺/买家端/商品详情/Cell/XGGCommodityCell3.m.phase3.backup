//
//  CommodityCell3.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityCell3.h"
@implementation CommodityCell3

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self createUI];
    }
    return self;
}
-(void)createUI{
    _headImg = [[UIImageView alloc]init];
    _headImg.frame = CGRectMake(15, 10, 50, 50);
    _headImg.backgroundColor = [UIColor lightGrayColor];
    _headImg.layer.cornerRadius = 25;
    _headImg.layer.masksToBounds = YES;
    [self.contentView addSubview:_headImg];
    
    _nameLb = [[UILabel alloc]init];
    _nameLb.frame = CGRectMake(_headImg.right+10, 0, 150, 20);
    _nameLb.centerY = _headImg.centerY;
    _nameLb.textColor = [UIColor blackColor];
    _nameLb.font = [UIFont systemFontOfSize:14];
    
    [self.contentView addSubview:_nameLb];
    
    
    UIButton *goshopBtn = [UIButton buttonWithType:0];
    goshopBtn.frame = CGRectMake(_window_width-90, 0, 80, 18);
    goshopBtn.centerY = _headImg.centerY;
    [goshopBtn setTitleColor:Pink_Cor forState:0];
    [goshopBtn setTitle:YZMsg(@"进店逛逛") forState:0];
    [goshopBtn setImage:[UIImage imageNamed:@"shop_right_normal"] forState:0];
    goshopBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [goshopBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, - goshopBtn.imageView.image.size.width, 0, goshopBtn.imageView.image.size.width)];
    [goshopBtn setImageEdgeInsets:UIEdgeInsetsMake(0, goshopBtn.titleLabel.bounds.size.width, 0, -goshopBtn.titleLabel.bounds.size.width)];
    [goshopBtn addTarget:self action:@selector(btnClick) forControlEvents:UIControlEventTouchUpInside];

    [self.contentView addSubview:goshopBtn];

    _infoView = [[StoreInfoView alloc]initWithFrame:CGRectMake(15, _headImg.bottom +15, _window_width-30, 64)];
    [self addSubview:_infoView];
    
    
}
-(void)setModel:(CommodityDetailModel *)model
{
    _model = model;
    [_headImg sd_setImageWithURL:[NSURL URLWithString:_model.shop_avatar]];
    _nameLb.text = model.shop_name;
    _infoView.storeModels = model;
}

-(void)btnClick{
    [self.delegate goshopClick];
}
@end
