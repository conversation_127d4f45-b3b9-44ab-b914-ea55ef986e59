//
//  GoodsExplainCell.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import "GoodsExplainCell.h"

@implementation GoodsExplainCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
#pragma mark -- 商品简介webview
- (WKWebView *)webView{
    if (!_webView) {
        _webView = [[WKWebView alloc] initWithFrame:CGRectMake(0, 0, _window_width, _window_height-64-statusbarHeight-40-80)];
        _webView.navigationDelegate = self;
        _webView.opaque = NO;
        _webView.multipleTouchEnabled = YES;
        _webView.scrollView.delegate = self;
        _webView.scrollView.bounces = NO;
        _webView.scrollView.showsVerticalScrollIndicator = NO;
        _webView.scrollView.scrollEnabled = YES;
        _webView.scrollView.panGestureRecognizer.enabled = NO;
    }
    return _webView;
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        allHeight = 0;
        _imageArray = [NSMutableArray array];
        NSString * htmlStyle = @" <style type=\"text/css\"> *{min-width: 80% !important;max-width: 100% !important;} table{ width: 100% !important;} img{ height: auto !important;}  </style> ";
        NSString *aaa = @"<meta content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0\" name=\"viewport\">";
        htmlString = [aaa stringByAppendingString:htmlStyle];
        [self.contentView addSubview:self.webView];

    }
    return self;
}
-(void)setModel:(CommodityDetailModel *)model
{
    _model = model;
    //计算评价content
    CGFloat content_height  = [PublicObj heightOfString:model.content andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-32];

    content.frame = CGRectMake(16, 0, _window_width-32, content_height);
    content.text = model.content;
    
    _topMas = content.mas_bottom;
    _spacess = content_height+15;
    
    NSArray *array = model.pictures_format;
    NSString * htmlStyle22 = @" <style type=\"text/css\">body{font-size:30} </style> ";

    NSString *htmlStr = [NSString stringWithFormat:@"%@<p>%@</p>",htmlStyle22,model.content];

    for (NSString *str in array) {
        NSString *ssss = [NSString stringWithFormat:@"<img src='%@'></img>",str];
        htmlStr = [NSString stringWithFormat:@"%@%@",htmlStr,ssss];
    }
    NSString * htmlStyle = @" <style type=\"text/css\"> *{min-width: 100% !important;max-width: 100% !important;} img{ height: auto !important;}  </style> ";
    htmlStr = [htmlStyle stringByAppendingString:htmlStr];
    [_webView loadHTMLString:htmlStr baseURL:nil];
}
- (void)webView:(WKWebView *)webView didFinishNavigation:(null_unspecified WKNavigation *)navigation{
    [webView evaluateJavaScript:@"document.body.scrollHeight" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
//        _webView.frame = CGRectMake(0, 0, _window_width, [result doubleValue]);//将WKWebView的高度设置为内容高度
//        //刷新制定位置Cell
//        _backScrollView.contentSize = CGSizeMake(0, _webView.bottom);
    }];
}
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler{
    
    NSURLRequest *request = navigationAction.request;
    if ([request.URL.scheme isEqualToString: @"image-preview"])
    {
        
        NSString *url = [request.URL.absoluteString substringFromIndex:14];
        //启动图片浏览器， 跳转到图片浏览页面
        if (_imageArray.count != 0) {
            
//            HZPhotoBrowser *browserVc = [[HZPhotoBrowser alloc] init];
//            browserVc.imageArray = imageArray;
//            browserVc.imageCount = imageArray.count; // 图片总数
//            browserVc.currentImageIndex = (int)[imageArray indexOfObject:url];//当前点击的图片
//            [browserVc show];
            
        }
        decisionHandler(WKNavigationActionPolicyAllow);
        return;
        
    }

    decisionHandler(WKNavigationActionPolicyAllow);
    NSLog(@"在发送请求之前：%@",navigationAction.request.URL.absoluteString);
}
@end
