//
//  GuaranteeView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/11.
//  Copyright © 2020 cat. All rights reserved.
//

#import "GuaranteeView.h"

@implementation GuaranteeView

-(void)awakeFromNib
{
    [super awakeFromNib];
    
    UITapGestureRecognizer *taps = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(finishClick:)];
    taps.delegate = self;
    [self addGestureRecognizer:taps];
    
    self.backView.size = CGSizeMake(_window_width, 290);
    
    self.titleLb.text = YZMsg(@"服务保障");
    self.bondTitleLb.text = YZMsg(@"已交保证金");
    self.bondContent.text = YZMsg(@"商家已向平台缴纳保证金，交易产生纠纷时用于保证买家的权益");
    self.guaranteeTitle.text = YZMsg(@"资质保障");
    self.guaranteeContent.text = YZMsg(@"商家已向平台提交经营执照、许可资质等相关资质证明");
    [self.sureBtn setTitle:YZMsg(@"完成") forState:0];
    _sureBtnBot.constant = 10+ShowDiff;
    [_backView layoutIfNeeded];
    
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.backView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = self.backView.bounds;
    maskLayer.path = maskPath.CGPath;
    self.backView.layer.mask = maskLayer;
    

}
- (IBAction)finishClick:(id)sender {
    if (self.hideEvent) {
        self.hideEvent();
    }

}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    if ([NSStringFromClass([touch.view class]) isEqual:@"UIView"]) {
        return NO;
    }
    return YES;
}

@end
