//
//  GuaranteeView.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/11.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef void(^hideSelfEvent)();
@interface GuaranteeView : UIView<UIGestureRecognizerDelegate>
@property (weak, nonatomic) IBOutlet UIView *backView;
@property (strong, nonatomic) IBOutlet UILabel *titleLb;
@property (strong, nonatomic) IBOutlet UILabel *bondTitleLb;
@property (strong, nonatomic) IBOutlet UILabel *bondContent;
@property (strong, nonatomic) IBOutlet UILabel *guaranteeTitle;
@property (strong, nonatomic) IBOutlet UILabel *guaranteeContent;
@property (strong, nonatomic) IBOutlet UIButton *sureBtn;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *sureBtnBot;


@property (nonatomic, copy)hideSelfEvent hideEvent;
@end

NS_ASSUME_NONNULL_END
