//
//  CommodityDetailModel.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/10.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CommodityDetailModel : NSObject

@property(nonatomic, strong)NSArray *comment_lists;
@property(nonatomic, assign)CGFloat commentCellHeight;
@property(nonatomic, strong)NSDictionary *comment_dic;
//@property(nonatomic, strong)NSString *comment_avatar;
@property(nonatomic, strong)NSString *comment_content;
//@property(nonatomic, strong)NSString *comment_user_nickname;
//@property(nonatomic, strong)NSString *comment_addtime;

@property(nonatomic, strong)NSDictionary *goods_info;
@property(nonatomic, strong)NSArray *pictures_format;
@property(nonatomic, strong)NSString *name;
@property(nonatomic, strong)NSString *comment_nums;
@property(nonatomic, strong)NSString *one_classid;//一级id
@property(nonatomic, strong)NSString *two_classid;//二级id
@property(nonatomic, strong)NSString *three_classid;//三级id
@property(nonatomic, strong)NSString *three_class_name;//三级名字
@property(nonatomic, strong)NSString *content;
@property(nonatomic, strong)NSString *price;//价格
@property(nonatomic, strong)NSString *commission;//佣金
@property(nonatomic, strong)NSString *is_sale_platform;//是否显示佣金
@property(nonatomic, strong)NSString *postage;//运费
@property(nonatomic, strong)NSString *sale_nums;//已售
@property(nonatomic, strong)NSString *address_format;//地址
@property(nonatomic, strong)NSArray *specs_format;//规格
@property(nonatomic, strong)NSDictionary *shop_info;//店铺信息
@property(nonatomic, strong)NSString *shop_uid;//账号
@property(nonatomic, strong)NSString *shop_avatar;//头像
@property(nonatomic, strong)NSString *shop_name;//名字
@property(nonatomic, strong)NSString *shop_usernicename;//名字
@property(nonatomic, strong)NSString *goods_status;

@property(nonatomic, strong)NSString *shop_sale_nums;//店铺销量
@property(nonatomic, strong)NSString *quality_points;//商品质量
@property(nonatomic, strong)NSString *service_points;//服务态度
@property(nonatomic, strong)NSString *express_points;//物流服务
@property(nonatomic, strong)NSArray  *thumbs_format;//轮播图图片数组
@property(nonatomic, strong)NSString *video_url_format;//轮播视频
@property(nonatomic, strong)NSString *video_url;
@property(nonatomic, strong)NSString  *video_thumb_format;
@property(nonatomic, strong)NSString  *video_thumb;
@property(nonatomic, strong)NSString  *isattention;
@property(nonatomic, strong)NSString *other_sale_nums;//代卖商品数量

@property(nonatomic, strong)NSString  *original_price;//外链商品原价
@property(nonatomic, strong)NSString  *present_price;//外链商品现价
@property(nonatomic, strong)NSString  *goods_desc;//外链商品简介
@property(nonatomic, strong)NSString  *href; //外链商品链接地址
@property(nonatomic, strong)NSString  *type; // 1外链商品 0 站内商品

@property(nonatomic, strong)NSString  *goodsid;
@property(nonatomic, strong)NSString  *iscollect;

+(instancetype)modelWithDic:(NSDictionary *)subdic;
@end

NS_ASSUME_NONNULL_END
