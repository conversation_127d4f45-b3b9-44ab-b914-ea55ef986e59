//
//  CommodityDetailModel.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/10.
//  Copyright © 2020 cat. All rights reserved.
//

#import "CommodityDetailModel.h"

@implementation CommodityDetailModel

-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        self.comment_lists = [dic valueForKey:@"comment_lists"];
//        self.comment_avatar = minstr([self.comment_lists valueForKey:@"avatar"]);
//        self.comment_content = minstr([self.comment_dic valueForKey:@"content"]);
//        self.comment_user_nickname = minstr([self.comment_lists valueForKey:@"user_nickname"]);
//        self.comment_addtime= minstr([self.comment_lists valueForKey:@"addtime"]);
        
//        CGFloat content_height  = [PublicObj heightOfString:self.comment_content andFont:[UIFont systemFontOfSize:14] andWidth:_window_width-50];
////
//        self.commentCellHeight = content_height + 60;
        
        self.goods_info = [dic valueForKey:@"goods_info"];
        self.comment_nums = minstr([self.goods_info valueForKey:@"comment_nums"]);
        self.pictures_format = [[dic valueForKey:@"goods_info"] valueForKey:@"pictures_format"];
        self.name =minstr([[dic valueForKey:@"goods_info"] valueForKey:@"name"]);
        self.goodsid =  minstr([self.goods_info valueForKey:@"id"]);
        self.iscollect = minstr([self.goods_info valueForKey:@"iscollect"]);
        self.price =minstr([[[[dic valueForKey:@"goods_info"] valueForKey:@"specs_format"] firstObject] valueForKey:@"price"]);
        self.commission = minstr([self.goods_info valueForKey:@"commission"]);
        self.is_sale_platform = minstr([self.goods_info valueForKey:@"is_sale_platform"]);
        self.postage = minstr([[dic valueForKey:@"goods_info"] valueForKey:@"postage"]);
        self.sale_nums = minstr([[dic valueForKey:@"goods_info"] valueForKey:@"sale_nums"]);
        self.other_sale_nums =  minstr([[dic valueForKey:@"goods_info"] valueForKey:@"platform_goods_nums"]);
        self.address_format =minstr([[dic valueForKey:@"shop_info"] valueForKey:@"address_format"]);
        self.specs_format = [[dic valueForKey:@"goods_info"] valueForKey:@"specs_format"];
        self.three_class_name = minstr([self.goods_info valueForKey:@"three_class_name"]);
        self.one_classid = minstr([self.goods_info valueForKey:@"one_classid"]);//一级id
        self.two_classid= minstr([self.goods_info valueForKey:@"two_classid"]);//二级id
        self.three_classid= minstr([self.goods_info valueForKey:@"three_classid"]);//三级id
        self.content = minstr([self.goods_info valueForKey:@"content"]);
        self.goods_status = minstr([self.goods_info valueForKey:@"status"]);
        
        self.shop_info = [dic valueForKey:@"shop_info"];
        self.shop_uid = minstr([self.shop_info valueForKey:@"uid"]);
        self.shop_avatar = minstr([[dic valueForKey:@"shop_info"] valueForKey:@"avatar"]);
        self.shop_name = minstr([[dic valueForKey:@"shop_info"] valueForKey:@"name"]);
        self.shop_sale_nums = minstr([[dic valueForKey:@"shop_info"] valueForKey:@"sale_nums"]);
        self.quality_points = minstr([[dic valueForKey:@"shop_info"] valueForKey:@"quality_points"]);
        self.service_points = minstr([[dic valueForKey:@"shop_info"] valueForKey:@"service_points"]);
        self.express_points = minstr([[dic valueForKey:@"shop_info"] valueForKey:@"express_points"]);
        self.isattention = minstr([self.shop_info valueForKey:@"isattention"]);
        self.shop_usernicename =minstr([[dic valueForKey:@"shop_info"] valueForKey:@"user_nickname"]);
        
        self.thumbs_format = [self.goods_info valueForKey:@"thumbs_format"];
        self.video_url_format = minstr([self.goods_info valueForKey:@"video_url_format"]);
        self.video_thumb_format = minstr([self.goods_info valueForKey:@"video_thumb_format"]);
        self.video_url =minstr([self.goods_info valueForKey:@"video_url"]);
        self.video_thumb =minstr([self.goods_info valueForKey:@"video_thumb"]);
        self.original_price = minstr([self.goods_info valueForKey:@"original_price"]);
        self.present_price = minstr([self.goods_info valueForKey:@"present_price"]);
        self.goods_desc = minstr([self.goods_info valueForKey:@"goods_desc"]);
        self.type = minstr([self.goods_info valueForKey:@"type"]);
        self.href = minstr([self.goods_info valueForKey:@"href"]);

    }
    return self;
}


+(instancetype)modelWithDic:(NSDictionary *)subdic{
    CommodityDetailModel *model = [[CommodityDetailModel alloc]initWithDic:subdic];
    return model;
}

@end
