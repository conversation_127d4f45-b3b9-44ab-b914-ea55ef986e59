//
//  sliderCollectionView.m
//  yunbaolive
//
//  Created by IOS1 on 2019/3/23.
//  Copyright © 2019 cat. All rights reserved.
//

#import "sliderCollectionView.h"
#import "sliderCollectionVCell.h"

#import <ZFPlayer/ZFPlayer.h>
#import <ZFPlayer/ZFAVPlayerManager.h>
#import <ZFPlayer/ZFIJKPlayerManager.h>
#import <ZFPlayer/ZFPlayerControlView.h>
#import "YBGoodPlayerCtrView.h"

@interface sliderCollectionView()

@property (nonatomic, strong) ZFPlayerController *player;
@property(nonatomic,strong)YBGoodPlayerCtrView *controlView;

@end

@implementation sliderCollectionView{
    UICollectionView *collectionV;
    NSArray *listArray;
    BOOL isVideo;
    
    UIImageView *videoBackImg;
    UIButton *pauseBtn;
    UIButton *muteBtn;
    
    BOOL isPause;
    NSString *_videoUrl;
}


-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self) {
        isPause = NO;
        [self creatUI];
        [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(stopGoodsVideo) name:@"stopGoodsVideo" object:nil];
    }
    return self;
}

-(void)creatUI{
    _backScroll = [[UIScrollView alloc]init];
    _backScroll.frame = CGRectMake(0, 0, _window_width, self.height);
    _backScroll.pagingEnabled = YES;
    _backScroll.delegate = self;
    [self addSubview:_backScroll];
    
    countLb = [[UILabel alloc]init];
    countLb.frame = CGRectMake(_window_width-55, self.height-55, 40, 22);
    countLb.backgroundColor = RGBA(5, 3, 3, 0.4);
    countLb.font = [UIFont systemFontOfSize:12];
    countLb.textColor = [UIColor whiteColor];
    countLb.textAlignment = NSTextAlignmentCenter;
    countLb.layer.cornerRadius = 11;
    countLb.layer.masksToBounds = YES;
    countLb.hidden = YES;
    [self addSubview:countLb];
}
- (void)reload:(NSArray *)array andIsvideo:(BOOL)video{
    listArray = array;
    isVideo = video;
    _backScroll.contentSize = CGSizeMake(_window_width *listArray.count, self.height);
    for (int i = 0; i < listArray.count; i ++) {
        UIImageView *img = [[UIImageView alloc]init];
        img.frame = CGRectMake(i * _window_width, 0, _window_width, self.height);
        img.contentMode = UIViewContentModeScaleAspectFill;
        img.clipsToBounds = YES;
        img.backgroundColor = [UIColor blackColor];
        [_backScroll addSubview:img];
        if (isVideo) {
            if (i == 0) {
                videoBackImg = img;
                _videoUrl = minstr(listArray[i]);
                [self initPlayer];
                [_controlView ctrVideoPlay:YES];
                //_controlView会打断_backScroll的滚动,这里利用遮罩避免问题
                UIButton *tapbtn = [UIButton buttonWithType:0];
                tapbtn.frame =CGRectMake(0, 0, videoBackImg.width, videoBackImg.height);
                [tapbtn addTarget:self action:@selector(pauseBtnClick) forControlEvents:UIControlEventTouchUpInside];
                [videoBackImg addSubview:tapbtn];
                
                muteBtn = [UIButton buttonWithType:0];
                muteBtn.frame = CGRectMake(_window_width-55, self.height-55-45, 36, 36);
                [muteBtn setImage:[UIImage imageNamed:@"mute_normal"] forState:UIControlStateNormal];
                [muteBtn setImage:[UIImage imageNamed:@"mute_sel"] forState:UIControlStateSelected];
                [muteBtn addTarget:self action:@selector(muteBtnClick) forControlEvents:UIControlEventTouchUpInside];
                [videoBackImg addSubview:muteBtn];
                muteBtn.hidden = NO;
                muteBtn.selected = NO;
            }else{
                [img sd_setImageWithURL:[NSURL URLWithString:minstr(listArray[i])]];
            }
        }else{

            [img sd_setImageWithURL:[NSURL URLWithString:minstr(listArray[i])]];
        }
    }
    if (listArray.count > 1) {
        countLb.hidden = NO;
    }
    countLb.text = [NSString stringWithFormat:@"1/%ld", listArray.count];

}
//点击暂停
-(void)pauseBtnClick{
    isPause = !isPause;
    if (isPause) {
        [_controlView ctrVideoPlay:NO];
        muteBtn.hidden = YES;
    }else{
        [_controlView ctrVideoPlay:YES];
        muteBtn.hidden = NO;
    }
}
-(void)muteBtnClick{
    muteBtn.selected = !muteBtn.selected;
    if (muteBtn.selected) {
        [self.player.currentPlayerManager setMuted:YES];
    }else{
        [self.player.currentPlayerManager setMuted:NO];
    }
}


- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    _page = scrollView.contentOffset.x/_window_width;
    if (isVideo) {
        if (_page == 0) {
            [self resumeGoodsVideo];
        }else{
            isPause = YES;
            if (isPause) {
                [_controlView ctrVideoPlay:NO];
            }
        }
    }
    countLb.text = [NSString stringWithFormat:@"%d/%ld",_page+1, listArray.count];
}
-(void)stopGoodsVideo{
    isPause = YES;
    if (isPause) {
        [_controlView ctrVideoPlay:NO];
    }
}
-(void)resumeGoodsVideo{
    if (isVideo) {
        isPause = NO;
        [_controlView ctrVideoPlay:YES];
        muteBtn.hidden = NO;
    }
}

-(void)initPlayer {
    if (self.player) {
        return;
    }
    ZFAVPlayerManager *playerManager = [[ZFAVPlayerManager alloc] init];
//    playerManager.requestHeader = @{@"Referer":h5url};
    NSDictionary *header = @{@"Referer":h5url};
    NSDictionary *optiosDic = @{@"AVURLAssetHTTPHeaderFieldsKey" : header};
    [playerManager setRequestHeader:optiosDic];
    /// player的tag值必须在cell里设置
    self.player = [ZFPlayerController playerWithPlayerManager:playerManager containerView:videoBackImg];
    self.player.controlView = self.controlView;
    //[self.player setDisableGestureTypes:ZFPlayerDisableGestureTypesAll];
    self.player.currentPlayerManager.scalingMode = ZFPlayerScalingModeAspectFill;
    /// 竖屏的全屏
    self.player.orientationObserver.fullScreenMode = ZFFullScreenModePortrait;
    /*
    /// 隐藏全屏的状态栏
    self.player.orientationObserver.fullScreenStatusBarHidden = YES;
    self.player.orientationObserver.fullScreenStatusBarAnimation = UIStatusBarAnimationNone;
    /// 全屏的填充模式（全屏填充、按视频大小填充）
    self.player.orientationObserver.portraitFullScreenMode = ZFPortraitFullScreenModeScaleAspectFit;
    /// 禁用竖屏全屏的手势（点击、拖动手势）
    self.player.orientationObserver.disablePortraitGestureTypes = ZFDisablePortraitGestureTypesNone;
    */
    self.player.playerDisapperaPercent = 1.0;
    
    NSURL *pathUrl = [NSURL URLWithString:_videoUrl];
    self.player.assetURL = pathUrl;
    //功能
    self.player.playerPrepareToPlay = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSURL * _Nonnull assetURL) {
        NSLog(@"准备");
    };
    YBWeakSelf;
    self.player.playerDidToEnd = ^(id  _Nonnull asset) {
        NSLog(@"结束");
        [weakSelf.player.currentPlayerManager replay];
    };
    
    /*
    /// 封面信息
    [self.controlView showCoverViewWithUrl:_itemLayout.data.video_thumb];
    CGSize videoSize = CGSizeMake(_itemLayout.data.width, _itemLayout.data.height);
    self.player.currentPlayerManager.presentationSize = videoSize;
    */
}

- (YBGoodPlayerCtrView *)controlView {
    if (!_controlView) {
        _controlView = [YBGoodPlayerCtrView new];
        YBWeakSelf;
        _controlView.ctrEvent = ^{
            [weakSelf pauseBtnClick];
        };
    }
    return _controlView;
}





@end
