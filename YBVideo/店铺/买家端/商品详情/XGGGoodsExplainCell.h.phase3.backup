//
//  GoodsExplainCell.h
//  yunbaolive
//
//  Created by ybRRR on 2020/3/28.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "CommodityDetailModel.h"
#import <WebKit/WebKit.h>
@interface GoodsExplainCell : UITableViewCell<WKNavigationDelegate,UIScrollViewDelegate>
{
    UIScrollView *_backScroll;
    UILabel *content;
    
    MASViewAttribute *_topMas;
    CGFloat _spacess;
    CGFloat allHeight;
    
    NSString *htmlString;
}

@property (nonatomic, strong)CommodityDetailModel *model;
@property (nonatomic,strong) WKWebView *webView;
@property (nonatomic,strong) NSMutableArray *imageArray;
@end
