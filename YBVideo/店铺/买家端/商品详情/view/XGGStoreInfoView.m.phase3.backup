//
//  StoreInfoView.m
//  yunbaolive
//
//  Created by ybRRR on 2020/3/3.
//  Copyright © 2020 cat. All rights reserved.
//

#import "StoreInfoView.h"

@implementation StoreInfoView
-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = RGB(250, 250, 250);
        [self creatUI];
    }
    return self;
}

-(void)creatUI{
    NSArray *data = @[@"1.1万件",@"4.8 高",@"4.8 高",@"4.8 高"];
    NSArray *arr = @[YZMsg(@"总销量"),YZMsg(@"商品质量"),YZMsg(@"服务态度"),YZMsg(@"物流服务")];
    for (int i = 0; i < arr.count; i ++) {
        UILabel *lb = [[UILabel alloc]init];
        lb.frame = CGRectMake(i * (self.width/4), self.height/2-20, (self.width/4), 20);
        lb.textAlignment = NSTextAlignmentCenter;
        lb.font = [UIFont boldSystemFontOfSize:13];
        lb.text = data[i];
        [self addSubview:lb];
        
        UILabel *title = [[UILabel alloc]init];
        title.frame = CGRectMake(i * (self.width/4), self.height/2, (self.width/4), 20);
        title.textAlignment = NSTextAlignmentCenter;
        title.text = arr[i];
        title.textColor = [UIColor grayColor];
        title.font = [UIFont systemFontOfSize:13];
        [self addSubview:title];
        
        if (i < 2) {
            lb.textColor = [UIColor darkGrayColor];
        }else{
            lb.textColor = [UIColor darkGrayColor];
        }
        if (i == 0) {
            numsLb = lb;
        }else if (i == 1){
            qualityLb = lb;
        }else if (i == 2){
            serviceLb = lb;
        }else if (i == 3){
            expressLb = lb;
        }
    }
    [PublicObj lineViewWithFrame:CGRectMake(self.width/4, self.height/2-10, 1, 20) andColor:[UIColor lightGrayColor] andView:self];

}
-(void)setStoreModels:(CommodityDetailModel *)storeModels
{
    numsLb.text = [NSString stringWithFormat:@"%@%@",storeModels.shop_sale_nums,YZMsg(@"件")];
    qualityLb.text = storeModels.quality_points;
    serviceLb.text = storeModels.service_points;
    expressLb.text = storeModels.express_points;
}
@end
