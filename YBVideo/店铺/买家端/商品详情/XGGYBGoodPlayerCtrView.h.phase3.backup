//
//  YBGoodPlayerCtrView.h
//  YBVideo
//
//  Created by YB007 on 2022/6/24.
//  Copyright © 2022 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <ZFPlayer/ZFPlayer.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^PlayerCtrBlock)(void);

@interface YBGoodPlayerCtrView : UIView <ZFPlayerMediaControl>

@property(nonatomic,copy)PlayerCtrBlock ctrEvent;

- (void)showCoverViewWithUrl:(NSString *)coverUrl;
-(void)ctrVideoPlay:(BOOL)isPlay;

@end

NS_ASSUME_NONNULL_END
