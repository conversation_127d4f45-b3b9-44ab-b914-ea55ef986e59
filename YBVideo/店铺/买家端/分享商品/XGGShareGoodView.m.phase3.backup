//
//  ShareGoodView.m
//  yunbaolive
//
//  Created by ybRRR on 2021/6/8.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ShareGoodView.h"
#import "CommodityDetailVC.h"
@implementation ShareGoodView
{
    UIImageView *thumbImg;
    UILabel *priceLb;
    UILabel *countLb;
    UILabel *titleLb;
}

-(instancetype)initWithFrame:(CGRect)frame andGoodsData:(CommodityDetailModel *)models{
    self = [super initWithFrame:frame];
    if (self) {
        _goodsModel = models;

        UITapGestureRecognizer *taps = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(goGoodsDetail)];
        [self addGestureRecognizer:taps];
        
        UIView *backView = [[UIView alloc]init];
        backView.frame = CGRectMake(10, 5, self.height *0.7, self.height-10);
        backView.backgroundColor = UIColor.whiteColor;
        backView.layer.cornerRadius = 10;
        backView.layer.masksToBounds = YES;
        backView.layer.borderColor = Line_Cor.CGColor;
        backView.layer.borderWidth = 1;
        [self addSubview:backView];
        
        thumbImg = [[UIImageView alloc]init];
        thumbImg.frame = CGRectMake(0, 0, backView.width, backView.height *0.6);
        NSArray *first = models.thumbs_format;
        if (first.count > 0) {
            [thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr(first[0])]];

        }
//        [thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([first valueForKey:@"thumb"])]];
        thumbImg.contentMode = UIViewContentModeScaleAspectFill;
        thumbImg.clipsToBounds = YES;
        [backView addSubview:thumbImg];
        
        priceLb = [[UILabel alloc]init];
        priceLb.font = [UIFont systemFontOfSize:16];
        priceLb.textColor = Pink_Cor;
        if ([models.type isEqual:@"1"]) {
            priceLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), models.present_price];
        }else{
            priceLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), models.price] ;
        }
        [backView addSubview:priceLb];
        [priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(backView).offset(10);
            make.top.equalTo(thumbImg.mas_bottom).offset(5);
        }];
        
        _getMoneyLb = [[UILabel alloc]init];
        _getMoneyLb.font = [UIFont systemFontOfSize:11];
        _getMoneyLb.backgroundColor = RGBA(255, 88, 120, 0.2);
        _getMoneyLb.textColor =Normal_Color;
        _getMoneyLb.layer.cornerRadius = 3;
        _getMoneyLb.layer.borderColor = Normal_Color.CGColor;
        _getMoneyLb.layer.borderWidth = 1;
        _getMoneyLb.layer.masksToBounds = YES;
        _getMoneyLb.text = [NSString stringWithFormat:@" 被跟买得%@元 ",@"1.02"];
        _getMoneyLb.hidden = YES;
        [backView addSubview:_getMoneyLb];
        [_getMoneyLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(priceLb.mas_right).offset(5);
            make.centerY.equalTo(priceLb.mas_centerY);
            make.height.mas_equalTo(15);
            
        }];
        
        if ([models.shop_uid isEqual:[Config getOwnID]]) {
            _getMoneyLb.hidden = NO;
            _getMoneyLb.text = [NSString stringWithFormat:YZMsg(@" 被跟买得%@元 "),models.commission];

        }else{
            _getMoneyLb.hidden = YES;
        }
        
        titleLb = [[UILabel alloc]init];
        titleLb.font = [UIFont systemFontOfSize:14];
        titleLb.textColor = UIColor.blackColor;
        titleLb.text = models.name;
        titleLb.numberOfLines = 0;
        titleLb.lineBreakMode = NSLineBreakByWordWrapping;
        [backView addSubview:titleLb];
        [titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(priceLb.mas_left);
            make.top.equalTo(priceLb.mas_bottom).offset(5);
            make.right.equalTo(backView.mas_right).offset(-10);
        }];
        countLb = [[UILabel alloc]init];
        countLb.font = [UIFont systemFontOfSize:14];
        countLb.textColor = UIColor.grayColor;
        countLb.text =[NSString stringWithFormat:YZMsg(@"已售%@件"),models.sale_nums];
        countLb.numberOfLines = 0;
        countLb.lineBreakMode = NSLineBreakByWordWrapping;
        [backView addSubview:countLb];
        [countLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(titleLb);
            make.top.equalTo(titleLb.mas_bottom).offset(5);
        }];
    }
    return self;
}
-(void)setGoodsModel:(CommodityDetailModel *)goodsModel
{
    _goodsModel = goodsModel;
    NSArray *first = goodsModel.thumbs_format;
    if (first.count > 0) {
        [thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr(first[0])]];

    }
//    priceLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), goodsModel.price] ;
    if ([goodsModel.type isEqual:@"1"]) {
        priceLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), goodsModel.present_price];
    }else{
        priceLb.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"¥"), goodsModel.price] ;
    }

    countLb.text =[NSString stringWithFormat:YZMsg(@"已售%@件"),goodsModel.sale_nums];
    titleLb.text = goodsModel.name;
//    _getMoneyLb.text = [NSString stringWithFormat:@" 被跟买得%@元 ",@"1.02"];
    _getMoneyLb.hidden = YES;

}
-(void)goGoodsDetail{
//    [PublicObj checkGoodsExistenceWithID:_goodsModel.goodsid Existence:^(NSString *code, NSString *msg) {
//        if ([code isEqual:@"0"]) {
            CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
            detail.goodsID = _goodsModel.goodsid;
            detail.liveUid =_goodsModel.shop_uid;
//            detail.shareuid = self.shareUserid;
            [[XGGAppDelegate sharedAppDelegate] pushViewController:detail animated:YES];
//        }else{
//            [MBProgressHUD showError:msg];
//
//        }
//    }];

}
@end
