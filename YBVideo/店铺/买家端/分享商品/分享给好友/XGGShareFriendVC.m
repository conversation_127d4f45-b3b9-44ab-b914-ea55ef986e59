//
//  ShareFriendVC.m
//  yunbaolive
//
//  Created by ybRRR on 2021/6/7.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ShareFriendVC.h"
#import "ShareFriendCell.h"
#import "FriendModel.h"

@interface ShareFriendVC ()<UITableViewDelegate,UITableViewDataSource>
{
    NSMutableArray *selArr;
    NSMutableArray *farr;
    int pageindex;
    
    int userIndex;
}
@property (nonatomic, strong)UITableView *listTable;
@property (nonatomic, strong)NSArray *modelArr;
@end

@implementation ShareFriendVC
-(void)requestData{
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"touid":[Config getOwnID],
                          @"p":@(pageindex)
                          };

    [YBNetworking postWithUrl:@"User.getFollowsList" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code == 0) {
            NSLog(@"data----:%@",info);
            NSArray *infos = info;
            if (pageindex == 1) {
                [farr removeAllObjects];
            }
            [farr addObjectsFromArray:infos];
            [self.listTable reloadData];
            }
        } Fail:^(id fail) {
            
        }];
    
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"分享给好友");
    [self.leftBtn setImage:nil forState:0];
    [self.leftBtn setTitleColor:UIColor.whiteColor forState:0];
    [self.leftBtn setTitle:YZMsg(@"取消") forState:0];
    self.leftBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    selArr = [NSMutableArray array];
    farr = [NSMutableArray array];
    pageindex = 1;
    userIndex = 0;
    [self.view addSubview:self.listTable];
    [self createBottomBtn];
    
    [self requestData];
}
-(NSArray *)modelArr{
    NSMutableArray *array = [NSMutableArray array];
    
    for (NSDictionary *dic in farr) {
        FriendModel *model = [FriendModel modelWithDic:dic];
        [array addObject:model];
    }
    _modelArr = array;
    return _modelArr;
}
-(void)createBottomBtn{
    UIButton *shareBtn =[UIButton buttonWithType:0];
    shareBtn.frame = CGRectMake(15, _window_height-60, _window_width-30, 44);
    shareBtn.layer.cornerRadius = 22;
    shareBtn.layer.masksToBounds = YES;
    [shareBtn setTitle:YZMsg(@"分享") forState:0];
    [shareBtn setTitleColor:[UIColor whiteColor] forState:0];
    shareBtn.titleLabel.font = [UIFont systemFontOfSize:16];
    [shareBtn setBackgroundColor:Pink_Cor];
    [shareBtn addTarget:self action:@selector(shareBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:shareBtn];
}

-(UITableView *)listTable{
    if (!_listTable) {
        _listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-60) style:UITableViewStylePlain];
        _listTable.delegate = self;
        _listTable.dataSource = self;
        _listTable.separatorStyle = 0;
        _listTable.backgroundColor = Normal_SubColor;
    }
    return _listTable;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.modelArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 75;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    ShareFriendCell *cell = [ShareFriendCell cellWithTableView:tableView];
    
    FriendModel *allmodel =self.modelArr[indexPath.row];
    for (FriendModel *model in selArr) {
        if ([model.idStr isEqual:allmodel.idStr]) {
            allmodel.gc_isshow = @"0";
        }
    }
    cell.model =allmodel;

    return cell;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.listTable deselectRowAtIndexPath:indexPath animated:YES];

    FriendModel *model = self.modelArr[indexPath.row];
     __block  NSInteger indexsss;
     __block BOOL isExist = NO;
     [selArr enumerateObjectsUsingBlock:^(FriendModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj.idStr isEqualToString:model.idStr]) {//数组中已经存在该对象
                    *stop = YES;
                    isExist = YES;
                    indexsss = idx;
              }
    }];
    if (!isExist) {//如果不存在就添加进去
        [selArr addObject:model];
    }else{
        [selArr removeObjectAtIndex:indexsss];
    }
    [self.listTable reloadData];
    NSLog(@"----------selarr:-----1111:%@",selArr);
}
-(void)shareBtnClick{
    NSLog(@"idstr------:%@",selArr);
    if (selArr.count < 1) {
        [MBProgressHUD showError:YZMsg(@"请选择好友")];
        return;
    }
    userIndex = 0;
    for (int i = 0; i < selArr.count; i ++) {
        FriendModel *model = selArr[i];
        [self checkBlackWithUserID:model.idStr];
    }
}
-(void)checkBlackWithUserID:(NSString *)touid {
    NSDictionary *pDic = @{@"uid":[Config getOwnID],
                           @"token":[Config getOwnToken],
                           @"touid":touid
    };
    [YBNetworking postWithUrl:@"User.checkBlack" Dic:pDic Suc:^(int code, id info, NSString *msg) {
            if (code == 0) {
                NSDictionary *infoDic = [info firstObject];
                NSString *t2u = [NSString stringWithFormat:@"%@",[infoDic valueForKey:@"t2u"]];
                if ([t2u isEqual:@"0"]) {
                    NSDictionary *imDic = @{
                        @"method":@"GoodsMsg",
                        @"goodsid":self.goodsId,
                    };
                    NSData *data = [NSJSONSerialization dataWithJSONObject:imDic options:NSJSONWritingPrettyPrinted error:nil];
                    V2TIMCustomElem * custom_elem = [[V2TIMCustomElem alloc] init];
                    [custom_elem setData:data];
                    [[YBImManager shareInstance]sendV2CustomMsg:custom_elem andReceiver:touid complete:^(BOOL isSuccess, V2TIMMessage *sendMsg, NSString *desc) {
                                            
                    }];
                }else {
                    [MBProgressHUD showError:YZMsg(@"对方暂时拒绝接收您的消息")];
                }
                userIndex += 1;
                [self checkMegSendOver];

            }else {
                [MBProgressHUD hideHUD];
                [MBProgressHUD showError:msg];
                userIndex += 1;
                [self checkMegSendOver];

            }
        } Fail:^(id fail) {
            
        }];
    
}
-(void)checkMegSendOver{
    if (userIndex == selArr.count) {
        [MBProgressHUD hideHUD];
        [[XGGAppDelegate sharedAppDelegate]popViewController:YES];
    }
}
@end
