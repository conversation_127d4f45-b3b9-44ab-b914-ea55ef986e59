//
//  FriendModel.m
//  yunbaolive
//
//  Created by ybRRR on 2021/7/10.
//  Copyright © 2021 cat. All rights reserved.
//

#import "FriendModel.h"

@implementation FriendModel
-(instancetype)initWithDic:(NSDictionary *)dic{
    self = [super init];
    if (self) {
        
        self.name = [NSString stringWithFormat:@"%@",[dic valueForKey:@"user_nickname"]];
        self.level = [NSString stringWithFormat:@"%@",[dic valueForKey:@"level"]];
        self.sex = [NSString stringWithFormat:@"%@",[dic valueForKey:@"sex"]];
        self.signature = [NSString stringWithFormat:@"%@",[dic valueForKey:@"signature"]];
        self.icon = [NSString stringWithFormat:@"%@",[dic valueForKey:@"avatar"]];
        self.level_anchor = [NSString stringWithFormat:@"%@",[dic valueForKey:@"level_anchor"]];
//        self.isexists = minstr([dic valueForKey:@"isexists"]);
        self.gc_isshow = minstr([dic valueForKey:@"gc_isshow"]);

        self.idStr = minstr([dic valueForKey:@"id"]);
    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)dic{
    return  [[self alloc]initWithDic:dic];
}

@end
