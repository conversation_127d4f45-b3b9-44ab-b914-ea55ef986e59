//
//  FriendModel.h
//  yunbaolive
//
//  Created by ybRRR on 2021/7/10.
//  Copyright © 2021 cat. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface FriendModel : NSObject
@property(nonatomic,copy)NSString *name;

@property(nonatomic,copy)NSString *level;

@property(nonatomic,copy)NSString *sex;

@property(nonatomic,copy)NSString *signature;

@property(nonatomic,copy)NSString *icon;

@property(nonatomic,copy)NSString *level_anchor;

@property (nonatomic, strong)NSString *gc_isshow;
@property (nonatomic, strong)NSString *idStr;

-(instancetype)initWithDic:(NSDictionary *)dic;
+(instancetype)modelWithDic:(NSDictionary *)dic;

@end
