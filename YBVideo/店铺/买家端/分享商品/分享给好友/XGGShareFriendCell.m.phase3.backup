//
//  ShareFriendCell.m
//  yunbaolive
//
//  Created by ybRRR on 2021/7/10.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ShareFriendCell.h"

@implementation ShareFriendCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(ShareFriendCell *)cellWithTableView:(UITableView *)tableView{
    ShareFriendCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ShareFriendCeLL"];
    if (!cell) {
        if (!cell) {
           cell = [[NSBundle mainBundle]loadNibNamed:@"ShareFriendCell" owner:self options:nil].lastObject;
        }
    }
    return cell;
}
-(void)setModel:(FriendModel *)model
{
    _model = model;
    _nameLb.text = _model.name;
    _signatureLb.text = _model.signature;
    //性别 1男
     if ([[_model valueForKey:@"sex"] isEqual:@"1"])
    {
        self.sexImg.image = [UIImage imageNamed:@"sex_man"];
    }
    else
    {
        self.sexImg.image = [UIImage imageNamed:@"sex_woman"];
    }
    //级别
    
//    NSDictionary *userLevel = [common getUserLevelMessage:_model.level];
//    [self.levImg sd_setImageWithURL:[NSURL URLWithString:minstr([[common getAnchorLevelMessage:_model.level_anchor] valueForKey:@"thumb"])]];
//    [self.hostImg sd_setImageWithURL:[NSURL URLWithString:minstr([userLevel valueForKey:@"thumb"])]];
    //头像
    [self.headImg sd_setImageWithURL:[NSURL URLWithString:model.icon]];
    
    if ([_model.gc_isshow isEqual:@"0"]) {
        self.selImg.image = [UIImage imageNamed:@"commodity_记录选中"];
    }else{
        self.selImg.image = [UIImage imageNamed:@"commodity_记录未选"];
    }

}
@end
