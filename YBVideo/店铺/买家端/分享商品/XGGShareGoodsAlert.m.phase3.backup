//
//  ShareGoodsAlert.m
//  yunbaolive
//
//  Created by ybRRR on 2021/6/7.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ShareGoodsAlert.h"
#import "ShareFriendVC.h"
//#import "PublishDynamicViewController.h"

@implementation ShareGoodsAlert
{
    CommodityDetailModel *goodsData;

}
-(instancetype)initWithFrame:(CGRect)frame andGoodsModel:(CommodityDetailModel *)goodsModel
{
    self = [super initWithFrame:frame];
    if (self) {
        goodsData = goodsModel;
        UITapGestureRecognizer *taps =[[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSelf)];
        [self addGestureRecognizer:taps];
        self.backgroundColor = RGBA(1, 1, 1, 0.3);
        backView = [[UIView alloc]init];
        backView.frame = CGRectMake(0, _window_height, _window_width, 185);
        backView.backgroundColor = UIColor.whiteColor;
        [self addSubview:backView];
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:backView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
               CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
               maskLayer.frame = backView.bounds;
               maskLayer.path = maskPath.CGPath;
        backView.layer.mask = maskLayer;
        
        NSArray *arr;
        if ([goodsModel.shop_uid isEqual:[Config getOwnID]]) {
            arr=@[YZMsg(@"分享商品"),YZMsg(@"取消分享")];
            backView.size = CGSizeMake(_window_width, 125);
        }else{
            arr=@[YZMsg(@"分享商品，赚取佣金¥"),YZMsg(@"分享商品"),YZMsg(@"取消分享")];
            backView.size = CGSizeMake(_window_width, 185);
        }
        for (int i = 0; i < arr.count; i ++) {
            UIView *views = [[UIView alloc]init];
            views.backgroundColor = [UIColor whiteColor];
            [backView addSubview:views];
            
            UILabel *lineLb = [[UILabel alloc]init];
            if ([goodsModel.shop_uid isEqual:[Config getOwnID]]) {
                if ( i == 0) {
                    views.frame = CGRectMake(0,i*60, backView.width, 65);

                    UIButton *friendBtn = [UIButton buttonWithType:0];
                    friendBtn.frame = CGRectMake(0, 0, views.width, views.height);
                    [friendBtn setTitle:arr[i] forState:0];
                    friendBtn.titleLabel.font = [UIFont systemFontOfSize:14];
                    [friendBtn setTitleColor:Pink_Cor forState:0];
                    [friendBtn addTarget:self action:@selector(shareFriend) forControlEvents:UIControlEventTouchUpInside];
                    [views addSubview:friendBtn];
                    lineLb.frame = CGRectMake(0, views.height-5, backView.width, 5);
                }else{
                    views.frame = CGRectMake(0,i*60+5, backView.width, 60);

                    UIButton *cancelBtn = [UIButton buttonWithType:0];
                    cancelBtn.frame = CGRectMake(0, 0, views.width, views.height);
                    [cancelBtn setTitle:arr[i] forState:0];
                    cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
                    [cancelBtn setTitleColor:UIColor.blackColor forState:0];
                    [cancelBtn addTarget:self action:@selector(cancelBtnClick) forControlEvents:UIControlEventTouchUpInside];
                    [views addSubview:cancelBtn];

                }
            }else{
                if (i == 0) {
                    views.frame = CGRectMake(0,i*60, backView.width, 60);

                    _subTitle = [[UILabel alloc]init];
                    _subTitle.frame = CGRectMake(0, 20, backView.width, 20);
                    _subTitle.font = [UIFont systemFontOfSize:14];
                    _subTitle.text = [NSString stringWithFormat:@"%@%@",arr[i],minstr([ goodsModel.goods_info valueForKey:@"share_income"])] ;
                    _subTitle.textAlignment = NSTextAlignmentCenter;
                    _subTitle.textColor = UIColor.grayColor;
                    [views addSubview:_subTitle];
                    lineLb.frame = CGRectMake(0, views.height-1, backView.width, 1);

                }else if(i == 1){
                    views.frame = CGRectMake(0,i*60, backView.width, 65);

                    UIButton *friendBtn = [UIButton buttonWithType:0];
                    friendBtn.frame = CGRectMake(0, 0, views.width, views.height);
                    [friendBtn setTitle:arr[i] forState:0];
                    friendBtn.titleLabel.font = [UIFont systemFontOfSize:14];
                    [friendBtn setTitleColor:Pink_Cor forState:0];
                    [friendBtn addTarget:self action:@selector(shareFriend) forControlEvents:UIControlEventTouchUpInside];
                    [views addSubview:friendBtn];
                    lineLb.frame = CGRectMake(0, views.height-5, backView.width, 5);

                }else{
                    views.frame = CGRectMake(0,i*60+5, backView.width, 60);

                    UIButton *cancelBtn = [UIButton buttonWithType:0];
                    cancelBtn.frame = CGRectMake(0, 0, views.width, views.height);
                    [cancelBtn setTitle:arr[i] forState:0];
                    cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
                    [cancelBtn setTitleColor:UIColor.blackColor forState:0];
                    [cancelBtn addTarget:self action:@selector(cancelBtnClick) forControlEvents:UIControlEventTouchUpInside];
                    [views addSubview:cancelBtn];
                }
            }
            lineLb.backgroundColor = RGB(245, 245, 245);
            [views addSubview:lineLb];
        }
        [UIView animateWithDuration:0.3 animations:^{
            if ([goodsModel.shop_uid isEqual:[Config getOwnID]]) {
                backView.frame = CGRectMake(0, _window_height-125, _window_width, 125);
            }else{
                backView.frame = CGRectMake(0, _window_height-185, _window_width, 185);

            }

        }];
    }
    return self;
}

-(void)hideSelf{
    [UIView animateWithDuration:0.3 animations:^{
        if ([goodsData.shop_uid isEqual:[Config getOwnID]]) {
            backView.frame = CGRectMake(0, _window_height, _window_width, 125);
        }else{
            backView.frame = CGRectMake(0, _window_height, _window_width, 185);
        }
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}
-(void)cancelBtnClick{
    [self hideSelf];
}
-(void)shareFriend{
    ShareFriendVC *friendVc = [[ShareFriendVC alloc]init];
    friendVc.goodsId =goodsData.goodsid;
    [[XGGAppDelegate sharedAppDelegate]pushViewController:friendVc animated:YES];
    [self cancelBtnClick];

}
@end
