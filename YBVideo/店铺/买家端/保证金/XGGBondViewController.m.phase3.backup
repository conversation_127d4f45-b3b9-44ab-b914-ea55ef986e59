//
//  BondViewController.m
//  yunbaolive
//
//  Created by ybRRR on 2020/2/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import "BondViewController.h"
#import "YBRechargeVC.h"
@interface BondViewController ()
{
    UILabel *priceLb;
    UIButton *payBtn;
}
@end

@implementation BondViewController
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"开店保证金");
    self.view.backgroundColor =RGB(24, 21, 43);
    
    UIView *tback = [[UIView alloc]init];
    tback.frame = CGRectMake(0, 64+statusbarHeight, _window_width, 50);
    tback.backgroundColor = RGB(24, 21, 43);
    [self.view addSubview:tback];
    
    UILabel *title = [[UILabel alloc]init];
    title.text = YZMsg(@"需要缴纳金额");
    title.font = [UIFont systemFontOfSize:14];
    title.textColor = Normal_TextColor;
    [tback addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(tback).offset(12);
        make.centerY.equalTo(tback.mas_centerY);
    }];
    
    UIImageView *rightImg = [[UIImageView alloc]init];
    rightImg.image = [UIImage imageNamed:@"logFirst_钻石"];
    [tback addSubview:rightImg];
    [rightImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(tback).offset(-15);
        make.width.height.mas_equalTo(16);
        make.centerY.equalTo(tback.mas_centerY);
    }];
    
    priceLb = [[UILabel alloc]init];
    priceLb.font = [UIFont boldSystemFontOfSize:14];
    priceLb.textColor = Normal_TextColor;
    [tback addSubview:priceLb];
    [priceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(rightImg.mas_left).offset(-5);
        make.centerY.equalTo(tback.mas_centerY);
    }];
    priceLb.text = minstr([_infoDic valueForKey:@"shop_bond"]);

    [PublicObj lineViewWithFrame:CGRectMake(0, tback.bottom, _window_width, 5) andColor:RGB(17, 13, 35) andView:self.view];
    
    UILabel *infoTitle = [[UILabel alloc]init];
    infoTitle.text = YZMsg(@"保证金说明");
    infoTitle.font = [UIFont systemFontOfSize:14];
    infoTitle.textColor = Normal_TextColor;
    [self.view addSubview:infoTitle];
    [infoTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(12);
        make.top.equalTo(tback.mas_bottom).offset(15);
        make.height.mas_equalTo(20);
    }];
    NSString *str2 = [NSString stringWithFormat:YZMsg(@"2、用户撤销%@时，可申请退还保证金。"),[common shop_system_name]];//YZMsg(@"2、用户撤销云豹小店时，可申请退还保证金。保证金说明")

    UILabel *contentLb = [[UILabel alloc]init];
    contentLb.font = [UIFont systemFontOfSize:13];
    contentLb.textColor = Normal_TextColor;
    contentLb.numberOfLines = 0;
    contentLb.lineBreakMode = NSLineBreakByWordWrapping;
    contentLb.text = [NSString stringWithFormat:@"%@\n%@\n%@\n%@",YZMsg(@"1、保证金由商户交由平台暂时保管，用于约束商户行为，保障消 费者权益。"),str2,YZMsg(@"3、当用户开通店铺后，若存在欺骗消费者、售卖假冒伪劣产品等一切违反国家法律法规以及平台规定的等行为，平台有权强制关闭店铺，保证金不予退还。"),YZMsg(@"4、店铺保证金最终解释权归平台所有。")];
    [self.view addSubview:contentLb];
    [contentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(12);
        make.top.equalTo(infoTitle.mas_bottom).offset(5);
        make.right.equalTo(self.view).offset(-12);
        
    }];
    
    
    payBtn = [UIButton buttonWithType:0];
    [payBtn setBackgroundColor:Pink_Cor];
    [payBtn setTitle:YZMsg(@"确认缴纳") forState:0];
    [payBtn setTitleColor:[UIColor whiteColor] forState:0];
    payBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    payBtn.layer.cornerRadius = 5;
    payBtn.layer.masksToBounds = YES;
    [payBtn addTarget:self action:@selector(payBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:payBtn];
    [payBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(15);
        make.right.equalTo(self.view).offset(-15);
        make.top.equalTo(contentLb.mas_bottom).offset(30);
        make.height.mas_equalTo(40);
    }];
    
//    [self requstBondStatus];
}

-(void)payBtnClick{    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
    NSString *sign = [PublicObj sortString:signdic];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                          @"sign":sign
                          };
    [YBNetworking postWithUrl:@"Shop.deductBond" Dic:dic Suc:^(int code, id info, NSString *msg) {
        if (code ==0) {
            [MBProgressHUD showError:msg];
            [self.navigationController popViewControllerAnimated:YES];
        }else if (code ==1004){
            UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"开通店铺保证金不足\n请先进行充值") preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                
            }];
            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"立刻充值") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                YBRechargeVC *chargeVC = [[YBRechargeVC alloc]init];
                [[XGGAppDelegate sharedAppDelegate]pushViewController:chargeVC animated:YES];
            }];
            [cancelAction setValue:[UIColor lightGrayColor] forKey:@"_titleTextColor"];
            [sureAction setValue:Normal_Color forKey:@"_titleTextColor"];
            [alertControl addAction:cancelAction];
            [alertControl addAction:sureAction];
            [self.navigationController  presentViewController:alertControl animated:YES completion:nil];
        } else{
            [MBProgressHUD showError:msg];
        }

        } Fail:^(id fail) {
            
        }];


}
@end
