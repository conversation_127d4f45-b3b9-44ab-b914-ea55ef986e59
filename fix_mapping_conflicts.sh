#!/bin/bash

# 修正映射文件中的冲突
echo "=== 修正映射文件中的冲突 ==="

MAPPING_FILE="need_rename_mapping.txt"
FIXED_MAPPING_FILE="need_rename_mapping_fixed.txt"
FIX_LOG="fix_mapping_log.txt"

# 清空日志
> "$FIX_LOG"

echo "开始修正映射文件..." | tee -a "$FIX_LOG"
echo "原始映射文件: $MAPPING_FILE" | tee -a "$FIX_LOG"
echo "修正后映射文件: $FIXED_MAPPING_FILE" | tee -a "$FIX_LOG"
echo "" | tee -a "$FIX_LOG"

if [[ ! -f "$MAPPING_FILE" ]]; then
    echo "❌ 错误：映射文件 $MAPPING_FILE 不存在" | tee -a "$FIX_LOG"
    exit 1
fi

# 备份原始映射文件
cp "$MAPPING_FILE" "${MAPPING_FILE}.backup"
echo "已备份原始映射文件到: ${MAPPING_FILE}.backup" | tee -a "$FIX_LOG"

# 开始修正
echo "开始修正映射..." | tee -a "$FIX_LOG"
echo "" | tee -a "$FIX_LOG"

# 统计变量
total_lines=0
fixed_lines=0
skipped_lines=0

# 创建修正后的映射文件
> "$FIXED_MAPPING_FILE"

while read -r line; do
    ((total_lines++))
    
    if [[ -n "$line" && "$line" == *" -> "* ]]; then
        # 分割映射行
        old_class="${line% -> *}"
        new_class="${line#* -> }"
        
        # 检查是否是需要修正的映射
        case "$old_class" in
            "TCRangeContentConfig")
                # 修正：TCRangeContentConfig 应该保持原名或使用唯一前缀
                fixed_new_class="XGGTCRangeContentConfig"
                echo "$old_class -> $fixed_new_class" >> "$FIXED_MAPPING_FILE"
                echo "修正映射: $old_class -> $new_class => $old_class -> $fixed_new_class" | tee -a "$FIX_LOG"
                ((fixed_lines++))
                ;;
            "类名: @implementation TCRangeContentConfig")
                # 这是implementation映射，需要相应修正
                echo "$old_class -> XGGTCRangeContentConfig" >> "$FIXED_MAPPING_FILE"
                echo "修正implementation映射: $line => $old_class -> XGGTCRangeContentConfig" | tee -a "$FIX_LOG"
                ((fixed_lines++))
                ;;
            *)
                # 其他映射保持不变
                echo "$line" >> "$FIXED_MAPPING_FILE"
                ((skipped_lines++))
                ;;
        esac
    else
        # 非映射行（空行等）保持不变
        echo "$line" >> "$FIXED_MAPPING_FILE"
        ((skipped_lines++))
    fi
    
    # 每处理100行显示进度
    if [[ $((total_lines % 100)) -eq 0 ]]; then
        echo "已处理 $total_lines 行..."
    fi
done < "$MAPPING_FILE"

echo "" | tee -a "$FIX_LOG"
echo "=== 修正完成 ===" | tee -a "$FIX_LOG"
echo "总行数: $total_lines" | tee -a "$FIX_LOG"
echo "修正行数: $fixed_lines" | tee -a "$FIX_LOG"
echo "保持不变: $skipped_lines" | tee -a "$FIX_LOG"
echo "" | tee -a "$FIX_LOG"

# 验证修正结果
echo "验证修正结果..." | tee -a "$FIX_LOG"
echo "" | tee -a "$FIX_LOG"

# 检查是否还有冲突
echo "检查修正后的映射文件是否还有冲突..." | tee -a "$FIX_LOG"

# 检查TCRangeContentConfig相关映射
tcrange_config_count=$(grep -c "TCRangeContentConfig -> " "$FIXED_MAPPING_FILE" || echo "0")
tcrange_content_count=$(grep -c "TCRangeContent -> " "$FIXED_MAPPING_FILE" || echo "0")

echo "修正后映射统计:" | tee -a "$FIX_LOG"
echo "  TCRangeContentConfig 映射数: $tcrange_config_count" | tee -a "$FIX_LOG"
echo "  TCRangeContent 映射数: $tcrange_content_count" | tee -a "$FIX_LOG"

# 显示相关映射
echo "" | tee -a "$FIX_LOG"
echo "TCRangeContentConfig 相关映射:" | tee -a "$FIX_LOG"
grep "TCRangeContentConfig" "$FIXED_MAPPING_FILE" | head -5 | tee -a "$FIX_LOG"

echo "" | tee -a "$FIX_LOG"
echo "TCRangeContent 相关映射:" | tee -a "$FIX_LOG"
grep "TCRangeContent[^C]" "$FIXED_MAPPING_FILE" | head -5 | tee -a "$FIX_LOG"

if [[ $fixed_lines -gt 0 ]]; then
    echo "" | tee -a "$FIX_LOG"
    echo "✅ 映射文件修正完成！修正了 $fixed_lines 个冲突映射。" | tee -a "$FIX_LOG"
    echo "修正后的映射文件: $FIXED_MAPPING_FILE" | tee -a "$FIX_LOG"
    echo "现在可以使用修正后的映射文件重新执行第三阶段。" | tee -a "$FIX_LOG"
else
    echo "" | tee -a "$FIX_LOG"
    echo "⚠️  未发现需要修正的映射，映射文件可能已经是正确的。" | tee -a "$FIX_LOG"
fi

echo "" | tee -a "$FIX_LOG"
echo "详细修正日志已保存到: $FIX_LOG"
