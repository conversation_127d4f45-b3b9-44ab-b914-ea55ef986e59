#!/bin/bash

# 调试sed命令
echo "=== 调试sed命令 ==="

OLD_CLASS="TCRangeContentConfig"
NEW_CLASS="XGGTCRangeContentConfig"
TEST_FILE="YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m"

echo "测试不同的sed命令..."
echo ""

# 创建测试文件
cp "$TEST_FILE" "test_file.tmp"

echo "1. 测试基本替换:"
sed "s/${OLD_CLASS}/${NEW_CLASS}/g" "test_file.tmp" | grep -n "$NEW_CLASS" || echo "未找到"

echo ""
echo "2. 测试单词边界替换:"
sed "s/\b${OLD_CLASS}\b/${NEW_CLASS}/g" "test_file.tmp" | grep -n "$NEW_CLASS" || echo "未找到"

echo ""
echo "3. 测试转义版本:"
sed "s/\\<${OLD_CLASS}\\>/${NEW_CLASS}/g" "test_file.tmp" | grep -n "$NEW_CLASS" || echo "未找到"

echo ""
echo "4. 测试简单替换（不使用单词边界）:"
sed "s/${OLD_CLASS}/${NEW_CLASS}/g" "test_file.tmp" | grep -n "$NEW_CLASS" || echo "未找到"

# 清理
rm -f "test_file.tmp"
