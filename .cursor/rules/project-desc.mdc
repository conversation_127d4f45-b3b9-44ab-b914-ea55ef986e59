---
alwaysApply: true
---


# YBVideo项目概况与开发规则

## 项目概况

### 基本信息
- **项目名称**: YBVideo (闲逛逛)
- **平台**: iOS原生应用
- **开发语言**: Objective-C
- **最低支持版本**: iOS 13.0
- **架构模式**: MVC架构
- **项目类型**: 短视频+直播社交平台

### 核心功能模块

#### 1. 直播模块 (`YBVideo/直播模块/`)
- **主播端**: 推流、美颜、连麦、PK、礼物系统
- **观众端**: 拉流观看、聊天互动、送礼
- **技术栈**: 腾讯云直播SDK (TXLiteAVSDK_Professional)
- **Socket通信**: Socket.IO实现实时互动
- **美颜**: 美狐SDK集成

#### 2. 短视频模块 (`YBVideo/首页/`, `YBVideo/录制_编辑_发布/`)
- **视频播放**: ZFPlayer播放器
- **视频录制**: 腾讯云UGC SDK
- **视频编辑**: 滤镜、特效、音乐
- **视频发布**: 上传、审核、分发

#### 3. 社交功能 (`YBVideo/消息/`, `YBVideo/个人中心/`)
- **即时通讯**: 腾讯IM SDK (TXIMSDK_Plus_iOS)
- **关注系统**: 粉丝、关注、拉黑
- **消息系统**: 私信、系统消息、评论

#### 4. 电商功能 (`YBVideo/店铺/`)
- **商品展示**: 视频带货、商品橱窗
- **交易系统**: 订单管理、支付集成
- **店铺管理**: 买家端、卖家端

#### 5. 其他核心功能
- **用户系统**: 登录注册、个人资料、权限管理
- **支付系统**: 支付宝、微信支付、Braintree
- **推送通知**: 极光推送 (JPush)
- **数据统计**: 友盟统计
- **地理位置**: 附近功能、城市选择

### 技术架构

#### 网络层
- **主要框架**: AFNetworking 4.0.1
- **自定义封装**: XGGNetworkManager, YBNetworking
- **API管理**: XGGURLConstant统一管理接口

#### 数据存储
- **本地缓存**: YYCache
- **用户偏好**: NSUserDefaults
- **数据库**: 极光IM数据库

#### UI框架
- **布局**: Masonry自动布局
- **图片加载**: SDWebImage
- **下拉刷新**: MJRefresh
- **空数据展示**: DZNEmptyDataSet

#### 第三方SDK集成
- **直播**: 腾讯云直播SDK
- **IM**: 腾讯IM SDK
- **推送**: 极光推送
- **支付**: 支付宝、微信、Braintree
- **分享**: ShareSDK
- **美颜**: 美狐SDK
- **云存储**: 腾讯云COS、七牛云、AWS S3

## 开发规则与规范

### 1. 代码结构规范

#### 目录组织
```
YBVideo/
├── 其他类/           # 基础类、AppDelegate、导航控制器
├── 公共方法类/       # 工具类、网络请求、公共视图
├── 工具和自定义类/   # 自定义控件、分类扩展
├── 底部导航/         # TabBar相关
├── 首页/            # 短视频推荐、分类
├── 直播模块/         # 直播相关功能
├── 录制_编辑_发布/   # 视频创作
├── 消息/            # IM聊天
├── 个人中心/         # 用户资料、设置
├── 店铺/            # 电商功能
├── 功能/            # 其他业务功能
└── 缓存/            # 本地数据缓存
```

#### 命名规范
- **类名**: 使用YB前缀，如`YBLiveVC`、`YBNetworking`
- **方法名**: 驼峰命名，动词开头
- **属性名**: 驼峰命名，名词性
- **常量**: 全大写，下划线分隔

### 2. 架构设计原则

#### MVC模式
- **Model**: 数据模型，使用MJExtension进行JSON转换
- **View**: 视图层，XIB+代码混合开发
- **Controller**: 控制器，继承自YBBaseViewController

#### 网络请求规范
```objective-c
// 统一使用YBNetworking进行网络请求
[YBNetworking postWithUrl:@"接口名" Dic:参数字典 Suc:^(int code, id info, NSString *msg) {
    // 成功回调
} Fail:^(int code, id info, NSString *msg) {
    // 失败回调
}];
```

#### 内存管理
- 使用ARC自动内存管理
- 注意循环引用，使用weak引用
- 及时移除通知和定时器

### 3. UI开发规范

#### 布局约束
- 优先使用Masonry进行自动布局
- 适配iPhone X系列刘海屏
- 支持横竖屏切换（部分功能）

#### 颜色管理
```objective-c
#define Pink_Cor RGB_COLOR(@"#EA377F", 1)
#define Normal_Color RGB_COLOR(@"#110D24", 1)
#define Normal_BackColor RGB(17, 13, 35)
```

#### 字体规范
```objective-c
#define SYS_Font(a) [UIFont systemFontOfSize:(a)]
#define NaviTitle_Font [UIFont boldSystemFontOfSize:17]
```

### 4. 业务开发规范

#### 直播功能开发
- 使用腾讯云直播SDK进行推拉流
- Socket.IO处理实时消息
- 美颜功能集成美狐SDK
- 连麦PK使用RTC模式

#### 短视频功能开发
- ZFPlayer作为播放器核心
- 腾讯云UGC SDK处理录制编辑
- 支持多种视频格式和分辨率

#### IM功能开发
- 使用腾讯IM SDK
- 支持文本、图片、语音、视频消息
- 实现群聊、私聊功能

### 5. 性能优化规范

#### 内存优化
- 及时释放不用的资源
- 图片缓存使用SDWebImage
- 视频播放器复用

#### 网络优化
- 接口请求合并
- 图片压缩上传
- 视频分片上传

#### 用户体验优化
- 加载状态提示
- 错误处理友好
- 离线缓存支持

### 6. 安全规范

#### 数据安全
- 敏感信息加密存储
- 网络传输HTTPS
- 用户隐私保护

#### 代码安全
- 防止数组越界（SafeObject）
- 异常捕获处理
- 输入验证

### 7. 测试规范

#### 单元测试
- 核心业务逻辑测试
- 网络请求测试
- 数据模型测试

#### 集成测试
- 功能模块集成测试
- 第三方SDK集成测试
- 性能压力测试

### 8. 发布规范

#### 版本管理
- 使用语义化版本号
- 维护版本更新日志
- 分支管理规范

#### 打包发布
- 多环境配置（Debug/Release）
- 证书管理
- App Store审核规范

## 注意事项

1. **第三方SDK版本**: 严格按照Podfile中指定版本，升级需谨慎测试
2. **多语言支持**: 使用YBLanguageTools进行国际化
3. **青少年模式**: 遵循相关法规要求
4. **隐私权限**: 合理申请和使用系统权限
5. **性能监控**: 集成Bugly进行崩溃监控

此规则文档将指导后续的功能开发和代码维护，确保项目的稳定性和可扩展性。

