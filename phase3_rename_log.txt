处理类名映射: TCRangeContentConfig -> > TCRangeContent
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m

处理类名映射: VideoColorInfo -> > XGGVideoColorInfo
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.h

处理类名映射: EffectSelectView -> > XGGEffectSelectView
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m

处理类名映射: TimeSelectView -> > XGGTimeSelectView
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m

处理类名映射: TCMusicInfo -> > TCMusicCollectionCell
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.m

处理类名映射: AlbumVideoCell -> > XGGAlbumVideoCell
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.h
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.m

处理类名映射: AlbumVideoVC -> > XGGAlbumVideoVC
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m

处理类名映射: VideoRecordProcessView -> > XGGVideoRecordProcessView
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.h
    YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m

处理类名映射: SpeedView -> > XGGSpeedView
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.m
    YBVideo//录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m
    YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.h
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m

处理类名映射: YBPicTransitionVC -> > XGGYBPicTransitionVC
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h

处理类名映射: SmallButton -> > XGGSmallButton
  找到引用文件:
    YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.h
    YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.m

处理类名映射: PhotoTransitionToolbar -> > XGGPhotoTransitionToolbar
  找到引用文件:
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
    YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.h

处理类名映射: VerticalButton -> > XGGVerticalButton
  找到引用文件:
    YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
    YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.m
    YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.h

处理类名映射: YBPublishCoverVC -> > XGGYBPublishCoverVC
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m
    YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h

处理类名映射: YBSetChargeView -> > XGGYBSetChargeView
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m
    YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.h

处理类名映射: YBVideoAddGoodsVC -> > XGGYBVideoAddGoodsVC
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m

处理类名映射: videoTopicCell -> > XGGvideoTopicCell
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.h
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.m
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m

处理类名映射: videoTopicVC -> > XGGvideoTopicVC
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h

处理类名映射: YBVideoClassVC -> > XGGYBVideoClassVC
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m

处理类名映射: MusicClassVC -> > XGGMusicClassVC
  找到引用文件:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m

处理类名映射: YBVideoMusicView -> > XGGYBVideoMusicView
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.h

处理类名映射: MusicModel -> > XGGMusicModel
  找到引用文件:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.h
    YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.h
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m

处理类名映射: MusicCell -> > XGGMusicCell
  找到引用文件:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.h
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m

处理类名映射: MusicHeaderView -> > XGGMusicHeaderView
  找到引用文件:
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.h

处理类名映射: MusicHeaderCell -> > XGGMusicHeaderCell
  找到引用文件:
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.h

处理类名映射: YBBaseAppDelegate -> > XGGYBBaseAppDelegate
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    YBVideo//其他类/XGGYBBaseAppDelegate.h
    YBVideo//其他类/TCBaseAppDelegate.m
    YBVideo//其他类/XGGYBBaseAppDelegate.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    YBVideo//缓存/定位/XGGRKLBSManager.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 更新成功: YBVideo//其他类/XGGYBBaseAppDelegate.h
    ✅ 更新成功: YBVideo//其他类/TCBaseAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBBaseAppDelegate.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 更新成功: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    ✅ 更新成功: YBVideo//缓存/定位/XGGRKLBSManager.m

处理类名映射: RKLBSManager -> > XGGRKLBSManager
  找到引用文件:
    YBVideo//其他类/XGGRKLBSManager.h
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//缓存/定位/XGGRKLBSManager.h
    YBVideo//缓存/定位/XGGRKLBSManager.m
    YBVideo//功能/极光消息/单聊/View/JCHATMessageContentView.m
    YBVideo//功能/极光消息/发送位置/XGGTencentLocationVC.m
    YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.m
    YBVideo//附近/XGGNearbyVC.m
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//其他类/XGGRKLBSManager.h
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//缓存/定位/XGGRKLBSManager.h
    ✅ 更新成功: YBVideo//缓存/定位/XGGRKLBSManager.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/JCHATMessageContentView.m
    ✅ 更新成功: YBVideo//功能/极光消息/发送位置/XGGTencentLocationVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m

处理类名映射: AppDelegate -> > XGGAppDelegate
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//其他类/XGGAppDelegate.h
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//main.m
    YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    YBVideo//功能/极光消息/单聊/View/JCHATToolBar.m
    YBVideo//功能/极光消息/Common/JCHATAlertToSendImage.m
    YBVideo//功能/极光消息/External/PreviewPicture/MJPhotoToolbar.m
    YBVideo//功能/极光消息/External/JCHATAlertViewWait.m
    YBVideo//功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoSelectViewController.m
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//功能/KeepLive/XGGRKKeepAlive.m
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//工具和自定义类/XGGUtils.m
    YBVideo//工具和自定义类/Categories/UIColor+Util.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.h
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//main.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/JCHATToolBar.m
    ✅ 更新成功: YBVideo//功能/极光消息/Common/JCHATAlertToSendImage.m
    ✅ 更新成功: YBVideo//功能/极光消息/External/PreviewPicture/MJPhotoToolbar.m
    ✅ 更新成功: YBVideo//功能/极光消息/External/JCHATAlertViewWait.m
    ✅ 更新成功: YBVideo//功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoSelectViewController.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//功能/KeepLive/XGGRKKeepAlive.m
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGUtils.m
    ✅ 更新成功: YBVideo//工具和自定义类/Categories/UIColor+Util.m

处理类名映射: YBBaseViewController -> > XGGYBBaseViewController
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.h
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.h
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h
    YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.h
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.h
    YBVideo//其他类/XGGYBBaseViewController.m
    YBVideo//其他类/XGGYBBaseViewController.h
    YBVideo//首页/推荐/XGGYBLookVideoVC.h
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.h
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.h
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.h
    YBVideo//首页/XGGYBHomeViewController.h
    YBVideo//登录注册/XGGDspLoginVC.h
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.h
    YBVideo//公共方法类/XGGPublicObj.h
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//引导页/XGGGuideViewController.h
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.h
    YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.h
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.h
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.h
    YBVideo//店铺/买家端/保证金/XGGBondViewController.h
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.h
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.h
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.h
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.h
    ✅ 更新成功: YBVideo//其他类/XGGYBBaseViewController.m
    ✅ 更新成功: YBVideo//其他类/XGGYBBaseViewController.h
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.h
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.h
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.h
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.h
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.h
    ✅ 更新成功: YBVideo//登录注册/国家代号/XGGCountryCodeVC.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.h
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/保证金/XGGBondViewController.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h

处理类名映射: YBNavigationController -> > XGGYBNavigationController
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//其他类/XGGYBNavigationController.m
    YBVideo//其他类/TCBaseAppDelegate.m
    YBVideo//其他类/XGGYBBaseAppDelegate.m
    YBVideo//其他类/XGGYBNavigationController.h
    YBVideo//其他类/TCNavigationController.h
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBNavigationController.m
    ✅ 更新成功: YBVideo//其他类/TCBaseAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBBaseAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBNavigationController.h
    ✅ 更新成功: YBVideo//其他类/TCNavigationController.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: YBNavigationController -> > TCNavigationController
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//其他类/XGGYBNavigationController.m
    YBVideo//其他类/TCBaseAppDelegate.m
    YBVideo//其他类/XGGYBBaseAppDelegate.m
    YBVideo//其他类/XGGYBNavigationController.h
    YBVideo//其他类/TCNavigationController.h
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBNavigationController.m
    ✅ 更新成功: YBVideo//其他类/TCBaseAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBBaseAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBNavigationController.h
    ✅ 更新成功: YBVideo//其他类/TCNavigationController.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: YBGetVideoObj -> > XGGYBGetVideoObj
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    YBVideo//消息/XGGMsgTopPubVC.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/广告管理/XGGMyAdvertVC.m
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    YBVideo//附近/XGGNearbyVC.m
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGMyAdvertVC.m
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m

处理类名映射: YBHomeRedObj -> > XGGYBHomeRedObj
  找到引用文件:
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.m

处理类名映射: YBLookVideoCell -> > XGGYBLookVideoCell
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.h
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.h
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m

处理类名映射: YBVideoControlView -> > XGGYBVideoControlView
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    YBVideo//首页/推荐/view/XGGYBVideoControlView.h
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.h

处理类名映射: YBLookVideoVC -> > XGGYBLookVideoVC
  找到引用文件:
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.h
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m

处理类名映射: NearbyVideoModel -> > XGGNearbyVideoModel
  找到引用文件:
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.m
    YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.h
    YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.m
    YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.h
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    YBVideo//功能/标签全部视频/XGGtopicVideoCell.h
    YBVideo//功能/标签全部视频/XGGtopicVideoCell.m
    YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    YBVideo//附近/XGGNearbyVC.m
    YBVideo//附近/view/XGGNearbyCell.h
    YBVideo//附近/view/XGGNearbyCell.m
    YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.h
    YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.m
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.h
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.h
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.h
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicVideoCell.h
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicVideoCell.m
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//附近/view/XGGNearbyCell.h
    ✅ 更新成功: YBVideo//附近/view/XGGNearbyCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.h
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.m

处理类名映射: MyFollowViewController -> > XGGMyFollowViewController
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.h
    YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.h
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m

处理类名映射: YBVideosVC -> > XGGYBVideosVC
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.h
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.h

处理类名映射: myVideoV -> > XGGmyVideoV
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.h
    YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.h
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m

处理类名映射: VideoCollectionCell -> > XGGVideoCollectionCell
  找到引用文件:
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.m
    YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.h
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.h
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m

处理类名映射: YBHomeViewController -> > XGGYBHomeViewController
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/XGGYBHomeViewController.h
    YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.h
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m

处理类名映射: DspLoginVC -> > XGGDspLoginVC
  找到引用文件:
    YBVideo//登录注册/XGGDspLoginVC.h
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m

处理类名映射: RegAlertView -> > XGGRegAlertView
  找到引用文件:
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.h
    YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    ✅ 更新成功: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m

处理类名映射: CountryCodeVC -> > XGGCountryCodeVC
  找到引用文件:
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.h
    YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 更新成功: YBVideo//登录注册/国家代号/XGGCountryCodeVC.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m

处理类名映射: RKActionSheet -> > XGGRKActionSheet
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    YBVideo//公共方法类/XGGRKActionSheet.h
    YBVideo//公共方法类/XGGRKActionSheet.m
    YBVideo//缓存/定位/XGGRKLBSManager.m
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/XGGcommCell.m
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//个人中心/头部/XGGHeaderBackImgView.m
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGRKActionSheet.h
    ✅ 更新成功: YBVideo//公共方法类/XGGRKActionSheet.m
    ✅ 更新成功: YBVideo//缓存/定位/XGGRKLBSManager.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGHeaderBackImgView.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m

处理类名映射: YBProgressObj -> > XGGYBProgressObj
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    YBVideo//公共方法类/XGGYBProgressObj.h
    YBVideo//公共方法类/XGGYBProgressObj.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBProgressObj.h
    ✅ 更新成功: YBVideo//公共方法类/XGGYBProgressObj.m

处理类名映射: PublicObj -> > XGGPublicObj
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    YBVideo//录制_编辑_发布/Record/views/TXBaseBeautyView.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//其他类/XGGYBBaseViewController.m
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//XGGNetwork/XGGNetworkManager.m
    YBVideo//公共方法类/XGGiOSNetworking.m
    YBVideo//公共方法类/XGGYBAlertView.m
    YBVideo//公共方法类/XGGPublicObj.h
    YBVideo//公共方法类/XGGYBImageView.m
    YBVideo//公共方法类/XGGRKActionSheet.m
    YBVideo//公共方法类/XGGBGSetting.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//公共方法类/XGGYBNetworking.m
    YBVideo//公共方法类/XGGRKSysAccess.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/views/TXBaseBeautyView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/XGGYBBaseViewController.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    ✅ 更新成功: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//XGGNetwork/XGGNetworkManager.m
    ✅ 更新成功: YBVideo//公共方法类/XGGiOSNetworking.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBAlertView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.h
    ✅ 更新成功: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGRKActionSheet.m
    ✅ 更新成功: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 更新成功: YBVideo//公共方法类/XGGRKSysAccess.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m

处理类名映射: YBNetworking -> > XGGYBNetworking
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//公共方法类/XGGYBNetworking.h
    YBVideo//公共方法类/XGGYBImageView.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//公共方法类/XGGYBNetworking.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 更新成功: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBNetworking.h
    ✅ 更新成功: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m

处理类名映射: BGSetting -> > XGGBGSetting
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//公共方法类/XGGBGSetting.h
    YBVideo//公共方法类/XGGBGSetting.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//公共方法类/XGGBGSetting.h
    ✅ 更新成功: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: RKSysAccess -> > XGGRKSysAccess
  找到引用文件:
    YBVideo//公共方法类/XGGRKSysAccess.h
    YBVideo//公共方法类/XGGRKSysAccess.m
    YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//公共方法类/XGGRKSysAccess.h
    ✅ 更新成功: YBVideo//公共方法类/XGGRKSysAccess.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m

处理类名映射: iOSNetworking -> > XGGiOSNetworking
  找到引用文件:
    YBVideo//公共方法类/XGGiOSNetworking.m
    YBVideo//公共方法类/XGGiOSNetworking.h
    YBVideo//公共方法类/XGGBGSetting.m
    YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 更新成功: YBVideo//公共方法类/XGGiOSNetworking.m
    ✅ 更新成功: YBVideo//公共方法类/XGGiOSNetworking.h
    ✅ 更新成功: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBNetworking.m

处理类名映射: YBAlertView -> > XGGYBAlertView
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//公共方法类/XGGYBAlertView.m
    YBVideo//公共方法类/XGGYBAlertView.h
    YBVideo//公共方法类/XGGBGSetting.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//公共方法类/XGGRKSysAccess.m
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//直播模块/socket/XGGYBSocketLive.m
    YBVideo//直播模块/socket/XGGYBSocketPlay.m
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    YBVideo//功能/极光消息/单聊/View/JCHATToolBar.m
    YBVideo//功能/上热门/XGGaddHotVideoVC.m
    YBVideo//功能/青少年/XGGYBYoungManager.m
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.m
    YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    YBVideo//功能/直播/管理员列表/XGGadminLists.m
    YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    YBVideo//功能/分享/观看分享/XGGYBShareView.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBAlertView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBAlertView.h
    ✅ 更新成功: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//公共方法类/XGGRKSysAccess.m
    ✅ 更新成功: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketLive.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketPlay.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/JCHATToolBar.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGaddHotVideoVC.m
    ✅ 更新成功: YBVideo//功能/青少年/XGGYBYoungManager.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminLists.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m

处理类名映射: RKUUIDManager -> > XGGRKUUIDManager
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//公共方法类/XGGRKUUIDManager.m
    YBVideo//公共方法类/XGGRKUUIDManager.h
    YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//公共方法类/XGGRKUUIDManager.m
    ✅ 更新成功: YBVideo//公共方法类/XGGRKUUIDManager.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m

处理类名映射: YBShowBigImageView -> > XGGYBShowBigImageView
  找到引用文件:
    YBVideo//公共方法类/XGGYBShowBigImageView.m
    YBVideo//公共方法类/XGGYBImageView.m
    YBVideo//公共方法类/XGGYBShowBigImageView.h
    YBVideo//功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBShowBigImageView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBShowBigImageView.h
    ✅ 更新成功: YBVideo//功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.m

处理类名映射: YBImageView -> > XGGYBImageView
  找到引用文件:
    YBVideo//公共方法类/XGGYBImageView.m
    YBVideo//公共方法类/XGGYBImageView.h
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBImageView.h
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m

处理类名映射: PublicView -> > XGGPublicView
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//公共方法类/XGGPublicView.m
    YBVideo//公共方法类/XGGPublicView.h
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//消息/XGGOrderMessageVC.m
    YBVideo//消息/XGGMsgTopPubVC.m
    YBVideo//消息/XGGMsgSysVC.m
    YBVideo//消息/XGGMessageFansVC.m
    YBVideo//消息/选择联系人/XGGSelPeopleV.m
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/广告管理/XGGMyAdvertVC.m
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    YBVideo//功能/礼物/XGGYBGiftView.m
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
    YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRecordView.m
    YBVideo//附近/XGGNearbyVC.m
    YBVideo//附近/城市选择/XGGYBCitySelVC.m
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicView.h
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//消息/XGGOrderMessageVC.m
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 更新成功: YBVideo//消息/XGGMsgSysVC.m
    ✅ 更新成功: YBVideo//消息/XGGMessageFansVC.m
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleV.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGMyAdvertVC.m
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    ✅ 更新成功: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRecordView.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//附近/城市选择/XGGYBCitySelVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m

处理类名映射: GuideViewController -> > XGGGuideViewController
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//引导页/XGGGuideViewController.h
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.h
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m

处理类名映射: ApplyRefundVC -> > XGGApplyRefundVC
  找到引用文件:
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m

处理类名映射: SelectClassVC -> > XGGSelectClassVC
  找到引用文件:
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m

处理类名映射: CommodityClassModel -> > XGGCommodityClassModel
  找到引用文件:
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.m
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m

处理类名映射: CommodityClassCell -> > XGGCommodityClassCell
  找到引用文件:
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h
    YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m

处理类名映射: ConfirmOrderVC -> > XGGConfirmOrderVC
  找到引用文件:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m

处理类名映射: HistoryListModel -> > XGGHistoryListModel
  找到引用文件:
    YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.m

处理类名映射: LookHistoryCell -> > XGGLookHistoryCell
  找到引用文件:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.m

处理类名映射: LookHistoryVC -> > XGGLookHistoryVC
  找到引用文件:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m

处理类名映射: LookHistoryModel -> > XGGLookHistoryModel
  找到引用文件:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.m
    YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.h

处理类名映射: OutsideGoodsDetailVC -> > XGGOutsideGoodsDetailVC
  找到引用文件:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.m
    YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 更新成功: YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.m
    ✅ 更新成功: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m

处理类名映射: OutsideHeadCell -> > XGGOutsideHeadCell
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m

处理类名映射: ShareGoodsAlert -> > XGGShareGoodsAlert
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: ShareFriendVC -> > XGGShareFriendVC
  找到引用文件:
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m

处理类名映射: ShareFriendCell -> > XGGShareFriendCell
  找到引用文件:
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.h

处理类名映射: FriendModel -> > XGGFriendModel
  找到引用文件:
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.h
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.h

处理类名映射: ShareGoodView -> > XGGShareGoodView
  找到引用文件:
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.h
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.h
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.h
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.h
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m

处理类名映射: PlatformInterventionVC -> > XGGPlatformInterventionVC
  找到引用文件:
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m

处理类名映射: PublishEvaluateVC -> > XGGPublishEvaluateVC
  找到引用文件:
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m

处理类名映射: ClassToExamineVC -> > XGGClassToExamineVC
  找到引用文件:
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m

处理类名映射: ClassificationVC -> > XGGClassificationVC
  找到引用文件:
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.h
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m

处理类名映射: StandardsCell -> > XGGStandardsCell
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.h
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGStandardsCell.m

处理类名映射: SelectStandardsView -> > XGGSelectStandardsView
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: GuaranteeView -> > XGGGuaranteeView
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.h
    YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/服务保障/XGGGuaranteeView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: sliderCollectionVCell -> > XGGsliderCollectionVCell
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.h
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.m
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionVCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.m

处理类名映射: sliderCollectionView -> > XGGsliderCollectionView
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.m

处理类名映射: CommodityDetailVC -> > XGGCommodityDetailVC
  找到引用文件:
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.m
    YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 更新成功: YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.m
    ✅ 更新成功: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m

处理类名映射: ShowDetailVC -> > XGGShowDetailVC
  找到引用文件:
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.m
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m

处理类名映射: GoodsExplainCell -> > XGGGoodsExplainCell
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.h
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: CommodityCell3 -> > XGGCommodityCell3
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: CommodityCell2Row2 -> > XGGCommodityCell2Row2
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: CommodityCell2Row1 -> > XGGCommodityCell2Row1
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: CommodityCell1 -> > XGGCommodityCell1
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: CommodityDetailModel -> > XGGCommodityDetailModel
  找到引用文件:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.h
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.m
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.h
    YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.h
    YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.h
    YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.h
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.h
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.h
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m

处理类名映射: YBGoodPlayerCtrView -> > XGGYBGoodPlayerCtrView
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m
    YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.h
    YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.m
    YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGsliderCollectionView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.m

处理类名映射: StoreInfoView -> > XGGStoreInfoView
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
    YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.h
    YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m

处理类名映射: GoodsDetailVC -> > XGGGoodsDetailVC
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.h

处理类名映射: CommodityEvaluationCell -> > XGGCommodityEvaluationCell
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m

处理类名映射: PayOrderView -> > XGGPayOrderView
  找到引用文件:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.h
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 更新成功: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m

处理类名映射: AppendEvaluateVC -> > XGGAppendEvaluateVC
  找到引用文件:
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m

处理类名映射: ApplyShopVC -> > XGGApplyShopVC
  找到引用文件:
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.h
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m

处理类名映射: ShopApplyStatusVC -> > XGGShopApplyStatusVC
  找到引用文件:
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.h
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m

处理类名映射: BondViewController -> > XGGBondViewController
  找到引用文件:
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    YBVideo//店铺/买家端/保证金/XGGBondViewController.h
    YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/保证金/XGGBondViewController.h
    ✅ 更新成功: YBVideo//店铺/买家端/保证金/XGGBondViewController.m

处理类名映射: AddressModel -> > XGGAddressModel
  找到引用文件:
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/我的地址/XGGAddressModel.h
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
    YBVideo//店铺/买家端/我的地址/XGGAddressModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressModel.m

处理类名映射: EditAdressVC -> > XGGEditAdressVC
  找到引用文件:
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m

处理类名映射: RejectAddressModel -> > XGGRejectAddressModel
  找到引用文件:
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m
    YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.h
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h

处理类名映射: AddressCell -> > XGGAddressCell
  找到引用文件:
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressCell.h

处理类名映射: AddressVC -> > XGGAddressVC
  找到引用文件:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m

处理类名映射: GoodsEvaluationListVC -> > XGGGoodsEvaluationListVC
  找到引用文件:
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m

处理类名映射: EvaluationListModel -> > XGGEvaluationListModel
  找到引用文件:
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.m
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.h
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.h

处理类名映射: EvaluationListCell -> > XGGEvaluationListCell
  找到引用文件:
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.h

处理类名映射: BuyerGetMoneyVC -> > XGGBuyerGetMoneyVC
  找到引用文件:
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.h
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m

处理类名映射: BuyerRefundDetailVC -> > XGGBuyerRefundDetailVC
  找到引用文件:
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m

处理类名映射: BuyerRefundModel -> > XGGBuyerRefundModel
  找到引用文件:
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.h
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.m
    YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.h
    YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.h
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.m

处理类名映射: BuyerRefundHeadView -> > XGGBuyerRefundHeadView
  找到引用文件:
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.h
    YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.h
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.m

处理类名映射: OrderDetailVC -> > XGGOrderDetailVC
  找到引用文件:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.h
    YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.h
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m

处理类名映射: OrderDetailModel -> > XGGOrderDetailModel
  找到引用文件:
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m

处理类名映射: OrderPublicView -> > XGGOrderPublicView
  找到引用文件:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m

处理类名映射: OrderHeaderView -> > XGGOrderHeaderView
  找到引用文件:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m

处理类名映射: OrderInfoView -> > XGGOrderInfoView
  找到引用文件:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m

处理类名映射: OrderPriceView -> > XGGOrderPriceView
  找到引用文件:
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m

处理类名映射: OrderListCell -> > XGGOrderListCell
  找到引用文件:
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListCell.h
    YBVideo//店铺/买家端/我的订单/XGGOrderListCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListCell.m

处理类名映射: OrderModel -> > XGGOrderModel
  找到引用文件:
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
    YBVideo//店铺/买家端/我的订单/XGGOrderModel.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListCell.h
    YBVideo//店铺/买家端/我的订单/XGGOrderModel.h
    YBVideo//店铺/买家端/我的订单/XGGOrderListCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderModel.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListCell.m

处理类名映射: OrderListVC -> > XGGOrderListVC
  找到引用文件:
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.h
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m

处理类名映射: AccountBalanceVC -> > XGGAccountBalanceVC
  找到引用文件:
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m

处理类名映射: ShopHomeVC -> > XGGShopHomeVC
  找到引用文件:
    YBVideo//店铺/小店主页/XGGShopHomeVC.h
    YBVideo//店铺/小店主页/XGGShopHomeVC.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//店铺/小店主页/XGGShopHomeVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/XGGShopHomeVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: SellerView -> > XGGSellerView
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.h
    YBVideo//店铺/小店主页/XGGShopHomeVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.h
    ✅ 更新成功: YBVideo//店铺/小店主页/XGGShopHomeVC.m

处理类名映射: BuyerView -> > XGGBuyerView
  找到引用文件:
    YBVideo//店铺/小店主页/XGGShopHomeVC.m
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.h
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/小店主页/XGGShopHomeVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.h
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m

处理类名映射: GetMoneyVC -> > XGGGetMoneyVC
  找到引用文件:
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.h
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m

处理类名映射: CommodityCell -> > XGGCommodityCell
  找到引用文件:
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.m

处理类名映射: CommodityModel -> > XGGCommodityModel
  找到引用文件:
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h
    YBVideo//店铺/卖家端/商品管理/XGGCommodityModel.h
    YBVideo//店铺/卖家端/商品管理/XGGCommodityModel.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.m
    YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityModel.h
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityModel.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.h

处理类名映射: CommodityManagementVC -> > XGGCommodityManagementVC
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.h

处理类名映射: OtherSellOrderDetailVC -> > XGGOtherSellOrderDetailVC
  找到引用文件:
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m

处理类名映射: SellOrderDetailModel -> > XGGSellOrderDetailModel
  找到引用文件:
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    YBVideo//店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.m
    YBVideo//店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.h
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m
    YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.h
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.h

处理类名映射: EditSaveAddressVC -> > XGGEditSaveAddressVC
  找到引用文件:
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h

处理类名映射: SellerOrderManagementVC -> > XGGSellerOrderManagementVC
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.h
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m

处理类名映射: SellOrderCell -> > XGGSellOrderCell
  找到引用文件:
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.h

处理类名映射: SellOrderModel -> > XGGSellOrderModel
  找到引用文件:
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderModel.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.h
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderModel.h
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.h
    YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderModel.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderModel.h
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.h
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m

处理类名映射: RefundDetailVC -> > XGGRefundDetailVC
  找到引用文件:
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m

处理类名映射: RefundDetailModel -> > XGGRefundDetailModel
  找到引用文件:
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.h
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.m
    YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.h
    YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.h
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailModel.m
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.m

处理类名映射: RefuseRefundVC -> > XGGRefuseRefundVC
  找到引用文件:
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.h
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m

处理类名映射: PlatformListCell -> > XGGPlatformListCell
  找到引用文件:
    YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.h
    YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.m
    YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformListCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m

处理类名映射: PlatformGoodsVC -> > XGGPlatformGoodsVC
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.h
    YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m

处理类名映射: QualificationsVC -> > XGGQualificationsVC
  找到引用文件:
    YBVideo//店铺/卖家端/资质/XGGQualificationsVC.m
    YBVideo//店铺/卖家端/资质/XGGQualificationsVC.h
    YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/资质/XGGQualificationsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/资质/XGGQualificationsVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.m

处理类名映射: EditStockVC -> > XGGEditStockVC
  找到引用文件:
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.h
    YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.m

处理类名映射: StockView -> > XGGStockView
  找到引用文件:
    YBVideo//店铺/卖家端/价格与库存/XGGStockView.m
    YBVideo//店铺/卖家端/价格与库存/XGGStockView.h
    YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/价格与库存/XGGStockView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/价格与库存/XGGStockView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/价格与库存/XGGEditStockVC.m

处理类名映射: BillManageVC -> > XGGBillManageVC
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.h
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m

处理类名映射: BillCell -> > XGGBillCell
  找到引用文件:
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    YBVideo//店铺/卖家端/账单管理/XGGBillCell.m
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    YBVideo//店铺/卖家端/账单管理/XGGBillCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillCell.h

处理类名映射: AddCommodityVC -> > XGGAddCommodityVC
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.h
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m

处理类名映射: SelCommodityClassVC -> > XGGSelCommodityClassVC
  找到引用文件:
    YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.h
    YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m

处理类名映射: StandardsView -> > XGGStandardsView
  找到引用文件:
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGStandardsView.h
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGStandardsView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGStandardsView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGStandardsView.m

处理类名映射: CommodityTitleView -> > XGGCommodityTitleView
  找到引用文件:
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.h
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m

处理类名映射: CommodityDetailView -> > XGGCommodityDetailView
  找到引用文件:
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h

处理类名映射: RelationVideoGoodsVC -> > XGGRelationVideoGoodsVC
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.h

处理类名映射: RelationGoodsModel -> > XGGRelationGoodsModel
  找到引用文件:
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsModel.h
    YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.h
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsModel.m
    YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsModel.h
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsModel.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m

处理类名映射: goodsShowCell -> > XGGgoodsShowCell
  找到引用文件:
    YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.h
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m

处理类名映射: shopDetailVC -> > XGGshopDetailVC
  找到引用文件:
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListCell.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.h
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: shopCell -> > XGGshopCell
  找到引用文件:
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopCell.m
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopCell.h

处理类名映射: AddGoodsVC -> > XGGAddGoodsVC
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.h
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.h

处理类名映射: RelationGoodsVC -> > XGGRelationGoodsVC
  找到引用文件:
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.h
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m

处理类名映射: PlatformCell -> > XGGPlatformCell
  找到引用文件:
    YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGPlatformCell.h

处理类名映射: GoodsDetailViewController -> > XGGGoodsDetailViewController
  找到引用文件:
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.h
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGGoodsDetailViewController.h

处理类名映射: SendGoodsInfo -> > XGGSendGoodsInfo
  找到引用文件:
    YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.h
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.m
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.h
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGSendGoodsInfo.m

处理类名映射: LogisticsCell -> > XGGLogisticsCell
  找到引用文件:
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.h
    YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.h
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGLogisticsCell.m

处理类名映射: WaitSendGoodsVC -> > XGGWaitSendGoodsVC
  找到引用文件:
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.h
    YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.h
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m

处理类名映射: ShopInfoVC -> > XGGShopInfoVC
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.h
    YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/小店详情/XGGShopInfoVC.m

处理类名映射: RefundHeadView -> > XGGRefundHeadView
  找到引用文件:
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.h
    YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGRefundHeadView.m

处理类名映射: SellOrderPublicView -> > XGGSellOrderPublicView
  找到引用文件:
    YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m
    YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.h

处理类名映射: AddOtherSaleGoodsVC -> > XGGAddOtherSaleGoodsVC
  找到引用文件:
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
    YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.h
    ✅ 更新成功: YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.h

处理类名映射: RKLBSManager -> > XGGRKLBSManager
  找到引用文件:
    YBVideo//其他类/XGGRKLBSManager.h
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//缓存/定位/XGGRKLBSManager.h
    YBVideo//缓存/定位/XGGRKLBSManager.m
    YBVideo//功能/极光消息/单聊/View/JCHATMessageContentView.m
    YBVideo//功能/极光消息/发送位置/XGGTencentLocationVC.m
    YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.m
    YBVideo//附近/XGGNearbyVC.m
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//其他类/XGGRKLBSManager.h
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//缓存/定位/XGGRKLBSManager.h
    ✅ 更新成功: YBVideo//缓存/定位/XGGRKLBSManager.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/JCHATMessageContentView.m
    ✅ 更新成功: YBVideo//功能/极光消息/发送位置/XGGTencentLocationVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m

处理类名映射: common -> > XGGcommon
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//其他类/common.h
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//公共方法类/XGGBGSetting.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//公共方法类/XGGYBNetworking.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    YBVideo//缓存/配置信息/XGGcommon.m
    YBVideo//缓存/配置信息/XGGcommon.h
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.m
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/会员/view/XGGvipBuyView.m
    YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.m
    YBVideo//功能/钱包/XGGYBRechargeVC.m
    YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    YBVideo//功能/腾讯消息/IM管理/XGGYBImManager.m
    YBVideo//功能/腾讯消息/IM管理/工具资源/TUIKitConfig.m
    YBVideo//功能/直播/守护/XGGguardShowView.m
    YBVideo//功能/直播/守护/view/XGGguardListCell.m
    YBVideo//功能/直播/守护/XGGshouhuView.m
    YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.m
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m
    YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.m
    YBVideo//功能/直播/管理员列表/XGGadminCell.m
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    YBVideo//功能/直播/幸运转盘/XGGturntableResultView.m
    YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    YBVideo//功能/分享/发布分享/XGGPublishShareV.m
    YBVideo//功能/分享/观看分享/XGGYBShareView.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/common.h
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 更新成功: YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    ✅ 更新成功: YBVideo//缓存/配置信息/XGGcommon.m
    ✅ 更新成功: YBVideo//缓存/配置信息/XGGcommon.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    ✅ 更新成功: YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/会员/view/XGGvipBuyView.m
    ✅ 更新成功: YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.m
    ✅ 更新成功: YBVideo//功能/钱包/XGGYBRechargeVC.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/IM管理/XGGYBImManager.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/IM管理/工具资源/TUIKitConfig.m
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGguardShowView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGguardListCell.m
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGshouhuView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.m
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminCell.m
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableResultView.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    ✅ 更新成功: YBVideo//功能/分享/发布分享/XGGPublishShareV.m
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m

处理类名映射: Config -> > XGGConfig
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//其他类/Config.h
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//XGGNetwork/XGGNetworkManager.m
    YBVideo//公共方法类/XGGiOSNetworking.m
    YBVideo//公共方法类/XGGYBImageView.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//公共方法类/XGGYBNetworking.m
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//其他类/Config.h
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//XGGNetwork/XGGNetworkManager.m
    ✅ 更新成功: YBVideo//公共方法类/XGGiOSNetworking.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m

处理类名映射: YBTabBarController -> > XGGYBTabBarController
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//公共方法类/XGGPublicObj.h
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//底部导航/XGGYBTabBarController.h
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    YBVideo//功能/语言包/XGGYBLanguageTools.m
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    YBVideo//功能/腾讯消息/IM管理/XGGYBImManager.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.h
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    ✅ 更新成功: YBVideo//功能/语言包/XGGYBLanguageTools.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/IM管理/XGGYBImManager.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: YBLiveOrVideo -> > XGGYBLiveOrVideo
  找到引用文件:
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.h
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.h
    ✅ 更新成功: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m

处理类名映射: YBTabBar -> > XGGYBTabBar
  找到引用文件:
    YBVideo//底部导航/XGGYBTabBar.m
    YBVideo//底部导航/XGGYBTabBar.h
    YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBar.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBar.h
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m

处理类名映射: MessageFansVC -> > XGGMessageFansVC
  找到引用文件:
    YBVideo//消息/XGGMessageFansVC.h
    YBVideo//消息/XGGMessageFansVC.m
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    ✅ 更新成功: YBVideo//消息/XGGMessageFansVC.h
    ✅ 更新成功: YBVideo//消息/XGGMessageFansVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m

处理类名映射: SelPeopleCell -> > XGGSelPeopleCell
  找到引用文件:
    YBVideo//消息/选择联系人/XGGSelPeopleCell.h
    YBVideo//消息/选择联系人/XGGSelPeopleCell.m
    YBVideo//消息/选择联系人/XGGSelPeopleV.m
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleCell.h
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleCell.m
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleV.m

处理类名映射: SelPeopleV -> > XGGSelPeopleV
  找到引用文件:
    YBVideo//消息/选择联系人/XGGSelPeopleV.h
    YBVideo//消息/选择联系人/XGGSelPeopleV.m
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleV.h
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleV.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m

处理类名映射: MsgSysModel -> > XGGMsgSysModel
  找到引用文件:
    YBVideo//消息/XGGMsgSysVC.m
    YBVideo//消息/model/XGGMsgSysModel.h
    YBVideo//消息/model/XGGMsgSysModel.m
    YBVideo//消息/view/XGGMsgSysCell.h
    YBVideo//消息/view/XGGMsgSysCell.m
    ✅ 更新成功: YBVideo//消息/XGGMsgSysVC.m
    ✅ 更新成功: YBVideo//消息/model/XGGMsgSysModel.h
    ✅ 更新成功: YBVideo//消息/model/XGGMsgSysModel.m
    ✅ 更新成功: YBVideo//消息/view/XGGMsgSysCell.h
    ✅ 更新成功: YBVideo//消息/view/XGGMsgSysCell.m

处理类名映射: MessageListModel -> > XGGMessageListModel
  找到引用文件:
    YBVideo//消息/选择联系人/XGGSelPeopleV.h
    YBVideo//消息/选择联系人/XGGSelPeopleV.m
    YBVideo//消息/model/XGGMsgSysModel.h
    YBVideo//消息/model/XGGMessageListModel.h
    YBVideo//消息/model/XGGMsgSysModel.m
    YBVideo//消息/model/XGGMessageListModel.m
    YBVideo//消息/view/XGGMessageListCell.m
    YBVideo//消息/view/XGGMessageListCell.h
    YBVideo//消息/view/MessageCell.m
    YBVideo//消息/MessageVC.m
    YBVideo//消息/XGGMsgSysVC.h
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/极光消息/单聊/JCHATConversationViewController.h
    YBVideo//功能/极光消息/单聊/Model/JCHATChatModel.h
    YBVideo//功能/极光消息/单聊/Model/JCHATChatModel.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    YBVideo//个人中心/XGGYBCenterVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleV.h
    ✅ 更新成功: YBVideo//消息/选择联系人/XGGSelPeopleV.m
    ✅ 更新成功: YBVideo//消息/model/XGGMsgSysModel.h
    ✅ 更新成功: YBVideo//消息/model/XGGMessageListModel.h
    ✅ 更新成功: YBVideo//消息/model/XGGMsgSysModel.m
    ✅ 更新成功: YBVideo//消息/model/XGGMessageListModel.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageListCell.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageListCell.h
    ✅ 更新成功: YBVideo//消息/view/MessageCell.m
    ✅ 更新成功: YBVideo//消息/MessageVC.m
    ✅ 更新成功: YBVideo//消息/XGGMsgSysVC.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.h
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/Model/JCHATChatModel.h
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/Model/JCHATChatModel.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    ✅ 更新成功: YBVideo//个人中心/XGGYBCenterVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: MessageFansModel -> > XGGMessageFansModel
  找到引用文件:
    YBVideo//消息/XGGMessageFansVC.m
    YBVideo//消息/model/XGGMessageFansModel.h
    YBVideo//消息/model/XGGMessageFansModel.m
    YBVideo//消息/view/XGGMessageFansCell.h
    YBVideo//消息/view/XGGMessageFansCell.m
    ✅ 更新成功: YBVideo//消息/XGGMessageFansVC.m
    ✅ 更新成功: YBVideo//消息/model/XGGMessageFansModel.h
    ✅ 更新成功: YBVideo//消息/model/XGGMessageFansModel.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageFansCell.h
    ✅ 更新成功: YBVideo//消息/view/XGGMessageFansCell.m

处理类名映射: MsgTopPubModel -> > XGGMsgTopPubModel
  找到引用文件:
    YBVideo//消息/XGGMsgTopPubVC.m
    YBVideo//消息/model/XGGMsgTopPubModel.m
    YBVideo//消息/model/XGGMsgTopPubModel.h
    YBVideo//消息/view/XGGMsgTopPubCell.m
    YBVideo//消息/view/XGGMsgTopPubCell.h
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 更新成功: YBVideo//消息/model/XGGMsgTopPubModel.m
    ✅ 更新成功: YBVideo//消息/model/XGGMsgTopPubModel.h
    ✅ 更新成功: YBVideo//消息/view/XGGMsgTopPubCell.m
    ✅ 更新成功: YBVideo//消息/view/XGGMsgTopPubCell.h

处理类名映射: MessageFansCell -> > XGGMessageFansCell
  找到引用文件:
    YBVideo//消息/XGGMessageFansVC.m
    YBVideo//消息/view/XGGMessageFansCell.h
    YBVideo//消息/view/XGGMessageFansCell.m
    ✅ 更新成功: YBVideo//消息/XGGMessageFansVC.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageFansCell.h
    ✅ 更新成功: YBVideo//消息/view/XGGMessageFansCell.m

处理类名映射: MsgSysCell -> > XGGMsgSysCell
  找到引用文件:
    YBVideo//消息/XGGMsgSysVC.m
    YBVideo//消息/view/XGGMsgSysCell.h
    YBVideo//消息/view/XGGMsgSysCell.m
    ✅ 更新成功: YBVideo//消息/XGGMsgSysVC.m
    ✅ 更新成功: YBVideo//消息/view/XGGMsgSysCell.h
    ✅ 更新成功: YBVideo//消息/view/XGGMsgSysCell.m

处理类名映射: MessageListCell -> > XGGMessageListCell
  找到引用文件:
    YBVideo//消息/view/XGGMessageListCell.m
    YBVideo//消息/view/XGGMessageListCell.h
    YBVideo//消息/view/MessageCell.m
    YBVideo//消息/MessageVC.m
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageListCell.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageListCell.h
    ✅ 更新成功: YBVideo//消息/view/MessageCell.m
    ✅ 更新成功: YBVideo//消息/MessageVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m

处理类名映射: MessageHeaderV -> > XGGMessageHeaderV
  找到引用文件:
    YBVideo//消息/view/XGGMessageHeaderV.m
    YBVideo//消息/view/XGGMessageHeaderV.h
    YBVideo//消息/MessageVC.m
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageHeaderV.m
    ✅ 更新成功: YBVideo//消息/view/XGGMessageHeaderV.h
    ✅ 更新成功: YBVideo//消息/MessageVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m

处理类名映射: MsgTopPubCell -> > XGGMsgTopPubCell
  找到引用文件:
    YBVideo//消息/XGGMsgTopPubVC.m
    YBVideo//消息/view/XGGMsgTopPubCell.m
    YBVideo//消息/view/XGGMsgTopPubCell.h
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 更新成功: YBVideo//消息/view/XGGMsgTopPubCell.m
    ✅ 更新成功: YBVideo//消息/view/XGGMsgTopPubCell.h

处理类名映射: OrderMessageVC -> > XGGOrderMessageVC
  找到引用文件:
    YBVideo//消息/XGGOrderMessageVC.m
    YBVideo//消息/XGGOrderMessageVC.h
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    ✅ 更新成功: YBVideo//消息/XGGOrderMessageVC.m
    ✅ 更新成功: YBVideo//消息/XGGOrderMessageVC.h
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m

处理类名映射: chatmessageCell -> > XGGchatmessageCell
  找到引用文件:
    YBVideo//消息/XGGchatmessageCell.m
    YBVideo//消息/XGGOrderMessageVC.m
    YBVideo//消息/XGGchatmessageCell.h
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//消息/XGGOrderMessageVC.m
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.h

处理类名映射: OrderMessageModel -> > XGGOrderMessageModel
  找到引用文件:
    YBVideo//消息/XGGchatmessageCell.m
    YBVideo//消息/XGGOrderMessageVC.m
    YBVideo//消息/XGGOrderMessageModel.m
    YBVideo//消息/XGGchatmessageCell.h
    YBVideo//消息/XGGOrderMessageModel.h
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.m
    ✅ 更新成功: YBVideo//消息/XGGOrderMessageVC.m
    ✅ 更新成功: YBVideo//消息/XGGOrderMessageModel.m
    ✅ 更新成功: YBVideo//消息/XGGchatmessageCell.h
    ✅ 更新成功: YBVideo//消息/XGGOrderMessageModel.h

处理类名映射: MsgSysVC -> > XGGMsgSysVC
  找到引用文件:
    YBVideo//消息/XGGMsgSysVC.m
    YBVideo//消息/XGGMsgSysVC.h
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    ✅ 更新成功: YBVideo//消息/XGGMsgSysVC.m
    ✅ 更新成功: YBVideo//消息/XGGMsgSysVC.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m

处理类名映射: MsgTopPubVC -> > XGGMsgTopPubVC
  找到引用文件:
    YBVideo//消息/XGGMsgTopPubVC.m
    YBVideo//消息/XGGMsgTopPubVC.h
    YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.h
    ✅ 更新成功: YBVideo//功能/腾讯消息/XGGYBMsgC2CListVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m

处理类名映射: YBPlayVC -> > XGGYBPlayVC
  找到引用文件:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.h
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.m

处理类名映射: YBPlayCtrlView -> > XGGYBPlayCtrlView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m

处理类名映射: YBCheckLiveObj -> > XGGYBCheckLiveObj
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.m
    YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.h
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/直播榜单/XGGLiveRankVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.h
    ✅ 更新成功: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/直播榜单/XGGLiveRankVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: YBLiveListVC -> > XGGYBLiveListVC
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.h
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/XGGYBLiveListVC.h
    ✅ 更新成功: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m

处理类名映射: YBLiveListCell -> > XGGYBLiveListCell
  找到引用文件:
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.h
    YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.h
    ✅ 更新成功: YBVideo//直播模块/直播列表/view/XGGYBLiveListCell.m

处理类名映射: YBLiveRoomAlertView -> > XGGYBLiveRoomAlertView
  找到引用文件:
    YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m
    YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.h
    YBVideo//直播模块/socket/XGGYBSocketLive.m
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    ✅ 更新成功: YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.m
    ✅ 更新成功: YBVideo//直播模块/房间警告/XGGYBLiveRoomAlertView.h
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketLive.m
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m

处理类名映射: YBLiveRTCManager -> > XGGYBLiveRTCManager
  找到引用文件:
    YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.h
    YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.m
    ✅ 更新成功: YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.h
    ✅ 更新成功: YBVideo//直播模块/RTCPush/XGGYBLiveRTCManager.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.m

处理类名映射: YBLiveEndView -> > XGGYBLiveEndView
  找到引用文件:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.h
    YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.h
    ✅ 更新成功: YBVideo//直播模块/主播-用户公用/XGGYBLiveEndView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m

处理类名映射: YBChatToolBar -> > XGGYBChatToolBar
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h
    YBVideo//直播模块/主播-用户公用/XGGYBChatToolBar.m
    YBVideo//直播模块/主播-用户公用/XGGYBChatToolBar.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.h
    ✅ 更新成功: YBVideo//直播模块/主播-用户公用/XGGYBChatToolBar.m
    ✅ 更新成功: YBVideo//直播模块/主播-用户公用/XGGYBChatToolBar.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m

处理类名映射: roomShowGoodsView -> > XGGroomShowGoodsView
  找到引用文件:
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m

处理类名映射: YBLiveVC -> > XGGYBLiveVC
  找到引用文件:
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.h
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m

处理类名映射: YBLiveFucView -> > YBLiveFunView
  找到引用文件:
    YBVideo//直播模块/主播端相关/YBLiveFunView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/YBLiveFunView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.h

处理类名映射: startLiveClassCell -> > XGGstartLiveClassCell
  找到引用文件:
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.h
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m

处理类名映射: startLiveClassVC -> > XGGstartLiveClassVC
  找到引用文件:
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.h
    YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m

处理类名映射: YBLiveCtrlView -> > XGGYBLiveCtrlView
  找到引用文件:
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m

处理类名映射: YBLivePreview -> > XGGYBLivePreview
  找到引用文件:
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.h
    YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m

处理类名映射: YBLiveFucView -> > XGGYBLiveFucView
  找到引用文件:
    YBVideo//直播模块/主播端相关/YBLiveFunView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/YBLiveFunView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveFucView.h

处理类名映射: YBSocketPlay -> > XGGYBSocketPlay
  找到引用文件:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//直播模块/socket/XGGYBSocketPlay.h
    YBVideo//直播模块/socket/XGGYBSocketPlay.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketPlay.h
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketPlay.m

处理类名映射: YBSocketLive -> > XGGYBSocketLive
  找到引用文件:
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//直播模块/socket/XGGYBSocketLive.m
    YBVideo//直播模块/socket/XGGYBSocketLive.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketLive.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketLive.h

处理类名映射: YBVipVC -> > XGGYBVipVC
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    YBVideo//功能/会员/XGGYBVipVC.h
    YBVideo//功能/会员/XGGYBVipVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    ✅ 更新成功: YBVideo//功能/会员/XGGYBVipVC.h
    ✅ 更新成功: YBVideo//功能/会员/XGGYBVipVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: vipBuyView -> > XGGvipBuyView
  找到引用文件:
    YBVideo//功能/会员/view/XGGvipBuyView.h
    YBVideo//功能/会员/view/XGGvipBuyView.m
    YBVideo//功能/会员/XGGYBVipVC.m
    ✅ 更新成功: YBVideo//功能/会员/view/XGGvipBuyView.h
    ✅ 更新成功: YBVideo//功能/会员/view/XGGvipBuyView.m
    ✅ 更新成功: YBVideo//功能/会员/XGGYBVipVC.m

处理类名映射: YBVipCell -> > XGGYBVipCell
  找到引用文件:
    YBVideo//功能/会员/view/XGGYBVipCell.m
    YBVideo//功能/会员/view/XGGYBVipCell.h
    YBVideo//功能/会员/XGGYBVipVC.m
    ✅ 更新成功: YBVideo//功能/会员/view/XGGYBVipCell.m
    ✅ 更新成功: YBVideo//功能/会员/view/XGGYBVipCell.h
    ✅ 更新成功: YBVideo//功能/会员/XGGYBVipVC.m

处理类名映射: YBVipHeader -> > XGGYBVipHeader
  找到引用文件:
    YBVideo//功能/会员/view/XGGYBVipHeader.m
    YBVideo//功能/会员/view/XGGYBVipHeader.h
    YBVideo//功能/会员/XGGYBVipVC.m
    ✅ 更新成功: YBVideo//功能/会员/view/XGGYBVipHeader.m
    ✅ 更新成功: YBVideo//功能/会员/view/XGGYBVipHeader.h
    ✅ 更新成功: YBVideo//功能/会员/XGGYBVipVC.m

处理类名映射: YBRechargeVC -> > XGGYBRechargeVC
  找到引用文件:
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    YBVideo//功能/钱包/XGGYBRechargeVC.h
    YBVideo//功能/钱包/XGGYBRechargeVC.m
    YBVideo//功能/上热门/XGGaddHotVideoVC.m
    YBVideo//功能/礼物/XGGYBGiftView.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    ✅ 更新成功: YBVideo//功能/钱包/XGGYBRechargeVC.h
    ✅ 更新成功: YBVideo//功能/钱包/XGGYBRechargeVC.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGaddHotVideoVC.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m

处理类名映射: YBRechargeType -> > XGGYBRechargeType
  找到引用文件:
    YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    YBVideo//功能/会员/view/XGGvipBuyView.m
    YBVideo//功能/会员/XGGYBVipVC.m
    YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.m
    YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.h
    YBVideo//功能/钱包/XGGYBRechargeVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 更新成功: YBVideo//功能/会员/view/XGGvipBuyView.m
    ✅ 更新成功: YBVideo//功能/会员/XGGYBVipVC.m
    ✅ 更新成功: YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.m
    ✅ 更新成功: YBVideo//功能/钱包/支付公共方法/XGGYBRechargeType.h
    ✅ 更新成功: YBVideo//功能/钱包/XGGYBRechargeVC.m

处理类名映射: fansModel -> > XGGfansModel
  找到引用文件:
    YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.h
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.m
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGfans.h
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.h
    YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.m
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    YBVideo//功能/粉丝_关注_拉黑/XGGfans.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/直播/管理员列表/XGGadminCell.h
    YBVideo//功能/直播/管理员列表/XGGadminCell.m
    YBVideo//功能/直播/管理员列表/XGGadminLists.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.h
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/blackListCell.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfans.h
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/blackListCell.h
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfans.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminCell.h
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminCell.m
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminLists.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m

处理类名映射: BlackListVC -> > XGGBlackListVC
  找到引用文件:
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.h
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.h
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: fans -> > XGGfans
  找到引用文件:
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGfans.h
    YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.m
    YBVideo//功能/粉丝_关注_拉黑/XGGfans.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfans.h
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansModel.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfans.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: BlackListCell -> > blackListCell
  找到引用文件:
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.m
    YBVideo//功能/粉丝_关注_拉黑/blackListCell.h
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/blackListCell.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/blackListCell.h
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m

处理类名映射: fansViewController -> > XGGfansViewController
  找到引用文件:
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.h
    YBVideo//功能/粉丝_关注_拉黑/XGGfans.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.h
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfans.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: attrViewController -> > XGGattrViewController
  找到引用文件:
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.h
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.h
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: PubH5 -> > XGGPubH5
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//引导页/XGGGuideViewController.m
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//消息/XGGMsgSysVC.m
    YBVideo//功能/钱包/XGGYBRechargeVC.m
    YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//功能/H5/XGGPubH5.h
    YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//引导页/XGGGuideViewController.m
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/订单管理/XGGSellOrderCell.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/XGGRefundDetailVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    ✅ 更新成功: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//消息/XGGMsgSysVC.m
    ✅ 更新成功: YBVideo//功能/钱包/XGGYBRechargeVC.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.h
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: addHotVideoVC -> > XGGaddHotVideoVC
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//功能/上热门/XGGaddHotVideoVC.h
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    YBVideo//功能/上热门/XGGaddHotVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGaddHotVideoVC.h
    ✅ 更新成功: YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGaddHotVideoVC.m

处理类名映射: UpHotCell -> > XGGUpHotCell
  找到引用文件:
    YBVideo//功能/上热门/XGGUpHotCell.m
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    YBVideo//功能/上热门/XGGUpHotCell.h
    ✅ 更新成功: YBVideo//功能/上热门/XGGUpHotCell.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGUpHotCell.h

处理类名映射: HotVideoDetailVC -> > XGGHotVideoDetailVC
  找到引用文件:
    YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    YBVideo//功能/上热门/XGGaddHotVideoVC.m
    YBVideo//功能/上热门/XGGHotVideoDetailVC.h
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGaddHotVideoVC.m
    ✅ 更新成功: YBVideo//功能/上热门/XGGHotVideoDetailVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m

处理类名映射: Loginbonus -> > XGGLoginbonus
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//功能/登录奖励/XGGLoginbonus.h
    YBVideo//功能/登录奖励/XGGLoginbonus.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLoginbonus.h
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLoginbonus.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: LogFirstCell -> > XGGLogFirstCell
  找到引用文件:
    YBVideo//功能/登录奖励/XGGLogFirstCell.m
    YBVideo//功能/登录奖励/XGGLogFirstCell.h
    YBVideo//功能/登录奖励/XGGLoginbonus.m
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLogFirstCell.m
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLogFirstCell.h
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLoginbonus.m

处理类名映射: LogFirstCell2 -> > XGGLogFirstCell2
  找到引用文件:
    YBVideo//功能/登录奖励/XGGLogFirstCell2.m
    YBVideo//功能/登录奖励/XGGLoginbonus.m
    YBVideo//功能/登录奖励/XGGLogFirstCell2.h
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLogFirstCell2.m
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLoginbonus.m
    ✅ 更新成功: YBVideo//功能/登录奖励/XGGLogFirstCell2.h

处理类名映射: searchVC -> > XGGsearchVC
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.h
    YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.h
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m

处理类名映射: SearchHistoryCell -> > XGGSearchHistoryCell
  找到引用文件:
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.h
    YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.h
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/view/XGGSearchHistoryCell.m

处理类名映射: YBSearchBarView -> > XGGYBSearchBarView
  找到引用文件:
    YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.h
    YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.m
    ✅ 更新成功: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.h
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.m

处理类名映射: StorageConfig -> > XGGStorageConfig
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//功能/存储功能类/XGGStorageConfig.h
    YBVideo//功能/存储功能类/XGGYBStorageObj.m
    YBVideo//功能/存储功能类/XGGStorageConfig.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//功能/存储功能类/XGGStorageConfig.h
    ✅ 更新成功: YBVideo//功能/存储功能类/XGGYBStorageObj.m
    ✅ 更新成功: YBVideo//功能/存储功能类/XGGStorageConfig.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: YBStorageObj -> > XGGYBStorageObj
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    YBVideo//功能/存储功能类/XGGYBStorageObj.m
    YBVideo//功能/存储功能类/XGGYBStorageObj.h
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/XGGAddCommodityVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
    ✅ 更新成功: YBVideo//功能/存储功能类/XGGYBStorageObj.m
    ✅ 更新成功: YBVideo//功能/存储功能类/XGGYBStorageObj.h
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: detailmodel -> > XGGdetailmodel
  找到引用文件:
    YBVideo//功能/评论/XGGcommDetailCell.m
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/XGGdetailmodel.h
    YBVideo//功能/评论/XGGcommCell.m
    YBVideo//功能/评论/XGGcommDetailCell.h
    YBVideo//功能/评论/XGGdetailmodel.m
    YBVideo//功能/评论/XGGcommCell.h
    ✅ 更新成功: YBVideo//功能/评论/XGGcommDetailCell.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/XGGdetailmodel.h
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommDetailCell.h
    ✅ 更新成功: YBVideo//功能/评论/XGGdetailmodel.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.h

处理类名映射: commentModel -> > XGGcommentModel
  找到引用文件:
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/XGGcommentModel.h
    YBVideo//功能/评论/XGGcommCell.m
    YBVideo//功能/评论/XGGcommCell.h
    YBVideo//功能/评论/XGGcommentModel.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentModel.h
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.h
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentModel.m

处理类名映射: commDetailCell -> > XGGcommDetailCell
  找到引用文件:
    YBVideo//功能/评论/XGGcommDetailCell.m
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/XGGcommCell.m
    YBVideo//功能/评论/XGGcommDetailCell.h
    YBVideo//功能/评论/XGGcommCell.h
    ✅ 更新成功: YBVideo//功能/评论/XGGcommDetailCell.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommDetailCell.h
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.h

处理类名映射: commentview -> > XGGcommentview
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//消息/XGGMsgTopPubVC.m
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/XGGcommentview.h
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.h
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h

处理类名映射: YBCommentToolBar -> > XGGYBCommentToolBar
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    ✅ 更新成功: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h

处理类名映射: commCell -> > XGGcommCell
  找到引用文件:
    YBVideo//功能/评论/XGGcommentview.m
    YBVideo//功能/评论/XGGcommCell.m
    YBVideo//功能/评论/XGGcommCell.h
    ✅ 更新成功: YBVideo//功能/评论/XGGcommentview.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.h

处理类名映射: YBYoungModeVC -> > XGGYBYoungModeVC
  找到引用文件:
    YBVideo//功能/青少年/XGGYBYoungManager.m
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.h
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
    ✅ 更新成功: YBVideo//功能/青少年/XGGYBYoungManager.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModeVC.h
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m

处理类名映射: YBYoungSetVC -> > XGGYBYoungSetVC
  找到引用文件:
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.h
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.h
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m

处理类名映射: RKCodeView -> > XGGRKCodeView
  找到引用文件:
    YBVideo//功能/青少年/vc/密码/XGGRKCodeView.h
    YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.h
    YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.m
    YBVideo//功能/青少年/vc/密码/XGGRKCodeView.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/密码/XGGRKCodeView.h
    ✅ 更新成功: YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.h
    ✅ 更新成功: YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/密码/XGGRKCodeView.m

处理类名映射: RKCodeInputView -> > XGGRKCodeInputView
  找到引用文件:
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m
    YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.h
    YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.m
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.h
    ✅ 更新成功: YBVideo//功能/青少年/vc/密码/XGGRKCodeInputView.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m

处理类名映射: YBYoungModifyVC -> > XGGYBYoungModifyVC
  找到引用文件:
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.h
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.h

处理类名映射: YBYoungSmall -> > XGGYBYoungSmall
  找到引用文件:
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//功能/青少年/XGGYBYoungManager.m
    YBVideo//功能/青少年/小窗/XGGYBYoungSmall.m
    YBVideo//功能/青少年/小窗/XGGYBYoungSmall.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//功能/青少年/XGGYBYoungManager.m
    ✅ 更新成功: YBVideo//功能/青少年/小窗/XGGYBYoungSmall.m
    ✅ 更新成功: YBVideo//功能/青少年/小窗/XGGYBYoungSmall.h

处理类名映射: YBYoungManager -> > XGGYBYoungManager
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    YBVideo//功能/青少年/XGGYBYoungManager.m
    YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    YBVideo//功能/青少年/小窗/XGGYBYoungSmall.m
    YBVideo//功能/青少年/XGGYBYoungManager.h
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 更新成功: YBVideo//功能/青少年/XGGYBYoungManager.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    ✅ 更新成功: YBVideo//功能/青少年/小窗/XGGYBYoungSmall.m
    ✅ 更新成功: YBVideo//功能/青少年/XGGYBYoungManager.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: BusinessCardVC -> > XGGBusinessCardVC
  找到引用文件:
    YBVideo//功能/我的名片/XGGBusinessCardVC.h
    YBVideo//功能/我的名片/XGGBusinessCardVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//功能/我的名片/XGGBusinessCardVC.h
    ✅ 更新成功: YBVideo//功能/我的名片/XGGBusinessCardVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: RKKeepAlive -> > XGGRKKeepAlive
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//功能/KeepLive/XGGRKKeepAlive.h
    YBVideo//功能/KeepLive/XGGRKKeepAlive.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//功能/KeepLive/XGGRKKeepAlive.h
    ✅ 更新成功: YBVideo//功能/KeepLive/XGGRKKeepAlive.m

处理类名映射: MyAdvertCell -> > XGGMyAdvertCell
  找到引用文件:
    YBVideo//功能/广告管理/XGGMyAdvertCell.h
    YBVideo//功能/广告管理/XGGMyAdvertVC.m
    YBVideo//功能/广告管理/XGGMyAdvertCell.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGMyAdvertCell.h
    ✅ 更新成功: YBVideo//功能/广告管理/XGGMyAdvertVC.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGMyAdvertCell.m

处理类名映射: AdvertManagerVC -> > XGGAdvertManagerVC
  找到引用文件:
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    YBVideo//功能/广告管理/XGGAdvertManagerVC.h
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGAdvertManagerVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m

处理类名映射: MyAdvertVC -> > XGGMyAdvertVC
  找到引用文件:
    YBVideo//功能/广告管理/XGGMyAdvertVC.m
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    YBVideo//功能/广告管理/XGGMyAdvertVC.h
    ✅ 更新成功: YBVideo//功能/广告管理/XGGMyAdvertVC.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    ✅ 更新成功: YBVideo//功能/广告管理/XGGMyAdvertVC.h

处理类名映射: lookVGoodsDView -> > XGGlookVGoodsDView
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    YBVideo//功能/观看商品/XGGlookVGoodsDView.h
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    ✅ 更新成功: YBVideo//功能/观看商品/XGGlookVGoodsDView.h

处理类名映射: YBInvitationView -> > XGGYBInvitationView
  找到引用文件:
    YBVideo//公共方法类/XGGYBAlertView.m
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//功能/邀请码/XGGYBInvitationView.h
    YBVideo//功能/邀请码/XGGYBInviteCode.m
    YBVideo//功能/邀请码/XGGYBInvitationView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGYBAlertView.m
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//功能/邀请码/XGGYBInvitationView.h
    ✅ 更新成功: YBVideo//功能/邀请码/XGGYBInviteCode.m
    ✅ 更新成功: YBVideo//功能/邀请码/XGGYBInvitationView.m

处理类名映射: YBInviteCode -> > XGGYBInviteCode
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//功能/邀请码/XGGYBInviteCode.m
    YBVideo//功能/邀请码/XGGYBInviteCode.h
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//功能/邀请码/XGGYBInviteCode.m
    ✅ 更新成功: YBVideo//功能/邀请码/XGGYBInviteCode.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m

处理类名映射: YBVideoReportVC -> > XGGYBVideoReportVC
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.h
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.h
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m

处理类名映射: YBReportCell -> > XGGYBReportCell
  找到引用文件:
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
    YBVideo//功能/举报(直播间+看视频)/view/XGGYBReportCell.m
    YBVideo//功能/举报(直播间+看视频)/view/XGGYBReportCell.h
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/view/XGGYBReportCell.m
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/view/XGGYBReportCell.h

处理类名映射: YBLiveReportVC -> > XGGYBLiveReportVC
  找到引用文件:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m
    YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.m
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/XGGYBLiveReportVC.h

处理类名映射: YBLanguageTools -> > XGGYBLanguageTools
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//XGGNetwork/XGGNetworkManager.m
    YBVideo//公共方法类/XGGiOSNetworking.m
    YBVideo//直播模块/socket/XGGYBSocketLive.m
    YBVideo//直播模块/socket/XGGYBSocketPlay.m
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//功能/语言包/XGGYBLanguageTools.h
    YBVideo//功能/语言包/XGGYBLanguageTools.m
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.m
    YBVideo//功能/分享/观看分享/XGGYBShareView.m
    YBVideo//个人中心/自己更多/明细/XGGaccountDetails.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//XGGNetwork/XGGNetworkManager.m
    ✅ 更新成功: YBVideo//公共方法类/XGGiOSNetworking.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketLive.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketPlay.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//功能/语言包/XGGYBLanguageTools.h
    ✅ 更新成功: YBVideo//功能/语言包/XGGYBLanguageTools.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.m
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/明细/XGGaccountDetails.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: YBTakeSameVideoVC -> > XGGYBTakeSameVideoVC
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 更新成功: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.h

处理类名映射: YBDestroySureVC -> > XGGYBDestroySureVC
  找到引用文件:
    YBVideo//功能/注销账号/XGGYBDestroySureVC.h
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m
    YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.h
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroyAccount.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroySureVC.m

处理类名映射: YBDestroyCell -> > XGGYBDestroyCell
  找到引用文件:
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m
    YBVideo//功能/注销账号/view/XGGYBDestroyCell.m
    YBVideo//功能/注销账号/view/XGGYBDestroyCell.h
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroyAccount.m
    ✅ 更新成功: YBVideo//功能/注销账号/view/XGGYBDestroyCell.m
    ✅ 更新成功: YBVideo//功能/注销账号/view/XGGYBDestroyCell.h

处理类名映射: YBDestroyAccount -> > XGGYBDestroyAccount
  找到引用文件:
    YBVideo//功能/注销账号/XGGYBDestroyAccount.m
    YBVideo//功能/注销账号/XGGYBDestroyAccount.h
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroyAccount.m
    ✅ 更新成功: YBVideo//功能/注销账号/XGGYBDestroyAccount.h
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: YBPageControl -> > XGGYBPageControl
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/礼物/page/XGGYBPageControl.h
    YBVideo//功能/礼物/page/XGGYBPageControl.m
    YBVideo//功能/礼物/XGGYBGiftView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/礼物/page/XGGYBPageControl.h
    ✅ 更新成功: YBVideo//功能/礼物/page/XGGYBPageControl.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.h

处理类名映射: RKPaintedGiftView -> > XGGRKPaintedGiftView
  找到引用文件:
    YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.h
    YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.m
    YBVideo//功能/礼物/XGGYBGiftPage.m
    YBVideo//功能/礼物/XGGYBGiftView.h
    YBVideo//功能/礼物/XGGYBGiftPage.h
    ✅ 更新成功: YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.h
    ✅ 更新成功: YBVideo//功能/礼物/手绘礼物/XGGRKPaintedGiftView.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftPage.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.h
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftPage.h

处理类名映射: RKShowPaintedView -> > XGGRKShowPaintedView
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/礼物/手绘礼物/XGGRKShowPaintedView.m
    YBVideo//功能/礼物/手绘礼物/XGGRKShowPaintedView.h
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/礼物/手绘礼物/XGGRKShowPaintedView.m
    ✅ 更新成功: YBVideo//功能/礼物/手绘礼物/XGGRKShowPaintedView.h

处理类名映射: expensiveGiftV -> > XGGexpensiveGiftV
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.h
    YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.h
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.m

处理类名映射: continueGift -> > XGGcontinueGift
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/礼物/礼物特效/XGGcontinueGift.h
    YBVideo//功能/礼物/礼物特效/XGGcontinueGift.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGcontinueGift.h
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGcontinueGift.m

处理类名映射: liansongBackView -> > XGGliansongBackView
  找到引用文件:
    YBVideo//功能/礼物/礼物特效/XGGliansongBackView.h
    YBVideo//功能/礼物/礼物特效/XGGliansongBackView.m
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGliansongBackView.h
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGliansongBackView.m

处理类名映射: exoensiveGifGiftV -> > XGGexoensiveGifGiftV
  找到引用文件:
    YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.h
    YBVideo//功能/礼物/礼物特效/XGGexoensiveGifGiftV.m
    YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.m
    YBVideo//功能/礼物/礼物特效/XGGexoensiveGifGiftV.h
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.h
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGexoensiveGifGiftV.m
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGexpensiveGiftV.m
    ✅ 更新成功: YBVideo//功能/礼物/礼物特效/XGGexoensiveGifGiftV.h

处理类名映射: YBGiftView -> > XGGYBGiftView
  找到引用文件:
    YBVideo//功能/礼物/XGGYBGiftView.m
    YBVideo//功能/礼物/XGGYBGiftPage.m
    YBVideo//功能/礼物/XGGYBGiftView.h
    YBVideo//功能/礼物/XGGYBGiftPage.h
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftPage.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.h
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftPage.h

处理类名映射: YBGiftModel -> > XGGYBGiftModel
  找到引用文件:
    YBVideo//功能/礼物/XGGYBGiftView.m
    YBVideo//功能/礼物/model/XGGYBGiftModel.m
    YBVideo//功能/礼物/model/XGGYBGiftModel.h
    YBVideo//功能/礼物/model/GiftModel.m
    YBVideo//功能/礼物/view/XGGYBGiftCell.m
    YBVideo//功能/礼物/view/GiftCell.m
    YBVideo//功能/礼物/view/XGGYBGiftCell.h
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.m
    ✅ 更新成功: YBVideo//功能/礼物/model/XGGYBGiftModel.m
    ✅ 更新成功: YBVideo//功能/礼物/model/XGGYBGiftModel.h
    ✅ 更新成功: YBVideo//功能/礼物/model/GiftModel.m
    ✅ 更新成功: YBVideo//功能/礼物/view/XGGYBGiftCell.m
    ✅ 更新成功: YBVideo//功能/礼物/view/GiftCell.m
    ✅ 更新成功: YBVideo//功能/礼物/view/XGGYBGiftCell.h

处理类名映射: YBGiftCell -> > XGGYBGiftCell
  找到引用文件:
    YBVideo//功能/礼物/XGGYBGiftView.m
    YBVideo//功能/礼物/view/XGGYBGiftCell.m
    YBVideo//功能/礼物/view/GiftCell.m
    YBVideo//功能/礼物/view/XGGYBGiftCell.h
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.m
    ✅ 更新成功: YBVideo//功能/礼物/view/XGGYBGiftCell.m
    ✅ 更新成功: YBVideo//功能/礼物/view/GiftCell.m
    ✅ 更新成功: YBVideo//功能/礼物/view/XGGYBGiftCell.h

处理类名映射: NSObject -> > TYPagerViewLayout
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoPreview.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCFilterSettingView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCBottomTabBar.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoCutView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoTextFiled.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.h
    YBVideo//录制_编辑_发布/UGCEditor/Views/TCTextAddView.h
    YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.h
    YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.h
    YBVideo//其他类/XGGRKLBSManager.h
    YBVideo//其他类/Config.h
    YBVideo//其他类/XGGcityDefault.h
    YBVideo//其他类/common.h
    YBVideo//其他类/sproutCommon.h
    YBVideo//其他类/XGGAppDelegate.h
    YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.h
    YBVideo//XGGNetwork/XGGNetworkManager.h
    YBVideo//XGGNetwork/XGGNetworkUtils.h
    YBVideo//公共方法类/XGGYBProgressObj.h
    YBVideo//公共方法类/XGGPublicObj.h
    YBVideo//公共方法类/XGGYBNetworking.h
    YBVideo//公共方法类/XGGBGSetting.h
    YBVideo//公共方法类/XGGRKSysAccess.h
    YBVideo//公共方法类/XGGiOSNetworking.h
    YBVideo//公共方法类/XGGRKUUIDManager.h
    YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h
    YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.h
    YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.h
    YBVideo//店铺/买家端/评价/CWStar/CWStarRateView.h
    YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.h
    YBVideo//店铺/买家端/我的地址/XGGAddressModel.h
    YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.h
    YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
    YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
    YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.h
    YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.h
    YBVideo//店铺/买家端/三方/MJExtension/MJPropertyKey.h
    YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJClass.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoPreview.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCFilterSettingView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCBottomTabBar.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoCutView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCVideoTextFiled.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCEditor/Views/TCTextAddView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.h
    ✅ 更新成功: YBVideo//其他类/XGGRKLBSManager.h
    ✅ 更新成功: YBVideo//其他类/Config.h
    ✅ 更新成功: YBVideo//其他类/XGGcityDefault.h
    ✅ 更新成功: YBVideo//其他类/common.h
    ✅ 更新成功: YBVideo//其他类/sproutCommon.h
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.h
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    ✅ 更新成功: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.h
    ✅ 更新成功: YBVideo//XGGNetwork/XGGNetworkManager.h
    ✅ 更新成功: YBVideo//XGGNetwork/XGGNetworkUtils.h
    ✅ 更新成功: YBVideo//公共方法类/XGGYBProgressObj.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.h
    ✅ 更新成功: YBVideo//公共方法类/XGGYBNetworking.h
    ✅ 更新成功: YBVideo//公共方法类/XGGBGSetting.h
    ✅ 更新成功: YBVideo//公共方法类/XGGRKSysAccess.h
    ✅ 更新成功: YBVideo//公共方法类/XGGiOSNetworking.h
    ✅ 更新成功: YBVideo//公共方法类/XGGRKUUIDManager.h
    ✅ 更新成功: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/分享商品/分享给好友/XGGFriendModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/评价/CWStar/CWStarRateView.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGRejectAddressModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressCell.h
    ✅ 更新成功: YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
    ✅ 更新成功: YBVideo//店铺/买家端/商品评价/XGGEvaluationListModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundModel.h
    ✅ 更新成功: YBVideo//店铺/买家端/三方/MJExtension/MJPropertyKey.h
    ✅ 更新成功: YBVideo//店铺/买家端/三方/MJExtension/NSObject+MJClass.h

处理类名映射: YBGiftPage -> > XGGYBGiftPage
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    YBVideo//功能/礼物/XGGYBGiftView.m
    YBVideo//功能/礼物/XGGYBGiftPage.m
    YBVideo//功能/礼物/XGGYBGiftPage.h
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftView.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftPage.m
    ✅ 更新成功: YBVideo//功能/礼物/XGGYBGiftPage.h

处理类名映射: topicVideoCell -> > XGGtopicVideoCell
  找到引用文件:
    YBVideo//功能/标签全部视频/XGGtopicVideoCell.h
    YBVideo//功能/标签全部视频/XGGtopicVideoCell.m
    YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicVideoCell.h
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicVideoCell.m
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m

处理类名映射: topicDetailsVC -> > XGGtopicDetailsVC
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//功能/标签全部视频/XGGtopicDetailsVC.h
    YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.h
    ✅ 更新成功: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m

处理类名映射: shouhuView -> > XGGshouhuView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//功能/直播/守护/XGGshouhuView.h
    YBVideo//功能/直播/守护/XGGshouhuView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGshouhuView.h
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGshouhuView.m

处理类名映射: guardShowView -> > XGGguardShowView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/守护/XGGguardShowView.m
    YBVideo//功能/直播/守护/XGGguardShowView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGguardShowView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGguardShowView.h

处理类名映射: guardListModel -> > XGGguardListModel
  找到引用文件:
    YBVideo//功能/直播/守护/XGGguardShowView.m
    YBVideo//功能/直播/守护/model/XGGguardListModel.m
    YBVideo//功能/直播/守护/model/XGGguardListModel.h
    YBVideo//功能/直播/守护/view/XGGguardListCell.m
    YBVideo//功能/直播/守护/view/XGGguardListCell.h
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGguardShowView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/model/XGGguardListModel.m
    ✅ 更新成功: YBVideo//功能/直播/守护/model/XGGguardListModel.h
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGguardListCell.m
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGguardListCell.h

处理类名映射: grardButton -> > XGGgrardButton
  找到引用文件:
    YBVideo//功能/直播/守护/view/XGGgrardButton.h
    YBVideo//功能/直播/守护/view/XGGgrardButton.m
    YBVideo//功能/直播/守护/XGGshouhuView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGgrardButton.h
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGgrardButton.m
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGshouhuView.m

处理类名映射: guardListCell -> > XGGguardListCell
  找到引用文件:
    YBVideo//功能/直播/守护/XGGguardShowView.m
    YBVideo//功能/直播/守护/view/XGGguardListCell.m
    YBVideo//功能/直播/守护/view/XGGguardListCell.h
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGguardShowView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGguardListCell.m
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGguardListCell.h

处理类名映射: guardAlertView -> > XGGguardAlertView
  找到引用文件:
    YBVideo//功能/直播/守护/view/XGGguardAlertView.m
    YBVideo//功能/直播/守护/view/XGGguardAlertView.h
    YBVideo//功能/直播/守护/XGGshouhuView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGguardAlertView.m
    ✅ 更新成功: YBVideo//功能/直播/守护/view/XGGguardAlertView.h
    ✅ 更新成功: YBVideo//功能/直播/守护/XGGshouhuView.m

处理类名映射: YBPkProgressView -> > XGGYBPkProgressView
  找到引用文件:
    YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.m
    YBVideo//功能/直播/连麦+PK/PK/XGGYBPkProgressView.h
    YBVideo//功能/直播/连麦+PK/PK/XGGYBPkProgressView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/PK/XGGYBPkProgressView.h
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/PK/XGGYBPkProgressView.m

处理类名映射: YBAnchorPKView -> > XGGYBAnchorPKView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.m
    YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKView.h

处理类名映射: YBAnchorPKAlert -> > XGGYBAnchorPKAlert
  找到引用文件:
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//直播模块/socket/XGGYBSocketLive.m
    YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.m
    YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketLive.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.h

处理类名映射: YBAnchorLinkInfo -> > XGGYBAnchorLinkInfo
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.m
    YBVideo//功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.h

处理类名映射: YBLinkAlertView -> > XGGYBLinkAlertView
  找到引用文件:
    YBVideo//直播模块/socket/XGGYBSocketLive.m
    YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.h
    YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.m
    ✅ 更新成功: YBVideo//直播模块/socket/XGGYBSocketLive.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.h
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.m

处理类名映射: YBAnchorOnlineCell -> > anchorCell
  找到引用文件:
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/anchorCell.h
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.h
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/anchorCell.h
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.h
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m

处理类名映射: YBAnchorOnlineCell -> > XGGYBAnchorOnlineCell
  找到引用文件:
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/anchorCell.h
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.h
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/anchorCell.h
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.h
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m

处理类名映射: YBAnchorOnline -> > XGGYBAnchorOnline
  找到引用文件:
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.h

处理类名映射: YBTxLinkMicView -> > XGGYBTxLinkMicView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.m
    YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.m
    ✅ 更新成功: YBVideo//功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.h

处理类名映射: YBUserListModel -> > XGGYBUserListModel
  找到引用文件:
    YBVideo//功能/直播/用户列表/XGGYBUserListView.m
    YBVideo//功能/直播/用户列表/model/XGGYBUserListModel.m
    YBVideo//功能/直播/用户列表/model/XGGYBUserListModel.h
    YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.m
    YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.h
    ✅ 更新成功: YBVideo//功能/直播/用户列表/XGGYBUserListView.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/model/XGGYBUserListModel.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/model/XGGYBUserListModel.h
    ✅ 更新成功: YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.h

处理类名映射: YBUserListCell -> > XGGYBUserListCell
  找到引用文件:
    YBVideo//功能/直播/用户列表/XGGYBUserListView.m
    YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.m
    YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.h
    ✅ 更新成功: YBVideo//功能/直播/用户列表/XGGYBUserListView.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/view/XGGYBUserListCell.h

处理类名映射: YBUserListView -> > XGGYBUserListView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/用户列表/XGGYBUserListView.m
    YBVideo//功能/直播/用户列表/XGGYBUserListView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/XGGYBUserListView.m
    ✅ 更新成功: YBVideo//功能/直播/用户列表/XGGYBUserListView.h

处理类名映射: adminCell -> > XGGadminCell
  找到引用文件:
    YBVideo//功能/直播/管理员列表/XGGadminCell.h
    YBVideo//功能/直播/管理员列表/XGGadminCell.m
    YBVideo//功能/直播/管理员列表/XGGadminLists.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminCell.h
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminCell.m
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminLists.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m

处理类名映射: adminLists -> > XGGadminLists
  找到引用文件:
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/直播/管理员列表/XGGadminLists.h
    YBVideo//功能/直播/管理员列表/XGGadminLists.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminLists.h
    ✅ 更新成功: YBVideo//功能/直播/管理员列表/XGGadminLists.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m

处理类名映射: YBDayTaskManager -> > XGGYBDayTaskManager
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//功能/直播/每日任务/XGGYBDayTaskManager.h
    YBVideo//功能/直播/每日任务/XGGYBDayTaskManager.m
    YBVideo//功能/分享/观看分享/XGGYBShareView.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//功能/直播/每日任务/XGGYBDayTaskManager.h
    ✅ 更新成功: YBVideo//功能/直播/每日任务/XGGYBDayTaskManager.m
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareView.m

处理类名映射: YBDayTaskVC -> > XGGYBDayTaskVC
  找到引用文件:
    YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.m
    YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.h
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.m
    ✅ 更新成功: YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m

处理类名映射: YBDayTaskView -> > XGGYBDayTaskView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.h
    YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.h
    ✅ 更新成功: YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.m

处理类名映射: YBDayTaskCell -> > XGGYBDayTaskCell
  找到引用文件:
    YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.m
    YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.m
    YBVideo//功能/直播/每日任务/View/XGGYBDayTaskCell.h
    YBVideo//功能/直播/每日任务/View/XGGYBDayTaskCell.m
    ✅ 更新成功: YBVideo//功能/直播/每日任务/个中VC/XGGYBDayTaskVC.m
    ✅ 更新成功: YBVideo//功能/直播/每日任务/直播间内/XGGYBDayTaskView.m
    ✅ 更新成功: YBVideo//功能/直播/每日任务/View/XGGYBDayTaskCell.h
    ✅ 更新成功: YBVideo//功能/直播/每日任务/View/XGGYBDayTaskCell.m

处理类名映射: YBGoodsBriefView -> > XGGYBGoodsBriefView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.h
    YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.h
    ✅ 更新成功: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m

处理类名映射: YBLiveChatView -> > XGGYBLiveChatView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.m
    YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.m
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.h

处理类名映射: YBLiveChatModel -> > XGGYBLiveChatModel
  找到引用文件:
    YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.m
    YBVideo//功能/直播/直播间聊天/model/XGGYBLiveChatModel.h
    YBVideo//功能/直播/直播间聊天/model/XGGYBLiveChatModel.m
    YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.m
    YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.h
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.m
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/model/XGGYBLiveChatModel.h
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/model/XGGYBLiveChatModel.m
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.m
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.h

处理类名映射: YBLiveChatCell -> > XGGYBLiveChatCell
  找到引用文件:
    YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.m
    YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.m
    YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.h
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/XGGYBLiveChatView.m
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.m
    ✅ 更新成功: YBVideo//功能/直播/直播间聊天/view/XGGYBLiveChatCell.h

处理类名映射: YBOnSaleCell -> > XGGYBOnSaleCell
  找到引用文件:
    YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
    YBVideo//功能/直播/在售商品/view/XGGYBOnSaleCell.m
    YBVideo//功能/直播/在售商品/view/XGGYBOnSaleCell.h
    YBVideo//功能/直播/在售商品/XGGYBOnSaleView.h
    ✅ 更新成功: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
    ✅ 更新成功: YBVideo//功能/直播/在售商品/view/XGGYBOnSaleCell.m
    ✅ 更新成功: YBVideo//功能/直播/在售商品/view/XGGYBOnSaleCell.h
    ✅ 更新成功: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.h

处理类名映射: YBOnSaleView -> > XGGYBOnSaleView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
    YBVideo//功能/直播/在售商品/XGGYBOnSaleView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
    ✅ 更新成功: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.h

处理类名映射: userLevelView -> > XGGuserLevelView
  找到引用文件:
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.h
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.m
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.h
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.m

处理类名映射: UserBulletWindow -> > XGGUserBulletWindow
  找到引用文件:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
    ✅ 更新成功: YBVideo//功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.h

处理类名映射: turntableResultView -> > XGGturntableResultView
  找到引用文件:
    YBVideo//功能/直播/幸运转盘/XGGturntableResultView.h
    YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    YBVideo//功能/直播/幸运转盘/XGGturntableResultView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableResultView.h
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableResultView.m

处理类名映射: turntableResultCell -> > XGGturntableResultCell
  找到引用文件:
    YBVideo//功能/直播/幸运转盘/view/XGGturntableResultCell.h
    YBVideo//功能/直播/幸运转盘/view/XGGturntableResultCell.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/view/XGGturntableResultCell.h
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/view/XGGturntableResultCell.m

处理类名映射: turntableRecordCell -> > XGGturntableRecordCell
  找到引用文件:
    YBVideo//功能/直播/幸运转盘/view/XGGturntableRecordCell.m
    YBVideo//功能/直播/幸运转盘/view/XGGturntableRecordCell.h
    YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRecordView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/view/XGGturntableRecordCell.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/view/XGGturntableRecordCell.h
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRecordView.m

处理类名映射: turntableView -> > XGGturntableView
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    YBVideo//功能/直播/幸运转盘/XGGturntableView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableView.h

处理类名映射: turntableRuleView -> > XGGturntableRuleView
  找到引用文件:
    YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.m
    YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.h
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/XGGturntableView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.m
    ✅ 更新成功: YBVideo//功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.h

处理类名映射: YBUserEnterAnimation -> > XGGYBUserEnterAnimation
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//功能/直播/进房间动画/XGGYBUserEnterAnimation.h
    YBVideo//功能/直播/进房间动画/XGGYBUserEnterAnimation.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//功能/直播/进房间动画/XGGYBUserEnterAnimation.h
    ✅ 更新成功: YBVideo//功能/直播/进房间动画/XGGYBUserEnterAnimation.m

处理类名映射: huanxinsixinview -> > XGGhuanxinsixinview
  找到引用文件:
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.h

处理类名映射: YBImRoomSmallView -> > XGGYBImRoomSmallView
  找到引用文件:
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.h
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGYBImRoomSmallView.h

处理类名映射: LiveRankVC -> > XGGLiveRankVC
  找到引用文件:
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/直播榜单/XGGLiveRankVC.h
    YBVideo//功能/直播榜单/XGGLiveRankVC.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/直播榜单/XGGLiveRankVC.h
    ✅ 更新成功: YBVideo//功能/直播榜单/XGGLiveRankVC.m

处理类名映射: LiveRankCell -> > XGGLiveRankCell
  找到引用文件:
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/直播榜单/XGGLiveRankCell.m
    YBVideo//功能/直播榜单/XGGLiveRankVC.m
    YBVideo//功能/直播榜单/XGGLiveRankCell.h
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/直播榜单/XGGLiveRankCell.m
    ✅ 更新成功: YBVideo//功能/直播榜单/XGGLiveRankVC.m
    ✅ 更新成功: YBVideo//功能/直播榜单/XGGLiveRankCell.h

处理类名映射: PublishShareV -> > XGGPublishShareV
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//功能/分享/发布分享/XGGPublishShareV.m
    YBVideo//功能/分享/发布分享/XGGPublishShareV.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//功能/分享/发布分享/XGGPublishShareV.m
    ✅ 更新成功: YBVideo//功能/分享/发布分享/XGGPublishShareV.h

处理类名映射: YBShareView -> > XGGYBShareView
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//功能/我的名片/XGGBusinessCardVC.m
    YBVideo//功能/分享/观看分享/XGGYBShareView.h
    YBVideo//功能/分享/观看分享/XGGYBShareView.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//功能/我的名片/XGGBusinessCardVC.m
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareView.h
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareView.m

处理类名映射: YBShareViewCell -> > XGGYBShareViewCell
  找到引用文件:
    YBVideo//功能/分享/观看分享/XGGYBShareViewCell.h
    YBVideo//功能/分享/观看分享/XGGYBShareView.m
    YBVideo//功能/分享/观看分享/XGGYBShareViewCell.m
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareViewCell.h
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareView.m
    ✅ 更新成功: YBVideo//功能/分享/观看分享/XGGYBShareViewCell.m

处理类名映射: NearbyVC -> > XGGNearbyVC
  找到引用文件:
    YBVideo//首页/XGGYBHomeViewController.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//附近/XGGNearbyVC.h
    YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.h
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m

处理类名映射: YBCitySelVC -> > XGGYBCitySelVC
  找到引用文件:
    YBVideo//附近/XGGNearbyVC.m
    YBVideo//附近/城市选择/XGGYBCitySelVC.h
    YBVideo//附近/城市选择/XGGYBCitySelVC.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//附近/城市选择/XGGYBCitySelVC.h
    ✅ 更新成功: YBVideo//附近/城市选择/XGGYBCitySelVC.m

处理类名映射: YBCitySelCell -> > XGGYBCitySelCell
  找到引用文件:
    YBVideo//附近/城市选择/view/XGGYBCitySelCell.h
    YBVideo//附近/城市选择/view/XGGYBCitySelCell.m
    YBVideo//附近/城市选择/XGGYBCitySelVC.m
    ✅ 更新成功: YBVideo//附近/城市选择/view/XGGYBCitySelCell.h
    ✅ 更新成功: YBVideo//附近/城市选择/view/XGGYBCitySelCell.m
    ✅ 更新成功: YBVideo//附近/城市选择/XGGYBCitySelVC.m

处理类名映射: NearbyCell -> > XGGNearbyCell
  找到引用文件:
    YBVideo//附近/XGGNearbyVC.m
    YBVideo//附近/view/XGGNearbyCell.h
    YBVideo//附近/view/XGGNearbyCell.m
    ✅ 更新成功: YBVideo//附近/XGGNearbyVC.m
    ✅ 更新成功: YBVideo//附近/view/XGGNearbyCell.h
    ✅ 更新成功: YBVideo//附近/view/XGGNearbyCell.m

处理类名映射: commodityRecordsVC -> > XGGcommodityRecordsVC
  找到引用文件:
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.h
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m

处理类名映射: commodityRecordsCell -> > XGGcommodityRecordsCell
  找到引用文件:
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsCell.m
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsCell.h

处理类名映射: YBRedProfitVC -> > XGGYBRedProfitVC
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.h
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.h

处理类名映射: YBGetTypeListVC -> > profitTypeVC
  找到引用文件:
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    YBVideo//个人中心/自己更多/我的收益/profitTypeVC.h
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.h
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/profitTypeVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m

处理类名映射: YBAddTypeView -> > addTypeView
  找到引用文件:
    YBVideo//个人中心/自己更多/我的收益/addTypeView.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/addTypeView.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m

处理类名映射: YBGetTypeListVC -> > XGGYBGetTypeListVC
  找到引用文件:
    YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    YBVideo//个人中心/自己更多/我的收益/profitTypeVC.h
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.h
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/提取收益/XGGGetMoneyVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/profitTypeVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m

处理类名映射: YBGetProVC -> > XGGYBGetProVC
  找到引用文件:
    YBVideo//功能/H5/XGGPubH5.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.h
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 更新成功: YBVideo//功能/H5/XGGPubH5.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m

处理类名映射: YBGetTypeListCell -> > profitTypeCell
  找到引用文件:
    YBVideo//个人中心/自己更多/我的收益/profitTypeCell.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/profitTypeCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m

处理类名映射: YBAddTypeView -> > XGGYBAddTypeView
  找到引用文件:
    YBVideo//个人中心/自己更多/我的收益/addTypeView.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/addTypeView.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBAddTypeView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m

处理类名映射: YBGetTypeListCell -> > XGGYBGetTypeListCell
  找到引用文件:
    YBVideo//个人中心/自己更多/我的收益/profitTypeCell.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.h
    YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/profitTypeCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m

处理类名映射: YBGoodsLikeVC -> > XGGYBGoodsLikeVC
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.h
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: YBGoodsLikeCell -> > XGGYBGoodsLikeCell
  找到引用文件:
    YBVideo//个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.m
    YBVideo//个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.h
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m

处理类名映射: YBApplyStoreVC -> > XGGYBApplyStoreVC
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.h

处理类名映射: YBApplyConditionVC -> > XGGYBApplyConditionVC
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.h

处理类名映射: YBApplyConditionCell -> > XGGYBApplyConditionCell
  找到引用文件:
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    YBVideo//个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.m
    YBVideo//个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.h

处理类名映射: RoomUserListViewController -> > XGGRoomUserListViewController
  找到引用文件:
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.h
    YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m

处理类名映射: RoomManagementVC -> > XGGRoomManagementVC
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.h
    YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m

处理类名映射: RoomUserTypeCell -> > XGGRoomUserTypeCell
  找到引用文件:
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserTypeCell.m
    YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserTypeCell.h
    YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserTypeCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserTypeCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m

处理类名映射: OtherRoomViewController -> > XGGOtherRoomViewController
  找到引用文件:
    YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
    YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.h
    YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGOtherRoomViewController.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/房间管理/XGGRoomManagementVC.m

处理类名映射: WatchRecordListCell -> > XGGWatchRecordListCell
  找到引用文件:
    YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    YBVideo//个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.h
    YBVideo//个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.m

处理类名映射: watchingRecordsVC -> > XGGwatchingRecordsVC
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.h

处理类名映射: accountDetails -> > XGGaccountDetails
  找到引用文件:
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    YBVideo//个人中心/自己更多/明细/XGGaccountDetails.m
    YBVideo//个人中心/自己更多/明细/XGGaccountDetails.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/明细/XGGaccountDetails.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/明细/XGGaccountDetails.h

处理类名映射: YBGoodsInfoVC -> > XGGYBGoodsInfoVC
  找到引用文件:
    YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.h
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 更新成功: YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    ✅ 更新成功: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m

处理类名映射: YBGoodsListVC -> > XGGYBGoodsListVC
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.h
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m

处理类名映射: YBGoodsListCell -> > XGGYBGoodsListCell
  找到引用文件:
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.h
    YBVideo//个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.h
    YBVideo//个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m

处理类名映射: YBCenterMoreView -> > XGGYBCenterMoreView
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.h
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.h
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: YBCenterMoreCell -> > XGGYBCenterMoreCell
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/view/XGGYBCenterMoreCell.m
    YBVideo//个人中心/自己更多/view/XGGYBCenterMoreCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/view/XGGYBCenterMoreCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/view/XGGYBCenterMoreCell.h

处理类名映射: orderVideoCell -> > XGGorderVideoCell
  找到引用文件:
    YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.h
    YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.m

处理类名映射: depositAccountVC -> > XGGdepositAccountVC
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.h

处理类名映射: YBOtherCenterMore -> > XGGYBOtherCenterMore
  找到引用文件:
    YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.m
    YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.h
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.m
    ✅ 更新成功: YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.h
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m

处理类名映射: SetViewControllor -> > XGGSetViewControllor
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/设置/XGGSetViewControllor.h
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//个人中心/设置/setView.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.h
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/设置/setView.h

处理类名映射: SetLogoutCell -> > XGGSetLogoutCell
  找到引用文件:
    YBVideo//个人中心/设置/view/XGGSetLogoutCell.h
    YBVideo//个人中心/设置/view/XGGSetLogoutCell.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//个人中心/设置/userItemCell5.h
    ✅ 更新成功: YBVideo//个人中心/设置/view/XGGSetLogoutCell.h
    ✅ 更新成功: YBVideo//个人中心/设置/view/XGGSetLogoutCell.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/设置/userItemCell5.h

处理类名映射: SetCell -> > XGGSetCell
  找到引用文件:
    YBVideo//个人中心/设置/view/XGGSetCell.m
    YBVideo//个人中心/设置/view/XGGSetCell.h
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/设置/view/XGGSetCell.m
    ✅ 更新成功: YBVideo//个人中心/设置/view/XGGSetCell.h
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: YBUserAuthVC -> > XGGYBUserAuthVC
  找到引用文件:
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.h
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.h
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m

处理类名映射: SetViewControllor -> > setView
  找到引用文件:
    YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    YBVideo//个人中心/设置/XGGSetViewControllor.h
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//个人中心/设置/setView.h
    ✅ 更新成功: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.h
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/设置/setView.h

处理类名映射: YBPrivateVC -> > XGGYBPrivateVC
  找到引用文件:
    YBVideo//其他类/XGGAppDelegate.m
    YBVideo//登录注册/XGGDspLoginVC.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m
    YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.h
    ✅ 更新成功: YBVideo//其他类/XGGAppDelegate.m
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.h

处理类名映射: SetLogoutCell -> > userItemCell5
  找到引用文件:
    YBVideo//个人中心/设置/view/XGGSetLogoutCell.h
    YBVideo//个人中心/设置/view/XGGSetLogoutCell.m
    YBVideo//个人中心/设置/XGGSetViewControllor.m
    YBVideo//个人中心/设置/userItemCell5.h
    ✅ 更新成功: YBVideo//个人中心/设置/view/XGGSetLogoutCell.h
    ✅ 更新成功: YBVideo//个人中心/设置/view/XGGSetLogoutCell.m
    ✅ 更新成功: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 更新成功: YBVideo//个人中心/设置/userItemCell5.h

处理类名映射: HeaderBackImgView -> > XGGHeaderBackImgView
  找到引用文件:
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    YBVideo//个人中心/头部/XGGHeaderBackImgView.h
    YBVideo//个人中心/头部/XGGHeaderBackImgView.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGHeaderBackImgView.h
    ✅ 更新成功: YBVideo//个人中心/头部/XGGHeaderBackImgView.m

处理类名映射: YBCenterTopView -> > XGGYBCenterTopView
  找到引用文件:
    YBVideo//个人中心/XGGYBCenterVC.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.h
    ✅ 更新成功: YBVideo//个人中心/XGGYBCenterVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.h

处理类名映射: CenterListCell -> > XGGCenterListCell
  找到引用文件:
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.h
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.h
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.m

处理类名映射: CenterListVC -> > XGGCenterListVC
  找到引用文件:
    YBVideo//个人中心/XGGYBCenterVC.m
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.h
    ✅ 更新成功: YBVideo//个人中心/XGGYBCenterVC.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    ✅ 更新成功: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.h

处理类名映射: YBCenterVC -> > XGGYBCenterVC
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//底部导航/XGGYBTabBarController.m
    YBVideo//消息/XGGMsgTopPubVC.m
    YBVideo//消息/XGGMessageFansVC.m
    YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    YBVideo//功能/评论/XGGcommDetailCell.m
    YBVideo//功能/评论/XGGcommCell.m
    YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.m
    YBVideo//功能/腾讯消息/view/TConversationCell.m
    YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    YBVideo//功能/直播榜单/XGGLiveRankVC.m
    YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    YBVideo//个人中心/XGGYBCenterVC.m
    YBVideo//个人中心/XGGYBCenterVC.h
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 更新成功: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 更新成功: YBVideo//消息/XGGMessageFansVC.m
    ✅ 更新成功: YBVideo//直播模块/用户端相关/XGGYBPlayVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    ✅ 更新成功: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    ✅ 更新成功: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommDetailCell.m
    ✅ 更新成功: YBVideo//功能/评论/XGGcommCell.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.m
    ✅ 更新成功: YBVideo//功能/腾讯消息/view/TConversationCell.m
    ✅ 更新成功: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    ✅ 更新成功: YBVideo//功能/直播榜单/XGGLiveRankVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 更新成功: YBVideo//个人中心/XGGYBCenterVC.m
    ✅ 更新成功: YBVideo//个人中心/XGGYBCenterVC.h

处理类名映射: EditHeader -> > XGGEditHeader
  找到引用文件:
    YBVideo//个人中心/编辑资料/XGGEditHeader.h
    YBVideo//个人中心/编辑资料/XGGEditHeader.m
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditHeader.h
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditHeader.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m

处理类名映射: EditVC -> > XGGEditVC
  找到引用文件:
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    YBVideo//个人中心/编辑资料/XGGEditVC.h
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.h
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m

处理类名映射: EditCell -> > XGGEditCell
  找到引用文件:
    YBVideo//个人中心/编辑资料/XGGEditCell.m
    YBVideo//个人中心/编辑资料/XGGEditCell.h
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditCell.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditCell.h
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m

处理类名映射: Utils -> > XGGUtils
  找到引用文件:
    YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    YBVideo//工具和自定义类/XGGUtils.h
    YBVideo//工具和自定义类/XGGUtils.m
    YBVideo//工具和自定义类/Categories/ZFModalTransitionAnimator.m
    YBVideo//工具和自定义类/Vendor/NSString+Common.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGUtils.h
    ✅ 更新成功: YBVideo//工具和自定义类/XGGUtils.m
    ✅ 更新成功: YBVideo//工具和自定义类/Categories/ZFModalTransitionAnimator.m
    ✅ 更新成功: YBVideo//工具和自定义类/Vendor/NSString+Common.m

处理类名映射: mylabels -> > XGGmylabels
  找到引用文件:
    YBVideo//工具和自定义类/XGGmylabels.h
    YBVideo//工具和自定义类/XGGmylabels.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGmylabels.h
    ✅ 更新成功: YBVideo//工具和自定义类/XGGmylabels.m

处理类名映射: RKLampView -> > XGGRKLampView
  找到引用文件:
    YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    YBVideo//工具和自定义类/跑马灯/XGGRKLampView.h
    YBVideo//工具和自定义类/跑马灯/XGGRKLampView.m
    ✅ 更新成功: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 更新成功: YBVideo//工具和自定义类/跑马灯/XGGRKLampView.h
    ✅ 更新成功: YBVideo//工具和自定义类/跑马灯/XGGRKLampView.m

处理类名映射: RKHorPickerView -> > XGGRKHorPickerView
  找到引用文件:
    YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    YBVideo//工具和自定义类/XGGRKHorPickerView.m
    YBVideo//工具和自定义类/XGGRKHorPickerView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGRKHorPickerView.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGRKHorPickerView.h

处理类名映射: YBUploadProgress -> > XGGYBUploadProgress
  找到引用文件:
    YBVideo//首页/推荐/XGGYBLookVideoVC.m
    YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.h
    YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.m
    ✅ 更新成功: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 更新成功: YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.h
    ✅ 更新成功: YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.m

处理类名映射: RKCircularProgress -> > XGGRKCircularProgress
  找到引用文件:
    YBVideo//工具和自定义类/发布进度/XGGRKCircularProgress.m
    YBVideo//工具和自定义类/发布进度/XGGRKCircularProgress.h
    YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.m
    ✅ 更新成功: YBVideo//工具和自定义类/发布进度/XGGRKCircularProgress.m
    ✅ 更新成功: YBVideo//工具和自定义类/发布进度/XGGRKCircularProgress.h
    ✅ 更新成功: YBVideo//工具和自定义类/发布进度/XGGYBUploadProgress.m

处理类名映射: YBButton -> > XGGYBButton
  找到引用文件:
    YBVideo//公共方法类/XGGPublicObj.h
    YBVideo//公共方法类/XGGPublicObj.m
    YBVideo//功能/会员/XGGYBVipVC.m
    YBVideo//功能/钱包/XGGYBRechargeVC.m
    YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m
    YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
    YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.m
    YBVideo//个人中心/头部/XGGYBCenterTopView.m
    YBVideo//工具和自定义类/XGGYBButton.m
    YBVideo//工具和自定义类/XGGYBButton.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.h
    ✅ 更新成功: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 更新成功: YBVideo//功能/会员/XGGYBVipVC.m
    ✅ 更新成功: YBVideo//功能/钱包/XGGYBRechargeVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModifyVC.m
    ✅ 更新成功: YBVideo//功能/青少年/vc/XGGYBYoungModeVC.m
    ✅ 更新成功: YBVideo//个人中心/他人更多/XGGYBOtherCenterMore.m
    ✅ 更新成功: YBVideo//个人中心/头部/XGGYBCenterTopView.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGYBButton.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGYBButton.h

处理类名映射: YBAlertActionSheet -> > XGGYBAlertActionSheet
  找到引用文件:
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    YBVideo//店铺/小店主页/卖家页面/XGGSellerView.h
    YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
    YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    YBVideo//工具和自定义类/XGGYBAlertActionSheet.m
    YBVideo//工具和自定义类/XGGYBAlertActionSheet.h
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 更新成功: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.h
    ✅ 更新成功: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
    ✅ 更新成功: YBVideo//直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
    ✅ 更新成功: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGYBAlertActionSheet.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGYBAlertActionSheet.h

处理类名映射: MyTextView -> > XGGMyTextView
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h
    YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
    YBVideo//功能/百度语音/VC/XGGASRView.h
    YBVideo//功能/百度语音/VC/XGGASRView.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    YBVideo//工具和自定义类/XGGMyTextView.m
    YBVideo//工具和自定义类/XGGMyTextView.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 更新成功: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 更新成功: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    ✅ 更新成功: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h
    ✅ 更新成功: YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    ✅ 更新成功: YBVideo//功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
    ✅ 更新成功: YBVideo//功能/百度语音/VC/XGGASRView.h
    ✅ 更新成功: YBVideo//功能/百度语音/VC/XGGASRView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGMyTextView.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGMyTextView.h

处理类名映射: YBSegControl -> > XGGYBSegControl
  找到引用文件:
    YBVideo//工具和自定义类/XGGYBSegControl.m
    YBVideo//工具和自定义类/XGGYBSegControl.h
    ✅ 更新成功: YBVideo//工具和自定义类/XGGYBSegControl.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGYBSegControl.h

处理类名映射: MyTextField -> > XGGMyTextField
  找到引用文件:
    YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.h
    YBVideo//登录注册/XGGDspLoginVC.h
    YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.h
    YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.m
    YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    YBVideo//个人中心/编辑资料/XGGEditCell.h
    YBVideo//个人中心/编辑资料/XGGEditVC.m
    YBVideo//工具和自定义类/XGGMyTextField.m
    YBVideo//工具和自定义类/XGGMyTextField.h
    ✅ 更新成功: YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.h
    ✅ 更新成功: YBVideo//登录注册/XGGDspLoginVC.h
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.h
    ✅ 更新成功: YBVideo//功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 更新成功: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    ✅ 更新成功: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditCell.h
    ✅ 更新成功: YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGMyTextField.m
    ✅ 更新成功: YBVideo//工具和自定义类/XGGMyTextField.h

处理类名映射: 类名: -> @implementation TCVideoEditViewController { -> 文件名: TCVideoEditViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCMusicMixView -> 文件名: TCMusicMixView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation VideoColorInfo -> 文件名: XGGVideoColorInfo
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCRangeContentConfig -> 文件名: TCRangeContent
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCVideoRangeSlider -> 文件名: TCVideoRangeSlider
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCVideoTextFiled -> 文件名: TCVideoTextFiled
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCVideoCutView -> 文件名: TCVideoCutView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCTextCollectionCell -> 文件名: TCTextCollectionCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCTextAddView -> 文件名: TCTextAddView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCVideoPreview -> 文件名: TCVideoPreview
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCBottomTabBar -> 文件名: TCBottomTabBar
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCFilterSettingView -> 文件名: TCFilterSettingView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EffectSelectView -> 文件名: XGGEffectSelectView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCMusicInfo -> 文件名: TCMusicCollectionCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TimeSelectView -> 文件名: XGGTimeSelectView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AlbumVideoVC -> 文件名: XGGAlbumVideoVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AlbumVideoCell -> 文件名: XGGAlbumVideoCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SpeedView{ -> 文件名: XGGSpeedView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SpeedView{ -> 文件名: SpeedView.temp_caseinsensitive_rename
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TXBaseBeautyView -> 文件名: TXBaseBeautyView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation VideoRecordProcessView -> 文件名: XGGVideoRecordProcessView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBPicTransitionVC { -> 文件名: XGGYBPicTransitionVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PhotoTransitionToolbar -> 文件名: XGGPhotoTransitionToolbar
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation VerticalButton -> 文件名: XGGVerticalButton
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SmallButton -> 文件名: XGGSmallButton
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TCVideoPublishController { -> 文件名: TCVideoPublishController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBPublishCoverVC -> 文件名: XGGYBPublishCoverVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBSetChargeView -> 文件名: XGGYBSetChargeView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVideoAddGoodsVC -> 文件名: XGGYBVideoAddGoodsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation videoTopicVC -> 文件名: XGGvideoTopicVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation videoTopicCell -> 文件名: XGGvideoTopicCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVideoClassVC -> 文件名: XGGYBVideoClassVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVideoMusicView -> 文件名: XGGYBVideoMusicView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MusicClassVC -> 文件名: XGGMusicClassVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MusicModel -> 文件名: XGGMusicModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MusicHeaderView -> 文件名: XGGMusicHeaderView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MusicHeaderCell -> 文件名: XGGMusicHeaderCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MusicCell -> 文件名: XGGMusicCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AppDelegate -> 文件名: XGGAppDelegate
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBBaseViewController -> 文件名: XGGYBBaseViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBNavigationController -> 文件名: XGGYBNavigationController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBBaseAppDelegate -> 文件名: TCBaseAppDelegate
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBBaseAppDelegate -> 文件名: XGGYBBaseAppDelegate
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBHomeRedObj -> 文件名: XGGYBHomeRedObj
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGetVideoObj -> 文件名: XGGYBGetVideoObj
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBHomeViewController -> 文件名: XGGYBHomeViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLookVideoVC -> 文件名: XGGYBLookVideoVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVideoControlView -> 文件名: XGGYBVideoControlView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLookVideoCell -> 文件名: XGGYBLookVideoCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ZFCustomControlView -> 文件名: ZFCustomControlView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVideosVC -> 文件名: XGGYBVideosVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MyFollowViewController -> 文件名: XGGMyFollowViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation myVideoV -> 文件名: XGGmyVideoV
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation NearbyVideoModel -> 文件名: XGGNearbyVideoModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation VideoCollectionCell -> 文件名: XGGVideoCollectionCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RegAlertView -> 文件名: XGGRegAlertView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CountryCodeVC -> 文件名: XGGCountryCodeVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation DspLoginVC -> 文件名: XGGDspLoginVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation XGGNetworkManager -> 文件名: XGGNetworkManager
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation XGGNetworkUtils -> 文件名: XGGNetworkUtils
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation iOSNetworking -> 文件名: XGGiOSNetworking
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBShowBigImageView -> 文件名: XGGYBShowBigImageView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAlertView -> 文件名: XGGYBAlertView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKUUIDManager -> 文件名: XGGRKUUIDManager
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBImageView{ -> 文件名: XGGYBImageView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PublicView -> 文件名: XGGPublicView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKSheetBtn -> 文件名: XGGRKActionSheet
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBProgressObj -> 文件名: XGGYBProgressObj
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BGSetting -> 文件名: XGGBGSetting
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PublicObj -> 文件名: XGGPublicObj
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBNetworking -> 文件名: XGGYBNetworking
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKSysAccess -> 文件名: XGGRKSysAccess
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation GuideViewController -> 文件名: XGGGuideViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ApplyRefundVC -> 文件名: XGGApplyRefundVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SelectClassVC -> 文件名: XGGSelectClassVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityClassModel -> 文件名: XGGCommodityClassModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityClassCell -> 文件名: XGGCommodityClassCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ConfirmOrderVC -> 文件名: XGGConfirmOrderVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LookHistoryVC -> 文件名: XGGLookHistoryVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LookHistoryModel -> 文件名: XGGLookHistoryModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LookHistoryCell -> 文件名: XGGLookHistoryCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation HistoryListModel -> 文件名: XGGHistoryListModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OutsideHeadCell -> 文件名: XGGOutsideHeadCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OutsideGoodsDetailVC -> 文件名: XGGOutsideGoodsDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShareFriendCell -> 文件名: XGGShareFriendCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShareFriendVC -> 文件名: XGGShareFriendVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation FriendModel -> 文件名: XGGFriendModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShareGoodView -> 文件名: XGGShareGoodView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShareGoodsAlert -> 文件名: XGGShareGoodsAlert
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PlatformInterventionVC -> 文件名: XGGPlatformInterventionVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PublishEvaluateVC -> 文件名: XGGPublishEvaluateVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ClassificationVC -> 文件名: XGGClassificationVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ClassToExamineVC -> 文件名: XGGClassToExamineVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SelectStandardsView -> 文件名: XGGSelectStandardsView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation StandardsCell -> 文件名: XGGStandardsCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityDetailModel -> 文件名: XGGCommodityDetailModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation GuaranteeView -> 文件名: XGGGuaranteeView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGoodPlayerCtrView -> 文件名: XGGYBGoodPlayerCtrView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation GoodsDetailVC -> 文件名: XGGGoodsDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityEvaluationCell -> 文件名: XGGCommodityEvaluationCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityCell1 -> 文件名: XGGCommodityCell1
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityCell2Row2 -> 文件名: XGGCommodityCell2Row2
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityCell3 -> 文件名: XGGCommodityCell3
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityCell2Row1 -> 文件名: XGGCommodityCell2Row1
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation sliderCollectionVCell -> 文件名: XGGsliderCollectionVCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation StoreInfoView -> 文件名: XGGStoreInfoView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation sliderCollectionView{ -> 文件名: XGGsliderCollectionView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation GoodsExplainCell -> 文件名: XGGGoodsExplainCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShowDetailVC -> 文件名: XGGShowDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityDetailVC -> 文件名: XGGCommodityDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PayOrderView -> 文件名: XGGPayOrderView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AppendEvaluateVC -> 文件名: XGGAppendEvaluateVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ApplyShopVC -> 文件名: XGGApplyShopVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShopApplyStatusVC -> 文件名: XGGShopApplyStatusVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BondViewController -> 文件名: XGGBondViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AddressCell -> 文件名: XGGAddressCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RejectAddressModel -> 文件名: XGGRejectAddressModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AddressVC -> 文件名: XGGAddressVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EditAdressVC -> 文件名: XGGEditAdressVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AddressModel -> 文件名: XGGAddressModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EvaluationListCell -> 文件名: XGGEvaluationListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EvaluationListModel -> 文件名: XGGEvaluationListModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation GoodsEvaluationListVC -> 文件名: XGGGoodsEvaluationListVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BuyerGetMoneyVC -> 文件名: XGGBuyerGetMoneyVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BuyerRefundDetailVC -> 文件名: XGGBuyerRefundDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BuyerRefundModel -> 文件名: XGGBuyerRefundModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BuyerRefundHeadView -> 文件名: XGGBuyerRefundHeadView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderModel -> 文件名: XGGOrderModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderListVC -> 文件名: XGGOrderListVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderDetailModel -> 文件名: XGGOrderDetailModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderDetailVC -> 文件名: XGGOrderDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderInfoView -> 文件名: XGGOrderInfoView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderPriceView -> 文件名: XGGOrderPriceView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderPublicView -> 文件名: XGGOrderPublicView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderHeaderView -> 文件名: XGGOrderHeaderView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderListCell -> 文件名: XGGOrderListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AccountBalanceVC -> 文件名: XGGAccountBalanceVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SellerView -> 文件名: XGGSellerView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShopHomeVC -> 文件名: XGGShopHomeVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BuyerView -> 文件名: XGGBuyerView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation GetMoneyVC -> 文件名: XGGGetMoneyVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityManagementVC -> 文件名: XGGCommodityManagementVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityModel -> 文件名: XGGCommodityModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityCell -> 文件名: XGGCommodityCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OtherSellOrderDetailVC -> 文件名: XGGOtherSellOrderDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SellOrderDetailModel -> 文件名: XGGSellOrderDetailModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EditSaveAddressVC -> 文件名: XGGEditSaveAddressVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SellOrderCell -> 文件名: XGGSellOrderCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SellOrderModel -> 文件名: XGGSellOrderModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SellerOrderManagementVC -> 文件名: XGGSellerOrderManagementVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RefuseRefundVC -> 文件名: XGGRefuseRefundVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RefundDetailVC -> 文件名: XGGRefundDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RefundDetailModel -> 文件名: XGGRefundDetailModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PlatformListCell -> 文件名: XGGPlatformListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PlatformGoodsVC -> 文件名: XGGPlatformGoodsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation QualificationsVC -> 文件名: XGGQualificationsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation StockView -> 文件名: XGGStockView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EditStockVC -> 文件名: XGGEditStockVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BillCell -> 文件名: XGGBillCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BillManageVC -> 文件名: XGGBillManageVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SelCommodityClassVC -> 文件名: XGGSelCommodityClassVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AddCommodityVC -> 文件名: XGGAddCommodityVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityDetailView -> 文件名: XGGCommodityDetailView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation StandardsView -> 文件名: XGGStandardsView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CommodityTitleView -> 文件名: XGGCommodityTitleView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RelationVideoGoodsVC -> 文件名: XGGRelationVideoGoodsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation shopCell -> 文件名: XGGshopCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RelationGoodsVC -> 文件名: XGGRelationGoodsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PlatformCell -> 文件名: XGGPlatformCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation GoodsDetailViewController -> 文件名: XGGGoodsDetailViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation shopDetailVC -> 文件名: XGGshopDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RelationGoodsModel -> 文件名: XGGRelationGoodsModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation goodsShowCell -> 文件名: XGGgoodsShowCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AddGoodsVC -> 文件名: XGGAddGoodsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation WaitSendGoodsVC -> 文件名: XGGWaitSendGoodsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SendGoodsInfo -> 文件名: XGGSendGoodsInfo
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LogisticsCell -> 文件名: XGGLogisticsCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation ShopInfoVC -> 文件名: XGGShopInfoVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SellOrderPublicView -> 文件名: XGGSellOrderPublicView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RefundHeadView -> 文件名: XGGRefundHeadView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AddOtherSaleGoodsVC -> 文件名: XGGAddOtherSaleGoodsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation XGGPower -> 文件名: XGGPower
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation XGGcityDefault -> 文件名: XGGcityDefault
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKLBSManager -> 文件名: XGGRKLBSManager
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation common -> 文件名: XGGcommon
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation Config -> 文件名: XGGConfig
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBTabBar -> 文件名: XGGYBTabBar
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveOrVideo -> 文件名: XGGYBLiveOrVideo
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBTabBarController -> 文件名: XGGYBTabBarController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation chatmessageCell -> 文件名: XGGchatmessageCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderMessageVC -> 文件名: XGGOrderMessageVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MsgTopPubVC -> 文件名: XGGMsgTopPubVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MsgSysVC -> 文件名: XGGMsgSysVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OrderMessageModel -> 文件名: XGGOrderMessageModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageFansVC -> 文件名: XGGMessageFansVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SelPeopleCell -> 文件名: XGGSelPeopleCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SelPeopleV -> 文件名: XGGSelPeopleV
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MsgTopPubModel -> 文件名: XGGMsgTopPubModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MsgSysModel -> 文件名: XGGMsgSysModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageListModel -> 文件名: XGGMessageListModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageFansModel -> 文件名: XGGMessageFansModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageListCell -> 文件名: XGGMessageListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageHeaderV -> 文件名: XGGMessageHeaderV
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MsgTopPubCell -> 文件名: XGGMsgTopPubCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageFansCell -> 文件名: XGGMessageFansCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageListCell -> 文件名: MessageCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MsgSysCell -> 文件名: XGGMsgSysCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MessageListVC -> 文件名: MessageVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBPlayCtrlView -> 文件名: XGGYBPlayCtrlView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBPlayVC -> 文件名: XGGYBPlayVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCheckLiveObj -> 文件名: XGGYBCheckLiveObj
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveListVC -> 文件名: XGGYBLiveListVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveListCell -> 文件名: XGGYBLiveListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveRoomAlertView -> 文件名: XGGYBLiveRoomAlertView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveRTCManager -> 文件名: XGGYBLiveRTCManager
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBChatToolBar -> 文件名: XGGYBChatToolBar
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveEndView -> 文件名: XGGYBLiveEndView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation roomShowGoodsView -> 文件名: XGGroomShowGoodsView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation startLiveClassCell -> 文件名: XGGstartLiveClassCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation startLiveClassVC -> 文件名: XGGstartLiveClassVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveFucView -> 文件名: XGGYBLiveFucView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveCtrlView -> 文件名: XGGYBLiveCtrlView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLivePreview -> 文件名: XGGYBLivePreview
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveVC -> 文件名: XGGYBLiveVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBSocketLive -> 文件名: XGGYBSocketLive
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBSocketPlay -> 文件名: XGGYBSocketPlay
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVipCell -> 文件名: XGGYBVipCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVipHeader -> 文件名: XGGYBVipHeader
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation vipBuyView -> 文件名: XGGvipBuyView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVipVC -> 文件名: XGGYBVipVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBRechargeType -> 文件名: XGGYBRechargeType
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBRechargeVC -> 文件名: XGGYBRechargeVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation fansViewController -> 文件名: XGGfansViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BlackListCell -> 文件名: blackListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation attrViewController -> 文件名: XGGattrViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation fansModel -> 文件名: XGGfansModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BlackListVC -> 文件名: XGGBlackListVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation fans -> 文件名: XGGfans
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PubH5 -> 文件名: XGGPubH5
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation UpHotCell -> 文件名: XGGUpHotCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation HotVideoDetailVC -> 文件名: XGGHotVideoDetailVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation addHotVideoVC -> 文件名: XGGaddHotVideoVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LogFirstCell -> 文件名: XGGLogFirstCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LogFirstCell2 -> 文件名: XGGLogFirstCell2
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation Loginbonus -> 文件名: XGGLoginbonus
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation searchVC -> 文件名: XGGsearchVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation HXSearchBar -> 文件名: HXSearchBar
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SearchHistoryCell -> 文件名: XGGSearchHistoryCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBSearchBarView -> 文件名: XGGYBSearchBarView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBStorageObj -> 文件名: XGGYBStorageObj
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation StorageConfig -> 文件名: XGGStorageConfig
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation commDetailCell -> 文件名: XGGcommDetailCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation commentview -> 文件名: XGGcommentview
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation commCell{ -> 文件名: XGGcommCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation detailmodel -> 文件名: XGGdetailmodel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCommentToolBar -> 文件名: XGGYBCommentToolBar
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation commentModel -> 文件名: XGGcommentModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBYoungManager -> 文件名: XGGYBYoungManager
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBYoungModifyVC -> 文件名: XGGYBYoungModifyVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKCodeInputView -> 文件名: XGGRKCodeInputView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKCodeView -> 文件名: XGGRKCodeView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBYoungModeVC -> 文件名: XGGYBYoungModeVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBYoungSetVC -> 文件名: XGGYBYoungSetVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBYoungSmall -> 文件名: XGGYBYoungSmall
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation BusinessCardVC -> 文件名: XGGBusinessCardVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKKeepAlive -> 文件名: XGGRKKeepAlive
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MyAdvertVC -> 文件名: XGGMyAdvertVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation AdvertManagerVC -> 文件名: XGGAdvertManagerVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MyAdvertCell -> 文件名: XGGMyAdvertCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation lookVGoodsDView{ -> 文件名: XGGlookVGoodsDView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBInviteCode -> 文件名: XGGYBInviteCode
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBInvitationView{ -> 文件名: XGGYBInvitationView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveReportVC -> 文件名: XGGYBLiveReportVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBVideoReportVC -> 文件名: XGGYBVideoReportVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBReportCell -> 文件名: XGGYBReportCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLanguageTools -> 文件名: XGGYBLanguageTools
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBTakeSameVideoVC -> 文件名: XGGYBTakeSameVideoVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBDestroyAccount -> 文件名: XGGYBDestroyAccount
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBDestroySureVC -> 文件名: XGGYBDestroySureVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBDestroyCell -> 文件名: XGGYBDestroyCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CollectionCellWhite -> 文件名: XGGYBGiftView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBPageControl -> 文件名: XGGYBPageControl
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKShowPaintedView -> 文件名: XGGRKShowPaintedView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKPaintedGiftView -> 文件名: XGGRKPaintedGiftView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGiftPage -> 文件名: XGGYBGiftPage
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation exoensiveGifGiftV{ -> 文件名: XGGexoensiveGifGiftV
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation expensiveGiftV -> 文件名: XGGexpensiveGiftV
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKPopView -> 文件名: XGGcontinueGift
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation liansongBackView -> 文件名: XGGliansongBackView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CFGradientLabel -> 文件名: CFGradientLabel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGiftModel -> 文件名: XGGYBGiftModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGiftModel -> 文件名: GiftModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGiftCell -> 文件名: XGGYBGiftCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGiftCell -> 文件名: GiftCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TYAutoPurgeCache -> 文件名: TYPagerViewLayout
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TYTabPagerBar -> 文件名: TYTabPagerBar
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TYTabPagerBarLayout -> 文件名: TYTabPagerBarLayout
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TYPagerView -> 文件名: TYPagerView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation TYTabPagerBarCell -> 文件名: TYTabPagerBarCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation topicVideoCell -> 文件名: XGGtopicVideoCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation topicDetailsVC -> 文件名: XGGtopicDetailsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation guardShowView{ -> 文件名: XGGguardShowView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation guardListModel -> 文件名: XGGguardListModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation guardListCell -> 文件名: XGGguardListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation guardAlertView -> 文件名: XGGguardAlertView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation grardButton -> 文件名: XGGgrardButton
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation shouhuView{ -> 文件名: XGGshouhuView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAnchorPKView{ -> 文件名: XGGYBAnchorPKView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAnchorPKAlert{ -> 文件名: XGGYBAnchorPKAlert
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBPkProgressView{ -> 文件名: XGGYBPkProgressView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAnchorLinkInfo -> 文件名: XGGYBAnchorLinkInfo
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLinkAlertView{ -> 文件名: XGGYBLinkAlertView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAnchorOnline -> 文件名: XGGYBAnchorOnline
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAnchorOnlineCell -> 文件名: XGGYBAnchorOnlineCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBTxLinkMicView -> 文件名: XGGYBTxLinkMicView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CSActionSheet -> 文件名: CSActionSheet
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CSActionPicker -> 文件名: CSActionPicker
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBUserListView -> 文件名: XGGYBUserListView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBUserListModel -> 文件名: XGGYBUserListModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBUserListCell -> 文件名: XGGYBUserListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation adminCell -> 文件名: XGGadminCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation adminLists -> 文件名: XGGadminLists
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBDayTaskVC -> 文件名: XGGYBDayTaskVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBDayTaskView -> 文件名: XGGYBDayTaskView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBDayTaskCell -> 文件名: XGGYBDayTaskCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBDayTaskManager -> 文件名: XGGYBDayTaskManager
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGoodsBriefView -> 文件名: XGGYBGoodsBriefView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveChatView -> 文件名: XGGYBLiveChatView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveChatModel -> 文件名: XGGYBLiveChatModel
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBLiveChatCell -> 文件名: XGGYBLiveChatCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBOnSaleView -> 文件名: XGGYBOnSaleView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBOnSaleCell -> 文件名: XGGYBOnSaleCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation UserBulletWindow{ -> 文件名: XGGUserBulletWindow
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation userLevelView -> 文件名: XGGuserLevelView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation turntableView{ -> 文件名: XGGturntableView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation turntableRecordCell -> 文件名: XGGturntableRecordCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation turntableResultCell -> 文件名: XGGturntableResultCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation turntableResultView -> 文件名: XGGturntableResultView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation turntableRuleView{ -> 文件名: XGGturntableRuleView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBUserEnterAnimation -> 文件名: XGGYBUserEnterAnimation
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation huanxinsixinview{ -> 文件名: XGGhuanxinsixinview
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBImRoomSmallView -> 文件名: XGGYBImRoomSmallView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LiveRankCell -> 文件名: XGGLiveRankCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation LiveRankVC -> 文件名: XGGLiveRankVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation PublishShareV -> 文件名: XGGPublishShareV
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBShareView -> 文件名: XGGYBShareView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBShareViewCell -> 文件名: XGGYBShareViewCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation NearbyVC -> 文件名: XGGNearbyVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCitySelCell -> 文件名: XGGYBCitySelCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCitySelVC -> 文件名: XGGYBCitySelVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation NearbyCell -> 文件名: XGGNearbyCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation commodityRecordsCell -> 文件名: XGGcommodityRecordsCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation commodityRecordsVC -> 文件名: XGGcommodityRecordsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCenterMoreView -> 文件名: XGGYBCenterMoreView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBRedProfitVC -> 文件名: XGGYBRedProfitVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation UITextField (WLRange) -> 文件名: WLCardNoFormatter
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAddTypeView{ -> 文件名: XGGYBAddTypeView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGetTypeListCell -> 文件名: XGGYBGetTypeListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGetProVC -> 文件名: XGGYBGetProVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGetTypeListVC -> 文件名: XGGYBGetTypeListVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGoodsLikeCell -> 文件名: XGGYBGoodsLikeCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGoodsLikeVC -> 文件名: XGGYBGoodsLikeVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBApplyStoreVC -> 文件名: XGGYBApplyStoreVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBApplyConditionVC -> 文件名: XGGYBApplyConditionVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBApplyConditionCell -> 文件名: XGGYBApplyConditionCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RoomUserTypeCell -> 文件名: XGGRoomUserTypeCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation OtherRoomViewController -> 文件名: XGGOtherRoomViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RoomUserListViewController -> 文件名: XGGRoomUserListViewController
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RoomManagementVC -> 文件名: XGGRoomManagementVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation watchingRecordsVC -> 文件名: XGGwatchingRecordsVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation WatchRecordListCell -> 文件名: XGGWatchRecordListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation accountDetails -> 文件名: XGGaccountDetails
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGoodsInfoVC -> 文件名: XGGYBGoodsInfoVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGoodsListCell -> 文件名: XGGYBGoodsListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBGoodsListVC -> 文件名: XGGYBGoodsListVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCenterMoreCell -> 文件名: XGGYBCenterMoreCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation depositAccountVC -> 文件名: XGGdepositAccountVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation orderVideoCell -> 文件名: XGGorderVideoCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBOtherCenterMore -> 文件名: XGGYBOtherCenterMore
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCenterVC -> 文件名: XGGYBCenterVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SetCell -> 文件名: XGGSetCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SetLogoutCell -> 文件名: XGGSetLogoutCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBUserAuthVC -> 文件名: XGGYBUserAuthVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation SetViewControllor -> 文件名: XGGSetViewControllor
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBPrivateVC -> 文件名: XGGYBPrivateVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBCenterTopView -> 文件名: XGGYBCenterTopView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation HeaderBackImgView -> 文件名: XGGHeaderBackImgView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CenterListVC -> 文件名: XGGCenterListVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CenterListCell -> 文件名: XGGCenterListCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EditCell -> 文件名: XGGEditCell
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EditHeader -> 文件名: XGGEditHeader
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation EditVC -> 文件名: XGGEditVC
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKHorPickerView { -> 文件名: XGGRKHorPickerView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBAlertActionSheet -> 文件名: XGGYBAlertActionSheet
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBButton -> 文件名: XGGYBButton
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MyTextView -> 文件名: XGGMyTextView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation MyTextField -> 文件名: XGGMyTextField
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBSegControl -> 文件名: XGGYBSegControl
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKLampView -> 文件名: XGGRKLampView
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation RKCircularProgress -> 文件名: XGGRKCircularProgress
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation YBUploadProgress -> 文件名: XGGYBUploadProgress
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation Utils -> 文件名: XGGUtils
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation CCAnimationBtn -> 文件名: CCAnimationBtn
  未找到引用该类的文件

处理类名映射: 类名: -> @implementation mylabels -> 文件名: XGGmylabels
  未找到引用该类的文件

