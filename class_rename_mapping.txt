@interface TCVideoEditViewController : YBBaseViewController -> TCVideoEditViewController
@interface TCVideoPreview : UIView<TXVideoPreviewListener> -> TCVideoPreview
@interface TCVideoRangeSlider : UIView -> TCVideoRangeSlider
@interface TCRangeContentConfig : NSObject -> TCRangeContent
@interface VideoColorInfo : NSObject -> XGGVideoColorInfo
@interface EffectSelectView : UIView -> XGGEffectSelectView
@interface TCFilterSettingView : UIView -> TCFilterSettingView
@interface TCBottomTabBar : UIView -> TCBottomTabBar
@interface TimeSelectView : UIView -> XGGTimeSelectView
@interface TCMusicInfo : NSObject -> TCMusicCollectionCell
@interface TCVideoCutView : UIView -> TCVideoCutView
@interface TCVideoTextFiled : UIView -> TCVideoTextFiled
@interface TCMusicMixView : UIView -> TCMusicMixView
@interface TCTextAddView : UIView -> TCTextAddView
@interface TCTextCollectionCell : UICollectionViewCell -> TCTextCollectionCell
@interface TCVideoRecordViewController : YBBaseViewController -> TCVideoRecordViewController
@interface AlbumVideoCell : UICollectionViewCell -> XGGAlbumVideoCell
@interface AlbumVideoVC : YBBaseViewController -> XGGAlbumVideoVC
@interface VideoRecordProcessView : UIView -> XGGVideoRecordProcessView
@interface TXBaseBeautyView : UIView -> TXBaseBeautyView
@interface SpeedView : UIView -> XGGSpeedView
@interface YBPicTransitionVC : YBBaseViewController -> XGGYBPicTransitionVC
@interface SmallButton : UIButton -> XGGSmallButton
@interface PhotoTransitionToolbar : UIView -> XGGPhotoTransitionToolbar
@interface VerticalButton : UIButton -> XGGVerticalButton
@interface YBPublishCoverVC : YBBaseViewController -> XGGYBPublishCoverVC
@interface TCVideoPublishController : YBBaseViewController<UITextViewDelegate> -> TCVideoPublishController
@interface YBSetChargeView : UIView -> XGGYBSetChargeView
@interface YBVideoAddGoodsVC : YBBaseViewController -> XGGYBVideoAddGoodsVC
@interface videoTopicCell : UITableViewCell -> XGGvideoTopicCell
@interface videoTopicVC : YBBaseViewController -> XGGvideoTopicVC
@interface YBVideoClassVC : YBBaseViewController -> XGGYBVideoClassVC
@interface MusicClassVC : YBBaseViewController -> XGGMusicClassVC
@interface YBVideoMusicView : YBBaseViewController -> XGGYBVideoMusicView
@interface MusicModel : NSObject -> XGGMusicModel
@interface MusicCell : UITableViewCell -> XGGMusicCell
@interface MusicHeaderView : UIView -> XGGMusicHeaderView
@interface MusicHeaderCell : UICollectionViewCell -> XGGMusicHeaderCell
@interface YBBaseAppDelegate : UIResponder<UIApplicationDelegate> -> XGGYBBaseAppDelegate
@interface RKLBSManager : NSObject -> XGGRKLBSManager
@interface Config : NSObject -> Config
@interface XGGcityDefault : NSObject -> XGGcityDefault
@interface common : NSObject -> common
@interface sproutCommon : NSObject -> sproutCommon
@interface AppDelegate : NSObject -> XGGAppDelegate
@interface YBBaseViewController : UIViewController -> XGGYBBaseViewController
@interface YBNavigationController : UINavigationController -> XGGYBNavigationController
@interface YBNavigationController : UINavigationController -> TCNavigationController
@interface YBGetVideoObj : NSObject -> XGGYBGetVideoObj
@interface YBHomeRedObj : NSObject -> XGGYBHomeRedObj
@interface YBLookVideoCell : UICollectionViewCell -> XGGYBLookVideoCell
@interface ZFCustomControlView : UIView <ZFPlayerMediaControl> -> ZFCustomControlView
@interface YBVideoControlView : UIView<ZFPlayerMediaControl> -> XGGYBVideoControlView
@interface YBLookVideoVC : YBBaseViewController -> XGGYBLookVideoVC
@interface NearbyVideoModel : NSObject -> XGGNearbyVideoModel
@interface MyFollowViewController : YBBaseViewController -> XGGMyFollowViewController
@interface YBVideosVC : YBBaseViewController -> XGGYBVideosVC
@interface myVideoV : YBBaseViewController -> XGGmyVideoV
@interface VideoCollectionCell : UICollectionViewCell -> XGGVideoCollectionCell
@interface YBHomeViewController : YBBaseViewController -> XGGYBHomeViewController
@interface DspLoginVC : YBBaseViewController -> XGGDspLoginVC
@interface RegAlertView : UIView -> XGGRegAlertView
@interface CountryCodeVC : YBBaseViewController -> XGGCountryCodeVC
@interface XGGNetworkManager : NSObject -> XGGNetworkManager
@interface XGGNetworkUtils : NSObject -> XGGNetworkUtils
@interface RKActionSheet : UIView -> XGGRKActionSheet
@interface YBProgressObj : NSObject -> XGGYBProgressObj
@interface PublicObj : NSObject -> XGGPublicObj
@interface YBNetworking : NSObject -> XGGYBNetworking
@interface BGSetting : NSObject -> XGGBGSetting
@interface RKSysAccess : NSObject -> XGGRKSysAccess
@interface iOSNetworking : NSObject -> XGGiOSNetworking
@interface YBAlertView : UIView -> XGGYBAlertView
@interface RKUUIDManager : NSObject -> XGGRKUUIDManager
@interface YBShowBigImageView : UIScrollView -> XGGYBShowBigImageView
@interface YBImageView : UIView -> XGGYBImageView
@interface PublicView : UIView -> XGGPublicView
@interface GuideViewController : YBBaseViewController -> XGGGuideViewController
@interface ApplyRefundVC : YBBaseViewController -> XGGApplyRefundVC
@interface SelectClassVC : YBBaseViewController -> XGGSelectClassVC
@interface CommodityClassModel : NSObject -> XGGCommodityClassModel
@interface CommodityClassCell : UITableViewCell -> XGGCommodityClassCell
@interface ConfirmOrderVC : YBBaseViewController -> XGGConfirmOrderVC
@interface HistoryListModel : NSObject -> XGGHistoryListModel
@interface LookHistoryCell : UITableViewCell -> XGGLookHistoryCell
@interface LookHistoryVC : YBBaseViewController -> XGGLookHistoryVC
@interface LookHistoryModel : NSObject -> XGGLookHistoryModel
@interface OutsideGoodsDetailVC : YBBaseViewController -> XGGOutsideGoodsDetailVC
@interface OutsideHeadCell : UITableViewCell<SDCycleScrollViewDelegate> -> XGGOutsideHeadCell
@interface ShareGoodsAlert : UIView -> XGGShareGoodsAlert
@interface ShareFriendVC : YBBaseViewController -> XGGShareFriendVC
@interface ShareFriendCell : UITableViewCell -> XGGShareFriendCell
@interface FriendModel : NSObject -> XGGFriendModel
@interface ShareGoodView : UIView -> XGGShareGoodView
@interface PlatformInterventionVC : YBBaseViewController -> XGGPlatformInterventionVC
@interface CWStarRateView : UIView -> CWStarRateView
@interface PublishEvaluateVC : YBBaseViewController -> XGGPublishEvaluateVC
@interface ClassToExamineVC : YBBaseViewController -> XGGClassToExamineVC
@interface ClassificationVC : YBBaseViewController -> XGGClassificationVC
@interface StandardsCell : UICollectionViewCell -> XGGStandardsCell
@interface SelectStandardsView : UIView<UICollectionViewDelegate, UICollectionViewDataSource> -> XGGSelectStandardsView
@interface GuaranteeView : UIView<UIGestureRecognizerDelegate> -> XGGGuaranteeView
@interface sliderCollectionVCell : UICollectionViewCell -> XGGsliderCollectionVCell
@interface sliderCollectionView : UIView<UIScrollViewDelegate> -> XGGsliderCollectionView
@interface CommodityDetailVC : YBBaseViewController -> XGGCommodityDetailVC
@interface ShowDetailVC : UIViewController -> XGGShowDetailVC
@interface GoodsExplainCell : UITableViewCell<WKNavigationDelegate,UIScrollViewDelegate> -> XGGGoodsExplainCell
@interface CommodityCell3 : UITableViewCell -> XGGCommodityCell3
@interface CommodityCell2Row2 : UITableViewCell -> XGGCommodityCell2Row2
@interface CommodityCell2Row1 : UITableViewCell -> XGGCommodityCell2Row1
@interface CommodityCell1 : UITableViewCell<SDCycleScrollViewDelegate> -> XGGCommodityCell1
@interface CommodityDetailModel : NSObject -> XGGCommodityDetailModel
@interface YBGoodPlayerCtrView : UIView <ZFPlayerMediaControl> -> XGGYBGoodPlayerCtrView
@interface StoreInfoView : UIView -> XGGStoreInfoView
@interface GoodsDetailVC : YBBaseViewController -> XGGGoodsDetailVC
@interface CommodityEvaluationCell : UITableViewCell<CWStarRateViewDelegate> -> XGGCommodityEvaluationCell
@interface PayOrderView : UIView<UITableViewDelegate, UITableViewDataSource> -> XGGPayOrderView
@interface AppendEvaluateVC : YBBaseViewController -> XGGAppendEvaluateVC
@interface ApplyShopVC : YBBaseViewController -> XGGApplyShopVC
@interface ShopApplyStatusVC : YBBaseViewController -> XGGShopApplyStatusVC
@interface BondViewController : YBBaseViewController -> XGGBondViewController
@interface AddressModel : NSObject -> XGGAddressModel
@interface EditAdressVC : YBBaseViewController -> XGGEditAdressVC
@interface RejectAddressModel : NSObject -> XGGRejectAddressModel
@interface AddressCell : UITableViewCell -> XGGAddressCell
@interface AddressVC : YBBaseViewController -> XGGAddressVC
@interface GoodsEvaluationListVC : YBBaseViewController -> XGGGoodsEvaluationListVC
@interface EvaluationListModel : NSObject -> XGGEvaluationListModel
@interface EvaluationListCell : UITableViewCell<CWStarRateViewDelegate> -> XGGEvaluationListCell
@interface BuyerGetMoneyVC : UIViewController -> XGGBuyerGetMoneyVC
@interface BuyerRefundDetailVC : YBBaseViewController -> XGGBuyerRefundDetailVC
@interface BuyerRefundModel : NSObject -> XGGBuyerRefundModel
@interface BuyerRefundHeadView : UIView -> XGGBuyerRefundHeadView
@interface MJPropertyKey : NSObject -> MJPropertyKey
@interface MJFoundation : NSObject -> MJFoundation
@interface MJPropertyType : NSObject -> MJPropertyType
@interface MJProperty : NSObject -> MJProperty
@interface OrderDetailVC : UIViewController -> XGGOrderDetailVC
@interface OrderDetailModel : NSObject -> XGGOrderDetailModel
@interface OrderPublicView : UIView -> XGGOrderPublicView
@interface OrderHeaderView : UIView -> XGGOrderHeaderView
@interface OrderInfoView : UIView -> XGGOrderInfoView
@interface OrderPriceView : UIView -> XGGOrderPriceView
@interface OrderListCell : UITableViewCell -> XGGOrderListCell
@interface OrderModel : NSObject -> XGGOrderModel
@interface OrderListVC : YBBaseViewController -> XGGOrderListVC
@interface AccountBalanceVC : YBBaseViewController -> XGGAccountBalanceVC
@interface ShopHomeVC : UIViewController -> XGGShopHomeVC
@interface SellerView : UIView<JMessageDelegate> -> XGGSellerView
@interface BuyerView : UIView -> XGGBuyerView
@interface GetMoneyVC : UIViewController -> XGGGetMoneyVC
@interface CommodityCell : UITableViewCell -> XGGCommodityCell
@interface CommodityModel : NSObject -> XGGCommodityModel
@interface CommodityManagementVC : YBBaseViewController -> XGGCommodityManagementVC
@interface OtherSellOrderDetailVC : UIViewController -> XGGOtherSellOrderDetailVC
@interface SellOrderDetailModel : NSObject -> XGGSellOrderDetailModel
@interface EditSaveAddressVC : YBBaseViewController -> XGGEditSaveAddressVC
@interface SellerOrderManagementVC : YBBaseViewController -> XGGSellerOrderManagementVC
@interface SellOrderCell : UITableViewCell -> XGGSellOrderCell
@interface SellOrderModel : NSObject -> XGGSellOrderModel
@interface RefundDetailVC : YBBaseViewController -> XGGRefundDetailVC
@interface RefundDetailModel : NSObject -> XGGRefundDetailModel
@interface RefuseRefundVC : YBBaseViewController -> XGGRefuseRefundVC
@interface PlatformListCell : UICollectionViewCell -> XGGPlatformListCell
@interface PlatformGoodsVC : YBBaseViewController -> XGGPlatformGoodsVC
@interface QualificationsVC : YBBaseViewController -> XGGQualificationsVC
@interface EditStockVC : YBBaseViewController -> XGGEditStockVC
@interface StockView : UIView -> XGGStockView
@interface BillManageVC : YBBaseViewController -> XGGBillManageVC
@interface BillCell : UITableViewCell -> XGGBillCell
@interface AddCommodityVC : YBBaseViewController -> XGGAddCommodityVC
@interface SelCommodityClassVC : YBBaseViewController -> XGGSelCommodityClassVC
@interface StandardsView : UIView<TZImagePickerControllerDelegate,UITextFieldDelegate> -> XGGStandardsView
@interface CommodityTitleView : UIView<TZImagePickerControllerDelegate,UITextFieldDelegate> -> XGGCommodityTitleView
@interface CommodityDetailView : UIView<TZImagePickerControllerDelegate,UITextViewDelegate> -> XGGCommodityDetailView
@interface RelationVideoGoodsVC : YBBaseViewController -> XGGRelationVideoGoodsVC
@interface RelationGoodsModel : NSObject -> XGGRelationGoodsModel
@interface goodsShowCell : UITableViewCell -> XGGgoodsShowCell
@interface shopDetailVC : YBBaseViewController -> XGGshopDetailVC
@interface shopCell : UICollectionViewCell -> XGGshopCell
@interface AddGoodsVC : YBBaseViewController -> XGGAddGoodsVC
@interface RelationGoodsVC : YBBaseViewController -> XGGRelationGoodsVC
@interface PlatformCell : UICollectionViewCell -> XGGPlatformCell
@interface GoodsDetailViewController : YBBaseViewController -> XGGGoodsDetailViewController
@interface SendGoodsInfo : UIView<UITextFieldDelegate> -> XGGSendGoodsInfo
@interface LogisticsCell : UITableViewCell -> XGGLogisticsCell
@interface WaitSendGoodsVC : YBBaseViewController -> XGGWaitSendGoodsVC
@interface ShopInfoVC : YBBaseViewController -> XGGShopInfoVC
@interface RefundHeadView : UIView -> XGGRefundHeadView
@interface SellOrderPublicView : UIView -> XGGSellOrderPublicView
@interface AddOtherSaleGoodsVC : YBBaseViewController -> XGGAddOtherSaleGoodsVC
@interface XGGPower : NSObject -> XGGPower
@interface RKLBSManager : NSObject -> XGGRKLBSManager
@interface XGGcityDefault : NSObject -> XGGcityDefault
@interface common : NSObject -> XGGcommon
@interface Config : NSObject -> XGGConfig
@interface YBTabBarController : UITabBarController -> XGGYBTabBarController
@interface YBLiveOrVideo : UIView -> XGGYBLiveOrVideo
@interface YBTabBar : UITabBar -> XGGYBTabBar
@interface QPolygon : QMultiPoint <QOverlay> -> XGGQPolygon
@interface QOverlayPathView : QOverlayView -> XGGQOverlayPathView
@interface QCircle : QShape <QOverlay> -> XGGQCircle
@interface QUserLocation : NSObject <QAnnotation> -> XGGQUserLocation
@interface QPolyline : QMultiPoint <QOverlay> -> XGGQPolyline
@interface QPointAnnotation : QShape -> XGGQPointAnnotation
@interface QCircleView : QOverlayPathView -> XGGQCircleView
@interface QStyledPolylineView : QPolylineView -> XGGQStyledPolylineView
@interface QPolylineView : QOverlayPathView -> XGGQPolylineView
@interface QMultiPoint : QShape{ -> XGGQMultiPoint
@interface QMapServices : NSObject -> QMapServices
@interface QOverlayView : UIView -> XGGQOverlayView
@interface QMapView : UIView <NSCoding> -> QMapView
@interface QPinAnnotationView : QAnnotationView -> XGGQPinAnnotationView
@interface QAnnotationView : UIView -> XGGQAnnotationView
@interface QShape : NSObject <QAnnotation> { -> XGGQShape
@interface NSValue (NSValueQGeometryExtensions) -> QGeometry
@interface QPolygonView : QOverlayPathView -> XGGQPolygonView
@interface QMSSearchOption : NSObject -> QMSSearchOption
@interface QMSSearchServices : NSObject -> QMSSearchServices
@interface QMSSearchOperation : NSObject -> QMSSearchOperation
@interface QMSSearcher : NSObject -> QMSSearcher
@interface QMSBaseResult : NSObject -> QMSSearchResult
@interface MHBeautiesModel : NSObject -> MHBeautiesModel
@interface MHFilterModel : NSObject -> MHFilterModel
@interface MHActionView : UIView -> MHActionView
@interface MHCompleteBeautyView : UIView -> MHCompleteBeautyView
@interface MHBeautySlider : UISlider -> MHBeautySlider
@interface MHFiltersView : UIView -> MHFiltersView
@interface WNSegmentControl :UIControl -> WNSegmentControl
@interface WNSegmentItem : UIView -> WNSegmentItem
@interface MHBeautyFaceView : UIView -> MHBeautyFaceView
@interface MHSpecificAssembleView : UIView -> MHSpecificAssembleView
@interface MHStickersView : UIView -> MHStickersView
@interface MHBeautyMenuCell : UICollectionViewCell -> MHBeautyMenuCell
@interface MHPrintView : UIView -> MHPrintView
@interface MHBeautyView : UIView -> MHBeautyView
@interface MHMakeUpView : UIView -> MHMakeUpView
@interface MHSectionStickersView : UIView -> MHSectionStickersView
@interface MHMagnifiedView : UIView -> MHMagnifiedView
@interface MHSpecificEffectView : UIView -> MHSpecificEffectView
@interface MHBottomView : UIView -> MHBottomView
@interface MHBeautyAssembleView : UIView -> MHBeautyAssembleView
@interface MHStickerIndicatorView : UIView -> MHStickerCell
@interface MHMeiyanMenusView : UIView -> MHMeiyanMenusView
@interface sproutCommon : NSObject -> XGGsproutCommon
@interface StickerManager : NSObject -> XGGStickerManager
@interface StickerDataListModel : NSObject -> XGGStickerDataListModel
@interface MHBeautyManager : NSObject -> MHBeautyManager
@interface MHSDK : NSObject -> MHSDK
@interface MHZipArchive : NSObject -> MHZipArchive
@interface MessageFansVC : YBBaseViewController -> XGGMessageFansVC
@interface SelPeopleCell : UITableViewCell -> XGGSelPeopleCell
@interface SelPeopleV : UIView -> XGGSelPeopleV
@interface MsgSysModel : NSObject -> XGGMsgSysModel
@interface MessageListModel : NSObject -> XGGMessageListModel
@interface MessageFansModel : NSObject -> XGGMessageFansModel
@interface MsgTopPubModel : NSObject -> XGGMsgTopPubModel
@interface MessageFansCell : UITableViewCell -> XGGMessageFansCell
@interface MsgSysCell : UITableViewCell -> XGGMsgSysCell
@interface MessageListCell : UITableViewCell -> XGGMessageListCell
@interface MessageHeaderV : UIView -> XGGMessageHeaderV
@interface MsgTopPubCell : UITableViewCell -> XGGMsgTopPubCell
@interface OrderMessageVC : YBBaseViewController -> XGGOrderMessageVC
@interface chatmessageCell : UITableViewCell -> XGGchatmessageCell
@interface OrderMessageModel : NSObject -> XGGOrderMessageModel
@interface MsgSysVC : YBBaseViewController -> XGGMsgSysVC
@interface MsgTopPubVC : YBBaseViewController -> XGGMsgTopPubVC
@interface YBPlayVC : YBBaseViewController -> XGGYBPlayVC
@interface YBPlayCtrlView : UIView -> XGGYBPlayCtrlView
@interface YBCheckLiveObj : NSObject -> XGGYBCheckLiveObj
@interface YBLiveListVC : YBBaseViewController -> XGGYBLiveListVC
@interface YBLiveListCell : UICollectionViewCell -> XGGYBLiveListCell
@interface YBLiveRoomAlertView : UIView -> XGGYBLiveRoomAlertView
@interface YBLiveRTCManager : NSObject -> XGGYBLiveRTCManager
@interface YBLiveEndView : UIView -> XGGYBLiveEndView
@interface YBChatToolBar : UIView -> XGGYBChatToolBar
@interface roomShowGoodsView : UIView -> XGGroomShowGoodsView
@interface YBLiveVC : YBBaseViewController -> XGGYBLiveVC
@interface YBLiveFucView : UIView -> YBLiveFunView
@interface startLiveClassCell : UITableViewCell -> XGGstartLiveClassCell
@interface startLiveClassVC : UIViewController -> XGGstartLiveClassVC
@interface YBLiveCtrlView : UIView -> XGGYBLiveCtrlView
@interface YBLivePreview : UIView -> XGGYBLivePreview
@interface YBLiveFucView : UIView -> XGGYBLiveFucView
@interface YBSocketPlay : NSObject -> XGGYBSocketPlay
@interface YBSocketLive : NSObject -> XGGYBSocketLive
@interface YBVipVC : YBBaseViewController -> XGGYBVipVC
@interface vipBuyView : UIView -> XGGvipBuyView
@interface YBVipCell : UITableViewCell -> XGGYBVipCell
@interface YBVipHeader : UIView -> XGGYBVipHeader
@interface YBRechargeVC : YBBaseViewController -> XGGYBRechargeVC
@interface YBRechargeType : NSObject -> XGGYBRechargeType
@interface RSADataVerifier : NSObject <DataVerifier> { -> XGGRSADataVerifier
@interface NSData (NSDataBase64Additions) -> NSDataEx
@interface Base64 : NSObject
 -> base64
@interface RSADataSigner : NSObject <DataSigner> { -> XGGRSADataSigner
@interface MD5DataSigner : NSObject <DataSigner> { -> XGGMD5DataSigner
@interface Order : NSObject -> XGGOrder
@interface fansModel : NSObject -> XGGfansModel
@interface BlackListVC : YBBaseViewController -> XGGBlackListVC
@interface fans : UITableViewCell -> XGGfans
@interface BlackListCell : UITableViewCell -> blackListCell
@interface fansViewController : YBBaseViewController -> XGGfansViewController
@interface attrViewController : YBBaseViewController -> XGGattrViewController
@interface JCHATConversationViewController : YBBaseViewController < -> JCHATConversationViewController
@interface JCHATRawAudioDataPlayer : NSObject { -> JCHATRawAudioDataPlayer
@interface JCHATChatModel : NSObject -> JCHATChatModel
@interface JCHATAudioPlayerHelper : NSObject <AVAudioPlayerDelegate> -> JCHATAudioPlayerHelper
@interface JCHATMessageTableView : UITableView -> JCHATMessageTableView
@interface JCHATMoreView : UIView -> JCHATMoreView
@interface JCHATMessageTextView : UITextView -> JCHATMessageTextView
@interface JCHATToolBar : UIView<UITextViewDelegate> -> JCHATToolBar
@interface JCHATShowTimeCell : UITableViewCell -> JCHATShowTimeCell
@interface JCHATMessageTableViewCell : UITableViewCell <XHAudioPlayerHelperDelegate, -> JCHATMessageTableViewCell
@interface JCHATLoadMessageTableViewCell : UITableViewCell -> JCHATLoadMessageTableViewCell
@interface JCHATMessageContentView :UIImageView -> JCHATMessageContentView
@interface XHVoiceCommonHelper : NSObject -> XHVoiceCommonHelper
@interface XHVoiceRecordHelper : NSObject -> XHVoiceRecordHelper
@interface XHVoiceRecordHUD : UIView -> XHVoiceRecordHUD
@interface JCHATRecordAnimationView : UIView -> JCHATRecordAnimationView
@interface LocationCell : UITableViewCell -> XGGLocationCell
@interface SearchResultView : UIView -> XGGSearchResultView
@interface TencentLocationVC : YBBaseViewController -> XGGTencentLocationVC
@interface ViewUtil : NSObject -> XGGViewUtil
@interface JCHATTimeOutManager : NSObject -> JCHATTimeOutManager
@interface JCHATStringUtils : NSObject -> JCHATStringUtils
@interface JCHATSendMsgManager : NSObject -> JCHATSendMsgManager
@interface JCHATSendMsgController : NSObject<JMessageDelegate> -> JCHATSendMsgController
@interface JCHATFileManager : NSObject -> JCHATFileManager
@interface JCHATAlertToSendImage : NSObject -> JCHATAlertToSendImage
@interface twEmojiView : UIView<UICollectionViewDelegate,UICollectionViewDataSource> -> XGGtwEmojiView
@interface LWLCollectionViewHorizontalLayout : UICollectionViewFlowLayout -> LWLCollectionViewHorizontalLayout
@interface emojiCell : UICollectionViewCell -> XGGemojiCell
@interface MJPhotoToolbar : UIView -> MJPhotoToolbar
@interface MJPhotoProgressView : UIView -> MJPhotoProgressView
@interface MJPhotoBrowser : UIViewController <UIScrollViewDelegate> -> MJPhotoBrowser
@interface MJPhotoLoadingView : UIView -> MJPhotoLoadingView
@interface MJPhotoView : UIScrollView <UIScrollViewDelegate> -> MJPhotoView
@interface MJPhoto : NSObject -> MJPhoto
@interface ChatBubbleLayer : CALayer -> XGGChatBubbleLayer
@interface ChatImageBubble : UIImageView -> XGGChatImageBubble
@interface JCHATAlbumViewController : YBBaseViewController -> JCHATAlbumViewController
@interface JCHATPhotoBrowserViewController : UIViewController -> JCHATPhotoBrowserViewController
@interface JCHATPhotoPickerViewController : UINavigationController -> JCHATPhotoPickerViewController
@interface JCHATPhotoSelectViewController : YBBaseViewController<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout> -> JCHATPhotoSelectViewController
@interface JCHATAlbumModel : NSObject -> JCHATAlbumModel
@interface JCHATPhotoModel : NSObject -> JCHATPhotoModel
@interface JCHATSelectImgCollectionView : UICollectionView -> JCHATSelectImgCollectionView
@interface ThumbImageCollectionViewCell : UICollectionViewCell -> XGGThumbImageCollectionViewCell
@interface JCHATAlbumTableViewCell : UITableViewCell -> JCHATAlbumTableViewCell
@interface JCHATPhotoBrowserCollectionViewCell : UICollectionViewCell -> JCHATPhotoBrowserCollectionViewCell
@interface JCHATAlertViewWait : NSObject -> JCHATAlertViewWait
@interface JCHATCustomFormatter : NSObject <DDLogFormatter> -> JCHATCustomFormatter
@interface PubH5 : YBBaseViewController -> XGGPubH5
@interface addHotVideoVC : YBBaseViewController -> XGGaddHotVideoVC
@interface UpHotCell : UITableViewCell -> XGGUpHotCell
@interface HotVideoDetailVC : YBBaseViewController -> XGGHotVideoDetailVC
@interface Loginbonus : UIView -> XGGLoginbonus
@interface LogFirstCell : UICollectionViewCell -> XGGLogFirstCell
@interface LogFirstCell2 : UICollectionViewCell -> XGGLogFirstCell2
@interface HMSegmentedControl : UIControl -> HMSegmentedControl
@interface searchVC : YBBaseViewController -> XGGsearchVC
@interface SearchHistoryCell : UITableViewCell -> XGGSearchHistoryCell
@interface YBSearchBarView : UIView -> XGGYBSearchBarView
@interface HXSearchBar : UISearchBar -> HXSearchBar
@interface StorageConfig : NSObject -> XGGStorageConfig
@interface YBStorageObj : NSObject -> XGGYBStorageObj
@interface detailmodel : NSObject -> XGGdetailmodel
@interface commentModel : NSObject -> XGGcommentModel
@interface commDetailCell : UITableViewCell -> XGGcommDetailCell
@interface commentview : UIView -> XGGcommentview
@interface YBCommentToolBar : UIView -> XGGYBCommentToolBar
@interface commCell : UITableViewCell<UITableViewDelegate,UITableViewDataSource> -> XGGcommCell
@interface YBYoungModeVC : YBBaseViewController -> XGGYBYoungModeVC
@interface YBYoungSetVC : YBBaseViewController -> XGGYBYoungSetVC
@interface RKCodeView : UIView -> XGGRKCodeView
@interface RKCodeInputView : UIView -> XGGRKCodeInputView
@interface YBYoungModifyVC : YBBaseViewController -> XGGYBYoungModifyVC
@interface YBYoungSmall : UIView -> XGGYBYoungSmall
@interface YBYoungManager : NSObject -> XGGYBYoungManager
@interface BusinessCardVC : YBBaseViewController -> XGGBusinessCardVC
@interface RKKeepAlive : NSObject -> XGGRKKeepAlive
@interface MyAdvertCell : UICollectionViewCell -> XGGMyAdvertCell
@interface AdvertManagerVC : YBBaseViewController -> XGGAdvertManagerVC
@interface MyAdvertVC : YBBaseViewController -> XGGMyAdvertVC
@interface lookVGoodsDView : UIView -> XGGlookVGoodsDView
@interface YBInvitationView : UIView -> XGGYBInvitationView
@interface YBInviteCode : NSObject -> XGGYBInviteCode
@interface YBVideoReportVC : YBBaseViewController -> XGGYBVideoReportVC
@interface YBReportCell : UITableViewCell -> XGGYBReportCell
@interface YBLiveReportVC : YBBaseViewController -> XGGYBLiveReportVC
@interface YBLanguageTools : NSObject -> XGGYBLanguageTools
@interface YBTakeSameVideoVC : YBBaseViewController -> XGGYBTakeSameVideoVC
@interface YBDestroySureVC : YBBaseViewController -> XGGYBDestroySureVC
@interface YBDestroyCell : UITableViewCell -> XGGYBDestroyCell
@interface YBDestroyAccount : YBBaseViewController -> XGGYBDestroyAccount
@interface YBMsgC2CListVC : YBBaseViewController -> XGGYBMsgC2CListVC
@interface GDYTranslateTool : NSObject -> GDYTranslateTool
@interface YBMessageManager : NSObject -> XGGYBMessageManager
@interface GDYLimitAlert : UIView -> GDYLimitAlert
@interface TUnReadView : UIView -> XGGTUnReadView
@interface TResponderTextView : UITextView -> XGGTResponderTextView
@interface THelper : NSObject -> XGGTHelper
@interface TMessageController : UITableViewController -> XGGTMessageController
@interface TGoodsCellData : TMessageCellData -> XGGTGoodsCell
@interface TSystemMessageCellData : TMessageCellData -> XGGTSystemMessageCell
@interface TVideoItem : NSObject -> XGGTVideoMessageCell
@interface TCallCellData : TMessageCellData -> TCallCell
@interface TMenuView : UIView -> XGGTMenuView
@interface TGiftMessageCellData : TMessageCellData -> XGGTGiftMessageCell
@interface TLocationCellData : TMessageCellData -> XGGTLocationCell
@interface TRecordView : UIView -> XGGTRecordView
@interface TImageItem : NSObject -> XGGTImageMessageCell
@interface TFaceMessageCellData : TMessageCellData -> XGGTFaceMessageCell
@interface TTextView : UIView -> XGGTTextView
@interface TMenuCellData : NSObject -> XGGTMenuCell
@interface TFaceGroup : NSObject -> XGGTFaceView
@interface TMessageCellData : NSObject -> XGGTMessageCell
@interface TFaceCellData : NSObject -> XGGTFaceCell
@interface TChatAlertView : UIView -> TChatAlertView
@interface TVoiceMessageCellData : TMessageCellData -> XGGTVoiceMessageCell
@interface TTextMessageCellData : TMessageCellData -> XGGTTextMessageCell
@interface TMoreCellData : NSObject -> XGGTMoreCell
@interface TMoreView : UIView -> XGGTMoreView
@interface TFileMessageCellData: TMessageCellData -> XGGTFileMessageCell
@interface TInputController : UIViewController -> XGGTInputController
@interface TChatC2CController : YBBaseViewController -> TChatC2CController
@interface YBImManager : NSObject -> XGGYBImManager
@interface TUIKitConfig : NSObject -> TUIKitConfig
@interface EMVoiceConverter : NSObject -> XGGEMVoiceConverter
@interface YBScrollImageView : UIView -> XGGYBScrollImageView
@interface TUIKit : NSObject -> TUIKit
@interface TConversationCellData : NSObject -> TConversationCell
@interface YBPageControl : UIPageControl -> XGGYBPageControl
@interface RKPaintedGiftView : UIView -> XGGRKPaintedGiftView
@interface RKShowPaintedView : UIView -> XGGRKShowPaintedView
@interface expensiveGiftV : UIView -> XGGexpensiveGiftV
@interface continueGift : UIView -> XGGcontinueGift
@interface liansongBackView : UIView -> XGGliansongBackView
@interface exoensiveGifGiftV : UIView -> XGGexoensiveGifGiftV
@interface CFGradientLabel : UILabel -> CFGradientLabel
@interface YBGiftView : UIView -> XGGYBGiftView
@interface YBGiftModel : NSObject -> XGGYBGiftModel
@interface YBGiftCell : UICollectionViewCell -> XGGYBGiftCell
@interface TYTabPagerBarCell : UICollectionViewCell<TYTabPagerBarCellProtocol> -> TYTabPagerBarCell
@interface TYTabPagerBar : UIView -> TYTabPagerBar
@interface NSObject (TY_PagerReuseIdentify) -> TYPagerViewLayout
@interface TYPagerView : UIView -> TYPagerView
@interface TYTabPagerBarLayout : NSObject -> TYTabPagerBarLayout
@interface YBGiftPage : UIView -> XGGYBGiftPage
@interface topicVideoCell : UICollectionViewCell -> XGGtopicVideoCell
@interface topicDetailsVC : YBBaseViewController -> XGGtopicDetailsVC
@interface shouhuView : UIView<UIAlertViewDelegate> -> XGGshouhuView
@interface guardShowView : UIView<UITableViewDelegate,UITableViewDataSource> -> XGGguardShowView
@interface guardListModel : NSObject -> XGGguardListModel
@interface grardButton : UIButton -> XGGgrardButton
@interface guardListCell : UITableViewCell -> XGGguardListCell
@interface guardAlertView : UIView -> XGGguardAlertView
@interface YBPkProgressView : UIView -> XGGYBPkProgressView
@interface YBAnchorPKView : UIView -> XGGYBAnchorPKView
@interface YBAnchorPKAlert : UIView -> XGGYBAnchorPKAlert
@interface YBAnchorLinkInfo : UIView -> XGGYBAnchorLinkInfo
@interface YBLinkAlertView : UIView -> XGGYBLinkAlertView
@interface YBAnchorOnlineCell : UITableViewCell -> anchorCell
@interface YBAnchorOnlineCell : UITableViewCell -> XGGYBAnchorOnlineCell
@interface YBAnchorOnline : UIView -> XGGYBAnchorOnline
@interface YBTxLinkMicView : UIView -> XGGYBTxLinkMicView
@interface CSActionSheet : UIView { -> CSActionSheet
@interface CSActionPicker : UIView { -> CSActionPicker
@interface YBUserListModel : NSObject -> XGGYBUserListModel
@interface YBUserListCell : UICollectionViewCell -> XGGYBUserListCell
@interface YBUserListView : UIView -> XGGYBUserListView
@interface adminCell : UITableViewCell -> XGGadminCell
@interface adminLists : YBBaseViewController -> XGGadminLists
@interface YBDayTaskManager : NSObject -> XGGYBDayTaskManager
@interface YBDayTaskVC : YBBaseViewController -> XGGYBDayTaskVC
@interface YBDayTaskView : UIView -> XGGYBDayTaskView
@interface YBDayTaskCell : UITableViewCell -> XGGYBDayTaskCell
@interface YBGoodsBriefView : UIView -> XGGYBGoodsBriefView
@interface YBLiveChatView : UIView -> XGGYBLiveChatView
@interface YBLiveChatModel : NSObject -> XGGYBLiveChatModel
@interface YBLiveChatCell : UITableViewCell -> XGGYBLiveChatCell
@interface YBOnSaleCell : UITableViewCell -> XGGYBOnSaleCell
@interface YBOnSaleView : UIView -> XGGYBOnSaleView
@interface userLevelView : UIView -> XGGuserLevelView
@interface UserBulletWindow : UIView -> XGGUserBulletWindow
@interface turntableResultView : UIView -> XGGturntableResultView
@interface turntableResultCell : UICollectionViewCell -> XGGturntableResultCell
@interface turntableRecordCell : UITableViewCell -> XGGturntableRecordCell
@interface turntableView : UIView<CAAnimationDelegate> -> XGGturntableView
@interface turntableRuleView : UIView -> XGGturntableRuleView
@interface turntableRecordView : UIView<UITableViewDelegate,UITableViewDataSource> -> XGGturntableRecordView
@interface YBUserEnterAnimation : UIView -> XGGYBUserEnterAnimation
@interface huanxinsixinview : UIViewController <UITableViewDataSource,UITableViewDelegate> { -> XGGhuanxinsixinview
@interface YBImRoomSmallView : UIView -> XGGYBImRoomSmallView
@interface LiveRankVC : YBBaseViewController -> XGGLiveRankVC
@interface LiveRankCell : UITableViewCell -> XGGLiveRankCell
@interface DetailInputTableViewCell : UITableViewCell -> XGGDetailInputTableViewCell
@interface DetailTableViewCell : UITableViewCell -> XGGDetailTableViewCell
@interface BDVRSettings : NSObject -> BDVRSettings
@interface SettingsTableViewCell : UITableViewCell -> XGGSettingsTableViewCell
@interface BDVRSettingsItem : NSObject -> BDVRSettingsItem
@interface SettingsTableViewController : UITableViewController -> XGGSettingsTableViewController
@interface DetailTableViewController : UITableViewController -> XGGDetailTableViewController
@interface ASRView : UIView -> XGGASRView
@interface AudioInputStream : NSInputStream -> XGGAudioInputStream
@interface BDSEventManager : NSObject -> BDSEventManager
@interface BDRecognizerViewParamsObject : NSObject -> BDRecognizerViewParamsObject
@interface BDRecognizerViewController : UIViewController<BDSClientASRDelegate, BDRecognizerDialogDelegate, AVAudioPlayerDelegate> -> BDRecognizerViewController
@interface BDTheme : NSObject <NSCoding> -> BDTheme
@interface PublishShareV : UIView -> XGGPublishShareV
@interface YBShareView : UIView -> XGGYBShareView
@interface YBShareViewCell : UICollectionViewCell -> XGGYBShareViewCell
@interface NearbyVC : YBBaseViewController -> XGGNearbyVC
@interface YBCitySelVC : YBBaseViewController -> XGGYBCitySelVC
@interface YBCitySelCell : UITableViewCell -> XGGYBCitySelCell
@interface NearbyCell : UICollectionViewCell -> XGGNearbyCell
@interface commodityRecordsVC : YBBaseViewController -> XGGcommodityRecordsVC
@interface commodityRecordsCell : UITableViewCell -> XGGcommodityRecordsCell
@interface YBRedProfitVC : YBBaseViewController -> XGGYBRedProfitVC
@interface YBGetTypeListVC : YBBaseViewController -> profitTypeVC
@interface YBAddTypeView : UIView -> addTypeView
@interface YBGetTypeListVC : YBBaseViewController -> XGGYBGetTypeListVC
@interface YBGetProVC : YBBaseViewController -> XGGYBGetProVC
@interface YBGetTypeListCell : UITableViewCell -> profitTypeCell
@interface YBAddTypeView : UIView -> XGGYBAddTypeView
@interface YBGetTypeListCell : UITableViewCell -> XGGYBGetTypeListCell
@interface WLCardNoFormatter : NSObject -> WLCardNoFormatter
@interface YBGoodsLikeVC : YBBaseViewController -> XGGYBGoodsLikeVC
@interface YBGoodsLikeCell : UITableViewCell -> XGGYBGoodsLikeCell
@interface YBApplyStoreVC : YBBaseViewController -> XGGYBApplyStoreVC
@interface YBApplyConditionVC : YBBaseViewController -> XGGYBApplyConditionVC
@interface YBApplyConditionCell : UITableViewCell -> XGGYBApplyConditionCell
@interface RoomUserListViewController : YBBaseViewController -> XGGRoomUserListViewController
@interface RoomManagementVC : YBBaseViewController -> XGGRoomManagementVC
@interface RoomUserTypeCell : UITableViewCell -> XGGRoomUserTypeCell
@interface OtherRoomViewController : YBBaseViewController -> XGGOtherRoomViewController
@interface WatchRecordListCell : UITableViewCell -> XGGWatchRecordListCell
@interface watchingRecordsVC : YBBaseViewController -> XGGwatchingRecordsVC
@interface accountDetails : YBBaseViewController -> XGGaccountDetails
@interface YBGoodsInfoVC : YBBaseViewController -> XGGYBGoodsInfoVC
@interface YBGoodsListVC : YBBaseViewController -> XGGYBGoodsListVC
@interface YBGoodsListCell : UICollectionViewCell -> XGGYBGoodsListCell
@interface YBCenterMoreView : UIView -> XGGYBCenterMoreView
@interface YBCenterMoreCell : UITableViewCell -> XGGYBCenterMoreCell
@interface orderVideoCell : UITableViewCell -> XGGorderVideoCell
@interface depositAccountVC : YBBaseViewController -> XGGdepositAccountVC
@interface YBOtherCenterMore : UIView -> XGGYBOtherCenterMore
@interface SetViewControllor : YBBaseViewController -> XGGSetViewControllor
@interface SetLogoutCell : UITableViewCell -> XGGSetLogoutCell
@interface SetCell : UITableViewCell -> XGGSetCell
@interface YBUserAuthVC : YBBaseViewController -> XGGYBUserAuthVC
@interface SetViewControllor : UIViewController -> setView
@interface YBPrivateVC : YBBaseViewController -> XGGYBPrivateVC
@interface SetLogoutCell : UITableViewCell -> userItemCell5
@interface HeaderBackImgView : UIView -> XGGHeaderBackImgView
@interface YBCenterTopView : UIView -> XGGYBCenterTopView
@interface CenterListCell : UICollectionViewCell -> XGGCenterListCell
@interface CenterListVC : YBBaseViewController -> XGGCenterListVC
@interface YBCenterVC : YBBaseViewController -> XGGYBCenterVC
@interface EditHeader : UIView -> XGGEditHeader
@interface EditVC : YBBaseViewController -> XGGEditVC
@interface EditCell : UITableViewCell -> XGGEditCell
@interface Utils : NSObject -> XGGUtils
@interface mylabels : UILabel -> XGGmylabels
@interface RKLampView : UIView -> XGGRKLampView
@interface RKHorPickerView : UIView -> XGGRKHorPickerView
@interface YBUploadProgress : UIView -> XGGYBUploadProgress
@interface RKCircularProgress : UIView -> XGGRKCircularProgress
@interface ZZCircleProgress : UIView -> ZZCircleProgress
@interface ZZCountingLabel : UILabel -> ZZCountingLabel
@interface YBButton : UIButton -> XGGYBButton
@interface YBAlertActionSheet : UIView -> XGGYBAlertActionSheet
@interface CCAnimationBtn : UIButton -> CCAnimationBtn
@interface MyTextView : UITextView -> XGGMyTextView
@interface XLCircleProgress : UIView -> XLCircleProgress
@interface XLCircle : UIView -> XLCircle
@interface ZFDetectScrollViewEndGestureRecognizer : UIPanGestureRecognizer -> ZFModalTransitionAnimator
@interface YBSegControl : UISegmentedControl -> XGGYBSegControl
@interface MyTextField : UITextField -> XGGMyTextField
@interface V8LabelNode : NSObject -> V8HorizontalPickerView
@interface SBJsonStreamParserState : NSObject -> SBJsonStreamParserState
@interface SBJsonUTF8Stream : NSObject { -> SBJsonUTF8Stream
@interface SBJsonStreamWriterAccumulator : NSObject <SBJsonStreamWriterDelegate> -> SBJsonStreamWriterAccumulator
@interface SBJsonStreamParserAdapter : NSObject <SBJsonStreamParserDelegate> { -> SBJsonStreamParserAdapter
@interface NSObject (SBJSONValue) -> SBJsonStreamWriter
@interface SBJsonWriter : NSObject -> SBJsonWriter
@interface SBJsonStreamWriterState : NSObject -> SBJsonStreamWriterState
@interface SBJsonTokeniser : NSObject  -> SBJsonTokeniser
@interface SBJsonParser : NSObject -> SBJsonParser
@interface SBJsonStreamParser : NSObject { -> SBJsonStreamParser
@interface SBJsonStreamParserAccumulator : NSObject <SBJsonStreamParserAdapterDelegate> -> SBJsonStreamParserAccumulator
@implementation TCVideoEditViewController { -> TCVideoEditViewController
@implementation TCMusicMixView -> TCMusicMixView
@implementation VideoColorInfo -> XGGVideoColorInfo
@implementation TCRangeContentConfig -> TCRangeContent
@implementation TCVideoRangeSlider -> TCVideoRangeSlider
@implementation TCVideoTextFiled -> TCVideoTextFiled
@implementation TCVideoCutView -> TCVideoCutView
@implementation TCTextCollectionCell -> TCTextCollectionCell
@implementation TCTextAddView -> TCTextAddView
@implementation TCVideoPreview -> TCVideoPreview
@implementation TCBottomTabBar -> TCBottomTabBar
@implementation TCFilterSettingView -> TCFilterSettingView
@implementation EffectSelectView -> XGGEffectSelectView
@implementation TCMusicInfo -> TCMusicCollectionCell
@implementation TimeSelectView -> XGGTimeSelectView
@implementation AlbumVideoVC -> XGGAlbumVideoVC
@implementation AlbumVideoCell -> XGGAlbumVideoCell
@implementation SpeedView{ -> XGGSpeedView
@implementation SpeedView{ -> SpeedView.temp_caseinsensitive_rename
@implementation TXBaseBeautyView -> TXBaseBeautyView
@implementation VideoRecordProcessView -> XGGVideoRecordProcessView
@implementation TCVideoRecordViewController -> TCVideoRecordViewController
@implementation YBPicTransitionVC { -> XGGYBPicTransitionVC
@implementation PhotoTransitionToolbar -> XGGPhotoTransitionToolbar
@implementation VerticalButton -> XGGVerticalButton
@implementation SmallButton -> XGGSmallButton
@implementation TCVideoPublishController { -> TCVideoPublishController
@implementation YBPublishCoverVC -> XGGYBPublishCoverVC
@implementation YBSetChargeView -> XGGYBSetChargeView
@implementation YBVideoAddGoodsVC -> XGGYBVideoAddGoodsVC
@implementation videoTopicVC -> XGGvideoTopicVC
@implementation videoTopicCell -> XGGvideoTopicCell
@implementation YBVideoClassVC -> XGGYBVideoClassVC
@implementation YBVideoMusicView -> XGGYBVideoMusicView
@implementation MusicClassVC -> XGGMusicClassVC
@implementation MusicModel -> XGGMusicModel
@implementation MusicHeaderView -> XGGMusicHeaderView
@implementation MusicHeaderCell -> XGGMusicHeaderCell
@implementation MusicCell -> XGGMusicCell
@implementation AppDelegate -> XGGAppDelegate
@implementation YBBaseViewController -> XGGYBBaseViewController
@implementation YBNavigationController -> XGGYBNavigationController
@implementation YBBaseAppDelegate -> TCBaseAppDelegate
@implementation YBBaseAppDelegate -> XGGYBBaseAppDelegate
@implementation YBHomeRedObj -> XGGYBHomeRedObj
@implementation YBGetVideoObj -> XGGYBGetVideoObj
@implementation YBHomeViewController -> XGGYBHomeViewController
@implementation YBLookVideoVC -> XGGYBLookVideoVC
@implementation YBVideoControlView -> XGGYBVideoControlView
@implementation YBLookVideoCell -> XGGYBLookVideoCell
@implementation ZFCustomControlView -> ZFCustomControlView
@implementation YBVideosVC -> XGGYBVideosVC
@implementation MyFollowViewController -> XGGMyFollowViewController
@implementation myVideoV -> XGGmyVideoV
@implementation NearbyVideoModel -> XGGNearbyVideoModel
@implementation VideoCollectionCell -> XGGVideoCollectionCell
@implementation RegAlertView -> XGGRegAlertView
@implementation CountryCodeVC -> XGGCountryCodeVC
@implementation DspLoginVC -> XGGDspLoginVC
@implementation XGGNetworkManager -> XGGNetworkManager
@implementation XGGNetworkUtils -> XGGNetworkUtils
@implementation iOSNetworking -> XGGiOSNetworking
@implementation YBShowBigImageView -> XGGYBShowBigImageView
@implementation YBAlertView -> XGGYBAlertView
@implementation RKUUIDManager -> XGGRKUUIDManager
@implementation YBImageView{ -> XGGYBImageView
@implementation PublicView -> XGGPublicView
@implementation RKSheetBtn -> XGGRKActionSheet
@implementation YBProgressObj -> XGGYBProgressObj
@implementation BGSetting -> XGGBGSetting
@implementation PublicObj -> XGGPublicObj
@implementation YBNetworking -> XGGYBNetworking
@implementation RKSysAccess -> XGGRKSysAccess
@implementation GuideViewController -> XGGGuideViewController
@implementation ApplyRefundVC -> XGGApplyRefundVC
@implementation SelectClassVC -> XGGSelectClassVC
@implementation CommodityClassModel -> XGGCommodityClassModel
@implementation CommodityClassCell -> XGGCommodityClassCell
@implementation ConfirmOrderVC -> XGGConfirmOrderVC
@implementation LookHistoryVC -> XGGLookHistoryVC
@implementation LookHistoryModel -> XGGLookHistoryModel
@implementation LookHistoryCell -> XGGLookHistoryCell
@implementation HistoryListModel -> XGGHistoryListModel
@implementation OutsideHeadCell -> XGGOutsideHeadCell
@implementation OutsideGoodsDetailVC -> XGGOutsideGoodsDetailVC
@implementation ShareFriendCell -> XGGShareFriendCell
@implementation ShareFriendVC -> XGGShareFriendVC
@implementation FriendModel -> XGGFriendModel
@implementation ShareGoodView -> XGGShareGoodView
@implementation ShareGoodsAlert -> XGGShareGoodsAlert
@implementation PlatformInterventionVC -> XGGPlatformInterventionVC
@implementation CWStarRateView -> CWStarRateView
@implementation PublishEvaluateVC -> XGGPublishEvaluateVC
@implementation ClassificationVC -> XGGClassificationVC
@implementation ClassToExamineVC -> XGGClassToExamineVC
@implementation SelectStandardsView -> XGGSelectStandardsView
@implementation StandardsCell -> XGGStandardsCell
@implementation CommodityDetailModel -> XGGCommodityDetailModel
@implementation GuaranteeView -> XGGGuaranteeView
@implementation YBGoodPlayerCtrView -> XGGYBGoodPlayerCtrView
@implementation GoodsDetailVC -> XGGGoodsDetailVC
@implementation CommodityEvaluationCell -> XGGCommodityEvaluationCell
@implementation CommodityCell1 -> XGGCommodityCell1
@implementation CommodityCell2Row2 -> XGGCommodityCell2Row2
@implementation CommodityCell3 -> XGGCommodityCell3
@implementation CommodityCell2Row1 -> XGGCommodityCell2Row1
@implementation sliderCollectionVCell -> XGGsliderCollectionVCell
@implementation StoreInfoView -> XGGStoreInfoView
@implementation sliderCollectionView{ -> XGGsliderCollectionView
@implementation GoodsExplainCell -> XGGGoodsExplainCell
@implementation ShowDetailVC -> XGGShowDetailVC
@implementation CommodityDetailVC -> XGGCommodityDetailVC
@implementation PayOrderView -> XGGPayOrderView
@implementation AppendEvaluateVC -> XGGAppendEvaluateVC
@implementation ApplyShopVC -> XGGApplyShopVC
@implementation ShopApplyStatusVC -> XGGShopApplyStatusVC
@implementation BondViewController -> XGGBondViewController
@implementation AddressCell -> XGGAddressCell
@implementation RejectAddressModel -> XGGRejectAddressModel
@implementation AddressVC -> XGGAddressVC
@implementation EditAdressVC -> XGGEditAdressVC
@implementation AddressModel -> XGGAddressModel
@implementation EvaluationListCell -> XGGEvaluationListCell
@implementation EvaluationListModel -> XGGEvaluationListModel
@implementation GoodsEvaluationListVC -> XGGGoodsEvaluationListVC
@implementation BuyerGetMoneyVC -> XGGBuyerGetMoneyVC
@implementation BuyerRefundDetailVC -> XGGBuyerRefundDetailVC
@implementation BuyerRefundModel -> XGGBuyerRefundModel
@implementation BuyerRefundHeadView -> XGGBuyerRefundHeadView
@implementation MJPropertyType -> MJPropertyType
@implementation MJProperty -> MJProperty
@implementation MJPropertyKey -> MJPropertyKey
@implementation MJFoundation -> MJFoundation
@implementation OrderModel -> XGGOrderModel
@implementation OrderListVC -> XGGOrderListVC
@implementation OrderDetailModel -> XGGOrderDetailModel
@implementation OrderDetailVC -> XGGOrderDetailVC
@implementation OrderInfoView -> XGGOrderInfoView
@implementation OrderPriceView -> XGGOrderPriceView
@implementation OrderPublicView -> XGGOrderPublicView
@implementation OrderHeaderView -> XGGOrderHeaderView
@implementation OrderListCell -> XGGOrderListCell
@implementation AccountBalanceVC -> XGGAccountBalanceVC
@implementation SellerView -> XGGSellerView
@implementation ShopHomeVC -> XGGShopHomeVC
@implementation BuyerView -> XGGBuyerView
@implementation GetMoneyVC -> XGGGetMoneyVC
@implementation CommodityManagementVC -> XGGCommodityManagementVC
@implementation CommodityModel -> XGGCommodityModel
@implementation CommodityCell -> XGGCommodityCell
@implementation OtherSellOrderDetailVC -> XGGOtherSellOrderDetailVC
@implementation SellOrderDetailModel -> XGGSellOrderDetailModel
@implementation EditSaveAddressVC -> XGGEditSaveAddressVC
@implementation SellOrderCell -> XGGSellOrderCell
@implementation SellOrderModel -> XGGSellOrderModel
@implementation SellerOrderManagementVC -> XGGSellerOrderManagementVC
@implementation RefuseRefundVC -> XGGRefuseRefundVC
@implementation RefundDetailVC -> XGGRefundDetailVC
@implementation RefundDetailModel -> XGGRefundDetailModel
@implementation PlatformListCell -> XGGPlatformListCell
@implementation PlatformGoodsVC -> XGGPlatformGoodsVC
@implementation QualificationsVC -> XGGQualificationsVC
@implementation StockView -> XGGStockView
@implementation EditStockVC -> XGGEditStockVC
@implementation BillCell -> XGGBillCell
@implementation BillManageVC -> XGGBillManageVC
@implementation SelCommodityClassVC -> XGGSelCommodityClassVC
@implementation AddCommodityVC -> XGGAddCommodityVC
@implementation CommodityDetailView -> XGGCommodityDetailView
@implementation StandardsView -> XGGStandardsView
@implementation CommodityTitleView -> XGGCommodityTitleView
@implementation RelationVideoGoodsVC -> XGGRelationVideoGoodsVC
@implementation shopCell -> XGGshopCell
@implementation RelationGoodsVC -> XGGRelationGoodsVC
@implementation PlatformCell -> XGGPlatformCell
@implementation GoodsDetailViewController -> XGGGoodsDetailViewController
@implementation shopDetailVC -> XGGshopDetailVC
@implementation RelationGoodsModel -> XGGRelationGoodsModel
@implementation goodsShowCell -> XGGgoodsShowCell
@implementation AddGoodsVC -> XGGAddGoodsVC
@implementation WaitSendGoodsVC -> XGGWaitSendGoodsVC
@implementation SendGoodsInfo -> XGGSendGoodsInfo
@implementation LogisticsCell -> XGGLogisticsCell
@implementation ShopInfoVC -> XGGShopInfoVC
@implementation SellOrderPublicView -> XGGSellOrderPublicView
@implementation RefundHeadView -> XGGRefundHeadView
@implementation AddOtherSaleGoodsVC -> XGGAddOtherSaleGoodsVC
@implementation XGGPower -> XGGPower
@implementation XGGcityDefault -> XGGcityDefault
@implementation RKLBSManager -> XGGRKLBSManager
@implementation common -> XGGcommon
@implementation Config -> XGGConfig
@implementation YBTabBar -> XGGYBTabBar
@implementation YBLiveOrVideo -> XGGYBLiveOrVideo
@implementation YBTabBarController -> XGGYBTabBarController
@implementation MHMeiyanMenusView -> MHMeiyanMenusView
@implementation MHFilterModel -> MHFilterModel
@implementation MHBeautiesModel -> MHBeautiesModel
@implementation MHPrintView -> MHPrintView
@implementation MHBeautyMenuCell -> MHBeautyMenuCell
@implementation MHMakeUpView -> MHMakeUpView
@implementation MHBeautyView -> MHBeautyView
@implementation MHBottomView -> MHBottomView
@implementation MHSpecificEffectView -> MHSpecificEffectView
@implementation MHMagnifiedView -> MHMagnifiedView
@implementation MHSectionStickersView -> MHSectionStickersView
@implementation MHStickerIndicatorView -> MHStickerCell
@implementation MHBeautyAssembleView -> MHBeautyAssembleView
@implementation MHActionView -> MHActionView
@implementation MHBeautySlider -> MHBeautySlider
@implementation MHCompleteBeautyView -> MHCompleteBeautyView
@implementation MHFiltersView -> MHFiltersView
@implementation MHStickersView -> MHStickersView
@implementation MHSpecificAssembleView -> MHSpecificAssembleView
@implementation MHBeautyFaceView -> MHBeautyFaceView
@implementation WNSegmentItem -> WNSegmentItem
@implementation WNSegmentControl -> WNSegmentControl
@implementation sproutCommon -> XGGsproutCommon
@implementation chatmessageCell -> XGGchatmessageCell
@implementation OrderMessageVC -> XGGOrderMessageVC
@implementation MsgTopPubVC -> XGGMsgTopPubVC
@implementation MsgSysVC -> XGGMsgSysVC
@implementation OrderMessageModel -> XGGOrderMessageModel
@implementation MessageFansVC -> XGGMessageFansVC
@implementation SelPeopleCell -> XGGSelPeopleCell
@implementation SelPeopleV -> XGGSelPeopleV
@implementation MsgTopPubModel -> XGGMsgTopPubModel
@implementation MsgSysModel -> XGGMsgSysModel
@implementation MessageListModel -> XGGMessageListModel
@implementation MessageFansModel -> XGGMessageFansModel
@implementation MessageListCell -> XGGMessageListCell
@implementation MessageHeaderV -> XGGMessageHeaderV
@implementation MsgTopPubCell -> XGGMsgTopPubCell
@implementation MessageFansCell -> XGGMessageFansCell
@implementation MessageListCell -> MessageCell
@implementation MsgSysCell -> XGGMsgSysCell
@implementation MessageListVC -> MessageVC
@implementation YBPlayCtrlView -> XGGYBPlayCtrlView
@implementation YBPlayVC -> XGGYBPlayVC
@implementation YBCheckLiveObj -> XGGYBCheckLiveObj
@implementation YBLiveListVC -> XGGYBLiveListVC
@implementation YBLiveListCell -> XGGYBLiveListCell
@implementation YBLiveRoomAlertView -> XGGYBLiveRoomAlertView
@implementation YBLiveRTCManager -> XGGYBLiveRTCManager
@implementation YBChatToolBar -> XGGYBChatToolBar
@implementation YBLiveEndView -> XGGYBLiveEndView
@implementation roomShowGoodsView -> XGGroomShowGoodsView
@implementation startLiveClassCell -> XGGstartLiveClassCell
@implementation startLiveClassVC -> XGGstartLiveClassVC
@implementation YBLiveFucView -> XGGYBLiveFucView
@implementation YBLiveCtrlView -> XGGYBLiveCtrlView
@implementation YBLivePreview -> XGGYBLivePreview
@implementation YBLiveVC -> XGGYBLiveVC
@implementation YBSocketLive -> XGGYBSocketLive
@implementation YBSocketPlay -> XGGYBSocketPlay
@implementation YBVipCell -> XGGYBVipCell
@implementation YBVipHeader -> XGGYBVipHeader
@implementation vipBuyView -> XGGvipBuyView
@implementation YBVipVC -> XGGYBVipVC
@implementation YBRechargeType -> XGGYBRechargeType
@implementation Order -> XGGOrder
@implementation MD5DataSigner -> XGGMD5DataSigner
@implementation NSData (NSDataBase64Additions) -> NSDataEx
@implementation RSADataVerifier -> XGGRSADataVerifier
@implementation Base64
 -> base64
@implementation RSADataSigner -> XGGRSADataSigner
@implementation YBRechargeVC -> XGGYBRechargeVC
@implementation fansViewController -> XGGfansViewController
@implementation BlackListCell -> blackListCell
@implementation attrViewController -> XGGattrViewController
@implementation fansModel -> XGGfansModel
@implementation BlackListVC -> XGGBlackListVC
@implementation fans -> XGGfans
@implementation JCHATConversationViewController -> JCHATConversationViewController
@implementation JCHATRawAudioDataPlayer -> JCHATRawAudioDataPlayer
@implementation JCHATChatModel -> JCHATChatModel
@implementation JCHATLoadMessageTableViewCell -> JCHATLoadMessageTableViewCell
@implementation JCHATShowTimeCell -> JCHATShowTimeCell
@implementation JCHATMessageTableViewCell -> JCHATMessageTableViewCell
@implementation JCHATRecordAnimationView -> JCHATRecordAnimationView
@implementation JCHATMessageTableView -> JCHATMessageTableView
@implementation JCHATAudioPlayerHelper -> JCHATAudioPlayerHelper
@implementation JCHATToolBar -> JCHATToolBar
@implementation JCHATMessageTextView -> JCHATMessageTextView
@implementation JCHATMoreView -> JCHATMoreView
@implementation XHVoiceCommonHelper -> XHVoiceCommonHelper
@implementation XHVoiceRecordHelper -> XHVoiceRecordHelper
@implementation XHVoiceRecordHUD -> XHVoiceRecordHUD
@implementation JCHATMessageContentView -> JCHATMessageContentView
@implementation TencentLocationVC -> XGGTencentLocationVC
@implementation SearchResultView -> XGGSearchResultView
@implementation LocationCell -> XGGLocationCell
@implementation ViewUtil -> XGGViewUtil
@implementation JCHATSendMsgManager -> JCHATSendMsgManager
@implementation JCHATStringUtils { -> JCHATStringUtils
@implementation JCHATSendMsgController -> JCHATSendMsgController
@implementation JCHATFileManager -> JCHATFileManager
@implementation JCHATAlertToSendImage -> JCHATAlertToSendImage
@implementation JCHATTimeOutManager -> JCHATTimeOutManager
@implementation emojiCell -> XGGemojiCell
@implementation CollCellWhite -> XGGtwEmojiView
@implementation LWLCollectionViewHorizontalLayout -> LWLCollectionViewHorizontalLayout
@implementation MJPhotoLoadingView -> MJPhotoLoadingView
@implementation MJPhotoView -> MJPhotoView
@implementation MJPhoto -> MJPhoto
@implementation MJPhotoToolbar -> MJPhotoToolbar
@implementation MJPhotoProgressView -> MJPhotoProgressView
@implementation MJPhotoBrowser -> MJPhotoBrowser
@implementation JCHATAlertViewWait -> JCHATAlertViewWait
@implementation ChatImageBubble -> XGGChatImageBubble
@implementation ChatBubbleLayer -> XGGChatBubbleLayer
@implementation JCHATCustomFormatter { -> JCHATCustomFormatter
@implementation JCHATPhotoBrowserViewController -> JCHATPhotoBrowserViewController
@implementation JCHATAlbumViewController -> JCHATAlbumViewController
@implementation JCHATPhotoSelectViewController -> JCHATPhotoSelectViewController
@implementation JCHATPhotoPickerViewController -> JCHATPhotoPickerViewController
@implementation JCHATPhotoModel -> JCHATPhotoModel
@implementation JCHATAlbumModel -> JCHATAlbumModel
@implementation JCHATAlbumTableViewCell -> JCHATAlbumTableViewCell
@implementation JCHATPhotoBrowserCollectionViewCell -> JCHATPhotoBrowserCollectionViewCell
@implementation ThumbImageCollectionViewCell -> XGGThumbImageCollectionViewCell
@implementation JCHATSelectImgCollectionView -> JCHATSelectImgCollectionView
@implementation PubH5 -> XGGPubH5
@implementation UpHotCell -> XGGUpHotCell
@implementation HotVideoDetailVC -> XGGHotVideoDetailVC
@implementation addHotVideoVC -> XGGaddHotVideoVC
@implementation LogFirstCell -> XGGLogFirstCell
@implementation LogFirstCell2 -> XGGLogFirstCell2
@implementation Loginbonus -> XGGLoginbonus
@implementation searchVC -> XGGsearchVC
@implementation HMScrollView -> HMSegmentedControl
@implementation HXSearchBar -> HXSearchBar
@implementation SearchHistoryCell -> XGGSearchHistoryCell
@implementation YBSearchBarView -> XGGYBSearchBarView
@implementation YBStorageObj -> XGGYBStorageObj
@implementation StorageConfig -> XGGStorageConfig
@implementation commDetailCell -> XGGcommDetailCell
@implementation commentview -> XGGcommentview
@implementation commCell{ -> XGGcommCell
@implementation detailmodel -> XGGdetailmodel
@implementation YBCommentToolBar -> XGGYBCommentToolBar
@implementation commentModel -> XGGcommentModel
@implementation YBYoungManager -> XGGYBYoungManager
@implementation YBYoungModifyVC -> XGGYBYoungModifyVC
@implementation RKCodeInputView -> XGGRKCodeInputView
@implementation RKCodeView -> XGGRKCodeView
@implementation YBYoungModeVC -> XGGYBYoungModeVC
@implementation YBYoungSetVC -> XGGYBYoungSetVC
@implementation YBYoungSmall -> XGGYBYoungSmall
@implementation BusinessCardVC -> XGGBusinessCardVC
@implementation RKKeepAlive -> XGGRKKeepAlive
@implementation MyAdvertVC -> XGGMyAdvertVC
@implementation AdvertManagerVC -> XGGAdvertManagerVC
@implementation MyAdvertCell -> XGGMyAdvertCell
@implementation lookVGoodsDView{ -> XGGlookVGoodsDView
@implementation YBInviteCode -> XGGYBInviteCode
@implementation YBInvitationView{ -> XGGYBInvitationView
@implementation YBLiveReportVC -> XGGYBLiveReportVC
@implementation YBVideoReportVC -> XGGYBVideoReportVC
@implementation YBReportCell -> XGGYBReportCell
@implementation YBLanguageTools -> XGGYBLanguageTools
@implementation YBTakeSameVideoVC -> XGGYBTakeSameVideoVC
@implementation YBDestroyAccount -> XGGYBDestroyAccount
@implementation YBDestroySureVC -> XGGYBDestroySureVC
@implementation YBDestroyCell -> XGGYBDestroyCell
@implementation GDYTranslateTool -> GDYTranslateTool
@implementation GDYLimitAlert -> GDYLimitAlert
@implementation THelper -> XGGTHelper
@implementation TResponderTextView -> XGGTResponderTextView
@implementation TUnReadView -> XGGTUnReadView
@implementation TMessageController -> XGGTMessageController
@implementation TInputController -> XGGTInputController
@implementation TFaceGroup -> XGGTFaceView
@implementation TVoiceMessageCellData -> XGGTVoiceMessageCell
@implementation TChatAlertView -> TChatAlertView
@implementation TFaceCellData -> XGGTFaceCell
@implementation TMessageCellData : NSObject -> XGGTMessageCell
@implementation TMoreCellData -> XGGTMoreCell
@implementation TTextMessageCellData -> XGGTTextMessageCell
@implementation TFileMessageCellData -> XGGTFileMessageCell
@implementation TMoreView -> XGGTMoreView
@implementation TSystemMessageCellData -> XGGTSystemMessageCell
@implementation TGoodsCellData -> XGGTGoodsCell
@implementation TCallCellData -> TCallCell
@implementation TVideoItem -> XGGTVideoMessageCell
@implementation TLocationCellData -> XGGTLocationCell
@implementation TGiftMessageCellData -> XGGTGiftMessageCell
@implementation TMenuView -> XGGTMenuView
@implementation TMenuCellData -> XGGTMenuCell
@implementation TTextView -> XGGTTextView
@implementation TRecordView -> XGGTRecordView
@implementation TFaceMessageCellData -> XGGTFaceMessageCell
@implementation TImageItem -> XGGTImageMessageCell
@implementation TChatC2CController -> TChatC2CController
@implementation YBImManager -> XGGYBImManager
@implementation YBScrollImageView -> XGGYBScrollImageView
@implementation TUIKit -> TUIKit
@implementation TUIKitConfig -> TUIKitConfig
@implementation TConversationCellData -> TConversationCell
@implementation YBMsgC2CListVC -> XGGYBMsgC2CListVC
@implementation YBMessageManager -> XGGYBMessageManager
@implementation CollectionCellWhite -> XGGYBGiftView
@implementation YBPageControl -> XGGYBPageControl
@implementation RKShowPaintedView -> XGGRKShowPaintedView
@implementation RKPaintedGiftView -> XGGRKPaintedGiftView
@implementation YBGiftPage -> XGGYBGiftPage
@implementation exoensiveGifGiftV{ -> XGGexoensiveGifGiftV
@implementation expensiveGiftV -> XGGexpensiveGiftV
@implementation RKPopView -> XGGcontinueGift
@implementation liansongBackView -> XGGliansongBackView
@implementation CFGradientLabel -> CFGradientLabel
@implementation YBGiftModel -> XGGYBGiftModel
@implementation YBGiftModel -> GiftModel
@implementation YBGiftCell -> XGGYBGiftCell
@implementation YBGiftCell -> GiftCell
@implementation TYAutoPurgeCache -> TYPagerViewLayout
@implementation TYTabPagerBar -> TYTabPagerBar
@implementation TYTabPagerBarLayout -> TYTabPagerBarLayout
@implementation TYPagerView -> TYPagerView
@implementation TYTabPagerBarCell -> TYTabPagerBarCell
@implementation topicVideoCell -> XGGtopicVideoCell
@implementation topicDetailsVC -> XGGtopicDetailsVC
@implementation guardShowView{ -> XGGguardShowView
@implementation guardListModel -> XGGguardListModel
@implementation guardListCell -> XGGguardListCell
@implementation guardAlertView -> XGGguardAlertView
@implementation grardButton -> XGGgrardButton
@implementation shouhuView{ -> XGGshouhuView
@implementation YBAnchorPKView{ -> XGGYBAnchorPKView
@implementation YBAnchorPKAlert{ -> XGGYBAnchorPKAlert
@implementation YBPkProgressView{ -> XGGYBPkProgressView
@implementation YBAnchorLinkInfo -> XGGYBAnchorLinkInfo
@implementation YBLinkAlertView{ -> XGGYBLinkAlertView
@implementation YBAnchorOnline -> XGGYBAnchorOnline
@implementation YBAnchorOnlineCell -> XGGYBAnchorOnlineCell
@implementation YBTxLinkMicView -> XGGYBTxLinkMicView
@implementation CSActionSheet -> CSActionSheet
@implementation CSActionPicker -> CSActionPicker
@implementation YBUserListView -> XGGYBUserListView
@implementation YBUserListModel -> XGGYBUserListModel
@implementation YBUserListCell -> XGGYBUserListCell
@implementation adminCell -> XGGadminCell
@implementation adminLists -> XGGadminLists
@implementation YBDayTaskVC -> XGGYBDayTaskVC
@implementation YBDayTaskView -> XGGYBDayTaskView
@implementation YBDayTaskCell -> XGGYBDayTaskCell
@implementation YBDayTaskManager -> XGGYBDayTaskManager
@implementation YBGoodsBriefView -> XGGYBGoodsBriefView
@implementation YBLiveChatView -> XGGYBLiveChatView
@implementation YBLiveChatModel -> XGGYBLiveChatModel
@implementation YBLiveChatCell -> XGGYBLiveChatCell
@implementation YBOnSaleView -> XGGYBOnSaleView
@implementation YBOnSaleCell -> XGGYBOnSaleCell
@implementation UserBulletWindow{ -> XGGUserBulletWindow
@implementation userLevelView -> XGGuserLevelView
@implementation turntableView{ -> XGGturntableView
@implementation turntableRecordCell -> XGGturntableRecordCell
@implementation turntableResultCell -> XGGturntableResultCell
@implementation turntableResultView -> XGGturntableResultView
@implementation turntableRuleView{ -> XGGturntableRuleView
@implementation turntableRecordView{ -> XGGturntableRecordView
@implementation YBUserEnterAnimation -> XGGYBUserEnterAnimation
@implementation huanxinsixinview{ -> XGGhuanxinsixinview
@implementation YBImRoomSmallView -> XGGYBImRoomSmallView
@implementation LiveRankCell -> XGGLiveRankCell
@implementation LiveRankVC -> XGGLiveRankVC
@implementation SettingsTableViewController -> XGGSettingsTableViewController
@implementation DetailTableViewController -> XGGDetailTableViewController
@implementation DetailInputTableViewCell -> XGGDetailInputTableViewCell
@implementation SettingsTableViewCell -> XGGSettingsTableViewCell
@implementation BDVRSettings -> BDVRSettings
@implementation DetailTableViewCell -> XGGDetailTableViewCell
@implementation BDVRSettingsItem -> BDVRSettingsItem
@implementation ASRView -> XGGASRView
@implementation PublishShareV -> XGGPublishShareV
@implementation YBShareView -> XGGYBShareView
@implementation YBShareViewCell -> XGGYBShareViewCell
@implementation NearbyVC -> XGGNearbyVC
@implementation YBCitySelCell -> XGGYBCitySelCell
@implementation YBCitySelVC -> XGGYBCitySelVC
@implementation NearbyCell -> XGGNearbyCell
@implementation commodityRecordsCell -> XGGcommodityRecordsCell
@implementation commodityRecordsVC -> XGGcommodityRecordsVC
@implementation YBCenterMoreView -> XGGYBCenterMoreView
@implementation YBRedProfitVC -> XGGYBRedProfitVC
@implementation UITextField (WLRange) -> WLCardNoFormatter
@implementation YBAddTypeView{ -> XGGYBAddTypeView
@implementation YBGetTypeListCell -> XGGYBGetTypeListCell
@implementation YBGetProVC -> XGGYBGetProVC
@implementation YBGetTypeListVC -> XGGYBGetTypeListVC
@implementation YBGoodsLikeCell -> XGGYBGoodsLikeCell
@implementation YBGoodsLikeVC -> XGGYBGoodsLikeVC
@implementation YBApplyStoreVC -> XGGYBApplyStoreVC
@implementation YBApplyConditionVC -> XGGYBApplyConditionVC
@implementation YBApplyConditionCell -> XGGYBApplyConditionCell
@implementation RoomUserTypeCell -> XGGRoomUserTypeCell
@implementation OtherRoomViewController -> XGGOtherRoomViewController
@implementation RoomUserListViewController -> XGGRoomUserListViewController
@implementation RoomManagementVC -> XGGRoomManagementVC
@implementation watchingRecordsVC -> XGGwatchingRecordsVC
@implementation WatchRecordListCell -> XGGWatchRecordListCell
@implementation accountDetails -> XGGaccountDetails
@implementation YBGoodsInfoVC -> XGGYBGoodsInfoVC
@implementation YBGoodsListCell -> XGGYBGoodsListCell
@implementation YBGoodsListVC -> XGGYBGoodsListVC
@implementation YBCenterMoreCell -> XGGYBCenterMoreCell
@implementation depositAccountVC -> XGGdepositAccountVC
@implementation orderVideoCell -> XGGorderVideoCell
@implementation YBOtherCenterMore -> XGGYBOtherCenterMore
@implementation YBCenterVC -> XGGYBCenterVC
@implementation SetCell -> XGGSetCell
@implementation SetLogoutCell -> XGGSetLogoutCell
@implementation YBUserAuthVC -> XGGYBUserAuthVC
@implementation SetViewControllor -> XGGSetViewControllor
@implementation YBPrivateVC -> XGGYBPrivateVC
@implementation YBCenterTopView -> XGGYBCenterTopView
@implementation HeaderBackImgView -> XGGHeaderBackImgView
@implementation CenterListVC -> XGGCenterListVC
@implementation CenterListCell -> XGGCenterListCell
@implementation EditCell -> XGGEditCell
@implementation EditHeader -> XGGEditHeader
@implementation EditVC -> XGGEditVC
@implementation RKHorPickerView { -> XGGRKHorPickerView
@implementation YBAlertActionSheet -> XGGYBAlertActionSheet
@implementation YBButton -> XGGYBButton
@implementation MyTextView -> XGGMyTextView
@implementation MyTextField -> XGGMyTextField
@implementation YBSegControl -> XGGYBSegControl
@implementation RKLampView -> XGGRKLampView
@implementation RKCircularProgress -> XGGRKCircularProgress
@implementation YBUploadProgress -> XGGYBUploadProgress
@implementation ZZCountingLabel -> ZZCountingLabel
@implementation ZZCircleProgress -> ZZCircleProgress
@implementation Utils -> XGGUtils
@implementation CCAnimationBtn -> CCAnimationBtn
@implementation XLCircle -> XLCircle
@implementation XLCircleProgress -> XLCircleProgress
@implementation mylabels -> XGGmylabels
@implementation ZFModalTransitionAnimator -> ZFModalTransitionAnimator
@implementation V8LabelNode -> V8HorizontalPickerView
@implementation SBJsonStreamParser -> SBJsonStreamParser
@implementation SBJsonParser -> SBJsonParser
@implementation SBJsonStreamParserAccumulator -> SBJsonStreamParserAccumulator
@implementation SBJsonUTF8Stream -> SBJsonUTF8Stream
@implementation SBJsonStreamParserState -> SBJsonStreamParserState
@implementation SBJsonWriter -> SBJsonWriter
@implementation SBJsonStreamWriter -> SBJsonStreamWriter
@implementation SBJsonStreamParserAdapter -> SBJsonStreamParserAdapter
@implementation SBJsonStreamWriterAccumulator -> SBJsonStreamWriterAccumulator
@implementation SBJsonStreamWriterState -> SBJsonStreamWriterState
@implementation SBJsonTokeniser -> SBJsonTokeniser
