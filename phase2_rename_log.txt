处理文件: YBVideo/录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
  当前类名: 类名: @implementation TCVideoEditViewController {
  目标类名: 文件名: TCVideoEditViewController
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
  当前类名: 类名: @implementation TCMusicMixView
  目标类名: 文件名: TCMusicMixView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m
  当前类名: 类名: @implementation VideoColorInfo
  目标类名: 文件名: XGGVideoColorInfo
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.m
  当前类名: 类名: @implementation TCRangeContentConfig
  目标类名: 文件名: TCRangeContent
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
  当前类名: 类名: @implementation TCVideoRangeSlider
  目标类名: 文件名: TCVideoRangeSlider
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoTextFiled.m
  当前类名: 类名: @implementation TCVideoTextFiled
  目标类名: 文件名: TCVideoTextFiled
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoCutView.m
  当前类名: 类名: @implementation TCVideoCutView
  目标类名: 文件名: TCVideoCutView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCTextCollectionCell.m
  当前类名: 类名: @implementation TCTextCollectionCell
  目标类名: 文件名: TCTextCollectionCell
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCTextAddView.m
  当前类名: 类名: @implementation TCTextAddView
  目标类名: 文件名: TCTextAddView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoPreview.m
  当前类名: 类名: @implementation TCVideoPreview
  目标类名: 文件名: TCVideoPreview
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCBottomTabBar.m
  当前类名: 类名: @implementation TCBottomTabBar
  目标类名: 文件名: TCBottomTabBar
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCFilterSettingView.m
  当前类名: 类名: @implementation TCFilterSettingView
  目标类名: 文件名: TCFilterSettingView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
  当前类名: 类名: @implementation EffectSelectView
  目标类名: 文件名: XGGEffectSelectView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.m
  当前类名: 类名: @implementation TCMusicInfo
  目标类名: 文件名: TCMusicCollectionCell
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
  当前类名: 类名: @implementation TimeSelectView
  目标类名: 文件名: XGGTimeSelectView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
  当前类名: 类名: @implementation AlbumVideoVC
  目标类名: 文件名: XGGAlbumVideoVC
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.m
  当前类名: 类名: @implementation AlbumVideoCell
  目标类名: 文件名: XGGAlbumVideoCell
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/Record/views/XGGSpeedView.m
  当前类名: 类名: @implementation SpeedView{
  目标类名: 文件名: XGGSpeedView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m
  当前类名: 类名: @implementation SpeedView{
  目标类名: 文件名: SpeedView.temp_caseinsensitive_rename
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/Record/views/TXBaseBeautyView.m
  当前类名: 类名: @implementation TXBaseBeautyView
  目标类名: 文件名: TXBaseBeautyView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m
  当前类名: 类名: @implementation VideoRecordProcessView
  目标类名: 文件名: XGGVideoRecordProcessView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
  当前类名: 类名: @implementation YBPicTransitionVC {
  目标类名: 文件名: XGGYBPicTransitionVC
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
  当前类名: 类名: @implementation PhotoTransitionToolbar
  目标类名: 文件名: XGGPhotoTransitionToolbar
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/图片转场/view/XGGVerticalButton.m
  当前类名: 类名: @implementation VerticalButton
  目标类名: 文件名: XGGVerticalButton
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/图片转场/view/XGGSmallButton.m
  当前类名: 类名: @implementation SmallButton
  目标类名: 文件名: XGGSmallButton
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCPublish/TCVideoPublishController.m
  当前类名: 类名: @implementation TCVideoPublishController {
  目标类名: 文件名: TCVideoPublishController
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m
  当前类名: 类名: @implementation YBPublishCoverVC
  目标类名: 文件名: XGGYBPublishCoverVC
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m
  当前类名: 类名: @implementation YBSetChargeView
  目标类名: 文件名: XGGYBSetChargeView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
  当前类名: 类名: @implementation YBVideoAddGoodsVC
  目标类名: 文件名: XGGYBVideoAddGoodsVC
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
  当前类名: 类名: @implementation videoTopicVC
  目标类名: 文件名: XGGvideoTopicVC
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.m
  当前类名: 类名: @implementation videoTopicCell
  目标类名: 文件名: XGGvideoTopicCell
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
  当前类名: 类名: @implementation YBVideoClassVC
  目标类名: 文件名: XGGYBVideoClassVC
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/音乐/XGGYBVideoMusicView.m
  当前类名: 类名: @implementation YBVideoMusicView
  目标类名: 文件名: XGGYBVideoMusicView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/音乐/XGGMusicClassVC.m
  当前类名: 类名: @implementation MusicClassVC
  目标类名: 文件名: XGGMusicClassVC
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/音乐/model/XGGMusicModel.m
  当前类名: 类名: @implementation MusicModel
  目标类名: 文件名: XGGMusicModel
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
  当前类名: 类名: @implementation MusicHeaderView
  目标类名: 文件名: XGGMusicHeaderView
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m
  当前类名: 类名: @implementation MusicHeaderCell
  目标类名: 文件名: XGGMusicHeaderCell
  ✅ 成功修改

处理文件: YBVideo/录制_编辑_发布/音乐/view/XGGMusicCell.m
  当前类名: 类名: @implementation MusicCell
  目标类名: 文件名: XGGMusicCell
  ✅ 成功修改

处理文件: YBVideo/其他类/XGGAppDelegate.m
  当前类名: 类名: @implementation AppDelegate
  目标类名: 文件名: XGGAppDelegate
  ✅ 成功修改

处理文件: YBVideo/其他类/XGGYBBaseViewController.m
  当前类名: 类名: @implementation YBBaseViewController
  目标类名: 文件名: XGGYBBaseViewController
  ✅ 成功修改

处理文件: YBVideo/其他类/XGGYBNavigationController.m
  当前类名: 类名: @implementation YBNavigationController
  目标类名: 文件名: XGGYBNavigationController
  ✅ 成功修改

处理文件: YBVideo/其他类/TCBaseAppDelegate.m
  当前类名: 类名: @implementation YBBaseAppDelegate
  目标类名: 文件名: TCBaseAppDelegate
  ✅ 成功修改

处理文件: YBVideo/其他类/XGGYBBaseAppDelegate.m
  当前类名: 类名: @implementation YBBaseAppDelegate
  目标类名: 文件名: XGGYBBaseAppDelegate
  ✅ 成功修改

处理文件: YBVideo/首页/获取视频详情公用obj/XGGYBHomeRedObj.m
  当前类名: 类名: @implementation YBHomeRedObj
  目标类名: 文件名: XGGYBHomeRedObj
  ✅ 成功修改

处理文件: YBVideo/首页/获取视频详情公用obj/XGGYBGetVideoObj.m
  当前类名: 类名: @implementation YBGetVideoObj
  目标类名: 文件名: XGGYBGetVideoObj
  ✅ 成功修改

处理文件: YBVideo/首页/XGGYBHomeViewController.m
  当前类名: 类名: @implementation YBHomeViewController
  目标类名: 文件名: XGGYBHomeViewController
  ✅ 成功修改

处理文件: YBVideo/首页/推荐/XGGYBLookVideoVC.m
  当前类名: 类名: @implementation YBLookVideoVC
  目标类名: 文件名: XGGYBLookVideoVC
  ✅ 成功修改

处理文件: YBVideo/首页/推荐/view/XGGYBVideoControlView.m
  当前类名: 类名: @implementation YBVideoControlView
  目标类名: 文件名: XGGYBVideoControlView
  ✅ 成功修改

处理文件: YBVideo/首页/推荐/view/XGGYBLookVideoCell.m
  当前类名: 类名: @implementation YBLookVideoCell
  目标类名: 文件名: XGGYBLookVideoCell
  ✅ 成功修改

处理文件: YBVideo/首页/推荐/view/ZFCustomControlView.m
  当前类名: 类名: @implementation ZFCustomControlView
  目标类名: 文件名: ZFCustomControlView
  ✅ 成功修改

处理文件: YBVideo/首页/热门-关注-分类/XGGYBVideosVC.m
  当前类名: 类名: @implementation YBVideosVC
  目标类名: 文件名: XGGYBVideosVC
  ✅ 成功修改

处理文件: YBVideo/首页/热门-关注-分类/XGGMyFollowViewController.m
  当前类名: 类名: @implementation MyFollowViewController
  目标类名: 文件名: XGGMyFollowViewController
  ✅ 成功修改

处理文件: YBVideo/首页/热门-关注-分类/XGGmyVideoV.m
  当前类名: 类名: @implementation myVideoV
  目标类名: 文件名: XGGmyVideoV
  ✅ 成功修改

处理文件: YBVideo/首页/热门-关注-分类/models/XGGNearbyVideoModel.m
  当前类名: 类名: @implementation NearbyVideoModel
  目标类名: 文件名: XGGNearbyVideoModel
  ✅ 成功修改

处理文件: YBVideo/首页/热门-关注-分类/views/XGGVideoCollectionCell.m
  当前类名: 类名: @implementation VideoCollectionCell
  目标类名: 文件名: XGGVideoCollectionCell
  ✅ 成功修改

处理文件: YBVideo/登录注册/隐私提醒文本/XGGRegAlertView.m
  当前类名: 类名: @implementation RegAlertView
  目标类名: 文件名: XGGRegAlertView
  ✅ 成功修改

处理文件: YBVideo/登录注册/国家代号/XGGCountryCodeVC.m
  当前类名: 类名: @implementation CountryCodeVC
  目标类名: 文件名: XGGCountryCodeVC
  ✅ 成功修改

处理文件: YBVideo/登录注册/XGGDspLoginVC.m
  当前类名: 类名: @implementation DspLoginVC
  目标类名: 文件名: XGGDspLoginVC
  ✅ 成功修改

处理文件: YBVideo/XGGNetwork/XGGNetworkManager.m
  当前类名: 类名: @implementation XGGNetworkManager
  目标类名: 文件名: XGGNetworkManager
  ✅ 成功修改

处理文件: YBVideo/XGGNetwork/XGGNetworkUtils.m
  当前类名: 类名: @implementation XGGNetworkUtils
  目标类名: 文件名: XGGNetworkUtils
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGiOSNetworking.m
  当前类名: 类名: @implementation iOSNetworking
  目标类名: 文件名: XGGiOSNetworking
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGYBShowBigImageView.m
  当前类名: 类名: @implementation YBShowBigImageView
  目标类名: 文件名: XGGYBShowBigImageView
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGYBAlertView.m
  当前类名: 类名: @implementation YBAlertView
  目标类名: 文件名: XGGYBAlertView
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGRKUUIDManager.m
  当前类名: 类名: @implementation RKUUIDManager
  目标类名: 文件名: XGGRKUUIDManager
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGYBImageView.m
  当前类名: 类名: @implementation YBImageView{
  目标类名: 文件名: XGGYBImageView
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGPublicView.m
  当前类名: 类名: @implementation PublicView
  目标类名: 文件名: XGGPublicView
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGRKActionSheet.m
  当前类名: 类名: @implementation RKSheetBtn
  目标类名: 文件名: XGGRKActionSheet
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGYBProgressObj.m
  当前类名: 类名: @implementation YBProgressObj
  目标类名: 文件名: XGGYBProgressObj
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGBGSetting.m
  当前类名: 类名: @implementation BGSetting
  目标类名: 文件名: XGGBGSetting
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGPublicObj.m
  当前类名: 类名: @implementation PublicObj
  目标类名: 文件名: XGGPublicObj
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGYBNetworking.m
  当前类名: 类名: @implementation YBNetworking
  目标类名: 文件名: XGGYBNetworking
  ✅ 成功修改

处理文件: YBVideo/公共方法类/XGGRKSysAccess.m
  当前类名: 类名: @implementation RKSysAccess
  目标类名: 文件名: XGGRKSysAccess
  ✅ 成功修改

处理文件: YBVideo/引导页/XGGGuideViewController.m
  当前类名: 类名: @implementation GuideViewController
  目标类名: 文件名: XGGGuideViewController
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/退款申请/XGGApplyRefundVC.m
  当前类名: 类名: @implementation ApplyRefundVC
  目标类名: 文件名: XGGApplyRefundVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/经营类目选择/XGGSelectClassVC.m
  当前类名: 类名: @implementation SelectClassVC
  目标类名: 文件名: XGGSelectClassVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/经营类目选择/XGGCommodityClassModel.m
  当前类名: 类名: @implementation CommodityClassModel
  目标类名: 文件名: XGGCommodityClassModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/经营类目选择/XGGCommodityClassCell.m
  当前类名: 类名: @implementation CommodityClassCell
  目标类名: 文件名: XGGCommodityClassCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/确认订单/XGGConfirmOrderVC.m
  当前类名: 类名: @implementation ConfirmOrderVC
  目标类名: 文件名: XGGConfirmOrderVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryVC.m
  当前类名: 类名: @implementation LookHistoryVC
  目标类名: 文件名: XGGLookHistoryVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryModel.m
  当前类名: 类名: @implementation LookHistoryModel
  目标类名: 文件名: XGGLookHistoryModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryCell.m
  当前类名: 类名: @implementation LookHistoryCell
  目标类名: 文件名: XGGLookHistoryCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/浏览记录/XGGHistoryListModel.m
  当前类名: 类名: @implementation HistoryListModel
  目标类名: 文件名: XGGHistoryListModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
  当前类名: 类名: @implementation OutsideHeadCell
  目标类名: 文件名: XGGOutsideHeadCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
  当前类名: 类名: @implementation OutsideGoodsDetailVC
  目标类名: 文件名: XGGOutsideGoodsDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
  当前类名: 类名: @implementation ShareFriendCell
  目标类名: 文件名: XGGShareFriendCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
  当前类名: 类名: @implementation ShareFriendVC
  目标类名: 文件名: XGGShareFriendVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/分享商品/分享给好友/XGGFriendModel.m
  当前类名: 类名: @implementation FriendModel
  目标类名: 文件名: XGGFriendModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/分享商品/XGGShareGoodView.m
  当前类名: 类名: @implementation ShareGoodView
  目标类名: 文件名: XGGShareGoodView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/分享商品/XGGShareGoodsAlert.m
  当前类名: 类名: @implementation ShareGoodsAlert
  目标类名: 文件名: XGGShareGoodsAlert
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/平台介入/XGGPlatformInterventionVC.m
  当前类名: 类名: @implementation PlatformInterventionVC
  目标类名: 文件名: XGGPlatformInterventionVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/评价/XGGPublishEvaluateVC.m
  当前类名: 类名: @implementation PublishEvaluateVC
  目标类名: 文件名: XGGPublishEvaluateVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/经营类目设置/XGGClassificationVC.m
  当前类名: 类名: @implementation ClassificationVC
  目标类名: 文件名: XGGClassificationVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/经营类目设置/XGGClassToExamineVC.m
  当前类名: 类名: @implementation ClassToExamineVC
  目标类名: 文件名: XGGClassToExamineVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
  当前类名: 类名: @implementation SelectStandardsView
  目标类名: 文件名: XGGSelectStandardsView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/选择规格/XGGStandardsCell.m
  当前类名: 类名: @implementation StandardsCell
  目标类名: 文件名: XGGStandardsCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGCommodityDetailModel.m
  当前类名: 类名: @implementation CommodityDetailModel
  目标类名: 文件名: XGGCommodityDetailModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/服务保障/XGGGuaranteeView.m
  当前类名: 类名: @implementation GuaranteeView
  目标类名: 文件名: XGGGuaranteeView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m
  当前类名: 类名: @implementation YBGoodPlayerCtrView
  目标类名: 文件名: XGGYBGoodPlayerCtrView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGGoodsDetailVC.m
  当前类名: 类名: @implementation GoodsDetailVC
  目标类名: 文件名: XGGGoodsDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
  当前类名: 类名: @implementation CommodityEvaluationCell
  目标类名: 文件名: XGGCommodityEvaluationCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
  当前类名: 类名: @implementation CommodityCell1
  目标类名: 文件名: XGGCommodityCell1
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.m
  当前类名: 类名: @implementation CommodityCell2Row2
  目标类名: 文件名: XGGCommodityCell2Row2
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
  当前类名: 类名: @implementation CommodityCell3
  目标类名: 文件名: XGGCommodityCell3
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.m
  当前类名: 类名: @implementation CommodityCell2Row1
  目标类名: 文件名: XGGCommodityCell2Row1
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGsliderCollectionVCell.m
  当前类名: 类名: @implementation sliderCollectionVCell
  目标类名: 文件名: XGGsliderCollectionVCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/view/XGGStoreInfoView.m
  当前类名: 类名: @implementation StoreInfoView
  目标类名: 文件名: XGGStoreInfoView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGsliderCollectionView.m
  当前类名: 类名: @implementation sliderCollectionView{
  目标类名: 文件名: XGGsliderCollectionView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGGoodsExplainCell.m
  当前类名: 类名: @implementation GoodsExplainCell
  目标类名: 文件名: XGGGoodsExplainCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGShowDetailVC.m
  当前类名: 类名: @implementation ShowDetailVC
  目标类名: 文件名: XGGShowDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品详情/XGGCommodityDetailVC.m
  当前类名: 类名: @implementation CommodityDetailVC
  目标类名: 文件名: XGGCommodityDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/立即支付/XGGPayOrderView.m
  当前类名: 类名: @implementation PayOrderView
  目标类名: 文件名: XGGPayOrderView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/追评/XGGAppendEvaluateVC.m
  当前类名: 类名: @implementation AppendEvaluateVC
  目标类名: 文件名: XGGAppendEvaluateVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/开店申请/XGGApplyShopVC.m
  当前类名: 类名: @implementation ApplyShopVC
  目标类名: 文件名: XGGApplyShopVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/开店申请/XGGShopApplyStatusVC.m
  当前类名: 类名: @implementation ShopApplyStatusVC
  目标类名: 文件名: XGGShopApplyStatusVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/保证金/XGGBondViewController.m
  当前类名: 类名: @implementation BondViewController
  目标类名: 文件名: XGGBondViewController
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的地址/XGGAddressCell.m
  当前类名: 类名: @implementation AddressCell
  目标类名: 文件名: XGGAddressCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的地址/XGGRejectAddressModel.m
  当前类名: 类名: @implementation RejectAddressModel
  目标类名: 文件名: XGGRejectAddressModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的地址/XGGAddressVC.m
  当前类名: 类名: @implementation AddressVC
  目标类名: 文件名: XGGAddressVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
  当前类名: 类名: @implementation EditAdressVC
  目标类名: 文件名: XGGEditAdressVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的地址/XGGAddressModel.m
  当前类名: 类名: @implementation AddressModel
  目标类名: 文件名: XGGAddressModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品评价/XGGEvaluationListCell.m
  当前类名: 类名: @implementation EvaluationListCell
  目标类名: 文件名: XGGEvaluationListCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品评价/XGGEvaluationListModel.m
  当前类名: 类名: @implementation EvaluationListModel
  目标类名: 文件名: XGGEvaluationListModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
  当前类名: 类名: @implementation GoodsEvaluationListVC
  目标类名: 文件名: XGGGoodsEvaluationListVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
  当前类名: 类名: @implementation BuyerGetMoneyVC
  目标类名: 文件名: XGGBuyerGetMoneyVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
  当前类名: 类名: @implementation BuyerRefundDetailVC
  目标类名: 文件名: XGGBuyerRefundDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/买家退款详情/XGGBuyerRefundModel.m
  当前类名: 类名: @implementation BuyerRefundModel
  目标类名: 文件名: XGGBuyerRefundModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.m
  当前类名: 类名: @implementation BuyerRefundHeadView
  目标类名: 文件名: XGGBuyerRefundHeadView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/XGGOrderModel.m
  当前类名: 类名: @implementation OrderModel
  目标类名: 文件名: XGGOrderModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/XGGOrderListVC.m
  当前类名: 类名: @implementation OrderListVC
  目标类名: 文件名: XGGOrderListVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m
  当前类名: 类名: @implementation OrderDetailModel
  目标类名: 文件名: XGGOrderDetailModel
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
  当前类名: 类名: @implementation OrderDetailVC
  目标类名: 文件名: XGGOrderDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
  当前类名: 类名: @implementation OrderInfoView
  目标类名: 文件名: XGGOrderInfoView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
  当前类名: 类名: @implementation OrderPriceView
  目标类名: 文件名: XGGOrderPriceView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
  当前类名: 类名: @implementation OrderPublicView
  目标类名: 文件名: XGGOrderPublicView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
  当前类名: 类名: @implementation OrderHeaderView
  目标类名: 文件名: XGGOrderHeaderView
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/我的订单/XGGOrderListCell.m
  当前类名: 类名: @implementation OrderListCell
  目标类名: 文件名: XGGOrderListCell
  ✅ 成功修改

处理文件: YBVideo/店铺/买家端/账户余额/XGGAccountBalanceVC.m
  当前类名: 类名: @implementation AccountBalanceVC
  目标类名: 文件名: XGGAccountBalanceVC
  ✅ 成功修改

处理文件: YBVideo/店铺/小店主页/卖家页面/XGGSellerView.m
  当前类名: 类名: @implementation SellerView
  目标类名: 文件名: XGGSellerView
  ✅ 成功修改

处理文件: YBVideo/店铺/小店主页/XGGShopHomeVC.m
  当前类名: 类名: @implementation ShopHomeVC
  目标类名: 文件名: XGGShopHomeVC
  ✅ 成功修改

处理文件: YBVideo/店铺/小店主页/买家页面/XGGBuyerView.m
  当前类名: 类名: @implementation BuyerView
  目标类名: 文件名: XGGBuyerView
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/提取收益/XGGGetMoneyVC.m
  当前类名: 类名: @implementation GetMoneyVC
  目标类名: 文件名: XGGGetMoneyVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/商品管理/XGGCommodityManagementVC.m
  当前类名: 类名: @implementation CommodityManagementVC
  目标类名: 文件名: XGGCommodityManagementVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/商品管理/XGGCommodityModel.m
  当前类名: 类名: @implementation CommodityModel
  目标类名: 文件名: XGGCommodityModel
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/商品管理/XGGCommodityCell.m
  当前类名: 类名: @implementation CommodityCell
  目标类名: 文件名: XGGCommodityCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
  当前类名: 类名: @implementation OtherSellOrderDetailVC
  目标类名: 文件名: XGGOtherSellOrderDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.m
  当前类名: 类名: @implementation SellOrderDetailModel
  目标类名: 文件名: XGGSellOrderDetailModel
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
  当前类名: 类名: @implementation EditSaveAddressVC
  目标类名: 文件名: XGGEditSaveAddressVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/订单管理/XGGSellOrderCell.m
  当前类名: 类名: @implementation SellOrderCell
  目标类名: 文件名: XGGSellOrderCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/订单管理/XGGSellOrderModel.m
  当前类名: 类名: @implementation SellOrderModel
  目标类名: 文件名: XGGSellOrderModel
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
  当前类名: 类名: @implementation SellerOrderManagementVC
  目标类名: 文件名: XGGSellerOrderManagementVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m
  当前类名: 类名: @implementation RefuseRefundVC
  目标类名: 文件名: XGGRefuseRefundVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/退款详情/XGGRefundDetailVC.m
  当前类名: 类名: @implementation RefundDetailVC
  目标类名: 文件名: XGGRefundDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/退款详情/XGGRefundDetailModel.m
  当前类名: 类名: @implementation RefundDetailModel
  目标类名: 文件名: XGGRefundDetailModel
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/平台商品/XGGPlatformListCell.m
  当前类名: 类名: @implementation PlatformListCell
  目标类名: 文件名: XGGPlatformListCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
  当前类名: 类名: @implementation PlatformGoodsVC
  目标类名: 文件名: XGGPlatformGoodsVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/资质/XGGQualificationsVC.m
  当前类名: 类名: @implementation QualificationsVC
  目标类名: 文件名: XGGQualificationsVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/价格与库存/XGGStockView.m
  当前类名: 类名: @implementation StockView
  目标类名: 文件名: XGGStockView
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/价格与库存/XGGEditStockVC.m
  当前类名: 类名: @implementation EditStockVC
  目标类名: 文件名: XGGEditStockVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/账单管理/XGGBillCell.m
  当前类名: 类名: @implementation BillCell
  目标类名: 文件名: XGGBillCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/账单管理/XGGBillManageVC.m
  当前类名: 类名: @implementation BillManageVC
  目标类名: 文件名: XGGBillManageVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m
  当前类名: 类名: @implementation SelCommodityClassVC
  目标类名: 文件名: XGGSelCommodityClassVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/添加商品/XGGAddCommodityVC.m
  当前类名: 类名: @implementation AddCommodityVC
  目标类名: 文件名: XGGAddCommodityVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
  当前类名: 类名: @implementation CommodityDetailView
  目标类名: 文件名: XGGCommodityDetailView
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/添加商品/子页面view/XGGStandardsView.m
  当前类名: 类名: @implementation StandardsView
  目标类名: 文件名: XGGStandardsView
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
  当前类名: 类名: @implementation CommodityTitleView
  目标类名: 文件名: XGGCommodityTitleView
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
  当前类名: 类名: @implementation RelationVideoGoodsVC
  目标类名: 文件名: XGGRelationVideoGoodsVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/XGGshopCell.m
  当前类名: 类名: @implementation shopCell
  目标类名: 文件名: XGGshopCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
  当前类名: 类名: @implementation RelationGoodsVC
  目标类名: 文件名: XGGRelationGoodsVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/XGGPlatformCell.m
  当前类名: 类名: @implementation PlatformCell
  目标类名: 文件名: XGGPlatformCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
  当前类名: 类名: @implementation GoodsDetailViewController
  目标类名: 文件名: XGGGoodsDetailViewController
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/XGGshopDetailVC.m
  当前类名: 类名: @implementation shopDetailVC
  目标类名: 文件名: XGGshopDetailVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/XGGRelationGoodsModel.m
  当前类名: 类名: @implementation RelationGoodsModel
  目标类名: 文件名: XGGRelationGoodsModel
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/XGGgoodsShowCell.m
  当前类名: 类名: @implementation goodsShowCell
  目标类名: 文件名: XGGgoodsShowCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
  当前类名: 类名: @implementation AddGoodsVC
  目标类名: 文件名: XGGAddGoodsVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
  当前类名: 类名: @implementation WaitSendGoodsVC
  目标类名: 文件名: XGGWaitSendGoodsVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/待发货详情/XGGSendGoodsInfo.m
  当前类名: 类名: @implementation SendGoodsInfo
  目标类名: 文件名: XGGSendGoodsInfo
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/待发货详情/XGGLogisticsCell.m
  当前类名: 类名: @implementation LogisticsCell
  目标类名: 文件名: XGGLogisticsCell
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/小店详情/XGGShopInfoVC.m
  当前类名: 类名: @implementation ShopInfoVC
  目标类名: 文件名: XGGShopInfoVC
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/公共页面/XGGSellOrderPublicView.m
  当前类名: 类名: @implementation SellOrderPublicView
  目标类名: 文件名: XGGSellOrderPublicView
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/公共页面/XGGRefundHeadView.m
  当前类名: 类名: @implementation RefundHeadView
  目标类名: 文件名: XGGRefundHeadView
  ✅ 成功修改

处理文件: YBVideo/店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
  当前类名: 类名: @implementation AddOtherSaleGoodsVC
  目标类名: 文件名: XGGAddOtherSaleGoodsVC
  ✅ 成功修改

处理文件: YBVideo/缓存/权限(tab中间按钮)/XGGPower.m
  当前类名: 类名: @implementation XGGPower
  目标类名: 文件名: XGGPower
  ✅ 成功修改

处理文件: YBVideo/缓存/定位/XGGcityDefault.m
  当前类名: 类名: @implementation XGGcityDefault
  目标类名: 文件名: XGGcityDefault
  ✅ 成功修改

处理文件: YBVideo/缓存/定位/XGGRKLBSManager.m
  当前类名: 类名: @implementation RKLBSManager
  目标类名: 文件名: XGGRKLBSManager
  ✅ 成功修改

处理文件: YBVideo/缓存/配置信息/XGGcommon.m
  当前类名: 类名: @implementation common
  目标类名: 文件名: XGGcommon
  ✅ 成功修改

处理文件: YBVideo/缓存/个人信息/XGGConfig.m
  当前类名: 类名: @implementation Config
  目标类名: 文件名: XGGConfig
  ✅ 成功修改

处理文件: YBVideo/底部导航/XGGYBTabBar.m
  当前类名: 类名: @implementation YBTabBar
  目标类名: 文件名: XGGYBTabBar
  ✅ 成功修改

处理文件: YBVideo/底部导航/直播or视频/XGGYBLiveOrVideo.m
  当前类名: 类名: @implementation YBLiveOrVideo
  目标类名: 文件名: XGGYBLiveOrVideo
  ✅ 成功修改

处理文件: YBVideo/底部导航/XGGYBTabBarController.m
  当前类名: 类名: @implementation YBTabBarController
  目标类名: 文件名: XGGYBTabBarController
  ✅ 成功修改

处理文件: YBVideo/消息/XGGchatmessageCell.m
  当前类名: 类名: @implementation chatmessageCell
  目标类名: 文件名: XGGchatmessageCell
  ✅ 成功修改

处理文件: YBVideo/消息/XGGOrderMessageVC.m
  当前类名: 类名: @implementation OrderMessageVC
  目标类名: 文件名: XGGOrderMessageVC
  ✅ 成功修改

处理文件: YBVideo/消息/XGGMsgTopPubVC.m
  当前类名: 类名: @implementation MsgTopPubVC
  目标类名: 文件名: XGGMsgTopPubVC
  ✅ 成功修改

处理文件: YBVideo/消息/XGGMsgSysVC.m
  当前类名: 类名: @implementation MsgSysVC
  目标类名: 文件名: XGGMsgSysVC
  ✅ 成功修改

处理文件: YBVideo/消息/XGGOrderMessageModel.m
  当前类名: 类名: @implementation OrderMessageModel
  目标类名: 文件名: XGGOrderMessageModel
  ✅ 成功修改

处理文件: YBVideo/消息/XGGMessageFansVC.m
  当前类名: 类名: @implementation MessageFansVC
  目标类名: 文件名: XGGMessageFansVC
  ✅ 成功修改

处理文件: YBVideo/消息/选择联系人/XGGSelPeopleCell.m
  当前类名: 类名: @implementation SelPeopleCell
  目标类名: 文件名: XGGSelPeopleCell
  ✅ 成功修改

处理文件: YBVideo/消息/选择联系人/XGGSelPeopleV.m
  当前类名: 类名: @implementation SelPeopleV
  目标类名: 文件名: XGGSelPeopleV
  ✅ 成功修改

处理文件: YBVideo/消息/model/XGGMsgTopPubModel.m
  当前类名: 类名: @implementation MsgTopPubModel
  目标类名: 文件名: XGGMsgTopPubModel
  ✅ 成功修改

处理文件: YBVideo/消息/model/XGGMsgSysModel.m
  当前类名: 类名: @implementation MsgSysModel
  目标类名: 文件名: XGGMsgSysModel
  ✅ 成功修改

处理文件: YBVideo/消息/model/XGGMessageListModel.m
  当前类名: 类名: @implementation MessageListModel
  目标类名: 文件名: XGGMessageListModel
  ✅ 成功修改

处理文件: YBVideo/消息/model/XGGMessageFansModel.m
  当前类名: 类名: @implementation MessageFansModel
  目标类名: 文件名: XGGMessageFansModel
  ✅ 成功修改

处理文件: YBVideo/消息/view/XGGMessageListCell.m
  当前类名: 类名: @implementation MessageListCell
  目标类名: 文件名: XGGMessageListCell
  ✅ 成功修改

处理文件: YBVideo/消息/view/XGGMessageHeaderV.m
  当前类名: 类名: @implementation MessageHeaderV
  目标类名: 文件名: XGGMessageHeaderV
  ✅ 成功修改

处理文件: YBVideo/消息/view/XGGMsgTopPubCell.m
  当前类名: 类名: @implementation MsgTopPubCell
  目标类名: 文件名: XGGMsgTopPubCell
  ✅ 成功修改

处理文件: YBVideo/消息/view/XGGMessageFansCell.m
  当前类名: 类名: @implementation MessageFansCell
  目标类名: 文件名: XGGMessageFansCell
  ✅ 成功修改

处理文件: YBVideo/消息/view/MessageCell.m
  当前类名: 类名: @implementation MessageListCell
  目标类名: 文件名: MessageCell
  ✅ 成功修改

处理文件: YBVideo/消息/view/XGGMsgSysCell.m
  当前类名: 类名: @implementation MsgSysCell
  目标类名: 文件名: XGGMsgSysCell
  ✅ 成功修改

处理文件: YBVideo/消息/MessageVC.m
  当前类名: 类名: @implementation MessageListVC
  目标类名: 文件名: MessageVC
  ✅ 成功修改

处理文件: YBVideo/直播模块/用户端相关/view/XGGYBPlayCtrlView.m
  当前类名: 类名: @implementation YBPlayCtrlView
  目标类名: 文件名: XGGYBPlayCtrlView
  ✅ 成功修改

处理文件: YBVideo/直播模块/用户端相关/XGGYBPlayVC.m
  当前类名: 类名: @implementation YBPlayVC
  目标类名: 文件名: XGGYBPlayVC
  ✅ 成功修改

处理文件: YBVideo/直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.m
  当前类名: 类名: @implementation YBCheckLiveObj
  目标类名: 文件名: XGGYBCheckLiveObj
  ✅ 成功修改

处理文件: YBVideo/直播模块/直播列表/XGGYBLiveListVC.m
  当前类名: 类名: @implementation YBLiveListVC
  目标类名: 文件名: XGGYBLiveListVC
  ✅ 成功修改

处理文件: YBVideo/直播模块/直播列表/view/XGGYBLiveListCell.m
  当前类名: 类名: @implementation YBLiveListCell
  目标类名: 文件名: XGGYBLiveListCell
  ✅ 成功修改

处理文件: YBVideo/直播模块/房间警告/XGGYBLiveRoomAlertView.m
  当前类名: 类名: @implementation YBLiveRoomAlertView
  目标类名: 文件名: XGGYBLiveRoomAlertView
  ✅ 成功修改

处理文件: YBVideo/直播模块/RTCPush/XGGYBLiveRTCManager.m
  当前类名: 类名: @implementation YBLiveRTCManager
  目标类名: 文件名: XGGYBLiveRTCManager
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播-用户公用/XGGYBChatToolBar.m
  当前类名: 类名: @implementation YBChatToolBar
  目标类名: 文件名: XGGYBChatToolBar
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播-用户公用/XGGYBLiveEndView.m
  当前类名: 类名: @implementation YBLiveEndView
  目标类名: 文件名: XGGYBLiveEndView
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
  当前类名: 类名: @implementation roomShowGoodsView
  目标类名: 文件名: XGGroomShowGoodsView
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m
  当前类名: 类名: @implementation startLiveClassCell
  目标类名: 文件名: XGGstartLiveClassCell
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
  当前类名: 类名: @implementation startLiveClassVC
  目标类名: 文件名: XGGstartLiveClassVC
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播端相关/view/XGGYBLiveFucView.m
  当前类名: 类名: @implementation YBLiveFucView
  目标类名: 文件名: XGGYBLiveFucView
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播端相关/view/XGGYBLiveCtrlView.m
  当前类名: 类名: @implementation YBLiveCtrlView
  目标类名: 文件名: XGGYBLiveCtrlView
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播端相关/view/XGGYBLivePreview.m
  当前类名: 类名: @implementation YBLivePreview
  目标类名: 文件名: XGGYBLivePreview
  ✅ 成功修改

处理文件: YBVideo/直播模块/主播端相关/XGGYBLiveVC.m
  当前类名: 类名: @implementation YBLiveVC
  目标类名: 文件名: XGGYBLiveVC
  ✅ 成功修改

处理文件: YBVideo/直播模块/socket/XGGYBSocketLive.m
  当前类名: 类名: @implementation YBSocketLive
  目标类名: 文件名: XGGYBSocketLive
  ✅ 成功修改

处理文件: YBVideo/直播模块/socket/XGGYBSocketPlay.m
  当前类名: 类名: @implementation YBSocketPlay
  目标类名: 文件名: XGGYBSocketPlay
  ✅ 成功修改

处理文件: YBVideo/功能/会员/view/XGGYBVipCell.m
  当前类名: 类名: @implementation YBVipCell
  目标类名: 文件名: XGGYBVipCell
  ✅ 成功修改

处理文件: YBVideo/功能/会员/view/XGGYBVipHeader.m
  当前类名: 类名: @implementation YBVipHeader
  目标类名: 文件名: XGGYBVipHeader
  ✅ 成功修改

处理文件: YBVideo/功能/会员/view/XGGvipBuyView.m
  当前类名: 类名: @implementation vipBuyView
  目标类名: 文件名: XGGvipBuyView
  ✅ 成功修改

处理文件: YBVideo/功能/会员/XGGYBVipVC.m
  当前类名: 类名: @implementation YBVipVC
  目标类名: 文件名: XGGYBVipVC
  ✅ 成功修改

处理文件: YBVideo/功能/钱包/支付公共方法/XGGYBRechargeType.m
  当前类名: 类名: @implementation YBRechargeType
  目标类名: 文件名: XGGYBRechargeType
  ✅ 成功修改

处理文件: YBVideo/功能/钱包/XGGYBRechargeVC.m
  当前类名: 类名: @implementation YBRechargeVC
  目标类名: 文件名: XGGYBRechargeVC
  ✅ 成功修改

处理文件: YBVideo/功能/粉丝_关注_拉黑/XGGfansViewController.m
  当前类名: 类名: @implementation fansViewController
  目标类名: 文件名: XGGfansViewController
  ✅ 成功修改

处理文件: YBVideo/功能/粉丝_关注_拉黑/blackListCell.m
  当前类名: 类名: @implementation BlackListCell
  目标类名: 文件名: blackListCell
  ✅ 成功修改

处理文件: YBVideo/功能/粉丝_关注_拉黑/XGGattrViewController.m
  当前类名: 类名: @implementation attrViewController
  目标类名: 文件名: XGGattrViewController
  ✅ 成功修改

处理文件: YBVideo/功能/粉丝_关注_拉黑/XGGfansModel.m
  当前类名: 类名: @implementation fansModel
  目标类名: 文件名: XGGfansModel
  ✅ 成功修改

处理文件: YBVideo/功能/粉丝_关注_拉黑/XGGBlackListVC.m
  当前类名: 类名: @implementation BlackListVC
  目标类名: 文件名: XGGBlackListVC
  ✅ 成功修改

处理文件: YBVideo/功能/粉丝_关注_拉黑/XGGfans.m
  当前类名: 类名: @implementation fans
  目标类名: 文件名: XGGfans
  ✅ 成功修改

处理文件: YBVideo/功能/H5/XGGPubH5.m
  当前类名: 类名: @implementation PubH5
  目标类名: 文件名: XGGPubH5
  ✅ 成功修改

处理文件: YBVideo/功能/上热门/XGGUpHotCell.m
  当前类名: 类名: @implementation UpHotCell
  目标类名: 文件名: XGGUpHotCell
  ✅ 成功修改

处理文件: YBVideo/功能/上热门/XGGHotVideoDetailVC.m
  当前类名: 类名: @implementation HotVideoDetailVC
  目标类名: 文件名: XGGHotVideoDetailVC
  ✅ 成功修改

处理文件: YBVideo/功能/上热门/XGGaddHotVideoVC.m
  当前类名: 类名: @implementation addHotVideoVC
  目标类名: 文件名: XGGaddHotVideoVC
  ✅ 成功修改

处理文件: YBVideo/功能/登录奖励/XGGLogFirstCell.m
  当前类名: 类名: @implementation LogFirstCell
  目标类名: 文件名: XGGLogFirstCell
  ✅ 成功修改

处理文件: YBVideo/功能/登录奖励/XGGLogFirstCell2.m
  当前类名: 类名: @implementation LogFirstCell2
  目标类名: 文件名: XGGLogFirstCell2
  ✅ 成功修改

处理文件: YBVideo/功能/登录奖励/XGGLoginbonus.m
  当前类名: 类名: @implementation Loginbonus
  目标类名: 文件名: XGGLoginbonus
  ✅ 成功修改

处理文件: YBVideo/功能/顶部导航搜索/XGGsearchVC.m
  当前类名: 类名: @implementation searchVC
  目标类名: 文件名: XGGsearchVC
  ✅ 成功修改

处理文件: YBVideo/功能/顶部导航搜索/HXSearchBar.m
  当前类名: 类名: @implementation HXSearchBar
  目标类名: 文件名: HXSearchBar
  ✅ 成功修改

处理文件: YBVideo/功能/顶部导航搜索/view/XGGSearchHistoryCell.m
  当前类名: 类名: @implementation SearchHistoryCell
  目标类名: 文件名: XGGSearchHistoryCell
  ✅ 成功修改

处理文件: YBVideo/功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.m
  当前类名: 类名: @implementation YBSearchBarView
  目标类名: 文件名: XGGYBSearchBarView
  ✅ 成功修改

处理文件: YBVideo/功能/存储功能类/XGGYBStorageObj.m
  当前类名: 类名: @implementation YBStorageObj
  目标类名: 文件名: XGGYBStorageObj
  ✅ 成功修改

处理文件: YBVideo/功能/存储功能类/XGGStorageConfig.m
  当前类名: 类名: @implementation StorageConfig
  目标类名: 文件名: XGGStorageConfig
  ✅ 成功修改

处理文件: YBVideo/功能/评论/XGGcommDetailCell.m
  当前类名: 类名: @implementation commDetailCell
  目标类名: 文件名: XGGcommDetailCell
  ✅ 成功修改

处理文件: YBVideo/功能/评论/XGGcommentview.m
  当前类名: 类名: @implementation commentview
  目标类名: 文件名: XGGcommentview
  ✅ 成功修改

处理文件: YBVideo/功能/评论/XGGcommCell.m
  当前类名: 类名: @implementation commCell{
  目标类名: 文件名: XGGcommCell
  ✅ 成功修改

处理文件: YBVideo/功能/评论/XGGdetailmodel.m
  当前类名: 类名: @implementation detailmodel
  目标类名: 文件名: XGGdetailmodel
  ✅ 成功修改

处理文件: YBVideo/功能/评论/评论工具栏/XGGYBCommentToolBar.m
  当前类名: 类名: @implementation YBCommentToolBar
  目标类名: 文件名: XGGYBCommentToolBar
  ✅ 成功修改

处理文件: YBVideo/功能/评论/XGGcommentModel.m
  当前类名: 类名: @implementation commentModel
  目标类名: 文件名: XGGcommentModel
  ✅ 成功修改

处理文件: YBVideo/功能/青少年/XGGYBYoungManager.m
  当前类名: 类名: @implementation YBYoungManager
  目标类名: 文件名: XGGYBYoungManager
  ✅ 成功修改

处理文件: YBVideo/功能/青少年/vc/XGGYBYoungModifyVC.m
  当前类名: 类名: @implementation YBYoungModifyVC
  目标类名: 文件名: XGGYBYoungModifyVC
  ✅ 成功修改

处理文件: YBVideo/功能/青少年/vc/密码/XGGRKCodeInputView.m
  当前类名: 类名: @implementation RKCodeInputView
  目标类名: 文件名: XGGRKCodeInputView
  ✅ 成功修改

处理文件: YBVideo/功能/青少年/vc/密码/XGGRKCodeView.m
  当前类名: 类名: @implementation RKCodeView
  目标类名: 文件名: XGGRKCodeView
  ✅ 成功修改

处理文件: YBVideo/功能/青少年/vc/XGGYBYoungModeVC.m
  当前类名: 类名: @implementation YBYoungModeVC
  目标类名: 文件名: XGGYBYoungModeVC
  ✅ 成功修改

处理文件: YBVideo/功能/青少年/vc/XGGYBYoungSetVC.m
  当前类名: 类名: @implementation YBYoungSetVC
  目标类名: 文件名: XGGYBYoungSetVC
  ✅ 成功修改

处理文件: YBVideo/功能/青少年/小窗/XGGYBYoungSmall.m
  当前类名: 类名: @implementation YBYoungSmall
  目标类名: 文件名: XGGYBYoungSmall
  ✅ 成功修改

处理文件: YBVideo/功能/我的名片/XGGBusinessCardVC.m
  当前类名: 类名: @implementation BusinessCardVC
  目标类名: 文件名: XGGBusinessCardVC
  ✅ 成功修改

处理文件: YBVideo/功能/KeepLive/XGGRKKeepAlive.m
  当前类名: 类名: @implementation RKKeepAlive
  目标类名: 文件名: XGGRKKeepAlive
  ✅ 成功修改

处理文件: YBVideo/功能/广告管理/XGGMyAdvertVC.m
  当前类名: 类名: @implementation MyAdvertVC
  目标类名: 文件名: XGGMyAdvertVC
  ✅ 成功修改

处理文件: YBVideo/功能/广告管理/XGGAdvertManagerVC.m
  当前类名: 类名: @implementation AdvertManagerVC
  目标类名: 文件名: XGGAdvertManagerVC
  ✅ 成功修改

处理文件: YBVideo/功能/广告管理/XGGMyAdvertCell.m
  当前类名: 类名: @implementation MyAdvertCell
  目标类名: 文件名: XGGMyAdvertCell
  ✅ 成功修改

处理文件: YBVideo/功能/观看商品/XGGlookVGoodsDView.m
  当前类名: 类名: @implementation lookVGoodsDView{
  目标类名: 文件名: XGGlookVGoodsDView
  ✅ 成功修改

处理文件: YBVideo/功能/邀请码/XGGYBInviteCode.m
  当前类名: 类名: @implementation YBInviteCode
  目标类名: 文件名: XGGYBInviteCode
  ✅ 成功修改

处理文件: YBVideo/功能/邀请码/XGGYBInvitationView.m
  当前类名: 类名: @implementation YBInvitationView{
  目标类名: 文件名: XGGYBInvitationView
  ✅ 成功修改

处理文件: YBVideo/功能/举报(直播间+看视频)/XGGYBLiveReportVC.m
  当前类名: 类名: @implementation YBLiveReportVC
  目标类名: 文件名: XGGYBLiveReportVC
  ✅ 成功修改

处理文件: YBVideo/功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
  当前类名: 类名: @implementation YBVideoReportVC
  目标类名: 文件名: XGGYBVideoReportVC
  ✅ 成功修改

处理文件: YBVideo/功能/举报(直播间+看视频)/view/XGGYBReportCell.m
  当前类名: 类名: @implementation YBReportCell
  目标类名: 文件名: XGGYBReportCell
  ✅ 成功修改

处理文件: YBVideo/功能/语言包/XGGYBLanguageTools.m
  当前类名: 类名: @implementation YBLanguageTools
  目标类名: 文件名: XGGYBLanguageTools
  ✅ 成功修改

处理文件: YBVideo/功能/拍摄同款/XGGYBTakeSameVideoVC.m
  当前类名: 类名: @implementation YBTakeSameVideoVC
  目标类名: 文件名: XGGYBTakeSameVideoVC
  ✅ 成功修改

处理文件: YBVideo/功能/注销账号/XGGYBDestroyAccount.m
  当前类名: 类名: @implementation YBDestroyAccount
  目标类名: 文件名: XGGYBDestroyAccount
  ✅ 成功修改

处理文件: YBVideo/功能/注销账号/XGGYBDestroySureVC.m
  当前类名: 类名: @implementation YBDestroySureVC
  目标类名: 文件名: XGGYBDestroySureVC
  ✅ 成功修改

处理文件: YBVideo/功能/注销账号/view/XGGYBDestroyCell.m
  当前类名: 类名: @implementation YBDestroyCell
  目标类名: 文件名: XGGYBDestroyCell
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/XGGYBGiftView.m
  当前类名: 类名: @implementation CollectionCellWhite
  目标类名: 文件名: XGGYBGiftView
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/page/XGGYBPageControl.m
  当前类名: 类名: @implementation YBPageControl
  目标类名: 文件名: XGGYBPageControl
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/手绘礼物/XGGRKShowPaintedView.m
  当前类名: 类名: @implementation RKShowPaintedView
  目标类名: 文件名: XGGRKShowPaintedView
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/手绘礼物/XGGRKPaintedGiftView.m
  当前类名: 类名: @implementation RKPaintedGiftView
  目标类名: 文件名: XGGRKPaintedGiftView
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/XGGYBGiftPage.m
  当前类名: 类名: @implementation YBGiftPage
  目标类名: 文件名: XGGYBGiftPage
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/礼物特效/XGGexoensiveGifGiftV.m
  当前类名: 类名: @implementation exoensiveGifGiftV{
  目标类名: 文件名: XGGexoensiveGifGiftV
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/礼物特效/XGGexpensiveGiftV.m
  当前类名: 类名: @implementation expensiveGiftV
  目标类名: 文件名: XGGexpensiveGiftV
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/礼物特效/XGGcontinueGift.m
  当前类名: 类名: @implementation RKPopView
  目标类名: 文件名: XGGcontinueGift
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/礼物特效/XGGliansongBackView.m
  当前类名: 类名: @implementation liansongBackView
  目标类名: 文件名: XGGliansongBackView
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/礼物特效/图片/CFGradientLabel.m
  当前类名: 类名: @implementation CFGradientLabel
  目标类名: 文件名: CFGradientLabel
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/model/XGGYBGiftModel.m
  当前类名: 类名: @implementation YBGiftModel
  目标类名: 文件名: XGGYBGiftModel
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/model/GiftModel.m
  当前类名: 类名: @implementation YBGiftModel
  目标类名: 文件名: GiftModel
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/view/XGGYBGiftCell.m
  当前类名: 类名: @implementation YBGiftCell
  目标类名: 文件名: XGGYBGiftCell
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/view/GiftCell.m
  当前类名: 类名: @implementation YBGiftCell
  目标类名: 文件名: GiftCell
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/PageBar/TYPagerViewLayout.m
  当前类名: 类名: @implementation TYAutoPurgeCache
  目标类名: 文件名: TYPagerViewLayout
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/PageBar/TYTabPagerBar.m
  当前类名: 类名: @implementation TYTabPagerBar
  目标类名: 文件名: TYTabPagerBar
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/PageBar/TYTabPagerBarLayout.m
  当前类名: 类名: @implementation TYTabPagerBarLayout
  目标类名: 文件名: TYTabPagerBarLayout
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/PageBar/TYPagerView.m
  当前类名: 类名: @implementation TYPagerView
  目标类名: 文件名: TYPagerView
  ✅ 成功修改

处理文件: YBVideo/功能/礼物/PageBar/TYTabPagerBarCell.m
  当前类名: 类名: @implementation TYTabPagerBarCell
  目标类名: 文件名: TYTabPagerBarCell
  ✅ 成功修改

处理文件: YBVideo/功能/标签全部视频/XGGtopicVideoCell.m
  当前类名: 类名: @implementation topicVideoCell
  目标类名: 文件名: XGGtopicVideoCell
  ✅ 成功修改

处理文件: YBVideo/功能/标签全部视频/XGGtopicDetailsVC.m
  当前类名: 类名: @implementation topicDetailsVC
  目标类名: 文件名: XGGtopicDetailsVC
  ✅ 成功修改

处理文件: YBVideo/功能/直播/守护/XGGguardShowView.m
  当前类名: 类名: @implementation guardShowView{
  目标类名: 文件名: XGGguardShowView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/守护/model/XGGguardListModel.m
  当前类名: 类名: @implementation guardListModel
  目标类名: 文件名: XGGguardListModel
  ✅ 成功修改

处理文件: YBVideo/功能/直播/守护/view/XGGguardListCell.m
  当前类名: 类名: @implementation guardListCell
  目标类名: 文件名: XGGguardListCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/守护/view/XGGguardAlertView.m
  当前类名: 类名: @implementation guardAlertView
  目标类名: 文件名: XGGguardAlertView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/守护/view/XGGgrardButton.m
  当前类名: 类名: @implementation grardButton
  目标类名: 文件名: XGGgrardButton
  ✅ 成功修改

处理文件: YBVideo/功能/直播/守护/XGGshouhuView.m
  当前类名: 类名: @implementation shouhuView{
  目标类名: 文件名: XGGshouhuView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/PK/XGGYBAnchorPKView.m
  当前类名: 类名: @implementation YBAnchorPKView{
  目标类名: 文件名: XGGYBAnchorPKView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.m
  当前类名: 类名: @implementation YBAnchorPKAlert{
  目标类名: 文件名: XGGYBAnchorPKAlert
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/PK/XGGYBPkProgressView.m
  当前类名: 类名: @implementation YBPkProgressView{
  目标类名: 文件名: XGGYBPkProgressView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.m
  当前类名: 类名: @implementation YBAnchorLinkInfo
  目标类名: 文件名: XGGYBAnchorLinkInfo
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.m
  当前类名: 类名: @implementation YBLinkAlertView{
  目标类名: 文件名: XGGYBLinkAlertView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
  当前类名: 类名: @implementation YBAnchorOnline
  目标类名: 文件名: XGGYBAnchorOnline
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m
  当前类名: 类名: @implementation YBAnchorOnlineCell
  目标类名: 文件名: XGGYBAnchorOnlineCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.m
  当前类名: 类名: @implementation YBTxLinkMicView
  目标类名: 文件名: XGGYBTxLinkMicView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/ActionSheet/CSActionSheet.m
  当前类名: 类名: @implementation CSActionSheet
  目标类名: 文件名: CSActionSheet
  ✅ 成功修改

处理文件: YBVideo/功能/直播/ActionSheet/CSActionPicker.m
  当前类名: 类名: @implementation CSActionPicker
  目标类名: 文件名: CSActionPicker
  ✅ 成功修改

处理文件: YBVideo/功能/直播/用户列表/XGGYBUserListView.m
  当前类名: 类名: @implementation YBUserListView
  目标类名: 文件名: XGGYBUserListView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/用户列表/model/XGGYBUserListModel.m
  当前类名: 类名: @implementation YBUserListModel
  目标类名: 文件名: XGGYBUserListModel
  ✅ 成功修改

处理文件: YBVideo/功能/直播/用户列表/view/XGGYBUserListCell.m
  当前类名: 类名: @implementation YBUserListCell
  目标类名: 文件名: XGGYBUserListCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/管理员列表/XGGadminCell.m
  当前类名: 类名: @implementation adminCell
  目标类名: 文件名: XGGadminCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/管理员列表/XGGadminLists.m
  当前类名: 类名: @implementation adminLists
  目标类名: 文件名: XGGadminLists
  ✅ 成功修改

处理文件: YBVideo/功能/直播/每日任务/个中VC/XGGYBDayTaskVC.m
  当前类名: 类名: @implementation YBDayTaskVC
  目标类名: 文件名: XGGYBDayTaskVC
  ✅ 成功修改

处理文件: YBVideo/功能/直播/每日任务/直播间内/XGGYBDayTaskView.m
  当前类名: 类名: @implementation YBDayTaskView
  目标类名: 文件名: XGGYBDayTaskView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/每日任务/View/XGGYBDayTaskCell.m
  当前类名: 类名: @implementation YBDayTaskCell
  目标类名: 文件名: XGGYBDayTaskCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/每日任务/XGGYBDayTaskManager.m
  当前类名: 类名: @implementation YBDayTaskManager
  目标类名: 文件名: XGGYBDayTaskManager
  ✅ 成功修改

处理文件: YBVideo/功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
  当前类名: 类名: @implementation YBGoodsBriefView
  目标类名: 文件名: XGGYBGoodsBriefView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/直播间聊天/XGGYBLiveChatView.m
  当前类名: 类名: @implementation YBLiveChatView
  目标类名: 文件名: XGGYBLiveChatView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/直播间聊天/model/XGGYBLiveChatModel.m
  当前类名: 类名: @implementation YBLiveChatModel
  目标类名: 文件名: XGGYBLiveChatModel
  ✅ 成功修改

处理文件: YBVideo/功能/直播/直播间聊天/view/XGGYBLiveChatCell.m
  当前类名: 类名: @implementation YBLiveChatCell
  目标类名: 文件名: XGGYBLiveChatCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/在售商品/XGGYBOnSaleView.m
  当前类名: 类名: @implementation YBOnSaleView
  目标类名: 文件名: XGGYBOnSaleView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/在售商品/view/XGGYBOnSaleCell.m
  当前类名: 类名: @implementation YBOnSaleCell
  目标类名: 文件名: XGGYBOnSaleCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
  当前类名: 类名: @implementation UserBulletWindow{
  目标类名: 文件名: XGGUserBulletWindow
  ✅ 成功修改

处理文件: YBVideo/功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.m
  当前类名: 类名: @implementation userLevelView
  目标类名: 文件名: XGGuserLevelView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/幸运转盘/XGGturntableView.m
  当前类名: 类名: @implementation turntableView{
  目标类名: 文件名: XGGturntableView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/幸运转盘/view/XGGturntableRecordCell.m
  当前类名: 类名: @implementation turntableRecordCell
  目标类名: 文件名: XGGturntableRecordCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/幸运转盘/view/XGGturntableResultCell.m
  当前类名: 类名: @implementation turntableResultCell
  目标类名: 文件名: XGGturntableResultCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播/幸运转盘/XGGturntableResultView.m
  当前类名: 类名: @implementation turntableResultView
  目标类名: 文件名: XGGturntableResultView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.m
  当前类名: 类名: @implementation turntableRuleView{
  目标类名: 文件名: XGGturntableRuleView
  ✅ 成功修改

处理文件: YBVideo/功能/直播/进房间动画/XGGYBUserEnterAnimation.m
  当前类名: 类名: @implementation YBUserEnterAnimation
  目标类名: 文件名: XGGYBUserEnterAnimation
  ✅ 成功修改

处理文件: YBVideo/功能/直播/直播间私信/XGGhuanxinsixinview.m
  当前类名: 类名: @implementation huanxinsixinview{
  目标类名: 文件名: XGGhuanxinsixinview
  ✅ 成功修改

处理文件: YBVideo/功能/直播/直播间私信/XGGYBImRoomSmallView.m
  当前类名: 类名: @implementation YBImRoomSmallView
  目标类名: 文件名: XGGYBImRoomSmallView
  ✅ 成功修改

处理文件: YBVideo/功能/直播榜单/XGGLiveRankCell.m
  当前类名: 类名: @implementation LiveRankCell
  目标类名: 文件名: XGGLiveRankCell
  ✅ 成功修改

处理文件: YBVideo/功能/直播榜单/XGGLiveRankVC.m
  当前类名: 类名: @implementation LiveRankVC
  目标类名: 文件名: XGGLiveRankVC
  ✅ 成功修改

处理文件: YBVideo/功能/分享/发布分享/XGGPublishShareV.m
  当前类名: 类名: @implementation PublishShareV
  目标类名: 文件名: XGGPublishShareV
  ✅ 成功修改

处理文件: YBVideo/功能/分享/观看分享/XGGYBShareView.m
  当前类名: 类名: @implementation YBShareView
  目标类名: 文件名: XGGYBShareView
  ✅ 成功修改

处理文件: YBVideo/功能/分享/观看分享/XGGYBShareViewCell.m
  当前类名: 类名: @implementation YBShareViewCell
  目标类名: 文件名: XGGYBShareViewCell
  ✅ 成功修改

处理文件: YBVideo/附近/XGGNearbyVC.m
  当前类名: 类名: @implementation NearbyVC
  目标类名: 文件名: XGGNearbyVC
  ✅ 成功修改

处理文件: YBVideo/附近/城市选择/view/XGGYBCitySelCell.m
  当前类名: 类名: @implementation YBCitySelCell
  目标类名: 文件名: XGGYBCitySelCell
  ✅ 成功修改

处理文件: YBVideo/附近/城市选择/XGGYBCitySelVC.m
  当前类名: 类名: @implementation YBCitySelVC
  目标类名: 文件名: XGGYBCitySelVC
  ✅ 成功修改

处理文件: YBVideo/附近/view/XGGNearbyCell.m
  当前类名: 类名: @implementation NearbyCell
  目标类名: 文件名: XGGNearbyCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/商品记录/XGGcommodityRecordsCell.m
  当前类名: 类名: @implementation commodityRecordsCell
  目标类名: 文件名: XGGcommodityRecordsCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
  当前类名: 类名: @implementation commodityRecordsVC
  目标类名: 文件名: XGGcommodityRecordsVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/XGGYBCenterMoreView.m
  当前类名: 类名: @implementation YBCenterMoreView
  目标类名: 文件名: XGGYBCenterMoreView
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
  当前类名: 类名: @implementation YBRedProfitVC
  目标类名: 文件名: XGGYBRedProfitVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/我的收益/view/WLCardNoFormatter.m
  当前类名: 类名: @implementation UITextField (WLRange)
  目标类名: 文件名: WLCardNoFormatter
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/我的收益/view/XGGYBAddTypeView.m
  当前类名: 类名: @implementation YBAddTypeView{
  目标类名: 文件名: XGGYBAddTypeView
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.m
  当前类名: 类名: @implementation YBGetTypeListCell
  目标类名: 文件名: XGGYBGetTypeListCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/我的收益/XGGYBGetProVC.m
  当前类名: 类名: @implementation YBGetProVC
  目标类名: 文件名: XGGYBGetProVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
  当前类名: 类名: @implementation YBGetTypeListVC
  目标类名: 文件名: XGGYBGetTypeListVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.m
  当前类名: 类名: @implementation YBGoodsLikeCell
  目标类名: 文件名: XGGYBGoodsLikeCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
  当前类名: 类名: @implementation YBGoodsLikeVC
  目标类名: 文件名: XGGYBGoodsLikeVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
  当前类名: 类名: @implementation YBApplyStoreVC
  目标类名: 文件名: XGGYBApplyStoreVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
  当前类名: 类名: @implementation YBApplyConditionVC
  目标类名: 文件名: XGGYBApplyConditionVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.m
  当前类名: 类名: @implementation YBApplyConditionCell
  目标类名: 文件名: XGGYBApplyConditionCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/房间管理/XGGRoomUserTypeCell.m
  当前类名: 类名: @implementation RoomUserTypeCell
  目标类名: 文件名: XGGRoomUserTypeCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
  当前类名: 类名: @implementation OtherRoomViewController
  目标类名: 文件名: XGGOtherRoomViewController
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
  当前类名: 类名: @implementation RoomUserListViewController
  目标类名: 文件名: XGGRoomUserListViewController
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/房间管理/XGGRoomManagementVC.m
  当前类名: 类名: @implementation RoomManagementVC
  目标类名: 文件名: XGGRoomManagementVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
  当前类名: 类名: @implementation watchingRecordsVC
  目标类名: 文件名: XGGwatchingRecordsVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.m
  当前类名: 类名: @implementation WatchRecordListCell
  目标类名: 文件名: XGGWatchRecordListCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/明细/XGGaccountDetails.m
  当前类名: 类名: @implementation accountDetails
  目标类名: 文件名: XGGaccountDetails
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
  当前类名: 类名: @implementation YBGoodsInfoVC
  目标类名: 文件名: XGGYBGoodsInfoVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.m
  当前类名: 类名: @implementation YBGoodsListCell
  目标类名: 文件名: XGGYBGoodsListCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
  当前类名: 类名: @implementation YBGoodsListVC
  目标类名: 文件名: XGGYBGoodsListVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/view/XGGYBCenterMoreCell.m
  当前类名: 类名: @implementation YBCenterMoreCell
  目标类名: 文件名: XGGYBCenterMoreCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
  当前类名: 类名: @implementation depositAccountVC
  目标类名: 文件名: XGGdepositAccountVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/自己更多/投放账户--/XGGorderVideoCell.m
  当前类名: 类名: @implementation orderVideoCell
  目标类名: 文件名: XGGorderVideoCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/他人更多/XGGYBOtherCenterMore.m
  当前类名: 类名: @implementation YBOtherCenterMore
  目标类名: 文件名: XGGYBOtherCenterMore
  ✅ 成功修改

处理文件: YBVideo/个人中心/XGGYBCenterVC.m
  当前类名: 类名: @implementation YBCenterVC
  目标类名: 文件名: XGGYBCenterVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/设置/view/XGGSetCell.m
  当前类名: 类名: @implementation SetCell
  目标类名: 文件名: XGGSetCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/设置/view/XGGSetLogoutCell.m
  当前类名: 类名: @implementation SetLogoutCell
  目标类名: 文件名: XGGSetLogoutCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/设置/用户认证/XGGYBUserAuthVC.m
  当前类名: 类名: @implementation YBUserAuthVC
  目标类名: 文件名: XGGYBUserAuthVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/设置/XGGSetViewControllor.m
  当前类名: 类名: @implementation SetViewControllor
  目标类名: 文件名: XGGSetViewControllor
  ✅ 成功修改

处理文件: YBVideo/个人中心/设置/隐私政策/XGGYBPrivateVC.m
  当前类名: 类名: @implementation YBPrivateVC
  目标类名: 文件名: XGGYBPrivateVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/头部/XGGYBCenterTopView.m
  当前类名: 类名: @implementation YBCenterTopView
  目标类名: 文件名: XGGYBCenterTopView
  ✅ 成功修改

处理文件: YBVideo/个人中心/头部/XGGHeaderBackImgView.m
  当前类名: 类名: @implementation HeaderBackImgView
  目标类名: 文件名: XGGHeaderBackImgView
  ✅ 成功修改

处理文件: YBVideo/个人中心/作品_喜欢_收藏/XGGCenterListVC.m
  当前类名: 类名: @implementation CenterListVC
  目标类名: 文件名: XGGCenterListVC
  ✅ 成功修改

处理文件: YBVideo/个人中心/作品_喜欢_收藏/XGGCenterListCell.m
  当前类名: 类名: @implementation CenterListCell
  目标类名: 文件名: XGGCenterListCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/编辑资料/XGGEditCell.m
  当前类名: 类名: @implementation EditCell
  目标类名: 文件名: XGGEditCell
  ✅ 成功修改

处理文件: YBVideo/个人中心/编辑资料/XGGEditHeader.m
  当前类名: 类名: @implementation EditHeader
  目标类名: 文件名: XGGEditHeader
  ✅ 成功修改

处理文件: YBVideo/个人中心/编辑资料/XGGEditVC.m
  当前类名: 类名: @implementation EditVC
  目标类名: 文件名: XGGEditVC
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGRKHorPickerView.m
  当前类名: 类名: @implementation RKHorPickerView {
  目标类名: 文件名: XGGRKHorPickerView
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGYBAlertActionSheet.m
  当前类名: 类名: @implementation YBAlertActionSheet
  目标类名: 文件名: XGGYBAlertActionSheet
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGYBButton.m
  当前类名: 类名: @implementation YBButton
  目标类名: 文件名: XGGYBButton
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGMyTextView.m
  当前类名: 类名: @implementation MyTextView
  目标类名: 文件名: XGGMyTextView
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGMyTextField.m
  当前类名: 类名: @implementation MyTextField
  目标类名: 文件名: XGGMyTextField
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGYBSegControl.m
  当前类名: 类名: @implementation YBSegControl
  目标类名: 文件名: XGGYBSegControl
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/跑马灯/XGGRKLampView.m
  当前类名: 类名: @implementation RKLampView
  目标类名: 文件名: XGGRKLampView
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/发布进度/XGGRKCircularProgress.m
  当前类名: 类名: @implementation RKCircularProgress
  目标类名: 文件名: XGGRKCircularProgress
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/发布进度/XGGYBUploadProgress.m
  当前类名: 类名: @implementation YBUploadProgress
  目标类名: 文件名: XGGYBUploadProgress
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGUtils.m
  当前类名: 类名: @implementation Utils
  目标类名: 文件名: XGGUtils
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/赞动画/CCAnimationBtn.m
  当前类名: 类名: @implementation CCAnimationBtn
  目标类名: 文件名: CCAnimationBtn
  ✅ 成功修改

处理文件: YBVideo/工具和自定义类/XGGmylabels.m
  当前类名: 类名: @implementation mylabels
  目标类名: 文件名: XGGmylabels
  ✅ 成功修改

