#!/bin/bash

# 第三阶段：使用修正后的映射文件更新全局引用（工作版本）
echo "=== 第三阶段：更新全局引用（工作版本） ==="

MAPPING_FILE="need_rename_mapping_fixed.txt"
PHASE3_LOG="phase3_working_log.txt"

# 清空日志
> "$PHASE3_LOG"

echo "开始第三阶段：更新全局引用..." | tee -a "$PHASE3_LOG"
echo "使用映射文件: $MAPPING_FILE" | tee -a "$PHASE3_LOG"
echo "日志文件: $PHASE3_LOG"
echo ""

if [[ ! -f "$MAPPING_FILE" ]]; then
    echo "❌ 错误：映射文件 $MAPPING_FILE 不存在" | tee -a "$PHASE3_LOG"
    exit 1
fi

# 统计变量
total_mappings=0
processed_files=0
total_replacements=0
error_count=0

# 函数：处理单个类名映射
process_class_mapping() {
    local old_class="$1"
    local new_class="$2"
    local mapping_count=0
    
    echo "处理类名映射: $old_class -> $new_class" | tee -a "$PHASE3_LOG"
    
    # 查找包含该类名的所有文件（排除备份文件和映射文件）
    local files_with_class=$(grep -r -l "$old_class" YBVideo/ 2>/dev/null | grep -v "\.backup$" | grep -v "mapping" | head -50)
    
    if [[ -z "$files_with_class" ]]; then
        echo "  未找到包含 '$old_class' 的文件" | tee -a "$PHASE3_LOG"
        return 0
    fi
    
    # 处理每个文件
    while read -r file; do
        if [[ -n "$file" && -f "$file" ]]; then
            # 检查文件是否包含该类名
            if grep -q "$old_class" "$file" 2>/dev/null; then
                echo "  处理文件: $file" | tee -a "$PHASE3_LOG"
                
                # 创建备份
                if [[ ! -f "${file}.phase3.backup" ]]; then
                    cp "$file" "${file}.phase3.backup"
                fi
                
                # 执行替换（使用简单替换，不使用单词边界）
                if sed -i '' "s/${old_class}/${new_class}/g" "$file"; then
                    # 统计替换后的新类名出现次数
                    local replacement_count=$(grep -c "$new_class" "$file" 2>/dev/null || echo "0")
                    echo "    ✅ 替换成功，新类名出现 $replacement_count 次" | tee -a "$PHASE3_LOG"
                    mapping_count=$((mapping_count + replacement_count))
                    processed_files=$((processed_files + 1))
                else
                    echo "    ❌ 替换失败" | tee -a "$PHASE3_LOG"
                    error_count=$((error_count + 1))
                fi
            fi
        fi
    done <<< "$files_with_class"
    
    echo "  类名 '$old_class' 总共替换了 $mapping_count 次" | tee -a "$PHASE3_LOG"
    echo "" | tee -a "$PHASE3_LOG"
    
    total_replacements=$((total_replacements + mapping_count))
}

# 读取映射文件并处理每个类名映射
echo "开始处理映射..." | tee -a "$PHASE3_LOG"
echo "" | tee -a "$PHASE3_LOG"

while read -r line; do
    if [[ -n "$line" && "$line" == *" -> "* ]]; then
        # 分割映射行
        old_class="${line% -> *}"
        new_class="${line#* -> }"
        
        # 跳过implementation映射（这些不需要在第三阶段处理）
        if [[ "$old_class" == *"类名: @implementation"* ]]; then
            echo "跳过implementation映射: $line" | tee -a "$PHASE3_LOG"
            continue
        fi
        
        # 跳过相同的映射
        if [[ "$old_class" == "$new_class" ]]; then
            echo "跳过相同映射: $line" | tee -a "$PHASE3_LOG"
            continue
        fi
        
        total_mappings=$((total_mappings + 1))
        
        # 处理类名映射
        process_class_mapping "$old_class" "$new_class"
        
        # 每处理10个映射显示进度
        if [[ $((total_mappings % 10)) -eq 0 ]]; then
            echo "已处理 $total_mappings 个映射..."
        fi
        
        # 限制处理前5个映射进行测试
        if [[ $total_mappings -ge 5 ]]; then
            echo "测试模式：只处理前5个映射" | tee -a "$PHASE3_LOG"
            break
        fi
    fi
done < "$MAPPING_FILE"

echo "" | tee -a "$PHASE3_LOG"
echo "=== 第三阶段完成 ===" | tee -a "$PHASE3_LOG"
echo "处理的映射数: $total_mappings" | tee -a "$PHASE3_LOG"
echo "处理的文件数: $processed_files" | tee -a "$PHASE3_LOG"
echo "总替换次数: $total_replacements" | tee -a "$PHASE3_LOG"
echo "错误数: $error_count" | tee -a "$PHASE3_LOG"

if [[ $error_count -gt 0 ]]; then
    echo "" | tee -a "$PHASE3_LOG"
    echo "⚠️  有 $error_count 个错误，请检查日志" | tee -a "$PHASE3_LOG"
    exit 1
else
    echo "" | tee -a "$PHASE3_LOG"
    echo "✅ 第三阶段执行成功！所有类名引用已更新。" | tee -a "$PHASE3_LOG"
    exit 0
fi
