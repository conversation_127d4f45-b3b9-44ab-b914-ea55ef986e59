#!/bin/bash

# 过滤出需要修改的文件，排除Category文件（包含+号的文件）
# 排除第三方SDK、框架、Category等

echo "=== 过滤业务代码文件（排除Category） ==="

INPUT_FILE="class_filename_inconsistency_report.txt"
FILTERED_REPORT="filtered_inconsistency_report.txt"
FILTERED_MAPPING="filtered_rename_mapping.txt"

# 清空输出文件
> "$FILTERED_REPORT"
> "$FILTERED_MAPPING"

# 需要排除的路径模式
EXCLUDE_PATTERNS=(
    "三方_SDK"
    "QMapKit.framework"
    "QMapSearchKit.framework" 
    "MHBeautySDK.framework"
    "美狐sdk"
    "MJExtension"
    "sbjson"
    "V8HorizontalPickerView"
    "HMSegmentedControl"
    "PreviewPicture"
    "ChatImageBubble"
    "HMPhotoPicker"
    "RecordView"
    "External"
    "CWStar"
    "aliPay"
    "百度语音"
    "极光消息"
    "腾讯消息"
    "ZZCircleProgress"
    "XLProgress"
    "Categories"
    "Vendor"
)

echo "正在过滤第三方SDK、框架和Category文件..."

# 读取原始报告文件并过滤
while IFS= read -r line; do
    if [[ "$line" == "不一致:"* ]]; then
        # 提取文件路径
        file_path=$(echo "$line" | sed 's/不一致: //')
        
        # 检查是否是Category文件（文件名包含+号）
        filename=$(basename "$file_path")
        if [[ "$filename" == *"+"* ]]; then
            # 跳过Category文件
            read -r filename_line
            read -r classname_line  
            read -r type_line
            read -r empty_line
            continue
        fi
        
        # 检查是否需要排除第三方文件
        should_exclude=false
        for pattern in "${EXCLUDE_PATTERNS[@]}"; do
            if [[ "$file_path" == *"$pattern"* ]]; then
                should_exclude=true
                break
            fi
        done
        
        # 如果不需要排除，则添加到过滤后的报告中
        if [[ "$should_exclude" == false ]]; then
            echo "$line" >> "$FILTERED_REPORT"
            # 读取接下来的4行（文件名、类名、类型、空行）
            read -r filename_line
            read -r classname_line  
            read -r type_line
            read -r empty_line
            
            echo "$filename_line" >> "$FILTERED_REPORT"
            echo "$classname_line" >> "$FILTERED_REPORT"
            echo "$type_line" >> "$FILTERED_REPORT"
            echo "$empty_line" >> "$FILTERED_REPORT"
            
            # 提取类名映射信息
            filename_only=$(echo "$filename_line" | sed 's/  文件名: //')
            classname=$(echo "$classname_line" | sed 's/  类名: //')
            echo "$classname -> $filename_only" >> "$FILTERED_MAPPING"
        else
            # 跳过这个条目的其他行
            read -r filename_line
            read -r classname_line  
            read -r type_line
            read -r empty_line
        fi
    fi
done < "$INPUT_FILE"

# 统计结果
filtered_count=$(grep -c "不一致:" "$FILTERED_REPORT")
total_count=$(grep -c "不一致:" "$INPUT_FILE")
excluded_count=$((total_count - filtered_count))

echo ""
echo "=== 过滤结果 ==="
echo "原始不一致文件数: $total_count"
echo "排除文件数: $excluded_count"  
echo "需要修改的文件数: $filtered_count"
echo ""
echo "过滤后报告: $FILTERED_REPORT"
echo "过滤后映射: $FILTERED_MAPPING"

if [[ $filtered_count -gt 0 ]]; then
    echo ""
    echo "=== 需要修改的文件预览 ==="
    head -30 "$FILTERED_REPORT"
    if [[ $filtered_count -gt 6 ]]; then
        echo "..."
        echo "(还有更多文件，请查看完整报告)"
    fi
fi
