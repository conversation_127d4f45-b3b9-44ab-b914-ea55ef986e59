#!/bin/bash

# 修正映射文件格式
echo "=== 修正映射文件格式 ==="

INPUT_REPORT="filtered_inconsistency_report.txt"
CORRECT_MAPPING="correct_rename_mapping.txt"

# 清空输出文件
> "$CORRECT_MAPPING"

echo "正在生成正确的类名映射..."

# 读取过滤后的报告文件并生成正确的映射
while IFS= read -r line; do
    if [[ "$line" == "不一致:"* ]]; then
        # 读取接下来的3行
        read -r filename_line
        read -r classname_line  
        read -r type_line
        read -r empty_line
        
        # 提取文件名和类名
        filename=$(echo "$filename_line" | sed 's/  文件名: //')
        full_classname=$(echo "$classname_line" | sed 's/  类名: //')
        
        # 从完整类名中提取实际的类名（去掉@interface和继承部分）
        if [[ "$full_classname" == *"@interface"* ]]; then
            # 提取@interface后面的类名
            actual_classname=$(echo "$full_classname" | sed -E 's/@interface\s+([A-Za-z_][A-Za-z0-9_]*).*/\1/')
        elif [[ "$full_classname" == *"@implementation"* ]]; then
            # 提取@implementation后面的类名
            actual_classname=$(echo "$full_classname" | sed -E 's/@implementation\s+([A-Za-z_][A-Za-z0-9_]*).*/\1/')
        else
            actual_classname="$full_classname"
        fi
        
        # 生成映射：原类名 -> 新类名（文件名）
        echo "$actual_classname -> $filename" >> "$CORRECT_MAPPING"
    fi
done < "$INPUT_REPORT"

# 统计结果
mapping_count=$(wc -l < "$CORRECT_MAPPING")

echo ""
echo "=== 映射修正完成 ==="
echo "生成映射条目数: $mapping_count"
echo "映射文件: $CORRECT_MAPPING"
echo ""
echo "=== 映射示例 ==="
head -10 "$CORRECT_MAPPING"
echo "..."
