不一致: YBVideo/录制_编辑_发布/UGCEditor/TCVideoEditViewController.h
  文件名: TCVideoEditViewController
  类名: @interface TCVideoEditViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoPreview.h
  文件名: TCVideoPreview
  类名: @interface TCVideoPreview : UIView<TXVideoPreviewListener>
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
  文件名: TCVideoRangeSlider
  类名: @interface TCVideoRangeSlider : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h
  文件名: TCRangeContent
  类名: @interface TCRangeContentConfig : NSObject
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.h
  文件名: XGGVideoColorInfo
  类名: @interface VideoColorInfo : NSObject
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h
  文件名: XGGEffectSelectView
  类名: @interface EffectSelectView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCFilterSettingView.h
  文件名: TCFilterSettingView
  类名: @interface TCFilterSettingView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCBottomTabBar.h
  文件名: TCBottomTabBar
  类名: @interface TCBottomTabBar : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h
  文件名: XGGTimeSelectView
  类名: @interface TimeSelectView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.h
  文件名: TCMusicCollectionCell
  类名: @interface TCMusicInfo : NSObject
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoCutView.h
  文件名: TCVideoCutView
  类名: @interface TCVideoCutView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoTextFiled.h
  文件名: TCVideoTextFiled
  类名: @interface TCVideoTextFiled : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicMixView.h
  文件名: TCMusicMixView
  类名: @interface TCMusicMixView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCTextAddView.h
  文件名: TCTextAddView
  类名: @interface TCTextAddView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCTextCollectionCell.h
  文件名: TCTextCollectionCell
  类名: @interface TCTextCollectionCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/Record/TCVideoRecordViewController.h
  文件名: TCVideoRecordViewController
  类名: @interface TCVideoRecordViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.h
  文件名: XGGAlbumVideoCell
  类名: @interface AlbumVideoCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
  文件名: XGGAlbumVideoVC
  类名: @interface AlbumVideoVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/Record/views/XGGVideoRecordProcessView.h
  文件名: XGGVideoRecordProcessView
  类名: @interface VideoRecordProcessView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/Record/views/TXBaseBeautyView.h
  文件名: TXBaseBeautyView
  类名: @interface TXBaseBeautyView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/Record/views/XGGSpeedView.h
  文件名: XGGSpeedView
  类名: @interface SpeedView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h
  文件名: XGGYBPicTransitionVC
  类名: @interface YBPicTransitionVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/图片转场/view/XGGSmallButton.h
  文件名: XGGSmallButton
  类名: @interface SmallButton : UIButton
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.h
  文件名: XGGPhotoTransitionToolbar
  类名: @interface PhotoTransitionToolbar : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/图片转场/view/XGGVerticalButton.h
  文件名: XGGVerticalButton
  类名: @interface VerticalButton : UIButton
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h
  文件名: XGGYBPublishCoverVC
  类名: @interface YBPublishCoverVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/TCVideoPublishController.h
  文件名: TCVideoPublishController
  类名: @interface TCVideoPublishController : YBBaseViewController<UITextViewDelegate>
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.h
  文件名: XGGYBSetChargeView
  类名: @interface YBSetChargeView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
  文件名: XGGYBVideoAddGoodsVC
  类名: @interface YBVideoAddGoodsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.h
  文件名: XGGvideoTopicCell
  类名: @interface videoTopicCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h
  文件名: XGGvideoTopicVC
  类名: @interface videoTopicVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
  文件名: XGGYBVideoClassVC
  类名: @interface YBVideoClassVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/音乐/XGGMusicClassVC.h
  文件名: XGGMusicClassVC
  类名: @interface MusicClassVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/音乐/XGGYBVideoMusicView.h
  文件名: XGGYBVideoMusicView
  类名: @interface YBVideoMusicView : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/音乐/model/XGGMusicModel.h
  文件名: XGGMusicModel
  类名: @interface MusicModel : NSObject
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/音乐/view/XGGMusicCell.h
  文件名: XGGMusicCell
  类名: @interface MusicCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/音乐/view/XGGMusicHeaderView.h
  文件名: XGGMusicHeaderView
  类名: @interface MusicHeaderView : UIView
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/音乐/view/XGGMusicHeaderCell.h
  文件名: XGGMusicHeaderCell
  类名: @interface MusicHeaderCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/其他类/XGGYBBaseAppDelegate.h
  文件名: XGGYBBaseAppDelegate
  类名: @interface YBBaseAppDelegate : UIResponder<UIApplicationDelegate>
  类型: @interface声明

不一致: YBVideo/其他类/XGGRKLBSManager.h
  文件名: XGGRKLBSManager
  类名: @interface RKLBSManager : NSObject
  类型: @interface声明

不一致: YBVideo/其他类/Config.h
  文件名: Config
  类名: @interface Config : NSObject
  类型: @interface声明

不一致: YBVideo/其他类/XGGcityDefault.h
  文件名: XGGcityDefault
  类名: @interface XGGcityDefault : NSObject
  类型: @interface声明

不一致: YBVideo/其他类/common.h
  文件名: common
  类名: @interface common : NSObject
  类型: @interface声明

不一致: YBVideo/其他类/sproutCommon.h
  文件名: sproutCommon
  类名: @interface sproutCommon : NSObject
  类型: @interface声明

不一致: YBVideo/其他类/XGGAppDelegate.h
  文件名: XGGAppDelegate
  类名: @interface AppDelegate : NSObject
  类型: @interface声明

不一致: YBVideo/其他类/XGGYBBaseViewController.h
  文件名: XGGYBBaseViewController
  类名: @interface YBBaseViewController : UIViewController
  类型: @interface声明

不一致: YBVideo/其他类/XGGYBNavigationController.h
  文件名: XGGYBNavigationController
  类名: @interface YBNavigationController : UINavigationController
  类型: @interface声明

不一致: YBVideo/其他类/TCNavigationController.h
  文件名: TCNavigationController
  类名: @interface YBNavigationController : UINavigationController
  类型: @interface声明

不一致: YBVideo/首页/获取视频详情公用obj/XGGYBGetVideoObj.h
  文件名: XGGYBGetVideoObj
  类名: @interface YBGetVideoObj : NSObject
  类型: @interface声明

不一致: YBVideo/首页/获取视频详情公用obj/XGGYBHomeRedObj.h
  文件名: XGGYBHomeRedObj
  类名: @interface YBHomeRedObj : NSObject
  类型: @interface声明

不一致: YBVideo/首页/推荐/view/XGGYBLookVideoCell.h
  文件名: XGGYBLookVideoCell
  类名: @interface YBLookVideoCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/首页/推荐/view/ZFCustomControlView.h
  文件名: ZFCustomControlView
  类名: @interface ZFCustomControlView : UIView <ZFPlayerMediaControl>
  类型: @interface声明

不一致: YBVideo/首页/推荐/view/XGGYBVideoControlView.h
  文件名: XGGYBVideoControlView
  类名: @interface YBVideoControlView : UIView<ZFPlayerMediaControl>
  类型: @interface声明

不一致: YBVideo/首页/推荐/XGGYBLookVideoVC.h
  文件名: XGGYBLookVideoVC
  类名: @interface YBLookVideoVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/首页/热门-关注-分类/models/XGGNearbyVideoModel.h
  文件名: XGGNearbyVideoModel
  类名: @interface NearbyVideoModel : NSObject
  类型: @interface声明

不一致: YBVideo/首页/热门-关注-分类/XGGMyFollowViewController.h
  文件名: XGGMyFollowViewController
  类名: @interface MyFollowViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/首页/热门-关注-分类/XGGYBVideosVC.h
  文件名: XGGYBVideosVC
  类名: @interface YBVideosVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/首页/热门-关注-分类/XGGmyVideoV.h
  文件名: XGGmyVideoV
  类名: @interface myVideoV : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/首页/热门-关注-分类/views/XGGVideoCollectionCell.h
  文件名: XGGVideoCollectionCell
  类名: @interface VideoCollectionCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/首页/XGGYBHomeViewController.h
  文件名: XGGYBHomeViewController
  类名: @interface YBHomeViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/登录注册/XGGDspLoginVC.h
  文件名: XGGDspLoginVC
  类名: @interface DspLoginVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/登录注册/隐私提醒文本/XGGRegAlertView.h
  文件名: XGGRegAlertView
  类名: @interface RegAlertView : UIView
  类型: @interface声明

不一致: YBVideo/登录注册/国家代号/XGGCountryCodeVC.h
  文件名: XGGCountryCodeVC
  类名: @interface CountryCodeVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/XGGNetwork/XGGNetworkManager.h
  文件名: XGGNetworkManager
  类名: @interface XGGNetworkManager : NSObject
  类型: @interface声明

不一致: YBVideo/XGGNetwork/XGGNetworkUtils.h
  文件名: XGGNetworkUtils
  类名: @interface XGGNetworkUtils : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGRKActionSheet.h
  文件名: XGGRKActionSheet
  类名: @interface RKActionSheet : UIView
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGYBProgressObj.h
  文件名: XGGYBProgressObj
  类名: @interface YBProgressObj : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGPublicObj.h
  文件名: XGGPublicObj
  类名: @interface PublicObj : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGYBNetworking.h
  文件名: XGGYBNetworking
  类名: @interface YBNetworking : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGBGSetting.h
  文件名: XGGBGSetting
  类名: @interface BGSetting : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGRKSysAccess.h
  文件名: XGGRKSysAccess
  类名: @interface RKSysAccess : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGiOSNetworking.h
  文件名: XGGiOSNetworking
  类名: @interface iOSNetworking : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGYBAlertView.h
  文件名: XGGYBAlertView
  类名: @interface YBAlertView : UIView
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGRKUUIDManager.h
  文件名: XGGRKUUIDManager
  类名: @interface RKUUIDManager : NSObject
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGYBShowBigImageView.h
  文件名: XGGYBShowBigImageView
  类名: @interface YBShowBigImageView : UIScrollView
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGYBImageView.h
  文件名: XGGYBImageView
  类名: @interface YBImageView : UIView
  类型: @interface声明

不一致: YBVideo/公共方法类/XGGPublicView.h
  文件名: XGGPublicView
  类名: @interface PublicView : UIView
  类型: @interface声明

不一致: YBVideo/引导页/XGGGuideViewController.h
  文件名: XGGGuideViewController
  类名: @interface GuideViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/退款申请/XGGApplyRefundVC.h
  文件名: XGGApplyRefundVC
  类名: @interface ApplyRefundVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/经营类目选择/XGGSelectClassVC.h
  文件名: XGGSelectClassVC
  类名: @interface SelectClassVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/经营类目选择/XGGCommodityClassModel.h
  文件名: XGGCommodityClassModel
  类名: @interface CommodityClassModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/经营类目选择/XGGCommodityClassCell.h
  文件名: XGGCommodityClassCell
  类名: @interface CommodityClassCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/确认订单/XGGConfirmOrderVC.h
  文件名: XGGConfirmOrderVC
  类名: @interface ConfirmOrderVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGHistoryListModel.h
  文件名: XGGHistoryListModel
  类名: @interface HistoryListModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryCell.h
  文件名: XGGLookHistoryCell
  类名: @interface LookHistoryCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryVC.h
  文件名: XGGLookHistoryVC
  类名: @interface LookHistoryVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryModel.h
  文件名: XGGLookHistoryModel
  类名: @interface LookHistoryModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
  文件名: XGGOutsideGoodsDetailVC
  类名: @interface OutsideGoodsDetailVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
  文件名: XGGOutsideHeadCell
  类名: @interface OutsideHeadCell : UITableViewCell<SDCycleScrollViewDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/分享商品/XGGShareGoodsAlert.h
  文件名: XGGShareGoodsAlert
  类名: @interface ShareGoodsAlert : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
  文件名: XGGShareFriendVC
  类名: @interface ShareFriendVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.h
  文件名: XGGShareFriendCell
  类名: @interface ShareFriendCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/分享商品/分享给好友/XGGFriendModel.h
  文件名: XGGFriendModel
  类名: @interface FriendModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/分享商品/XGGShareGoodView.h
  文件名: XGGShareGoodView
  类名: @interface ShareGoodView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/平台介入/XGGPlatformInterventionVC.h
  文件名: XGGPlatformInterventionVC
  类名: @interface PlatformInterventionVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/评价/CWStar/CWStarRateView.h
  文件名: CWStarRateView
  类名: @interface CWStarRateView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/评价/XGGPublishEvaluateVC.h
  文件名: XGGPublishEvaluateVC
  类名: @interface PublishEvaluateVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/经营类目设置/XGGClassToExamineVC.h
  文件名: XGGClassToExamineVC
  类名: @interface ClassToExamineVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/经营类目设置/XGGClassificationVC.h
  文件名: XGGClassificationVC
  类名: @interface ClassificationVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/选择规格/XGGStandardsCell.h
  文件名: XGGStandardsCell
  类名: @interface StandardsCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.h
  文件名: XGGSelectStandardsView
  类名: @interface SelectStandardsView : UIView<UICollectionViewDelegate, UICollectionViewDataSource>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/服务保障/XGGGuaranteeView.h
  文件名: XGGGuaranteeView
  类名: @interface GuaranteeView : UIView<UIGestureRecognizerDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGsliderCollectionVCell.h
  文件名: XGGsliderCollectionVCell
  类名: @interface sliderCollectionVCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGsliderCollectionView.h
  文件名: XGGsliderCollectionView
  类名: @interface sliderCollectionView : UIView<UIScrollViewDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGCommodityDetailVC.h
  文件名: XGGCommodityDetailVC
  类名: @interface CommodityDetailVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGShowDetailVC.h
  文件名: XGGShowDetailVC
  类名: @interface ShowDetailVC : UIViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGGoodsExplainCell.h
  文件名: XGGGoodsExplainCell
  类名: @interface GoodsExplainCell : UITableViewCell<WKNavigationDelegate,UIScrollViewDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell3.h
  文件名: XGGCommodityCell3
  类名: @interface CommodityCell3 : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.h
  文件名: XGGCommodityCell2Row2
  类名: @interface CommodityCell2Row2 : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.h
  文件名: XGGCommodityCell2Row1
  类名: @interface CommodityCell2Row1 : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell1.h
  文件名: XGGCommodityCell1
  类名: @interface CommodityCell1 : UITableViewCell<SDCycleScrollViewDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGCommodityDetailModel.h
  文件名: XGGCommodityDetailModel
  类名: @interface CommodityDetailModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.h
  文件名: XGGYBGoodPlayerCtrView
  类名: @interface YBGoodPlayerCtrView : UIView <ZFPlayerMediaControl>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/view/XGGStoreInfoView.h
  文件名: XGGStoreInfoView
  类名: @interface StoreInfoView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGGoodsDetailVC.h
  文件名: XGGGoodsDetailVC
  类名: @interface GoodsDetailVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品详情/XGGCommodityEvaluationCell.h
  文件名: XGGCommodityEvaluationCell
  类名: @interface CommodityEvaluationCell : UITableViewCell<CWStarRateViewDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/立即支付/XGGPayOrderView.h
  文件名: XGGPayOrderView
  类名: @interface PayOrderView : UIView<UITableViewDelegate, UITableViewDataSource>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/追评/XGGAppendEvaluateVC.h
  文件名: XGGAppendEvaluateVC
  类名: @interface AppendEvaluateVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/开店申请/XGGApplyShopVC.h
  文件名: XGGApplyShopVC
  类名: @interface ApplyShopVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/开店申请/XGGShopApplyStatusVC.h
  文件名: XGGShopApplyStatusVC
  类名: @interface ShopApplyStatusVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/保证金/XGGBondViewController.h
  文件名: XGGBondViewController
  类名: @interface BondViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的地址/XGGAddressModel.h
  文件名: XGGAddressModel
  类名: @interface AddressModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
  文件名: XGGEditAdressVC
  类名: @interface EditAdressVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的地址/XGGRejectAddressModel.h
  文件名: XGGRejectAddressModel
  类名: @interface RejectAddressModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的地址/XGGAddressCell.h
  文件名: XGGAddressCell
  类名: @interface AddressCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的地址/XGGAddressVC.h
  文件名: XGGAddressVC
  类名: @interface AddressVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h
  文件名: XGGGoodsEvaluationListVC
  类名: @interface GoodsEvaluationListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品评价/XGGEvaluationListModel.h
  文件名: XGGEvaluationListModel
  类名: @interface EvaluationListModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/商品评价/XGGEvaluationListCell.h
  文件名: XGGEvaluationListCell
  类名: @interface EvaluationListCell : UITableViewCell<CWStarRateViewDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/买家端/提取余额/XGGBuyerGetMoneyVC.h
  文件名: XGGBuyerGetMoneyVC
  类名: @interface BuyerGetMoneyVC : UIViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h
  文件名: XGGBuyerRefundDetailVC
  类名: @interface BuyerRefundDetailVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/买家退款详情/XGGBuyerRefundModel.h
  文件名: XGGBuyerRefundModel
  类名: @interface BuyerRefundModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.h
  文件名: XGGBuyerRefundHeadView
  类名: @interface BuyerRefundHeadView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJPropertyKey.h
  文件名: MJPropertyKey
  类名: @interface MJPropertyKey : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJFoundation.h
  文件名: MJFoundation
  类名: @interface MJFoundation : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJPropertyType.h
  文件名: MJPropertyType
  类名: @interface MJPropertyType : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJProperty.h
  文件名: MJProperty
  类名: @interface MJProperty : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.h
  文件名: XGGOrderDetailVC
  类名: @interface OrderDetailVC : UIViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.h
  文件名: XGGOrderDetailModel
  类名: @interface OrderDetailModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
  文件名: XGGOrderPublicView
  类名: @interface OrderPublicView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.h
  文件名: XGGOrderHeaderView
  类名: @interface OrderHeaderView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.h
  文件名: XGGOrderInfoView
  类名: @interface OrderInfoView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.h
  文件名: XGGOrderPriceView
  类名: @interface OrderPriceView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/XGGOrderListCell.h
  文件名: XGGOrderListCell
  类名: @interface OrderListCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/XGGOrderModel.h
  文件名: XGGOrderModel
  类名: @interface OrderModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/买家端/我的订单/XGGOrderListVC.h
  文件名: XGGOrderListVC
  类名: @interface OrderListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/买家端/账户余额/XGGAccountBalanceVC.h
  文件名: XGGAccountBalanceVC
  类名: @interface AccountBalanceVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/小店主页/XGGShopHomeVC.h
  文件名: XGGShopHomeVC
  类名: @interface ShopHomeVC : UIViewController
  类型: @interface声明

不一致: YBVideo/店铺/小店主页/卖家页面/XGGSellerView.h
  文件名: XGGSellerView
  类名: @interface SellerView : UIView<JMessageDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/小店主页/买家页面/XGGBuyerView.h
  文件名: XGGBuyerView
  类名: @interface BuyerView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/提取收益/XGGGetMoneyVC.h
  文件名: XGGGetMoneyVC
  类名: @interface GetMoneyVC : UIViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/商品管理/XGGCommodityCell.h
  文件名: XGGCommodityCell
  类名: @interface CommodityCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/商品管理/XGGCommodityModel.h
  文件名: XGGCommodityModel
  类名: @interface CommodityModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/商品管理/XGGCommodityManagementVC.h
  文件名: XGGCommodityManagementVC
  类名: @interface CommodityManagementVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.h
  文件名: XGGOtherSellOrderDetailVC
  类名: @interface OtherSellOrderDetailVC : UIViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.h
  文件名: XGGSellOrderDetailModel
  类名: @interface SellOrderDetailModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/收货地址/XGGEditSaveAddressVC.h
  文件名: XGGEditSaveAddressVC
  类名: @interface EditSaveAddressVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/订单管理/XGGSellerOrderManagementVC.h
  文件名: XGGSellerOrderManagementVC
  类名: @interface SellerOrderManagementVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/订单管理/XGGSellOrderCell.h
  文件名: XGGSellOrderCell
  类名: @interface SellOrderCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/订单管理/XGGSellOrderModel.h
  文件名: XGGSellOrderModel
  类名: @interface SellOrderModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/退款详情/XGGRefundDetailVC.h
  文件名: XGGRefundDetailVC
  类名: @interface RefundDetailVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/退款详情/XGGRefundDetailModel.h
  文件名: XGGRefundDetailModel
  类名: @interface RefundDetailModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.h
  文件名: XGGRefuseRefundVC
  类名: @interface RefuseRefundVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/平台商品/XGGPlatformListCell.h
  文件名: XGGPlatformListCell
  类名: @interface PlatformListCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/平台商品/XGGPlatformGoodsVC.h
  文件名: XGGPlatformGoodsVC
  类名: @interface PlatformGoodsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/资质/XGGQualificationsVC.h
  文件名: XGGQualificationsVC
  类名: @interface QualificationsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/价格与库存/XGGEditStockVC.h
  文件名: XGGEditStockVC
  类名: @interface EditStockVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/价格与库存/XGGStockView.h
  文件名: XGGStockView
  类名: @interface StockView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/账单管理/XGGBillManageVC.h
  文件名: XGGBillManageVC
  类名: @interface BillManageVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/账单管理/XGGBillCell.h
  文件名: XGGBillCell
  类名: @interface BillCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/添加商品/XGGAddCommodityVC.h
  文件名: XGGAddCommodityVC
  类名: @interface AddCommodityVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.h
  文件名: XGGSelCommodityClassVC
  类名: @interface SelCommodityClassVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/添加商品/子页面view/XGGStandardsView.h
  文件名: XGGStandardsView
  类名: @interface StandardsView : UIView<TZImagePickerControllerDelegate,UITextFieldDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.h
  文件名: XGGCommodityTitleView
  类名: @interface CommodityTitleView : UIView<TZImagePickerControllerDelegate,UITextFieldDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.h
  文件名: XGGCommodityDetailView
  类名: @interface CommodityDetailView : UIView<TZImagePickerControllerDelegate,UITextViewDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.h
  文件名: XGGRelationVideoGoodsVC
  类名: @interface RelationVideoGoodsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGRelationGoodsModel.h
  文件名: XGGRelationGoodsModel
  类名: @interface RelationGoodsModel : NSObject
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGgoodsShowCell.h
  文件名: XGGgoodsShowCell
  类名: @interface goodsShowCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGshopDetailVC.h
  文件名: XGGshopDetailVC
  类名: @interface shopDetailVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGshopCell.h
  文件名: XGGshopCell
  类名: @interface shopCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.h
  文件名: XGGAddGoodsVC
  类名: @interface AddGoodsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGRelationGoodsVC.h
  文件名: XGGRelationGoodsVC
  类名: @interface RelationGoodsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGPlatformCell.h
  文件名: XGGPlatformCell
  类名: @interface PlatformCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGGoodsDetailViewController.h
  文件名: XGGGoodsDetailViewController
  类名: @interface GoodsDetailViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/待发货详情/XGGSendGoodsInfo.h
  文件名: XGGSendGoodsInfo
  类名: @interface SendGoodsInfo : UIView<UITextFieldDelegate>
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/待发货详情/XGGLogisticsCell.h
  文件名: XGGLogisticsCell
  类名: @interface LogisticsCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.h
  文件名: XGGWaitSendGoodsVC
  类名: @interface WaitSendGoodsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/小店详情/XGGShopInfoVC.h
  文件名: XGGShopInfoVC
  类名: @interface ShopInfoVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/公共页面/XGGRefundHeadView.h
  文件名: XGGRefundHeadView
  类名: @interface RefundHeadView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/公共页面/XGGSellOrderPublicView.h
  文件名: XGGSellOrderPublicView
  类名: @interface SellOrderPublicView : UIView
  类型: @interface声明

不一致: YBVideo/店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.h
  文件名: XGGAddOtherSaleGoodsVC
  类名: @interface AddOtherSaleGoodsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/缓存/权限(tab中间按钮)/XGGPower.h
  文件名: XGGPower
  类名: @interface XGGPower : NSObject
  类型: @interface声明

不一致: YBVideo/缓存/定位/XGGRKLBSManager.h
  文件名: XGGRKLBSManager
  类名: @interface RKLBSManager : NSObject
  类型: @interface声明

不一致: YBVideo/缓存/定位/XGGcityDefault.h
  文件名: XGGcityDefault
  类名: @interface XGGcityDefault : NSObject
  类型: @interface声明

不一致: YBVideo/缓存/配置信息/XGGcommon.h
  文件名: XGGcommon
  类名: @interface common : NSObject
  类型: @interface声明

不一致: YBVideo/缓存/个人信息/XGGConfig.h
  文件名: XGGConfig
  类名: @interface Config : NSObject
  类型: @interface声明

不一致: YBVideo/底部导航/XGGYBTabBarController.h
  文件名: XGGYBTabBarController
  类名: @interface YBTabBarController : UITabBarController
  类型: @interface声明

不一致: YBVideo/底部导航/直播or视频/XGGYBLiveOrVideo.h
  文件名: XGGYBLiveOrVideo
  类名: @interface YBLiveOrVideo : UIView
  类型: @interface声明

不一致: YBVideo/底部导航/XGGYBTabBar.h
  文件名: XGGYBTabBar
  类名: @interface YBTabBar : UITabBar
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQPolygon.h
  文件名: XGGQPolygon
  类名: @interface QPolygon : QMultiPoint <QOverlay>
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQOverlayPathView.h
  文件名: XGGQOverlayPathView
  类名: @interface QOverlayPathView : QOverlayView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQCircle.h
  文件名: XGGQCircle
  类名: @interface QCircle : QShape <QOverlay>
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQUserLocation.h
  文件名: XGGQUserLocation
  类名: @interface QUserLocation : NSObject <QAnnotation>
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQPolyline.h
  文件名: XGGQPolyline
  类名: @interface QPolyline : QMultiPoint <QOverlay>
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQPointAnnotation.h
  文件名: XGGQPointAnnotation
  类名: @interface QPointAnnotation : QShape
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQCircleView.h
  文件名: XGGQCircleView
  类名: @interface QCircleView : QOverlayPathView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQStyledPolylineView.h
  文件名: XGGQStyledPolylineView
  类名: @interface QStyledPolylineView : QPolylineView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQPolylineView.h
  文件名: XGGQPolylineView
  类名: @interface QPolylineView : QOverlayPathView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQMultiPoint.h
  文件名: XGGQMultiPoint
  类名: @interface QMultiPoint : QShape{
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/QMapServices.h
  文件名: QMapServices
  类名: @interface QMapServices : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQOverlayView.h
  文件名: XGGQOverlayView
  类名: @interface QOverlayView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/QMapView.h
  文件名: QMapView
  类名: @interface QMapView : UIView <NSCoding>
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQPinAnnotationView.h
  文件名: XGGQPinAnnotationView
  类名: @interface QPinAnnotationView : QAnnotationView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQAnnotationView.h
  文件名: XGGQAnnotationView
  类名: @interface QAnnotationView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQShape.h
  文件名: XGGQShape
  类名: @interface QShape : NSObject <QAnnotation> {
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/QGeometry.h
  文件名: QGeometry
  类名: @interface NSValue (NSValueQGeometryExtensions)
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapKit.framework/Versions/1.2.9/Headers/XGGQPolygonView.h
  文件名: XGGQPolygonView
  类名: @interface QPolygonView : QOverlayPathView
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapSearchKit.framework/Versions/1.1.2/Headers/QMSSearchOption.h
  文件名: QMSSearchOption
  类名: @interface QMSSearchOption : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapSearchKit.framework/Versions/1.1.2/Headers/QMSSearchServices.h
  文件名: QMSSearchServices
  类名: @interface QMSSearchServices : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapSearchKit.framework/Versions/1.1.2/Headers/QMSSearchOperation.h
  文件名: QMSSearchOperation
  类名: @interface QMSSearchOperation : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapSearchKit.framework/Versions/1.1.2/Headers/QMSSearcher.h
  文件名: QMSSearcher
  类名: @interface QMSSearcher : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/腾讯地图/QMapSearchKit.framework/Versions/1.1.2/Headers/QMSSearchResult.h
  文件名: QMSSearchResult
  类名: @interface QMSBaseResult : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/Model/MHBeautiesModel.h
  文件名: MHBeautiesModel
  类名: @interface MHBeautiesModel : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/Model/MHFilterModel.h
  文件名: MHFilterModel
  类名: @interface MHFilterModel : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHActionView.h
  文件名: MHActionView
  类名: @interface MHActionView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHCompleteBeautyView.h
  文件名: MHCompleteBeautyView
  类名: @interface MHCompleteBeautyView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautySlider.h
  文件名: MHBeautySlider
  类名: @interface MHBeautySlider : UISlider
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHFiltersView.h
  文件名: MHFiltersView
  类名: @interface MHFiltersView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/WNSegmentControl.h
  文件名: WNSegmentControl
  类名: @interface WNSegmentControl :UIControl
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/WNSegmentItem.h
  文件名: WNSegmentItem
  类名: @interface WNSegmentItem : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyFaceView.h
  文件名: MHBeautyFaceView
  类名: @interface MHBeautyFaceView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHSpecificAssembleView.h
  文件名: MHSpecificAssembleView
  类名: @interface MHSpecificAssembleView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHStickersView.h
  文件名: MHStickersView
  类名: @interface MHStickersView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyMenuCell.h
  文件名: MHBeautyMenuCell
  类名: @interface MHBeautyMenuCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHPrintView.h
  文件名: MHPrintView
  类名: @interface MHPrintView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyView.h
  文件名: MHBeautyView
  类名: @interface MHBeautyView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHMakeUpView.h
  文件名: MHMakeUpView
  类名: @interface MHMakeUpView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHSectionStickersView.h
  文件名: MHSectionStickersView
  类名: @interface MHSectionStickersView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHMagnifiedView.h
  文件名: MHMagnifiedView
  类名: @interface MHMagnifiedView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHSpecificEffectView.h
  文件名: MHSpecificEffectView
  类名: @interface MHSpecificEffectView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBottomView.h
  文件名: MHBottomView
  类名: @interface MHBottomView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyAssembleView.h
  文件名: MHBeautyAssembleView
  类名: @interface MHBeautyAssembleView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHStickerCell.h
  文件名: MHStickerCell
  类名: @interface MHStickerIndicatorView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/MHMeiyanMenusView.h
  文件名: MHMeiyanMenusView
  类名: @interface MHMeiyanMenusView : UIView
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/服务端默认美颜数值缓存/XGGsproutCommon.h
  文件名: XGGsproutCommon
  类名: @interface sproutCommon : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/Framework/MHBeautySDK.framework/Headers/XGGStickerManager.h
  文件名: XGGStickerManager
  类名: @interface StickerManager : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/Framework/MHBeautySDK.framework/Headers/XGGStickerDataListModel.h
  文件名: XGGStickerDataListModel
  类名: @interface StickerDataListModel : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/Framework/MHBeautySDK.framework/Headers/MHBeautyManager.h
  文件名: MHBeautyManager
  类名: @interface MHBeautyManager : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/Framework/MHBeautySDK.framework/Headers/MHSDK.h
  文件名: MHSDK
  类名: @interface MHSDK : NSObject
  类型: @interface声明

不一致: YBVideo/三方_SDK/美狐sdk/Framework/MHBeautySDK.framework/Headers/MHZipArchive.h
  文件名: MHZipArchive
  类名: @interface MHZipArchive : NSObject
  类型: @interface声明

不一致: YBVideo/消息/XGGMessageFansVC.h
  文件名: XGGMessageFansVC
  类名: @interface MessageFansVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/消息/选择联系人/XGGSelPeopleCell.h
  文件名: XGGSelPeopleCell
  类名: @interface SelPeopleCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/消息/选择联系人/XGGSelPeopleV.h
  文件名: XGGSelPeopleV
  类名: @interface SelPeopleV : UIView
  类型: @interface声明

不一致: YBVideo/消息/model/XGGMsgSysModel.h
  文件名: XGGMsgSysModel
  类名: @interface MsgSysModel : NSObject
  类型: @interface声明

不一致: YBVideo/消息/model/XGGMessageListModel.h
  文件名: XGGMessageListModel
  类名: @interface MessageListModel : NSObject
  类型: @interface声明

不一致: YBVideo/消息/model/XGGMessageFansModel.h
  文件名: XGGMessageFansModel
  类名: @interface MessageFansModel : NSObject
  类型: @interface声明

不一致: YBVideo/消息/model/XGGMsgTopPubModel.h
  文件名: XGGMsgTopPubModel
  类名: @interface MsgTopPubModel : NSObject
  类型: @interface声明

不一致: YBVideo/消息/view/XGGMessageFansCell.h
  文件名: XGGMessageFansCell
  类名: @interface MessageFansCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/消息/view/XGGMsgSysCell.h
  文件名: XGGMsgSysCell
  类名: @interface MsgSysCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/消息/view/XGGMessageListCell.h
  文件名: XGGMessageListCell
  类名: @interface MessageListCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/消息/view/XGGMessageHeaderV.h
  文件名: XGGMessageHeaderV
  类名: @interface MessageHeaderV : UIView
  类型: @interface声明

不一致: YBVideo/消息/view/XGGMsgTopPubCell.h
  文件名: XGGMsgTopPubCell
  类名: @interface MsgTopPubCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/消息/XGGOrderMessageVC.h
  文件名: XGGOrderMessageVC
  类名: @interface OrderMessageVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/消息/XGGchatmessageCell.h
  文件名: XGGchatmessageCell
  类名: @interface chatmessageCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/消息/XGGOrderMessageModel.h
  文件名: XGGOrderMessageModel
  类名: @interface OrderMessageModel : NSObject
  类型: @interface声明

不一致: YBVideo/消息/XGGMsgSysVC.h
  文件名: XGGMsgSysVC
  类名: @interface MsgSysVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/消息/XGGMsgTopPubVC.h
  文件名: XGGMsgTopPubVC
  类名: @interface MsgTopPubVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/直播模块/用户端相关/XGGYBPlayVC.h
  文件名: XGGYBPlayVC
  类名: @interface YBPlayVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/直播模块/用户端相关/view/XGGYBPlayCtrlView.h
  文件名: XGGYBPlayCtrlView
  类名: @interface YBPlayCtrlView : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.h
  文件名: XGGYBCheckLiveObj
  类名: @interface YBCheckLiveObj : NSObject
  类型: @interface声明

不一致: YBVideo/直播模块/直播列表/XGGYBLiveListVC.h
  文件名: XGGYBLiveListVC
  类名: @interface YBLiveListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/直播模块/直播列表/view/XGGYBLiveListCell.h
  文件名: XGGYBLiveListCell
  类名: @interface YBLiveListCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/直播模块/房间警告/XGGYBLiveRoomAlertView.h
  文件名: XGGYBLiveRoomAlertView
  类名: @interface YBLiveRoomAlertView : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/RTCPush/XGGYBLiveRTCManager.h
  文件名: XGGYBLiveRTCManager
  类名: @interface YBLiveRTCManager : NSObject
  类型: @interface声明

不一致: YBVideo/直播模块/主播-用户公用/XGGYBLiveEndView.h
  文件名: XGGYBLiveEndView
  类名: @interface YBLiveEndView : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/主播-用户公用/XGGYBChatToolBar.h
  文件名: XGGYBChatToolBar
  类名: @interface YBChatToolBar : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.h
  文件名: XGGroomShowGoodsView
  类名: @interface roomShowGoodsView : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/XGGYBLiveVC.h
  文件名: XGGYBLiveVC
  类名: @interface YBLiveVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/YBLiveFunView.h
  文件名: YBLiveFunView
  类名: @interface YBLiveFucView : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/直播分类/XGGstartLiveClassCell.h
  文件名: XGGstartLiveClassCell
  类名: @interface startLiveClassCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/直播分类/XGGstartLiveClassVC.h
  文件名: XGGstartLiveClassVC
  类名: @interface startLiveClassVC : UIViewController
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/view/XGGYBLiveCtrlView.h
  文件名: XGGYBLiveCtrlView
  类名: @interface YBLiveCtrlView : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/view/XGGYBLivePreview.h
  文件名: XGGYBLivePreview
  类名: @interface YBLivePreview : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/主播端相关/view/XGGYBLiveFucView.h
  文件名: XGGYBLiveFucView
  类名: @interface YBLiveFucView : UIView
  类型: @interface声明

不一致: YBVideo/直播模块/socket/XGGYBSocketPlay.h
  文件名: XGGYBSocketPlay
  类名: @interface YBSocketPlay : NSObject
  类型: @interface声明

不一致: YBVideo/直播模块/socket/XGGYBSocketLive.h
  文件名: XGGYBSocketLive
  类名: @interface YBSocketLive : NSObject
  类型: @interface声明

不一致: YBVideo/功能/会员/XGGYBVipVC.h
  文件名: XGGYBVipVC
  类名: @interface YBVipVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/会员/view/XGGvipBuyView.h
  文件名: XGGvipBuyView
  类名: @interface vipBuyView : UIView
  类型: @interface声明

不一致: YBVideo/功能/会员/view/XGGYBVipCell.h
  文件名: XGGYBVipCell
  类名: @interface YBVipCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/会员/view/XGGYBVipHeader.h
  文件名: XGGYBVipHeader
  类名: @interface YBVipHeader : UIView
  类型: @interface声明

不一致: YBVideo/功能/钱包/XGGYBRechargeVC.h
  文件名: XGGYBRechargeVC
  类名: @interface YBRechargeVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/钱包/支付公共方法/XGGYBRechargeType.h
  文件名: XGGYBRechargeType
  类名: @interface YBRechargeType : NSObject
  类型: @interface声明

不一致: YBVideo/功能/钱包/aliPay/Util/XGGRSADataVerifier.h
  文件名: XGGRSADataVerifier
  类名: @interface RSADataVerifier : NSObject <DataVerifier> {
  类型: @interface声明

不一致: YBVideo/功能/钱包/aliPay/Util/NSDataEx.h
  文件名: NSDataEx
  类名: @interface NSData (NSDataBase64Additions)
  类型: @interface声明

不一致: YBVideo/功能/钱包/aliPay/Util/base64.h
  文件名: base64
  类名: @interface Base64 : NSObject
  类型: @interface声明

不一致: YBVideo/功能/钱包/aliPay/Util/XGGRSADataSigner.h
  文件名: XGGRSADataSigner
  类名: @interface RSADataSigner : NSObject <DataSigner> {
  类型: @interface声明

不一致: YBVideo/功能/钱包/aliPay/Util/XGGMD5DataSigner.h
  文件名: XGGMD5DataSigner
  类名: @interface MD5DataSigner : NSObject <DataSigner> {
  类型: @interface声明

不一致: YBVideo/功能/钱包/aliPay/XGGOrder.h
  文件名: XGGOrder
  类名: @interface Order : NSObject
  类型: @interface声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGfansModel.h
  文件名: XGGfansModel
  类名: @interface fansModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGBlackListVC.h
  文件名: XGGBlackListVC
  类名: @interface BlackListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGfans.h
  文件名: XGGfans
  类名: @interface fans : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/粉丝_关注_拉黑/blackListCell.h
  文件名: blackListCell
  类名: @interface BlackListCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGfansViewController.h
  文件名: XGGfansViewController
  类名: @interface fansViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGattrViewController.h
  文件名: XGGattrViewController
  类名: @interface attrViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/JCHATConversationViewController.h
  文件名: JCHATConversationViewController
  类名: @interface JCHATConversationViewController : YBBaseViewController <
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/Model/JCHATRawAudioDataPlayer.h
  文件名: JCHATRawAudioDataPlayer
  类名: @interface JCHATRawAudioDataPlayer : NSObject {
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/Model/JCHATChatModel.h
  文件名: JCHATChatModel
  类名: @interface JCHATChatModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATAudioPlayerHelper.h
  文件名: JCHATAudioPlayerHelper
  类名: @interface JCHATAudioPlayerHelper : NSObject <AVAudioPlayerDelegate>
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMessageTableView.h
  文件名: JCHATMessageTableView
  类名: @interface JCHATMessageTableView : UITableView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMoreView.h
  文件名: JCHATMoreView
  类名: @interface JCHATMoreView : UIView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMessageTextView.h
  文件名: JCHATMessageTextView
  类名: @interface JCHATMessageTextView : UITextView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATToolBar.h
  文件名: JCHATToolBar
  类名: @interface JCHATToolBar : UIView<UITextViewDelegate>
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/CellView/JCHATShowTimeCell.h
  文件名: JCHATShowTimeCell
  类名: @interface JCHATShowTimeCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.h
  文件名: JCHATMessageTableViewCell
  类名: @interface JCHATMessageTableViewCell : UITableViewCell <XHAudioPlayerHelperDelegate,
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/CellView/JCHATLoadMessageTableViewCell.h
  文件名: JCHATLoadMessageTableViewCell
  类名: @interface JCHATLoadMessageTableViewCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMessageContentView.h
  文件名: JCHATMessageContentView
  类名: @interface JCHATMessageContentView :UIImageView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/RecordView/XHVoiceCommonHelper.h
  文件名: XHVoiceCommonHelper
  类名: @interface XHVoiceCommonHelper : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/RecordView/XHVoiceRecordHelper.h
  文件名: XHVoiceRecordHelper
  类名: @interface XHVoiceRecordHelper : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/RecordView/XHVoiceRecordHUD.h
  文件名: XHVoiceRecordHUD
  类名: @interface XHVoiceRecordHUD : UIView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATRecordAnimationView.h
  文件名: JCHATRecordAnimationView
  类名: @interface JCHATRecordAnimationView : UIView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/发送位置/XGGLocationCell.h
  文件名: XGGLocationCell
  类名: @interface LocationCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/极光消息/发送位置/XGGSearchResultView.h
  文件名: XGGSearchResultView
  类名: @interface SearchResultView : UIView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/发送位置/XGGTencentLocationVC.h
  文件名: XGGTencentLocationVC
  类名: @interface TencentLocationVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/极光消息/Utils/XGGViewUtil.h
  文件名: XGGViewUtil
  类名: @interface ViewUtil : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/Common/JCHATTimeOutManager.h
  文件名: JCHATTimeOutManager
  类名: @interface JCHATTimeOutManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/Common/JCHATStringUtils.h
  文件名: JCHATStringUtils
  类名: @interface JCHATStringUtils : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/Common/JCHATSendMsgManager.h
  文件名: JCHATSendMsgManager
  类名: @interface JCHATSendMsgManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/Common/JCHATSendMsgController.h
  文件名: JCHATSendMsgController
  类名: @interface JCHATSendMsgController : NSObject<JMessageDelegate>
  类型: @interface声明

不一致: YBVideo/功能/极光消息/Common/JCHATFileManager.h
  文件名: JCHATFileManager
  类名: @interface JCHATFileManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/Common/JCHATAlertToSendImage.h
  文件名: JCHATAlertToSendImage
  类名: @interface JCHATAlertToSendImage : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/发送表情/XGGtwEmojiView.h
  文件名: XGGtwEmojiView
  类名: @interface twEmojiView : UIView<UICollectionViewDelegate,UICollectionViewDataSource>
  类型: @interface声明

不一致: YBVideo/功能/极光消息/发送表情/LWLCollectionViewHorizontalLayout.h
  文件名: LWLCollectionViewHorizontalLayout
  类名: @interface LWLCollectionViewHorizontalLayout : UICollectionViewFlowLayout
  类型: @interface声明

不一致: YBVideo/功能/极光消息/发送表情/XGGemojiCell.h
  文件名: XGGemojiCell
  类名: @interface emojiCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoToolbar.h
  文件名: MJPhotoToolbar
  类名: @interface MJPhotoToolbar : UIView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoProgressView.h
  文件名: MJPhotoProgressView
  类名: @interface MJPhotoProgressView : UIView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoBrowser.h
  文件名: MJPhotoBrowser
  类名: @interface MJPhotoBrowser : UIViewController <UIScrollViewDelegate>
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoLoadingView.h
  文件名: MJPhotoLoadingView
  类名: @interface MJPhotoLoadingView : UIView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoView.h
  文件名: MJPhotoView
  类名: @interface MJPhotoView : UIScrollView <UIScrollViewDelegate>
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhoto.h
  文件名: MJPhoto
  类名: @interface MJPhoto : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/ChatImageBubble/XGGChatBubbleLayer.h
  文件名: XGGChatBubbleLayer
  类名: @interface ChatBubbleLayer : CALayer
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/ChatImageBubble/XGGChatImageBubble.h
  文件名: XGGChatImageBubble
  类名: @interface ChatImageBubble : UIImageView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATAlbumViewController.h
  文件名: JCHATAlbumViewController
  类名: @interface JCHATAlbumViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoBrowserViewController.h
  文件名: JCHATPhotoBrowserViewController
  类名: @interface JCHATPhotoBrowserViewController : UIViewController
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoPickerViewController.h
  文件名: JCHATPhotoPickerViewController
  类名: @interface JCHATPhotoPickerViewController : UINavigationController
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoSelectViewController.h
  文件名: JCHATPhotoSelectViewController
  类名: @interface JCHATPhotoSelectViewController : YBBaseViewController<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Model/JCHATAlbumModel.h
  文件名: JCHATAlbumModel
  类名: @interface JCHATAlbumModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Model/JCHATPhotoModel.h
  文件名: JCHATPhotoModel
  类名: @interface JCHATPhotoModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/JCHATSelectImgCollectionView.h
  文件名: JCHATSelectImgCollectionView
  类名: @interface JCHATSelectImgCollectionView : UICollectionView
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/XGGThumbImageCollectionViewCell.h
  文件名: XGGThumbImageCollectionViewCell
  类名: @interface ThumbImageCollectionViewCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/JCHATAlbumTableViewCell.h
  文件名: JCHATAlbumTableViewCell
  类名: @interface JCHATAlbumTableViewCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/JCHATPhotoBrowserCollectionViewCell.h
  文件名: JCHATPhotoBrowserCollectionViewCell
  类名: @interface JCHATPhotoBrowserCollectionViewCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/JCHATAlertViewWait.h
  文件名: JCHATAlertViewWait
  类名: @interface JCHATAlertViewWait : NSObject
  类型: @interface声明

不一致: YBVideo/功能/极光消息/External/JCHATCustomFormatter.h
  文件名: JCHATCustomFormatter
  类名: @interface JCHATCustomFormatter : NSObject <DDLogFormatter>
  类型: @interface声明

不一致: YBVideo/功能/H5/XGGPubH5.h
  文件名: XGGPubH5
  类名: @interface PubH5 : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/上热门/XGGaddHotVideoVC.h
  文件名: XGGaddHotVideoVC
  类名: @interface addHotVideoVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/上热门/XGGUpHotCell.h
  文件名: XGGUpHotCell
  类名: @interface UpHotCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/上热门/XGGHotVideoDetailVC.h
  文件名: XGGHotVideoDetailVC
  类名: @interface HotVideoDetailVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/登录奖励/XGGLoginbonus.h
  文件名: XGGLoginbonus
  类名: @interface Loginbonus : UIView
  类型: @interface声明

不一致: YBVideo/功能/登录奖励/XGGLogFirstCell.h
  文件名: XGGLogFirstCell
  类名: @interface LogFirstCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/登录奖励/XGGLogFirstCell2.h
  文件名: XGGLogFirstCell2
  类名: @interface LogFirstCell2 : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/顶部导航搜索/HMSegmentedControl/HMSegmentedControl.h
  文件名: HMSegmentedControl
  类名: @interface HMSegmentedControl : UIControl
  类型: @interface声明

不一致: YBVideo/功能/顶部导航搜索/XGGsearchVC.h
  文件名: XGGsearchVC
  类名: @interface searchVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/顶部导航搜索/view/XGGSearchHistoryCell.h
  文件名: XGGSearchHistoryCell
  类名: @interface SearchHistoryCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.h
  文件名: XGGYBSearchBarView
  类名: @interface YBSearchBarView : UIView
  类型: @interface声明

不一致: YBVideo/功能/顶部导航搜索/HXSearchBar.h
  文件名: HXSearchBar
  类名: @interface HXSearchBar : UISearchBar
  类型: @interface声明

不一致: YBVideo/功能/存储功能类/XGGStorageConfig.h
  文件名: XGGStorageConfig
  类名: @interface StorageConfig : NSObject
  类型: @interface声明

不一致: YBVideo/功能/存储功能类/XGGYBStorageObj.h
  文件名: XGGYBStorageObj
  类名: @interface YBStorageObj : NSObject
  类型: @interface声明

不一致: YBVideo/功能/评论/XGGdetailmodel.h
  文件名: XGGdetailmodel
  类名: @interface detailmodel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/评论/XGGcommentModel.h
  文件名: XGGcommentModel
  类名: @interface commentModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/评论/XGGcommDetailCell.h
  文件名: XGGcommDetailCell
  类名: @interface commDetailCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/评论/XGGcommentview.h
  文件名: XGGcommentview
  类名: @interface commentview : UIView
  类型: @interface声明

不一致: YBVideo/功能/评论/评论工具栏/XGGYBCommentToolBar.h
  文件名: XGGYBCommentToolBar
  类名: @interface YBCommentToolBar : UIView
  类型: @interface声明

不一致: YBVideo/功能/评论/XGGcommCell.h
  文件名: XGGcommCell
  类名: @interface commCell : UITableViewCell<UITableViewDelegate,UITableViewDataSource>
  类型: @interface声明

不一致: YBVideo/功能/青少年/vc/XGGYBYoungModeVC.h
  文件名: XGGYBYoungModeVC
  类名: @interface YBYoungModeVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/青少年/vc/XGGYBYoungSetVC.h
  文件名: XGGYBYoungSetVC
  类名: @interface YBYoungSetVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/青少年/vc/密码/XGGRKCodeView.h
  文件名: XGGRKCodeView
  类名: @interface RKCodeView : UIView
  类型: @interface声明

不一致: YBVideo/功能/青少年/vc/密码/XGGRKCodeInputView.h
  文件名: XGGRKCodeInputView
  类名: @interface RKCodeInputView : UIView
  类型: @interface声明

不一致: YBVideo/功能/青少年/vc/XGGYBYoungModifyVC.h
  文件名: XGGYBYoungModifyVC
  类名: @interface YBYoungModifyVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/青少年/小窗/XGGYBYoungSmall.h
  文件名: XGGYBYoungSmall
  类名: @interface YBYoungSmall : UIView
  类型: @interface声明

不一致: YBVideo/功能/青少年/XGGYBYoungManager.h
  文件名: XGGYBYoungManager
  类名: @interface YBYoungManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/我的名片/XGGBusinessCardVC.h
  文件名: XGGBusinessCardVC
  类名: @interface BusinessCardVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/KeepLive/XGGRKKeepAlive.h
  文件名: XGGRKKeepAlive
  类名: @interface RKKeepAlive : NSObject
  类型: @interface声明

不一致: YBVideo/功能/广告管理/XGGMyAdvertCell.h
  文件名: XGGMyAdvertCell
  类名: @interface MyAdvertCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/广告管理/XGGAdvertManagerVC.h
  文件名: XGGAdvertManagerVC
  类名: @interface AdvertManagerVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/广告管理/XGGMyAdvertVC.h
  文件名: XGGMyAdvertVC
  类名: @interface MyAdvertVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/观看商品/XGGlookVGoodsDView.h
  文件名: XGGlookVGoodsDView
  类名: @interface lookVGoodsDView : UIView
  类型: @interface声明

不一致: YBVideo/功能/邀请码/XGGYBInvitationView.h
  文件名: XGGYBInvitationView
  类名: @interface YBInvitationView : UIView
  类型: @interface声明

不一致: YBVideo/功能/邀请码/XGGYBInviteCode.h
  文件名: XGGYBInviteCode
  类名: @interface YBInviteCode : NSObject
  类型: @interface声明

不一致: YBVideo/功能/举报(直播间+看视频)/XGGYBVideoReportVC.h
  文件名: XGGYBVideoReportVC
  类名: @interface YBVideoReportVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/举报(直播间+看视频)/view/XGGYBReportCell.h
  文件名: XGGYBReportCell
  类名: @interface YBReportCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/举报(直播间+看视频)/XGGYBLiveReportVC.h
  文件名: XGGYBLiveReportVC
  类名: @interface YBLiveReportVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/语言包/XGGYBLanguageTools.h
  文件名: XGGYBLanguageTools
  类名: @interface YBLanguageTools : NSObject
  类型: @interface声明

不一致: YBVideo/功能/拍摄同款/XGGYBTakeSameVideoVC.h
  文件名: XGGYBTakeSameVideoVC
  类名: @interface YBTakeSameVideoVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/注销账号/XGGYBDestroySureVC.h
  文件名: XGGYBDestroySureVC
  类名: @interface YBDestroySureVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/注销账号/view/XGGYBDestroyCell.h
  文件名: XGGYBDestroyCell
  类名: @interface YBDestroyCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/注销账号/XGGYBDestroyAccount.h
  文件名: XGGYBDestroyAccount
  类名: @interface YBDestroyAccount : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/XGGYBMsgC2CListVC.h
  文件名: XGGYBMsgC2CListVC
  类名: @interface YBMsgC2CListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/翻译api/GDYTranslateTool.h
  文件名: GDYTranslateTool
  类名: @interface GDYTranslateTool : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/XGGYBMessageManager.h
  文件名: XGGYBMessageManager
  类名: @interface YBMessageManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/受限提示/GDYLimitAlert.h
  文件名: GDYLimitAlert
  类名: @interface GDYLimitAlert : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/commom/XGGTUnReadView.h
  文件名: XGGTUnReadView
  类名: @interface TUnReadView : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/commom/XGGTResponderTextView.h
  文件名: XGGTResponderTextView
  类名: @interface TResponderTextView : UITextView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/commom/XGGTHelper.h
  文件名: XGGTHelper
  类名: @interface THelper : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.h
  文件名: XGGTMessageController
  类名: @interface TMessageController : UITableViewController
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.h
  文件名: XGGTGoodsCell
  类名: @interface TGoodsCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTSystemMessageCell.h
  文件名: XGGTSystemMessageCell
  类名: @interface TSystemMessageCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTVideoMessageCell.h
  文件名: XGGTVideoMessageCell
  类名: @interface TVideoItem : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/TCallCell.h
  文件名: TCallCell
  类名: @interface TCallCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMenuView.h
  文件名: XGGTMenuView
  类名: @interface TMenuView : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGiftMessageCell.h
  文件名: XGGTGiftMessageCell
  类名: @interface TGiftMessageCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.h
  文件名: XGGTLocationCell
  类名: @interface TLocationCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTRecordView.h
  文件名: XGGTRecordView
  类名: @interface TRecordView : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTImageMessageCell.h
  文件名: XGGTImageMessageCell
  类名: @interface TImageItem : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceMessageCell.h
  文件名: XGGTFaceMessageCell
  类名: @interface TFaceMessageCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTTextView.h
  文件名: XGGTTextView
  类名: @interface TTextView : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMenuCell.h
  文件名: XGGTMenuCell
  类名: @interface TMenuCellData : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceView.h
  文件名: XGGTFaceView
  类名: @interface TFaceGroup : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMessageCell.h
  文件名: XGGTMessageCell
  类名: @interface TMessageCellData : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceCell.h
  文件名: XGGTFaceCell
  类名: @interface TFaceCellData : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/TChatAlertView.h
  文件名: TChatAlertView
  类名: @interface TChatAlertView : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTVoiceMessageCell.h
  文件名: XGGTVoiceMessageCell
  类名: @interface TVoiceMessageCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTTextMessageCell.h
  文件名: XGGTTextMessageCell
  类名: @interface TTextMessageCellData : TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMoreCell.h
  文件名: XGGTMoreCell
  类名: @interface TMoreCellData : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMoreView.h
  文件名: XGGTMoreView
  类名: @interface TMoreView : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFileMessageCell.h
  文件名: XGGTFileMessageCell
  类名: @interface TFileMessageCellData: TMessageCellData
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/XGGTInputController.h
  文件名: XGGTInputController
  类名: @interface TInputController : UIViewController
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/消息会话/TChatC2CController.h
  文件名: TChatC2CController
  类名: @interface TChatC2CController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/IM管理/XGGYBImManager.h
  文件名: XGGYBImManager
  类名: @interface YBImManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/IM管理/工具资源/TUIKitConfig.h
  文件名: TUIKitConfig
  类名: @interface TUIKitConfig : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/IM管理/工具资源/third/voiceConvert/XGGEMVoiceConverter.h
  文件名: XGGEMVoiceConverter
  类名: @interface EMVoiceConverter : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.h
  文件名: XGGYBScrollImageView
  类名: @interface YBScrollImageView : UIView
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/IM管理/工具资源/TUIKit.h
  文件名: TUIKit
  类名: @interface TUIKit : NSObject
  类型: @interface声明

不一致: YBVideo/功能/腾讯消息/view/TConversationCell.h
  文件名: TConversationCell
  类名: @interface TConversationCellData : NSObject
  类型: @interface声明

不一致: YBVideo/功能/礼物/page/XGGYBPageControl.h
  文件名: XGGYBPageControl
  类名: @interface YBPageControl : UIPageControl
  类型: @interface声明

不一致: YBVideo/功能/礼物/手绘礼物/XGGRKPaintedGiftView.h
  文件名: XGGRKPaintedGiftView
  类名: @interface RKPaintedGiftView : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/手绘礼物/XGGRKShowPaintedView.h
  文件名: XGGRKShowPaintedView
  类名: @interface RKShowPaintedView : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/礼物特效/XGGexpensiveGiftV.h
  文件名: XGGexpensiveGiftV
  类名: @interface expensiveGiftV : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/礼物特效/XGGcontinueGift.h
  文件名: XGGcontinueGift
  类名: @interface continueGift : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/礼物特效/XGGliansongBackView.h
  文件名: XGGliansongBackView
  类名: @interface liansongBackView : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/礼物特效/XGGexoensiveGifGiftV.h
  文件名: XGGexoensiveGifGiftV
  类名: @interface exoensiveGifGiftV : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/礼物特效/图片/CFGradientLabel.h
  文件名: CFGradientLabel
  类名: @interface CFGradientLabel : UILabel
  类型: @interface声明

不一致: YBVideo/功能/礼物/XGGYBGiftView.h
  文件名: XGGYBGiftView
  类名: @interface YBGiftView : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/model/XGGYBGiftModel.h
  文件名: XGGYBGiftModel
  类名: @interface YBGiftModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/礼物/view/XGGYBGiftCell.h
  文件名: XGGYBGiftCell
  类名: @interface YBGiftCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/礼物/PageBar/TYTabPagerBarCell.h
  文件名: TYTabPagerBarCell
  类名: @interface TYTabPagerBarCell : UICollectionViewCell<TYTabPagerBarCellProtocol>
  类型: @interface声明

不一致: YBVideo/功能/礼物/PageBar/TYTabPagerBar.h
  文件名: TYTabPagerBar
  类名: @interface TYTabPagerBar : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/PageBar/TYPagerViewLayout.h
  文件名: TYPagerViewLayout
  类名: @interface NSObject (TY_PagerReuseIdentify)
  类型: @interface声明

不一致: YBVideo/功能/礼物/PageBar/TYPagerView.h
  文件名: TYPagerView
  类名: @interface TYPagerView : UIView
  类型: @interface声明

不一致: YBVideo/功能/礼物/PageBar/TYTabPagerBarLayout.h
  文件名: TYTabPagerBarLayout
  类名: @interface TYTabPagerBarLayout : NSObject
  类型: @interface声明

不一致: YBVideo/功能/礼物/XGGYBGiftPage.h
  文件名: XGGYBGiftPage
  类名: @interface YBGiftPage : UIView
  类型: @interface声明

不一致: YBVideo/功能/标签全部视频/XGGtopicVideoCell.h
  文件名: XGGtopicVideoCell
  类名: @interface topicVideoCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/标签全部视频/XGGtopicDetailsVC.h
  文件名: XGGtopicDetailsVC
  类名: @interface topicDetailsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/直播/守护/XGGshouhuView.h
  文件名: XGGshouhuView
  类名: @interface shouhuView : UIView<UIAlertViewDelegate>
  类型: @interface声明

不一致: YBVideo/功能/直播/守护/XGGguardShowView.h
  文件名: XGGguardShowView
  类名: @interface guardShowView : UIView<UITableViewDelegate,UITableViewDataSource>
  类型: @interface声明

不一致: YBVideo/功能/直播/守护/model/XGGguardListModel.h
  文件名: XGGguardListModel
  类名: @interface guardListModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/直播/守护/view/XGGgrardButton.h
  文件名: XGGgrardButton
  类名: @interface grardButton : UIButton
  类型: @interface声明

不一致: YBVideo/功能/直播/守护/view/XGGguardListCell.h
  文件名: XGGguardListCell
  类名: @interface guardListCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/守护/view/XGGguardAlertView.h
  文件名: XGGguardAlertView
  类名: @interface guardAlertView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/PK/XGGYBPkProgressView.h
  文件名: XGGYBPkProgressView
  类名: @interface YBPkProgressView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/PK/XGGYBAnchorPKView.h
  文件名: XGGYBAnchorPKView
  类名: @interface YBAnchorPKView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.h
  文件名: XGGYBAnchorPKAlert
  类名: @interface YBAnchorPKAlert : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.h
  文件名: XGGYBAnchorLinkInfo
  类名: @interface YBAnchorLinkInfo : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.h
  文件名: XGGYBLinkAlertView
  类名: @interface YBLinkAlertView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/主播连麦在线列表/view/anchorCell.h
  文件名: anchorCell
  类名: @interface YBAnchorOnlineCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.h
  文件名: XGGYBAnchorOnlineCell
  类名: @interface YBAnchorOnlineCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.h
  文件名: XGGYBAnchorOnline
  类名: @interface YBAnchorOnline : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.h
  文件名: XGGYBTxLinkMicView
  类名: @interface YBTxLinkMicView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/ActionSheet/CSActionSheet.h
  文件名: CSActionSheet
  类名: @interface CSActionSheet : UIView {
  类型: @interface声明

不一致: YBVideo/功能/直播/ActionSheet/CSActionPicker.h
  文件名: CSActionPicker
  类名: @interface CSActionPicker : UIView {
  类型: @interface声明

不一致: YBVideo/功能/直播/用户列表/model/XGGYBUserListModel.h
  文件名: XGGYBUserListModel
  类名: @interface YBUserListModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/直播/用户列表/view/XGGYBUserListCell.h
  文件名: XGGYBUserListCell
  类名: @interface YBUserListCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/用户列表/XGGYBUserListView.h
  文件名: XGGYBUserListView
  类名: @interface YBUserListView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/管理员列表/XGGadminCell.h
  文件名: XGGadminCell
  类名: @interface adminCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/管理员列表/XGGadminLists.h
  文件名: XGGadminLists
  类名: @interface adminLists : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/直播/每日任务/XGGYBDayTaskManager.h
  文件名: XGGYBDayTaskManager
  类名: @interface YBDayTaskManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/直播/每日任务/个中VC/XGGYBDayTaskVC.h
  文件名: XGGYBDayTaskVC
  类名: @interface YBDayTaskVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/直播/每日任务/直播间内/XGGYBDayTaskView.h
  文件名: XGGYBDayTaskView
  类名: @interface YBDayTaskView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/每日任务/View/XGGYBDayTaskCell.h
  文件名: XGGYBDayTaskCell
  类名: @interface YBDayTaskCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/用户端商品简介/XGGYBGoodsBriefView.h
  文件名: XGGYBGoodsBriefView
  类名: @interface YBGoodsBriefView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/直播间聊天/XGGYBLiveChatView.h
  文件名: XGGYBLiveChatView
  类名: @interface YBLiveChatView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/直播间聊天/model/XGGYBLiveChatModel.h
  文件名: XGGYBLiveChatModel
  类名: @interface YBLiveChatModel : NSObject
  类型: @interface声明

不一致: YBVideo/功能/直播/直播间聊天/view/XGGYBLiveChatCell.h
  文件名: XGGYBLiveChatCell
  类名: @interface YBLiveChatCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/在售商品/view/XGGYBOnSaleCell.h
  文件名: XGGYBOnSaleCell
  类名: @interface YBOnSaleCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/在售商品/XGGYBOnSaleView.h
  文件名: XGGYBOnSaleView
  类名: @interface YBOnSaleView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.h
  文件名: XGGuserLevelView
  类名: @interface userLevelView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.h
  文件名: XGGUserBulletWindow
  类名: @interface UserBulletWindow : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/幸运转盘/XGGturntableResultView.h
  文件名: XGGturntableResultView
  类名: @interface turntableResultView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/幸运转盘/view/XGGturntableResultCell.h
  文件名: XGGturntableResultCell
  类名: @interface turntableResultCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/幸运转盘/view/XGGturntableRecordCell.h
  文件名: XGGturntableRecordCell
  类名: @interface turntableRecordCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/直播/幸运转盘/XGGturntableView.h
  文件名: XGGturntableView
  类名: @interface turntableView : UIView<CAAnimationDelegate>
  类型: @interface声明

不一致: YBVideo/功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.h
  文件名: XGGturntableRuleView
  类名: @interface turntableRuleView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/幸运转盘/记录-规则/XGGturntableRecordView.h
  文件名: XGGturntableRecordView
  类名: @interface turntableRecordView : UIView<UITableViewDelegate,UITableViewDataSource>
  类型: @interface声明

不一致: YBVideo/功能/直播/进房间动画/XGGYBUserEnterAnimation.h
  文件名: XGGYBUserEnterAnimation
  类名: @interface YBUserEnterAnimation : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播/直播间私信/XGGhuanxinsixinview.h
  文件名: XGGhuanxinsixinview
  类名: @interface huanxinsixinview : UIViewController <UITableViewDataSource,UITableViewDelegate> {
  类型: @interface声明

不一致: YBVideo/功能/直播/直播间私信/XGGYBImRoomSmallView.h
  文件名: XGGYBImRoomSmallView
  类名: @interface YBImRoomSmallView : UIView
  类型: @interface声明

不一致: YBVideo/功能/直播榜单/XGGLiveRankVC.h
  文件名: XGGLiveRankVC
  类名: @interface LiveRankVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/功能/直播榜单/XGGLiveRankCell.h
  文件名: XGGLiveRankCell
  类名: @interface LiveRankCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGDetailInputTableViewCell.h
  文件名: XGGDetailInputTableViewCell
  类名: @interface DetailInputTableViewCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGDetailTableViewCell.h
  文件名: XGGDetailTableViewCell
  类名: @interface DetailTableViewCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/Settings/BDVRSettings.h
  文件名: BDVRSettings
  类名: @interface BDVRSettings : NSObject
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGSettingsTableViewCell.h
  文件名: XGGSettingsTableViewCell
  类名: @interface SettingsTableViewCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/Settings/BDVRSettingsItem.h
  文件名: BDVRSettingsItem
  类名: @interface BDVRSettingsItem : NSObject
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGSettingsTableViewController.h
  文件名: XGGSettingsTableViewController
  类名: @interface SettingsTableViewController : UITableViewController
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGDetailTableViewController.h
  文件名: XGGDetailTableViewController
  类名: @interface DetailTableViewController : UITableViewController
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/XGGASRView.h
  文件名: XGGASRView
  类名: @interface ASRView : UIView
  类型: @interface声明

不一致: YBVideo/功能/百度语音/VC/XGGAudioInputStream.h
  文件名: XGGAudioInputStream
  类名: @interface AudioInputStream : NSInputStream
  类型: @interface声明

不一致: YBVideo/功能/百度语音/资源/ASRHeader/BDSEventManager.h
  文件名: BDSEventManager
  类名: @interface BDSEventManager : NSObject
  类型: @interface声明

不一致: YBVideo/功能/百度语音/资源/ASRHeader/UIHeaders/BDRecognizerViewParamsObject.h
  文件名: BDRecognizerViewParamsObject
  类名: @interface BDRecognizerViewParamsObject : NSObject
  类型: @interface声明

不一致: YBVideo/功能/百度语音/资源/ASRHeader/UIHeaders/BDRecognizerViewController.h
  文件名: BDRecognizerViewController
  类名: @interface BDRecognizerViewController : UIViewController<BDSClientASRDelegate, BDRecognizerDialogDelegate, AVAudioPlayerDelegate>
  类型: @interface声明

不一致: YBVideo/功能/百度语音/资源/ASRHeader/UIHeaders/BDTheme.h
  文件名: BDTheme
  类名: @interface BDTheme : NSObject <NSCoding>
  类型: @interface声明

不一致: YBVideo/功能/分享/发布分享/XGGPublishShareV.h
  文件名: XGGPublishShareV
  类名: @interface PublishShareV : UIView
  类型: @interface声明

不一致: YBVideo/功能/分享/观看分享/XGGYBShareView.h
  文件名: XGGYBShareView
  类名: @interface YBShareView : UIView
  类型: @interface声明

不一致: YBVideo/功能/分享/观看分享/XGGYBShareViewCell.h
  文件名: XGGYBShareViewCell
  类名: @interface YBShareViewCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/附近/XGGNearbyVC.h
  文件名: XGGNearbyVC
  类名: @interface NearbyVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/附近/城市选择/XGGYBCitySelVC.h
  文件名: XGGYBCitySelVC
  类名: @interface YBCitySelVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/附近/城市选择/view/XGGYBCitySelCell.h
  文件名: XGGYBCitySelCell
  类名: @interface YBCitySelCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/附近/view/XGGNearbyCell.h
  文件名: XGGNearbyCell
  类名: @interface NearbyCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/商品记录/XGGcommodityRecordsVC.h
  文件名: XGGcommodityRecordsVC
  类名: @interface commodityRecordsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/商品记录/XGGcommodityRecordsCell.h
  文件名: XGGcommodityRecordsCell
  类名: @interface commodityRecordsCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/红包收益/XGGYBRedProfitVC.h
  文件名: XGGYBRedProfitVC
  类名: @interface YBRedProfitVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/profitTypeVC.h
  文件名: profitTypeVC
  类名: @interface YBGetTypeListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/addTypeView.h
  文件名: addTypeView
  类名: @interface YBAddTypeView : UIView
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/XGGYBGetTypeListVC.h
  文件名: XGGYBGetTypeListVC
  类名: @interface YBGetTypeListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/XGGYBGetProVC.h
  文件名: XGGYBGetProVC
  类名: @interface YBGetProVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/profitTypeCell.h
  文件名: profitTypeCell
  类名: @interface YBGetTypeListCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/view/XGGYBAddTypeView.h
  文件名: XGGYBAddTypeView
  类名: @interface YBAddTypeView : UIView
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.h
  文件名: XGGYBGetTypeListCell
  类名: @interface YBGetTypeListCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/我的收益/view/WLCardNoFormatter.h
  文件名: WLCardNoFormatter
  类名: @interface WLCardNoFormatter : NSObject
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.h
  文件名: XGGYBGoodsLikeVC
  类名: @interface YBGoodsLikeVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.h
  文件名: XGGYBGoodsLikeCell
  类名: @interface YBGoodsLikeCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.h
  文件名: XGGYBApplyStoreVC
  类名: @interface YBApplyStoreVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.h
  文件名: XGGYBApplyConditionVC
  类名: @interface YBApplyConditionVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.h
  文件名: XGGYBApplyConditionCell
  类名: @interface YBApplyConditionCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGRoomUserListViewController.h
  文件名: XGGRoomUserListViewController
  类名: @interface RoomUserListViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGRoomManagementVC.h
  文件名: XGGRoomManagementVC
  类名: @interface RoomManagementVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGRoomUserTypeCell.h
  文件名: XGGRoomUserTypeCell
  类名: @interface RoomUserTypeCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGOtherRoomViewController.h
  文件名: XGGOtherRoomViewController
  类名: @interface OtherRoomViewController : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.h
  文件名: XGGWatchRecordListCell
  类名: @interface WatchRecordListCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/观看记录/XGGwatchingRecordsVC.h
  文件名: XGGwatchingRecordsVC
  类名: @interface watchingRecordsVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/明细/XGGaccountDetails.h
  文件名: XGGaccountDetails
  类名: @interface accountDetails : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.h
  文件名: XGGYBGoodsInfoVC
  类名: @interface YBGoodsInfoVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.h
  文件名: XGGYBGoodsListVC
  类名: @interface YBGoodsListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.h
  文件名: XGGYBGoodsListCell
  类名: @interface YBGoodsListCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/XGGYBCenterMoreView.h
  文件名: XGGYBCenterMoreView
  类名: @interface YBCenterMoreView : UIView
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/view/XGGYBCenterMoreCell.h
  文件名: XGGYBCenterMoreCell
  类名: @interface YBCenterMoreCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/投放账户--/XGGorderVideoCell.h
  文件名: XGGorderVideoCell
  类名: @interface orderVideoCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/自己更多/投放账户--/XGGdepositAccountVC.h
  文件名: XGGdepositAccountVC
  类名: @interface depositAccountVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/他人更多/XGGYBOtherCenterMore.h
  文件名: XGGYBOtherCenterMore
  类名: @interface YBOtherCenterMore : UIView
  类型: @interface声明

不一致: YBVideo/个人中心/设置/XGGSetViewControllor.h
  文件名: XGGSetViewControllor
  类名: @interface SetViewControllor : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/设置/view/XGGSetLogoutCell.h
  文件名: XGGSetLogoutCell
  类名: @interface SetLogoutCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/设置/view/XGGSetCell.h
  文件名: XGGSetCell
  类名: @interface SetCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/设置/用户认证/XGGYBUserAuthVC.h
  文件名: XGGYBUserAuthVC
  类名: @interface YBUserAuthVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/设置/setView.h
  文件名: setView
  类名: @interface SetViewControllor : UIViewController
  类型: @interface声明

不一致: YBVideo/个人中心/设置/隐私政策/XGGYBPrivateVC.h
  文件名: XGGYBPrivateVC
  类名: @interface YBPrivateVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/设置/userItemCell5.h
  文件名: userItemCell5
  类名: @interface SetLogoutCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/头部/XGGHeaderBackImgView.h
  文件名: XGGHeaderBackImgView
  类名: @interface HeaderBackImgView : UIView
  类型: @interface声明

不一致: YBVideo/个人中心/头部/XGGYBCenterTopView.h
  文件名: XGGYBCenterTopView
  类名: @interface YBCenterTopView : UIView
  类型: @interface声明

不一致: YBVideo/个人中心/作品_喜欢_收藏/XGGCenterListCell.h
  文件名: XGGCenterListCell
  类名: @interface CenterListCell : UICollectionViewCell
  类型: @interface声明

不一致: YBVideo/个人中心/作品_喜欢_收藏/XGGCenterListVC.h
  文件名: XGGCenterListVC
  类名: @interface CenterListVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/XGGYBCenterVC.h
  文件名: XGGYBCenterVC
  类名: @interface YBCenterVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/编辑资料/XGGEditHeader.h
  文件名: XGGEditHeader
  类名: @interface EditHeader : UIView
  类型: @interface声明

不一致: YBVideo/个人中心/编辑资料/XGGEditVC.h
  文件名: XGGEditVC
  类名: @interface EditVC : YBBaseViewController
  类型: @interface声明

不一致: YBVideo/个人中心/编辑资料/XGGEditCell.h
  文件名: XGGEditCell
  类名: @interface EditCell : UITableViewCell
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGUtils.h
  文件名: XGGUtils
  类名: @interface Utils : NSObject
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGmylabels.h
  文件名: XGGmylabels
  类名: @interface mylabels : UILabel
  类型: @interface声明

不一致: YBVideo/工具和自定义类/跑马灯/XGGRKLampView.h
  文件名: XGGRKLampView
  类名: @interface RKLampView : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGRKHorPickerView.h
  文件名: XGGRKHorPickerView
  类名: @interface RKHorPickerView : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/发布进度/XGGYBUploadProgress.h
  文件名: XGGYBUploadProgress
  类名: @interface YBUploadProgress : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/发布进度/XGGRKCircularProgress.h
  文件名: XGGRKCircularProgress
  类名: @interface RKCircularProgress : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/ZZCircleProgress/ZZCircleProgress.h
  文件名: ZZCircleProgress
  类名: @interface ZZCircleProgress : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/ZZCircleProgress/ZZCountingLabel.h
  文件名: ZZCountingLabel
  类名: @interface ZZCountingLabel : UILabel
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGYBButton.h
  文件名: XGGYBButton
  类名: @interface YBButton : UIButton
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGYBAlertActionSheet.h
  文件名: XGGYBAlertActionSheet
  类名: @interface YBAlertActionSheet : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/赞动画/CCAnimationBtn.h
  文件名: CCAnimationBtn
  类名: @interface CCAnimationBtn : UIButton
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGMyTextView.h
  文件名: XGGMyTextView
  类名: @interface MyTextView : UITextView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XLProgress/XLCircleProgress.h
  文件名: XLCircleProgress
  类名: @interface XLCircleProgress : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XLProgress/XLCircle.h
  文件名: XLCircle
  类名: @interface XLCircle : UIView
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Categories/ZFModalTransitionAnimator.h
  文件名: ZFModalTransitionAnimator
  类名: @interface ZFDetectScrollViewEndGestureRecognizer : UIPanGestureRecognizer
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGYBSegControl.h
  文件名: XGGYBSegControl
  类名: @interface YBSegControl : UISegmentedControl
  类型: @interface声明

不一致: YBVideo/工具和自定义类/XGGMyTextField.h
  文件名: XGGMyTextField
  类名: @interface MyTextField : UITextField
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/V8HorizontalPickerView/V8HorizontalPickerView.h
  文件名: V8HorizontalPickerView
  类名: @interface V8LabelNode : NSObject
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParserState.h
  文件名: SBJsonStreamParserState
  类名: @interface SBJsonStreamParserState : NSObject
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonUTF8Stream.h
  文件名: SBJsonUTF8Stream
  类名: @interface SBJsonUTF8Stream : NSObject {
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamWriterAccumulator.h
  文件名: SBJsonStreamWriterAccumulator
  类名: @interface SBJsonStreamWriterAccumulator : NSObject <SBJsonStreamWriterDelegate>
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParserAdapter.h
  文件名: SBJsonStreamParserAdapter
  类名: @interface SBJsonStreamParserAdapter : NSObject <SBJsonStreamParserDelegate> {
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamWriter.h
  文件名: SBJsonStreamWriter
  类名: @interface NSObject (SBJSONValue)
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonWriter.h
  文件名: SBJsonWriter
  类名: @interface SBJsonWriter : NSObject
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamWriterState.h
  文件名: SBJsonStreamWriterState
  类名: @interface SBJsonStreamWriterState : NSObject
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonTokeniser.h
  文件名: SBJsonTokeniser
  类名: @interface SBJsonTokeniser : NSObject 
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonParser.h
  文件名: SBJsonParser
  类名: @interface SBJsonParser : NSObject
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParser.h
  文件名: SBJsonStreamParser
  类名: @interface SBJsonStreamParser : NSObject {
  类型: @interface声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParserAccumulator.h
  文件名: SBJsonStreamParserAccumulator
  类名: @interface SBJsonStreamParserAccumulator : NSObject <SBJsonStreamParserAdapterDelegate>
  类型: @interface声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
  文件名: TCVideoEditViewController
  类名: @implementation TCVideoEditViewController {
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
  文件名: TCMusicMixView
  类名: @implementation TCMusicMixView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m
  文件名: XGGVideoColorInfo
  类名: @implementation VideoColorInfo
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.m
  文件名: TCRangeContent
  类名: @implementation TCRangeContentConfig
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
  文件名: TCVideoRangeSlider
  类名: @implementation TCVideoRangeSlider
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoTextFiled.m
  文件名: TCVideoTextFiled
  类名: @implementation TCVideoTextFiled
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoCutView.m
  文件名: TCVideoCutView
  类名: @implementation TCVideoCutView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCTextCollectionCell.m
  文件名: TCTextCollectionCell
  类名: @implementation TCTextCollectionCell
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCTextAddView.m
  文件名: TCTextAddView
  类名: @implementation TCTextAddView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCVideoPreview.m
  文件名: TCVideoPreview
  类名: @implementation TCVideoPreview
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCBottomTabBar.m
  文件名: TCBottomTabBar
  类名: @implementation TCBottomTabBar
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCFilterSettingView.m
  文件名: TCFilterSettingView
  类名: @implementation TCFilterSettingView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
  文件名: XGGEffectSelectView
  类名: @implementation EffectSelectView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.m
  文件名: TCMusicCollectionCell
  类名: @implementation TCMusicInfo
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
  文件名: XGGTimeSelectView
  类名: @implementation TimeSelectView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
  文件名: XGGAlbumVideoVC
  类名: @implementation AlbumVideoVC
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.m
  文件名: XGGAlbumVideoCell
  类名: @implementation AlbumVideoCell
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/Record/views/XGGSpeedView.m
  文件名: XGGSpeedView
  类名: @implementation SpeedView{
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m
  文件名: SpeedView.temp_caseinsensitive_rename
  类名: @implementation SpeedView{
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/Record/views/TXBaseBeautyView.m
  文件名: TXBaseBeautyView
  类名: @implementation TXBaseBeautyView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m
  文件名: XGGVideoRecordProcessView
  类名: @implementation VideoRecordProcessView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/Record/TCVideoRecordViewController.m
  文件名: TCVideoRecordViewController
  类名: @implementation TCVideoRecordViewController
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
  文件名: XGGYBPicTransitionVC
  类名: @implementation YBPicTransitionVC {
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
  文件名: XGGPhotoTransitionToolbar
  类名: @implementation PhotoTransitionToolbar
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/图片转场/view/XGGVerticalButton.m
  文件名: XGGVerticalButton
  类名: @implementation VerticalButton
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/图片转场/view/XGGSmallButton.m
  文件名: XGGSmallButton
  类名: @implementation SmallButton
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/TCVideoPublishController.m
  文件名: TCVideoPublishController
  类名: @implementation TCVideoPublishController {
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m
  文件名: XGGYBPublishCoverVC
  类名: @implementation YBPublishCoverVC
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m
  文件名: XGGYBSetChargeView
  类名: @implementation YBSetChargeView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
  文件名: XGGYBVideoAddGoodsVC
  类名: @implementation YBVideoAddGoodsVC
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
  文件名: XGGvideoTopicVC
  类名: @implementation videoTopicVC
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.m
  文件名: XGGvideoTopicCell
  类名: @implementation videoTopicCell
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
  文件名: XGGYBVideoClassVC
  类名: @implementation YBVideoClassVC
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/音乐/XGGYBVideoMusicView.m
  文件名: XGGYBVideoMusicView
  类名: @implementation YBVideoMusicView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/音乐/XGGMusicClassVC.m
  文件名: XGGMusicClassVC
  类名: @implementation MusicClassVC
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/音乐/model/XGGMusicModel.m
  文件名: XGGMusicModel
  类名: @implementation MusicModel
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
  文件名: XGGMusicHeaderView
  类名: @implementation MusicHeaderView
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m
  文件名: XGGMusicHeaderCell
  类名: @implementation MusicHeaderCell
  类型: @implementation声明

不一致: YBVideo/录制_编辑_发布/音乐/view/XGGMusicCell.m
  文件名: XGGMusicCell
  类名: @implementation MusicCell
  类型: @implementation声明

不一致: YBVideo/其他类/XGGAppDelegate.m
  文件名: XGGAppDelegate
  类名: @implementation AppDelegate
  类型: @implementation声明

不一致: YBVideo/其他类/XGGYBBaseViewController.m
  文件名: XGGYBBaseViewController
  类名: @implementation YBBaseViewController
  类型: @implementation声明

不一致: YBVideo/其他类/XGGYBNavigationController.m
  文件名: XGGYBNavigationController
  类名: @implementation YBNavigationController
  类型: @implementation声明

不一致: YBVideo/其他类/TCBaseAppDelegate.m
  文件名: TCBaseAppDelegate
  类名: @implementation YBBaseAppDelegate
  类型: @implementation声明

不一致: YBVideo/其他类/XGGYBBaseAppDelegate.m
  文件名: XGGYBBaseAppDelegate
  类名: @implementation YBBaseAppDelegate
  类型: @implementation声明

不一致: YBVideo/首页/获取视频详情公用obj/XGGYBHomeRedObj.m
  文件名: XGGYBHomeRedObj
  类名: @implementation YBHomeRedObj
  类型: @implementation声明

不一致: YBVideo/首页/获取视频详情公用obj/XGGYBGetVideoObj.m
  文件名: XGGYBGetVideoObj
  类名: @implementation YBGetVideoObj
  类型: @implementation声明

不一致: YBVideo/首页/XGGYBHomeViewController.m
  文件名: XGGYBHomeViewController
  类名: @implementation YBHomeViewController
  类型: @implementation声明

不一致: YBVideo/首页/推荐/XGGYBLookVideoVC.m
  文件名: XGGYBLookVideoVC
  类名: @implementation YBLookVideoVC
  类型: @implementation声明

不一致: YBVideo/首页/推荐/view/XGGYBVideoControlView.m
  文件名: XGGYBVideoControlView
  类名: @implementation YBVideoControlView
  类型: @implementation声明

不一致: YBVideo/首页/推荐/view/XGGYBLookVideoCell.m
  文件名: XGGYBLookVideoCell
  类名: @implementation YBLookVideoCell
  类型: @implementation声明

不一致: YBVideo/首页/推荐/view/ZFCustomControlView.m
  文件名: ZFCustomControlView
  类名: @implementation ZFCustomControlView
  类型: @implementation声明

不一致: YBVideo/首页/热门-关注-分类/XGGYBVideosVC.m
  文件名: XGGYBVideosVC
  类名: @implementation YBVideosVC
  类型: @implementation声明

不一致: YBVideo/首页/热门-关注-分类/XGGMyFollowViewController.m
  文件名: XGGMyFollowViewController
  类名: @implementation MyFollowViewController
  类型: @implementation声明

不一致: YBVideo/首页/热门-关注-分类/XGGmyVideoV.m
  文件名: XGGmyVideoV
  类名: @implementation myVideoV
  类型: @implementation声明

不一致: YBVideo/首页/热门-关注-分类/models/XGGNearbyVideoModel.m
  文件名: XGGNearbyVideoModel
  类名: @implementation NearbyVideoModel
  类型: @implementation声明

不一致: YBVideo/首页/热门-关注-分类/views/XGGVideoCollectionCell.m
  文件名: XGGVideoCollectionCell
  类名: @implementation VideoCollectionCell
  类型: @implementation声明

不一致: YBVideo/登录注册/隐私提醒文本/XGGRegAlertView.m
  文件名: XGGRegAlertView
  类名: @implementation RegAlertView
  类型: @implementation声明

不一致: YBVideo/登录注册/国家代号/XGGCountryCodeVC.m
  文件名: XGGCountryCodeVC
  类名: @implementation CountryCodeVC
  类型: @implementation声明

不一致: YBVideo/登录注册/XGGDspLoginVC.m
  文件名: XGGDspLoginVC
  类名: @implementation DspLoginVC
  类型: @implementation声明

不一致: YBVideo/XGGNetwork/XGGNetworkManager.m
  文件名: XGGNetworkManager
  类名: @implementation XGGNetworkManager
  类型: @implementation声明

不一致: YBVideo/XGGNetwork/XGGNetworkUtils.m
  文件名: XGGNetworkUtils
  类名: @implementation XGGNetworkUtils
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGiOSNetworking.m
  文件名: XGGiOSNetworking
  类名: @implementation iOSNetworking
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGYBShowBigImageView.m
  文件名: XGGYBShowBigImageView
  类名: @implementation YBShowBigImageView
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGYBAlertView.m
  文件名: XGGYBAlertView
  类名: @implementation YBAlertView
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGRKUUIDManager.m
  文件名: XGGRKUUIDManager
  类名: @implementation RKUUIDManager
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGYBImageView.m
  文件名: XGGYBImageView
  类名: @implementation YBImageView{
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGPublicView.m
  文件名: XGGPublicView
  类名: @implementation PublicView
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGRKActionSheet.m
  文件名: XGGRKActionSheet
  类名: @implementation RKSheetBtn
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGYBProgressObj.m
  文件名: XGGYBProgressObj
  类名: @implementation YBProgressObj
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGBGSetting.m
  文件名: XGGBGSetting
  类名: @implementation BGSetting
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGPublicObj.m
  文件名: XGGPublicObj
  类名: @implementation PublicObj
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGYBNetworking.m
  文件名: XGGYBNetworking
  类名: @implementation YBNetworking
  类型: @implementation声明

不一致: YBVideo/公共方法类/XGGRKSysAccess.m
  文件名: XGGRKSysAccess
  类名: @implementation RKSysAccess
  类型: @implementation声明

不一致: YBVideo/引导页/XGGGuideViewController.m
  文件名: XGGGuideViewController
  类名: @implementation GuideViewController
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/退款申请/XGGApplyRefundVC.m
  文件名: XGGApplyRefundVC
  类名: @implementation ApplyRefundVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/经营类目选择/XGGSelectClassVC.m
  文件名: XGGSelectClassVC
  类名: @implementation SelectClassVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/经营类目选择/XGGCommodityClassModel.m
  文件名: XGGCommodityClassModel
  类名: @implementation CommodityClassModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/经营类目选择/XGGCommodityClassCell.m
  文件名: XGGCommodityClassCell
  类名: @implementation CommodityClassCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/确认订单/XGGConfirmOrderVC.m
  文件名: XGGConfirmOrderVC
  类名: @implementation ConfirmOrderVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryVC.m
  文件名: XGGLookHistoryVC
  类名: @implementation LookHistoryVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryModel.m
  文件名: XGGLookHistoryModel
  类名: @implementation LookHistoryModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGLookHistoryCell.m
  文件名: XGGLookHistoryCell
  类名: @implementation LookHistoryCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/浏览记录/XGGHistoryListModel.m
  文件名: XGGHistoryListModel
  类名: @implementation HistoryListModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
  文件名: XGGOutsideHeadCell
  类名: @implementation OutsideHeadCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
  文件名: XGGOutsideGoodsDetailVC
  类名: @implementation OutsideGoodsDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/分享商品/分享给好友/XGGShareFriendCell.m
  文件名: XGGShareFriendCell
  类名: @implementation ShareFriendCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
  文件名: XGGShareFriendVC
  类名: @implementation ShareFriendVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/分享商品/分享给好友/XGGFriendModel.m
  文件名: XGGFriendModel
  类名: @implementation FriendModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/分享商品/XGGShareGoodView.m
  文件名: XGGShareGoodView
  类名: @implementation ShareGoodView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/分享商品/XGGShareGoodsAlert.m
  文件名: XGGShareGoodsAlert
  类名: @implementation ShareGoodsAlert
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/平台介入/XGGPlatformInterventionVC.m
  文件名: XGGPlatformInterventionVC
  类名: @implementation PlatformInterventionVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/评价/CWStar/CWStarRateView.m
  文件名: CWStarRateView
  类名: @implementation CWStarRateView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/评价/XGGPublishEvaluateVC.m
  文件名: XGGPublishEvaluateVC
  类名: @implementation PublishEvaluateVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/经营类目设置/XGGClassificationVC.m
  文件名: XGGClassificationVC
  类名: @implementation ClassificationVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/经营类目设置/XGGClassToExamineVC.m
  文件名: XGGClassToExamineVC
  类名: @implementation ClassToExamineVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
  文件名: XGGSelectStandardsView
  类名: @implementation SelectStandardsView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/选择规格/XGGStandardsCell.m
  文件名: XGGStandardsCell
  类名: @implementation StandardsCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGCommodityDetailModel.m
  文件名: XGGCommodityDetailModel
  类名: @implementation CommodityDetailModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/服务保障/XGGGuaranteeView.m
  文件名: XGGGuaranteeView
  类名: @implementation GuaranteeView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGYBGoodPlayerCtrView.m
  文件名: XGGYBGoodPlayerCtrView
  类名: @implementation YBGoodPlayerCtrView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGGoodsDetailVC.m
  文件名: XGGGoodsDetailVC
  类名: @implementation GoodsDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
  文件名: XGGCommodityEvaluationCell
  类名: @implementation CommodityEvaluationCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell1.m
  文件名: XGGCommodityCell1
  类名: @implementation CommodityCell1
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell2Row2.m
  文件名: XGGCommodityCell2Row2
  类名: @implementation CommodityCell2Row2
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell3.m
  文件名: XGGCommodityCell3
  类名: @implementation CommodityCell3
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/Cell/XGGCommodityCell2Row1.m
  文件名: XGGCommodityCell2Row1
  类名: @implementation CommodityCell2Row1
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGsliderCollectionVCell.m
  文件名: XGGsliderCollectionVCell
  类名: @implementation sliderCollectionVCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/view/XGGStoreInfoView.m
  文件名: XGGStoreInfoView
  类名: @implementation StoreInfoView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGsliderCollectionView.m
  文件名: XGGsliderCollectionView
  类名: @implementation sliderCollectionView{
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGGoodsExplainCell.m
  文件名: XGGGoodsExplainCell
  类名: @implementation GoodsExplainCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGShowDetailVC.m
  文件名: XGGShowDetailVC
  类名: @implementation ShowDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品详情/XGGCommodityDetailVC.m
  文件名: XGGCommodityDetailVC
  类名: @implementation CommodityDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/立即支付/XGGPayOrderView.m
  文件名: XGGPayOrderView
  类名: @implementation PayOrderView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/追评/XGGAppendEvaluateVC.m
  文件名: XGGAppendEvaluateVC
  类名: @implementation AppendEvaluateVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/开店申请/XGGApplyShopVC.m
  文件名: XGGApplyShopVC
  类名: @implementation ApplyShopVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/开店申请/XGGShopApplyStatusVC.m
  文件名: XGGShopApplyStatusVC
  类名: @implementation ShopApplyStatusVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/保证金/XGGBondViewController.m
  文件名: XGGBondViewController
  类名: @implementation BondViewController
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的地址/XGGAddressCell.m
  文件名: XGGAddressCell
  类名: @implementation AddressCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的地址/XGGRejectAddressModel.m
  文件名: XGGRejectAddressModel
  类名: @implementation RejectAddressModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的地址/XGGAddressVC.m
  文件名: XGGAddressVC
  类名: @implementation AddressVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
  文件名: XGGEditAdressVC
  类名: @implementation EditAdressVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的地址/XGGAddressModel.m
  文件名: XGGAddressModel
  类名: @implementation AddressModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品评价/XGGEvaluationListCell.m
  文件名: XGGEvaluationListCell
  类名: @implementation EvaluationListCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品评价/XGGEvaluationListModel.m
  文件名: XGGEvaluationListModel
  类名: @implementation EvaluationListModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
  文件名: XGGGoodsEvaluationListVC
  类名: @implementation GoodsEvaluationListVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
  文件名: XGGBuyerGetMoneyVC
  类名: @implementation BuyerGetMoneyVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
  文件名: XGGBuyerRefundDetailVC
  类名: @implementation BuyerRefundDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/买家退款详情/XGGBuyerRefundModel.m
  文件名: XGGBuyerRefundModel
  类名: @implementation BuyerRefundModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/买家退款详情/子页面/XGGBuyerRefundHeadView.m
  文件名: XGGBuyerRefundHeadView
  类名: @implementation BuyerRefundHeadView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJPropertyType.m
  文件名: MJPropertyType
  类名: @implementation MJPropertyType
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJProperty.m
  文件名: MJProperty
  类名: @implementation MJProperty
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJPropertyKey.m
  文件名: MJPropertyKey
  类名: @implementation MJPropertyKey
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/三方/MJExtension/MJFoundation.m
  文件名: MJFoundation
  类名: @implementation MJFoundation
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/XGGOrderModel.m
  文件名: XGGOrderModel
  类名: @implementation OrderModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/XGGOrderListVC.m
  文件名: XGGOrderListVC
  类名: @implementation OrderListVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/XGGOrderDetailModel.m
  文件名: XGGOrderDetailModel
  类名: @implementation OrderDetailModel
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
  文件名: XGGOrderDetailVC
  类名: @implementation OrderDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
  文件名: XGGOrderInfoView
  类名: @implementation OrderInfoView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
  文件名: XGGOrderPriceView
  类名: @implementation OrderPriceView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
  文件名: XGGOrderPublicView
  类名: @implementation OrderPublicView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/订单详情/子页面/XGGOrderHeaderView.m
  文件名: XGGOrderHeaderView
  类名: @implementation OrderHeaderView
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/我的订单/XGGOrderListCell.m
  文件名: XGGOrderListCell
  类名: @implementation OrderListCell
  类型: @implementation声明

不一致: YBVideo/店铺/买家端/账户余额/XGGAccountBalanceVC.m
  文件名: XGGAccountBalanceVC
  类名: @implementation AccountBalanceVC
  类型: @implementation声明

不一致: YBVideo/店铺/小店主页/卖家页面/XGGSellerView.m
  文件名: XGGSellerView
  类名: @implementation SellerView
  类型: @implementation声明

不一致: YBVideo/店铺/小店主页/XGGShopHomeVC.m
  文件名: XGGShopHomeVC
  类名: @implementation ShopHomeVC
  类型: @implementation声明

不一致: YBVideo/店铺/小店主页/买家页面/XGGBuyerView.m
  文件名: XGGBuyerView
  类名: @implementation BuyerView
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/提取收益/XGGGetMoneyVC.m
  文件名: XGGGetMoneyVC
  类名: @implementation GetMoneyVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/商品管理/XGGCommodityManagementVC.m
  文件名: XGGCommodityManagementVC
  类名: @implementation CommodityManagementVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/商品管理/XGGCommodityModel.m
  文件名: XGGCommodityModel
  类名: @implementation CommodityModel
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/商品管理/XGGCommodityCell.m
  文件名: XGGCommodityCell
  类名: @implementation CommodityCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
  文件名: XGGOtherSellOrderDetailVC
  类名: @implementation OtherSellOrderDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/其他订单详情/XGGSellOrderDetailModel.m
  文件名: XGGSellOrderDetailModel
  类名: @implementation SellOrderDetailModel
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
  文件名: XGGEditSaveAddressVC
  类名: @implementation EditSaveAddressVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/订单管理/XGGSellOrderCell.m
  文件名: XGGSellOrderCell
  类名: @implementation SellOrderCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/订单管理/XGGSellOrderModel.m
  文件名: XGGSellOrderModel
  类名: @implementation SellOrderModel
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
  文件名: XGGSellerOrderManagementVC
  类名: @implementation SellerOrderManagementVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/退款详情/拒绝退款/XGGRefuseRefundVC.m
  文件名: XGGRefuseRefundVC
  类名: @implementation RefuseRefundVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/退款详情/XGGRefundDetailVC.m
  文件名: XGGRefundDetailVC
  类名: @implementation RefundDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/退款详情/XGGRefundDetailModel.m
  文件名: XGGRefundDetailModel
  类名: @implementation RefundDetailModel
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/平台商品/XGGPlatformListCell.m
  文件名: XGGPlatformListCell
  类名: @implementation PlatformListCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
  文件名: XGGPlatformGoodsVC
  类名: @implementation PlatformGoodsVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/资质/XGGQualificationsVC.m
  文件名: XGGQualificationsVC
  类名: @implementation QualificationsVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/价格与库存/XGGStockView.m
  文件名: XGGStockView
  类名: @implementation StockView
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/价格与库存/XGGEditStockVC.m
  文件名: XGGEditStockVC
  类名: @implementation EditStockVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/账单管理/XGGBillCell.m
  文件名: XGGBillCell
  类名: @implementation BillCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/账单管理/XGGBillManageVC.m
  文件名: XGGBillManageVC
  类名: @implementation BillManageVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/添加商品/选择商品类别/XGGSelCommodityClassVC.m
  文件名: XGGSelCommodityClassVC
  类名: @implementation SelCommodityClassVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/添加商品/XGGAddCommodityVC.m
  文件名: XGGAddCommodityVC
  类名: @implementation AddCommodityVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
  文件名: XGGCommodityDetailView
  类名: @implementation CommodityDetailView
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/添加商品/子页面view/XGGStandardsView.m
  文件名: XGGStandardsView
  类名: @implementation StandardsView
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
  文件名: XGGCommodityTitleView
  类名: @implementation CommodityTitleView
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
  文件名: XGGRelationVideoGoodsVC
  类名: @implementation RelationVideoGoodsVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGshopCell.m
  文件名: XGGshopCell
  类名: @implementation shopCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
  文件名: XGGRelationGoodsVC
  类名: @implementation RelationGoodsVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGPlatformCell.m
  文件名: XGGPlatformCell
  类名: @implementation PlatformCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGGoodsDetailViewController.m
  文件名: XGGGoodsDetailViewController
  类名: @implementation GoodsDetailViewController
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGshopDetailVC.m
  文件名: XGGshopDetailVC
  类名: @implementation shopDetailVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGRelationGoodsModel.m
  文件名: XGGRelationGoodsModel
  类名: @implementation RelationGoodsModel
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/XGGgoodsShowCell.m
  文件名: XGGgoodsShowCell
  类名: @implementation goodsShowCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/我的店铺/添加商品/XGGAddGoodsVC.m
  文件名: XGGAddGoodsVC
  类名: @implementation AddGoodsVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
  文件名: XGGWaitSendGoodsVC
  类名: @implementation WaitSendGoodsVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/待发货详情/XGGSendGoodsInfo.m
  文件名: XGGSendGoodsInfo
  类名: @implementation SendGoodsInfo
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/待发货详情/XGGLogisticsCell.m
  文件名: XGGLogisticsCell
  类名: @implementation LogisticsCell
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/小店详情/XGGShopInfoVC.m
  文件名: XGGShopInfoVC
  类名: @implementation ShopInfoVC
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/公共页面/XGGSellOrderPublicView.m
  文件名: XGGSellOrderPublicView
  类名: @implementation SellOrderPublicView
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/公共页面/XGGRefundHeadView.m
  文件名: XGGRefundHeadView
  类名: @implementation RefundHeadView
  类型: @implementation声明

不一致: YBVideo/店铺/卖家端/添加平台商品/XGGAddOtherSaleGoodsVC.m
  文件名: XGGAddOtherSaleGoodsVC
  类名: @implementation AddOtherSaleGoodsVC
  类型: @implementation声明

不一致: YBVideo/缓存/权限(tab中间按钮)/XGGPower.m
  文件名: XGGPower
  类名: @implementation XGGPower
  类型: @implementation声明

不一致: YBVideo/缓存/定位/XGGcityDefault.m
  文件名: XGGcityDefault
  类名: @implementation XGGcityDefault
  类型: @implementation声明

不一致: YBVideo/缓存/定位/XGGRKLBSManager.m
  文件名: XGGRKLBSManager
  类名: @implementation RKLBSManager
  类型: @implementation声明

不一致: YBVideo/缓存/配置信息/XGGcommon.m
  文件名: XGGcommon
  类名: @implementation common
  类型: @implementation声明

不一致: YBVideo/缓存/个人信息/XGGConfig.m
  文件名: XGGConfig
  类名: @implementation Config
  类型: @implementation声明

不一致: YBVideo/底部导航/XGGYBTabBar.m
  文件名: XGGYBTabBar
  类名: @implementation YBTabBar
  类型: @implementation声明

不一致: YBVideo/底部导航/直播or视频/XGGYBLiveOrVideo.m
  文件名: XGGYBLiveOrVideo
  类名: @implementation YBLiveOrVideo
  类型: @implementation声明

不一致: YBVideo/底部导航/XGGYBTabBarController.m
  文件名: XGGYBTabBarController
  类名: @implementation YBTabBarController
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/MHMeiyanMenusView.m
  文件名: MHMeiyanMenusView
  类名: @implementation MHMeiyanMenusView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/Model/MHFilterModel.m
  文件名: MHFilterModel
  类名: @implementation MHFilterModel
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/Model/MHBeautiesModel.m
  文件名: MHBeautiesModel
  类名: @implementation MHBeautiesModel
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHPrintView.m
  文件名: MHPrintView
  类名: @implementation MHPrintView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyMenuCell.m
  文件名: MHBeautyMenuCell
  类名: @implementation MHBeautyMenuCell
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHMakeUpView.m
  文件名: MHMakeUpView
  类名: @implementation MHMakeUpView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyView.m
  文件名: MHBeautyView
  类名: @implementation MHBeautyView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBottomView.m
  文件名: MHBottomView
  类名: @implementation MHBottomView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHSpecificEffectView.m
  文件名: MHSpecificEffectView
  类名: @implementation MHSpecificEffectView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHMagnifiedView.m
  文件名: MHMagnifiedView
  类名: @implementation MHMagnifiedView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHSectionStickersView.m
  文件名: MHSectionStickersView
  类名: @implementation MHSectionStickersView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHStickerCell.m
  文件名: MHStickerCell
  类名: @implementation MHStickerIndicatorView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyAssembleView.m
  文件名: MHBeautyAssembleView
  类名: @implementation MHBeautyAssembleView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHActionView.m
  文件名: MHActionView
  类名: @implementation MHActionView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautySlider.m
  文件名: MHBeautySlider
  类名: @implementation MHBeautySlider
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHCompleteBeautyView.m
  文件名: MHCompleteBeautyView
  类名: @implementation MHCompleteBeautyView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHFiltersView.m
  文件名: MHFiltersView
  类名: @implementation MHFiltersView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHStickersView.m
  文件名: MHStickersView
  类名: @implementation MHStickersView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHSpecificAssembleView.m
  文件名: MHSpecificAssembleView
  类名: @implementation MHSpecificAssembleView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/MHBeautyFaceView.m
  文件名: MHBeautyFaceView
  类名: @implementation MHBeautyFaceView
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/WNSegmentItem.m
  文件名: WNSegmentItem
  类名: @implementation WNSegmentItem
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/UI/View/WNSegmentControl.m
  文件名: WNSegmentControl
  类名: @implementation WNSegmentControl
  类型: @implementation声明

不一致: YBVideo/三方_SDK/美狐sdk/服务端默认美颜数值缓存/XGGsproutCommon.m
  文件名: XGGsproutCommon
  类名: @implementation sproutCommon
  类型: @implementation声明

不一致: YBVideo/消息/XGGchatmessageCell.m
  文件名: XGGchatmessageCell
  类名: @implementation chatmessageCell
  类型: @implementation声明

不一致: YBVideo/消息/XGGOrderMessageVC.m
  文件名: XGGOrderMessageVC
  类名: @implementation OrderMessageVC
  类型: @implementation声明

不一致: YBVideo/消息/XGGMsgTopPubVC.m
  文件名: XGGMsgTopPubVC
  类名: @implementation MsgTopPubVC
  类型: @implementation声明

不一致: YBVideo/消息/XGGMsgSysVC.m
  文件名: XGGMsgSysVC
  类名: @implementation MsgSysVC
  类型: @implementation声明

不一致: YBVideo/消息/XGGOrderMessageModel.m
  文件名: XGGOrderMessageModel
  类名: @implementation OrderMessageModel
  类型: @implementation声明

不一致: YBVideo/消息/XGGMessageFansVC.m
  文件名: XGGMessageFansVC
  类名: @implementation MessageFansVC
  类型: @implementation声明

不一致: YBVideo/消息/选择联系人/XGGSelPeopleCell.m
  文件名: XGGSelPeopleCell
  类名: @implementation SelPeopleCell
  类型: @implementation声明

不一致: YBVideo/消息/选择联系人/XGGSelPeopleV.m
  文件名: XGGSelPeopleV
  类名: @implementation SelPeopleV
  类型: @implementation声明

不一致: YBVideo/消息/model/XGGMsgTopPubModel.m
  文件名: XGGMsgTopPubModel
  类名: @implementation MsgTopPubModel
  类型: @implementation声明

不一致: YBVideo/消息/model/XGGMsgSysModel.m
  文件名: XGGMsgSysModel
  类名: @implementation MsgSysModel
  类型: @implementation声明

不一致: YBVideo/消息/model/XGGMessageListModel.m
  文件名: XGGMessageListModel
  类名: @implementation MessageListModel
  类型: @implementation声明

不一致: YBVideo/消息/model/XGGMessageFansModel.m
  文件名: XGGMessageFansModel
  类名: @implementation MessageFansModel
  类型: @implementation声明

不一致: YBVideo/消息/view/XGGMessageListCell.m
  文件名: XGGMessageListCell
  类名: @implementation MessageListCell
  类型: @implementation声明

不一致: YBVideo/消息/view/XGGMessageHeaderV.m
  文件名: XGGMessageHeaderV
  类名: @implementation MessageHeaderV
  类型: @implementation声明

不一致: YBVideo/消息/view/XGGMsgTopPubCell.m
  文件名: XGGMsgTopPubCell
  类名: @implementation MsgTopPubCell
  类型: @implementation声明

不一致: YBVideo/消息/view/XGGMessageFansCell.m
  文件名: XGGMessageFansCell
  类名: @implementation MessageFansCell
  类型: @implementation声明

不一致: YBVideo/消息/view/MessageCell.m
  文件名: MessageCell
  类名: @implementation MessageListCell
  类型: @implementation声明

不一致: YBVideo/消息/view/XGGMsgSysCell.m
  文件名: XGGMsgSysCell
  类名: @implementation MsgSysCell
  类型: @implementation声明

不一致: YBVideo/消息/MessageVC.m
  文件名: MessageVC
  类名: @implementation MessageListVC
  类型: @implementation声明

不一致: YBVideo/直播模块/用户端相关/view/XGGYBPlayCtrlView.m
  文件名: XGGYBPlayCtrlView
  类名: @implementation YBPlayCtrlView
  类型: @implementation声明

不一致: YBVideo/直播模块/用户端相关/XGGYBPlayVC.m
  文件名: XGGYBPlayVC
  类名: @implementation YBPlayVC
  类型: @implementation声明

不一致: YBVideo/直播模块/直播列表/进房间公用obj/XGGYBCheckLiveObj.m
  文件名: XGGYBCheckLiveObj
  类名: @implementation YBCheckLiveObj
  类型: @implementation声明

不一致: YBVideo/直播模块/直播列表/XGGYBLiveListVC.m
  文件名: XGGYBLiveListVC
  类名: @implementation YBLiveListVC
  类型: @implementation声明

不一致: YBVideo/直播模块/直播列表/view/XGGYBLiveListCell.m
  文件名: XGGYBLiveListCell
  类名: @implementation YBLiveListCell
  类型: @implementation声明

不一致: YBVideo/直播模块/房间警告/XGGYBLiveRoomAlertView.m
  文件名: XGGYBLiveRoomAlertView
  类名: @implementation YBLiveRoomAlertView
  类型: @implementation声明

不一致: YBVideo/直播模块/RTCPush/XGGYBLiveRTCManager.m
  文件名: XGGYBLiveRTCManager
  类名: @implementation YBLiveRTCManager
  类型: @implementation声明

不一致: YBVideo/直播模块/主播-用户公用/XGGYBChatToolBar.m
  文件名: XGGYBChatToolBar
  类名: @implementation YBChatToolBar
  类型: @implementation声明

不一致: YBVideo/直播模块/主播-用户公用/XGGYBLiveEndView.m
  文件名: XGGYBLiveEndView
  类名: @implementation YBLiveEndView
  类型: @implementation声明

不一致: YBVideo/直播模块/主播端相关/直播间商品/XGGroomShowGoodsView.m
  文件名: XGGroomShowGoodsView
  类名: @implementation roomShowGoodsView
  类型: @implementation声明

不一致: YBVideo/直播模块/主播端相关/直播分类/XGGstartLiveClassCell.m
  文件名: XGGstartLiveClassCell
  类名: @implementation startLiveClassCell
  类型: @implementation声明

不一致: YBVideo/直播模块/主播端相关/直播分类/XGGstartLiveClassVC.m
  文件名: XGGstartLiveClassVC
  类名: @implementation startLiveClassVC
  类型: @implementation声明

不一致: YBVideo/直播模块/主播端相关/view/XGGYBLiveFucView.m
  文件名: XGGYBLiveFucView
  类名: @implementation YBLiveFucView
  类型: @implementation声明

不一致: YBVideo/直播模块/主播端相关/view/XGGYBLiveCtrlView.m
  文件名: XGGYBLiveCtrlView
  类名: @implementation YBLiveCtrlView
  类型: @implementation声明

不一致: YBVideo/直播模块/主播端相关/view/XGGYBLivePreview.m
  文件名: XGGYBLivePreview
  类名: @implementation YBLivePreview
  类型: @implementation声明

不一致: YBVideo/直播模块/主播端相关/XGGYBLiveVC.m
  文件名: XGGYBLiveVC
  类名: @implementation YBLiveVC
  类型: @implementation声明

不一致: YBVideo/直播模块/socket/XGGYBSocketLive.m
  文件名: XGGYBSocketLive
  类名: @implementation YBSocketLive
  类型: @implementation声明

不一致: YBVideo/直播模块/socket/XGGYBSocketPlay.m
  文件名: XGGYBSocketPlay
  类名: @implementation YBSocketPlay
  类型: @implementation声明

不一致: YBVideo/功能/会员/view/XGGYBVipCell.m
  文件名: XGGYBVipCell
  类名: @implementation YBVipCell
  类型: @implementation声明

不一致: YBVideo/功能/会员/view/XGGYBVipHeader.m
  文件名: XGGYBVipHeader
  类名: @implementation YBVipHeader
  类型: @implementation声明

不一致: YBVideo/功能/会员/view/XGGvipBuyView.m
  文件名: XGGvipBuyView
  类名: @implementation vipBuyView
  类型: @implementation声明

不一致: YBVideo/功能/会员/XGGYBVipVC.m
  文件名: XGGYBVipVC
  类名: @implementation YBVipVC
  类型: @implementation声明

不一致: YBVideo/功能/钱包/支付公共方法/XGGYBRechargeType.m
  文件名: XGGYBRechargeType
  类名: @implementation YBRechargeType
  类型: @implementation声明

不一致: YBVideo/功能/钱包/aliPay/XGGOrder.m
  文件名: XGGOrder
  类名: @implementation Order
  类型: @implementation声明

不一致: YBVideo/功能/钱包/aliPay/Util/XGGMD5DataSigner.m
  文件名: XGGMD5DataSigner
  类名: @implementation MD5DataSigner
  类型: @implementation声明

不一致: YBVideo/功能/钱包/aliPay/Util/NSDataEx.m
  文件名: NSDataEx
  类名: @implementation NSData (NSDataBase64Additions)
  类型: @implementation声明

不一致: YBVideo/功能/钱包/aliPay/Util/XGGRSADataVerifier.m
  文件名: XGGRSADataVerifier
  类名: @implementation RSADataVerifier
  类型: @implementation声明

不一致: YBVideo/功能/钱包/aliPay/Util/base64.m
  文件名: base64
  类名: @implementation Base64
  类型: @implementation声明

不一致: YBVideo/功能/钱包/aliPay/Util/XGGRSADataSigner.m
  文件名: XGGRSADataSigner
  类名: @implementation RSADataSigner
  类型: @implementation声明

不一致: YBVideo/功能/钱包/XGGYBRechargeVC.m
  文件名: XGGYBRechargeVC
  类名: @implementation YBRechargeVC
  类型: @implementation声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGfansViewController.m
  文件名: XGGfansViewController
  类名: @implementation fansViewController
  类型: @implementation声明

不一致: YBVideo/功能/粉丝_关注_拉黑/blackListCell.m
  文件名: blackListCell
  类名: @implementation BlackListCell
  类型: @implementation声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGattrViewController.m
  文件名: XGGattrViewController
  类名: @implementation attrViewController
  类型: @implementation声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGfansModel.m
  文件名: XGGfansModel
  类名: @implementation fansModel
  类型: @implementation声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGBlackListVC.m
  文件名: XGGBlackListVC
  类名: @implementation BlackListVC
  类型: @implementation声明

不一致: YBVideo/功能/粉丝_关注_拉黑/XGGfans.m
  文件名: XGGfans
  类名: @implementation fans
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/JCHATConversationViewController.m
  文件名: JCHATConversationViewController
  类名: @implementation JCHATConversationViewController
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/Model/JCHATRawAudioDataPlayer.m
  文件名: JCHATRawAudioDataPlayer
  类名: @implementation JCHATRawAudioDataPlayer
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/Model/JCHATChatModel.m
  文件名: JCHATChatModel
  类名: @implementation JCHATChatModel
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/CellView/JCHATLoadMessageTableViewCell.m
  文件名: JCHATLoadMessageTableViewCell
  类名: @implementation JCHATLoadMessageTableViewCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/CellView/JCHATShowTimeCell.m
  文件名: JCHATShowTimeCell
  类名: @implementation JCHATShowTimeCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
  文件名: JCHATMessageTableViewCell
  类名: @implementation JCHATMessageTableViewCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATRecordAnimationView.m
  文件名: JCHATRecordAnimationView
  类名: @implementation JCHATRecordAnimationView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMessageTableView.m
  文件名: JCHATMessageTableView
  类名: @implementation JCHATMessageTableView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATAudioPlayerHelper.m
  文件名: JCHATAudioPlayerHelper
  类名: @implementation JCHATAudioPlayerHelper
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATToolBar.m
  文件名: JCHATToolBar
  类名: @implementation JCHATToolBar
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMessageTextView.m
  文件名: JCHATMessageTextView
  类名: @implementation JCHATMessageTextView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMoreView.m
  文件名: JCHATMoreView
  类名: @implementation JCHATMoreView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/RecordView/XHVoiceCommonHelper.m
  文件名: XHVoiceCommonHelper
  类名: @implementation XHVoiceCommonHelper
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/RecordView/XHVoiceRecordHelper.m
  文件名: XHVoiceRecordHelper
  类名: @implementation XHVoiceRecordHelper
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/RecordView/XHVoiceRecordHUD.m
  文件名: XHVoiceRecordHUD
  类名: @implementation XHVoiceRecordHUD
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/单聊/View/JCHATMessageContentView.m
  文件名: JCHATMessageContentView
  类名: @implementation JCHATMessageContentView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/发送位置/XGGTencentLocationVC.m
  文件名: XGGTencentLocationVC
  类名: @implementation TencentLocationVC
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/发送位置/XGGSearchResultView.m
  文件名: XGGSearchResultView
  类名: @implementation SearchResultView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/发送位置/XGGLocationCell.m
  文件名: XGGLocationCell
  类名: @implementation LocationCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/Utils/XGGViewUtil.m
  文件名: XGGViewUtil
  类名: @implementation ViewUtil
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/Common/JCHATSendMsgManager.m
  文件名: JCHATSendMsgManager
  类名: @implementation JCHATSendMsgManager
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/Common/JCHATStringUtils.m
  文件名: JCHATStringUtils
  类名: @implementation JCHATStringUtils {
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/Common/JCHATSendMsgController.m
  文件名: JCHATSendMsgController
  类名: @implementation JCHATSendMsgController
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/Common/JCHATFileManager.m
  文件名: JCHATFileManager
  类名: @implementation JCHATFileManager
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/Common/JCHATAlertToSendImage.m
  文件名: JCHATAlertToSendImage
  类名: @implementation JCHATAlertToSendImage
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/Common/JCHATTimeOutManager.m
  文件名: JCHATTimeOutManager
  类名: @implementation JCHATTimeOutManager
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/发送表情/XGGemojiCell.m
  文件名: XGGemojiCell
  类名: @implementation emojiCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/发送表情/XGGtwEmojiView.m
  文件名: XGGtwEmojiView
  类名: @implementation CollCellWhite
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/发送表情/LWLCollectionViewHorizontalLayout.m
  文件名: LWLCollectionViewHorizontalLayout
  类名: @implementation LWLCollectionViewHorizontalLayout
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoLoadingView.m
  文件名: MJPhotoLoadingView
  类名: @implementation MJPhotoLoadingView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoView.m
  文件名: MJPhotoView
  类名: @implementation MJPhotoView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhoto.m
  文件名: MJPhoto
  类名: @implementation MJPhoto
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoToolbar.m
  文件名: MJPhotoToolbar
  类名: @implementation MJPhotoToolbar
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoProgressView.m
  文件名: MJPhotoProgressView
  类名: @implementation MJPhotoProgressView
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/PreviewPicture/MJPhotoBrowser.m
  文件名: MJPhotoBrowser
  类名: @implementation MJPhotoBrowser
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/JCHATAlertViewWait.m
  文件名: JCHATAlertViewWait
  类名: @implementation JCHATAlertViewWait
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/ChatImageBubble/XGGChatImageBubble.m
  文件名: XGGChatImageBubble
  类名: @implementation ChatImageBubble
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/ChatImageBubble/XGGChatBubbleLayer.m
  文件名: XGGChatBubbleLayer
  类名: @implementation ChatBubbleLayer
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/JCHATCustomFormatter.m
  文件名: JCHATCustomFormatter
  类名: @implementation JCHATCustomFormatter {
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoBrowserViewController.m
  文件名: JCHATPhotoBrowserViewController
  类名: @implementation JCHATPhotoBrowserViewController
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATAlbumViewController.m
  文件名: JCHATAlbumViewController
  类名: @implementation JCHATAlbumViewController
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoSelectViewController.m
  文件名: JCHATPhotoSelectViewController
  类名: @implementation JCHATPhotoSelectViewController
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Controller/JCHATPhotoPickerViewController.m
  文件名: JCHATPhotoPickerViewController
  类名: @implementation JCHATPhotoPickerViewController
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Model/JCHATPhotoModel.m
  文件名: JCHATPhotoModel
  类名: @implementation JCHATPhotoModel
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/Model/JCHATAlbumModel.m
  文件名: JCHATAlbumModel
  类名: @implementation JCHATAlbumModel
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/JCHATAlbumTableViewCell.m
  文件名: JCHATAlbumTableViewCell
  类名: @implementation JCHATAlbumTableViewCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/JCHATPhotoBrowserCollectionViewCell.m
  文件名: JCHATPhotoBrowserCollectionViewCell
  类名: @implementation JCHATPhotoBrowserCollectionViewCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/XGGThumbImageCollectionViewCell.m
  文件名: XGGThumbImageCollectionViewCell
  类名: @implementation ThumbImageCollectionViewCell
  类型: @implementation声明

不一致: YBVideo/功能/极光消息/External/HMPhotoPicker/View/JCHATSelectImgCollectionView.m
  文件名: JCHATSelectImgCollectionView
  类名: @implementation JCHATSelectImgCollectionView
  类型: @implementation声明

不一致: YBVideo/功能/H5/XGGPubH5.m
  文件名: XGGPubH5
  类名: @implementation PubH5
  类型: @implementation声明

不一致: YBVideo/功能/上热门/XGGUpHotCell.m
  文件名: XGGUpHotCell
  类名: @implementation UpHotCell
  类型: @implementation声明

不一致: YBVideo/功能/上热门/XGGHotVideoDetailVC.m
  文件名: XGGHotVideoDetailVC
  类名: @implementation HotVideoDetailVC
  类型: @implementation声明

不一致: YBVideo/功能/上热门/XGGaddHotVideoVC.m
  文件名: XGGaddHotVideoVC
  类名: @implementation addHotVideoVC
  类型: @implementation声明

不一致: YBVideo/功能/登录奖励/XGGLogFirstCell.m
  文件名: XGGLogFirstCell
  类名: @implementation LogFirstCell
  类型: @implementation声明

不一致: YBVideo/功能/登录奖励/XGGLogFirstCell2.m
  文件名: XGGLogFirstCell2
  类名: @implementation LogFirstCell2
  类型: @implementation声明

不一致: YBVideo/功能/登录奖励/XGGLoginbonus.m
  文件名: XGGLoginbonus
  类名: @implementation Loginbonus
  类型: @implementation声明

不一致: YBVideo/功能/顶部导航搜索/XGGsearchVC.m
  文件名: XGGsearchVC
  类名: @implementation searchVC
  类型: @implementation声明

不一致: YBVideo/功能/顶部导航搜索/HMSegmentedControl/HMSegmentedControl.m
  文件名: HMSegmentedControl
  类名: @implementation HMScrollView
  类型: @implementation声明

不一致: YBVideo/功能/顶部导航搜索/HXSearchBar.m
  文件名: HXSearchBar
  类名: @implementation HXSearchBar
  类型: @implementation声明

不一致: YBVideo/功能/顶部导航搜索/view/XGGSearchHistoryCell.m
  文件名: XGGSearchHistoryCell
  类名: @implementation SearchHistoryCell
  类型: @implementation声明

不一致: YBVideo/功能/顶部导航搜索/自定义搜索框/XGGYBSearchBarView.m
  文件名: XGGYBSearchBarView
  类名: @implementation YBSearchBarView
  类型: @implementation声明

不一致: YBVideo/功能/存储功能类/XGGYBStorageObj.m
  文件名: XGGYBStorageObj
  类名: @implementation YBStorageObj
  类型: @implementation声明

不一致: YBVideo/功能/存储功能类/XGGStorageConfig.m
  文件名: XGGStorageConfig
  类名: @implementation StorageConfig
  类型: @implementation声明

不一致: YBVideo/功能/评论/XGGcommDetailCell.m
  文件名: XGGcommDetailCell
  类名: @implementation commDetailCell
  类型: @implementation声明

不一致: YBVideo/功能/评论/XGGcommentview.m
  文件名: XGGcommentview
  类名: @implementation commentview
  类型: @implementation声明

不一致: YBVideo/功能/评论/XGGcommCell.m
  文件名: XGGcommCell
  类名: @implementation commCell{
  类型: @implementation声明

不一致: YBVideo/功能/评论/XGGdetailmodel.m
  文件名: XGGdetailmodel
  类名: @implementation detailmodel
  类型: @implementation声明

不一致: YBVideo/功能/评论/评论工具栏/XGGYBCommentToolBar.m
  文件名: XGGYBCommentToolBar
  类名: @implementation YBCommentToolBar
  类型: @implementation声明

不一致: YBVideo/功能/评论/XGGcommentModel.m
  文件名: XGGcommentModel
  类名: @implementation commentModel
  类型: @implementation声明

不一致: YBVideo/功能/青少年/XGGYBYoungManager.m
  文件名: XGGYBYoungManager
  类名: @implementation YBYoungManager
  类型: @implementation声明

不一致: YBVideo/功能/青少年/vc/XGGYBYoungModifyVC.m
  文件名: XGGYBYoungModifyVC
  类名: @implementation YBYoungModifyVC
  类型: @implementation声明

不一致: YBVideo/功能/青少年/vc/密码/XGGRKCodeInputView.m
  文件名: XGGRKCodeInputView
  类名: @implementation RKCodeInputView
  类型: @implementation声明

不一致: YBVideo/功能/青少年/vc/密码/XGGRKCodeView.m
  文件名: XGGRKCodeView
  类名: @implementation RKCodeView
  类型: @implementation声明

不一致: YBVideo/功能/青少年/vc/XGGYBYoungModeVC.m
  文件名: XGGYBYoungModeVC
  类名: @implementation YBYoungModeVC
  类型: @implementation声明

不一致: YBVideo/功能/青少年/vc/XGGYBYoungSetVC.m
  文件名: XGGYBYoungSetVC
  类名: @implementation YBYoungSetVC
  类型: @implementation声明

不一致: YBVideo/功能/青少年/小窗/XGGYBYoungSmall.m
  文件名: XGGYBYoungSmall
  类名: @implementation YBYoungSmall
  类型: @implementation声明

不一致: YBVideo/功能/我的名片/XGGBusinessCardVC.m
  文件名: XGGBusinessCardVC
  类名: @implementation BusinessCardVC
  类型: @implementation声明

不一致: YBVideo/功能/KeepLive/XGGRKKeepAlive.m
  文件名: XGGRKKeepAlive
  类名: @implementation RKKeepAlive
  类型: @implementation声明

不一致: YBVideo/功能/广告管理/XGGMyAdvertVC.m
  文件名: XGGMyAdvertVC
  类名: @implementation MyAdvertVC
  类型: @implementation声明

不一致: YBVideo/功能/广告管理/XGGAdvertManagerVC.m
  文件名: XGGAdvertManagerVC
  类名: @implementation AdvertManagerVC
  类型: @implementation声明

不一致: YBVideo/功能/广告管理/XGGMyAdvertCell.m
  文件名: XGGMyAdvertCell
  类名: @implementation MyAdvertCell
  类型: @implementation声明

不一致: YBVideo/功能/观看商品/XGGlookVGoodsDView.m
  文件名: XGGlookVGoodsDView
  类名: @implementation lookVGoodsDView{
  类型: @implementation声明

不一致: YBVideo/功能/邀请码/XGGYBInviteCode.m
  文件名: XGGYBInviteCode
  类名: @implementation YBInviteCode
  类型: @implementation声明

不一致: YBVideo/功能/邀请码/XGGYBInvitationView.m
  文件名: XGGYBInvitationView
  类名: @implementation YBInvitationView{
  类型: @implementation声明

不一致: YBVideo/功能/举报(直播间+看视频)/XGGYBLiveReportVC.m
  文件名: XGGYBLiveReportVC
  类名: @implementation YBLiveReportVC
  类型: @implementation声明

不一致: YBVideo/功能/举报(直播间+看视频)/XGGYBVideoReportVC.m
  文件名: XGGYBVideoReportVC
  类名: @implementation YBVideoReportVC
  类型: @implementation声明

不一致: YBVideo/功能/举报(直播间+看视频)/view/XGGYBReportCell.m
  文件名: XGGYBReportCell
  类名: @implementation YBReportCell
  类型: @implementation声明

不一致: YBVideo/功能/语言包/XGGYBLanguageTools.m
  文件名: XGGYBLanguageTools
  类名: @implementation YBLanguageTools
  类型: @implementation声明

不一致: YBVideo/功能/拍摄同款/XGGYBTakeSameVideoVC.m
  文件名: XGGYBTakeSameVideoVC
  类名: @implementation YBTakeSameVideoVC
  类型: @implementation声明

不一致: YBVideo/功能/注销账号/XGGYBDestroyAccount.m
  文件名: XGGYBDestroyAccount
  类名: @implementation YBDestroyAccount
  类型: @implementation声明

不一致: YBVideo/功能/注销账号/XGGYBDestroySureVC.m
  文件名: XGGYBDestroySureVC
  类名: @implementation YBDestroySureVC
  类型: @implementation声明

不一致: YBVideo/功能/注销账号/view/XGGYBDestroyCell.m
  文件名: XGGYBDestroyCell
  类名: @implementation YBDestroyCell
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/翻译api/GDYTranslateTool.m
  文件名: GDYTranslateTool
  类名: @implementation GDYTranslateTool
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/受限提示/GDYLimitAlert.m
  文件名: GDYLimitAlert
  类名: @implementation GDYLimitAlert
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/commom/XGGTHelper.m
  文件名: XGGTHelper
  类名: @implementation THelper
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/commom/XGGTResponderTextView.m
  文件名: XGGTResponderTextView
  类名: @implementation TResponderTextView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/commom/XGGTUnReadView.m
  文件名: XGGTUnReadView
  类名: @implementation TUnReadView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.m
  文件名: XGGTMessageController
  类名: @implementation TMessageController
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/XGGTInputController.m
  文件名: XGGTInputController
  类名: @implementation TInputController
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceView.m
  文件名: XGGTFaceView
  类名: @implementation TFaceGroup
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTVoiceMessageCell.m
  文件名: XGGTVoiceMessageCell
  类名: @implementation TVoiceMessageCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/TChatAlertView.m
  文件名: TChatAlertView
  类名: @implementation TChatAlertView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceCell.m
  文件名: XGGTFaceCell
  类名: @implementation TFaceCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMessageCell.m
  文件名: XGGTMessageCell
  类名: @implementation TMessageCellData : NSObject
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMoreCell.m
  文件名: XGGTMoreCell
  类名: @implementation TMoreCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTTextMessageCell.m
  文件名: XGGTTextMessageCell
  类名: @implementation TTextMessageCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFileMessageCell.m
  文件名: XGGTFileMessageCell
  类名: @implementation TFileMessageCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMoreView.m
  文件名: XGGTMoreView
  类名: @implementation TMoreView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTSystemMessageCell.m
  文件名: XGGTSystemMessageCell
  类名: @implementation TSystemMessageCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.m
  文件名: XGGTGoodsCell
  类名: @implementation TGoodsCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/TCallCell.m
  文件名: TCallCell
  类名: @implementation TCallCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTVideoMessageCell.m
  文件名: XGGTVideoMessageCell
  类名: @implementation TVideoItem
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.m
  文件名: XGGTLocationCell
  类名: @implementation TLocationCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGiftMessageCell.m
  文件名: XGGTGiftMessageCell
  类名: @implementation TGiftMessageCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMenuView.m
  文件名: XGGTMenuView
  类名: @implementation TMenuView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTMenuCell.m
  文件名: XGGTMenuCell
  类名: @implementation TMenuCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTTextView.m
  文件名: XGGTTextView
  类名: @implementation TTextView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTRecordView.m
  文件名: XGGTRecordView
  类名: @implementation TRecordView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTFaceMessageCell.m
  文件名: XGGTFaceMessageCell
  类名: @implementation TFaceMessageCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/单-群聊公共/view/XGGTImageMessageCell.m
  文件名: XGGTImageMessageCell
  类名: @implementation TImageItem
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/消息会话/TChatC2CController.m
  文件名: TChatC2CController
  类名: @implementation TChatC2CController
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/IM管理/XGGYBImManager.m
  文件名: XGGYBImManager
  类名: @implementation YBImManager
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.m
  文件名: XGGYBScrollImageView
  类名: @implementation YBScrollImageView
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/IM管理/工具资源/TUIKit.m
  文件名: TUIKit
  类名: @implementation TUIKit
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/IM管理/工具资源/TUIKitConfig.m
  文件名: TUIKitConfig
  类名: @implementation TUIKitConfig
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/view/TConversationCell.m
  文件名: TConversationCell
  类名: @implementation TConversationCellData
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/XGGYBMsgC2CListVC.m
  文件名: XGGYBMsgC2CListVC
  类名: @implementation YBMsgC2CListVC
  类型: @implementation声明

不一致: YBVideo/功能/腾讯消息/XGGYBMessageManager.m
  文件名: XGGYBMessageManager
  类名: @implementation YBMessageManager
  类型: @implementation声明

不一致: YBVideo/功能/礼物/XGGYBGiftView.m
  文件名: XGGYBGiftView
  类名: @implementation CollectionCellWhite
  类型: @implementation声明

不一致: YBVideo/功能/礼物/page/XGGYBPageControl.m
  文件名: XGGYBPageControl
  类名: @implementation YBPageControl
  类型: @implementation声明

不一致: YBVideo/功能/礼物/手绘礼物/XGGRKShowPaintedView.m
  文件名: XGGRKShowPaintedView
  类名: @implementation RKShowPaintedView
  类型: @implementation声明

不一致: YBVideo/功能/礼物/手绘礼物/XGGRKPaintedGiftView.m
  文件名: XGGRKPaintedGiftView
  类名: @implementation RKPaintedGiftView
  类型: @implementation声明

不一致: YBVideo/功能/礼物/XGGYBGiftPage.m
  文件名: XGGYBGiftPage
  类名: @implementation YBGiftPage
  类型: @implementation声明

不一致: YBVideo/功能/礼物/礼物特效/XGGexoensiveGifGiftV.m
  文件名: XGGexoensiveGifGiftV
  类名: @implementation exoensiveGifGiftV{
  类型: @implementation声明

不一致: YBVideo/功能/礼物/礼物特效/XGGexpensiveGiftV.m
  文件名: XGGexpensiveGiftV
  类名: @implementation expensiveGiftV
  类型: @implementation声明

不一致: YBVideo/功能/礼物/礼物特效/XGGcontinueGift.m
  文件名: XGGcontinueGift
  类名: @implementation RKPopView
  类型: @implementation声明

不一致: YBVideo/功能/礼物/礼物特效/XGGliansongBackView.m
  文件名: XGGliansongBackView
  类名: @implementation liansongBackView
  类型: @implementation声明

不一致: YBVideo/功能/礼物/礼物特效/图片/CFGradientLabel.m
  文件名: CFGradientLabel
  类名: @implementation CFGradientLabel
  类型: @implementation声明

不一致: YBVideo/功能/礼物/model/XGGYBGiftModel.m
  文件名: XGGYBGiftModel
  类名: @implementation YBGiftModel
  类型: @implementation声明

不一致: YBVideo/功能/礼物/model/GiftModel.m
  文件名: GiftModel
  类名: @implementation YBGiftModel
  类型: @implementation声明

不一致: YBVideo/功能/礼物/view/XGGYBGiftCell.m
  文件名: XGGYBGiftCell
  类名: @implementation YBGiftCell
  类型: @implementation声明

不一致: YBVideo/功能/礼物/view/GiftCell.m
  文件名: GiftCell
  类名: @implementation YBGiftCell
  类型: @implementation声明

不一致: YBVideo/功能/礼物/PageBar/TYPagerViewLayout.m
  文件名: TYPagerViewLayout
  类名: @implementation TYAutoPurgeCache
  类型: @implementation声明

不一致: YBVideo/功能/礼物/PageBar/TYTabPagerBar.m
  文件名: TYTabPagerBar
  类名: @implementation TYTabPagerBar
  类型: @implementation声明

不一致: YBVideo/功能/礼物/PageBar/TYTabPagerBarLayout.m
  文件名: TYTabPagerBarLayout
  类名: @implementation TYTabPagerBarLayout
  类型: @implementation声明

不一致: YBVideo/功能/礼物/PageBar/TYPagerView.m
  文件名: TYPagerView
  类名: @implementation TYPagerView
  类型: @implementation声明

不一致: YBVideo/功能/礼物/PageBar/TYTabPagerBarCell.m
  文件名: TYTabPagerBarCell
  类名: @implementation TYTabPagerBarCell
  类型: @implementation声明

不一致: YBVideo/功能/标签全部视频/XGGtopicVideoCell.m
  文件名: XGGtopicVideoCell
  类名: @implementation topicVideoCell
  类型: @implementation声明

不一致: YBVideo/功能/标签全部视频/XGGtopicDetailsVC.m
  文件名: XGGtopicDetailsVC
  类名: @implementation topicDetailsVC
  类型: @implementation声明

不一致: YBVideo/功能/直播/守护/XGGguardShowView.m
  文件名: XGGguardShowView
  类名: @implementation guardShowView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/守护/model/XGGguardListModel.m
  文件名: XGGguardListModel
  类名: @implementation guardListModel
  类型: @implementation声明

不一致: YBVideo/功能/直播/守护/view/XGGguardListCell.m
  文件名: XGGguardListCell
  类名: @implementation guardListCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/守护/view/XGGguardAlertView.m
  文件名: XGGguardAlertView
  类名: @implementation guardAlertView
  类型: @implementation声明

不一致: YBVideo/功能/直播/守护/view/XGGgrardButton.m
  文件名: XGGgrardButton
  类名: @implementation grardButton
  类型: @implementation声明

不一致: YBVideo/功能/直播/守护/XGGshouhuView.m
  文件名: XGGshouhuView
  类名: @implementation shouhuView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/PK/XGGYBAnchorPKView.m
  文件名: XGGYBAnchorPKView
  类名: @implementation YBAnchorPKView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/PK/XGGYBAnchorPKAlert.m
  文件名: XGGYBAnchorPKAlert
  类名: @implementation YBAnchorPKAlert{
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/PK/XGGYBPkProgressView.m
  文件名: XGGYBPkProgressView
  类名: @implementation YBPkProgressView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/主播连麦头像昵称/XGGYBAnchorLinkInfo.m
  文件名: XGGYBAnchorLinkInfo
  类名: @implementation YBAnchorLinkInfo
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/连麦弹窗/XGGYBLinkAlertView.m
  文件名: XGGYBLinkAlertView
  类名: @implementation YBLinkAlertView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
  文件名: XGGYBAnchorOnline
  类名: @implementation YBAnchorOnline
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/主播连麦在线列表/view/XGGYBAnchorOnlineCell.m
  文件名: XGGYBAnchorOnlineCell
  类名: @implementation YBAnchorOnlineCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/连麦+PK/连麦小窗/XGGYBTxLinkMicView.m
  文件名: XGGYBTxLinkMicView
  类名: @implementation YBTxLinkMicView
  类型: @implementation声明

不一致: YBVideo/功能/直播/ActionSheet/CSActionSheet.m
  文件名: CSActionSheet
  类名: @implementation CSActionSheet
  类型: @implementation声明

不一致: YBVideo/功能/直播/ActionSheet/CSActionPicker.m
  文件名: CSActionPicker
  类名: @implementation CSActionPicker
  类型: @implementation声明

不一致: YBVideo/功能/直播/用户列表/XGGYBUserListView.m
  文件名: XGGYBUserListView
  类名: @implementation YBUserListView
  类型: @implementation声明

不一致: YBVideo/功能/直播/用户列表/model/XGGYBUserListModel.m
  文件名: XGGYBUserListModel
  类名: @implementation YBUserListModel
  类型: @implementation声明

不一致: YBVideo/功能/直播/用户列表/view/XGGYBUserListCell.m
  文件名: XGGYBUserListCell
  类名: @implementation YBUserListCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/管理员列表/XGGadminCell.m
  文件名: XGGadminCell
  类名: @implementation adminCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/管理员列表/XGGadminLists.m
  文件名: XGGadminLists
  类名: @implementation adminLists
  类型: @implementation声明

不一致: YBVideo/功能/直播/每日任务/个中VC/XGGYBDayTaskVC.m
  文件名: XGGYBDayTaskVC
  类名: @implementation YBDayTaskVC
  类型: @implementation声明

不一致: YBVideo/功能/直播/每日任务/直播间内/XGGYBDayTaskView.m
  文件名: XGGYBDayTaskView
  类名: @implementation YBDayTaskView
  类型: @implementation声明

不一致: YBVideo/功能/直播/每日任务/View/XGGYBDayTaskCell.m
  文件名: XGGYBDayTaskCell
  类名: @implementation YBDayTaskCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/每日任务/XGGYBDayTaskManager.m
  文件名: XGGYBDayTaskManager
  类名: @implementation YBDayTaskManager
  类型: @implementation声明

不一致: YBVideo/功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
  文件名: XGGYBGoodsBriefView
  类名: @implementation YBGoodsBriefView
  类型: @implementation声明

不一致: YBVideo/功能/直播/直播间聊天/XGGYBLiveChatView.m
  文件名: XGGYBLiveChatView
  类名: @implementation YBLiveChatView
  类型: @implementation声明

不一致: YBVideo/功能/直播/直播间聊天/model/XGGYBLiveChatModel.m
  文件名: XGGYBLiveChatModel
  类名: @implementation YBLiveChatModel
  类型: @implementation声明

不一致: YBVideo/功能/直播/直播间聊天/view/XGGYBLiveChatCell.m
  文件名: XGGYBLiveChatCell
  类名: @implementation YBLiveChatCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/在售商品/XGGYBOnSaleView.m
  文件名: XGGYBOnSaleView
  类名: @implementation YBOnSaleView
  类型: @implementation声明

不一致: YBVideo/功能/直播/在售商品/view/XGGYBOnSaleCell.m
  文件名: XGGYBOnSaleCell
  类名: @implementation YBOnSaleCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/直播间用户弹窗选项卡/XGGUserBulletWindow.m
  文件名: XGGUserBulletWindow
  类名: @implementation UserBulletWindow{
  类型: @implementation声明

不一致: YBVideo/功能/直播/直播间用户弹窗选项卡/XGGuserLevelView.m
  文件名: XGGuserLevelView
  类名: @implementation userLevelView
  类型: @implementation声明

不一致: YBVideo/功能/直播/幸运转盘/XGGturntableView.m
  文件名: XGGturntableView
  类名: @implementation turntableView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/幸运转盘/view/XGGturntableRecordCell.m
  文件名: XGGturntableRecordCell
  类名: @implementation turntableRecordCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/幸运转盘/view/XGGturntableResultCell.m
  文件名: XGGturntableResultCell
  类名: @implementation turntableResultCell
  类型: @implementation声明

不一致: YBVideo/功能/直播/幸运转盘/XGGturntableResultView.m
  文件名: XGGturntableResultView
  类名: @implementation turntableResultView
  类型: @implementation声明

不一致: YBVideo/功能/直播/幸运转盘/记录-规则/XGGturntableRuleView.m
  文件名: XGGturntableRuleView
  类名: @implementation turntableRuleView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/幸运转盘/记录-规则/XGGturntableRecordView.m
  文件名: XGGturntableRecordView
  类名: @implementation turntableRecordView{
  类型: @implementation声明

不一致: YBVideo/功能/直播/进房间动画/XGGYBUserEnterAnimation.m
  文件名: XGGYBUserEnterAnimation
  类名: @implementation YBUserEnterAnimation
  类型: @implementation声明

不一致: YBVideo/功能/直播/直播间私信/XGGhuanxinsixinview.m
  文件名: XGGhuanxinsixinview
  类名: @implementation huanxinsixinview{
  类型: @implementation声明

不一致: YBVideo/功能/直播/直播间私信/XGGYBImRoomSmallView.m
  文件名: XGGYBImRoomSmallView
  类名: @implementation YBImRoomSmallView
  类型: @implementation声明

不一致: YBVideo/功能/直播榜单/XGGLiveRankCell.m
  文件名: XGGLiveRankCell
  类名: @implementation LiveRankCell
  类型: @implementation声明

不一致: YBVideo/功能/直播榜单/XGGLiveRankVC.m
  文件名: XGGLiveRankVC
  类名: @implementation LiveRankVC
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGSettingsTableViewController.m
  文件名: XGGSettingsTableViewController
  类名: @implementation SettingsTableViewController
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGDetailTableViewController.m
  文件名: XGGDetailTableViewController
  类名: @implementation DetailTableViewController
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGDetailInputTableViewCell.m
  文件名: XGGDetailInputTableViewCell
  类名: @implementation DetailInputTableViewCell
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGSettingsTableViewCell.m
  文件名: XGGSettingsTableViewCell
  类名: @implementation SettingsTableViewCell
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/Settings/BDVRSettings.m
  文件名: BDVRSettings
  类名: @implementation BDVRSettings
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/Settings/XGGDetailTableViewCell.m
  文件名: XGGDetailTableViewCell
  类名: @implementation DetailTableViewCell
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/Settings/BDVRSettingsItem.m
  文件名: BDVRSettingsItem
  类名: @implementation BDVRSettingsItem
  类型: @implementation声明

不一致: YBVideo/功能/百度语音/VC/XGGASRView.m
  文件名: XGGASRView
  类名: @implementation ASRView
  类型: @implementation声明

不一致: YBVideo/功能/分享/发布分享/XGGPublishShareV.m
  文件名: XGGPublishShareV
  类名: @implementation PublishShareV
  类型: @implementation声明

不一致: YBVideo/功能/分享/观看分享/XGGYBShareView.m
  文件名: XGGYBShareView
  类名: @implementation YBShareView
  类型: @implementation声明

不一致: YBVideo/功能/分享/观看分享/XGGYBShareViewCell.m
  文件名: XGGYBShareViewCell
  类名: @implementation YBShareViewCell
  类型: @implementation声明

不一致: YBVideo/附近/XGGNearbyVC.m
  文件名: XGGNearbyVC
  类名: @implementation NearbyVC
  类型: @implementation声明

不一致: YBVideo/附近/城市选择/view/XGGYBCitySelCell.m
  文件名: XGGYBCitySelCell
  类名: @implementation YBCitySelCell
  类型: @implementation声明

不一致: YBVideo/附近/城市选择/XGGYBCitySelVC.m
  文件名: XGGYBCitySelVC
  类名: @implementation YBCitySelVC
  类型: @implementation声明

不一致: YBVideo/附近/view/XGGNearbyCell.m
  文件名: XGGNearbyCell
  类名: @implementation NearbyCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/商品记录/XGGcommodityRecordsCell.m
  文件名: XGGcommodityRecordsCell
  类名: @implementation commodityRecordsCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
  文件名: XGGcommodityRecordsVC
  类名: @implementation commodityRecordsVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/XGGYBCenterMoreView.m
  文件名: XGGYBCenterMoreView
  类名: @implementation YBCenterMoreView
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
  文件名: XGGYBRedProfitVC
  类名: @implementation YBRedProfitVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/我的收益/view/WLCardNoFormatter.m
  文件名: WLCardNoFormatter
  类名: @implementation UITextField (WLRange)
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/我的收益/view/XGGYBAddTypeView.m
  文件名: XGGYBAddTypeView
  类名: @implementation YBAddTypeView{
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/我的收益/view/XGGYBGetTypeListCell.m
  文件名: XGGYBGetTypeListCell
  类名: @implementation YBGetTypeListCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/我的收益/XGGYBGetProVC.m
  文件名: XGGYBGetProVC
  类名: @implementation YBGetProVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
  文件名: XGGYBGetTypeListVC
  类名: @implementation YBGetTypeListVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/商品收藏/view/XGGYBGoodsLikeCell.m
  文件名: XGGYBGoodsLikeCell
  类名: @implementation YBGoodsLikeCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
  文件名: XGGYBGoodsLikeVC
  类名: @implementation YBGoodsLikeVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
  文件名: XGGYBApplyStoreVC
  类名: @implementation YBApplyStoreVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/申请店铺/XGGYBApplyConditionVC.m
  文件名: XGGYBApplyConditionVC
  类名: @implementation YBApplyConditionVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/申请店铺/view/XGGYBApplyConditionCell.m
  文件名: XGGYBApplyConditionCell
  类名: @implementation YBApplyConditionCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGRoomUserTypeCell.m
  文件名: XGGRoomUserTypeCell
  类名: @implementation RoomUserTypeCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGOtherRoomViewController.m
  文件名: XGGOtherRoomViewController
  类名: @implementation OtherRoomViewController
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
  文件名: XGGRoomUserListViewController
  类名: @implementation RoomUserListViewController
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/房间管理/XGGRoomManagementVC.m
  文件名: XGGRoomManagementVC
  类名: @implementation RoomManagementVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
  文件名: XGGwatchingRecordsVC
  类名: @implementation watchingRecordsVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/观看记录/view/XGGWatchRecordListCell.m
  文件名: XGGWatchRecordListCell
  类名: @implementation WatchRecordListCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/明细/XGGaccountDetails.m
  文件名: XGGaccountDetails
  类名: @implementation accountDetails
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
  文件名: XGGYBGoodsInfoVC
  类名: @implementation YBGoodsInfoVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/店铺商品列表-详情/view/XGGYBGoodsListCell.m
  文件名: XGGYBGoodsListCell
  类名: @implementation YBGoodsListCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
  文件名: XGGYBGoodsListVC
  类名: @implementation YBGoodsListVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/view/XGGYBCenterMoreCell.m
  文件名: XGGYBCenterMoreCell
  类名: @implementation YBCenterMoreCell
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
  文件名: XGGdepositAccountVC
  类名: @implementation depositAccountVC
  类型: @implementation声明

不一致: YBVideo/个人中心/自己更多/投放账户--/XGGorderVideoCell.m
  文件名: XGGorderVideoCell
  类名: @implementation orderVideoCell
  类型: @implementation声明

不一致: YBVideo/个人中心/他人更多/XGGYBOtherCenterMore.m
  文件名: XGGYBOtherCenterMore
  类名: @implementation YBOtherCenterMore
  类型: @implementation声明

不一致: YBVideo/个人中心/XGGYBCenterVC.m
  文件名: XGGYBCenterVC
  类名: @implementation YBCenterVC
  类型: @implementation声明

不一致: YBVideo/个人中心/设置/view/XGGSetCell.m
  文件名: XGGSetCell
  类名: @implementation SetCell
  类型: @implementation声明

不一致: YBVideo/个人中心/设置/view/XGGSetLogoutCell.m
  文件名: XGGSetLogoutCell
  类名: @implementation SetLogoutCell
  类型: @implementation声明

不一致: YBVideo/个人中心/设置/用户认证/XGGYBUserAuthVC.m
  文件名: XGGYBUserAuthVC
  类名: @implementation YBUserAuthVC
  类型: @implementation声明

不一致: YBVideo/个人中心/设置/XGGSetViewControllor.m
  文件名: XGGSetViewControllor
  类名: @implementation SetViewControllor
  类型: @implementation声明

不一致: YBVideo/个人中心/设置/隐私政策/XGGYBPrivateVC.m
  文件名: XGGYBPrivateVC
  类名: @implementation YBPrivateVC
  类型: @implementation声明

不一致: YBVideo/个人中心/头部/XGGYBCenterTopView.m
  文件名: XGGYBCenterTopView
  类名: @implementation YBCenterTopView
  类型: @implementation声明

不一致: YBVideo/个人中心/头部/XGGHeaderBackImgView.m
  文件名: XGGHeaderBackImgView
  类名: @implementation HeaderBackImgView
  类型: @implementation声明

不一致: YBVideo/个人中心/作品_喜欢_收藏/XGGCenterListVC.m
  文件名: XGGCenterListVC
  类名: @implementation CenterListVC
  类型: @implementation声明

不一致: YBVideo/个人中心/作品_喜欢_收藏/XGGCenterListCell.m
  文件名: XGGCenterListCell
  类名: @implementation CenterListCell
  类型: @implementation声明

不一致: YBVideo/个人中心/编辑资料/XGGEditCell.m
  文件名: XGGEditCell
  类名: @implementation EditCell
  类型: @implementation声明

不一致: YBVideo/个人中心/编辑资料/XGGEditHeader.m
  文件名: XGGEditHeader
  类名: @implementation EditHeader
  类型: @implementation声明

不一致: YBVideo/个人中心/编辑资料/XGGEditVC.m
  文件名: XGGEditVC
  类名: @implementation EditVC
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGRKHorPickerView.m
  文件名: XGGRKHorPickerView
  类名: @implementation RKHorPickerView {
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGYBAlertActionSheet.m
  文件名: XGGYBAlertActionSheet
  类名: @implementation YBAlertActionSheet
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGYBButton.m
  文件名: XGGYBButton
  类名: @implementation YBButton
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGMyTextView.m
  文件名: XGGMyTextView
  类名: @implementation MyTextView
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGMyTextField.m
  文件名: XGGMyTextField
  类名: @implementation MyTextField
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGYBSegControl.m
  文件名: XGGYBSegControl
  类名: @implementation YBSegControl
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/跑马灯/XGGRKLampView.m
  文件名: XGGRKLampView
  类名: @implementation RKLampView
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/发布进度/XGGRKCircularProgress.m
  文件名: XGGRKCircularProgress
  类名: @implementation RKCircularProgress
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/发布进度/XGGYBUploadProgress.m
  文件名: XGGYBUploadProgress
  类名: @implementation YBUploadProgress
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/ZZCircleProgress/ZZCountingLabel.m
  文件名: ZZCountingLabel
  类名: @implementation ZZCountingLabel
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/ZZCircleProgress/ZZCircleProgress.m
  文件名: ZZCircleProgress
  类名: @implementation ZZCircleProgress
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGUtils.m
  文件名: XGGUtils
  类名: @implementation Utils
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/赞动画/CCAnimationBtn.m
  文件名: CCAnimationBtn
  类名: @implementation CCAnimationBtn
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XLProgress/XLCircle.m
  文件名: XLCircle
  类名: @implementation XLCircle
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XLProgress/XLCircleProgress.m
  文件名: XLCircleProgress
  类名: @implementation XLCircleProgress
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/XGGmylabels.m
  文件名: XGGmylabels
  类名: @implementation mylabels
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Categories/ZFModalTransitionAnimator.m
  文件名: ZFModalTransitionAnimator
  类名: @implementation ZFModalTransitionAnimator
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/V8HorizontalPickerView/V8HorizontalPickerView.m
  文件名: V8HorizontalPickerView
  类名: @implementation V8LabelNode
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParser.m
  文件名: SBJsonStreamParser
  类名: @implementation SBJsonStreamParser
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonParser.m
  文件名: SBJsonParser
  类名: @implementation SBJsonParser
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParserAccumulator.m
  文件名: SBJsonStreamParserAccumulator
  类名: @implementation SBJsonStreamParserAccumulator
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonUTF8Stream.m
  文件名: SBJsonUTF8Stream
  类名: @implementation SBJsonUTF8Stream
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParserState.m
  文件名: SBJsonStreamParserState
  类名: @implementation SBJsonStreamParserState
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonWriter.m
  文件名: SBJsonWriter
  类名: @implementation SBJsonWriter
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamWriter.m
  文件名: SBJsonStreamWriter
  类名: @implementation SBJsonStreamWriter
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamParserAdapter.m
  文件名: SBJsonStreamParserAdapter
  类名: @implementation SBJsonStreamParserAdapter
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamWriterAccumulator.m
  文件名: SBJsonStreamWriterAccumulator
  类名: @implementation SBJsonStreamWriterAccumulator
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonStreamWriterState.m
  文件名: SBJsonStreamWriterState
  类名: @implementation SBJsonStreamWriterState
  类型: @implementation声明

不一致: YBVideo/工具和自定义类/Vendor/sbjson/SBJsonTokeniser.m
  文件名: SBJsonTokeniser
  类名: @implementation SBJsonTokeniser
  类型: @implementation声明

