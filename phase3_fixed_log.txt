开始第三阶段：更新全局引用...
使用映射文件: need_rename_mapping_fixed.txt
开始处理映射...

处理类名映射: TCRangeContentConfig -> XGGTCRangeContentConfig
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'TCRangeContentConfig' 总共替换了 0 次

处理类名映射: VideoColorInfo -> XGGVideoColorInfo
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCVideoRangeSlider.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/XGGVideoColorInfo.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'VideoColorInfo' 总共替换了 0 次

处理类名映射: EffectSelectView -> XGGEffectSelectView
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'EffectSelectView' 总共替换了 0 次

处理类名映射: TimeSelectView -> XGGTimeSelectView
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'TimeSelectView' 总共替换了 0 次

处理类名映射: TCMusicInfo -> TCMusicCollectionCell
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.h
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.h
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicCollectionCell.m
    ✅ 替换成功，新类名出现 3 次
  类名 'TCMusicInfo' 总共替换了 7 次

处理类名映射: AlbumVideoCell -> XGGAlbumVideoCell
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/AlbumVideoCell.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoCell.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'AlbumVideoCell' 总共替换了 0 次

处理类名映射: AlbumVideoVC -> XGGAlbumVideoVC
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'AlbumVideoVC' 总共替换了 0 次

处理类名映射: VideoRecordProcessView -> XGGVideoRecordProcessView
  处理文件: YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/views/XGGVideoRecordProcessView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  类名 'VideoRecordProcessView' 总共替换了 0 次

处理类名映射: SpeedView -> XGGSpeedView
  处理文件: YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/views/SpeedView.temp_caseinsensitive_rename.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/views/XGGSpeedView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'SpeedView' 总共替换了 0 次

处理类名映射: YBPicTransitionVC -> XGGYBPicTransitionVC
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBPicTransitionVC' 总共替换了 0 次

处理类名映射: SmallButton -> XGGSmallButton
  处理文件: YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/view/XGGSmallButton.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'SmallButton' 总共替换了 0 次

处理类名映射: PhotoTransitionToolbar -> XGGPhotoTransitionToolbar
  处理文件: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'PhotoTransitionToolbar' 总共替换了 0 次

处理类名映射: VerticalButton -> XGGVerticalButton
  处理文件: YBVideo//录制_编辑_发布/图片转场/view/XGGPhotoTransitionToolbar.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/view/XGGVerticalButton.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'VerticalButton' 总共替换了 0 次

处理类名映射: YBPublishCoverVC -> XGGYBPublishCoverVC
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBPublishCoverVC' 总共替换了 0 次

处理类名映射: YBSetChargeView -> XGGYBSetChargeView
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/设置价格/YBSetChargeView.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/设置价格/XGGYBSetChargeView.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBSetChargeView' 总共替换了 0 次

处理类名映射: YBVideoAddGoodsVC -> XGGYBVideoAddGoodsVC
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBVideoAddGoodsVC' 总共替换了 0 次

处理类名映射: videoTopicCell -> XGGvideoTopicCell
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/videoTopicCell.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'videoTopicCell' 总共替换了 0 次

处理类名映射: videoTopicVC -> XGGvideoTopicVC
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'videoTopicVC' 总共替换了 0 次

处理类名映射: YBVideoClassVC -> XGGYBVideoClassVC
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBVideoClassVC' 总共替换了 0 次

处理类名映射: MusicClassVC -> XGGMusicClassVC
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'MusicClassVC' 总共替换了 0 次

处理类名映射: YBVideoMusicView -> XGGYBVideoMusicView
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBVideoMusicView' 总共替换了 0 次

处理类名映射: MusicModel -> XGGMusicModel
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/model/XGGMusicModel.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'MusicModel' 总共替换了 0 次

处理类名映射: MusicCell -> XGGMusicCell
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/MusicCell.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'MusicCell' 总共替换了 0 次

处理类名映射: MusicHeaderView -> XGGMusicHeaderView
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'MusicHeaderView' 总共替换了 0 次

处理类名映射: MusicHeaderCell -> XGGMusicHeaderCell
  处理文件: YBVideo//录制_编辑_发布/音乐/view/MusicHeaderCell.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderCell.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'MusicHeaderCell' 总共替换了 0 次

处理类名映射: YBBaseAppDelegate -> XGGYBBaseAppDelegate
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseAppDelegate.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/TCBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//缓存/定位/XGGRKLBSManager.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBBaseAppDelegate' 总共替换了 0 次

处理类名映射: RKLBSManager -> XGGRKLBSManager
  处理文件: YBVideo//其他类/XGGRKLBSManager.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//缓存/定位/XGGRKLBSManager.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//缓存/定位/XGGRKLBSManager.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/极光消息/单聊/View/JCHATMessageContentView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/极光消息/发送位置/XGGTencentLocationVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTLocationCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//附近/XGGNearbyVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'RKLBSManager' 总共替换了 1 次

处理类名映射: AppDelegate -> XGGAppDelegate
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 3 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseAppDelegate.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/TCBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 替换成功，新类名出现 3 次
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 3 次
  处理文件: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 替换成功，新类名出现 6 次
  处理文件: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 4 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 5 次
  处理文件: YBVideo//公共方法类/XGGRKSysAccess.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//引导页/XGGGuideViewController.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 替换成功，新类名出现 4 次
  处理文件: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 替换成功，新类名出现 4 次
  处理文件: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 替换成功，新类名出现 4 次
  处理文件: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 替换成功，新类名出现 3 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 替换成功，新类名出现 6 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 替换成功，新类名出现 5 次
  处理文件: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    ✅ 替换成功，新类名出现 1 次
  处理文件: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    ✅ 替换成功，新类名出现 2 次
  处理文件: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 替换成功，新类名出现 7 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 替换成功，新类名出现 8 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 替换成功，新类名出现 3 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPriceView.m
    ✅ 替换成功，新类名出现 1 次
  类名 'AppDelegate' 总共替换了 102 次

处理类名映射: YBBaseViewController -> XGGYBBaseViewController
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择封面/XGGYBPublishCoverVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/XGGYBHomeViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/国家代号/XGGCountryCodeVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目设置/XGGClassToExamineVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGShowDetailVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGGoodsDetailVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/保证金/XGGBondViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的地址/XGGAddressVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/收货地址/XGGEditSaveAddressVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBBaseViewController' 总共替换了 0 次

处理类名映射: YBNavigationController -> XGGYBNavigationController
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBNavigationController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/TCBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBNavigationController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/TCNavigationController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/H5/XGGPubH5.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBNavigationController' 总共替换了 0 次

处理类名映射: YBNavigationController -> TCNavigationController
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBNavigationController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/TCBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBNavigationController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/TCNavigationController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/H5/XGGPubH5.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBNavigationController' 总共替换了 0 次

处理类名映射: YBGetVideoObj -> XGGYBGetVideoObj
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/广告管理/XGGMyAdvertVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//附近/XGGNearbyVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/商品记录/XGGcommodityRecordsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBGetVideoObj' 总共替换了 0 次

处理类名映射: YBHomeRedObj -> XGGYBHomeRedObj
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBHomeRedObj' 总共替换了 0 次

处理类名映射: YBLookVideoCell -> XGGYBLookVideoCell
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBLookVideoCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBLookVideoCell' 总共替换了 0 次

处理类名映射: YBVideoControlView -> XGGYBVideoControlView
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBVideoControlView.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBVideoControlView' 总共替换了 0 次

处理类名映射: YBLookVideoVC -> XGGYBLookVideoVC
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/评论/评论工具栏/XGGYBCommentToolBar.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBLookVideoVC' 总共替换了 0 次

处理类名映射: NearbyVideoModel -> XGGNearbyVideoModel
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/models/XGGNearbyVideoModel.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/标签全部视频/XGGtopicVideoCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/标签全部视频/XGGtopicVideoCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/标签全部视频/XGGtopicDetailsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//附近/XGGNearbyVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//附近/view/XGGNearbyCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//附近/view/XGGNearbyCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/投放账户--/XGGdepositAccountVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/投放账户--/XGGorderVideoCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/作品_喜欢_收藏/XGGCenterListCell.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'NearbyVideoModel' 总共替换了 0 次

处理类名映射: MyFollowViewController -> XGGMyFollowViewController
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'MyFollowViewController' 总共替换了 0 次

处理类名映射: YBVideosVC -> XGGYBVideosVC
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBVideosVC' 总共替换了 0 次

处理类名映射: myVideoV -> XGGmyVideoV
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'myVideoV' 总共替换了 0 次

处理类名映射: VideoCollectionCell -> XGGVideoCollectionCell
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/views/XGGVideoCollectionCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/views/VideoCollectionCell.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'VideoCollectionCell' 总共替换了 0 次

处理类名映射: YBHomeViewController -> XGGYBHomeViewController
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/XGGYBHomeViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBHomeViewController' 总共替换了 0 次

处理类名映射: DspLoginVC -> XGGDspLoginVC
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/DspLoginVC.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'DspLoginVC' 总共替换了 0 次

处理类名映射: RegAlertView -> XGGRegAlertView
  处理文件: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/隐私提醒文本/RegAlertView.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'RegAlertView' 总共替换了 0 次

处理类名映射: CountryCodeVC -> XGGCountryCodeVC
  处理文件: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/国家代号/XGGCountryCodeVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'CountryCodeVC' 总共替换了 0 次

处理类名映射: RKActionSheet -> XGGRKActionSheet
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKActionSheet.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKActionSheet.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//缓存/定位/XGGRKLBSManager.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/极光消息/单聊/JCHATConversationViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/评论/XGGcommentview.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/评论/XGGcommCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/广告管理/XGGAdvertManagerVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/申请店铺/XGGYBApplyStoreVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/设置/用户认证/XGGYBUserAuthVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/头部/XGGHeaderBackImgView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/编辑资料/XGGEditVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'RKActionSheet' 总共替换了 0 次

处理类名映射: YBProgressObj -> XGGYBProgressObj
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBProgressObj.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBProgressObj.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBProgressObj' 总共替换了 0 次

处理类名映射: PublicObj -> XGGPublicObj
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGEffectSelectView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/Views/XGGTimeSelectView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/views/TXBaseBeautyView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGYBBaseViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBVideoControlView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/隐私提醒文本/XGGRegAlertView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//XGGNetwork/XGGNetworkManager.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGiOSNetworking.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBAlertView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKActionSheet.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKSysAccess.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/XGGShareGoodView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailModel.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/view/XGGStoreInfoView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGGoodsExplainCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'PublicObj' 总共替换了 0 次

处理类名映射: YBNetworking -> XGGYBNetworking
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/添加商品/XGGYBVideoAddGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/选择话题/XGGvideoTopicVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/视频分类/XGGYBVideoClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicHeaderView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/view/XGGMusicCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBHomeRedObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/XGGYBHomeViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/view/XGGYBLookVideoCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/国家代号/XGGCountryCodeVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBNetworking.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/平台介入/XGGPlatformInterventionVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/立即支付/XGGPayOrderView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/追评/XGGAppendEvaluateVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGShopApplyStatusVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/保证金/XGGBondViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的地址/编辑地址/XGGEditAdressVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/提取余额/XGGBuyerGetMoneyVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/买家退款详情/XGGBuyerRefundDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/小店主页/卖家页面/XGGSellerView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBNetworking' 总共替换了 0 次

处理类名映射: BGSetting -> XGGBGSetting
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGBGSetting.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/红包收益/XGGYBRedProfitVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/我的收益/XGGYBGetProVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/设置/XGGSetViewControllor.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'BGSetting' 总共替换了 0 次

处理类名映射: RKSysAccess -> XGGRKSysAccess
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKSysAccess.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKSysAccess.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'RKSysAccess' 总共替换了 0 次

处理类名映射: iOSNetworking -> XGGiOSNetworking
  处理文件: YBVideo//公共方法类/XGGiOSNetworking.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGiOSNetworking.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBNetworking.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'iOSNetworking' 总共替换了 0 次

处理类名映射: YBAlertView -> XGGYBAlertView
  处理文件: YBVideo//录制_编辑_发布/UGCEditor/TCVideoEditViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/Record/TCVideoRecordViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/图片转场/XGGYBPicTransitionVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/UGCPublish/TCVideoPublishController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/获取视频详情公用obj/XGGYBGetVideoObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBAlertView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/YBAlertView.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBAlertView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGBGSetting.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKSysAccess.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/直播or视频/XGGYBLiveOrVideo.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//底部导航/XGGYBTabBarController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/主播端相关/view/XGGYBLivePreview.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/主播端相关/XGGYBLiveVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/socket/XGGYBSocketLive.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/socket/XGGYBSocketPlay.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/极光消息/单聊/View/JCHATToolBar.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/上热门/XGGaddHotVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/青少年/XGGYBYoungManager.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/青少年/vc/XGGYBYoungSetVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/注销账号/XGGYBDestroySureVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/XGGTMessageController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/消息会话/TChatC2CController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/直播/管理员列表/XGGadminLists.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/直播/直播间私信/XGGhuanxinsixinview.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/分享/观看分享/XGGYBShareView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/我的收益/XGGYBGetTypeListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/房间管理/XGGRoomUserListViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/观看记录/XGGwatchingRecordsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/店铺商品列表-详情/XGGYBGoodsInfoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBAlertView' 总共替换了 0 次

处理类名映射: RKUUIDManager -> XGGRKUUIDManager
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKUUIDManager.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGRKUUIDManager.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicObj.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'RKUUIDManager' 总共替换了 0 次

处理类名映射: YBShowBigImageView -> XGGYBShowBigImageView
  处理文件: YBVideo//公共方法类/XGGYBShowBigImageView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBShowBigImageView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBShowBigImageView' 总共替换了 0 次

处理类名映射: YBImageView -> XGGYBImageView
  处理文件: YBVideo//公共方法类/XGGYBImageView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGYBImageView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/评价/XGGPublishEvaluateVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityEvaluationCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品评价/XGGEvaluationListCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityDetailView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/添加商品/子页面view/XGGCommodityTitleView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/IM管理/工具资源/XGGYBScrollImageView.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'YBImageView' 总共替换了 0 次

处理类名映射: PublicView -> XGGPublicView
  处理文件: YBVideo//录制_编辑_发布/Record/选相册视频/XGGAlbumVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGYBVideoMusicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//录制_编辑_发布/音乐/XGGMusicClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//其他类/PrefixHeader.pch
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/推荐/XGGYBLookVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGYBVideosVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGMyFollowViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//首页/热门-关注-分类/XGGmyVideoV.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//登录注册/XGGDspLoginVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/PublicView.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//公共方法类/XGGPublicView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的地址/XGGAddressVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品评价/XGGGoodsEvaluationListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/XGGOrderListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/XGGOrderDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderPublicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/账户余额/XGGAccountBalanceVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/其他订单详情/XGGOtherSellOrderDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/订单管理/XGGSellerOrderManagementVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/账单管理/XGGBillManageVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/关联商品付费内容/XGGRelationVideoGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/我的店铺/XGGRelationGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/待发货详情/XGGWaitSendGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/公共页面/XGGSellOrderPublicView.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//消息/XGGOrderMessageVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//消息/XGGMsgTopPubVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//消息/XGGMsgSysVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//消息/XGGMessageFansVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//消息/选择联系人/XGGSelPeopleV.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/直播列表/XGGYBLiveListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/主播端相关/view/XGGYBLiveCtrlView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/粉丝_关注_拉黑/XGGfansViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/粉丝_关注_拉黑/XGGattrViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/粉丝_关注_拉黑/XGGBlackListVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/上热门/XGGHotVideoDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/顶部导航搜索/XGGsearchVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/评论/XGGcommentview.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/广告管理/XGGMyAdvertVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/拍摄同款/XGGYBTakeSameVideoVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/礼物/XGGYBGiftView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/直播/连麦+PK/主播连麦在线列表/XGGYBAnchorOnline.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/直播/在售商品/XGGYBOnSaleView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'PublicView' 总共替换了 0 次

处理类名映射: GuideViewController -> XGGGuideViewController
  处理文件: YBVideo//其他类/XGGAppDelegate.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//引导页/XGGGuideViewController.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/设置/隐私政策/XGGYBPrivateVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'GuideViewController' 总共替换了 0 次

处理类名映射: ApplyRefundVC -> XGGApplyRefundVC
  处理文件: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/退款申请/XGGApplyRefundVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/我的订单/订单详情/子页面/XGGOrderInfoView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'ApplyRefundVC' 总共替换了 0 次

处理类名映射: SelectClassVC -> XGGSelectClassVC
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'SelectClassVC' 总共替换了 0 次

处理类名映射: CommodityClassModel -> XGGCommodityClassModel
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassModel.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/开店申请/XGGApplyShopVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'CommodityClassModel' 总共替换了 0 次

处理类名映射: CommodityClassCell -> XGGCommodityClassCell
  处理文件: YBVideo//店铺/买家端/经营类目选择/CommodityClassCell.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGSelectClassVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目选择/XGGCommodityClassCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/经营类目设置/XGGClassificationVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'CommodityClassCell' 总共替换了 0 次

处理类名映射: ConfirmOrderVC -> XGGConfirmOrderVC
  处理文件: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/确认订单/XGGConfirmOrderVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/选择规格/XGGSelectStandardsView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'ConfirmOrderVC' 总共替换了 0 次

处理类名映射: HistoryListModel -> XGGHistoryListModel
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'HistoryListModel' 总共替换了 0 次

处理类名映射: LookHistoryCell -> XGGLookHistoryCell
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/LookHistoryCell.xib
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'LookHistoryCell' 总共替换了 0 次

处理类名映射: LookHistoryVC -> XGGLookHistoryVC
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/小店主页/买家页面/XGGBuyerView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/XGGYBCenterMoreView.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'LookHistoryVC' 总共替换了 0 次

处理类名映射: LookHistoryModel -> XGGLookHistoryModel
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGHistoryListModel.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryModel.h
    ✅ 替换成功，新类名出现 0
0 次
  类名 'LookHistoryModel' 总共替换了 0 次

处理类名映射: OutsideGoodsDetailVC -> XGGOutsideGoodsDetailVC
  处理文件: YBVideo//店铺/买家端/浏览记录/XGGLookHistoryVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/商品管理/XGGCommodityManagementVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/平台商品/XGGPlatformGoodsVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/我的店铺/XGGshopDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/卖家端/我的店铺/XGGgoodsShowCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//直播模块/用户端相关/view/XGGYBPlayCtrlView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/极光消息/单聊/View/CellView/JCHATMessageTableViewCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/观看商品/XGGlookVGoodsDView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/腾讯消息/消息会话/单-群聊公共/view/XGGTGoodsCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//功能/直播/用户端商品简介/XGGYBGoodsBriefView.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//个人中心/自己更多/商品收藏/XGGYBGoodsLikeVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'OutsideGoodsDetailVC' 总共替换了 0 次

处理类名映射: OutsideHeadCell -> XGGOutsideHeadCell
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideHeadCell.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'OutsideHeadCell' 总共替换了 0 次

处理类名映射: ShareGoodsAlert -> XGGShareGoodsAlert
  处理文件: YBVideo//店铺/买家端/站外商品详情/XGGOutsideGoodsDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/商品详情/XGGCommodityDetailVC.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'ShareGoodsAlert' 总共替换了 0 次

处理类名映射: ShareFriendVC -> XGGShareFriendVC
  处理文件: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.m
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/分享给好友/XGGShareFriendVC.h
    ✅ 替换成功，新类名出现 0
0 次
  处理文件: YBVideo//店铺/买家端/分享商品/XGGShareGoodsAlert.m
    ✅ 替换成功，新类名出现 0
0 次
  类名 'ShareFriendVC' 总共替换了 0 次

处理类名映射: ShareFriendCell -> XGGShareFriendCell
