#!/bin/bash

# 测试单个替换
echo "=== 测试单个替换 ==="

OLD_CLASS="TCRangeContentConfig"
NEW_CLASS="XGGTCRangeContentConfig"
TEST_FILE="YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m"

echo "测试替换: $OLD_CLASS -> $NEW_CLASS"
echo "测试文件: $TEST_FILE"
echo ""

# 检查文件是否存在
if [[ ! -f "$TEST_FILE" ]]; then
    echo "❌ 文件不存在: $TEST_FILE"
    exit 1
fi

# 检查文件是否包含旧类名
if ! grep -q "$OLD_CLASS" "$TEST_FILE"; then
    echo "❌ 文件中不包含类名: $OLD_CLASS"
    exit 1
fi

echo "✅ 文件存在且包含目标类名"

# 显示替换前的内容
echo ""
echo "替换前的内容:"
grep -n "$OLD_CLASS" "$TEST_FILE"

# 创建备份
cp "$TEST_FILE" "${TEST_FILE}.test.backup"
echo ""
echo "✅ 已创建备份: ${TEST_FILE}.test.backup"

# 执行替换
sed -i '' "s/\b${OLD_CLASS}\b/${NEW_CLASS}/g" "$TEST_FILE"

echo ""
echo "✅ 替换完成"

# 显示替换后的内容
echo ""
echo "替换后的内容:"
grep -n "$NEW_CLASS" "$TEST_FILE" || echo "未找到新类名"

# 验证替换结果
if grep -q "$NEW_CLASS" "$TEST_FILE"; then
    echo ""
    echo "✅ 替换成功！"
    
    # 统计替换次数
    count=$(grep -c "$NEW_CLASS" "$TEST_FILE")
    echo "新类名出现次数: $count"
else
    echo ""
    echo "❌ 替换失败！"
    
    # 恢复备份
    cp "${TEST_FILE}.test.backup" "$TEST_FILE"
    echo "已恢复备份"
fi
