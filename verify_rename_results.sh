#!/bin/bash

# 第四阶段：验证修改结果
echo "=== 第四阶段：验证修改结果 ==="

MAPPING_FILE="need_rename_mapping.txt"
VERIFY_LOG="verification_log.txt"

# 清空日志文件
> "$VERIFY_LOG"

if [[ ! -f "$MAPPING_FILE" ]]; then
    echo "错误：映射文件 $MAPPING_FILE 不存在"
    exit 1
fi

echo "开始验证修改结果..."
echo "映射文件: $MAPPING_FILE"
echo "验证日志: $VERIFY_LOG"
echo ""

# 统计变量
total_checks=0
success_checks=0
error_checks=0

# 验证函数：检查类名是否已经正确修改
verify_class_rename() {
    local old_class="$1"
    local new_class="$2"
    
    ((total_checks++))
    
    echo "验证映射: $old_class -> $new_class" | tee -a "$VERIFY_LOG"
    
    # 搜索是否还有旧类名的引用（排除备份文件）
    old_references=$(grep -r "\b${old_class}\b" YBVideo/ --include="*.h" --include="*.m" --include="*.mm" --exclude="*.backup" --exclude="*.phase3.backup" 2>/dev/null | grep -v "Pods/" | grep -v "三方_SDK" | wc -l)
    
    # 搜索新类名的引用
    new_references=$(grep -r "\b${new_class}\b" YBVideo/ --include="*.h" --include="*.m" --include="*.mm" --exclude="*.backup" --exclude="*.phase3.backup" 2>/dev/null | grep -v "Pods/" | grep -v "三方_SDK" | wc -l)
    
    if [[ $old_references -eq 0 ]]; then
        echo "  ✅ 旧类名 '$old_class' 已完全替换" | tee -a "$VERIFY_LOG"
        if [[ $new_references -gt 0 ]]; then
            echo "  ✅ 新类名 '$new_class' 存在 $new_references 个引用" | tee -a "$VERIFY_LOG"
            ((success_checks++))
        else
            echo "  ⚠️  新类名 '$new_class' 没有找到引用" | tee -a "$VERIFY_LOG"
            ((error_checks++))
        fi
    else
        echo "  ❌ 旧类名 '$old_class' 仍有 $old_references 个引用未替换" | tee -a "$VERIFY_LOG"
        echo "  未替换的引用:" | tee -a "$VERIFY_LOG"
        grep -r "\b${old_class}\b" YBVideo/ --include="*.h" --include="*.m" --include="*.mm" --exclude="*.backup" --exclude="*.phase3.backup" 2>/dev/null | grep -v "Pods/" | grep -v "三方_SDK" | head -5 | sed 's/^/    /' | tee -a "$VERIFY_LOG"
        ((error_checks++))
    fi
    
    echo "" | tee -a "$VERIFY_LOG"
}

# 读取映射文件并验证每个重命名
while read -r line; do
    if [[ -n "$line" && "$line" == *" -> "* ]]; then
        # 正确分割映射行
        old_class="${line% -> *}"
        new_class="${line#* -> }"

        if [[ -n "$old_class" && -n "$new_class" && "$old_class" != "$new_class" ]]; then
            verify_class_rename "$old_class" "$new_class"
        fi
    fi
done < "$MAPPING_FILE"

echo "=== 验证完成 ==="
echo "总验证数: $total_checks"
echo "成功: $success_checks"
echo "失败/警告: $error_checks"
echo "详细日志: $VERIFY_LOG"

# 检查是否有语法错误
echo ""
echo "=== 检查语法错误 ==="
syntax_errors=0

# 检查一些关键文件的语法
key_files=(
    "YBVideo/其他类/XGGAppDelegate.m"
    "YBVideo/其他类/XGGYBBaseViewController.m"
    "YBVideo/首页/XGGYBHomeViewController.m"
    "YBVideo/底部导航/XGGYBTabBarController.m"
)

for file in "${key_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "检查文件: $file"
        # 简单的语法检查：查找明显的语法错误
        if grep -q "@@interface\|@@implementation\|##import" "$file"; then
            echo "  ❌ 发现语法错误" | tee -a "$VERIFY_LOG"
            ((syntax_errors++))
        else
            echo "  ✅ 语法检查通过" | tee -a "$VERIFY_LOG"
        fi
    else
        echo "  ⚠️  文件不存在: $file" | tee -a "$VERIFY_LOG"
    fi
done

echo ""
if [[ $error_checks -eq 0 && $syntax_errors -eq 0 ]]; then
    echo "🎉 验证完成！所有类名重命名成功，未发现语法错误。"
    echo ""
    echo "=== 重命名统计 ==="
    echo "成功重命名的类: $success_checks"
    echo "处理的文件数: 约$(find YBVideo/ -name "*.backup" | wc -l)"
    echo ""
    echo "✅ 类名与文件名一致性修复完成！"
else
    echo "⚠️  验证发现问题："
    echo "  - 重命名失败/警告: $error_checks"
    echo "  - 语法错误: $syntax_errors"
    echo ""
    echo "请检查日志文件: $VERIFY_LOG"
fi
