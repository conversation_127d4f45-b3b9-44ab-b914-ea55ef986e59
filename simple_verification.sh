#!/bin/bash

# 简化验证脚本 - 检查关键问题
echo "=== 简化验证脚本 ==="

MAPPING_FILE="need_rename_mapping.txt"
SIMPLE_LOG="simple_verification.txt"

# 清空日志
> "$SIMPLE_LOG"

echo "检查关键映射问题..." | tee -a "$SIMPLE_LOG"
echo "" | tee -a "$SIMPLE_LOG"

# 检查第一个映射：TCRangeContentConfig -> TCRangeContent
echo "1. 检查 TCRangeContentConfig 映射问题:" | tee -a "$SIMPLE_LOG"
first_mapping=$(head -1 "$MAPPING_FILE")
echo "   映射文件第一行: $first_mapping" | tee -a "$SIMPLE_LOG"

if [[ "$first_mapping" == "TCRangeContentConfig -> TCRangeContent" ]]; then
    echo "   ❌ 问题确认: TCRangeContentConfig 被错误映射到 TCRangeContent" | tee -a "$SIMPLE_LOG"
    echo "   这会导致两个不同的类使用相同的名字" | tee -a "$SIMPLE_LOG"
else
    echo "   ✅ 第一行映射正常" | tee -a "$SIMPLE_LOG"
fi

echo "" | tee -a "$SIMPLE_LOG"

# 检查实际文件状态
echo "2. 检查实际文件中的类名状态:" | tee -a "$SIMPLE_LOG"

# 检查TCRangeContent.h文件
range_content_file="YBVideo/录制_编辑_发布/UGCEditor/Views/VideoRangeSlider/TCRangeContent.h"
if [[ -f "$range_content_file" ]]; then
    echo "   检查文件: $range_content_file" | tee -a "$SIMPLE_LOG"
    
    # 检查TCRangeContentConfig类声明
    config_class_count=$(grep -c "^@interface TCRangeContentConfig" "$range_content_file" 2>/dev/null || echo "0")
    content_class_count=$(grep -c "^@interface TCRangeContent[^C]" "$range_content_file" 2>/dev/null || echo "0")
    
    echo "   - TCRangeContentConfig 类声明: $config_class_count 个" | tee -a "$SIMPLE_LOG"
    echo "   - TCRangeContent 类声明: $content_class_count 个" | tee -a "$SIMPLE_LOG"
    
    if [[ $config_class_count -gt 0 && $content_class_count -gt 0 ]]; then
        echo "   ❌ 问题: 同一文件中有两个不同的类，不应该映射到相同名字" | tee -a "$SIMPLE_LOG"
    fi
else
    echo "   ⚠️  文件不存在: $range_content_file" | tee -a "$SIMPLE_LOG"
fi

echo "" | tee -a "$SIMPLE_LOG"

# 检查第三阶段的实际执行结果
echo "3. 检查第三阶段执行结果:" | tee -a "$SIMPLE_LOG"

# 检查一个使用了这些类的文件
music_mix_file="YBVideo/录制_编辑_发布/UGCEditor/Views/TCMusicMixView.m"
if [[ -f "$music_mix_file" ]]; then
    echo "   检查文件: $music_mix_file" | tee -a "$SIMPLE_LOG"
    
    # 统计类名引用
    config_refs=$(grep -c "TCRangeContentConfig" "$music_mix_file" 2>/dev/null || echo "0")
    content_refs=$(grep -c "TCRangeContent[^C]" "$music_mix_file" 2>/dev/null || echo "0")
    
    echo "   - TCRangeContentConfig 引用: $config_refs 个" | tee -a "$SIMPLE_LOG"
    echo "   - TCRangeContent 引用: $content_refs 个" | tee -a "$SIMPLE_LOG"
    
    if [[ $config_refs -gt 0 ]]; then
        echo "   ❌ 问题: TCRangeContentConfig 引用未被替换" | tee -a "$SIMPLE_LOG"
        echo "   显示前3个引用:" | tee -a "$SIMPLE_LOG"
        grep -n "TCRangeContentConfig" "$music_mix_file" | head -3 | sed 's/^/     /' | tee -a "$SIMPLE_LOG"
    fi
    
    if [[ $content_refs -gt 0 ]]; then
        echo "   ✅ TCRangeContent 引用存在 (第三阶段部分成功)" | tee -a "$SIMPLE_LOG"
    fi
else
    echo "   ⚠️  文件不存在: $music_mix_file" | tee -a "$SIMPLE_LOG"
fi

echo "" | tee -a "$SIMPLE_LOG"

# 检查备份文件
echo "4. 检查备份文件状态:" | tee -a "$SIMPLE_LOG"
backup_count=$(find YBVideo/ -name "*.phase3.backup" | wc -l)
echo "   第三阶段备份文件数量: $backup_count" | tee -a "$SIMPLE_LOG"

if [[ $backup_count -gt 0 ]]; then
    echo "   ✅ 备份文件存在，可以回滚" | tee -a "$SIMPLE_LOG"
else
    echo "   ⚠️  没有第三阶段备份文件" | tee -a "$SIMPLE_LOG"
fi

echo "" | tee -a "$SIMPLE_LOG"

# 总结
echo "=== 验证总结 ===" | tee -a "$SIMPLE_LOG"
echo "" | tee -a "$SIMPLE_LOG"
echo "主要问题:" | tee -a "$SIMPLE_LOG"
echo "1. 映射文件中存在错误映射: TCRangeContentConfig -> TCRangeContent" | tee -a "$SIMPLE_LOG"
echo "2. 这两个类在同一文件中，是不同的类，不应该映射到相同名字" | tee -a "$SIMPLE_LOG"
echo "3. 第三阶段执行了错误的替换，导致代码不一致" | tee -a "$SIMPLE_LOG"
echo "" | tee -a "$SIMPLE_LOG"
echo "建议解决方案:" | tee -a "$SIMPLE_LOG"
echo "1. 回滚第三阶段的更改" | tee -a "$SIMPLE_LOG"
echo "2. 修正映射文件中的错误映射" | tee -a "$SIMPLE_LOG"
echo "3. 重新执行第三阶段" | tee -a "$SIMPLE_LOG"
echo "" | tee -a "$SIMPLE_LOG"
echo "详细日志已保存到: $SIMPLE_LOG"
